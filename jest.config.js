module.exports = {
    'moduleFileExtensions': ['*', 'js', 'vue'],
    'testResultsProcessor': 'jest-sonar-reporter',
    'moduleNameMapper': {
        '^@/(.*)$': '<rootDir>/src/$1',
        '^src(.*)$': '<rootDir>/src$1',
        '^api(.*)$': '<rootDir>/src/api$1',
        '^common(.*)$': '<rootDir>/src/common$1',
        '^components(.*)$': '<rootDir>/src/components$1',
        '^lang(.*)$': '<rootDir>/src/lang$1',
        '^mixins(.*)$': '<rootDir>/src/mixins$1',
        '^pages(.*)$': '<rootDir>/src/pages$1',
        '^router(.*)$': '<rootDir>/src/router$1',
        '^service(.*)$': '<rootDir>/src/service$1',
        '^store(.*)$': '<rootDir>/src/store$1',
        '^utils(.*)$': '<rootDir>/src/utils$1',
        '^testUtils(.*)$': '<rootDir>/test/testUtils$1',
    },
    'transform': {
        '^.+\\.js$': 'babel-jest',
        '^.+\\.vue$': 'vue-jest',
    },
    'globals': {
        'signPath': true,
    },
};
