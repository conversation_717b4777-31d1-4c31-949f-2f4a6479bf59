'use strict';
const fs = require('fs');
const path = require('path');
const webpack = require('webpack');
const NODE_ENV = process.env.NODE_ENV;
const config  = require('./package.json');
process.env.VUE_APP_PROJECT_NAME = config.name;
// const ProgressBarPlugin = require('progress-bar-webpack-plugin');
// const HardSourceWebpackPlugin = require('hard-source-webpack-plugin');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
// 是否是development模式
const isDevelopment = NODE_ENV.indexOf('development') > -1;
const releaseNum = 'ent@2022_1';
const ignoreSentry = process.env.VUE_APP_NO_SENTRY === 'ignore-sentry';
function sentrySourceMapPlugin() {
    const plugins = [];
    if (!ignoreSentry && !isDevelopment) {
        plugins.push(new CleanWebpackPlugin());
        plugins.push(new SentryWebpackPlugin({
            release: releaseNum,
            include: './build',
            ignore: ['node_modules', 'vue.config.js'],
            configFile: 'sentry.properties',
            urlPrefix: '~/',
        }));
    }
    return plugins;
}
function resolve(dir) {
    return path.join(__dirname, dir);
}

const prodPlugins = [];
// if (!isDevelopment) {
//     prodPlugins.push(new ProgressBarPlugin());
//     prodPlugins.push(new HardSourceWebpackPlugin());
// }
// 将proxy.config.dev.js文件中的配置格式化成vue-cli可以接受的格式
function proxyFormat() {
    const proxyFile = fs.readFileSync(resolve('proxy.config.dev.js'), 'utf-8');
    const proxyConfig = eval(proxyFile);
    return proxyConfig.reduce((proxy, item) => {
        const context = item.context;
        delete item.context;
        if (typeof context === 'string') {
            proxy[context] = item;
        } else {
            context.forEach((c) => {
                proxy[c] = item;
            });
        }
        return proxy;
    }, {});
}
const SentryWebpackPlugin = require('@sentry/webpack-plugin');
module.exports = {
    publicPath: '/',
    outputDir: 'build',
    assetsDir: 'static',
    lintOnSave: false,
    productionSourceMap: true,
    devServer: {
        host: '0.0.0.0',
        port: 9000,
        historyApiFallback: true,
        overlay: {
            warnings: true,
            errors: true,
        },
        hot: true,
        disableHostCheck: true,
        clientLogLevel: 'silent',
        open: true,
        // 修改配置文件，请移步至 proxy.config.dev.js 文件。修改target后，不需要重新npm run dev，刷新下页面即可。
        proxy: (() => {
            const proxy = proxyFormat();
            Object.keys(proxy).forEach(item => {
                proxy[item].router = function(request) {
                    const proxy = proxyFormat();
                    const item = Object.keys(proxy).find(key => request.url.indexOf(key) > -1);
                    return (item && proxy[item].target) || false;
                };
            });
            return proxy;
        })(),
    },
    configureWebpack: {
        resolve: {
            extensions: ['*', '.js', '.vue', '.json', '.css', '.scss', '.less'],
            alias: {
                'src': resolve('src'),
                'css': resolve('src/common/assets/css'),
                'js': resolve('src/common/assets/js'),
                'img': resolve('src/common/assets/img'),
                'utils': resolve('src/common/utils'),
                'common_const': resolve('src/common/const'),
                'components': resolve('src/components'),
                'common_pages': resolve('src/pages/common'),
                'active_pages': resolve('src/pages/active'),
                'enterprise_pages': resolve('src/pages/enterprise'),
                'foundation_pages': resolve('src/pages/foundation'),
            },
        },
        devtool: isDevelopment ? 'cheap-module-source-map' : undefined,
        plugins: sentrySourceMapPlugin(),
    },
    chainWebpack(config) {
        config.plugins.delete('preload');
        config.plugins.delete('prefetch');

        // DefinePlugin
        config.plugin('define')
            .tap((args) => {
                return [{
                    ...args[0],
                    pdfCmaps: JSON.stringify('/public_static/cmaps/'),
                    signPath: JSON.stringify('/contract-api'),
                    tempPath: JSON.stringify('/template-api'),
                }];
            });

        // CopyPlugin
        config.plugin('copy')
            .tap((args) => {
                let copyObj = [
                    { from: 'public_static/', to: 'public_static/' },
                    {
                        from: 'node_modules/pdfview/cmaps/',
                        to: 'public_static/cmaps',
                    },
                ];
                // 生产环境
                if (!isDevelopment) {
                    copyObj = [...copyObj, {
                        from: 'doc/',
                        to: 'doc/',
                    }, {
                        from: 'yiL2SvxliG.txt',
                        to: 'yiL2SvxliG.txt',
                    }, { /* 这个名字不能改哦！！这个文件是为了配置小程序的业务域名，如果改了的话，小程序里面则打不开我们的h5页面了，勿动！！ */
                        from: 'WW_verify_OXYYVH4IowSYaNvw.txt',
                        to: 'WW_verify_OXYYVH4IowSYaNvw.txt',
                    }, { /* 这个名字不能改哦！！这个文件是为了配置小程序的业务域名，如果改了的话，小程序里面则打不开我们的h5页面了，勿动！！ */
                        from: 'src/common/assets/img/bestsign-logo.png',
                        to: 'static/img/bestsign-logo.png',
                    }];
                }
                return [args[0].concat(copyObj)];
            });

        // ProvidePlugin
        config.plugin('provide')
            .use(webpack.ProvidePlugin, [{
                Vue: ['vue/dist/vue.runtime.esm.js', 'default'],
                VueRouter: 'vue-router/dist/vue-router.min.js',
                Vuex: 'vuex/dist/vuex.min.js',
            }]);

        config.module
            .rule('vue')
            .use('vue-loader')
            .loader('vue-loader')
            .tap(options => {
                options.compilerOptions.preserveWhitespace = true;
                return options;
            })
            .end();

        // config
        // .when(isDevelopment,
        //     config => config.devtool('cheap-source-map')
        // );
        config
            .when(!isDevelopment,
                config => {
                    config
                        .plugin('ScriptExtHtmlWebpackPlugin')
                        .after('html')
                        .use('script-ext-html-webpack-plugin', [{
                            inline: /runtime\..*\.js$/,
                        }])
                        .end();
                    config.optimization.runtimeChunk('single');
                },
            );

        // 开发环境不建议使用此模式，热更新比较耗时，但旗舰版遗留问题，暂时先这样处理
        config.optimization.splitChunks({
            chunks: 'all',
            cacheGroups: {
                vendors: {
                    name: 'chunk-vendors',
                    test: /[\\/]node_modules[\\/]/,
                    priority: -10,
                    chunks: 'initial',
                },
                common: {
                    name: 'chunk-common',
                    minChunks: 2,
                    priority: -20,
                    chunks: 'initial',
                    reuseExistingChunk: true,
                },
                // css代码如果根据动态导入分割css文件，会有样式问题，暂时先将所有css合并成一个包
                styles: {
                    name: 'styles',
                    test: module => module.constructor.name === 'CssModule',
                    chunks: 'all',
                    enforce: true,
                    minChunks: 1,
                    priority: -15,
                },
            },
        });
    },
    // 预定义变量
    css: {
        loaderOptions: {
            scss: {
                prependData: `@import "./src/common/assets/css/config.scss";`,
            },
        },
        extract: { // 开发环境不建议使用此模式
            ignoreOrder: true,
        },
    },
    runtimeCompiler: false,
    transpileDependencies: ['pdfjs-dist'],
};
