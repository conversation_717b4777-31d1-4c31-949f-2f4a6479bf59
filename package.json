{"name": "delta-front-end", "version": "1.0.0", "description": "", "author": "bestsign", "repository": {"type": "git", "url": "https://git.bestsign.tech/delta/delta-front-end"}, "scripts": {"dev": "vue-cli-service serve --mode development", "dev-ccb": "vue-cli-service serve --mode development-ccb", "dev-ja": "vue-cli-service serve --mode development-ja", "dev-uae": "vue-cli-service serve --mode development-uae", "build": "vue-cli-service build", "build-noSentry": "vue-cli-service build --mode ignore-sentry", "lint": "eslint --ext .js,.vue src --max-warnings 0", "lint-fix": "eslint --fix --ext .js,.vue src", "test": "jest --no-cache", "test:cover": "jest --no-cache --coverage", "lint-report": "eslint --ext .vue,.js -f json src/ > eslint-report.json"}, "dependencies": {"@sentry/tracing": "^6.12.0", "@sentry/vue": "^6.12.0", "@sentry/webpack-plugin": "^1.19.1", "animate.css": "^3.5.2", "axios": "~0.19.0", "babel-plugin-add-module-exports": "^1.0.0", "clean-webpack-plugin": "^4.0.0", "core-js": "^3.6.4", "crypto-js": "^3.1.9-1", "dayjs": "^1.11.7", "diff": "^7.0.0", "echarts": "^5.5.1", "element-ui": "^1.4.5", "fingerprintjs2": "^1.8.6", "js-confetti": "^0.11.0", "js-cookie": "^2.2.1", "js-sha1": "^0.6.0", "lint-staged": "^11.0.0", "pdfjs-dist": "3.0.279", "pdfview": "^1.10.102", "qrious": "^4.0.2", "random-js": "^1.0.8", "socket.io-client": "^4.7.2", "uglifyjs-webpack-plugin": "^1.2.4", "vconsole": "^3.3.0", "vue": "2.6.11", "vue-clipboard2": "^0.2.1", "vue-i18n": "^8.14.1", "vue-markdown": "^2.2.4", "vue-qr": "^2.3.0", "vue-qriously": "^1.1.1", "vue-router": "^2.7.0", "vuedraggable": "^2.14.1", "vuex": "^2.4.0", "weixin-js-sdk": "^1.6.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.2.0", "@vue/cli-plugin-eslint": "~4.2.0", "@vue/cli-plugin-router": "~4.2.0", "@vue/cli-plugin-vuex": "~4.2.0", "@vue/cli-service": "~4.2.0", "@vue/eslint-config-standard": "^5.1.0", "@vue/test-utils": "^1.1.3", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "^10.1.0", "babel-jest": "^26.6.3", "babel-plugin-import": "^1.13.0", "cz-conventional-changelog": "^3.1.0", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.1", "eslint-plugin-node": "^11.0.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "flush-promises": "^1.0.2", "hard-source-webpack-plugin": "^0.13.1", "husky": "^4.2.3", "jest": "^26.6.3", "jest-sonar-reporter": "^2.0.0", "less": "^3.11.1", "less-loader": "^5.0.0", "lodash": "^4.17.21", "node-sass": "^4.14.1", "postcss-import": "^12.0.1", "postcss-write-svg": "^3.0.1", "sass-loader": "^8.0.2", "script-ext-html-webpack-plugin": "^2.1.4", "vue-jest": "^3.0.7", "vue-template-compiler": "2.6.11"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["ie >= 9", "iOS >= 6", "last 5 versions"], "license": "ISC", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "husky": {"hooks": {"pre-commit": "lint-staged && npm run test"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --ext .js,.vue --max-warnings 0"], "public-library/**/*.{js,vue}": ["eslint --ext .js,.vue --max-warnings 0"]}, "jestSonar": {"reportFile": "test-report.xml", "indent": 4}}