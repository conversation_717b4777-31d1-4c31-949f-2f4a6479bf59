<template>
    <div class="wps">
        <div class="wps-box">
            <div class="wps-container" ref="editorContainer">
                <el-upload
                    :action="uploadAjaxUrl"
                    :on-success="handleSuccess"
                    :show-file-list="false"
                    v-show="!fileId"
                >
                    <button class="btn-type-two" style="margin: 20px">上传文档</button>
                </el-upload>
            </div>
            <div class="wps-opt">
                <el-radio-group v-model="replaceType">
                    <el-radio :label="1">文本替换</el-radio>
                    <el-radio :label="2">位置替换</el-radio>
                </el-radio-group>
                <el-form ref="form" label-width="200px" label-position="top">
                    <el-form-item label="被替换的文本" v-if="replaceType === 1">
                        <el-input v-model="formData.key"></el-input>
                    </el-form-item>
                    <template v-if="replaceType === 2">
                        <el-form-item label="起始位置">
                            <el-input type="number" v-model="formData.start"></el-input>
                        </el-form-item>
                        <el-form-item label="结束位置">
                            <el-input type="number" v-model="formData.end"></el-input>
                        </el-form-item>
                    </template>
                    <el-form-item label="替换的文本">
                        <el-input v-model="formData.value"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="findText">查找</el-button>
                        <el-button type="primary" @click="replaceText">替换</el-button>
                    </el-form-item>
                </el-form>
            </div>
        </div>
    </div>
</template>

<script>
import { mapGetters } from 'vuex';
import WebOfficeSDK from 'js/web-office-sdk-solution-v2.0.7.es.js';
export default {
    data() {
        return {
            editor: null,
            wpsApplication: null,
            // fileId: '3794953070459300870',
            fileId: this.$route.query.fileId,
            formData: {
                key: '',
                value: '替换的文本',
                start: 1,
                end: 1,
            },
            replaceType: 1,
        };
    },
    computed: {
        ...mapGetters(['getUserId']),
        uploadAjaxUrl() {
            return `/web/hubble/multi-version-file/init?creatorId=${this.getUserId}`;
        },
    },
    methods: {
        async findText() {
            if (!this.wpsApplication) {
                return this.$MessageToast.error('请先加载文档');
            }
            const findResult = await this.wpsApplication.ActiveDocument.Find.Execute(this.formData.key);
            console.log('findResult', findResult);
            const { pos } = findResult[0]
            if (pos) {
                const range = await this.wpsApplication.ActiveDocument.Range(pos, pos);
                await this.wpsApplication.ActiveDocument.ActiveWindow.ScrollIntoView(range);
            }
        },
        async replaceText() {
            await this.editor.ready();
            if (!this.wpsApplication) {
                return this.$MessageToast.error('请先加载文档');
            }
            const { key, value, start, end } = this.formData;
            try {
                if (this.replaceType === 1) {
                    this.wpsApplication.ActiveDocument.ReplaceText([
                        { key, value },
                    ]);
                } else if (this.replaceType === 2) {
                    const range = await this.wpsApplication.ActiveDocument.Range(start, end);

                    range.Text = value;  // 替换内容
                }
            } catch (error) {
                console.log(error);
            }
        },
        handleSuccess(data) {
            this.fileId = data.multiVersionFileId;
            this.initWpsEditor();
        },
        async initWpsEditor() {
            console.log(this.fileId);

            const _this = this;
            this.editor = WebOfficeSDK.init({
                officeType: WebOfficeSDK.OfficeType.Writer,
                appId: 'SX20250206RYNEAG',
                fileId: _this.fileId + '',
                mode: 'simple',
                mount: _this.$refs.editorContainer,
                isManual: true,
                isListenResize: true, // 默认跟随浏览器大小
                Cooperation: false,
                // mode: 'simple', //简洁模式 没有菜单
                token: _this.$cookie.get('access_token'), // 登录时的token
                commonOptions: {
                    isShowTopArea: false, // 隐藏顶部区域（头部和工具栏）
                    isShowHeader: false, // 隐藏头部区域
                    isBrowserViewFullscreen: false, // 是否在浏览器区域全屏true 不允许全屏
                    isIframeViewFullscreen: false, // 是否在 iframe 区域内全屏true 不允许全屏
                    acceptVisualViewportResizeEvent: false, // 控制 WebOffice 是否接受外部的 VisualViewport
                },
                wordOptions: {
                    isShowDocMap: false,
                    isBestScale: true,
                },
            });
            await this.editor.ready();
            const app = this.editor.Application;
            this.wpsApplication = this.editor.Application;
            await (this.editor.Application.ActiveDocument.TrackRevisions = true);
            const range = await this.wpsApplication.ActiveDocument.GetDocumentRange();
            console.log('rangeText', await range.Text);
            const Value = await app.ActiveDBSheet.View.GetScrollXMax()
            console.log(Value)
            await app.ActiveDBSheet.View.SetScrollXPos({
                X: Value,
            });
            // const revisions = await this.wpsApplication.ActiveDocument.Revisions
            // 获取全文修订内容
            // const revisionData = await revisions.Json();
            // console.log(revisionData)
            // const app = this.editor.Application;
            // const findResult = await app.ActiveDocument.Find.Execute('JavaScript');
            // const { pos, len } = findResult[0];  // 获取文本位置
            // const range = await app.ActiveDocument.Range(pos, pos + len);
            // // range.Text = '替换的文本';  // 替换内容
        },
    },
    beforeDestroy() {
        if (this.editor) {
            this.wpsApplication = null;
            this.editor.destroy();
        }
    },
    mounted() {
        this.initWpsEditor();
    },
};
</script>

<style lang='scss'>
.wps {
    &-box{
        height: 100vh;
        display: flex;
    }
    &-container{
        flex: 1;
        height: 100%;
    }
    &-opt{
        width: 40%;
        height: 100%;
        padding-left: 80px;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }
}
</style>
