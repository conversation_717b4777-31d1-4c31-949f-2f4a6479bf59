<!-- 403页 -->
<template>
    <Error class="forbidden-page">
        <p class="p1">403</p>
        <p class="p2">无法访问此页面</p>
    </Error>
</template>
<script>
import Error from './common/Error.vue';
export default {
    components: {
        Error,
    },
};
</script>
<style lang="scss">
	.forbidden-page {
		p {
			text-align: center;
		}
		.p1 {
			font-size: 80px;
			color: #4e4e4e;
		}
		.p2 {
			font-size: 14px;
			color: #bababa;
		}
	}
</style>
