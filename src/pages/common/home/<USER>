<template>
    <!-- 如果身份未认证，发起签约时提示 -->
    <el-dialog
        :title="$t('home.tip')"
        :visible.sync="params.visible"
        size="tiny"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :before-close="handleClose"
        class="home-dialog-unverifyConfirm el-dialog-bg"
    >
        <div>
            <h4>
                {{
                    isGroupProxyAuthStatus
                        ? $t("home.isGroupProxyAuth")
                        : `${userNameText}${$t("home.unverify")}`
                }}
            </h4>
        </div>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary"
                @click="handleToAuth"
            >{{ $t("home.goAuthenticate") }}{{ userType }}</el-button>
        </div>
    </el-dialog>
</template>

<script>
import authPath from 'src/common/utils/authPath';
export default {
    // eslint-disable-next-line
    props: ['params', 'isGroupProxyAuthStatus'],
    data() {
        return {};
    },
    computed: {
        authUrl() {
            return this.$cookie.get('sino-russia') === '1'
                ? '/enterprise/authentication/sino-russia'
                : '/enterprise/authentication';
        },
        authLink() {
            return this.$store.getters.getUserType === 'Enterprise'
                ? this.authUrl
                : authPath();
        },
        userType() {
            return this.$store.getters.getUserType === 'Enterprise'
                ? this.$t('home.enterprise')
                : '';
        },
        userNameText() {
            return this.$store.getters.getUserType === 'Enterprise'
                ? this.$t('home.theEnterprise')
                : this.$t('home.you'); // '该企业' : '您'
        },
    },
    methods: {
        handleToAuth() {
            this.$router.push(this.authLink);
        },
        handleClose() {
            this.$emit('close', false);
        },
    },
};
</script>

<style lang="scss">
.home-dialog-unverifyConfirm {
  .el-dialog {
    width: 432px;

    .el-dialog__body h4 {
      line-height: 25px;
    }
  }
}
</style>
