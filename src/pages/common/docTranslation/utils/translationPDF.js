
import * as pdfjsLib from 'pdfjs-dist/build/pdf.js';
import workerSrc from 'pdfjs-dist/build/pdf.worker.entry.js';
import 'pdfjs-dist/web/pdf_viewer.css';
import 'pdfjs-dist/web/pdf_viewer.js';
pdfjsLib.workerSrc = workerSrc;
pdfjsLib.cMapPacked = true;

class TranslationPDF {
    constructor() {
        this.scale = 0;
        this.pdfMaxWidth = 0;
        this.DPR = 2;
        this.direction = 'left';
        this.translateRecords = [];
        this.fileName = '';
        this.updatHoverSentence = () => {
        };
    }
    // pdf页面渲染
    async renderPDF(pdf, num) {
        const page = await pdf.getPage(num);
        const viewport = page.getViewport({ scale: this.DPR });
        const pageDiv = document.querySelector(`#${this.direction}-page-${num}`) || (() => {
            const newPageDiv = document.createElement('div');
            newPageDiv.setAttribute('id', `${this.direction}-page-${num}`);
            newPageDiv.setAttribute('class', 'document-page');
            const pdfContainer = document.querySelector(`.doc-translation__doc-${this.direction} .pdfContainer`);
            pdfContainer.appendChild(newPageDiv);
            const canvas = document.createElement('canvas');
            newPageDiv.appendChild(canvas);
            return newPageDiv;
        })();
        const canvas = pageDiv.querySelector('canvas');
        const context = canvas.getContext('2d');
        canvas.height = viewport.height;
        canvas.width = viewport.width;
        this.pdfMaxWidth = Math.max(this.pdfMaxWidth, viewport.width / this.DPR);
        pageDiv.style.height = `${viewport.height / this.DPR}px`;
        pageDiv.style.width = `${viewport.width / this.DPR}px`;
        canvas.style.height = `${viewport.height / this.DPR}px`;
        canvas.style.width = `${viewport.width / this.DPR}px`;
        const renderContext = {
            canvasContext: context,
            viewport,
        };
        await page.render(renderContext).promise;
        const textContent = await page.getTextContent();
        await this.renderTextLayer(pageDiv, textContent, page);
    }
    // pdf获取
    async getDocument(direction, url) {
        this.pdfMaxWidth = 0;
        this.direction = direction;
        const pdf = await this.loadFile(url);
        for (let i = 1; i <= pdf._pdfInfo.numPages; i++) {
            await this.renderPDF(pdf, i);
        }
        const textLayerDivList = document.querySelector(`.doc-translation__doc-${direction}`);
        this.scale = textLayerDivList.clientWidth / this.pdfMaxWidth;
    }
    loadFile(url) {
        return new Promise((resolve, reject) => {
            const _this = this;
            const xhr = new XMLHttpRequest();
            xhr.open('GET', url, true);
            xhr.responseType = 'blob';
            xhr.send(null);
            async function onload() {
                if (xhr.status === 200) {
                    let filename = '';
                    const disposition = xhr.getResponseHeader('Content-Disposition');
                    if (disposition && disposition.indexOf('attachment') !== -1) {
                        const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                        const matches = filenameRegex.exec(disposition);
                        if (matches != null && matches[1]) {
                            filename = matches[1].replace(/['"]/g, '');
                        }
                    }
                    _this.fileName = decodeURIComponent(filename);
                    // 使用 PDF.js 加载 PDF 文件并进行其他操作
                    const pdf = await pdfjsLib.getDocument(window.URL.createObjectURL(xhr.response)).promise;
                    resolve(pdf);
                } else {
                    reject();
                    console.error('Failed to load PDF:', xhr.status);
                }
            }
            xhr.onload = onload;
        });
    }
    changeScale() {
        const textLayerDivList = document.querySelector(`.doc-translation__doc-${this.direction}`);
        this.scale = textLayerDivList.clientWidth / this.pdfMaxWidth;
    }
    // text层渲染
    renderTextLayer(pageDiv, textContent, page) {
        const canvas = pageDiv.querySelector('canvas');
        const textLayerDiv = pageDiv.querySelector('.textLayer') || (() => {
            const newDiv = document.createElement('div');
            pageDiv.appendChild(newDiv);
            return newDiv;
        })();
        pageDiv.appendChild(textLayerDiv);
        const textLayerDivWidth = canvas.style.width;
        const textLayerDivHeight = canvas.style.height;
        textLayerDiv.setAttribute('class', 'textLayer');
        textLayerDiv.setAttribute('style', `width:${textLayerDivWidth};height:${textLayerDivHeight}`);
        if (textLayerDiv) {
            textLayerDiv.innerHTML = '';
            pdfjsLib.renderTextLayer({
                textContent,
                container: textLayerDiv,
                viewport: page.getViewport({ scale: 1 }),
            });
            const sentenceDiv = document.createElement('div');
            sentenceDiv.setAttribute('class', 'sentence');
            textLayerDiv.appendChild(sentenceDiv);
        }
    }
    // 监听hover处理高亮
    handleHover(updatHoverSentence, translateRecords) {
        this.updatHoverSentence = updatHoverSentence;
        this.translateRecords = translateRecords;
        const textLayerDivList = document.querySelector(`.doc-translation__doc-${this.direction}`);
        textLayerDivList.addEventListener('mousemove', (event) => this.mouseMove(event));
        textLayerDivList.addEventListener('mouseout', () => this.unHighlight('lis'));
    }
    // 鼠标移动hover处理
    mouseMove(event) {
        const documentPage = event.target.closest('.document-page');
        if (documentPage) {
            const page = Number(documentPage.id.split('-')[2]);
            // const sentence = documentPage.querySelector('.sentence');
            // this.unHighlight();
            const dom = documentPage.getBoundingClientRect();
            const left = ((event.x - dom.left) / dom.width).toFixed(4);
            const top = ((event.y - dom.top) / dom.height).toFixed(4);
            // const key = {
            //     left: 'source_coordinate',
            //     right: 'target_coordinate',
            // }[this.direction];
            this.translateRecords.forEach((coord, index) => {
                coord.contentPositions.forEach((item) => {
                    const { x, y, width, height } = item;
                    if ((item.page === page) && (left >= x && left <= x + width) && (top >= y && top <= y + height)) {
                        this.updatHoverSentence(index, true);
                    }
                });
            });
        }
    }
    // 高亮
    highlight(id) {
        this.unHighlight('watch');
        // const key = {
        //     left: 'source_coordinate',
        //     right: 'target_coordinate',
        // }[this.direction];
        const coord = this.translateRecords[id];
        coord.contentPositions.forEach((item) => {
            const pageTextLayer = document.querySelector(`#${this.direction}-page-${item.page}`).querySelector('.textLayer');
            const sentence = pageTextLayer.querySelector('.sentence');
            sentence.classList.add('highlight');
            sentence.setAttribute('style', `width:${(item.width * 100).toFixed(2)}%;height: ${(item.height * 100).toFixed(2)}%;left: ${(item.x * 100).toFixed(2)}%;top:${(item.y * 100).toFixed(2)}%`);
        });
    }
    scroll(id) {
        const coord = this.translateRecords[id];
        const pageDiv = document.querySelector(`#${this.direction}-page-${coord.page}`);
        const pageTextLayer = pageDiv.querySelector('.textLayer');

        const scrollHeight = Math.round(document.querySelector(`.doc-translation__content-${this.direction} .doc-translation__doc`).scrollTop / this.scale);
        console.log(scrollHeight, pageDiv.offsetTop, pageDiv.offsetTop + pageDiv.clientHeight);
        if (scrollHeight < pageDiv.offsetTop ||  (pageDiv.offsetTop + pageDiv.clientHeight) <= scrollHeight) {
            // 滚动到hover页可见
            pageTextLayer.scrollIntoView({
                behavior: 'smooth',
            });
        }
    }
    // 取消高亮
    unHighlight(from) {
        const highlights = document.querySelectorAll(`.doc-translation__doc-${this.direction} .highlight`);
        highlights.length && highlights.forEach((node) => node.classList.remove('highlight'));
        from !== 'watch' && this.updatHoverSentence(-1);
    }
    // 取消监听事件
    removeListener() {
        const textLayerDivList = document.querySelector(`.doc-translation__doc-${this.direction}`);
        textLayerDivList.removeEventListener('mousemove', (event) => this.mouseMove(event));
        textLayerDivList.removeEventListener('mouseout', () => this.unHighlight());
    }
}

export default TranslationPDF;
