class TranslationHTML {
    constructor() {
        this.direction = 'right';
        this.translateRecords = [];
        this.updatHoverSentence = () => {
        };
    }
    // 监听hover处理高亮
    handleHover(updatHoverSentence, translateRecords) {
        this.updatHoverSentence = updatHoverSentence;
        this.translateRecords = translateRecords;
        const textLayerDivList = document.querySelector(`.doc-translation__doc-${this.direction}`);
        textLayerDivList.addEventListener('mousemove', (event) => this.mouseMove(event));
        textLayerDivList.addEventListener('mouseout', () => this.unHighlight('lis'));
    }
    // 鼠标移动hover处理
    mouseMove(event) {
        const sentence = event.target.closest('.text-sentence');
        sentence && this.updatHoverSentence(sentence.getAttribute('data-index'));
        // if (sentence) {
        //     this.updatHoverSentence(sentence.getAttribute('data-index'), true);
        // }
    }
    // 高亮
    highlight(id) {
        this.unHighlight('watch');
        const sentence = document.querySelectorAll(`.doc-translation__doc-${this.direction} p`)[id];
        sentence.classList.add('highlight');
    }
    scroll(id) {
        const sentence = document.querySelectorAll(`.doc-translation__doc-${this.direction} p`)[id];
        const scrollDom = document.querySelector(`.doc-translation__content-${this.direction} .doc-translation__doc`);
        if (sentence.offsetTop <= scrollDom.scrollTop || sentence.offsetTop + sentence.clientHeight >= (scrollDom.scrollTop + scrollDom.clientHeight)) {
            // 滚动到hover页可见
            sentence.scrollIntoView({
                behavior: 'smooth',
            });
        }
    }
    // 取消高亮
    unHighlight(from) {
        const highlight = document.querySelector(`.doc-translation__doc-${this.direction} .highlight`);
        highlight && highlight.classList.remove('highlight');
        from !== 'watch' && this.updatHoverSentence(-1);
    }
    // 取消监听事件
    removeListener() {
        const textLayerDivList = document.querySelector(`.doc-translation__doc-${this.direction}`);
        textLayerDivList.removeEventListener('mousemove', (event) => this.mouseMove(event));
        textLayerDivList.removeEventListener('mouseout', () => this.unHighlight());
    }
}

export default TranslationHTML;
