<template>
    <div>
        <Header ref="header"
            :config="headerConfig"
        ></Header>
        <div class="doc-translation-content">
            <div class="doc-translation-content-box">
                <DocLeft
                    @newTranslation="newTranslation"
                    @checkInitTask="checkInitTask"
                    ref="left"
                ></DocLeft>
                <DocRight
                    ref="right"
                    :translateRecords="translateRecords"
                    @updateTranslateRecords="updateTranslateRecords"
                    @handleHover="handleHover"
                ></DocRight>
            </div>
            <SideBar ref="sideBar" @goHistory="goHistory"></SideBar>
        </div>
        <RegisterFooter v-if="isPC" class="login-footer absolute-login-footer"></RegisterFooter>
    </div>
</template>
<script>
import DocLeft from './docLeft/index.vue';
import DocRight from './docRight/index.vue';
import SideBar from './sideBar/index.vue';
import Header from 'components/hubbleApplyHeader';
import RegisterFooter from 'components/register_footer/RegisterFooter.vue';
import { getHistoryDetail, initTask } from 'src/api/docTranslation.js';
import { isPC } from 'src/common/utils/device.js';
import { mapMutations, mapState } from 'vuex';
export default {
    components: {
        Header,
        DocLeft,
        DocRight,
        SideBar,
        RegisterFooter,
    },
    data() {
        return {
            hoverSentence: -1,
            headerConfig: { productType: 16, toolType: '翻译' },
            translateRecords: [],
            isPC: isPC(),
            needNewTranslationId: true,
        };
    },
    computed: {
        ...mapState('docTranslation', ['translationId', 'translationStatus']),
    },
    watch: {
        hoverSentence(val) {
            if (val > -1) {
                this.$refs.left.translationPDF.highlight(val);
                this.$refs.right.translationHTML.highlight(val);
            } else {
                this.$refs.left.translationPDF.unHighlight();
                this.$refs.right.translationHTML.unHighlight();
            }
        },
    },
    methods: {
        ...mapMutations('docTranslation', ['setTranslationId']),
        // 更新hover的句子
        updatHoverSentence(val, needScroll) {
            this.hoverSentence = val;
            if (needScroll && val > -1) {
                this.$refs.right.translationHTML.scroll(val);
            }
        },
        // // 同步滚动
        // onDocScroll(top) {
        //     const scrollDom = document.querySelector('.doc-translation__content-left .doc-translation__doc');
        //     scrollDom.scrollTo({
        //         top,
        //         behavior: 'smooth',
        //     });
        // },
        updateTranslateRecords(list) {
            this.translateRecords = list;
        },
        // 处理hover高亮
        handleHover() {
            if (this.$refs.sideBar.activeTabName === 'records') {
                this.$refs.sideBar.translationRecords = [];
                this.$refs.sideBar.getHistory(); // 更新历史记录
            } else if (this.$refs.sideBar.activeTabName === 'currentRecords') {
                this.$refs.sideBar.currentTranslationRecords = [];
                this.$refs.sideBar.getCurrentHistory(); // 更新当前文档历史记录
            }
            this.$refs.left.translationPDF.handleHover(this.updatHoverSentence, this.translateRecords);
            this.$refs.right.translationHTML.handleHover(this.updatHoverSentence, this.translateRecords);
        },
        // 重新上传
        newTranslation() {
            this.$refs.left.reset();
            this.$refs.right.reset();
        },
        // 重新初始化翻译任务
        checkInitTask() {
            if (this.translationStatus === 2) {
                this.initTranslateTask();
            }
            if (this.needNewTranslationId && this.translationStatus === 0 && this.$route.params.contractId) {
                this.needNewTranslationId = false;
                this.initTranslateTask();
            }
            this.newTranslation();
        },
        // 展示历史记录
        async goHistory(translationRecordId, translationStatus) {
            this.setTranslationId(translationRecordId);
            const loading = this.$loading();
            try {
                const { data: { translatedParagraphs, translationLanguage, ifUseTermBase, translationStyle } } =  await getHistoryDetail(translationRecordId);
                this.newTranslation();
                await this.$refs.left.getDocument();
                await this.$refs.right.handleResult({ translatedParagraphs, translationRecordId, translationLanguage, ifUseTermBase, translationStyle }, true, translationStatus);
                loading.close();
            } catch (error) {
                loading.close();
            }
        },
        // async  initLeftDocument() {
        //     const { contractId, documentId } = this.$route.params;
        //     if (contractId && documentId) {
        //         const loading = this.$loading();
        //         try {
        //             await uploadFromContract(this.translationId, contractId, documentId);
        //             await this.$refs.left.getDocument();
        //             loading.close();
        //         } catch (error) {
        //             loading.close();
        //         }
        //     }
        // },
        initTranslateTask() {
            const loading = this.$loading();
            try {
                initTask().then((res) => {
                    this.setTranslationId(res.data.translationId);
                    loading.close();
                });
            } catch (error) {
                loading.close();
            }
        },
    },
    mounted() {
        const { contractId, documentId } = this.$route.params;
        if (contractId && documentId) {
            return;
        } else {
            this.initTranslateTask();
        }
    },
    beforeDestroy() {
        sessionStorage.removeItem('fromDocument');
        sessionStorage.removeItem('signingPagePath');
        this.$refs.right.reset();
    },
};
</script>
<style lang="scss">
.doc-translation-content{
    display: flex;
    position: absolute;
    top:63px;
    bottom: 35px;
    left:0;
    width: 100%;
    .doc-translation-content-box{
        flex:1;
        background-color: #f6f6f6;
        display: flex;
        overflow: hidden;
        .doc-translation__content{
            width:50%;
            height: 100%;
            display: flex;
            flex-direction: column;
            .content-header{
                height: 34px;
                background: #fff;
                box-shadow: 0 0 8px 0 #0000001a;
                font-size:14px;
                line-height: 34px;
                padding: 10px 20px;
                .el-button--default{
                    padding: 0 21px;
                    border-color: #ccc;
                    background: #f8f8f8;
                    height: 30px;
                    &:hover{
                        border-color: #ccc;
                        background: #fff;
                    }

                    span{
                        font-size: 14px;
                        color: #333;
                    }
                }
                .el-button--primary{
                    padding: 0 21px;
                    height: 30px;
                    // background: #0C8AEE;
                    font-weight: normal;
                    border:none;
                    &.is-disabled{
                        // background: #9CCFFF;
                        color: #fff;
                    }
                }

            }
            .doc-translation__doc{
                flex: 1;
                overflow-y: auto;
                overflow-x: hidden;
                margin: 10px;
                .document-page{
                    position: relative;
                }
                .textLayer{
                    position: absolute;
                    top:0;
                    left: 0;
                    span{
                        position: absolute;
                        z-index: 999;
                    }
                }
                .doc-translation__doc-left{
                    height:100%;
                    .sentence{
                        background: #FF5500;
                    }
                }
                .doc-translation__doc-right{
                    .sentence{
                        background: #f5e313;
                    }
                    .highlight{
                        background: #FFF8D8;;
                    }
                    font-size: 14px;
                    line-height: 30px;
                    padding:50px;
                    p{
                        // text-indent: 2rem;
                        white-space: pre-wrap;
                    }
                }
                .pdfContainer{
                    transform-origin: left top;
                    .sentence{
                        z-index: 999;
                        position: absolute;
                        display: none;
                        &.highlight{
                            display: block;
                        }
                    }
                    ::selection {
                        // background:red;
                    }
                }
            }
        }
    }
    .el-button--primary {
        background-color: $theme-color;
        border-color: $theme-color;
        &:hover{
            color: #fff;
            background-color: $btn-primary-hover-color;
            border-color: $btn-primary-hover-color;
        }
    }
}

.absolute-login-footer{
    position: absolute;
    bottom: 0;
}

</style>
