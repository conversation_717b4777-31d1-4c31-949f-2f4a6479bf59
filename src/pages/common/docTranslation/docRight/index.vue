<template>
    <div class="doc-translation__content doc-translation__content-right">
        <div class="content-header">
            {{ $t('docTranslation.docRight.title') }}
            <div class="fr" v-if="originFileReady">
                <el-checkbox class="term-bank" v-model="termBank">{{ $t('docTranslation.docRight.applyTermBank') }}</el-checkbox>
                {{ $t('docTranslation.docRight.translationLang') }}:
                <el-select popper-class="doc-translation__select-dropdown" v-model="lang">
                    <el-option v-for="item in langs" :label="item.label" :value="item.value" :key="item.value">
                    </el-option>
                </el-select>
                {{ translationStyleText }}
                <el-button v-show="translationStatus !== 2" :disabled="translationStatus===1" type="primary" @click="chooseTranslateMode">
                    {{ translationStatus === 2 ? $t('docTranslation.docRight.reTranslation'):$t('docTranslation.docRight.startTranslation') }}</el-button>
            </div>
        </div>
        <div class="doc-translation__doc" v-loading="loading">
            <!-- 翻译结果 -->
            <div v-show="translationFileId && translationStatus===2" class="doc-translation__doc-right">
                <p class="text-sentence" v-for="(item,index) in translateRecords" :key="index" :data-index="index" v-html="item.translatedText">
                </p>
            </div>
            <!-- 翻译完成前tip -->
            <div v-show="translationStatus!==2" class="doc-translation__tip">
                <template v-if="translationStatus === 1">
                    <img src="~/img/docTranslation/translationLoading.gif">
                    <span class="doc-translation__translationing">{{ $t('docTranslation.docRight.translationing') }}</span>
                </template>
                <template v-else>
                    <img src="~/img/docTranslation/translation.png" />
                    <el-button v-if="originFileReady" type="primary" @click="chooseTranslateMode">{{ $t('docTranslation.docRight.startTranslation') }}</el-button>
                    <span v-else class="doc-translation__result-tip">{{ $t('docTranslation.docRight.translateResultTip') }}</span>
                </template>
            </div>
        </div>
        <el-dialog
            :visible="showTranslateMode"
            :modal-append-to-body="true"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            class="doc-translate-choose"
            :before-close="() => showTranslateMode = false"
        >
            <el-button :type="lang !== 'EN_CN' ? 'primary' : 'default'" @click="chooseMode('PROFESSIONAL')">{{ $tc('docTranslation.docRight.translateProfessional', 1) }}</el-button>
            <div class="doc-translate-choose-text">{{ $tc('docTranslation.docRight.translateProfessional', 2) }}</div>
            <el-button :type="lang === 'EN_CN' ? 'primary' : 'default'" @click="chooseMode('COLLOQUIAL')">{{ $tc('docTranslation.docRight.translateColloquial', 1) }}</el-button>
            <div class="doc-translate-choose-text">{{ $tc('docTranslation.docRight.translateColloquial', 2) }}</div>
        </el-dialog>
    </div>
</template>
<script>
import TranslationHTML  from '../utils/translationHTML.js';
import { getCalculateBilling, getTranslateResult, getHistoryDetail, getStatus } from 'src/api/docTranslation.js';
import { mapState, mapMutations } from 'vuex';
export default {
    props: {
        translateRecords: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            lang: 'CN_EN',
            langs: [{
                value: 'EN_CN',
                label: this.$t('docTranslation.docRight.langs.zh'),
            }, {
                value: 'CN_EN',
                label: this.$t('docTranslation.docRight.langs.en'),
            }],
            translationHTML: new TranslationHTML(),
            termBank: true,
            loading: false,
            showTranslateMode: false,
            translationStyle: '',
            translationStyleResult: '',
        };
    },
    computed: {
        ...mapState('docTranslation', ['originFileReady', 'translationFileId', 'translationStatus', 'translationId']),
        translationStyleText() {
            if (!this.translationStyleResult || this.translationStatus !== 2) {
                return '';
            } else {
                return `${this.translationStyleResult === 'PROFESSIONAL' ? this.$tc('docTranslation.docRight.translateProfessional', 1) : this.$tc('docTranslation.docRight.translateColloquial', 1)}`;
            }
        },
    },
    methods: {
        ...mapMutations('docTranslation', ['setTranslationFileId', 'setTranslationStatus']),
        // onDocScroll: debounce(function(e) {
        //     this.$emit('onDocScroll', e.target.scrollTop);
        // }, 200),
        // 翻译结果展示
        // async getDocument(res) {
        //     this.translationStatus = 2;
        //     this.$emit('uploadedFileId', 'translationFileId', res.translateFileId);
        //     const url = `${res.previewUrl}?access_token=${this.$cookie.get('access_token')}`;
        //     await this.translationPDF.getDocument('right', url);
        //     this.translationStatus = 3;
        // },
        chooseMode(mode) {
            this.translationStyle = mode;
            this.showTranslateMode = false;
            this.translate();
        },
        // 展示翻译模式
        chooseTranslateMode() {
            this.showTranslateMode = true;
        },
        // 翻译扣费信息获取
        async  translate() {
            const loading = this.$loading();
            // 扣费提示
            try {
                const { data: res } = await getCalculateBilling(this.translationId);
                loading.close();
                this.confirm(res);
            } catch (error) {
                loading.close();
                console.log(error);
            }
        },
        // 扣费提示
        confirm(res) {
            const { description, enterpriseName, balanceEnough } = res;
            const h = this.$createElement;
            const btnText = balanceEnough ? this.$t('docTranslation.docRight.confirm.ok') : this.$t('docTranslation.docRight.confirm.buy');
            const content = h('p', null, [
                h('p', null, description),
                h('p', { style: 'margin-top: 25px' }, this.$t('docTranslation.docRight.confirm.tip2', { enterpriseName })),
                balanceEnough ? '' : h('p', { style: 'margin-top: 25px' }, this.$t('docTranslation.docRight.confirm.unEnoungh')),
            ]);
            this.$confirm(content, this.$t('docTranslation.docRight.confirm.title'), {
                confirmButtonText: btnText,
                showCancelButton: true,
                customClass: 'doc-translation-confirm',
            }).then(() => {
                if (balanceEnough) {
                    this.doTranslate();
                } else {
                    // 去购买弹框
                    this.$parent.$refs.header.openChargeDialog();
                }
            });
        },
        // 获取翻译结果
        async doTranslate(translationRecordId) {
            let recordId = translationRecordId;
            if (this.translationStatus === 2) {
                this.setTranslationFileId('');
            }
            try {
                this.setTranslationStatus(1);
                if (!recordId) {
                    await getTranslateResult(this.translationId, this.lang, this.termBank, this.translationStyle);
                    recordId = this.translationId;
                }
                const timer = setInterval(async() => {
                    if (this.translationStatus === 0) {
                        clearInterval(timer);
                    } else {
                        const { data: status } = await getStatus(recordId);
                        if (status === 'SUCCESS') {
                            const { data: res } = await getHistoryDetail(recordId);
                            this.handleResult(res);
                            clearInterval(timer);
                        } else  if (status === 'FAILURE') {
                            clearInterval(timer);
                            this.$MessageToast.error(this.$t('docTranslation.docRight.errTip'));
                            this.setTranslationStatus(0);
                        }
                    }
                }, 15000);
            } catch (error) {
                this.setTranslationStatus(0);
                console.log(error);
            }
        },
        // 处理翻译结果并hover
        handleResult({ translatedParagraphs, translationRecordId, translationLanguage, ifUseTermBase, translationStyle }, isHistory, translationStatus) {
            console.log(isHistory);
            if (isHistory) {
                this.lang = translationLanguage;
                this.termBank = ifUseTermBase;
                if (translationStatus === 'FAILURE') {
                    return;
                } else if (['INIT', 'TRANSLATING'].includes(translationStatus)) {
                    return this.doTranslate(translationRecordId);
                }
            }
            this.translationStyleResult = translationStyle;
            this.setTranslationFileId(translationRecordId);
            this.$emit('updateTranslateRecords', translatedParagraphs);
            this.setTranslationStatus(2);
            this.$emit('handleHover');
        },
        destroyed() {
            this.translationHTML.removeListener();
        },
        // 重置
        reset() {
            this.setTranslationFileId('');
            this.setTranslationStatus(0);
            this.destroyed();
        },
    },
};
</script>
<style lang="scss">
.doc-translation__content-right{
    .term-bank.el-checkbox{
        padding-right: 20px;
        .el-checkbox__label{
            font-size: 14px;
        }
    }
    .el-select{
        width: 86px;
        padding: 0 20px 0 4px;
        .el-input {
            .el-icon-caret-top{
                width: 28px;
                font-size: 10px;
                color: #ccc;
            }
            input{
                height: 33px;
                font-size: 12px;
                padding-right: 28px;
                border-radius: 2px;
                border: 1px solid #EEE;
            }
        }
    }
    .doc-translation__doc{
        position: relative;
        background: #fff;
        .doc-translation__tip{
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            img{
                width: 100px;
                display: block;
            }
            .doc-translation__result-tip{
                font-size: 14px;
                color: #cccccc;
                margin-top: 20px;

            }
            .doc-translation__translationing{
                font-size: 14px;
                color: #cccccc;
                margin-top: -10px;
            }
            .el-button{
                height: 34px;
                width: 110px;
                // background: #0C8AEE;
                margin-top: 20px;
            }
        }
    }

}
.doc-translation__select-dropdown{
        min-width: 78px!important;
        .el-select-dropdown__item{
            text-align: center;
            line-height: 14px;
            height: 36px;
            font-size: 12px;
            &.selected{
                background: #fff!important;;
                color: $theme-color!important;
            }
        }
}
.doc-translate-choose {
    .el-dialog--small {
        width: 400px;
    }
    .el-dialog__body {
        text-align: center;
    }
    &-text {
        margin: 12px 0;
        &:first-of-type {
            margin-bottom: 24px;
        }
    }
}
</style>

