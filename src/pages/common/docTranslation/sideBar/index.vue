<template>
    <div class="doc-translation-side-bar">
        <el-tabs v-model="activeTabName">
            <el-tab-pane name="result">
                <span slot="label"> {{ $t('docTranslation.sideBar.toolsTab') }}</span>
                <div class="term-bank">
                    <h2>{{ $t('docTranslation.sideBar.termBank') }}<el-tooltip
                        class="item"
                        effect="dark"
                        placement="top"
                        popper-class="term-bank_tip"
                    >
                        <div slot="content" class="content">{{ $t('docTranslation.sideBar.termBankTip') }}</div>
                        <i class="el-icon-ssq-bangzhu cursor-point"></i>
                    </el-tooltip></h2>
                    <ul>
                        <li><span>{{ $t('docTranslation.sideBar.originaFile') }}</span><span>{{ $t('docTranslation.sideBar.translationFile') }}</span></li>
                        <li v-for="(item,index) in termBankList" :key="item.termId">
                            <template v-if="item.edit">
                                <span><el-input type="text"
                                    v-model="item.originTerm"
                                    :placeholder="$t('docTranslation.sideBar.placeholder')"
                                ></el-input></span>
                                <span><el-input type="text"
                                    :placeholder="$t('docTranslation.sideBar.placeholder')"
                                    v-model="item.targetTerm"
                                ></el-input></span>
                                <i @click="saveTermBank(index)" class="el-icon-ssq-xuanzhongbiaoqian"></i>
                            </template>
                            <template v-else>
                                <span>{{ item. originTerm }}</span>
                                <span>{{ item. targetTerm }}</span>
                                <i class="el-icon-ssq-yijuqian" @click="delTermBank(index)"></i>
                            </template>
                        </li>
                        <li class="term-add" v-if="showAdd"><span @click="addTermBank">+{{ $t('docTranslation.sideBar.addTermBank') }}</span></li>
                    </ul>
                </div>

            </el-tab-pane>
            <el-tab-pane name="records">
                <span slot="label">{{ $t('docTranslation.sideBar.historyTab') }}</span>
                <div class="history-list">
                    <ul v-if="translationRecords.length" @scroll="handleScroll">
                        <div class="list">
                            <li v-for="(item,index) in translationRecords" :key="index" @click="goHistory(item)">
                                <p>
                                    <el-tooltip :content="item.title" placement="top-start">
                                        <span>{{ item.title }}</span>
                                    </el-tooltip>
                                </p>
                                <span class="time">{{ item.createTime }}</span>
                                <img :src="statusImgs[item.translationStatus]">
                            </li>
                        </div>
                    </ul>
                    <NoData v-else />
                    <el-button v-if="translationFileId" type="primary" @click="downloadTranslation">{{ $t('docTranslation.sideBar.download') }}</el-button>
                </div>
            </el-tab-pane>
            <el-tab-pane name="currentRecords" v-if="$route.params.contractId">
                <span slot="label">{{ $t('docTranslation.sideBar.currentHistoryTab') }}</span>
                <div class="history-list">
                    <ul v-if="currentTranslationRecords.length">
                        <div class="list">
                            <li v-for="(item,index) in currentTranslationRecords" :key="index" @click="goHistory(item)">
                                <p>
                                    <el-tooltip :content="item.title" placement="top-start">
                                        <span>{{ item.title }}</span>
                                    </el-tooltip>
                                </p>
                                <span class="time">{{ item.createTime }}</span>
                                <img :src="statusImgs[item.status]">
                            </li>
                        </div>
                    </ul>
                    <NoData v-else />
                    <!-- <el-button v-if="translationFileId" type="primary" @click="downloadTranslation">{{ $t('docTranslation.sideBar.download') }}</el-button> -->
                </div>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>
<script>
import NoData from 'components/noData';
import { getHistory, getTerms, addTerms, deleteTerm, currentDocumentHistory } from 'src/api/docTranslation.js';
import { mapState } from 'vuex';
export default {
    components: {
        NoData,
    },
    data() {
        return {
            activeTabName: 'result',
            termBankList: [],
            translationRecords: [],
            currentTranslationRecords: [],
            statusImgs: {
                'SUCCESS': require('img/docTranslation/<EMAIL>'),
                'FAILURE': require('img/docTranslation/<EMAIL>'),
                'TRANSLATING': require('img/docTranslation/<EMAIL>'),
                'INIT': require('img/docTranslation/<EMAIL>'),
            },
            showAdd: true,
            page: 1,
            totalPage: 1,
            loadMore: false,
        };
    },
    computed: {
        ...mapState('docTranslation', ['originFileId', 'translationFileId']),
    },
    watch: {
        activeTabName(val) {
            this.page = 1;
            this.totalPage = 1;
            if (val === 'records') {
                this.translationRecords = [];
                this.getHistory();
            }
            if (val === 'currentRecords') {
                this.currentTranslationRecords = [];
                this.getCurrentHistory();
            }
        },
    },
    methods: {
        // 增加词条
        addTermBank() {
            this.termBankList.push({ originTerm: '', targetTerm: '', edit: true });
            this.showAdd = false;
        },
        // 下载翻译
        downloadTranslation() {
            Vue.$http.get(`/web/hubble/translations/file/${this.translationFileId}/download-translation`).then(() => {
                window.open(`/web/hubble/translations/file/${this.translationFileId}/download-translation`, '_self');
            });
        },
        // 删除词条
        delTermBank(i) {
            deleteTerm(this.termBankList[i].termId).then(() => {
                this.$MessageToast.success('删除成功！');
                this.termBankList.splice(i, 1);
            });
        },
        // 保存新词条
        saveTermBank(i) {
            addTerms(this.termBankList[i]).then(() => {
                this.$MessageToast.success('添加成功！');
                this.getTerms();
                this.showAdd = true;
            });
        },
        // 获取历史记录
        getHistory() {
            getHistory(this.page).then(({ data: { translationRecords, totalPages }  }) => {
                this.totalPage = totalPages;
                this.translationRecords = this.translationRecords.concat(translationRecords);
                this.loadMore = true;
            });
        },
        // 获取术语库
        getTerms() {
            getTerms().then(({ data: list }) => {
                this.termBankList = list;
            });
        },
        // 跳转历史翻译
        goHistory(history) {
            if (this.activeTabName === 'records') {
                this.$emit('goHistory', history.translationRecordId, history.translationStatus);
            } else {
                this.$emit('goHistory', history.id, history.status);
            }
        },
        // 获取当前合同的历史记录
        getCurrentHistory() {
            const { contractId, documentId } = this.$route.params;
            currentDocumentHistory(contractId, documentId, 'TRANSLATION').then(({ data: { result } }) => {
                this.currentTranslationRecords = result;
            });
        },
        handleScroll(event) {
            const { scrollTop, clientHeight } = event.target;
            const listHeight = this.$el.querySelector('.list').offsetHeight;
            if (listHeight - (scrollTop + clientHeight) < 20 && this.page < this.totalPage && this.loadMore) { // 距离底部20px时加载更多
                this.loadMore = false;
                this.page += 1;
                this.getHistory();
            }
        },
    },
    created() {
        // this.getHistory();
        this.getTerms();
    },
};
</script>
<style lang="scss" scoped>
.doc-translation-side-bar::v-deep .el-tabs__item {
    padding: 0 4px;
}
</style>
<style lang="scss">
.doc-translation-side-bar{
    width: 240px;
    .el-tabs{
        height: 100%;
        display: flex;
        flex-direction: column;
        .el-tabs__header{
            height: 54px;
            box-shadow: 0 0 8px 0 #0000001a;
            border-color: #eee;
            margin: 0;
            .el-tabs__item{
                height: 54px;
                line-height: 54px;
            }
        }
        .el-tabs__content{
            flex:1;
            .el-tab-pane{
                height:100%;
                overflow: auto;
                .el-button{
                    position: absolute;
                    bottom: 0;
                    margin: 20px;
                    width: 200px;
                    height: 34px;
                    // background: #0C8AEE;
                }
            }
            .term-bank{
                padding: 0 15px;
                h2{
                    padding:19px 0 10px 0;
                    font-size: 14px;
                    color: #000;
                    font-weight: bold;
                    .el-tooltip{
                        padding-left: 4px;
                        color: #ccc;
                    }
                }
                ul{
                    width: 190px;
                    border-top: 1px solid #eee;
                    border-left: 1px solid #eee;

                    li{
                        border-bottom: 1px solid #eee;
                        font-size: 12px;
                        color:#333;
                        position: relative;
                        display: flex;
                        i{
                            position: absolute;
                            right: -18px;
                            font-size: 14px;
                            color: $theme-color;
                            line-height: 40px;
                            cursor: pointer;
                        }
                        &:first-child{
                            background: #F8F8F8;
                        }
                        span{
                            width: 95px;
                            display: inline-block;
                            border-right:1px solid #eee;
                            padding: 10px 15px;
                            box-sizing: border-box;
                            line-height: 18px;
                        }
                        input{
                            border: none;
                            box-shadow: none!important;
                            padding: 0;
                        }
                        &.term-add{
                            span{
                                color: $theme-color;
                                width: 100%;
                                cursor: pointer;
                            }
                        }

                    }
                }
            }
            .history-list{
                padding-top:10px;
                box-sizing: border-box;
                padding-bottom: 74px;
                height: 100%;
                ul{
                    height: 100%;
                    overflow: auto;
                    li{
                        height: 42px;
                        padding: 18px 20px;
                        line-height: 14px;
                        position: relative;
                        &:hover{
                            background: #EFF6FF;
                            cursor: pointer;
                        }
                        p{
                            padding-bottom: 10px;
                            span{
                                text-overflow: ellipsis;
                                overflow: hidden;
                                white-space: nowrap;
                                font-size: 14px;
                                display:inline-block;
                                max-width: 100%;
                            }
                        }
                        .time{
                            color: #999999;
                            font-size: 12px;
                        }
                        img{
                            position: absolute;
                            bottom: 18px;
                            right: 20px;
                            height: 14px;
                        }
                        &:after{
                            display: block;
                            content: '';
                            height: 1px;
                            width: 200px;
                            border-bottom: 1px solid #eee;
                            position: absolute;
                            bottom: 0;
                            left:20px;
                        }
                    }
                }

            }
        }
    }

}
.term-bank_tip{
    .content{
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        color: #ffffff;
        width: 390px;
    }
}
</style>
