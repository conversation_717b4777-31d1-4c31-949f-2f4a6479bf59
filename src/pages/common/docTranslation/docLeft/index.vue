<template>
    <div class="doc-translation__content doc-translation__content-left">
        <div class="content-header">{{ $t('docTranslation.docLeft.title') }}<span v-if="originFileStatus===2">：{{ originFileName }}</span> <div class="fr">
            <el-button @click="newTranslation" :disabled="translationStatus===1" v-if="originFileStatus===2" type="default">{{ $t('docTranslation.docLeft.reUpload') }}</el-button></div>
        </div>
        <div class="doc-translation__doc" v-loading="loading">
            <div v-show="originFileStatus>0" class="doc-translation__doc-left">
                <div ref="pdfContainerLeft"
                    class="pdfContainer"
                    :style="{'transform': `scale(${translationPDF.scale})`}"
                ></div>
            </div>
            <Upload
                :uploadUrl="uploadUrl"
                v-show="originFileStatus===0"
                ref="upload"
                drag
                class="drag-upload"
                @onUploadSuccess="getDocument"
                @changeLoading="(val)=> loading=val"
            >
            </Upload>
        </div>
    </div>
</template>
<script>
import TranslationPDF  from '../utils/translationPDF';
import Upload from './Upload.vue';
import { mapMutations, mapState } from 'vuex';
import { initHubbleTask } from 'src/api/docTranslation.js';

export default {
    components: {
        Upload,
    },
    data() {
        return {
            originFileName: '',
            loading: false,
            translationPDF: new TranslationPDF(),
            originFileStatus: 0, // 0 未上传  1.已完成上传处理  2.上传完成
            pdfUrl: '',
        };
    },
    computed: {
        ...mapState('docTranslation', ['translationStatus', 'translationId']),
        uploadUrl() {
            return `/web/hubble/translations/${this.translationId}/common-file`;
        },
    },
    methods: {
        ...mapMutations('docTranslation', ['setOriginFileReady', 'setTranslationId']),
        // 处理pdf文档展示
        async getDocument() {
            this.loading = true;
            this.originFileStatus = 1;
            let url = '';
            if (this.pdfUrl) {
                url = `/web/hubble${this.pdfUrl}?access_token=${this.$cookie.get('access_token')}`;
            } else {
                url = `/web/hubble/translations/${this.translationId}/file?access_token=${this.$cookie.get('access_token')}`;
            }
            await this.translationPDF.getDocument('left', url);
            this.originFileName = this.translationPDF.fileName;
            this.loading = false;
            this.originFileStatus = 2;
            this.setOriginFileReady(true);
            this.$refs.upload = false;
            window.addEventListener('resize', this.handleResize);
        },
        // 打开新的翻译窗口
        newTranslation() {
            this.$emit('checkInitTask');
        },
        removeHover() {
            this.translationPDF.removeListener();
        },
        // 重置
        reset() {
            this.setOriginFileReady(false);
            this.pdfUrl = '';
            document.querySelector('.pdfContainer').innerHTML = '';
            this.originFileStatus = 0;
        },
        handleResize() {
            this.translationPDF.changeScale();
        },
        initContractTranslateTask(contractId, documentId, hubbleToolType) {
            return new Promise(resolve => {
                const loading = this.$loading();
                initHubbleTask(contractId, documentId, hubbleToolType).then((res) => {
                    this.setTranslationId(res.data.result.taskId);
                    this.pdfUrl = res.data.result.previewUrl;
                    loading.close();
                    resolve();
                });
            });
        },
    },
    mounted() {
        const { contractId, documentId } = this.$route.params;
        if (contractId && documentId) {
            this.initContractTranslateTask(contractId, documentId, 'TRANSLATION').then(() => {
                this.getDocument();
            });
        }
    },
    destroyed() {
        this.removeHover();
        window.removeEventListener('resize', this.handleResize);
    },
};
</script>
<style lang="scss">
.drag-upload{
   height: 100%;
   box-sizing: border-box;

   .el-upload{
    width: 100%;
    height: 100%;

    .el-upload-dragger{
        width: 100%;
        height: 100%;
        background: #f6f6f6;
        border-radius: 8px;
        border: 3px dashed #DDD;
        &:hover{
            border-color: $theme-color;
        }
        &.is-dragover{
            .drag-upload__drag-text{
                display: block;
            }
            .drag-upload__operate{
                display: none;
            }
            border-color: $theme-color;
            background: #FAFCFF;
        }
        .drag-upload__operate{
            display: block;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 12px;
            color: #ccc;
            i{
                font-size: 56px;
            }
            p{
                font-size: 16px;
                color: #999;
                padding: 20px 0 10px;
            }
            .el-button{
                margin-top:26px;
                height: 34px;
                border: 1px solid #CCC;
                width: 120px;
                color: #666;
            }
        }
        .drag-upload__drag-text{
            display: none;
            font-size: 16px;
            color: $theme-color;
            text-align: center;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    }
   }
}
</style>

