<template>
    <el-upload
        :drag="drag"
        :on-success="handleSuccess"
        :on-error="handleError"
        :before-upload="checkFile"
        :show-file-list="false"
        :action="uploadUrl"
        accept=".pdf,.doc,.docx"
    >
        <div class="drag-upload__operate">
            <i class="el-icon-ssq-shangchuanbendiwenjian"></i>
            <p>{{ $t('docTranslation.upload.tip1') }}</p>
            <span class="accept">{{ $t('docTranslation.upload.tip2') }}</span><br>
            <el-button>{{ $t('docTranslation.upload.choose') }}</el-button>
        </div>
        <span class="drag-upload__drag-text">{{ $t('docTranslation.upload.tip3') }}</span>
    </el-upload>
</template>

<script>
export default {
    props: {
        drag: {
            type: Boolean,
            default: false,
        },
        uploadUrl: {
            type: String,
            default: '',
        },
    },
    methods: {
        handleSuccess(res) {
            this.$emit('changeLoading', false);
            this.$emit('onUploadSuccess', res);
        },
        handleError(err) {
            let errObj = null;
            try {
                errObj = JSON.parse(err.message.substring(err.message.indexOf('{')));
            } catch  {
                errObj = null;
            }
            this.$emit('changeLoading', false);
            this.$MessageToast.error(errObj.message || this.$t('docTranslation.upload.err'));
        },
        checkFile(file) {
            const testmsg = file.name.substring(file.name.lastIndexOf('.') + 1);
            if (!['doc', 'docx', 'pdf'].includes(testmsg)) {
                this.$MessageToast.error(this.$t('docTranslation.upload.typeTip'));
                return false;
            }
            this.$emit('changeLoading', true);
            return true;
        },
    },
};
</script>
