<!-- 二维码合同信息页（pc/wap） -->
<template>
    <div class="qr-sign-info-page" v-loading.fullscreen.lock="loading">
        <QRHeader headerTitle="上上签电子签约云平台"> </QRHeader>
        <!-- 已发送正常展示 -->
        <div class="qr-sign-conainer" v-if="!loading && !error">
            <img :src="logoSrc" alt="" width="auto" height="76" />
            <p class="p1">
                {{
                    contractStatus === '已完成' || contractStatus === '签署完成'
                        ? '该合同通过上上签电子签约云平台签署'
                        : '上上签为您的合同保障签署安全'
                }}
            </p>
            <div class="status">
                <p>
                    <span class="label">合同编号：</span>{{ contractId }}
                    <i
                        class="el-icon-ssq-iconziti29"
                        v-clipboard:copy="contractId"
                        v-clipboard:success="onCopy"
                        v-clipboard:error="onError"
                    ></i>
                </p>

                <p><span class="label">合同状态：</span>{{ contractStatus }}</p>
                <p v-if="finishTime">
                    <span class="label">完成时间：</span>{{
                        formatDateToString({
                            date: finishTime,
                            format: 'YYYY年MM月DD日 hh:mm:ss',
                        })
                    }}
                </p>
            </div>
            <div class="signer" v-for="(item, index) in receivers" :key="index">
                <p><span class="label">签约主体：</span>{{ item.signerName }}</p>
                <p><span class="label">签署状态：</span>{{ item.signStatus }}</p>
                <p v-if="item.finishTime">
                    <span class="label">签署时间：</span>{{
                        formatDateToString({
                            date: item.finishTime,
                            format: 'YYYY年MM月DD日 hh:mm:ss',
                        })
                    }}
                </p>
            </div>
        </div>
        <!-- 查询次数过多 -->
        <div class="error-container" v-else-if="!loading && error">
            <img src="~/img/qr_sign_info_err.png" alt="" width="132" height="auto" />
            <p>您的查询次数过多，请联系客服</p>
        </div>
        <footer :class="isPC ? 'pc' : 'mobile'">
            可前往上上签官网在线验签页面，验证合同是否真实有效
        </footer>
        <div class="to-detail">
            <a target="_blank" :href="`/doc/detail/${contractId}`">查看合同原文</a>
        </div>
    </div>
</template>
<script>
import logoIngSrc from 'src/common/assets/img/qr_sign_info_ing.png';
import logoDoneSrc from 'src/common/assets/img/qr_sign_info_done.png';
import { isPC } from 'src/common/utils/device.js';
import { formatDateToString } from 'utils/date.js';
import QRHeader from './qr_header/QRHeader.vue';

export default {
    components: {
        QRHeader,
    },
    data() {
        return {
            isPC: isPC(),
            loading: true,
            contractId: this.$route.query.contractId,
            receivers: [],
            contractStatus: '',
            finishTime: '',
            error: false,
        };
    },
    computed: {
        logoSrc() {
            if (
                this.contractStatus === '已完成' ||
                this.contractStatus === '签署完成'
            ) {
                return logoDoneSrc;
            } else {
                return logoIngSrc;
            }
        },
    },
    methods: {
        formatDateToString(s) {
            return formatDateToString.call(this, s);
        },
        // 复制签署链接成功
        onCopy: function() {
            this.$MessageToast.success(this.$t('docDetail.copySucc'));
        },
        // 复制签署链接失败
        onError: function() {
            this.$MessageToast.success(this.$t('docDetail.copyFail'));
        },
        getMapText(contractStatus) {
            let transformStatus = '';
            switch (contractStatus) {
                case 'SENT':
                    transformStatus = '签署中';
                    break;
                case 'IN_SEND_APPROVAL':
                    transformStatus = '审批中';
                    break;
                case 'COMPLETE':
                    transformStatus = this.$t('docDetail.signComplete');
                    break;
                case 'OVERDUE':
                    transformStatus = this.$t('docDetail.signOverdue');
                    break;
                case 'REJECT':
                    transformStatus = this.$t('docDetail.rejected');
                    break;
                case 'REVOKE_CANCEL':
                    transformStatus = this.$t('docDetail.revoked');
                    break;
                case 'SEND_APPROVAL_NOT_PASSED':
                    transformStatus = '已驳回';
            }
            return transformStatus;
        },
    },
    created() {
        this.$http
            .get(`${signPath}/ignore/contracts/${this.contractId}/qr-code-info/v2`)
            .then((res) => {
                this.contractStatus = this.getMapText(res.data.contractStatus);
                this.finishTime = res.data.finishTime;
                this.receivers = res.data.signers;
            })
            .catch((err) => {
                if (err.response.data.code === '130002') {
                    this.error = true;
                }
            })
            .finally(() => {
                this.loading = false;
            });
    },
};
</script>
<style lang="scss">
.qr-sign-info-page {
  position: relative;
  .qr-sign-conainer {
    // width: 86%;
    width: 266px;
    font-size: 14px;
    text-align: center;
    margin: 0 auto;
    margin-top: 50px;
  }
  p {
    line-height: 22px;
  }
  .el-icon-ssq-iconziti29 {
    color: #337ccf;
    font-size: 18px;
    display: inline-block;
    padding-left: 4px;
  }
  .label {
    display: inline-block;
    width: 76px;
    color: #666;
    text-align: left;
  }
  .p1 {
    color: #177825;
    font-size: 15px;
    margin-top: 15px;
    margin-bottom: 34px;
  }
  .status {
    text-align: left;
    padding-bottom: 20px;
    border-bottom: 1px solid #ddd;
  }

  .signer {
    padding-top: 20px;
    text-align: left;
  }

  footer {
    box-sizing: border-box;
    margin: 20px auto 40px;
    width: 278px;
    padding: 15px 22px;
    font-size: 14px;
    background-color: #f8f8f8;
    color: #022133;
    text-align: left;
    a {
      text-decoration: underline;
    }
    &.mobile {
      text-align: left;
    }
  }
  .to-detail {
    text-align: center;
    a {
      text-decoration: underline;
    }
  }

  .error-container {
    text-align: center;
    margin-top: 60px;
    p {
      font-size: 16px;
      color: #999;
      margin-top: 30px;
    }
  }
}

@media only screen and (min-width: 319px) and (max-width: 374px) {
  .qr-sign-info-page {
    .qr-sign-conainer,
    .error-container {
      margin-top: 35px;
    }
    footer {
      margin-bottom: 10px;
    }
  }
}
@media only screen and (min-width: 415px) {
  .qr-sign-info-page {
    .qr-sign-conainer,
    .error-container {
      margin-top: 70px;
    }
  }
}
</style>
