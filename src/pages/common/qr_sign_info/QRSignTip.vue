<template>
    <!-- 未发送tip -->
    <div class="qr-sign-tip-page">
        <QRHeader
            headerTitle="上上签电子签约云平台"
        >
        </QRHeader>
        <div class="qr-sign-tip-conainer">
            <img src="~/img/qr_sign_info_tip.png" alt="" width="80" height="auto">
            <p>合同发送后，上上签会对每份合同生成专属二维码，扫描合同上的二维码即可进行签名查验。</p>
        </div>
    </div>
</template>
<script>
import QRHeader from './qr_header/QRHeader.vue';
export default {
    components: {
        QRHeader,
    },
};
</script>
<style lang="scss">
	.qr-sign-tip-page {
		.qr-sign-tip-conainer {
			width: 86%;
			max-width: 340px;
			font-size: 14px;
			text-align: center;
			margin: 0 auto;
			margin-top: 70px;
			padding-bottom: 160px;

			p {
				font-size: 15px;
				line-height: 22px;
				margin-top: 36px;
				text-align: left;
			}
		}
	}

	@media only screen and (min-width: 415px) {
    .qr-sign-tip-page {
        .qr-sign-tip-conainer {
            margin-top: 150px;
        }
    }
	}
</style>
