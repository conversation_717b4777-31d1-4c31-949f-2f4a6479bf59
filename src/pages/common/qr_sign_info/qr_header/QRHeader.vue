<template>
    <div class="qr-header-cpn">
        <div class="container">
            <router-link class="ilBlock" tag="div" to="/" exact>
                <img class="default-logo cursor-point" :src="GLOBAL.WHITE_LOGO" width="86" height="37" alt="">
            </router-link>
            <span class="line"></span>
            <span class="title">{{ headerTitle }}</span>
        </div>
    </div>
</template>

<script>
export default {
    // eslint-disable-next-line
    props: ['headerTitle', 'clientId', 'redirectUrl', 'userType'],
    data() {
        return {
            HOST_ENV: this.GLOBAL.HOST_ENV,
        };
    },
};
</script>

<style lang="scss">
	.qr-header-cpn {
		width: 100%;
		background: $header-color;
		.container {
			width: 1000px;
			height: 65px;
			line-height: 65px;
			color: #fff;
			margin: 0 auto;
			.el-icon-ssq-fenzu3 {
				font-size: 36px;
                vertical-align: middle;
                cursor: pointer;
			}
			.line {
				display: inline-block;
				padding-left: 12px;
				height: 30px;
                border-right: 1px solid #eee;
                vertical-align: middle;
			}
			.title {
				font-size: 16px;
				font-weight: bold;
				margin-left: 12px;
			}
		}
	}

	/*
        手机屏幕适配
	*/

	@media (min-width: 320px) and (max-width: 768px) {

		.qr-header-cpn {
			.container {
				width: 100%;
				text-align: center;
			}

		}

	}
</style>
