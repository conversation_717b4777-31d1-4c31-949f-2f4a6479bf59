<template>
    <!-- 二维码合同信息页（pc/wap）在查验码需求中替代原来的QRSignInfo页面， 更改之后的样式和之前有较大不同 -->
    <div class="qr-sign-info-view-page" v-loading.fullscreen.lock="loading">
        <QRHeader headerTitle="上上签电子签约云平台" />
        <div class="qr-sign-containerWrap" v-if="!loading && !error">
            <div class="qr-sign-container__inner">
                <header class="qr-sign-conainer__header">
                    <div class="qrconainer__header__logoWrap">
                        <img :src="logo" class="bestsign-logo" />
                    </div>
                    <div class="qrconainer__header__titleWrap">
                        <h2 class="header__titleWrap__major">合同签署信息公示书</h2>
                        <p class="header__titleWrap__sub">
                            上上签为您的合同保障签署安全真实有效
                        </p>
                        <i class="iconfont el-icon-ssq-shimingrenzheng2" />
                    </div>
                </header>
                <main class="qrconainer__main">
                    <img class="invalid-img" v-if="contractStatus === '合同已作废'" src="~img/sign-tip/invalid.png" alt="">
                    <div class="qrconainer__main__statusWrap">
                        <p class="status_text">
                            <label>合同状态:</label>
                            <span>{{ contractStatus }}</span>
                            <template v-if="invalidAnnouncementId && invalidAnnouncementId !== '0'">
                                <span @click="showContractPreview" class="invalid-contract">查看《作废申明》</span>
                            </template>
                        </p>
                    </div>
                    <div class="qrconainer__main__basicWrap">
                        <h3>基本信息</h3>
                        <div class="qrconainer__main__basicInner">
                            <p class="qrconainer__main__basicInner__item">
                                <label>合同名称：</label>
                                <span>{{ contractTitle }}</span>
                            </p>
                            <p class="qrconainer__main__basicInner__item">
                                <label>合同编号：</label>
                                <span>{{ contractId }}</span>
                                <i
                                    class="el-icon-ssq-iconziti29"
                                    v-clipboard:copy="contractId"
                                    v-clipboard:success="onCopy"
                                    v-clipboard:error="onError"
                                ></i>
                            </p>
                            <p class="qrconainer__main__basicInner__item">
                                <label>发件方：</label>
                                <span>{{ sender }}</span>
                            </p>
                            <p class="qrconainer__main__basicInner__item">
                                <label>合同发送时间：</label>
                                <span>{{ sendTime }}</span>
                            </p>
                            <p class="qrconainer__main__basicInner__item" v-if="contractStatus === $t('docDetail.signComplete')">
                                <label>签约完成时间：</label>
                                <span>{{ finishTime }}</span>
                            </p>
                            <p class="qrconainer__main__basicInner__item" v-else>
                                <label>截止签约时间：</label>
                                <span>{{ deadline }}</span>
                            </p>
                        </div>
                    </div>
                    <div class="qrconainer__main__receiverWrap">
                        <h3>合同收件方</h3>
                        <div
                            class="qrconainer__main__receiverInner"
                            v-for="(receiver, index) in receivers"
                            :key="index"
                        >
                            <p class="qrconainer__main__receiverInner__item">
                                <label>签约主体：</label>
                                <span>{{ receiver.signerName }}</span>
                            </p>
                            <p class="qrconainer__main__receiverInner__item">
                                <label>签约状态：</label>
                                <span>{{ receiver.signStatus }}</span>
                            </p>
                        </div>
                    </div>
                </main>
            </div>
        </div>
        <div class="error-container" v-else-if="!loading && error">
            <img src="~/img/qr_sign_info_err.png" alt="" width="132" height="auto" />
            <p>您的查询次数过多，请联系客服</p>
        </div>
        <footer class="qrconainer-footer" :class="isPC ? 'pc' : 'mobile'">
            <p>
                可前往上上签官网在线验签页面，验证合同是否真实有效
            </p>
        </footer>
        <div class="to-detail">
            <a target="_blank" :href="`/doc/detail/${contractId}`">查看合同原文</a>
        </div>
        <PreviewDoc :previewDoc="invalidClaimContract" :contractId="invalidAnnouncementId" ref="previewDoc"></PreviewDoc>
    </div>
</template>
<script>
import dayjs from 'dayjs';
import QRHeader from './qr_header/QRHeader.vue';
import { isPC } from 'src/common/utils/device.js';
import PreviewDoc from 'foundation_pages/sign/common/preview/Preview';

export default {
    name: 'QRSignInfoViewPage',
    components: {
        PreviewDoc,
        QRHeader,
    },
    data() {
        return {
            logo: require('src/common/assets/img/bestsign-logo.png'),
            isPC: isPC(),
            loading: false,
            error: false,
            contractId: this.$route.query.contractId,
            contractTitle: '',
            receivers: [],
            contractStatus: '',
            invalidAnnouncementId: '0', // 作废合同Id
            finishTime: '',
            sendTime: '无',
            deadline: '无',
            sender: '无',
            invalidClaimContract: {},
        };
    },
    methods: {
        getContractInfo() {
            return this.$http.get(`/contract-api/ignore/contracts/verify/contract-content?contractId=${this.invalidAnnouncementId}&wxUserId=0`);
        },
        getContractMockToken() {
            return this.$http.post('/auth-center/user/contractId-login', {
                contractId: this.invalidAnnouncementId,
            });
        },
        showContractPreview() {
            if (!this.invalidAnnouncementId) {
                return;
            }
            Promise.all([this.getContractMockToken(), this.getContractInfo()])
                .then(res => {
                    const { access_token, refresh_token } = res[0].data || {};
                    const { documentContentPageVOS = [] } = res[1].data || {};
                    if (documentContentPageVOS) {
                        const { documentId, pageSize } = documentContentPageVOS[0];
                        const invalidClaimDocImgList = [];
                        for (let i = 0; i < pageSize; i++) {
                            invalidClaimDocImgList.push({
                                highQualityPreviewUrl: `/contract-api/ignore/contracts/verify/page-preview?documentId=${documentId}&page=${i + 1}&contractId=${this.invalidAnnouncementId}&access_token=${access_token}&refresh_token=${refresh_token}`,
                            });
                        }
                        this.invalidClaimContract = {
                            pageSize,
                            page: invalidClaimDocImgList,
                            name: '作废申明',
                            imgSrcStatic: true, // 标记这里免登自己拼imgurl
                        };
                        this.$nextTick(() => {
                            this.$refs.previewDoc.open();
                        });
                    }
                });
        },
        renderDate(stamp) {
            if (~~stamp === 0) {
                return '';
            }
            return dayjs(stamp).format('YYYY年MM月DD日 HH:mm');
        },
        // 相比较原来的接口，兰宝提供了新的数据字段用于满足查验码需求
        initPage() {
            this.loading = true;
            return this.$http
                .get(`${signPath}/ignore/contracts/${this.contractId}/qr-code-info/v2`)
                .then((res) => {
                    const {
                        finishTime,
                        signers,
                        contractStatus,
                        contractTitle,
                        sender,
                        sendTime,
                        signDeadline,
                        invalidAnnouncementId,
                    } = res.data;

                    this.contractStatus = this.getContractStatusText(contractStatus);
                    this.finishTime = this.renderDate(finishTime);
                    this.receivers = signers;
                    this.contractTitle = contractTitle;
                    this.sender = sender;
                    this.sendTime = this.renderDate(sendTime);
                    this.deadline = this.renderDate(signDeadline);
                    this.invalidAnnouncementId = invalidAnnouncementId;
                })
                .catch((err) => {
                    if (err.response.data.code === '130002') {
                        this.error = true;
                    }
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        getContractStatusText(contractStatus) {
            let transformStatus = '';
            switch (contractStatus) {
                case 'SENT':
                    transformStatus = '签署中';
                    break;
                case 'IN_SEND_APPROVAL':
                    transformStatus = '审批中';
                    break;
                case 'COMPLETE':
                    transformStatus = this.$t('docDetail.signComplete');
                    break;
                case 'OVERDUE':
                    transformStatus = this.$t('docDetail.signOverdue');
                    break;
                case 'REJECT':
                    transformStatus = this.$t('docDetail.rejected');
                    break;
                case 'REVOKE_CANCEL':
                    transformStatus = this.$t('docDetail.revoked');
                    break;
                case 'SEND_APPROVAL_NOT_PASSED':
                    transformStatus = '已驳回';
                    break;
                case 'INVALID':
                    transformStatus = '合同已作废';
                    break;
            }
            return transformStatus;
        },
        onCopy: function() {
            this.$MessageToast.success(this.$t('docDetail.copySucc'));
        },
        // 复制签署链接失败
        onError: function() {
            this.$MessageToast.success(this.$t('docDetail.copyFail'));
        },
    },
    created() {
        this.initPage();
    },
};
</script>
<style lang="scss">
$--color-primary: #127fd2 !default;
.qr-sign-info-view-page {
  position: relative;
  .qr-sign-containerWrap {
    width: 100%;
    margin-top: 30px;
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: center;
  }
  .qrconainer__header__logoWrap {
    display: flex;
    flex-flow: row nowrap;
    justify-content: flex-start;
    align-items: center;
    .bestsign-logo {
      height: 22px;
      width: auto;
    }
  }
  .qr-sign-container__inner {
    margin-bottom: 20px;
    padding: 20px;
    width: 345px;
    min-height: 600px;
    background-image: url('~src/common/assets/img/qr-background.png');
    background-size: 100% 100%;
    box-sizing: border-box;
    background-repeat: no-repeat;
  }
  .qrconainer__header__titleWrap {
    text-align: center;
    margin-top: 28px;
  }
  .header__titleWrap__major {
    font-family: PingFangSC-Medium;
    font-size: 24px;
    color: #374249;
    line-height: 27px;
    font-weight: 500;
  }
  .header__titleWrap__sub {
    font-family: PingFangSC-Regular;
    font-size: 10px;
    color: #8c959c;
    line-height: 15px;
    font-weight: 400;
    margin-top: 9px;
  }
  .header__titleWrap__sub::before {
    content: '·';
    font-size: 16px;
  }
  .header__titleWrap__sub::after {
    content: '·';
    font-size: 16px;
  }
  .el-icon-ssq-shimingrenzheng2 {
    color: $--color-primary;
    margin-top: 17px;
    font-size: 17px;
  }
}
.qrconainer__main {
  padding-left: 18px;
  padding-right: 18px;
  box-sizing: border-box;
  margin-top: 41px;
    position: relative;
    .invalid-img {
        position: absolute;
        width: 90px;
        left: 190px;
        top: -68px;
    }
  .qrconainer__main__statusWrap {
    border-bottom: 1px solid #dbedfb;
    padding-bottom: 15px;
    margin-bottom: 20px;
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #333333;
    font-weight: 500;
    label {
      margin-right: 10px;
    }
      .invalid-contract {
          font-size: 12px;
          cursor: pointer;
          color: $--color-primary;
          padding-left: 10px;
      }
  }
  .qrconainer__main__basicWrap {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #dbedfb;
    h3 {
      font-family: PingFangSC-Medium;
      font-size: 14px;
      color: #333333;
      font-weight: 500;
      margin-bottom: 15px;
    }
    p {
      min-height: 25px;
      font-size: 12px;
      line-height: 25px;
      word-break: break-all;
      label {
        color: #999;
        margin-right: 5px;
      }
      span {
        color: #333;
      }
    }
  }
  .qrconainer__main__receiverWrap {
    h3 {
      font-family: PingFangSC-Medium;
      font-size: 14px;
      color: #333333;
      font-weight: 500;
      margin-bottom: 15px;
    }
    p {
      height: 25px;
      font-size: 12px;
      line-height: 25px;
      label {
        color: #999;
        margin-right: 5px;
      }
      span {
        color: #333;
      }
    }
  }
}
.qrconainer-footer {
    box-sizing: border-box;
    margin: 0px auto 20px auto;
    width: 278px;
    padding: 15px 22px;
    font-size: 14px;
    background-color: #f8f8f8;
    color: #022133;
    text-align: left;
    &.mobile {
      text-align: left;
    }
}
.to-detail {
   padding-bottom: 50px;
    text-align: center;
    a {
      text-decoration: underline;
    }
  }

@media only screen and (min-width: 319px) and (max-width: 374px) {
  .qr-sign-info-view-page {
    .qr-sign-containerWrap,
    .error-container {
      margin-top: 35px;
    }
    footer {
      margin-bottom: 10px;
    }
  }
}
@media only screen and (min-width: 415px) {
  .qr-sign-info-view-page {
    .qr-sign-containerWrap,
    .error-container {
      margin-top: 70px;
    }
  }
}
</style>
