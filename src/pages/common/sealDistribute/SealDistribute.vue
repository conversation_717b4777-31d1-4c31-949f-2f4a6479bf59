<template>
    <div class="seal-distribute" :class="{'seal-distribute__mobile': !isPC}">
        <CommonHeader
            :headerTitle="$t('sealDistribute.requestSeal')"
            :needLogin="0"
        ></CommonHeader>
        <div class="content">
            <h4 v-if="!isPC">{{ $t('sealDistribute.requestSeal') }}</h4>
            <div class="content__detail">
                <div class="info-row">
                    <p class="info-name">{{ $t('sealDistribute.company') }}:</p>
                    <p class="info-text ent-name">{{ sealInfo.applyEntName }}</p>
                </div>
                <div class="info-row">
                    <p class="info-name">{{ $t('sealDistribute.applicant') }}:</p>
                    <p class="info-text">{{ sealInfo.applyEmpName }}</p>
                </div>
                <div class="info-row">
                    <p class="info-name">{{ $t('sealDistribute.accountID') }}:</p>
                    <p class="info-text">{{ sealInfo.applyAccount }}</p>
                </div>
                <div class="info-row">
                    <p class="info-name">{{ $t('sealDistribute.submissionTime') }}:</p>
                    <p class="info-text">{{ sealInfo.applyTime }}</p>
                </div>
                <div class="info-row" v-if="sealInfo.status !== 0">
                    <p class="info-name">{{ $t('sealDistribute.status') }}:</p>
                    <p class="info-text">{{ sealInfo.status === 1 ? $t('sealDistribute.agree') : $t('sealDistribute.unAgree') }}</p>
                </div>
                <template v-if="sealInfo.senderEntName">
                    <div class="info-row">
                        <p class="info-name">{{ $t('sealDistribute.senderCompany') }}:</p>
                        <p class="info-text ent-name">{{ sealInfo.senderEntName }}</p>
                    </div>
                    <div class="info-row">
                        <p class="info-name">{{ $t('sealDistribute.documentTitle') }}:</p>
                        <p class="info-text ent-name">{{ sealInfo.contractTitle }}</p>
                    </div>
                    <div class="info-row">
                        <p class="info-name">{{ $t('sealDistribute.sealApplicationScope') }}:</p>
                        <p class="info-text ent-name">{{ sealInfo.sealRangeType === 0 ? $t('sign.allContract'): $t('sign.currentContract') }}</p>
                    </div>
                </template>
                <div class="info-row">
                    <span class="info-name">{{ $t('sealDistribute.applyforSeal') }}:</span>
                    <div class="seal-img">
                        <p>{{ sealInfo.sealName }}</p>
                        <img v-if="sealInfo.sealId" :src="`/ents/${sealInfo.sealId}/seal/${sealInfo.sealFileId}?access_token=${$cookie.get('access_token')}`" alt="">
                    </div>
                </div>
            </div>
        </div>
        <div class="footer-content">
            <div class="info-opt">
                <template v-if="(sealInfo.status === 0 || sealInfo.authorizationStartTime) && sealInfo.sealRangeType === 0">
                    <p><span v-if="sealInfo.status === 0">{{ $t('sealDistribute.ifAgree') }}</span>{{ $t('sealDistribute.applyTime') }}</p>
                    <div class="date-picker">
                        <el-date-picker
                            v-model="sealInfo.authorizationStartTime"
                            type="date"
                            :readonly="sealInfo.status !== 0"
                            :picker-options="startPickOptions"
                            format="yyyy-MM-dd"
                            :placeholder="$t('sealDistribute.placeHolderTime')"
                        >
                        </el-date-picker>
                        {{ $t('sealDistribute.to') }}
                        <el-date-picker
                            v-model="sealInfo.authorizationEndTime"
                            type="date"
                            :readonly="sealInfo.status !== 0"
                            format="yyyy-MM-dd"
                            :picker-options="endPickOptions"
                            :placeholder="$t('sealDistribute.placeHolderTime')"
                        >
                        </el-date-picker>
                    </div>
                </template>
                <div class="button-group" v-if="sealInfo.status === 0">
                    <el-button
                        @click="handleConfirm(false)"
                    >{{ $t('sealDistribute.reject') }}</el-button>
                    <el-button
                        type="primary"
                        @click="handleConfirm(true)"
                    >{{ $t('sealDistribute.approve') }}</el-button>
                </div>
            </div>
        </div>
        <CommonFooter v-if="isPC" class="footer-follow"></CommonFooter>
    </div>
</template>
<script>
import CommonHeader from 'components/register_header/RegisterHeader.vue';
import CommonFooter from 'components/register_footer/RegisterFooter.vue';
import { isPC } from 'src/common/utils/device.js';
import dayjs from 'dayjs';
export default {
    name: 'SealDistribute',
    components: {
        CommonHeader,
        CommonFooter,
    },
    data() {
        return {
            isPC: isPC(),
            sealInfo: {},
            applyId: this.$route.query.applyId || '',
        };
    },
    computed: {
        endPickOptions() {
            return {
                disabledDate: this.getEndDisabledDate,
            };
        },
        startPickOptions() {
            return {
                disabledDate: this.getStartDisabledDate,
            };
        },
    },
    methods: {
        getEndDisabledDate(time) {
            // 控制终止时间不得大于起始时间
            if (this.sealInfo.authorizationStartTime) {
                return time.getTime() < this.sealInfo.authorizationStartTime;
            }
            return time.getTime() < new Date().getTime() - 24 * 60 * 60 * 1000;
        },
        getStartDisabledDate(time) {
            // 控制起始时间不得小于终止时间
            if (this.sealInfo.authorizationEndTime) {
                return time.getTime() > this.sealInfo.authorizationEndTime || (time.getTime() < new Date().getTime() - 24 * 60 * 60 * 1000);
            }
            return time.getTime() < new Date().getTime() - 24 * 60 * 60 * 1000;
        },
        handleConfirm(pass) {
            const isTimeValid = !!this.sealInfo.authorizationEndTime && !!this.sealInfo.authorizationStartTime;
            this.$http.post('/ents/apply-seal/reject-or-pass', {
                authorizationEndTime: isTimeValid ? dayjs(this.sealInfo.authorizationEndTime).format('YYYY-MM-DD') : '',
                authorizationStartTime: isTimeValid ? dayjs(this.sealInfo.authorizationStartTime).format('YYYY-MM-DD') : '',
                pass,
                sealApplicationId: this.applyId,
            }).then(() => {
                location.reload();
            });
        },
        getSealApplyInfo() {
            this.$http.get(`/ents/apply-seal/detail?sealApplicationId=${this.applyId}`)
                .then(res => {
                    this.sealInfo = {
                        ...res.data.value,
                        applyTime: dayjs(res.data.value.applyTime).format('YYYY-MM-DD HH:mm'),
                    };
                });
        },
        async switchAccout(entId) {
            return this.$http.post('/authenticated/switch-ent', {
                entId: entId,
                refreshToken: this.$cookie.get('refresh_token'),
            }).then(res => {
                const resData = res.data;
                this.$token.save(resData.access_token, resData.refresh_token);
                this.$cookie.delete('en_auth_step');
                this.$cookie.delete('en_auth_type');
                location.reload();
            });
        },
    },
    async mounted() {
        // 登录账号主体和申请主体不一致时切换
        if (this.$route.query.entId !== this.$store.state.commonHeaderInfo.currentEntId) {
            await this.switchAccout(this.$route.query.entId);
        }
        await this.getSealApplyInfo();
    },
};
</script>

<style lang="scss">
.seal-distribute {
    .content {
        background: #f8f8f8;
        .content__detail {
            padding: 25px 0;
            .info-row {
                width: 310px;
                margin: 0 auto;
                color: #666;
                font-size: 14px;
                line-height: 30px;
                .info-name {
                    display: inline-block;
                    width: 120px;
                    vertical-align: middle;
                    line-height: 20px;
                }
                .info-text {
                    display: inline-block;
                    max-width: 226px;
                }
                .ent-name {
                    line-height: 20px;
                    margin-top: 4px;
                }
                .seal-img {
                    display: inline-block;
                    margin-top: 8px;
                    padding-top: 10px;
                    text-align: center;
                    width: 130px;
                    height: 145px;
                    background: #fff;
                    border: 1px solid #EEEEEE;
                    border-radius: 8px;
                    border-radius: 8px;
                    img {
                        width: 100px;
                        height: 100px;
                    }
                }
            }
        }
    }
    .footer-content {
        background: #fff;
        padding-bottom: 35px;
        .info-opt {
            width: 310px;
            margin: 0 auto;
            color: #666;
            font-size: 14px;
            padding: 10px 0;
            .date-picker {
                padding-top: 6px;
                .el-date-editor {
                    width: 140px;
                    font-size: 14px;
                }
            }
            .button-group {
                margin-top: 18px;
                .el-button {
                    width: 80px;
                    font-size: 16px;
                    &.el-button--default {
                        background: #f8f8f8;
                    }
                    &.el-button--primary {
                        background: #127FD2;
                    }
                }
            }
        }
    }
    .footer-follow {
        position: fixed;
        bottom: 0;
    }
}
.seal-distribute__mobile {
    .content {
        background: #fff;
        width: 100%;
        margin: 0;
        padding: 0;
        margin-top: 25px;

        h4 {
            text-align: center;
            width: 100%;
            color: #0E0E0E;
            font-size: 18px;
            font-weight: normal;
            margin-bottom: 20px;
        }
        .content__detail {
            padding: 15px 0;
            .info-row {
                line-height: 40px;
                .ent-name {
                    margin-top: 9px;
                }
                .seal-img p {
                    line-height: 25px;
                    padding-bottom: 5px;
                }
            }
        }
    }
    .footer-content {
        width: 100%;
        .info-opt {
            border-top: 1px solid #CCC;

            .button-group {
                text-align: center;
            }
        }
    }
}
</style>
