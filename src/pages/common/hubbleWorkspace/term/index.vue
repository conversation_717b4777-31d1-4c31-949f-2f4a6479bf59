<template>
    <div class="hubble__term">
        <div class="hubble-workspace__title">
            <span>{{ $t('termManagement.title') }}</span>
            <div>
                <el-button class="hubble-workspace__title-btn" :disabled="!ids.length" @click="batchDeleteTerms">{{ $t('termManagement.batchDelete') }}</el-button>
                <el-button class="hubble-workspace__title-btn" @click="showImportDialog = true">{{ $t('termManagement.import') }}</el-button>
                <el-button class="hubble-workspace__title-btn" @click="exportTerms" :loading="exportLoading">{{ $t('termManagement.export') }}</el-button>
                <el-button class="hubble-workspace__title-btn" @click="addTerm">{{ $t('termManagement.add') }}</el-button>
            </div>
        </div>
        <div class="hubble-workspace-table">
            <el-table
                :data="termList"
                row-class-name="custom-row"
                :border="false"
                style="width: 100%"
                @selection-change="getTermsIds"
            >
                <el-table-column
                    type="selection"
                    width="55"
                    :selectable="ifNotDefault"
                >
                </el-table-column>
                <el-table-column
                    prop="extractExpectedFieldName"
                    :label="$t('termManagement.name')"
                ></el-table-column>
                <el-table-column
                    prop="extractExpectedFieldDefinition"
                    :label="$t('termManagement.definition')"
                ></el-table-column>
                <el-table-column
                    prop="extractFormatRequirement"
                    :label="$t('termManagement.formatRequirement')"
                ></el-table-column>
                <el-table-column
                    prop="showType"
                    :label="$t('termManagement.dataFormat')"
                ></el-table-column>
                <el-table-column :label="$t('termManagement.operation')" width="140">
                    <template slot-scope="scope">
                        <div class="hubble-workspace-table-operate" v-if="scope.row.fieldSource !== 'SYSTEM_DEFAULT'">
                            <span @click="editTerm(scope.row)">{{ $t('termManagement.edit') }}</span>|<span @click="deleteTerm(scope.row.extractExpectedFieldId)">{{ $t('termManagement.delete') }}</span>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <el-dialog
            :visible.sync="showDialog"
            :title="isEdit ? $t('termManagement.detail') : $t('termManagement.addTitle')"
            :show-close="false"
            class="hubble__term-dialog"
        >
            <el-form>
                <el-form-item required :label="$t('termManagement.name')">
                    <el-input :placeholder="$t('termManagement.namePlaceholder')" v-model="currentTermData.extractExpectedFieldName"></el-input>
                </el-form-item>
                <el-form-item :label="$t('termManagement.definition')">
                    <el-input type="textarea" :placeholder="$t('termManagement.definitionPlaceholder')" v-model="currentTermData.extractExpectedFieldDefinition"></el-input>
                </el-form-item>
                <el-form-item :label="$t('termManagement.formatRequirement')">
                    <el-input type="textarea" :placeholder="$t('termManagement.formatRequirementPlaceholder')" v-model="currentTermData.extractFormatRequirement"></el-input>
                </el-form-item>
                <el-form-item required :label="$t('termManagement.dataFormat')">
                    <el-select :placeholder="$t('termManagement.dataFormatPlaceholder')" v-model="currentTermData.extractContentFieldType">
                        <el-option
                            v-for="item in extractTypeList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button @click="showDialog = false">{{ $t('termManagement.cancel') }}</el-button>
                <el-button type="primary" @click="submitTerm">{{ isEdit ? $t('termManagement.confirmEdit') : $t('termManagement.add') }}</el-button>
            </div>
        </el-dialog>
        <el-dialog
            :visible.sync="showImportDialog"
            :title="$t('termManagement.importTitle')"
            :show-close="false"
            class="hubble__term-dialog"
            v-loading="importLoading"
        >
            <el-upload
                :action="`/web/hubble/workspace/${workspaceId}/upload-terminology-database-excel`"
                :on-success="handleImportSuccess"
                :on-error="handleImportFail"
                :show-file-list="false"
                :before-upload="() => importLoading = true"
            >
                <el-button>
                    <i class="el-icon-ssq-shangchuan1"></i>
                    {{ $t('termManagement.uploadTemplate') }}
                </el-button>
            </el-upload>
            <div class="hubble__term-dialog-footer">
                <span class="ThemeColor" @click="downloadImportFile">{{ $t('termManagement.downloadTemplate') }}</span>
                <el-button @click="showImportDialog = false">{{ $t('termManagement.cancel') }}</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { download } from 'src/common/utils/download.js';
export default {
    data() {
        return {
            termList: [],
            workspaceId: this.$route.query.workspaceId,
            extractType: {
                TEXT: this.$t('termManagement.extractType.text'),
                LONG_TEXT: this.$t('termManagement.extractType.longText'),
                DATE: this.$t('termManagement.extractType.date'),
                NUMBER: this.$t('termManagement.extractType.number'),
                BOOLEAN: this.$t('termManagement.extractType.boolean'),
            },
            showDialog: false,
            showImportDialog: false,
            currentTermData: {},
            importLoading: false,
            exportLoading: false,
            ids: [],
        };
    },
    computed: {
        extractTypeList() {
            return Object.entries(this.extractType).map(([key, value]) => ({ label: value, value: key }));
        },
        isEdit() {
            return !!this.currentTermData.extractExpectedFieldId;
        },
    },
    methods: {
        downloadImportFile() {
            download(`/web/hubble/workspace/${this.workspaceId}/download-terminology-database-excel`);
        },
        handleImportFail(err) {
            this.importLoading = false;
            try {
                const index = err.message.indexOf('{');
                const errData = JSON.parse(err.message.slice(index));
                errData.message && this.$MessageToast.error(errData.message);
            } catch (error) {
                this.$MessageToast.error(err.message);
            }
        },
        handleImportSuccess() {
            this.importLoading = false;
            this.showImportDialog = false;
            this.$MessageToast(this.$t('termManagement.importSuccess'));
            this.init();
        },
        deleteTerm(termId) {
            this.$confirm(this.$t('termManagement.deleteConfirm'), this.$t('termManagement.prompt')).then(() => {
                this.$http.post(`/web/hubble/workspace/${this.workspaceId}/extract-expected-field/${termId}/remove`).then(() => {
                    this.getTermList();
                });
            });
        },
        submitTerm() {
            if (!this.currentTermData.extractExpectedFieldName.trim()) {
                return this.$MessageToast(this.$t('termManagement.nameEmptyError'));
            }
            this.$http.post(`/web/hubble/workspace/${this.workspaceId}/extract-expected-field/${this.isEdit ? 'modify' : 'append'}`, this.currentTermData).then(() => {
                this.showDialog = false;
                this.getTermList();
            });
        },
        addTerm() {
            this.currentTermData = {
                extractExpectedFieldName: '',
                extractExpectedFieldDefinition: '',
                extractFormatRequirement: '',
                extractContentFieldType: 'TEXT',
            };
            this.showDialog = true;
        },
        editTerm(data) {
            this.currentTermData = JSON.parse(JSON.stringify(data));
            this.showDialog = true;
        },
        getTermList() {
            this.$http(`/web/hubble/workspace/${this.workspaceId}/extract-expected-field/acquire`).then(res => {
                this.termList = res.data.map(el => {
                    el.showType = this.extractType[el.extractContentFieldType];
                    return el;
                });
            });
        },
        async init() {
            this.getTermList();
        },
        exportTerms() {
            this.exportLoading = true;
            this.$http.get(
                `/web/hubble/workspace/${this.workspaceId}/export-terminology-database`,
                { responseType: 'blob' },
            ).then(res => {
                const regex = new RegExp('filename=(.*)');
                const match = res.headers['content-disposition'].match(regex);
                const name = match[1] ? match[1] + '.xlsx' : 'export.xlsx';
                const u = window.URL.createObjectURL(res.data);
                const a = document.createElement('a');
                a.download = decodeURIComponent(name);
                a.href = u;
                a.style.display = 'none';
                document.body.appendChild(a);
                a.click();
                a.remove();
            }).finally(() => {
                this.exportLoading = false;
            });
        },
        getTermsIds(terms) {
            this.ids = terms.map(term => {
                return term.extractExpectedFieldId;
            });
        },
        batchDeleteTerms() {
            this.$http.post(
                `/web/hubble/workspace/${this.workspaceId}/extract-expected-field/batch-remove`,
                this.ids,
            ).then(() => {
                this.getTermList();
            });
        },
        ifNotDefault(row) {
            if (row.fieldSource !== 'SYSTEM_DEFAULT') {
                return true;
            } else {
                return false;
            }
        },
    },
    created() {
        this.init();
    },
};
</script>

<style lang="scss">
.hubble__term {
    display: flex;
    flex-direction: column;
    height: 100%;
    .hubble-workspace-table-operate{
        width: 80px;
    }
    &-dialog{
        .el-dialog{
            width: 420px;
            border-radius: 8px;
            overflow: hidden;
            .el-select{
                width: 100%;
            }
        }
        .el-upload {
            width: 100%;
            .el-button{
                width: 100%;
                line-height: 60px;
                padding: 0;
                text-align: center;
                border-radius: 5px;
                border-style: dashed;
            }
        }
        &-footer{
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
        }
    }
}
</style>
