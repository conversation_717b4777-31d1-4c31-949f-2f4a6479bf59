<template>
    <el-dialog
        :title="title"
        :visible.sync="aggregationDialog"
        width="60%"
        custom-class="hubble-workspace__dialog personal"
        :before-close="closeDialog"
    >
        <div class="hubble-workspace__dialog-body personal" :style="{'paddingBottom': staffReplyId ? '140px' : '0px'}">
            <p class="hubble-workspace__dialog-body-part" v-if="staffReplyId">{{ $t('workspace.review.replyContent') }}</p>
            <div class="hubble-workspace__dialog-body-text" v-if="staffReplyId">
                <i></i>
                <div class="hubble-workspace__dialog-body-text-detail">
                    <p class="hubble-workspace__dialog-body-text-detail-desc" v-html="persionalInfos.replyContent"></p>
                </div>
            </div>
            <p class="hubble-workspace__dialog-body-part" v-if="staffReplyId">{{ $t('workspace.review.advice') }}</p>
            <div class="hubble-workspace__dialog-body-item" v-if="!circleList.length">
                <div class="hubble-workspace__dialog-body-item-main">
                    <p class="hubble-workspace__dialog-body-item-main-idea"><span>{{ $t('workspace.review.noIdea') }}</span></p>
                </div>
            </div>
            <div class="hubble-workspace__dialog-body-item" v-for="(item, index) in circleList" :key="index">
                <div class="hubble-workspace__dialog-body-item-main">
                    <p class="hubble-workspace__dialog-body-item-main-doc">{{ staffReplyId ? item.revisionFile.fileName : item.modificationSuggestion.revisionFile.fileName }}</p>
                    <div class="hubble-workspace__dialog-body-item-main-content">
                        <p><span>{{ $t('workspace.review.origin') }}</span></p>
                        <p>{{ staffReplyId ? item.originalContent : item.modificationSuggestion.originalContent }}</p>
                    </div>
                    <div class="hubble-workspace__dialog-body-item-main-content">
                        <p><span>{{ $t('workspace.review.revised') }}</span></p>
                        <p>{{ staffReplyId ? item.revisedContent : item.modificationSuggestion.revisedContent }}</p>
                    </div>
                    <p class="hubble-workspace__dialog-body-item-main-idea"><span>{{ $t('workspace.review.suggestion') }}</span>{{ staffReplyId ? item.modificationSuggestion : item.modificationSuggestion.modificationSuggestion }}</p>
                </div>
                <p class="hubble-workspace__dialog-body-item-mark" v-if="!staffReplyId" v-html="$t('workspace.review.dateMark', { name: item.userName, date: renderDate(item.replayTime), version: item.reviewVersionOrder })"></p>
            </div>
        </div>
        <div class="hubble-workspace__dialog-body-file" v-if="staffReplyId && persionalInfos.revisionFiles">
            <p class="hubble-workspace__dialog-body-part">{{ $t('workspace.review.revisionFiles') }}</p>
            <div class="hubble-workspace__dialog-body-file-list">
                <div class="hubble-workspace__dialog-body-file-list-item" v-for="file in persionalInfos.revisionFiles" :key="file.fileRevisionId" @click="downloadFile(file.fileRevisionId)">
                    <img src="../../img/doc.png" alt="" class="doc-symbol" v-if="fileType(file.fileName) === 'doc'">
                    <img src="../../img/docx.png" alt="" class="doc-symbol" v-if="fileType(file.fileName) === 'docx'" />
                    <img src="../../img/pdf.png" alt="" class="doc-symbol" v-if="fileType(file.fileName) === 'pdf'" />
                    <span class="hubble-workspace__dialog-body-file-list-item-name">{{ file.fileName }}</span>
                    <span class="hubble-workspace__dialog-body-file-list-item-size">{{ getFileSize(file.fileSize) }}</span>
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<script>
import dayjs from 'dayjs';
import { download } from 'src/common/utils/download.js';
export default {
    props: {
        aggregationDialog: {
            type: Boolean,
            default: false,
        },
        staffReplyId: {
            type: String,
            default: '',
        },
        staffReplyIds: {
            type: Array,
            default: () => [],
        },
        agreementVersion: {
            type: String,
            default: '',
        },
        reviewId: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            persionalInfos: {},
            totalInfos: [],
            title: '',
        };
    },
    computed: {
        circleList() {
            if (!!this.staffReplyId && !!this.persionalInfos.modificationSuggestions) {
                return this.persionalInfos.modificationSuggestions;
            } else if (!!this.staffReplyId && !this.persionalInfos.modificationSuggestions) {
                return [];
            } else {
                return this.totalInfos;
            }
        },
        renderDate() {
            return (stamp) => {
                if (!stamp) {
                    return this.$t('workspace.review.unReviewed');
                }
                return dayjs(stamp).format('YYYY-MM-DD HH:mm:ss');
            };
        },
        getFileSize() {
            const gb = 1024 * 1024 * 1024;
            const mb = 1024 * 1024;
            const kb = 1024;
            return (size) => {
                if (size > gb) {
                    return (size / gb).toFixed(2) + 'GB';
                } else if (size > mb) {
                    return (size / mb).toFixed(2) + 'MB';
                } else {
                    return (size / kb).toFixed(2) + 'KB';
                }
            };
        },
        fileType() {
            return (fileName) => {
                const name = fileName.split('.');
                const type = name[name.length - 1];
                return type;
            };
        },
    },
    watch: {
        aggregationDialog(newVal) {
            if (newVal && !this.staffReplyId) {
                this.getTotalInfos();
            } else if (newVal && (this.staffReplyId !== '')) {
                this.getPersonalInfos();
            }
        },
    },
    methods: {
        closeDialog() {
            this.$emit('closeDialog');
        },
        getTotalInfos() {
            this.$http.post(`/web/hubble/agreement-review/${this.reviewId}/staff-reply-aggregation`, this.staffReplyIds).then(res => {
                this.title = this.$t('workspace.review.staffReplyAggregation');
                this.totalInfos = res.data;
            });
        },
        getPersonalInfos() {
            this.$http.get(`/web/hubble/agreement-review/${this.reviewId}/staff-reply/${this.staffReplyId}`).then(res => {
                this.title = this.$t('workspace.review.staffReply', { name: res.data.userName });
                this.persionalInfos = res.data;
            });
        },
        downloadFile(fileRevisionId) {
            const url = `/web/hubble/agreement-review/${this.reviewId}/download-reversion-file/${fileRevisionId}`;
            download(url);
        },
    },
    created() {},
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__header {
    padding: 15px 20px;
}
::v-deep .el-dialog__body {
    padding: 20px;
    background: #F9F9F9;
    // position: relative;
    max-height: 60vh;
    overflow-y: auto;
}
// /deep/.personal .el-dialog__body {
//     padding-bottom: 100px;
// }
.hubble-workspace__dialog-body {
    flex-direction: column;
    &-part {
        padding-left: 7px;
        font-size: 12px;
        font-weight: 500;
        line-height: 24px;
        color: #3D3D3D;
        border-left: 2px solid #0988EC;
        margin-bottom: 10px;
    }
    &-text {
        display: flex;
        align-items: flex-start;
        margin-bottom: 10px;
        i {
            flex-shrink: 0;
        }
        &-detail {
            font-size: 10px;
            font-weight: normal;
            line-height: 16px;
            color: #666666;
            flex-grow: 1;
            white-space: pre-wrap;
        }
    }
    &-item-main {
        padding: 13px 20px;
        background-color: #fff;
        border-radius: 5px;
        &-doc {
            font-size: 12px;
            font-weight: 500;
            line-height: 24px;
            color: #0988EC;
        }
        &-content {
            font-size: 10px;
            line-height: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #ccc;
            margin-bottom: 8px;
            color: #666666;
            span {
                color: #000;
            }
        }
        &-idea {
            font-size: 10px;
            line-height: 16px;
            color: #666666;
            span {
                color: #000;
            }
        }
    }
    &-item-mark {
        font-size: 10px;
        line-height: 32px;
        text-align: right;
        color: #979797;
        span {
            color: #0988EC
        }
    }
    &-file {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        background-color: #fff;
        padding: 13px 20px;
        &-list {
            overflow-x: auto;
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
            &-item {
                display: flex;
                align-items: center;
                padding: 8px 14px;
                border-radius: 5px;
                background: #FFFFFF;
                box-sizing: border-box;
                border: 1px solid #CCCCCC;
                margin-right: 10px;
                .doc-symbol {
                    width: 25px;
                    height: auto;
                    margin-right: 10px;
                }
                i {
                    margin-right: 12px;
                }
                &-name {
                    max-width: 160px;
                    margin-right: 17px;
                    font-size: 12px;
                    line-height: 18px;
                    color: #333333;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
                &-size {
                    font-size: 12px;
                    line-height: 18px;
                    color: #CCCCCC;
                }
            }
        }
    }
}
</style>
