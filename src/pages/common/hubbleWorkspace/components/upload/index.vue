<template>
    <div class="mutiple-upload">
        <el-dialog
            :visible.sync="uploadDialog"
            width="70%"
            :before-close="handleClose"
        >
            <!-- :close-on-click-modal="false" -->
            <div class="upload-container">
                <div class="step">
                    <div class="step-item" v-if="operate === 'extract'">
                        <span class="dot operating" v-if="current === '1'">
                            <span class="flag"></span>
                        </span>
                        <span class="dot done" v-if="current === '2'"></span>
                        <span>{{ $t('workspace.agreement.upload') }}</span>
                    </div>
                    <div class="process" v-if="operate === 'extract'"></div>
                    <div class="step-item">
                        <span class="dot not-start" v-if="current === '1'"></span>
                        <span class="dot operating" v-if="current === '2'">
                            <span class="flag"></span>
                        </span>
                        <span>{{ operate === 'terms' ? $t('workspace.agreement.define') : $t('workspace.agreement.extract') }}</span>
                    </div>
                </div>
                <div class="upload-body" :class="{'file-list': current === '1' && fileList.length,}">
                    <el-upload
                        ref="upload"
                        action="#"
                        drag
                        multiple
                        accept=".pdf,.doc,.docx"
                        :show-file-list="false"
                        :file-list="fileList"
                        v-show="!fileList.length && operate === 'extract'"
                        :on-change="handleChange"
                        :on-progress="handleProgress"
                        :before-upload="beforeUpload"
                        :http-request="uploadFiles"
                        :on-success="handleSuccess"
                        :on-error="handleError"
                        class="upload-area"
                        :disabled="!couldUpload"
                    >
                        <img src="../../img/upload.png" alt="上传文件">
                        <div class="el-upload__text">{{ $t('workspace.agreement.drag') }} <em>{{ $t('workspace.agreement.add') }}</em></div>
                        <p>{{ $t('workspace.agreement.format') }}</p>
                    </el-upload>
                    <el-table
                        :data="fileList"
                        style="width: 100%;"
                        v-show="current === '1' && fileList.length"
                    >
                        <el-table-column
                            :label="$t('workspace.agreement.fileName')"
                        >
                            <template slot-scope="{row}">
                                <div class="file-name">
                                    <img src="../../img/pdf.png" alt="pdf" v-if="row.type === 'pdf'">
                                    <img src="../../img/docx.png" alt="docx" v-if="row.type === 'docx'">
                                    <img src="../../img/doc.png" alt="doc" v-if="row.type === 'doc'">
                                    <p>{{ row.name }}</p>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column
                            :label="$t('workspace.agreement.status')"
                        >
                            <template slot-scope="{row}">
                                <el-progress :percentage="row.uploadPercent" :show-text="false" v-if="row.uploadStatus === 'pending'"></el-progress>
                                <div class="file-status" v-if="row.uploadStatus === 'success'">
                                    <img src="../../img/success.png" alt="上传成功">
                                    <span>{{ $t('workspace.agreement.completed') }}</span>
                                    <span>{{ uploadTime(row) }}</span>
                                </div>
                                <div class="file-status" v-if="row.uploadStatus === 'failed'">
                                    <span>{{ $t('workspace.agreement.failed') }}</span>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column
                            :label="$t('workspace.agreement.size')"
                            width="100"
                        >
                            <template slot-scope="{row}">
                                <div class="file-size"></div>
                                {{ fileSize(row.size) }}
                            </template>
                        </el-table-column>
                        <el-table-column
                            :label="$t('workspace.operate')"
                            width="90"
                        >
                            <template slot-scope="{row}">
                                <div class="file-operate">
                                    <img
                                        src="../../img/retry.png"
                                        alt="重新上传"
                                        v-if="row.uploadStatus === 'failed'"
                                        @click="reUpload(row)"
                                    >
                                    <img
                                        src="../../img/delete.png"
                                        alt="删除"
                                        v-if="row.uploadStatus === 'failed'"
                                        @click="deleteFile(row)"
                                    >
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                    <Keyword
                        v-if="current === '2' && !extracting"
                        ref="keyword"
                        @changeField="changeField"
                        :keyword="$t('workspace.agreement.terms')"
                        :workspaceId="workspaceId"
                    />
                    <div class="total-progress" v-show="current === '2' && extracting">
                        <div class="top">
                            <div class="main" v-if="extractProgress === 100">
                                <img src="../../img/success.png" alt="抽取完成">
                                <p class="detail">{{ $t('workspace.agreement.success', {total: extractInfo.total || 0}) }}</p>
                            </div>
                            <div class="main" v-else>
                                <p class="progress">{{ extractProgress }}%</p>
                                <p class="detail">{{ $t('workspace.agreement.ongoing', {total: extractInfo.total || 0}) }}</p>
                                <p class="detail">{{ $t('workspace.agreement.tips') }}</p>
                            </div>
                            <el-progress :percentage="extractProgress" :show-text="false" v-if="extractInfo.finishCount !== extractInfo.total"></el-progress>
                        </div>
                        <div class="foot">
                            <el-button type="primary" @click="reset">{{ $t('workspace.agreement.others') }}</el-button>
                            <el-button type="text" class="refresh" @click="handleClose('refresh')">{{ $t('workspace.agreement.result') }}<i class="el-icon-ssq-fenxiang"></i></el-button>
                        </div>
                    </div>
                </div>

            </div>
            <div slot="footer" class="dialog-footer" v-if="fileList.length && current === '1'">
                <div class="left">
                    <span class="process">{{ $t('workspace.agreement.curProgress') }}{{ totalProgress }}%</span>
                    <el-button type="text" class="refresh"><i class="el-icon-refresh"></i>{{ $t('workspace.agreement.refresh') }}</el-button>
                    <span class="result">{{ $t('workspace.agreement.tips', {successNum, length: fileList.length}) }}</span>
                </div>
                <div class="right">
                    <el-button type="primary" :disabled="successNum !== fileList.length" @click="completeUpload" :loading="btnLoading">{{ $t('workspace.agreement.start') }}</el-button>
                    <el-button type="primary" @click="addFiles">{{ $t('workspace.agreement.more') }}</el-button>
                </div>
            </div>
            <div slot="footer" class="dialog-footer" v-if="current === '2' && !extracting">
                <div class="left">
                    <el-button type="text" class="refresh" @click="handleClose('refresh')" v-if="operate === 'extract'">{{ $t('workspace.agreement.skip') }}<i class="el-icon-ssq-fenxiang"></i></el-button>
                </div>
                <div class="right">
                    <el-button type="primary" :disabled="!fields.length" @click="startExtract" :loading="btnLoading">{{ operate === 'terms' ? $t('workspace.agreement.tiqu') : $t('workspace.agreement.chouqu') }}</el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {
    requestBatchId,
    checkBalance,
    startBatchExtraction,
    getBatchExtractionProgress,
    restartExtraction,
    autoDefineTerms,
    // requestWorkspaceId,
} from 'src/api/batchAgreement.js';
import Keyword from '../keyword/index.vue';
export default {
    components: {
        Keyword,
    },
    props: {
        workspaceId: {
            type: String,
            default: '',
        },
        uploadDialog: {
            type: Boolean,
            default: false,
        },
        operate: {
            type: String,
            default: 'extract',
        },
        agreementIds: {
            type: Array,
            default: () => {
                return [];
            },
        },
    },
    data() {
        return {
            batchId: '',    // 批量上传/抽取的任务ID
            current: '1',   // 操作第几步
            fileList: [],   // 上传的文件列表
            totalProgress: 0,   // 上传文件总进度
            successNum: 0,      // 成功的文件数量
            fields: [],     // 选取的术语
            extracting: false,  // 是否开始抽取
            extractProgress: 0, // 抽取的总进度
            extractInfo: {},    // 抽取的进度
            couldUpload: true,
            btnLoading: false,
            // workspaceId: '',
        };
    },
    computed: {
        fileSize() {
            const gb = 1024 * 1024 * 1024;
            const mb = 1024 * 1024;
            const kb = 1024;
            return (size) => {
                if (size > gb) {
                    return (size / gb).toFixed(2) + 'G';
                } else if (size > mb) {
                    return (size / mb).toFixed(2) + 'M';
                } else {
                    return (size / kb).toFixed(2) + 'K';
                }
            };
        },
        uploadTime() {
            return (file) => {
                const milliseconds = file.endTime - file.startTime;
                const hours = Math.floor(milliseconds / 3600000);
                const minutes = Math.floor((milliseconds % 3600000) / 60000);
                const seconds = Math.floor(((milliseconds % 3600000) % 60000) / 1000);
                let time = '';
                if (hours >= 0 && hours < 10) {
                    time = time + '0' + hours + ':';
                } else if (hours > 10) {
                    time = time + hours + ':';
                }
                if (minutes > 0 && minutes > 10) {
                    time = time + minutes + ':';
                } else if (minutes >= 0 && minutes < 10) {
                    time = time + '0' + minutes + ':';
                }

                if (time && seconds > 10) {
                    return time + seconds;
                } else if (time && seconds < 10) {
                    return time + '0' + seconds;
                } else if (!time) {
                    return seconds + 's';
                }
            };
        },
    },
    methods: {
        // 获取任务ID
        async getBachId() {
            await requestBatchId(this.workspaceId).then(res => {
                this.batchId = res.data;
            }).catch(() => {
                this.couldUpload = false;
            });
        },
        // 获取工作空间ID
        // async getWorkspaceId() {
        //     await requestWorkspaceId().then(res => {
        //         this.workspaceId = res.data;
        //     });
        // },
        // 获取各文件上传进度及总进度
        handleProgress(event, file) {
            const fileIndex = this.fileList.findIndex(doc => doc.uid === file.uid);
            if (fileIndex !== -1) {
                this.$set(this.fileList[fileIndex], 'uploadPercent', Number.parseInt(event.percent));
            }
        },
        // 文件状态改变时触发，将选中的文件加入list
        handleChange(file, fileList) {
            // console.log(file);
            // console.log(fileList);
            // console.log('未判断状态handleChange');
            if (file.status !== 'ready') {
                return;
            }
            // console.log('判断状态后handleChange');
            const fileIndex = fileList.findIndex(doc => doc.uid === file.uid);
            this.fileList.push(fileList[fileIndex]);
        },
        // 上传前文件类型判断，并给符合条件的文件添加上传开始时间和状态
        beforeUpload(file) {
            const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
            const isAllowed = allowedTypes.includes(file.type);
            if (!isAllowed) {
                this.$message.error(this.$t('workspace.agreement.uploadError'));
                return false;
            }
            const fileIndex = this.fileList.findIndex(doc => doc.uid === file.uid);
            let type = '';
            if (file.type === 'application/pdf') {
                type = 'pdf';
            } else if (file.type === 'application/msword') {
                type = 'doc';
            } else {
                type = 'docx';
            }
            if (fileIndex !== -1) {
                const startTime = new Date().getTime();
                this.$set(this.fileList[fileIndex], 'startTime', startTime);
                this.$set(this.fileList[fileIndex], 'type', type);
                this.$set(this.fileList[fileIndex], 'uploadStatus', 'ready');
            }
            // console.log('beforeUpload', this.fileList);
            return file;
        },
        // 开始上传文件，并触发改变上传状态和进度
        uploadFiles(options) {
            // console.log(options);
            const isFile = !!options.uid;
            const uid = isFile ? options.uid : options.file.uid;
            const file = isFile ? options.raw : options.file;
            const index = this.fileList.findIndex(file => file.uid === uid);
            let action = '';
            if (this.fileList[index].uploadUrl) {
                action = this.fileList[index].uploadUrl;
            } else {
                // const timestamp = new Date().getTime();
                action = `/web/hubble/workspace/${this.workspaceId}/agreements/upload?batchId=${this.batchId}&uniqueIdentifier=${uid}`;
                this.$set(this.fileList[index], 'uploadUrl', action);
            }
            this.$set(this.fileList[index], 'uploadStatus', 'pending');
            const formData = new FormData();
            formData.append('file', file);
            const config = {
                onUploadProgress: progressEvent => {
                    const { loaded, total } = progressEvent;
                    const percent = (loaded / total) * 100;
                    if (isFile) {
                        this.handleProgress({ percent: percent }, file);
                    } else  {
                        options.onProgress({ percent: percent }, file);
                    }
                },
            };
            this.$http.post(action, formData, config).then(res => {
                if (isFile) {
                    this.handleSuccess(res, file);
                } else {
                    options.onSuccess(res, file);
                }
            }).catch(err => {
                if (isFile) {
                    this.handleError(err, file);
                } else {
                    options.onError(err, file);
                }
            });
        },
        // 上传成功
        handleSuccess(response, doc) {
            const index = this.fileList.findIndex(file => file.uid === doc.uid);
            const endTime = new Date().getTime();
            this.$set(this.fileList[index], 'endTime', endTime);
            this.$set(this.fileList[index], 'uploadStatus', 'success');
            this.successNum++;
            this.totalProgress = ((this.successNum / this.fileList.length) * 100).toFixed(2) - 0;
            // console.log('handleSuccess');
        },
        // 上传失败
        handleError(err, doc) {
            const index = this.fileList.findIndex(file => file.uid === doc.uid);
            this.$set(this.fileList[index], 'uploadStatus', 'failed');
            // console.log('handleError');
        },
        // 重新上传，修改文件状态和开始时间，并使用上一次的上传地址，
        reUpload(item) {
            const index = this.fileList.findIndex(file => file.uid === item.uid);
            const startTime = new Date().getTime();
            this.$set(this.fileList[index], 'startTime', startTime);
            this.$set(this.fileList[index], 'uploadStatus', 'ready');
            this.$set(this.fileList[index], 'status', 'ready');
            this.uploadFiles(item);
        },
        // 删除列表中的文件
        deleteFile(item) {
            const index = this.fileList.findIndex(file => file.uid === item.uid);
            if (item.uploadStatus === 'success') {
                this.successNum--;
            }
            this.fileList.splice(index, 1);
        },
        // 新增文件
        addFiles() {
            this.$refs['upload'].$refs['upload-inner'].handleClick();
        },
        // 选择术语
        changeField(value) {
            this.fields = value;
        },
        completeUpload() {
            this.btnLoading = true;
            checkBalance(this.workspaceId, this.batchId).then(() => {
                this.current = '2';
                this.btnLoading = false;
            });
        },
        // 开始抽取
        async startExtract() {
            this.btnLoading = true;
            if (this.operate === 'extract') {
                await this.batchExtract();
            } else if (this.operate === 'restart') {
                await this.restartExtra();
            } else {
                await this.defineTerms();
            }
        },
        // 首先获取一次结果，再轮询调取获取进度接口
        handleStartExtract(id) {
            this.getProgress(id);
            this.progressTimer = setInterval(() => {
                this.getProgress(id);
            }, 2000);
        },
        // 获取抽取进度
        getProgress(extractId) {
            getBatchExtractionProgress(this.workspaceId, extractId).then(res => {
                this.extractInfo = res.data;
                if (!this.extracting) {
                    this.extracting = true;
                    this.btnLoading = false;
                }
                this.extractProgress = (this.extractInfo.finishCount / this.extractInfo.total * 100).toFixed(2) - 0;
                if (this.extractInfo.finishCount === this.extractInfo.total) {
                    this.progressTimer && clearInterval(this['progressTimer']);
                }
            }).catch(() => {
                this.progressTimer && clearInterval(this['progressTimer']);
            });
        },
        // 清空术语列表、文件列表等缓存数据
        reset() {
            // if (this.operate === 'restart') {
            //     this.$emit('changeOperate');
            // }
            this.$emit('changeOperate', 'extract');
            this.progressTimer && clearInterval(this['progressTimer']);
            this.getBachId();
            this.fileList = [];
            this.fields = [];
            this.successNum = 0;
            this.current = '1';
            this.totalProgress = 0;
            this.extracting = false;
            this.extractInfo = {};
            this.extractProgress = 0;
            this.btnLoading = false;
        },
        handleClose(operate) {
            this.$emit('close', operate);
        },
        async batchExtract() {
            await startBatchExtraction(
                this.workspaceId,
                {
                    batchId: this.batchId,
                    keywordIds: this.fields,
                    extractionMode: this.$route.query.mode,
                    preferredModel: this.$route.query.preferredModel,
                    preferredOcrSupplier: this.$route.query.preferredOcrSupplier,
                },
            ).then((res) => {
                if (res.data.data && res.data.data.batchExtractionId) {
                    this.handleStartExtract(res.data.data.batchExtractionId);
                }
            });
        },
        async restartExtra() {
            await restartExtraction(
                this.workspaceId,
                {
                    agreementIds: this.agreementIds,
                    keywordIds: this.fields,
                    extractionMode: this.$route.query.mode,
                    preferredModel: this.$route.query.preferredModel,
                    preferredOcrSupplier: this.$route.query.preferredOcrSupplier,
                },
            ).then(res => {
                if (res.data.data && res.data.data.batchExtractionId) {
                    this.handleStartExtract(res.data.data.batchExtractionId);
                }
            });
        },
        async defineTerms() {
            await autoDefineTerms(
                this.workspaceId,
                {
                    agreementIds: this.agreementIds,
                    keywordIds: this.fields,
                    extractionMode: this.$route.query.mode,
                    preferredModel: this.$route.query.preferredModel,
                    preferredOcrSupplier: this.$route.query.preferredOcrSupplier,
                },
            ).then(() => {
                this.$MessageToast.info(this.$t('workspace.agreement.extractionRequest'));
                this.btnLoading = false;
                this.handleClose();
            });
        },
    },
    async created() {
        // await this.getWorkspaceId();
        await this.getBachId();
    },
    mounted() {
        if (this.operate === 'extract') {
            this.current = '1';
        } else {
            this.current = '2';
        }
    },
    beforeDestroy() {
        this.progressTimer && clearInterval(this['progressTimer']);
    },
};
</script>

<style lang="scss" scoped>
.mutiple-upload::v-deep .el-dialog {
    height: 70%;
    display: flex;
    flex-direction: column;
    border-radius: 8px;
    &__header{
        display: none;
    }
    .el-dialog__body {
        padding: 0;
        flex-grow: 1;
        overflow-y: hidden;
        .upload-container {
            display: flex;
            flex-direction: column;
            height: 100%;
            .step {
                display: flex;
                align-items: center;
                justify-content: center;
                padding-top: 27px;
                margin-bottom: 23px;
                .step-item {
                    display: flex;
                    align-items: center;
                    font-size: 14px;
                    font-weight: 500;
                    line-height: 18px;
                    color: #333333;
                    .dot {
                        display: inline-block;
                        width: 12px;
                        height: 12px;
                        border-radius: 12px;
                        margin-right: 4px;
                        box-sizing: border-box;
                    }
                    .not-start {
                        background-color: #fff;
                        border: 1px solid #CCCCCC;
                    }
                    .operating {
                        border: 1px solid #0988EC;
                        padding: 2px;
                        background-color: #fff;
                        .flag {
                            display: block;
                            width: 6px;
                            height: 6px;
                            border-radius: 6px;
                            background-color: #0988EC;
                        }
                    }
                    .done {
                        border: 1px solid #0988EC;
                        background-color: #0988EC;
                    }
                }
                .process {
                    height: 0;
                    width: 120px;
                    border: 1px solid #E5E5E5;
                    margin: 0 20px;
                }
            }
            .upload-body {
                flex-grow: 1;
                padding: 0 20px 30px;
                overflow-y: auto;
                .upload-area {
                    height: 100%;
                    .el-upload {
                        width: 100%;
                        height: 100%;
                        .el-upload-dragger {
                            width: 100%;
                            height: 100%;
                            background-color: #F8F8F8;
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            justify-content: center;
                            line-height: 18px;
                            &:hover {
                                // border-color: #d9d9d9;
                                background-color: #F1F9FF;
                            }
                            img {
                                width: 30px;
                                height: 38px;
                                margin-bottom: 16px;
                            }
                            .el-upload__text {
                                margin-bottom: 6px;
                                font-size: 14px;
                                color: #666;
                            }
                            p {
                                font-size: 12px;
                                color: #979797;
                            }
                        }
                    }
                }
                .el-table {
                    border: none;
                    &::before {
                        height: 0;
                    }
                    &::after {
                        width: 0;
                    }
                    tr {
                        border-radius: 5px;
                        margin-bottom: 10px;
                        .file-name {
                            display: flex;
                            align-items: center;
                            img {
                                width: 24px;
                                height: 30px;
                                margin-right: 10px;
                            }
                            p {
                                font-size: 12px;
                                line-height: 18px;
                                color: #333;
                            }
                        }
                        .file-operate {
                            display: flex;
                            align-items: center;
                            img {
                                width: 16px;
                                height: 16px;
                                margin-right: 20px;
                                cursor: pointer;
                            }
                        }
                    }
                    .file-status {
                        display: flex;
                        align-items: center;
                        img {
                            display: block;
                            width: 14px;
                            height: 14px;
                            margin-right: 7px;
                        }
                        span {
                            display: inline-block;
                            margin-right: 8px;
                            font-size: 12px;
                            line-height: 18px;
                            color: #333333;
                        }
                    }
                }
                .el-table td, .el-table th.is-leaf {
                    border: none;
                }
                .el-table__footer-wrapper thead div, .el-table__header-wrapper thead div, th {
                    background-color: #F8F8F8;
                }
                .keyword-introduce {
                    min-height: 250px;
                }
            }
            .file-list {
                background-color: #F8F8F8;
                overflow-y: auto;
            }
        }
    }
    .el-dialog__footer .dialog-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .left {
            display: flex;
            align-items: center;
            justify-content: space-between;
            span {
                display: block;
            }
            .process {
                font-size: 12px;
                font-weight: 500;
                line-height: 18px;
                color: #333333;
                margin-right: 5px;
            }
            .refresh {
                font-size: 12px;
                font-weight: normal;
                line-height: 36px;
                color: #0988EC;
                margin-right: 15px;
            }
            .result {
                font-size: 12px;
                font-weight: normal;
                line-height: 18px;
                color: #979797;
            }
        }
        .right {
            display: flex;
            align-items: center;
            justify-content: space-between;
            .el-button {
                border-radius: 5px;
                width: 100px;
                height: 32px;
                font-size: 14px;
                font-weight: normal;
                padding: 0;
                text-align: center;
                line-height: 32px;
            }
        }
    }
}
.total-progress {
    height: 100%;
    .top {
        width: 100%;
        height: 70%;
        display: flex;
        flex-direction: column;
        background-color: #F8F8F8;
        .main {
            flex-grow: 1;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            .progress {
                font-size: 60px;
                font-weight: 500;
                line-height: 18px;
                color: #0988EC;
                margin-bottom: 32px
            }
            .detail {
                font-size: 14px;
                font-weight: normal;
                line-height: 18px;
                color: #3D3D3D;
            }
            img {
                display: block;
                height: 60px;
                width: 60px;
                margin-bottom: 32px
            }
        }
        .el-progress {
            flex-shrink: 0;
            width: 100%;
        }
    }
    .foot {
        width: 100%;
        height: 30%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    .el-button {
        display: block;
        margin-bottom: 10px;
        border-radius: 5px;
    }
}
</style>
