<template>
    <div class="pop-content">
        <div class="pop-title">
            <p>{{ $t('workspace.contentTracing.title') }}</p>
            <p @click.stop="hidePop">
                <i class="el-icon-close"></i>
            </p>
        </div>
        <div class="content-body">
            <div class="content-part">
                <p class="title">{{ $t('workspace.contentTracing.fieldContent') }}:</p>
                <div class="origin-content">
                    <div class="origin-content-item">
                        <p class="origin-content-item-field">{{ fieldsReference.fieldValue }}</p>
                    </div>
                </div>
            </div>
            <div class="content-part">
                <p class="title">{{ $t('workspace.contentTracing.originalResult') }}:</p>
                <div class="origin-content">
                    <div class="origin-content-item">
                        <p class="origin-content-item-field">{{ fieldsReference.originalFieldValue || 'N/A' }}</p>
                    </div>
                </div>
            </div>
            <div class="content-part">
                <p class="title">{{ $t('workspace.contentTracing.contentSource') }}:</p>
                <div class="origin-content">
                    <div class="origin-content-item" v-for="origin in fieldsReference.fieldsReferenceList" :key="origin.referenceId">
                        <p class="origin-content-item-text">{{ origin.referenceData }}</p>
                        <div class="origin-content-item-operate"><span>{{ `${$t('workspace.contentTracing.page')} ${origin.contentPages}` }}</span></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        fieldsReference: {
            type: Object,
            default: () => {
                return {};
            },
        },
    },
    methods: {
        hidePop(event) {
            event.stopPropagation();
            this.$emit('closePopover');
        },
    },
};
</script>

<style lang="scss" scoped>
.pop-content {
    position: relative;
    .pop-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: sticky;
        top: 0;
        left: 0;
        padding: 7px 4px;
        font-size: 14px;
        background-color: #fff;
        i {
            font-size: 10px;
        }
    }
}
.content-part {
  margin: 0 5px 15px;
  .title {
    padding-left: 10px;
    border-left: 2px solid #3b8ce7;
    margin-bottom: 10px;
    [dir="rtl"] & {
      border-right: 2px solid #3b8ce7;
      border-left: none;
      padding-right: 10px;
      padding-left: 0;
    }
  }
}
.origin-content{
    background: #f8f8f8;
    display: flex;
    flex-direction: column;
    &-item{
        font-size: 12px;
        margin: 0px 15px;
        // background: #fff;
        border-radius: 5px;
        padding: 15px 0;
        border-bottom: 1px solid #e4e4e4;
        &:last-of-type {
          border-bottom: none;
        }
        &-text{
            white-space: pre-wrap;
            margin-bottom: 8px;
        }
        &-field{
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        &-operate{
            display: flex;
            justify-content: space-between;
            color: #979797;
        }
    }
}
</style>
