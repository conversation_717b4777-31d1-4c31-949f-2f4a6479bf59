<!-- 渲染PDF -->
<template>
    <div class="preview-pdf-container">
        <div class="preview-pdf pdfViewer" ref="pdfContainer" id="pdfContainer">
            <div
                class="pdfpage-wrapper"
                v-for="(item, index) in numPages"
                :key="index"
                :page-index="`${index}`"
            >
                <div class="pdfpage" :style="styleObject(index)">
                    <div ref="pdfpage" class="pdfpage-canvas" v-show="isVisible(index)"></div>
                    <div
                        class="contract-page-content"
                        :page-index="`${index}`"
                        :style="contentStyleObject(index)"
                        v-if="!isParsePdfing"
                    >
                        <slot :data="index"></slot>
                    </div>
                </div>
                <div class="pdfpage-number">{{ $t('pdf.pager', {x: index + 1, y: numPages}) }}</div>
            </div>
        </div>
    </div>
</template>

<script>
import { throttle } from 'utils/fn.js';
import pdf from 'utils/pdf.js';
// 页间距20
const PAGE_DISTANCE = 20;
const PAGE_BUFFER = 4;
export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['doc', 'currentPageIndex', 'docWidth', 'parentElId', // 是否是pc签署，true, pdf根据scale去渲染canvas, 父组件不根据transform去放大
    ],
    data() {
        return {
            error: false,
            pdfDocument: null,
            numPages: 0,
            visible: [], // 当前可视范围页列表
            isParsePdfing: false,
            scale: 1,
        };
    },
    computed: {
        styleObject() {
            return (pageIndex) => {
                const page = this.doc.page[pageIndex];
                const height = page && page.actualHeight ? page.actualHeight : 0;
                const width = page && page.actualWidth ? page.actualWidth : 0;
                return {
                    paddingRight: 0,
                    height: `${height}px`, // 这里需要增加每一页的高度，否则会导致滚动时页码计算错误
                    width: `${width}px`,
                };
            };
        },
        contentStyleObject() {
            const { doc } = this;
            return index => {
                const page = doc.page[index];
                // console.log(page);
                return {
                    width: `${page && page.actualWidth}px`,
                    height: `${page && page.actualHeight}px`,
                };
            };
        },
        isVisible() {
            return (pageNumber) => {
                if (!this.visible.length) {
                    return false;
                }
                const maxPageIndex = this.visible.slice(-1)[0].pageIndex;
                const minPageIndex = this.visible[0].pageIndex;
                // visible的前5页和后5页都显示，避免用户快速滚动时的问题
                return pageNumber >= Math.max(0, minPageIndex - PAGE_BUFFER) && pageNumber < Math.min(maxPageIndex + PAGE_BUFFER, this.numPages);
            };
        },
    },
    watch: {
        currentPageIndex: {
            handler(newValue) {
                const find = this.visible.reduce((total, item) => {
                    return item.percent >= total.percent ? item : total;
                });
                // 如果切换的新页面已经在当前视图范围内，则不更新
                if (find.pageIndex === newValue - 1) {
                    return;
                }
                this.handleDoc(newValue - 1);
            },
        },
    },
    methods: {
        // 滚动到某一页
        handleDoc(pageIndex) {
            // 滚动到文档的那一页
            const top = this.doc.page.slice(0, pageIndex).reduce((total, item) => {
                total += item.actualHeight  + 20;
                return total;
            }, 0);
            this.getParentEl().scrollTop = top + this.getStartTop();
        },
        // 获取可视区域内的列表
        getVisiblePageList({ pageList, scrollTop, scrollBottom, startTop, distance }) {
            const visible = pageList.reduce((total, page, pageIndex) => {
                const pageHeight = (page.height + distance) * this.scale;
                const endTop = startTop + pageHeight;
                let percent = 0;
                // 在可视范围内的元素
                if (startTop <= scrollTop && endTop >= scrollTop && endTop <= scrollBottom) { // 下半页在视图范围内
                    percent = (endTop - scrollTop) / pageHeight;
                } else if (startTop >= scrollTop && endTop <= scrollBottom) { // 整页都在视图范围内
                    percent = 1;
                } else if (startTop >= scrollTop && startTop <= scrollBottom && endTop >= scrollBottom) { // 上半页在视图范围内
                    percent = (scrollBottom - startTop) / pageHeight;
                } else if (startTop <= scrollTop && endTop >= scrollBottom) { // 视图范围小于一页的
                    percent = (scrollBottom - scrollTop) / pageHeight;
                }
                startTop += pageHeight;
                if (percent) {
                    total.push({
                        pageIndex: pageIndex,
                        percent,
                    });
                }
                return total;
            }, []);
            return visible;
        },
        handleScroll: throttle(function(e) {
            const initHeight = this.getStartTop();
            const target = e.currentTarget;
            // 视图内的可视范围
            const scrollTop = target.scrollTop;
            const scrollBottom = scrollTop + target.getBoundingClientRect().height;
            this.visible = this.getVisiblePageList({ pageList: this.doc.page, scrollTop, scrollBottom, startTop: initHeight, distance: PAGE_DISTANCE });

            // 找出最大页码的那一页
            const maxPageIndex = this.visible.length ? this.visible.slice(-1)[0].pageIndex : 0;
            const minPageIndex = this.visible.length ? this.visible[0].pageIndex : 0;
            // 第一次渲染文档或者切换文档时，提前渲染前4页
            this.renderPage({ startPage: Math.max(0, minPageIndex - PAGE_BUFFER), endPage: Math.min(maxPageIndex + PAGE_BUFFER, this.numPages) });

            // 上面找到的是最小的那个页码，但是如果页面中一半的内容，已经是下一页的内容，则需要进行下一页的渲染
            const find = this.visible.reduce((total, item) => {
                return item.percent >= total.percent ? item : total;
            });
            if (find.pageIndex !== this.currentPageIndex - 1) {
                this.$emit('updateCurrentPageIndex', find.pageIndex + 1);
            }
        }, 24),
        // 下载并解析pdf文档
        async parsePdf(times = 0) {
            // 判断文档是否已经解析过
            if (!this.doc.hasLoaded) {
                try {
                    // documentId变的时候pdfurl也可能一起变导致2次请求
                    if (this.isParsePdfing) {
                        return;
                    }
                    this.isParsePdfing = true;
                    const pdfDocument = await pdf.loadPDFDocument(this.doc.pdfurl);
                    this.doc.pdfDocument = pdfDocument;
                    this.error = false;
                } catch (error) {
                    console.error('parsePdf-catch', error);
                    this.error = true;
                    // 如果是接口请求失败500，不做重新下载渲染
                    if (error && error.name === 'UnexpectedResponseException') {
                        return;
                    }
                    // fix CFD-5188 Edge "InvalidPDFException: Invalid PDF structure 偶现，重新下载渲染
                    if (times < 3) { // 避免过多次调用接口
                        this.parsePdf(++times);
                    }
                    return;
                }
            }
            this.pdfDocument = this.doc.pdfDocument;
            this.numPages = this.pdfDocument.numPages;
            this.doc.hasLoaded = true;
            this.computePage();
        },
        // 下载完pdf流文件后，计算page的相关数据并更新
        computePage() {
            this.$nextTick(async() => {
                let pageNum = 0;
                const page = new Array(this.numPages);
                while (pageNum < this.pdfDocument.numPages) {
                    const pageContainer = this.$refs.pdfpage[pageNum];
                    // 每次渲染之前需要清空下原有页面的内容
                    if (pageContainer) {
                        pageContainer.innerHTML = '';
                    }
                    try {
                        const { pdfPageView, width, height, actualWidth, actualHeight } = await pdf.renderReviewPDFPage(this.pdfDocument, pageNum + 1, pageContainer, this.docWidth);
                        page[pageNum] = {
                            ...this.doc.page[pageNum],
                            hasRendered: false, // 当前页是否已经渲染过，每次切换文档时，需要重新渲染
                            width: Math.floor(actualWidth), // 放缩之后的 实际宽度 793*this.scale
                            height: Math.floor(actualHeight),
                            actualWidth: Math.floor(actualWidth), // 放缩之后的 实际宽度 793*this.scale
                            actualHeight: Math.floor(actualHeight),
                            width_init: Math.floor(width), // 原始宽度，793
                            height_init: Math.floor(height),
                            pdfPageView,
                        };
                    } catch (e) {
                        // console.error('计算pdfPageView-error', e)
                        // fix CFD-5188 Edge  pdf.renderPDFPage pdfjs内部报错 bad XRef entry，做异常处理，引导用户重新刷新页面
                        this.$alert(this.$t('pdf.parseFailed'), '', {
                            confirmButtonText: this.$t('pdf.confirm'),
                            showClose: false,
                            closeOnClickModal: false,
                            closeOnPressEscape: false,
                            callback: () => {
                                location.reload();
                            },
                        });
                        return false;
                    }
                    pageNum++;
                }
                // 文档基本信息解析完毕，触发ready事件，更新父元素page列表
                this.$emit('ready', page);
                this.isParsePdfing = false;
                const initHeight = this.getStartTop();
                const element = this.getParentEl();
                // 滚动到定顶部
                element.scrollTop = 0;
                this.visible = this.getVisiblePageList({
                    pageList: this.doc.page,
                    scrollTop: 0,
                    scrollBottom: element.getBoundingClientRect().height,
                    startTop: initHeight,
                    distance: PAGE_DISTANCE,
                });
                const maxPageIndex = this.visible.length ? this.visible.slice(-1)[0].pageIndex : 0;
                // 第一次渲染文档或者切换文档时，提前渲染前5页
                this.renderPage({ startPage: 0, endPage: Math.min(maxPageIndex + PAGE_BUFFER, this.numPages) }, true);
                this.$emit('updateCurrentPageIndex', 1);
            });
        },
        // 获取pdf外层元素，距离父级滚动元素的距离差
        getStartTop() {
            // const el = this.$parent.$el;
            const el = this.getParentEl();
            const current = this.$refs.pdfContainer;
            const distance1 = this.getElementTop(el, document.body);
            const distance2 = this.getElementTop(current, document.body);
            return distance2 - distance1;
        },
        getElementTop(el, targetEl) {
            const parent = el.offsetParent;
            const top = el.offsetTop;
            return parent && parent !== targetEl ? this.getElementTop(parent, targetEl) + top : top;
        },
        // 获取父组件的外层元素，绑定scroll事件
        getParentEl() {
            if (this.parentElId) {
                return document.getElementById(this.parentElId);
            }
            return this.$parent.$el;
        },
        // 渲染第几页到第几页
        async renderPage({ startPage, endPage }, isGenerateImage) {
            const pageList = this.doc.page.slice(startPage, endPage);
            pageList.forEach(async(page, pageIndex) => {
                if (page.hasRendered) {
                    return;
                }
                page.hasRendered = true;
                await page.pdfPageView.draw();
                if (!this.doc.thumbnail && isGenerateImage && pageIndex === 0) { // 生成第一页的缩略图
                    this.doc.thumbnail = pdf.generateImage(page.pdfPageView.canvas, page.width, page.height, 115, 144);
                    this.$emit('update-thumbnail', this.doc.thumbnail);
                }
            });
        },
    },
    async mounted() {
        await this.parsePdf();
        this.getParentEl().addEventListener('scroll', this.handleScroll);
    },
    beforeDestroy() {
        this.getParentEl().removeEventListener('scroll', this.handleScroll);
    },
};
</script>

<style lang="scss">
    .preview-pdf-container {
        .preview-pdf {
            user-select: text;
            transition: all 0 ease-out 0;
        }
        .preview-error {
            user-select: text;
            width: 100%;
            height: 100%;
            text-align: center;
            margin: 0 auto;
            background-color: #fff;
            font-size: 14px;
            height: 300px;
            box-sizing: border-box;
            color: #ccc;
            .el-loading-mask {
                z-index: 999; // 覆盖默认的10000， fix签署页面 盖住了默认的弹窗
            }
        }
        .pdfViewer.singlePageView .page {
            margin-top: 10px;
            border: none;
        }
        .pdfViewer .page {
            direction: ltr;
            width: 100%;
            margin: 0;
            position: relative;
            // z-index: -1; // fix CFD-4805: labels is under pdf layer
            overflow: visible;
            border: 0 none;
            background-clip: content-box;
            background-color: #fff;
            // margin-bottom: 20px;
            .pagenum {
                margin-top: 3px;
                text-align: center;
            }
        }
        .pdfpage-number {
            font-size: 14px;
            line-height: 20px;
            width: 100%;
            height: 20px;
            text-align: center;
            color: #999999;
        }
        .pdfpage-wrapper {
            font-size: 0;
            text-align: center;
            position: relative;
            .pdfpage {
                // display: inline-block;
                // position: relative;
                // transform-origin: top left;
                display: block;
                margin: 0 auto;
                position: relative;
            }
            .contract-page-content {
                position: absolute;
                top: 0;
                left: 0;
                transform-origin: 0 0 0;
                height: 100%;
                overflow: visible; // TODO
                transform: translateZ(120px); // 提高层级 // TODO
            }
        }
    }
</style>
