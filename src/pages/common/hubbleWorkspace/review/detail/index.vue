<template>
    <div class="hubble-workspace">
        <div class="hubble-workspace__header">
            <div class="hubble-workspace__header-left" @click="backToHome">
                <img src="../../img/logo.png" alt="">
                <div class="hubble-workspace__header-left-title">{{ $t('workspaceIndex.title') }}</div>
            </div>
            <div class="hubble-workspace__header-right" @click="overviewDialog = true">
                <div class="hubble-workspace__header-left-title">{{ $t('workspaceIndex.package') }}</div>
            </div>
        </div>
        <div class="hubble-review-header">
            <div class="hubble-review-header__title">
                <p>{{ $t('workspace.review.reviewDetail') }}</p>
            </div>
            <div class="hubble-review-header__operate">
                <div class="hubble-review-header__operate-body">
                    <div class="hubble-review-header__operate-body-text">
                        <span class="hubble-review-header__operate-body-text-doc">{{ summaryInfo.reviewName || '' }}</span>
                        <span class="hubble-review-header__operate-body-text-code">{{ $t('workspace.review.reviewId') }}：{{ reviewId || '' }}</span>
                    </div>
                    <div class="hubble-review-header__operate-body-btn">
                        <el-button class="hubble-workspace__title-btn" :disabled="versionSummaryInfo.status === 'COMPLETED'" @click="reUploadFiles">{{ $t('workspace.review.reupload') }}</el-button>
                        <el-button class="hubble-workspace__title-btn" :disabled="versionSummaryInfo.status === 'COMPLETED'" @click="endReview">{{ $t('workspace.review.finish') }}</el-button>
                    </div>
                </div>
            </div>
        </div>
        <div class="hubble-review-body">
            <div class="hubble-review-body-top">
                <div class="hubble-review-body-top-part">
                    <div class="hubble-review-body-top-part-name">
                        <p>{{ $t('workspace.review.reviewSummary') }}</p>
                    </div>
                    <p class="hubble-review-body-top-part-desc">{{ $t('workspace.review.reviewName') }}：{{ summaryInfo.reviewName || '' }} </p>
                    <p class="hubble-review-body-top-part-desc">{{ $t('workspace.review.reviewStartTime') }}：{{ moment(summaryInfo.startTime).format('YYYY-MM-DD HH:mm:ss') }}</p>
                    <p class="hubble-review-body-top-part-desc">{{ $t('workspace.review.reviewCompleteTime') }}：{{ summaryInfo.endTime ? moment(summaryInfo.endTie).format('YYYY-MM-DD HH:mm:ss') : $t('workspace.review.Incomplete') }}</p>
                    <p class="hubble-review-body-top-part-desc">{{ $t('workspace.review.initiator') }}：{{ summaryInfo.initiator || '' }}</p>
                </div>
                <div class="hubble-review-body-top-part">
                    <div class="hubble-review-body-top-part-name">
                        <p>{{ $t('workspace.review.versionSummary') }}</p>
                        <el-select v-model="reviewVersionId" @change="changeSummaryVersion">
                            <el-option :label="`${$t('workspace.review.version')}${vers.versionOrder}`" :value="vers.reviewVersionId" v-for="vers in versions" :key="vers.reviewVersionId"></el-option>
                        </el-select>
                    </div>
                    <p class="hubble-review-body-top-part-desc">{{ $t('workspace.review.curReviewStatus') + ': ' + getStatus(versionSummaryInfo.status) }}</p>
                    <p class="hubble-review-body-top-part-desc">{{ $t('workspace.review.curReviewVersion') + ': ' + `${$t('workspace.review.version')}${versionSummaryInfo.versionOrder || ''}` }}</p>
                    <p class="hubble-review-body-top-part-desc">{{ $t('workspace.review.curReviewPopulation') + ': ' + versionSummaryInfo.reviewersCount || '' }}</p>
                    <p class="hubble-review-body-top-part-desc">{{ $t('workspace.review.curReviewStartTime') + ': ' + moment(versionSummaryInfo.startTime).format('YYYY-MM-DD HH:mm:ss') }}</p>
                    <p class="hubble-review-body-top-part-desc">{{ $t('workspace.review.curReviewInitiator') + ': ' + versionSummaryInfo.initiator || '' }}</p>
                </div>
            </div>
            <div class="hubble-review-body-table">
                <div class="hubble-review-body-table__title">
                    <span>
                        {{ $t('workspace.review.history') }}
                        <i
                            class="el-icon-ssq-genghuanchayanma"
                            :class="{'refresh': isRefresh}"
                            @click="asyncEmail"
                        ></i>
                    </span>
                    <div>
                        <el-button class="hubble-workspace__title-btn" :disabled="!staffReplyIds.length" @click="checkDetails">{{ $t('workspace.review.checkComments') }}</el-button>
                        <el-button class="hubble-workspace__title-btn" @click="browseDialog = true">{{ $t('workspace.review.overview') }}</el-button>
                        <el-select v-model="agreementVersion" @change="changeHistoryVersion">
                            <el-option :label="`${$t('workspace.review.version')}${vers.versionOrder}`" :value="vers.reviewVersionId" v-for="(vers) in versions" :key="vers.reviewVersionId"></el-option>
                        </el-select>
                    </div>
                </div>
                <el-table
                    :data="historyList"
                    row-class-name="custom-row"
                    :border="false"
                    style="width: 100%"
                    @selection-change="getStaffReplyIds"
                >
                    <el-table-column
                        type="selection"
                        width="55"
                    ></el-table-column>
                    <el-table-column
                        :label="$t('workspace.review.agreement') + $t('workspace.review.version')"
                    >
                        <template slot-scope="scope">
                            {{ `${$t('workspace.review.versionOrder', {version: scope.row.versionOrder})}` }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="reviewerName"
                        :label="$t('workspace.review.reviewer')"
                    ></el-table-column>
                    <el-table-column
                        prop="reviewResult"
                        :label="$t('workspace.review.reviewResult')"
                    >
                        <template slot-scope="scope">
                            {{ $t(`workspace.review.${scope.row.reviewResult}`) }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        :label="$t('workspace.review.replyTime')"
                    >
                        <template slot-scope="scope">
                            {{ moment(scope.row.replyTime).format('YYYY-MM-DD HH:mm:ss') }}
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('workspace.operate')" width="120">
                        <template slot-scope="scope">
                            <div class="hubble-workspace-table-operate">
                                <span @click="checkDetails(scope.row.staffReplyId)">{{ $t('workspace.detail') }}</span>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="hubble-review-body-table">
                <div class="hubble-review-body-table__title">
                    <span>{{ $t('workspace.review.agreement') }}</span>
                </div>
                <el-table
                    :data="aggreementList"
                    row-class-name="custom-row"
                    :border="false"
                    style="width: 100%"
                >
                    <!-- @selection-change="getTermsIds" -->
                    <!-- <el-table-column
                        type="selection"
                        width="55"
                    ></el-table-column> -->
                    <el-table-column
                        :label="$t('workspace.review.agreement') + $t('workspace.review.version')"
                        width="160px"
                    >
                        <template slot-scope="scope">
                            {{ `${$t('workspace.review.versionOrder', {version: scope.row.reviewVersionOrder})}` }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        :label="$t('workspace.review.files')"
                        header-align="center"
                    >
                        <template slot-scope="scope">
                            <el-table
                                :data="scope.row.agreementVersionHistoryFiles"
                                :border="false"
                                :show-header="scope.$index === 0"
                                class="agreement-table"
                            >
                                <el-table-column
                                    prop="fileName"
                                    :label="$t('workspace.review.fileName')"
                                ></el-table-column>
                                <el-table-column
                                    prop="numberOfModificationSuggestions"
                                    :label="$t('workspace.review.numberOfModificationSuggestions')"
                                    width="200"
                                ></el-table-column>
                                <el-table-column
                                    :label="$t('workspace.review.uploadTime')"
                                    width="200"
                                >
                                    <template slot-scope="{row}">
                                        {{ row.uploadTime ? moment(row.uploadTime).format('YYYY-MM-DD HH:mm:ss') : '' }}
                                    </template>
                                </el-table-column>
                                <el-table-column :label="$t('workspace.operate')" width="160">
                                    <template slot-scope="{row}">
                                        <div class="hubble-workspace-table-operate">
                                            <span @click="downloadFile(row.reviewVersionFileId)" v-if="row.reviewVersionFileId">{{ $t('workspace.review.download') }}</span>
                                            <span v-if="scope.row.reviewVersionOrder === aggreementList.length && versionSummaryInfo.status !== 'COMPLETED' && row.reviewVersionFileId"> | </span>
                                            <span v-if="scope.row.reviewVersionOrder === aggreementList.length && versionSummaryInfo.status !== 'COMPLETED'" @click="toReviewManage(scope.row.reviewVersionOrder)">{{ $t('workspace.review.dispatch') }}</span>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <OverViewDialog
            :workspaceId="workspaceId"
            :overviewDialog="overviewDialog"
            @closeDialog="overviewDialog = false"
        ></OverViewDialog>
        <BrowseDialog
            :title="versionOrder"
            :browseDialog="browseDialog"
            :list="historyList"
            @closeDialog="browseDialog = false"
        ></BrowseDialog>
        <AggregationDialog
            :reviewId="reviewId"
            :staffReplyId="staffReplyId"
            :staffReplyIds="staffReplyIds"
            :agreementVersion="agreementVersion"
            :aggregationDialog="aggregationDialog"
            @closeDialog="resetDialog"
        ></AggregationDialog>
    </div>
</template>

<script>
import { download } from 'src/common/utils/download.js';
import OverViewDialog from '../../components/overViewDialog';
import BrowseDialog from '../../components/browseDialog';
import AggregationDialog from '../../components/aggregationDialog';
import moment from 'dayjs';
export default {
    components: {
        OverViewDialog,
        BrowseDialog,
        AggregationDialog,
    },
    data() {
        return {
            workspaceId: '',
            reviewId: '',
            overviewDialog: false,
            browseDialog: false,
            aggregationDialog: false,
            summaryInfo: {},
            versions: [],
            reviewVersionId: '',
            versionSummaryInfo: {},
            agreementVersion: '',
            versionOrder: '',
            historyList: [],
            aggreementList: [],
            staffReplyId: '',
            staffReplyIds: [],
            hasEnd: false,
            isRefresh: false,
        };
    },
    computed: {
        getStatus() {
            return (state) => {
                switch (state) {
                    case 'CREATED':
                        return this.$t('workspace.create');
                    case 'REVIEWING':
                        return this.$t('workspace.reviewing');
                    case 'COMPLETED':
                        return this.$t('workspace.completed');
                    default:
                        return '';
                }
            };
        },
    },
    methods: {
        moment,
        reviewSummary() {
            this.$http.get(`/web/hubble/agreement-review/${this.reviewId}/agreement-review-summary/${this.reviewId}`).then(res => {
                this.summaryInfo = res.data;
            });
        },
        reviewVersions() {
            return new Promise(resolve => {
                this.$http.get(`/web/hubble/agreement-review/${this.reviewId}/agreement-review-version-statistics`).then(res => {
                    this.versions = res.data;
                    this.reviewVersionId = this.versions[0].reviewVersionId;
                    this.agreementVersion = this.versions[0].reviewVersionId;
                    resolve();
                });
            });
        },
        versionSummary(reviewVersionId) {
            this.$http.get(`/web/hubble/agreement-review/${this.reviewId}/version-summary/${reviewVersionId}`).then(res => {
                this.versionSummaryInfo = res.data;
            });
        },
        reviewHistory(reviewVersionId) {
            this.$http.get(`/web/hubble/agreement-review/${this.reviewId}/version-history/${reviewVersionId}`).then(res => {
                this.historyList = res.data;
            });
        },
        changeSummaryVersion(reviewVersionId) {
            this.reviewVersionId = reviewVersionId;
            this.versionSummary(this.reviewVersionId);
        },
        changeHistoryVersion(reviewVersionId) {
            this.agreementVersion = reviewVersionId;
            const agreement = this.versions.find(item => item.reviewVersionId === reviewVersionId);
            this.versionOrder = `${this.$t('workspace.review.version')}${agreement.versionOrder}`;
            this.reviewHistory(reviewVersionId);
        },
        checkDetails(id) {
            this.staffReplyId = typeof (id) === 'string' ? id : '';
            this.aggregationDialog = true;
        },
        resetDialog() {
            this.aggregationDialog = false;
            this.staffReplyId = '';
        },
        agreementHistory() {
            this.$http.get(`/web/hubble/agreement-review/${this.reviewId}/agreement-review-history-view`).then(res => {
                // this.aggreementList = res.data;
                this.aggreementList = res.data.map((vers, index) => {
                    if ((!vers.agreementVersionHistoryFiles ||
                        !vers.agreementVersionHistoryFiles.length) &&
                        index === res.data.length - 1
                    ) {
                        vers.agreementVersionHistoryFiles = [{}];
                    }
                    return vers;
                });
            });
        },
        downloadFile(reviewVersionFileId) {
            download(`/web/hubble/agreement-review/${this.reviewId}/agreement-version-file-download/${reviewVersionFileId}`);
        },
        reUploadFiles() {
            this.$http.get(`/web/hubble/workspace/${this.workspaceId}/agreement-review/${this.reviewId}/re-upload`).then(() => {
                this.$router.push(`/hubble-workspace/review-manage?workspaceId=${this.workspaceId}&reviewId=${this.reviewId}&versions=${this.versions.length + 1}`);
            });
        },
        endReview() {
            const _this = this;
            this.$confirm(_this.$t('workspace.review.tipsContent'), _this.$t('workspace.review.tips'), {
                confirmButtonText: _this.$t('workspace.review.confirm'),
                cancelButtonText: _this.$t('workspace.review.cancel'),
            }).then(() => {
                this.$http.post(`/web/hubble/workspace/${this.workspaceId}/agreement-review/${this.reviewId}/end-review`).then(() => {
                    this.hasEnd = true;
                    this.reviewSummary();
                    this.versionSummary(this.versions[0].reviewVersionId);
                    this.$MessageToast.success(this.$t('workspace.review.successMessage'));
                });
            }).catch(() => {});
        },
        toReviewManage(reviewVersionOrder) {
            this.$router.push(`/hubble-workspace/review-manage?workspaceId=${this.workspaceId}&reviewId=${this.reviewId}&versions=${reviewVersionOrder}`);
        },
        getStaffReplyIds(list) {
            this.staffReplyIds = list.map(staff => staff.staffReplyId);
        },
        backToHome() {
            this.$router.push(`/hubble-workspace/review?workspaceId=${this.workspaceId}`);
        },
        asyncEmail() {
            this.isRefresh = true;
            this.$http.get(`/web/hubble/agreement-review/email/read-unseen-mail`).then(() => {
                this.$message.toast(this.$t('workspace.review.syncInitiated'));
                setTimeout(() => {
                    this.isRefresh = false;
                }, 1500);
            });
        },
    },
    created() {
        this.workspaceId = this.$route.query.workspaceId;
        this.reviewId = this.$route.query.review;
        this.reviewSummary();
        this.reviewVersions();
        this.agreementHistory();
        // this.init();
    },
};
</script>

<style lang="scss" scoped>
.hubble-workspace__header-left:hover {
    cursor: pointer;
}
.hubble-workspace {
    align-items: center;
    background: #f8f8f8;
    overflow-y: auto;
    &__header {
        width: 100%;
    }
}
.hubble-review-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    background-color: #fff;
    .hubble-review-header__title, .hubble-review-header__operate{
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .hubble-review-header__title {
        border-bottom: 1px solid #eee;
        p {
            width: 80%;
            font-size: 18px;
            font-weight: 500;
            line-height: 16px;
            color: #000;
            padding: 22px 0;
        }
    }
    .hubble-review-header__operate-body {
        width: 80%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 0;
        &-text {
            display: flex;
            align-items: center;
            &-doc {
                font-size: 14px;
                font-weight: 500;
                line-height: 16px;
                color: #3D3D3D;
                margin-right: 10px;
            }
            &-code {
                font-size: 12px;
                line-height: 16px;
                color: #979797;
            }
        }
        &-btn {
            display: flex;
            align-items: center;
            .el-button {
                padding: 10px 20px;
                border-radius: 5px;
                margin-top: 0;
            }
        }
    }
}
.hubble-review-body {
    width: 80%;
    display: flex;
    flex-direction: column;
    padding-top: 22px;
    &-top {
        width: 100%;
        display: flex;
        align-items: center;
        margin-bottom: 23px;
        &-part {
            flex: 1;
            padding: 16px 23px;
            background-color: #fff;
            border-radius: 5px;
            height: 100%;
            &:first-of-type {
                margin-right: 30px;
                [dir="rtl"] & {
                    margin-right: 0px;
                    margin-left: 30px;
                }
            }
            &-name {
                margin-bottom: 14px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                p {
                    font-size: 14px;
                    font-weight: 500;
                    line-height: 18px;
                    color: #333333;
                }
            }
            &-desc {
                font-size: 12px;
                font-weight: normal;
                line-height: 18px;
                color: #666666;
            }
        }
    }
    &-table {
        background-color: #fff;
        border-radius: 5px;
        margin-bottom: 20px;
        &__title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 23px;
            border-bottom: 1px solid #eee;
            span {
                font-size: 14px;
                font-weight: 500;
                line-height: 18px;
                color: #333333;
                i {
                    cursor: pointer;
                }
                i.refresh {
                    animation:fadenum 1.5s linear infinite;
                }
                @keyframes fadenum{
                    100%{transform:rotate(360deg);}
                }
            }
            .el-button {
                margin-top: 0;
                padding: 10px 20px;
                border-radius: 5px;
            }
            .el-select {
                margin-left: 6px;
            }
        }
        .el-table {
            border: none;
            &::before {
                height: 0;
            }
            &::after {
                width: 0;
            }
        }
    }
}
.el-select::v-deep .el-input .el-input__inner {
    border-radius: 3px;
}
.agreement-table::v-deep .el-table__body-wrapper {
    overflow: hidden;
}
.hubble-workspace-table-operate {
    width: 140px!important;
}
</style>
