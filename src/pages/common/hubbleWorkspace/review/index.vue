<template>
    <div class="hubble__term">
        <div class="hubble-workspace__title">
            <span>{{ $t('workspace.review.manageReview') }}</span>
            <div>
                <el-button class="hubble-workspace__title-btn" @click="createReview">{{ $t('workspace.review.createReview') }}</el-button>
            </div>
        </div>
        <div class="hubble-workspace-table">
            <el-table
                :data="reviewList"
                row-class-name="custom-row"
                :border="false"
                style="width: 100%"
            >
                <el-table-column
                    prop="reviewId"
                    :label="$t('workspace.review.reviewId')"
                ></el-table-column>
                <el-table-column
                    prop="reviewStatus"
                    :label="$t('workspace.review.reviewStatus')"
                >
                    <template slot-scope="scope">
                        {{ getStatus(scope.row.reviewStatus) }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="reviewName"
                    :label="$t('workspace.review.reviewName')"
                ></el-table-column>
                <el-table-column
                    prop="reviewStartTime"
                    :label="$t('workspace.review.reviewStartTime')"
                >
                    <template slot-scope="scope">
                        {{ renderDate(scope.row.reviewStartTime) }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="reviewCompleteTime"
                    :label="$t('workspace.review.reviewCompleteTime')"
                >
                    <template slot-scope="scope">
                        {{ renderDate(scope.row.reviewCompleteTime) }}
                    </template>
                </el-table-column>
                <el-table-column
                    :label="$t('workspace.operate')"
                    width="180"
                >
                    <template slot-scope="scope">
                        <div class="hubble-workspace-table-operate">
                            <span @click="reviewDetails(scope.row.reviewId)">{{ $t('workspace.detail') }}</span>|<span @click="deleteReview(scope.row.reviewId)">{{ $t('workspace.delete') }}</span>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script>
import dayjs from 'dayjs';
export default {
    data() {
        return {
            reviewList: [],
            workspaceId: '',
        };
    },
    computed: {
        renderDate() {
            return (stamp) => {
                if (!stamp) {
                    return '';
                }
                return dayjs(stamp).format('YYYY-MM-DD  HH:mm:ss');
            };
        },
        getStatus() {
            return (state) => {
                switch (state) {
                    case 'CREATED':
                        return this.$t('workspace.create');
                    case 'REVIEWING':
                        return this.$t('workspace.reviewing');
                    case 'COMPLETED':
                        return this.$t('workspace.completed');
                    default:
                        return '';
                }
            };
        },
    },
    methods: {
        async getReviewList() {
            await this.$http.get(`/web/hubble/workspace/${this.workspaceId}/agreement-review/list`).then(res => {
                this.reviewList = res.data.agreementReviews;
            });
        },
        init() {
            this.getReviewList();
        },
        async createReview() {
            await this.$http.get(`/web/hubble/workspace/${this.workspaceId}/agreement-review/init`).then(res => {
                const reviewId = res.data.reviewId;
                this.$router.push(`/hubble-workspace/review-manage?workspaceId=${this.workspaceId}&reviewId=${reviewId}`);
            });
        },
        async deleteReview(reviewId) {
            await this.$http.post(`/web/hubble/workspace/${this.workspaceId}/agreement-review/delete/${reviewId}`).then(() => {
                this.getReviewList();
            });
        },
        reviewDetails(id) {
            this.$router.push(`/hubble-workspace/review-details?workspaceId=${this.workspaceId}&review=${id}`);
        },
    },
    created() {
        this.workspaceId = this.$route.query.workspaceId;
        this.init();
    },
};
</script>

<style lang="scss">
.hubble__term {
  display: flex;
  flex-direction: column;
  height: 100%;
  &-dialog{
      .el-dialog{
          width: 420px;
          border-radius: 8px;
          overflow: hidden;
          .el-select{
              width: 100%;
          }
      }
      .el-upload {
          width: 100%;
          .el-button{
              width: 100%;
              line-height: 60px;
              padding: 0;
              text-align: center;
              border-radius: 5px;
              border-style: dashed;
          }
      }
      &-footer{
          margin-top: 20px;
          display: flex;
          justify-content: space-between;
      }
  }
}
</style>
<style lang="scss" scoped>
.hubble-workspace-table-operate {
    width: 120px!important;
}
</style>
