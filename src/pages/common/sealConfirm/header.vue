<template>
    <div class="seal-confirm-header">
        <div class="content">
            <h1 class="ssq-logo inline-block baseline-middle" v-if="!logoConfig || logoConfig.visible">
                <template v-if="russiaLogo">
                    <router-link tag="div" to="/account-center/home" class="ilBlock">
                        <img class="default-logo cursor-point" :src="`/ents/logo?t=${Date.parse(new Date())}`" width="86" height="37" alt="">
                    </router-link>
                </template>
                <template v-else-if="!logoConfig || logoConfig.clickAble">

                    <router-link v-if="(hostEnv == 'official' || HOST_ENV == 'CCB')" tag="div" to="/account-center/home" class="ilBlock">
                        <img class="default-logo cursor-point" :src="GLOBAL.WHITE_LOGO" width="86" height="37" alt="">
                    </router-link>
                    <img v-else
                        class="default-logo"
                        :src="GLOBAL.WHITE_LOGO"
                        width="86"
                        height="37"
                        alt=""
                    >
                </template>
                <template v-else>
                    <img class="default-logo"
                        :class="(hostEnv == 'official' || HOST_ENV == 'CCB') && 'cursor-point'"
                        :src="GLOBAL.WHITE_LOGO"
                        width="86"
                        height="37"
                        alt=""
                    >
                </template>
            </h1>
            <span class="line inline-block baseline-middle">line</span>
            <span class="login-title baseline-middle"
                v-if="developLogoUrl"
            >
                <img class="baseline-middle" :src="developLogoUrl" height="37" width="100" alt="">
            </span>
            <p class="inline-block baseline-middle certification-type">
                {{ $t('sealConfirm.title') }}
            </p>
            <div class="right-content">
                <span class="user-name"><span v-if="!getIsForeignVersion">{{ $t('entAuth.hi') }}，</span><em class="name">{{ userInfo.platformUser.fullName ? userInfo.platformUser.fullName : userInfo.platformUser.account }}</em></span>
                <template v-if="hostEnv == 'official' || HOST_ENV == 'CCB'">
                    <span @click="toLogout" class="cursor-point common-font-color logout">{{ $t('entAuth.exit') }}</span>
                    <span class="line-txt"> | </span>
                    <a class="cursor-point common-font-color help" target="_blank" href="http://bestsign.udesk.cn/hc" rel="noopener noreferrer">{{ $t('entAuth.help') }}</a>
                </template>
                <span class="line-txt"> | </span>
                <span class="hotline">{{ $t('entAuth.hotline') }}：400-993-6665</span>
                <LangSwitch></LangSwitch>
            </div>
        </div>
    </div>
</template>
<script>
import { mapGetters } from 'vuex';
import LangSwitch from 'components/langSwitch';

export default {
    components: { LangSwitch },
    props: {
        'hostEnv': {
            type: String,
            default: 'official',
        },
        'logoConfig': {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            HOST_ENV: this.GLOBAL.HOST_ENV,
            developLogoUrl: '',
            userInfo: {
                currentEntId: '',
                hasManager: false,
                enterprises: [],
                platformUser: {
                    fullName: '',
                },
                userType: '',
            },
            russiaLogo: 0,
        };
    },
    computed: {
        commonHeaderInfo: function() {
            return this.$store.state.commonHeaderInfo;
        },
        ...mapGetters(['getIsForeignVersion']),
    },
    watch: {
        commonHeaderInfo: {
            handler(val) {
                this.userInfo = val;
            },
            deep: true,
        },
        hostEnv() {
            this.setDevelopLogoUrl();
        },
    },
    methods: {
        toLogout() {
            this.$http.logOut()
                .then(() => {
                    this.$router.push('/login');
                });
        },
        setDevelopLogoUrl() {
            if (!(this.hostEnv === 'official' || this.HOST_ENV === 'CCB')) {
                this.$http.get('/ents/developer/info')
                    .then(res => {
                        this.developLogoUrl = res.data.entLogoUrl;
                    });
            }
        },
    },
    created: function() {
        this.userInfo = this.commonHeaderInfo;
        this.setDevelopLogoUrl();
        this.russiaLogo = ~~this.$cookie.get('homeLogoFileId');
    },
};
</script>
<style lang="scss">
	.seal-confirm-header {
		height: 63px;
		background-color: #002b45;
		font-size: 12px;
		.inline-block {
			display: inline-block;
		}
		.vertical-middle {
			vertical-align: middle;
		}
		.baseline-middle {
			vertical-align: -webkit-baseline-middle;
		}
		.content {
			position: relative;
			width: 1000px;
			margin: 0 auto;
		}
		.ssq-logo {
			height: 63px;
			line-height: 56px;
			text-align: right;
			color: #fff;
			i {
				font-size: 40px;
				vertical-align: middle;
			}
		}
		.line {
			height: 30px;
			margin-left: 20px;
			margin-right: 17px;
			border-right: 1px solid #4d6b7d;
			text-indent: -1000px;
			vertical-align: middle;
		}
		.login-title {
				display: inline-block;
				font-size: 24px;
				color: #666;
				margin-right: 12px;
			}
		.certification-type {
			font-size: 16px;
			font-weight: bold;
			color: #fff;
			line-height: 63px;
		}

		.right-content {
			position: absolute;
			right: 0;
			top: 20px;
			.user-name {
				padding-left: 18px;
				color: #fff;
			}
			.hotline {
				color: #fff;
			}
			span:nth-child(2n+1) {
				padding-left: 7px;
				padding-right: 7px;
			}
			span.line-txt {
				color: #4d6b7d;
			}
		}
        .cursor-point {
            cursor: pointer;
        }
	}
</style>
