<!--印章确认页-->
<template>
    <div>
        <CommonHeader v-if="isPC" :headerTitle="$t('sealConfirm.title')" :needLogin="0"></CommonHeader>
        <div class="seal-confirm" :class="{'seal-confirm__mobile': !isPC}">
            <div class="seal-confirm-title">
                <i class="el-icon-warning"></i>
                <h1>{{ $t('sealConfirm.header') }}</h1>
            </div>

            <div class="seal-confirm-content">
                <div class="seal-confirm-content__detail">
                    <div class="info-row">
                        <p class="info-name">{{ $t('sealConfirm.signerEnt') }}</p>
                        <p class="info-text ent-name">{{ signerEntName }}</p>
                    </div>
                    <div class="info-row">
                        <p class="info-name">{{ $t('sealConfirm.abnormalSeal') }}</p>
                        <div class="sealList">
                            <div v-for="(perSeal, index) in sealIdList" :key="index">
                                <img class="sealImg" :src="`/ents/${perSeal}/seal/0?entId=${signerEntId}`" alt="sealImg">
                            </div>
                        </div>
                    </div>
                </div>
                <el-button type="primary" @click="handleSealConfirm">{{ $t('sealConfirm.sealNormal') }}</el-button>
                <div class="seal-confirm-content__tip">
                    <p>{{ $t('sealConfirm.tip1') }}</p>
                    <p>{{ $t('sealConfirm.tip2') }}</p>
                </div>
            </div>
        </div>
        <CommonFooter v-if="isPC" class="seal-confirm-footer"></CommonFooter>
    </div>
</template>

<script>
import CommonHeader from './header.vue';
import CommonFooter from 'components/register_footer/RegisterFooter.vue';
import { isPC } from 'src/common/utils/device.js';
export default {
    name: 'SealConfirm',
    components: {
        CommonHeader,
        CommonFooter,
    },
    data() {
        return {
            isPC: isPC(),
            urlToken: this.$route.query.token || '',
            sealIdList: [],
            signerEntName: '',
            signerEntId: '',
            contractId: '',
        };
    },
    methods: {
        handleSealConfirm() {
            this.$http.post(`/contract-api/contracts/${this.contractId}/failed-seal-recognition/confirm?token=${this.urlToken}`)
                .finally(() => {
                    const jumpUrl = this.isPC ? `/account-center/home` : '/mobile/home';
                    this.$router.push(jumpUrl);
                });
        },
        getSealConfirmInfo() {
            this.$http.get(`/users/ignore/token-info?token=${this.urlToken}`)
                .then((res) => {
                    const { attachment: { sealIdList, signerName, signerEntId, contractId } } = res.data;
                    this.sealIdList = sealIdList || [];
                    this.signerEntName = signerName || '';
                    this.signerEntId = signerEntId || '';
                    this.contractId =  contractId || '';
                });
        },
    },
    mounted() {
        this.getSealConfirmInfo();
    },
};
</script>

<style lang="scss">
.seal-confirm {
    padding: 40px 0;
    color: #333;
    &-title {
        text-align: center;
        font-size: 16px;
        font-weight: 500;
        i {
            font-size: 35px;
            line-height: 60px;
        }
    }
    &-content {
        width: 520px;
        margin: 0 auto;
        &__detail {
            padding: 25px 0;
            .info-row {
                line-height: 30px;
                .info-name {
                    display: inline-block;
                    width: 90px;
                    vertical-align: top;
                    color: #666;
                }
                .info-text {
                    display: inline-block;
                }
                .ent-name {
                    line-height: 20px;
                    margin-top: 4px;
                }
                .sealList {
                    width: 520px;
                    overflow-x: scroll;
                    display: flex;
                    .sealImg {
                        width: 120px;
                        height: 120px;
                        margin: 10px 10px 0 0;
                        padding: 10px;
                        border: 1px dashed #eee;
                        box-sizing: border-box;
                    }
                }
            }

        }
        &__tip {
            p {
                margin: 20px 0;
                color: #666;
                font-size: 14px;
            }
        }
    }

    &-footer {
        position: fixed;
        bottom: 0;
    }
}
.seal-confirm__mobile {
    color: #333;
    .seal-confirm-content {
        width: calc(100% - 40px);
        padding: 20px;
        &__detail {
            .info-row {
                .sealList {
                    width: 100%;
                }
            }
        }
    }
}
</style>
