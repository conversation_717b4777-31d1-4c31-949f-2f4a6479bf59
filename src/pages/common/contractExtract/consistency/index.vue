<template>
    <div class="consistency-info">
        <div class="keywords">
            <p class="section-title">关键词:</p>
            <el-input v-model="expectedValue" placeholder="输入你期望检查的数据内容，例如：采购合同" class="input-with-select" @keyup.enter.native="handleEnter">
                <el-select v-model="submitFieldId" slot="prepend" placeholder="请选择">
                    <el-option :label="opt.extractSubmitFieldName" v-for="opt in fields" :key="opt.extractSubmitFieldId" :value="opt.extractSubmitFieldId"></el-option>
                </el-select>
                <el-button :disabled="!expectedValue || !submitFieldId" type="primary" slot="append" @click="checkConsistency" :loading="consistencyLoading">一致性检查</el-button>
            </el-input>
        </div>
        <div class="section-title">
            <p>检查结果:</p>
        </div>
        <div class="consistency-result">
            <div class="result">
                <div class="check-time" v-show="hasConsistency">
                    <p class="time">检查时间：{{ time(createTime) }}</p>
                    <p class="passed" v-if="consistencyResults.judgement === 1"><i class="el-icon-ssq-success_hollow"></i>通过</p>
                    <p class="passed" v-if="consistencyResults.judgement === 0"><i class="el-icon-ssq-error_hollow"></i>不通过</p>
                    <p class="passed" v-if="consistencyResults.judgement === 2"><i class="el-icon-ssq-info_hollow"></i>需人工确认</p>
                </div>
                <div class="consistency-reason"
                    v-show="hasConsistency"
                >
                    <span
                        v-for="(item, index) in summaryParagraphs"
                        :key="index"
                    >
                        {{ item.content }}
                        <span
                            class="sentence-number"
                            :class="{'select-mark': index+1 === indexMark}"
                            @click="setSelectMark(index)"
                        >{{ index + 1 }}</span>
                    </span>
                </div>
                <div class="consistency-guide" v-show="hasConsistency">
                    <div>{{ $t('contractCompare.keywordPosition') }}</div>
                    <span
                        class="sentence-number"
                        :class="{'select-mark': index+1 == indexMark}"
                        v-for="(item, index) in summaryParagraphs"
                        :key="index"
                        @click="setSelectMark(index)"
                    >{{ index + 1 }}</span>
                </div>
                <div class="no-extract-info" v-show="!hasConsistency">
                    <NoData />
                </div>
            </div>
        </div>
        <div class="compare-loading recognize-loading" v-show="consistencyLoading" style="top: 70px">
            <div class="compare-loading_content">
                <img src="~img/contractHubble/hubble-loading.gif" alt="">
                <p v-show="consistencyLoading">一致性检查中</p>
            </div>
        </div>
    </div>
</template>

<script>
import NoData from 'components/noData';
import { getConsistency, consistencyResult } from 'src/api/extract.js';
import moment from 'dayjs';

export default {
    components: { NoData },
    props: {
        extractTaskId: {
            type: String,
            default: '',
        },
        hasResult: {
            type: Boolean,
            default: false,
        },
        fields: {
            type: Array,
            default: () => [],
        },
        selectTab: {
            type: String,
            default: '1',
        },
    },
    data() {
        return {
            expectedValue: '',
            submitFieldId: '',
            extractResult: false,
            consistencyResults: {},
            createTime: '',
            indexMark: 1,
            consistencyLoading: false,
            summaryParagraphs: [],
        };
    },
    computed: {
        hasConsistency() {
            if (Object.keys(this.consistencyResults).length) {
                return true;
            } else {
                return false;
            }
        },
        time() {
            return (val) => {
                if (val) {
                    return moment(val).format('YYYY-MM-DD HH:mm:ss');
                } else {
                    return '';
                }
            };
        },
    },
    watch: {
        selectTab() {
            if (this.selectTab === '4') {
                this.indexMark = 1;
            }
        },
    },
    methods: {
        handleEnter() {
            if (!this.expectedValue || !this.submitFieldId) {
                return;
            }
            if (this.expectedValue && this.submitFieldId) {
                this.checkConsistency();
            }
        },
        async checkConsistency() {
            this.consistencyLoading = true;
            const data = {
                expectedConsistencyDataList: [
                    {
                        expectedValue: this.expectedValue,
                        submitFieldId: this.submitFieldId,
                    },
                ],
            };
            await getConsistency(this.extractTaskId, data).then((res) => {
                if (res.data.code === '1') {
                    this.getResult();
                }
            }).catch((err) => {
                this.$message({
                    message: err.message,
                    type: 'error',
                });
                this.consistencyLoading = false;
            });
        },
        async getResult() {
            await consistencyResult(this.extractTaskId).then(({ data: { createTime, extractConsistencyResults } }) => {
                this.createTime = createTime;
                if (extractConsistencyResults.length) {
                    this.consistencyResults = extractConsistencyResults.find(result => result.submitFieldId === this.submitFieldId) || {};
                    this.summaryParagraphs = this.consistencyResults.contentPositions;
                }
                if (this.consistencyResults.contentPositions) {
                    const fragment = this.consistencyResults.contentPositions.map(content => content.contentPositions.map(position => [position]));
                    // const fragment = number.map(num => [num]);
                    // console.log(fragment);
                    this.$emit('changeConsistencyFragment', fragment);
                }
            }).finally(() => {
                this.consistencyLoading = false;
            });
        },
        setSelectMark(number) {
            this.indexMark = number + 1;
            this.$emit('selectShadow', number);
        },
        reset() {
            this.expectedValue = '';
            this.submitFieldId = '';
            this.consistencyResults = {};
            this.createTime = '';
            this.indexMark = 1;
        },
    },
};
</script>

<style lang="scss" scoped>
@import '../index.scss';
$--color-primary: #127FD2;
$--color-border: #EEEEEE;
$--color-text-second: #666666;

.consistency-info {
  height: 100%;
  overflow-y: auto;
  padding: 30px;
  padding-top: 15px;
  border-radius: 8px;
  background: #FFFFFF;
  display: flex;
  flex-direction: column;
  .field-title {
      display: inline-block;
      margin-right: 10px;
      margin-bottom: 8px;
      height: 36px;
      line-height: 36px;
      padding: 0 15px;
      background-color: #F4FAFF;
      color: $theme-color;
  }
  .field-info {
      margin-bottom: 8px;
      white-space: break-spaces;
  }
  .no-extract-info {
      transform: translateY(-56px);
      position: relative;
      top: 50%;
  }
}
.section-title {
    margin-bottom: 10px;
}
.keywords {
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    margin-bottom: 10px;
}
.keywords::v-deep .el-select .el-input {
    width: 130px;
}
.keywords::v-deep .el-button--primary {
    color: #fff;
    background-color: #20a0ff;
    border-color: #20a0ff;
}
.keywords::v-deep .el-button--primary.is-disabled {
    color: #bfcbd9;
    background-color: #eef1f6;
    border-color: #d1dbe5;
}
.consistency-guide {
    padding-top: 10px;
    .sentence-number {
        margin-top: 8px;
    }
}
.sentence-number {
    display: inline-block;
    margin-right: 3px;
    min-width: 16px;
    padding: 0 4px;
    height: 16px;
    border-radius: 8px;
    color: $--color-text-second;
    background-color: $--color-border;
    cursor: pointer;
}
.select-mark {
    color: $--color-primary;
    background-color: rgba(77, 179, 255, 0.2);
}
.consistency-result {
    flex-grow: 1;
    background-color: #F8F8F8;
    padding: 12px 19px;
    .result {
        display: flex;
        flex-direction: column;
        height: 100%;
    }
    .check-time {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 8px;
        border-bottom: 1px solid #E1E0E0;
        margin-bottom: 14px;
        .time {
            color: #999999;
            font-size: 12px;
            line-height: 22px;
        }
        .passed {
            color: #0C8AEE;
            font-size: 14px;
            font-weight: normal;
            line-height: 22px;
            i {
                display: inline-block;
                margin-right: 6px;
            }
        }
    }
    .consistency-reason {
        padding-bottom: 13px;
        font-size: 14px;
        line-height: 22px;
        color: #3D3D3D;
        flex-grow: 1;
        overflow-y: auto;
    }
}
</style>
