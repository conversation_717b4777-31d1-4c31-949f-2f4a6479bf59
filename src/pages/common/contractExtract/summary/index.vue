<template>
    <div class="contract-extract-summary">
        <div>{{ $t('contractCompare.keyword') }}</div>
        <div class="summary-tab">
            <el-tabs v-model="activeKeyword" type="card">
                <el-tab-pane
                    v-for="(item, index) in summary"
                    :key="item.keyword.keyword"
                    :label="item.keyword.keyword"
                    :name="'' + index"
                >
                </el-tab-pane>
            </el-tabs>
        </div>
        <div>{{ $t('contractCompare.keySummary') }}</div>
        <div class="summary-content">
            <div
                class="summary-content-sentence"
                v-for="item in summaryParagraphs"
                :key="item.paragraphIndex"
            >
                <span v-for="(s, index) in item.summarySentences" :key="index">
                    {{ s.sentence }}
                    <span
                        v-for="mark in s.relatedDocumentParagraphs"
                        :key="mark.markNumber"
                        class="sentence-number"
                        :class="{'select-mark': mark.markNumber == numberMark}"
                        @click="setSelectMark(mark.markNumber)"
                    >{{ mark.markNumber }}</span>
                </span>
            </div>
        </div>
        <div class="summary-guide">
            <div>{{ $t('contractCompare.keywordPosition') }}</div>
            <span
                class="sentence-number"
                :class="{'select-mark': item.markNumber == numberMark}"
                v-for="item in numbers"
                v-show="item.contentPositions.length"
                :key="item.markNumber"
                @click="setSelectMark(item.markNumber)"
            >{{ item.markNumber }}</span>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        summary: {
            type: Array,
            default: () => [],
        },
        selectTab: {
            type: String,
            default: '1',
        },
    },
    data() {
        return {
            activeKeyword: '-1',
            numberMark: 1,
            summaryParagraphs: [],
            numbers: [],
            tempNumbers: [],
        };
    },
    watch: {
        selectTab() {
            if (this.selectTab === '3') {
                this.numberMark = 1;
            }
        },
        summary: {
            deep: true,
            handler(val) {
                this.activeKeyword = '-1';
                if (val.length === 0) {
                    this.summaryParagraphs = [];
                }
                setTimeout(() => {
                    this.activeKeyword = '0';
                }, 200);
            },
        },
        activeKeyword() {
            if (this.activeKeyword === '-1') {
                return;
            }
            this.numberMark = 1;
            this.numbers = [];
            this.tempNumbers = [];
            this.summaryParagraphs = this.summary[this.activeKeyword].summaryParagraphs;
            this.summaryParagraphs.forEach((item) => {
                item.summarySentences.forEach((s) => {
                    s.relatedDocumentParagraphs.forEach((mark) => {
                        this.setMarket(mark);
                    });
                });
            });
            // 处理空值
            for (let i = 0; i < this.tempNumbers.length; i++) {
                if (!this.tempNumbers[i]) {
                    this.tempNumbers[i] = { markNumber: `${i + 1}`, contentPositions: [] };
                }
            }
            this.numbers = [...this.tempNumbers];
            const fragment = this.numbers.map((number) => number.contentPositions.map((s) => [s]));
            this.$emit('changeSummaryFragment', fragment);
        },
    },
    methods: {
        setSelectMark(number) {
            this.numberMark = number;
            this.$emit('selectShadow', number - 1);
        },
        setMarket({ markNumber, contentPositions }) {
            const tempNumbers = [...this.tempNumbers];
            if (!tempNumbers.find((item) => item?.markNumber === markNumber)) {
                tempNumbers[markNumber - 1] = {
                    markNumber,
                    contentPositions,
                };
                this.tempNumbers = tempNumbers;
            }
        },
    },
};
</script>

<style lang="scss">
$--color-primary: #127FD2;
$--color-border: #EEEEEE;
$--color-text-primary: #333333;
$--color-text-second: #666666;
$--background-color-base: #F6F6F6;

.contract-extract-summary {
    height: 100%;
    overflow-y: auto;
    padding: 30px;
    padding-top: 15px;
    border-radius: 8px;
    background: #FFFFFF;
    .el-tabs__header {
        border: none;
        .el-tabs__item {
            margin: 0 8px;
            background-color: $--color-border;
            border: none;
            border-radius: 4px;
            color: $--color-text-primary;
            &.is-active {
                border: none;
                border-radius: 4px;
                color: $--color-primary;
                background-color: rgba(77, 179, 255, 0.2);
            }
        }
    }
    .summary-tab {
        height: 70px;
        border-bottom: 1px solid $--color-border;
        margin: 12px 0;
    }
    .summary-content {
        border-bottom: 1px solid $--color-border;
        height: calc(100% - 190px);
        padding-top: 8px;
        overflow-y: scroll;
        .summary-content-sentence {
            color: $--color-text-second;
            font-size: 13px;
            margin-bottom: 12px;
        }
    }
    .summary-guide {
        padding-top: 10px;
        .sentence-number {
            margin-top: 8px;
        }
    }
    .sentence-number {
        display: inline-block;
        margin-right: 3px;
        min-width: 16px;
        padding: 0 4px;
        height: 16px;
        border-radius: 8px;
        color: $--color-text-second;
        background-color: $--color-border;
        cursor: pointer;
    }
    .select-mark {
        color: $--color-primary;
        background-color: rgba(77, 179, 255, 0.2);
    }
}
</style>
