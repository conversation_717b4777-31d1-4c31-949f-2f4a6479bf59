<template>
    <div class="contract-review">
        <Header ref="header"
            :config="{
                productType: 21,
                toolType: '合同内容抽取',
            }"
        ></Header>
        <div class="contract-review__head">
            <div class="contract-review__head-box">
                <div class="contract-review__head-left">
                    <el-tooltip :disabled="!leftName" :content="leftName" placement="bottom">
                        <span class="ellipsis">{{ $t('contractCompare.extractTargetFile') }}：{{ leftName }}</span>
                    </el-tooltip>
                    <el-button :disabled="!leftDocReady || leftDocumentLoading || needNewExtractId" @click="handleScan">识别扫描件</el-button>
                    <el-button :disabled="!leftDocReady || leftDocumentLoading" @click="clear">重新上传</el-button>
                </div>
                <div class="contract-review__head-right">
                    <div class="right-tab">
                        <el-tabs v-model="selectTab" @tab-click="viewSummary">
                            <el-tab-pane v-if="!hasResult" name="1" :label="$t('contractCompare.extractKeyWord')"></el-tab-pane>
                            <el-tab-pane name="2" :label="$t('contractCompare.extractResult')"></el-tab-pane>
                            <el-tab-pane name="3" :label="$t('contractCompare.summary')"></el-tab-pane>
                            <el-tab-pane name="4" label="一致性检查"></el-tab-pane>
                            <el-tab-pane name="5" label="数据格式化"></el-tab-pane>
                        </el-tabs>
                    </div>
                    <div>
                        <el-button type="primary" :loading="btnLoading" :disabled="needNewExtractId || !leftDocReady || leftDocumentLoading" @click="startExtract">{{ $t('contractCompare.startExtract') }}</el-button>
                        <el-button type="primary" :loading="summaryLoading" :disabled="!hasResult || status === 'PENDING' || status === 'SUCCESS'" @click="handleStartSummary">生成合同摘要</el-button>
                    </div>
                </div>
            </div>
            <div class="contract-review__result flex">
                <div class="head-tab" @click="changeTab(1)" :class="selectedTab === 1 ? 'selected-tab' : ''">
                    <div class="head-tab_title">{{ $t('contractCompare.history') }}</div>
                    <div class="head-tab_info">{{ $t('contractCompare.historyLog', {num: total}) }}</div>
                </div>
                <div v-if="$route.query.contractId" class="head-tab" @click="changeTab(2)" :class="selectedTab === 2 ? 'selected-tab' : ''">
                    <div class="head-tab_title">{{ $t('contractCompare.currentHistory') }}</div>
                    <div class="head-tab_info">{{ $t('contractCompare.historyLog', {num: currentHistory.length}) }}</div>
                </div>
            </div>
        </div>
        <div class="contract-review__content">
            <div class="contract-review__content-box">
                <div class="contract-review__content-left" v-loading="leftLoading">
                    <Upload
                        v-if="!leftDocReady"
                        :uploadUrl="uploadUrl"
                        @uploadSuccess="readyCompare"
                    ></Upload>
                    <Document
                        v-else
                        ref="leftDocument"
                        :fileUrl="downloadUrl"
                        :fragment="fragment"
                        :showShadowMode="showShadowMode"
                        @changeLoading="(val) => leftDocumentLoading = val"
                        @changeFileName="(val) => leftName = val"
                    />
                </div>
                <div class="contract-review__content-right" v-loading="rightLoading">
                    <Extract
                        ref="extract"
                        v-show="selectTab === '1'"
                        :extractTaskId="extractTaskId"
                        :title="$t('contractCompare.extractTitle')"
                        :info="$t('contractCompare.selectKeyword')"
                        :addInfo="addFieldInfo"
                        :deleteInfo="deleteFieldInfo"
                        @changeFields="(val) => fields = val"
                    />
                    <ExtractInfo v-show="selectTab === '2'" :result="result" />
                    <Summary
                        v-show="selectTab === '3'"
                        :summary="summary"
                        :selectTab="selectTab"
                        @changeSummaryFragment="changeSummaryFragment"
                        @selectShadow="selectShadow"
                    />
                    <Consistency
                        ref="consistency"
                        :hasResult="hasResult"
                        v-show="selectTab === '4'"
                        :extractTaskId="extractTaskId"
                        :selectTab="selectTab"
                        :fields="fields"
                        @selectShadow="selectShadow"
                        @changeConsistencyFragment="changeConsistencyFragment"
                    />
                    <Format
                        ref="format"
                        :hasResult="hasResult"
                        v-show="selectTab === '5'"
                        :extractTaskId="extractTaskId"
                        :fields="fields"
                    />
                </div>
            </div>
            <div class="contract-review__result">
                <template v-if="selectedTab === 1">
                    <div class="contract-review__result-content">
                        <div
                            class="contract-review__result-content-item history"
                            v-for="(his, i) in history"
                            :key="i"
                            @click="goHistory(his)"
                        >
                            <el-tooltip :content="his.contractName" placement="top">
                                <p class="title ellipsis">{{ his.contractName }}</p>
                            </el-tooltip>
                            <p class="info">
                                <span>{{ moment(his.createTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
                                <span>{{ getReviewStatus(his.taskStatus) }}</span>
                            </p>
                        </div>
                        <NoData v-if="!history.length" />
                    </div>
                    <el-pagination
                        small
                        layout="pager"
                        @current-change="handleCurrentChange"
                        :pager-count="5"
                        :page-size="10"
                        :current-page.sync="page"
                        :total="total"
                    >
                    </el-pagination>
                </template>
                <template v-if="selectedTab === 2">
                    <div class="contract-review__result-content">
                        <div
                            class="contract-review__result-content-item history"
                            v-for="(his, i) in currentHistory"
                            :key="i"
                            @click="goHistory(his)"
                        >
                            <el-tooltip :content="his.title" placement="top">
                                <p class="title ellipsis">{{ his.title }}</p>
                            </el-tooltip>
                            <p class="info">
                                <span>{{ moment(his.createTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
                                <span>{{ getReviewStatus(his.status) }}</span>
                            </p>
                        </div>
                        <NoData v-if="!currentHistory.length" />
                    </div>
                </template>
            </div>
        </div>
        <RegisterFooter v-if="isPC" class="login-footer absolute-login-footer"></RegisterFooter>
        <div class="compare-loading recognize-loading" v-show="extractLoading || recognizing || checksummarize" style="top: 70px">
            <div class="compare-loading_content">
                <img src="~img/contractHubble/hubble-loading.gif" alt="">
                <p v-show="extractLoading">{{ $t('contractCompare.extracting') }}</p>
                <p v-show="recognizing">扫描件识别中</p>
            </div>
        </div>
    </div>
</template>

<script>
import moment from 'dayjs';
import Upload from './upload';
import Header from 'components/hubbleApplyHeader';
import RegisterFooter from 'components/register_footer/RegisterFooter.vue';
import NoData from 'components/noData';
import Document from './document';
import Extract from './exTract';
import ExtractInfo from './extractInfo';
import Summary from './summary';
import Consistency from './consistency';
import Format from './format';
import {
    getExtractHistory,
    startExtract,
    initExtractTask,
    recognizeScan,
    getExtractResult,
    getHistoryExtractResult,
    calcBilling,
    getSummary,
    addFieldInfo,
    deleteFieldInfo,
    startSummary,
} from 'src/api/extract.js';
import { initHubbleTask, currentDocumentHistory } from 'src/api/docTranslation.js';
import { isPC } from 'src/common/utils/device.js';

const statusMap = {
    SUCCESS: '已完成',
    NOT_START: '未开始',
    PENDING: '抽取中',
};

export default {
    components: { Upload, Header, RegisterFooter, NoData, Document, Extract, ExtractInfo, Summary, Consistency, Format },
    data() {
        return {
            leftFragment: [],
            leftName: '',
            leftLoading: false,
            rightLoading: false,
            history: [],
            btnLoading: false,
            leftDocumentLoading: false,
            noDataText: this.$t('contractCompare.noData'),
            needNewExtractId: false,
            extractLoading: false,
            extractTaskId: '',
            leftDocReady: false,
            page: 1,
            total: 0,
            background: {},
            recognizing: false,
            hasResult: false,
            isPC: isPC(),
            selectTab: '1',
            result: [],
            summary: [],
            summaryFragment: [],
            selectedTab: 1,
            currentHistory: [],
            // docHistoryPage: 1,
            // docHistorytotal: 0,
            pdfUrl: '',
            fields: [],
            consistencyFragment: [],
            summaryLoading: false,
            checksummarize: false,
            status: '',
        };
    },
    computed: {
        uploadUrl() {
            return `/web/hubble/extract-content/${this.extractTaskId}/upload-contract`;
        },
        downloadUrl() {
            if (this.pdfUrl) {
                return `/web/hubble${this.pdfUrl}`;
            } else {
                return `/web/hubble/extract-content/${this.extractTaskId}/download-file`;
            }
        },
        showShadowMode() {
            return this.selectTab !== '3';
        },
        fragment() {
            if (this.selectTab === '3') {
                return this.summaryFragment;
            } else if (this.selectTab === '4') {
                return this.consistencyFragment;
            } else {
                return this.leftFragment;
            }
        },
    },
    methods: {
        addFieldInfo,
        deleteFieldInfo,
        moment,
        clear() {
            if (!this.$route.query.extractTaskId) {
                this.needNewExtractId = true;
                this.pdfUrl = '';
            }
            if (this.needNewExtractId) {
                this.clearTimer('extractTimer');
                this.clearTimer('summaryTimer');
                this.initTask().then(() => {
                    this.needNewExtractId = false;
                    this.leftDocReady = false;
                    this.leftName = '';
                    this.extractLoading = false;
                    this.$refs.extract.reset();
                    this.$refs.consistency.reset();
                    this.$refs.format.reset();
                });
            } else {
                this.leftDocReady = '';
                this.leftName = '';
            }
            this.selectTab = '1';
            this.result = [];
            this.summary = [];
            this.status = '';
            this.leftFragment = [];
            this.noDataText = '';
            this.fields = [];
            this.summaryLoading = false;
        },
        readyCompare({ fileName }) {
            this.leftDocReady = true;
            this.leftName = fileName;
        },
        startExtract() {
            if (!this.$refs.extract.fields.length) {
                return this.$MessageToast(this.$t('contractCompare.needExtractKeyword'));
            }
            this.btnLoading = true;
            calcBilling(this.extractTaskId).then((res) => {
                this.calcTip(res.data);
            }).finally(() => {
                this.btnLoading = false;
            });
        },
        calcTip({ balanceEnough, enterpriseName, description }) {
            const content1 = description;
            const content2 = `扣除主体：${enterpriseName}`;
            const content3 = '可用页数不足，请购买充值。';
            const btnText = balanceEnough ? this.$t('contractCompare.confirm') : this.$t('contractCompare.toBuy');
            const h = this.$createElement;
            const content = balanceEnough ? h('p', null, [
                h('p', null, content1),
                h('p', { style: 'margin-top: 40px' }, content2),
            ]) : h('p', null, [
                h('p', null, content1),
                h('p', { style: 'margin-top: 30px' }, content2),
                h('p', { style: 'margin-top: 30px' }, content3),
            ]);
            this.$confirm(content, this.$t('contractCompare.tip'), {
                confirmButtonText: btnText,
                showCancelButton: true,
                customClass: 'compare-calc-billing',
            }).then(() => {
                if (balanceEnough) {
                    this.extractLoading = true;
                    startExtract(this.extractTaskId, this.$route.query.mode, this.$route.query.preferredModel)
                        .then(() => {
                            this.handleStartExtract();
                        });
                } else {
                    this.$refs.header.openChargeDialog();
                }
            }).catch(() => {});
        },
        handleStartExtract() {
            this.needNewExtractId = true;
            this.extractTimer = setInterval(() => {
                this.getResult(this.extractTaskId);
            }, 5000);
            if (this.selectedTab === 1) {
                this.getHistory();
            } else {
                this.getCurrentDocHistory();
            }
        },
        getResult(id) {
            getExtractResult(id)
                .then((res) => {
                    const { taskStatus, extractResults } = res.data;
                    if (taskStatus === 'SUCCESS') {
                        this.clearTimer('extractTimer');
                        this.initFragment(extractResults);
                        this.$nextTick(() => {
                            this.extractLoading = false;
                        });
                        if (this.selectedTab === 1) {
                            this.getHistory();
                        } else {
                            this.getCurrentDocHistory();
                        }
                        // this.contractSummary();
                    }
                }).catch(() => {
                    this.clearTimer('extractTimer');
                    this.extractLoading = false;
                });
        },
        initFragment(extractResults) {
            this.result = extractResults;
            this.leftFragment = extractResults.map((item) => item.extractResultContents.map((v) => v.paragraph.contentPositions));
            this.selectTab = '2';
            this.hasResult = true;
        },
        downloadResult() {
            const $el = document.querySelector('#J_download-link') || document.createElement('a');
            $el.setAttribute('id', 'J_download-link');
            $el.setAttribute('style', 'visibility:hidden;height:0;width:0;');
            $el.setAttribute('target', '_self');
            $el.setAttribute('href', `/web/hubble/contract-review/${this.extractTaskId}/reviewed-contract-file-with-comments`);
            $el.setAttribute('download', 'reviewed-file.pdf');
            document.body.appendChild($el);
            $el.click();
        },
        getHistory() {
            getExtractHistory(this.page)
                .then((res) => {
                    const { records, totalRecord } = res.data;
                    this.history = records;
                    this.total = +totalRecord;
                });
        },
        goHistory(obj) {
            if (obj.taskStatus === 'PENDING' || obj.status === 'PENDING') {
                this.$MessageToast.error('正在抽取中，请稍后再试');
                return;
            }
            this.$refs.extract.reset();
            this.$refs.consistency.reset();
            this.$refs.format.reset();
            this.clearTimer('extractTimer');
            this.clearTimer('summaryTimer');
            this.needNewExtractId = true;
            this.extractLoading = true;
            this.leftDocReady = false;
            this.getHistoryExtract(obj);
        },
        getHistoryExtract(obj) {
            let extractId = '';
            if (this.selectedTab === 1) {
                extractId = obj.extractTaskId;
            } else {
                extractId = obj.id;
            }
            this.extractTaskId = extractId;
            getHistoryExtractResult(extractId).then(async(res) => {
                this.fields = [];
                const { fileName, extractResults, taskStatus } = res.data;
                if (taskStatus === 'NOT_START') {
                    this.needNewExtractId = false;
                    return;
                }
                this.fields = [];
                extractResults.forEach(result => {
                    this.fields.push({ extractSubmitFieldName: result.extractFieldName, extractSubmitFieldId: result.submitFieldId });
                });
                this.leftName = fileName;
                this.initFragment(extractResults);
                this.summary = [];
                this.status = '';
                this.$nextTick(() => {
                    this.leftDocReady = true;
                });
                await this.contractSummary();
                if (this.status === 'PENDING') {
                    this.summaryTimer = setInterval(() => {
                        this.contractSummary();
                    }, 5000);
                }
            }).finally(() => {
                this.extractLoading = false;
            });
        },
        initTask() {
            return new Promise((resolve) => {
                initExtractTask()
                    .then((res) => {
                        this.hasResult = false;
                        this.extractTaskId = res.data.extractTaskId;
                        this.$router.replace({
                            query: {
                                ...this.$route.query,
                                extractTaskId: this.extractTaskId,
                            },
                        });
                        resolve();
                    });
            });
        },
        clearTimer(timer) {
            this[timer] && clearInterval(this[timer]);
        },
        getReviewStatus(status) {
            return statusMap[status];
        },
        handleCurrentChange(page) {
            // if (this.selectedTab === 1) {
            this.page = page;
            this.getHistory();
            // } else {
            //     this.docHistoryPage = page;
            //     this.getCurrentDocHistory();
            // }
        },
        handleScan() {
            this.recognizing = true;
            recognizeScan(this.extractTaskId).then((res) => {
                if (res.data.data) {
                    this.leftDocReady = false;
                    this.leftLoading = true;
                    setTimeout(() => {
                        this.leftLoading = false;
                        this.leftDocReady = true;
                    }, 1500);
                } else if (res.data.code === '1') {
                    setTimeout(() => {
                        this.handleScan();
                    }, 3000);
                }
            })
                .finally(() => {
                    this.recognizing = false;
                });
        },
        async contractSummary() {
            await getSummary(this.extractTaskId).then((res) => {
                if (res.data.summaryStatus === 'NOT_START') {
                    if (this.selectTab === '3') {
                        this.$MessageToast.error('请点击生成合同摘要获取内容');
                    }
                    this.status = 'NOT_START';
                    this.$nextTick(() => {
                        this.clearTimer('summaryTimer');
                    });
                    this.checksummarize = false;
                    this.summaryLoading = false;
                } else if (res.data.summaryStatus === 'SUCCESS' && this.extractTaskId === res.data.extractedTaskId) {
                    this.$nextTick(() => {
                        this.clearTimer('summaryTimer');
                    });
                    this.summary = res.data.documentSummaryOfSpecificKeywordVos;
                    this.status = 'SUCCESS';
                    this.checksummarize = false;
                    this.summaryLoading = false;
                } else if (this.extractTaskId === res.data.extractedTaskId) {
                    if (this.status !== 'PENDING') {
                        this.status = 'PENDING';
                    }
                    if (this.checksummarize) {
                        this.checksummarize = false;
                        this.$MessageToast.error('正在生成中，请稍后返回查看');
                    }
                }
            }).catch(() => {
                this.summaryLoading = false;
                this.checksummarize = false;
            });
        },
        changeSummaryFragment(val) {
            this.summaryFragment = val;
        },
        selectShadow(number) {
            this.$refs.leftDocument.showShadow(number);
        },
        changeTab(num) {
            switch (num) {
                case 1:
                    this.selectedTab = 1;
                    this.getHistory();
                    break;
                case 2:
                    this.selectedTab = 2;
                    this.getCurrentDocHistory();
                    break;
                default:
                    break;
            }
        },
        getCurrentDocHistory() {
            const { contractId, documentId } = this.$route.query;
            currentDocumentHistory(contractId, documentId, 'EXTRACTION')
                .then((res) => {
                    const { result } = res.data;
                    this.currentHistory = result;
                    // this.docHistorytotal = +totalRecord;
                });
        },
        initDocumentExtractTask(contractId, documentId) {
            return new Promise(resolve => {
                initHubbleTask(contractId, documentId, 'EXTRACTION').then(({ data: { result } }) => {
                    this.extractTaskId = result.taskId;
                    this.pdfUrl = result.previewUrl;
                    this.leftDocReady = true;
                    this.leftName = result.contractName;
                    resolve();
                });
            });
        },
        changeConsistencyFragment(val) {
            this.consistencyFragment = val;
        },
        handleStartSummary() {
            this.summaryLoading = true;
            startSummary(this.extractTaskId).then((res) => {
                if (res.data.code === '1') {
                    this.$MessageToast.error('正在生成中，请稍后返回查看');
                    this.status = 'PENDING';
                    this.summaryTimer = setInterval(() => {
                        this.contractSummary();
                    }, 5000);
                } else {
                    this.contractSummary();
                }
            }).catch(() => {
                this.summaryLoading = false;
            });
        },
        viewSummary(el) {
            if (this.hasResult && el.name === '3' && !this.status) {
                this.checksummarize = true;
                this.contractSummary();
            } else if (this.hasResult && el.name === '3' && this.status === 'PENDING') {
                this.$MessageToast.error('正在生成中，请稍后返回查看');
            } else if (this.hasResult && el.name === '3' && this.status === 'NOT_START') {
                this.$MessageToast.error('请点击生成合同摘要获取内容');
            }
        },
    },
    created() {
        const { contractId, documentId, extractTaskId } = this.$route.query;
        if (contractId && documentId && !extractTaskId) {
            this.initDocumentExtractTask(contractId, documentId).then(() => {
                this.selectedTab = 2;
                this.getCurrentDocHistory(contractId, documentId);
            });
        } else if (contractId && documentId && extractTaskId) {
            this.selectedTab = 2;
            this.needNewExtractId = true;
            this.getCurrentDocHistory(contractId, documentId);
            this.getHistoryExtract({ extractTaskId });
        } else if (extractTaskId) {
            this.needNewExtractId = true;
            this.getHistoryExtract({ extractTaskId });
        } else if (!contractId) {
            this.initTask();
        }
        this.getHistory();
    },
    beforeDestroy() {
        this.clearTimer('extractTimer');
        this.clearTimer('summaryTimer');
        sessionStorage.removeItem('fromDocument');
        sessionStorage.removeItem('signingPagePath');
    },
};
</script>

<style lang="scss">
@import './index.scss';
</style>
<style lang="scss" scoped>
.right-tab::v-deep .el-tabs__item {
    padding: 0 4px;
}
</style>
