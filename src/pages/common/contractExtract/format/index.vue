<template>
    <div class="consistency-info">
        <div class="keywords">
            <p class="section-title">关键词:</p>
            <el-input v-model="formatValue"
                placeholder="输入你期望检查的格式，例如：3000元，贰拾伍万等等"
                class="input-with-select"
                @keyup.enter.native="handleEnter"
            >
                <el-select v-model="submitFieldId" slot="prepend" placeholder="请选择">
                    <el-option :label="opt.extractSubmitFieldName"
                        v-for="opt in fields"
                        :key="opt.extractSubmitFieldId"
                        :value="opt.extractSubmitFieldId"
                    ></el-option>
                </el-select>
                <el-button :disabled="!formatValue || !submitFieldId"
                    type="primary"
                    slot="append"
                    @click="checkConsistency"
                    :loading="formatLoading"
                >数据格式化</el-button>
            </el-input>
        </div>
        <div class="section-title">
            <p>检查结果:</p>
        </div>
        <div class="consistency-result">
            <div class="result">
                <div class="check-time" v-show="hasFormatResult">
                    <p class="time">格式化时间：{{ time(createTime) }}</p>
                </div>
                <div class="consistency-reason" v-show="hasFormatResult">
                    <p v-for="(item, index) in formatResult" :key="index">
                        {{ item }}
                    </p>
                </div>
                <div class="no-extract-info" v-show="!hasFormatResult">
                    <NoData />
                </div>
            </div>
        </div>
        <div class="compare-loading recognize-loading" v-show="formatLoading" style="top: 70px">
            <div class="compare-loading_content">
                <img src="~img/contractHubble/hubble-loading.gif" alt="">
                <p v-show="formatLoading">格式化数据中</p>
            </div>
        </div>
    </div>
</template>

<script>
import NoData from 'components/noData';
import { startFormat, getFormatResult } from 'src/api/extract.js';
import moment from 'dayjs';

export default {
    components: { NoData },
    props: {
        extractTaskId: {
            type: String,
            default: '',
        },
        hasResult: {
            type: Boolean,
            default: false,
        },
        fields: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            formatValue: '',
            submitFieldId: '',
            formatResult: [],
            createTime: '',
            formatLoading: false,
            hasFormatResult: false,
        };
    },
    computed: {
        time() {
            return (val) => {
                if (val) {
                    return moment(val).format('YYYY-MM-DD HH:mm:ss');
                } else {
                    return '';
                }
            };
        },
    },
    methods: {
        handleEnter() {
            if (!this.formatValue || !this.submitFieldId) {
                return;
            }
            if (this.formatValue && this.submitFieldId) {
                this.checkConsistency();
            }
        },
        async checkConsistency() {
            this.formatLoading = true;
            const data = {
                format: this.formatValue,
                submitFieldId: this.submitFieldId,
            };
            await startFormat(this.extractTaskId, data).then((res) => {
                if (res.data.code === '1') {
                    this.getResult();
                }
            }).catch((err) => {
                this.$message({
                    message: err.message,
                    type: 'error',
                });
                this.formatLoading = false;
            });
        },
        async getResult() {
            await getFormatResult(this.extractTaskId, this.submitFieldId).then(({ data: { createTime, formatResult } }) => {
                this.createTime = createTime;
                this.hasFormatResult = true;
                this.formatResult = JSON.parse(formatResult).map(result => {
                    if (result !== '') {
                        return result;
                    }
                });
            }).finally(() => {
                this.formatLoading = false;
            });
        },
        reset() {
            this.formatValue = '';
            this.submitFieldId = '';
            this.formatResult = [];
            this.createTime = '';
            this.hasFormatResult = false;
        },
    },
};
</script>

<style lang="scss" scoped>
@import '../index.scss';
$--color-primary: #127FD2;
$--color-border: #EEEEEE;
$--color-text-second: #666666;

.consistency-info {
    height: 100%;
    overflow-y: auto;
    padding: 30px;
    padding-top: 15px;
    border-radius: 8px;
    background: #FFFFFF;
    display: flex;
    flex-direction: column;

    .field-title {
        display: inline-block;
        margin-right: 10px;
        margin-bottom: 8px;
        height: 36px;
        line-height: 36px;
        padding: 0 15px;
        background-color: #F4FAFF;
        color: $theme-color;
    }

    .field-info {
        margin-bottom: 8px;
        white-space: break-spaces;
    }

    .no-extract-info {
        transform: translateY(-56px);
        position: relative;
        top: 50%;
    }
}

.section-title {
    margin-bottom: 10px;
}

.keywords {
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    margin-bottom: 10px;
}

.keywords::v-deep .el-select .el-input {
    width: 130px;
}

.keywords::v-deep .el-button--primary {
    color: #fff;
    background-color: #20a0ff;
    border-color: #20a0ff;
}

.keywords::v-deep .el-button--primary.is-disabled {
    color: #bfcbd9;
    background-color: #eef1f6;
    border-color: #d1dbe5;
}

.consistency-guide {
    padding-top: 10px;

    .sentence-number {
        margin-top: 8px;
    }
}

.sentence-number {
    display: inline-block;
    margin-right: 3px;
    min-width: 16px;
    padding: 0 4px;
    height: 16px;
    border-radius: 8px;
    color: $--color-text-second;
    background-color: $--color-border;
    cursor: pointer;
}

.select-mark {
    color: $--color-primary;
    background-color: rgba(77, 179, 255, 0.2);
}

.consistency-result {
    flex-grow: 1;
    background-color: #F8F8F8;
    padding: 12px 19px;

    .result {
        display: flex;
        flex-direction: column;
        height: 100%;
    }

    .check-time {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 8px;
        border-bottom: 1px solid #E1E0E0;
        margin-bottom: 14px;

        .time {
            color: #999999;
            font-size: 12px;
            line-height: 22px;
        }

        .passed {
            color: #0C8AEE;
            font-size: 14px;
            font-weight: normal;
            line-height: 22px;

            i {
                display: inline-block;
                margin-right: 6px;
            }
        }
    }

    .consistency-reason {
        padding-bottom: 13px;
        font-size: 14px;
        line-height: 22px;
        color: #3D3D3D;
        flex-grow: 1;
        overflow-y: auto;
    }
}
</style>
