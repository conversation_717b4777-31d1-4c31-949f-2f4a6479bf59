<template>
    <div class="keyword-introduce">
        <div class="extract-keyword">
            <div class="extract-keyword-title">
                {{ keyword }}
                <span @click="addKeyword">
                    <i class="el-icon-ssq-jia"></i>
                    {{ $t('contractCompare.addKeyword', { keyword }) }}
                </span>
            </div>
            <div class="extract-keyword-content" v-loading="loading">
                <el-checkbox-group v-model="checkList" @change="changeField">
                    <EditInput
                        v-for="(value, index) in keywords"
                        :key="value.extractExpectedFieldId"
                        :label="value.extractExpectedFieldName"
                        :value="value.extractExpectedFieldId"
                        :field="value"
                        :activeField="activeField"
                        :editField="editField"
                        @changeValue="(value) => changeValue(value, index)"
                        @setEditField="setEditField"
                        @mouseenter.native="mouseenter(value)"
                        @deleteKeyword="deleteKeyword"
                    />
                </el-checkbox-group>
            </div>
        </div>
        <div class="extract-introduce">
            <div class="extract-introduce-title">
                {{ $t('contractCompare.introduce', { keyword }) }}
            </div>
            <div class="extract-introduce-content">
                <el-input
                    class="introduce-input"
                    :disabled="!editField.extractExpectedFieldId"
                    resize="none"
                    type="textarea"
                    v-model="introduce"
                    placeholder="选填"
                />
            </div>
        </div>
    </div>
</template>

<script>
import EditInput from './editInput.vue';
import { getExtractFileConfig, addExtractField, editExtractField, deleteKeyword } from 'src/api/extract.js';

export default {
    components: { EditInput },
    props: {
        keyword: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            keywords: [],
            checkList: [],
            preCheckList: [],
            introduce: '',
            adding: false,
            loading: false,
            activeField: {},
            editField: {},
        };
    },
    methods: {
        changeValue(value, index) {
            this.loading = true;
            const field = this.keywords[index];
            const { extractExpectedFieldId } = field;
            editExtractField({
                extractExpectedFieldId,
                extractExpectedFieldDefinition: this.introduce,
                extractExpectedFieldName: value,
            }).then(() => {
                const obj = { ...field, extractExpectedFieldName: value, extractExpectedFieldDefinition: this.introduce };
                this.keywords.splice(index, 1, obj);
                this.$emit('changeValue', obj);
            })
                .finally(() => {
                    this.editField = {};
                    this.loading = false;
                });
        },
        initFileConfig(focus) {
            getExtractFileConfig().then((res) => {
                this.keywords = res.data;
                if (focus) {
                    this.setEditField(this.keywords[this.keywords.length - 1]);
                    this.activeField = this.keywords[this.keywords.length - 1];
                    this.introduce = this.activeField.extractExpectedFieldDefinition;
                    this.$nextTick(() => {
                        const input = document.querySelector('.extract-keyword-content .el-checkbox-group .edit-input input');
                        input.scrollIntoView();
                        input.focus();
                    });
                }
            });
        },
        addKeyword() {
            if (this.adding) {
                return;
            }
            this.adding = true;
            addExtractField({
                extractExpectedFieldName: '',
                extractExpectedFieldDefinition: '',
            }).then(() => {
                this.initFileConfig(true);
            })
                .finally(() => {
                    this.adding = false;
                });
        },
        changeField(value) {
            const fields = value.map((item) => this.keywords.find((v) => v.extractExpectedFieldId === item));
            if (fields.find((item) => item.extractExpectedFieldName === '')) {
                this.checkList = this.preCheckList;
                return;
            }
            this.preCheckList = this.checkList;
            this.$emit('changeField', fields);
        },
        setEditField(value) {
            this.editField = value;
        },
        cancelField(item) {
            const index = this.checkList.findIndex((v) => v === item.extractExpectedFieldId);
            this.checkList.splice(index, 1);
        },
        deleteKeyword(item) {
            this.loading = true;
            deleteKeyword(item.extractExpectedFieldId).then((res) => {
                this.keywords = res.data;
                this.$emit('deleteKeyword', item);
            }).finally(() => {
                this.loading = false;
            });
        },
        mouseenter(value) {
            if (this.editField.extractExpectedFieldId) {
                return;
            }
            this.activeField = value;
            this.introduce = this.activeField.extractExpectedFieldDefinition;
        },
        reset() {
            this.checkList = [];
        },
    },
    created() {
        this.initFileConfig();
    },
};
</script>

<style lang="scss">
.keyword-introduce {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    justify-content: space-between;
    .extract-keyword {
        width: calc(45% - 5px);
    }
    .extract-introduce {
        width: calc(55% - 5px);
    }
}
.extract-keyword, .extract-introduce {
    height: 100%;
    border-radius: 4px;
    border: 1px solid #EEEEEE;
    &-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 44px;
        padding: 0 10px;
        font-size: 14px;
        color: #3D3D3D;
        background-color: #F8F8F8;
        span {
            cursor: pointer;
            color: $theme-color;
        }
    }
    &-content {
        overflow-y: auto;
        position: relative;
        height: calc(100% - 44px);
    }
    .introduce-input {
        height: calc(100% - 20px);
        outline: none;
        padding: 15px;
        line-height: 20px;
        color: #333333;
        textarea {
            height: 100%;
        }
        &.el-textarea.is-disabled .el-textarea__inner {
            color: #333333;
        }
    }
}
</style>
