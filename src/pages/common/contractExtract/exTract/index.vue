<template>
    <div class="contract-extract" v-loading="loading">
        <div class="contract-extract-title">{{ title }}</div>
        <div class="contract-extract-info">
            <div class="empty-info" v-show="!fields.length">{{ info }}</div>
            <span class="extract-field" v-for="item in fields" :key="item.extractSubmitFieldId">
                {{ item.extractSubmitFieldName }}
                <i @click="cancelField(item)" class="el-icon-ssq-hetongxiangqing-juqian1"></i>
            </span>
        </div>
        <div class="contract-extract-keyword">
            <Keyword ref="keyword" @changeField="changeField" @changeValue="changeValue" @deleteKeyword="deleteKeyword" :keyword="keyword" />
        </div>
    </div>
</template>

<script>
import Keyword from './keyword.vue';
export default {
    components: { Keyword },
    props: {
        extractTaskId: {
            type: String,
            default: '',
        },
        title: {
            type: String,
            default: '',
        },
        info: {
            type: String,
            default: '',
        },
        keyword: {
            type: String,
            default: '关键词',
        },
        addInfo: {
            type: Function,
            default: () => {},
        },
        deleteInfo: {
            type: Function,
            default: () => {},
        },
    },
    data() {
        return {
            fields: [],
            loading: false,
        };
    },
    watch: {
        fields: {
            deep: true,
            handler() {
                this.$emit('changeFields', this.fields);
            },
        },
    },
    methods: {
        changeField(value) {
            let obj = {};
            let result = null;
            this.loading = true;
            if (this.fields.length > value.length) {
                obj = this.getMissData(this.fields, value);
                result = this.deleteField(obj.extractSubmitFieldId);
            } else {
                obj = this.getMissData(value, this.fields);
                result = this.submitField({
                    extractSubmitFieldName: obj.extractExpectedFieldName,
                    extractSubmitFieldDefinition: obj.extractExpectedFieldDefinition,
                    extractContentFieldType: obj.extractContentFieldType,
                    originExtractExpectedFieldId: obj.extractExpectedFieldId,
                });
            }
            result.then((res) => {
                this.fields = res.data.map((item) => ({
                    ...item,
                    extractExpectedFieldId: item.originExtractExpectedFieldId,
                }));
            })
                .finally(() => {
                    this.loading = false;
                });
        },
        cancelField(item) {
            this.loading = true;
            this.deleteField(item.extractSubmitFieldId).then((res) => {
                this.$refs.keyword.cancelField(item);
                this.fields = res.data.map((item) => ({
                    ...item,
                    extractExpectedFieldId: item.originExtractExpectedFieldId,
                }));
            }).finally(() => {
                this.loading = false;
            });
        },
        changeValue(item) {
            const index = this.fields.findIndex((v) => v.originExtractExpectedFieldId === item.extractExpectedFieldId);
            if (index !== -1) {
                this.fields.splice(index, 1, { ...this.fields[index], extractSubmitFieldName: item.extractExpectedFieldName });
            }
        },
        deleteKeyword(item) {
            const field = this.fields.find((v) => v.originExtractExpectedFieldId === item.extractExpectedFieldId);
            if (field) {
                this.cancelField(field);
            }
        },
        submitField(data) {
            return this.addInfo(this.extractTaskId, data);
        },
        deleteField(id) {
            return this.deleteInfo(this.extractTaskId, id);
        },
        getMissData(pre, next) {
            const ids = next.map((item) => item.extractExpectedFieldId);
            return pre.find((item) => !ids.includes(item.extractExpectedFieldId));
        },
        reset() {
            this.fields = [];
            this.$refs.keyword.reset();
        },
    },
};
</script>

<style lang="scss">
.contract-extract {
    height: 100%;
    padding: 30px;
    padding-top: 15px;
    border-radius: 8px;
    background: #FFFFFF;
    &-title {
        line-height: 22px;
        font-size: 14px;
        color: #3D3D3D;
    }
    &-info {
        height: calc(40% - 20px);
        .extract-field {
            position: relative;
            display: inline-block;
            margin-right: 10px;
            margin-bottom: 8px;
            white-space: nowrap;
            height: 36px;
            line-height: 36px;
            padding: 0 15px;
            background-color: #F4FAFF;
            color: $theme-color;
            i {
                font-size: 12px;
                position: absolute;
                right: -4px;
                top: -4px;
            }
        }
    }
    .empty-info {
        position: relative;
        top: 50%;
        transform: translateY(-50%);
        height: 120px;
        color: #999999;
        text-align: center;
        background: url(~img/contractExtract/guide-empty.png) no-repeat;
        background-size: 100px;
        background-position: center bottom;
    }
    &-keyword {
        height: calc(60% - 5px);
    }
}
</style>
