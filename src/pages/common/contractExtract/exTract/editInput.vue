<template>
    <el-checkbox class="checkbox-edit" :class="{ active: activeField.extractExpectedFieldId === value }" :label="value">
        <div v-if="showEdit" class="input-wrap">
            <el-input class="edit-input" ref="input" v-model="tempValue"></el-input>
            <span class="save" @click.stop.prevent="save"><i class="el-icon-ssq-qianyuewancheng"></i></span>
        </div>
        <span class="text" v-else>{{ label }}</span>
        <i v-show="!showEdit" class="el-icon-ssq-xiugaihetongzhuangtai" @click.stop.prevent="edit"></i>
        <i v-show="!showEdit" class="el-icon-ssq-hetongxiangqing-juqian1" @click.stop.prevent="deleteKeyword"></i>
    </el-checkbox>
</template>

<script>
export default {
    props: {
        label: {
            type: String,
            default: '',
        },
        value: {
            type: String,
            default: '',
        },
        activeField: {
            type: Object,
            default: () => {},
        },
        editField: {
            type: Object,
            default: () => {},
        },
        field: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            tempValue: '',
            // showEdit: false,
        };
    },
    computed: {
        showEdit() {
            return this.editField?.extractExpectedFieldId === this.value;
        },
    },
    watch: {
        label() {
            this.tempValue = this.label;
        },
    },
    methods: {
        save() {
            this.$emit('changeValue', this.tempValue);
        },
        edit() {
            this.$emit('setEditField', this.field);
            this.$nextTick(() => {
                this.$refs.input.$el.querySelector('input').focus();
            });
        },
        deleteKeyword() {
            this.$confirm(this.$t('contractCompare.deleteKeywordConfirm'), this.$t('contractCompare.tip'), {
                confirmButtonText: this.$t('contractCompare.confirm'),
                showCancelButton: true,
            }).then(() => {
                this.$emit('deleteKeyword', this.field);
            });
        },
    },
    created() {
        this.tempValue = this.label;
    },
};
</script>

<style lang="scss">
.el-checkbox.checkbox-edit {
    width: 100%;
    height: 40px;
    line-height: 40px;
    padding: 0;
    padding-left: 10px;
    margin-left: 0;
    display: flex;
    align-items: center;
    cursor: pointer;
    &.active {
        background-color: #F4FAFF;
    }
    .text {
        width: calc(100% - 30px);
        display: inline-block;
        margin-left: 10px;
        margin-right: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    i {
        color: $theme-color;
        &:first-of-type {
            margin-right: 8px;
        }
    }
    .el-checkbox__label {
        display: flex;
        align-items: center;
        width: calc(100% - 21px);
    }
    .el-input {
        width: calc(100% - 20px);
        height: 28px;
        input {
            height: 28px;
            border: none;
            box-shadow: none;
            vertical-align: super;
        }
    }
    .input-wrap {
        display: inline-block;
        position: relative;
        height: 32px;
        width: 100%;
        border-radius: 1px;
        border: 1px solid #ccc;
        background-color: #FFFFFF;
        .save {
            width: 14px;
            height: 14px;
            position: absolute;
            top: 8px;
            font-size: 14px;
            border-radius: 2px;
            text-align: center;
            background-color: #EEEEEE;
            i {
                vertical-align: top;
                font-size: 12px;
                color: #999999;
            }
        }
    }
}
</style>
