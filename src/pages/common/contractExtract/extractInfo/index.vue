<template>
    <div class="extract-info">
        <div v-for="(item) in result" :key="item.extractFieldName">
            <div>
                <span class="field-title">{{ item.extractFieldName }}</span>
            </div>
            <div class="field-info" v-for="(v, num) in item.extractResultContents" :key="num">
                <div>{{ v.paragraph.content }}</div>
            </div>
            <div v-show="!item.extractResultContents.length">{{ $t('contractCompare.noData') }}</div>
        </div>
        <div class="no-extract-info" v-show="!result.length">
            <NoData />
        </div>
    </div>
</template>

<script>
import NoData from 'components/noData';

export default {
    components: { NoData },
    props: {
        result: {
            type: Array,
            default: () => [],
        },
    },
};
</script>

<style lang="scss">
.extract-info {
    height: 100%;
    overflow-y: auto;
    padding: 30px;
    padding-top: 15px;
    border-radius: 8px;
    background: #FFFFFF;
    .field-title {
        display: inline-block;
        margin-right: 10px;
        margin-bottom: 8px;
        height: 36px;
        line-height: 36px;
        padding: 0 15px;
        background-color: #F4FAFF;
        color: $theme-color;
    }
    .field-info {
        margin-bottom: 8px;
        white-space: break-spaces;
    }
    .no-extract-info {
        transform: translateY(-56px);
        position: relative;
        top: 50%;
    }
}
</style>
