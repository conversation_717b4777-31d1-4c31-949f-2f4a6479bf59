<template>
    <div class="icon-play">
        <i class="iconfont el-icon-ssq-icon_play" @click="handleRoute"></i>
        <!--<el-tag v-if="show" :closable="true" @close.prevent="handleViewedTag"><span class="text">这里查看功能操作视频</span></el-tag>-->
        <div class="new-from-experience-dialog" v-if="showDialog">
            <i class="el-icon-ssq-tongzhi1"></i>
            <span>这里可以查看操作视频指导！</span>
            <i class="el-icon-ssq--bs-guanbi" @click="closeDialog"></i>
        </div>
    </div>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
    name: 'IconPlay',
    props: {
        color: {
            type: String,
            default: '#fff',
        },
    },
    data() {
        return {
            // show: false,
            showDialog: false,
        };
    },
    computed: {
        ...mapGetters(['getUserType', 'getUserId']),
    },
    methods: {
        getUserGuide() {
            Vue.$http.get('/users/configs/USER_GUIDE').then(res => {
                this.showDialog = !res.data.value || res.data.value.indexOf('playDialog') === -1;
                this.$store.state.newFromExperience = this.showDialog;
            });
        },
        handleRoute() {
            this.$router.push('/operation-video');
        },
        closeDialog() {
            this.showDialog = false;
            this.$http.post('/users/configs/USER_GUIDE', {
                value: 'playDialog',
                name: 'USER_GUIDE',
            });
        },
    },
    created() {
        // 用户来源 是否是新官网注册流程的新用户
        this.$http.get('/users/configs/REGISTER_FROM_EXPERIENCE').then(res => {
            +res.data.value === 1 && this.getUserGuide();
        });
    },
};
</script>
<style lang="scss">
    .icon-play {
        .el-tag {
            width: 201px;
            height: 60px;
            position: absolute;
            top: 20px;
            left: -122px;
            color: #127fd2;
            background: transparent url("./<EMAIL>") no-repeat;
            /*background-size: 100% 150%;*/
            text-align: center;
            .text {
                position: relative;
                top: 29px;
                font-size: 14px;
            }
            .el-tag__close {
                position: relative;
                font-size: 12px;
                top: 27px;
                left: 10px;
                color: #C9C9C9;
            }
        }
    }
</style>
<style scoped lang="scss">
  .icon {
    width: 1em; height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
  }
  .icon-play {
      position: relative;
    display: inline-block;
    .el-icon-ssq-icon_play {
      font-size: 16px;
      cursor: pointer;
        color: #fff;
      &:hover {
        color: #2298f1;
      }
    }
    .new-from-experience-dialog{
        width: 250px;
        line-height: 44px;
        background: #fff;
        font-size: 12px;
        color: #333;
        border-radius: 4px;
        position: absolute;
        right: -20px;
        top: 33px;
        padding: 0 20px;
        i.el-icon-ssq-tongzhi1{
            color: #127FD2;
            font-size: 16px;
            position: relative;
            top: 2px;
            margin-right: 12px;
        }
        i.el-icon-ssq--bs-guanbi{
            position: absolute;
            color: #999;
            top: 10px;
            right: 12px;
            cursor: pointer;
        }
        &:before{
            content: '';
            display: block;
            width: 0;
            height: 0;
            border: 9px solid transparent;
            position: absolute;
            top: -18px;
            right: 20px;
            border-bottom-color: #fff;
        }
    }
  }
</style>
