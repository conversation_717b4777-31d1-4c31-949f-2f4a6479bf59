<template>
    <el-dialog
        :visible.sync="dialogVisible"
        :modal="false"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :show-close="false"
        class="hubble-page__share"
    >
        {{ shareData.shareUserName }} 邀请您参与Hubble智能文档 <br>
        文档名称：{{ shareData.shareFileName }} <br><br>
        <p>填写分享码查看文档：</p><br>
        <el-input v-model="password" placeholder="请输入6位数字分享码"></el-input>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="handleSubmit">确 认</el-button>
        </div>
    </el-dialog>
</template>

<script>
export default {
    data() {
        return {
            dialogVisible: true,
            shareData: {},
            password: '',
            shareToken: this.$route.params.token,
        };
    },
    methods: {
        handleSubmit() {
            this.$http.post('/auth-center/user/hubble/topic/login', {
                token: this.shareToken,
                password: this.password,
            }).then((res) => {
                const { access_token, refresh_token } = res.data;
                this.$token.save(access_token, refresh_token);
                this.$router.push(`/hubble/shareChat/${this.shareData.topicId}`);
            });
        },
    },
    created() {
        this.$http(`/web/hubble/ignore/topic/share-info/${this.shareToken}`).then((res) => {
            this.shareData = res.data;
        });
    },
};
</script>

<style lang="scss">
.hubble-page__share{
    .el-dialog{
        width: 400px;
        top: 50% !important;
        transform: translate(-50%, -50%) !important;
    }
}
</style>
