
.hubble-chat{
    *{
        box-sizing: border-box;
    }
    height: calc(100vh - 60px - 35px);
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 9;
    background: #fff;
    &__loading{
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        color: #ccc;
    }
    &__header{
        width: 100%;
        font-size: 14px;
        height: 55px;
        padding: 0 20px;
        line-height: 55px;
        background: #fff;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        img{
            width: 20px;
            height: 20px;
            position: relative;
            top: 5px;
        }
        .operate{
            i{
                cursor: pointer;
                padding: 8px;
                display: inline-block;
                &:hover{
                    background: #f6f6f6;
                }
                &+i{
                    margin-left: 10px;
                }
            }
        }
    }
    &__body{
        overflow: auto;
        padding-top: 15px;
        font-size: 12px;
        display: flex;
        flex-direction: column-reverse;
        flex-grow: 1;
        .message{
            margin: 0 20px;
            padding: 10px 0 20px;
            &+.message{
                border-top: 1px solid #eee;
            }
        }
        .question, .answer{
            display: flex;
            flex-direction:row;
            margin: 20px 0 10px;
            .message-content{
                min-width: 15px;
                max-width: calc(100% - 45px);
                padding: 10px;
                white-space: pre-line;
                word-break: break-all;
                line-height: 22px;
                border-radius: 4px;
                background: #D6E9FF;
            }
            .avatar{
                width: 30px;
                height: 30px;
                margin-right: 15px;
            }
            &.question{
                .message-content{
                    background: #F0F1F3;
                }
            }
            .cursor{
                display: inline-block;
                width: 1px;
                height: 16px;
                position: relative;
                top: 2px;
                background: #fff;
                animation: blink 1s infinite;
            }
            .explain{
                color: #bbb;
                font-size: 12px;
            }
        }
        .time, .related{
            color: #999;
            margin-left: 45px;
            overflow: hidden;
        }
        .related{
            span {
                color: $theme-color;
                cursor: pointer;
                padding: 0 5px;
                &+span{
                    border-left: 1px solid #ccc;
                }
            }
            .operate-icon{
                font-size: 16px;
                cursor: pointer;
                float: right;
                margin-left: 5px;
            }
        }
        &-empty{
            width: 100%;
            height: 200px;
            position: absolute;
            top: 50%;
            margin-top: -100px;
            text-align: center;
            color: #999;
            font-size: 12px;
            img{
                width: 160px;
                display: block;
                margin: 0 auto 12px;
            }
        }
        .continuous-chat{
            position: absolute;
            width: 100%;
            height: calc(100vh - 115px - 72px);
            top: 55px;
            right: 0;
            overflow: hidden;
            background: #fff;
            display: flex;
            flex-direction: column-reverse;
            justify-content: space-between;
            .hubble-chat__body-box{
                max-height: calc(100% - 38px);
                overflow: auto;
            }
            .continuous-exit{
                background: #0C8AEE 100%;
                color: #fff;
                line-height: 38px;
                padding: 0 20px;
                span.exit{
                    float: right;
                    cursor: pointer;
                }
            }
        }
    }
    &__footer{
        background: #F8F8F8;
        padding: 20px;
        position: relative;
        z-index: 9;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: stretch;
        &-operate{
            display: flex;
            position: absolute;
            right: 20px;
            bottom: 20px;
            i.operate-icon{
                font-size: 20px;
                flex: 1;
                border: 1px solid rgba(221,221,221,1);
                border-radius: 2px;
                color: $theme-color;
                display: inline-block;
                width: 34px;
                height: 34px;
                margin-left: 5px;
                text-align: center;
                line-height: 32px;
                background: #fff;
                cursor: pointer;
                &:hover{
                    background: #f8f8f8;
                }
            }
            .el-button{
                margin-left: 5px;
                padding: 0;
                height: 34px;
                line-height: 34px;
                width: 50px;
                text-align: center;
                background: $theme-color;
                border-color: $theme-color;
                &:hover{
                    background: #20a0ff;
                    border-color: #20a0ff;
                }
            }
        }
    }
    &__input{
        width: calc(100% - 95px);
        position: relative;
        background: #fff;
        &-plugin{
            position: absolute;
            font-size: 12px;
            line-height: 30px;
            color: #0C8AEE;
            padding: 0 5px;
            z-index: 2;
            padding-left: 10px;
        }
        &.line-feed{
            .el-textarea__inner{
                text-indent: 60px;
            }
        }
        .el-textarea__inner{
            min-height: 32px !important;
        }
    }
    &__prompt{
        width: 100%;
        background: #fff;
        position: absolute;
        z-index: 9;
        bottom: 60px;
        box-shadow: 0px 4px 26px 0px rgba(0,0,0,0.08);
        border-radius: 2px;
        padding: 10px 0;
        &-plugin{
            height: 60px;
            padding: 10px 0 10px 18px;
            font-size: 14px;
            display: flex;
            cursor: pointer;
            i{
                font-size: 16px;
                height: 24px;
                padding: 3px;
                background: #FFFFFF;
                border: 1px solid rgba(238,238,238,1);
                box-shadow: 0px 1px 4px 0px rgba(0,0,0,0.06);
                border-radius: 4px;
                margin: 6px 15px 0 0;
            }
            &:hover, &.active{
                color: unset;
                background: #f8f8f8;
            }
            span{
                font-size: 12px;
                color: #999;
            }
        }
    }
    &__dialog{
        width: 100%;
        font-size: 12px;
        position: absolute;
        bottom: 72px;
        border-bottom: 1px solid #eee;
    }
    &__quote{
        font-size: 12px;
        padding: 20px;
        background: #F8F8F8;
        box-shadow: 0px 0px 8px 0px rgba(0,0,0,0.1);
        &-header{
            position: relative;
            i{
                color: #ccc;
                position: absolute;
                cursor: pointer;
                right: 0;
                top: 0;
            }
        }
        &-body{
            margin-top: 5px;
        }
    }
    &__suggestion{
        background: #fff;
        font-size: 12px;
        position: relative;
        z-index: 9;
        &.without-quote{
            box-shadow: 0px 0px 8px 0px rgba(0,0,0,0.1);
        }
        &-header{
            height: 50px;
            line-height: 50px;
            font-size: 14px;
            border-bottom: 1px solid #eee;
            position: relative;
            padding-left: 20px;
            i.el-icon-ssq-guanbi{
                font-size: 12px;
                color: #ccc;
                position: absolute;
                top: 20px;
                right: 20px;
                cursor: pointer;
            }
        }
        &-list{
            padding: 14px 20px 20px 30px;
            line-height: 22px;
            li{
                list-style: auto;
                cursor: pointer;
                &:hover span{
                    background: #f8f8f8;
                }
                i{
                    color: $theme-color;
                }
                &+li{
                    margin-top: 10px;
                }
                &.empty{
                    list-style-type: none;
                    text-align: center;
                }
            }
        }
    }
    &__move{
        position: absolute;
        top: 50%;
        margin-top: -30px;
        left: -18px;
        cursor: col-resize;
        z-index: 999;
        background: #fff;
        box-shadow: -6px 0px 8px 0px rgba(0, 0, 0, 0.1);
        padding: 22px 1px;
        border-radius: 5px 0 0 5px;
    }

    .chat-quote{
        line-height: 22px;
        position: relative;
        padding-left: 20px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: pointer;
        &:hover{
            color: #666;
            background: #E2E4E8;
        }
        &::before{
            content: '“';
            position: absolute;
            top: 8px;
            left: 0px;
            color: #999;
            font-size: 40px;
        }
        &::after{
            content: '';
            position: absolute;
            top: 20px;
            left: 2px;
            width: 3px;
            height: calc(100% - 25px);
            background: #999;
        }
    }
    .slide-enter-active, .slide-leave-active, .fade-enter-active, .fade-leave-active {
        transition: all 0.2s ease;
    }
    .slide-enter, .slide-leave-to {
        transform: translateY(100%);
    }
    .fade-enter, .fade-leave-to {
        transform: translateX(100%);
    }
}
.download-popover{
    min-width: 120px !important;
    line-height: 35px;
    padding: 0;
    li{
        cursor: pointer;
        padding: 0 10px;
        &:hover{
            background: #f8f8f8;
        }
    }
}