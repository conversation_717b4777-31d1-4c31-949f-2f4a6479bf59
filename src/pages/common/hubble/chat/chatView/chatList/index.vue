<template>
    <ul class="hubble-chat__body-box" @scroll="$emit('loadMore', $event)">
        <li class="hubble-chat__body-empty" v-if="!messages.length">
            <img src="~src/pages/common/hubble/img/empty.png" alt="">
            <p>在下方输入或在合同上选取后点击“Hubble”，进行提问</p>
        </li>
        <li v-for="(topic, index) in currentMessageList" :key="index" class="message">
            <div class="question" v-if="topic.question">
                <img src="~src/pages/common/hubble/img/avatar.png" class="avatar" alt="">
                <div class="message-content">
                    <div class="quote chat-quote" v-if="topic.quote" @click="$emit('selectQuote', topic.questionDocumentQuote)">
                        {{ topic.quote }}
                    </div>
                    问题：{{ topic.question }}
                </div>
            </div>
            <span class="time">{{ topic.chatTime }}</span>
            <div class="answer" v-if="topic.answer">
                <img src="~src/pages/common/hubble/img/AIAvatar.png" class="avatar" alt="">
                <div class="message-content">
                    {{ topic.answer }}
                    <span class="cursor" v-if="showCursor(index)"></span><br>
                    <span class="explain">~ 以上内容为AI生成，不代表上上签立场，仅供您参考，请勿删除或修改本标记</span>
                </div>
            </div>
            <div class="related" v-show="topic.chatId">
                <template v-if="showAnswerDocumentQuotes(topic)">
                    相关内容：
                    <span v-for="(quote, i) in topic.answerDocumentQuotes" @click="$emit('selectQuote', quote)" :key="i">{{ quote.pageNumber }}</span>
                </template>
                <el-tooltip :open-delay="500" effect="dark" content="删除对话" placement="top">
                    <i class="operate-icon el-icon-ssq-Hubbleshanchu" @click="handleDelete(index)"></i>
                </el-tooltip>
                <el-tooltip :open-delay="500" effect="dark" content="开启连续对话" placement="top">
                    <i class="operate-icon el-icon-ssq-Hubblelianxuduihua" v-if="!isContinuousChat" @click="$emit('showContinuousChat', topic.chatId)"></i>
                </el-tooltip>
            </div>
        </li>
    </ul>
</template>

<script>
export default {
    props: {
        messages: {
            type: Array,
            default: () => [],
        },
        isContinuousChat: {
            type: Boolean,
            default: false,
        },
    },
    computed: {
        currentMessageList() {
            return [...this.messages].reverse();
        },
    },
    watch: {
        messages: {
            handler() {
                this.$nextTick(() => {
                    this.scrollList();
                });
            },
            deep: true,
            immediate: true,
        },
    },
    methods: {
        showAnswerDocumentQuotes(topic) {
            return !!topic.answerDocumentQuotes?.length;
        },
        showCursor(index) {
            return index === 0 && this.typing;
        },
        scrollList() {
            const element = document.querySelector('.continuous-chat')?.querySelector('.hubble-chat__body-box');
            element && (element.scrollTop = element.scrollHeight);
        },
        handleDelete(index) {
            this.$confirm('确认删除该对话吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
            }).then(() => {
                this.$http.delete(`/web/hubble/topic/${this.$route.params.topicId}/${this.currentMessageList[index].chatId}`).then(() => {
                    this.$MessageToast('删除成功');
                    this.messages.splice(this.messages.length - index - 1, 1);
                });
            }).catch(() => {});
        },
    },
};
</script>

<style lang="scss">
</style>
