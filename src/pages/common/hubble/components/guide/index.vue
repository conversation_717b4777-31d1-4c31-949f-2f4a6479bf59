<template>
    <div class="hubble-page__content-guide">
        <div class="hubble-page__content-guide__entry" @click="handleValue">
            <i class="el-icon-ssq-wenhaoxiao"></i>
            功能指引
        </div>
        <div class="hubble-page__content-guide__modal" v-show="value"></div>
        <div class="hubble-page__content-guide__dialog" v-show="value">
            <i class="el-icon-ssq-delete close" @click="handleValue"></i>
            <b>Hubble，一种全新的工作体验。</b>
            <p>你好，我是哈勃，你的签约智能助手<br>一款由上上签发布的具有大模型能力的人工智能应用，提供更高效、便捷的签约互动能力。<br><br>我们准备了必要的使用介绍来帮助快速上手，<br>可以通过下方按钮轻松探索特色功能。<br></p>
            <ul class="hubble-page__content-guide__btn-list">
                <li :class="`hubble-page__content-guide__btn-item ${currentBtn === btn.type ? 'active' : ''} guide-start-${btn.type}`" v-for="btn in btnList" :key="btn.type" @click="handleGuide(btn.type)">
                    <i :class="`el-icon-ssq-${btn.icon}`"></i><br>
                    <span>{{ btn.text }}</span>
                </li>
            </ul>
            <span class="confirm-btn" @click="handleValue">即刻开启 Hubble 之旅</span>
        </div>
        <svg class="hubble-page__content-guide__svg" v-if="svgPath">
            <path id="myPath" :d="svgPath" fill="none" stroke="#127fd2" stroke-width="2"></path>
        </svg>
        <div class="hubble-page__content-guide__end-point" v-if="svgPath" :style="endPointStyle"></div>
    </div>
</template>

<script>
import { mapMutations } from 'vuex';

export default {
    props: {
        value: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            btnList: [
                {
                    icon: 'shangchuan1',
                    text: '新建文档对话',
                    type: 'upload',
                },
                {
                    icon: 'wenzi',
                    text: '输入对话内容',
                    type: 'text',
                },
                {
                    icon: 'a-151-app-jiaocaiyinyong',
                    text: '划词引用',
                    type: 'quote',
                },
                {
                    icon: 'qieyingwen',
                    text: '输出内容语言',
                    type: 'lang',
                },
                {
                    icon: 'Hubbletishi',
                    text: '建议问题',
                    type: 'suggestion',
                },
            ],
            currentBtn: '',
            svgPath: '',
            startPoint: {},
            endPoint: {},
        };
    },
    computed: {
        endPointStyle() {
            const left = this.endPoint.x - 5;
            let top = this.endPoint.y - 10;
            if (this.currentBtn === 'upload') {
                top = this.endPoint.y;
            }
            return {
                left: `${left}px`,
                top: `${top}px`,
            };
        },
    },
    methods: {
        ...mapMutations('hubble', ['toggleSlider']),
        handleValue() {
            if (!this.value) {
                this.toggleSlider(true);
                setTimeout(() => {
                    this.handleGuide('upload');
                }, 5);
            } else {
                this.handleGuide('');
            }
            this.$emit('input', !this.value);
        },
        handleGuide(type) {
            this.currentBtn = type;
            if (!type) {
                this.svgPath = '';
                return;
            }
            this.handleStartPoint();
            this.handelEndPoint();
            this.handelPath();
        },
        handleStartPoint() {
            const startPoint = document.querySelector(`.guide-start-${this.currentBtn}`).getBoundingClientRect();
            let x = startPoint.x + startPoint.width / 2;
            let y = startPoint.y + startPoint.height;
            if (this.currentBtn === 'upload') {
                x = startPoint.x;
                y = startPoint.y + startPoint.height / 2;
            } else if (this.currentBtn === 'suggestion') {
                x = startPoint.x + startPoint.width;
                y = startPoint.y + startPoint.height / 2;
            } else if (this.currentBtn === 'lang') {
                y = startPoint.y;
            }
            this.startPoint = { x, y };
        },
        handelEndPoint() {
            const endPoint = document.querySelector(`#guide-${this.currentBtn}`).getBoundingClientRect();
            const x = endPoint.x + endPoint.width / 2;
            let y = endPoint.y;
            if (this.currentBtn === 'upload') {
                y = endPoint.y + endPoint.height;
            }
            this.endPoint = { x, y };
        },
        handelPath() {
            const { x: x1, y: y1 } = this.startPoint;
            const { x: x2, y: y2 } = this.endPoint;
            let svgPath = '';
            const middleY = (y1 + y2) / 2;
            switch (this.currentBtn) {
                case 'upload':
                    svgPath = `M ${x1} ${y1} L ${x2 + 30} ${y1} Q ${x2} ${y1} ${x2} ${y1 - 30} L ${x2} ${y2}`;
                    break;
                case 'text':
                case 'quote':
                    svgPath = `M ${x1} ${y1} L ${x1} ${middleY - 30} Q ${x1} ${middleY} ${x1 + 30} ${middleY} L ${x2 - 30} ${middleY} Q ${x2} ${middleY} ${x2} ${middleY + 30} L ${x2} ${y2}`;
                    break;
                case 'lang':
                    svgPath = `M ${x1} ${y1} L ${x1} ${middleY + 30} Q ${x1} ${middleY} ${x1 + 30} ${middleY} L ${x2 - 30} ${middleY} Q ${x2} ${middleY} ${x2} ${middleY - 30} L ${x2} ${y2}`;
                    break;
                case 'suggestion':
                    svgPath = `M ${x1} ${y1} L ${x2 - 30} ${y1} Q ${x2} ${y1} ${x2} ${y1 + 30} L ${x2} ${y2}`;
                    break;
            }
            this.svgPath = svgPath;
        },
        handleResize() {
            this.handleGuide(this.currentBtn);
        },
    },
    mounted() {
        window.addEventListener('resize', this.handleResize);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize);
    },
// hubble-page__content-guide
};
</script>

<style lang="scss">
.hubble-page__content-guide{
    position: relative;
    z-index: 99999;
    &__modal{
        position: fixed;
        width: 100vw;
        height: 100vh;
        background: #000;
        opacity: .2;
        left: 0;
        top: 0;
    }
    &__entry{
        position: fixed;
        z-index: 10;
        left: 20px;
        bottom: 55px;
        border-radius: 13px;
        padding: 4px 10px;
        background: $theme-color;
        font-size: 12px;
        color: #fff;
        cursor: pointer;
        user-select: none;
    }
    &__dialog{
        width: 560px;
        height: 500px;
        background: #fff;
        border-radius: 30px 0 30px 30px;
        position: fixed;
        z-index: 11;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        transition: all .3s;
        b{
            display: block;
            font-size: 20px;
            margin: 88px 0 30px;
        }
        p{
            font-size: 12px;
            transform: scale(.9);
        }
        .confirm-btn{
            font-size: 12px;
            cursor: pointer;
            &:hover{
                color: $theme-color;
            }
        }
        i.close{
            cursor: pointer;
            position: absolute;
            right: -20px;
            background: #000;
            color: #fff;
            padding: 2px 2px 2px 2px;
        }
    }
    &__btn{
        &-list{
            padding: 0 40px;
            display: flex;
            justify-content: space-between;
            margin: 50px 0 60px;
        }
        &-item{
            width: 80px;
            height: 80px;
            text-align: center;
            font-size: 12px;
            background: #e9f4fc;
            color: $theme-color;
            border-radius: 8px;
            cursor: pointer;
            i{
                font-size: 24px;
                margin: 20px 0 8px;
            }
            span{
                display: block;
                white-space: nowrap;
                transform: scale(.7);
            }
            &:hover, &.active{
                background: $theme-color;
                color: #fff;
            }
        }
    }
    &__svg{
        pointer-events: none;
        position: fixed;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        z-index: 12;
    }
    &__end-point{
        position: fixed;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        border: 2px solid #127fd2;
        background: #fff;
        z-index: 13;
    }
}
</style>
