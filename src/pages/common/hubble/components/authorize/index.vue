<template>
    <el-dialog
        class="hubble-page__authorize"
        :visible.sync="value"
        :before-close="handleClose"
        title="文件授权"
    >
        <p>您好，使用该功能需要授权上上签解析合同文件内容。</p>
        <span slot="footer">
            <a target="_blank" href="/哈勃产品使用须知.pdf">《合同授权协议》</a>
            <el-button @click="handleClose">取消</el-button>
            <el-button type="primary" @click="handleAuthorize">授权</el-button>
        </span>
    </el-dialog>
</template>

<script>
export default {
    props: {
        value: {
            type: Boolean,
            default: false,
        },
    },
    methods: {
        handleClose() {
            this.$emit('input', false);
        },
        handleAuthorize() {
            this.$http.post('/web/hubble/users/permissions/agree-authorization-letter').then(() => {
                this.$emit('handleAuthorize');
                this.$emit('input', false);
            });
        },
    },
};
</script>

<style lang="scss">
.hubble-page__authorize .el-dialog{
    font-size: 14px;
    width: 400px;
    .el-dialog__body {
        padding: 20px 20px;
    }
    a{
        float: left;
        line-height: 35px;
    }
}
</style>
