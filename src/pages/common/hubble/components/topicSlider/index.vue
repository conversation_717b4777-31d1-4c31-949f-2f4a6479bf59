<template>
    <div class="hubble-page__layout-slider">
        <Upload
            id="guide-upload"
            class="hubble-page__slider-upload"
            @onUploadSuccess="uploadSuccess"
            v-if="!$route.meta.isUploadPage"
            :needLoading="false"
        >
            <i class="el-icon-ssq-shangchuan"></i>
            上传文档
        </Upload>
        <ul class="hubble-page__slider-list" @scroll="handleScroll">
            <li :class="`hubble-page__slider-item ${ topic.topicId === $route.params.topicId && 'active' }`" v-for="(topic, i) in topicList" :key="topic.topicId">
                <el-tooltip :open-delay="500" effect="dark" :content="topic.topicTitle" placement="top">
                    <span class="hubble-page__slider-name" @click="handleSwitchTopic(topic.topicId)">
                        <i class="el-icon-ssq-kongbaiwendang"></i>
                        {{ topic.topicTitle }}
                    </span>
                </el-tooltip>
                <span class="hubble-page__slider-operate">
                    <el-tooltip :open-delay="500" effect="dark" content="重命名" placement="top">
                        <i class="el-icon-ssq-bianjibiaoqian" @click="handleRename(i)"></i>
                    </el-tooltip>
                    <el-tooltip :open-delay="500" effect="dark" content="删除" placement="top">
                        <i class="el-icon-ssq-Hubbleshanchu" @click="handleDelete(i)"></i>
                    </el-tooltip>
                </span>
            </li>
            <li class="empty">
                <img src="~src/pages/common/hubble/img/loading.gif" v-if="loading" alt="">
                <span v-else-if="noMore">没有更多了</span>
            </li>
        </ul>
        <el-dialog
            class="hubble-page__slider-rename"
            title="重命名"
            :visible.sync="dialogVisible"
            :modal-append-to-body="false"
        >
            <p>新的名称</p>
            <el-input type="textarea" v-model="currentTopic.topicTitle"></el-input>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="rename">确认</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import Upload from '../upload/index.vue';
export default {
    components: {
        Upload,
    },
    data() {
        return {
            topicList: [],
            startTopicId: '',
            size: 20,
            loading: false,
            noMore: false,
            currentIndex: 0,
            dialogVisible: false,
            currentTopic: {},
            routeTopicId: this.$route.params.topicId || '',
        };
    },
    computed: {
        ...mapState('hubble', ['showSlider']),
        isTopicPage() {
            return this.routeTopicId.length > 0;
        },
    },
    methods: {
        ...mapMutations('hubble', ['toggleSlider']),
        init() {
            this.startTopicId = '';
            this.topicList = [];
            this.getTopicList();
        },
        async handleScroll(event) {
            if (this.noMore || this.loading) {
                return;
            }
            const list = event.target;
            const scrollPosition = list.scrollTop + list.clientHeight;
            const scrollHeight = list.scrollHeight;

            if (scrollPosition >= scrollHeight - 20) {
                this.getTopicList();
            }
        },
        uploadSuccess({ topicId, fileName }) {
            this.topicList.unshift({
                topicId,
                topicTitle: fileName,
            });
            this.handleSwitchTopic(topicId);
        },
        handleSwitchTopic(topicId) {
            this.$router.push(`/hubble/chat/${topicId}`);
        },
        getTopicList() {
            this.loading = true;
            return this.$http('web/hubble/topic/list', {
                params: {
                    startTopicId: this.startTopicId,
                    number: this.size,
                },
            }).then(res => {
                const topicLength = res.data.length;
                this.topicList = this.topicList.concat(res.data);
                this.startTopicId = topicLength ? this.topicList[this.topicList.length - 1].topicId : '';
                this.noMore = topicLength < this.size;

                this.toggleSlider(topicLength !== 0 || this.isTopicPage);
            }).finally(() => {
                this.loading = false;
            });
        },
        handleDelete(index) {
            this.setCurrentTopic(index);
            this.$confirm('此操作将永久删除该话题, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                this.$http.delete(`/web/hubble/topic/${this.currentTopic.topicId}`).then(() => {
                    this.topicList.splice(index, 1);
                    this.$MessageToast('删除成功');
                    if (this.currentTopic.topicId === this.$route.params.topicId) {
                        this.$router.replace(this.topicList[0] ? `/hubble/chat/${this.topicList[0].topicId}` : '/hubble/upload');
                    }
                    if (this.topicList.length === 0) {
                        this.toggleSlider(this.isTopicPage);
                    }
                });
            });
        },
        handleRename(index) {
            this.setCurrentTopic(index);
            this.dialogVisible = true;
        },
        setCurrentTopic(index) {
            this.currentIndex = index;
            this.currentTopic = JSON.parse(JSON.stringify(this.topicList[index]));
        },
        rename() {
            const { topicId, topicTitle } = this.currentTopic;
            this.$http.post(`/web/hubble/topic/${topicId}/rename`, {
                topicTitle,
            }).then(() => {
                this.dialogVisible = false;
                this.topicList[this.currentIndex].topicTitle = topicTitle;
                this.$MessageToast('重命名成功');
            });
        },
    },
    created() {
        this.init();
    },
};
</script>

<style lang="scss">
.hubble-page__slider{
    &-rename{
        .el-dialog{
            width: 300px;
        }
    }
    &-upload{
        width: 170px;
        line-height: 40px;
        border-radius: 2px;
        margin: 12px auto;
        background:
            linear-gradient(90deg, #999 50%, transparent 0) repeat-x,
            linear-gradient(90deg, #999 50%, transparent 0) repeat-x,
            linear-gradient(0deg, #999 50%, transparent 0) repeat-y,
            linear-gradient(0deg, #999 50%, transparent 0) repeat-y;
        background-size: 4px 2px, 4px 2px, 2px 4px, 2px 4px;
        background-position: 0 0, 0 100%, 0 0, 100% 0;
        &.loading {
            animation: linearGradientMove .3s infinite linear;
            cursor: not-allowed;
        }
        @keyframes linearGradientMove {
            100% {
                background-position: 4px 0, -4px 100%, 0 -4px, 100% 4px;
            }
        }
        .el-upload{
            display: block;
            font-size: 12px;
            text-align: center;
        }
        &:hover{
            border-color: $theme-color;
            color: $theme-color;
        }
    }
    &-list{
        height: calc(100% - 80px);
        overflow: auto;
        .empty{
            text-align: center;
            color: #ccc;
            font-size: 12px;
            line-height: 50px;
        }
    }
    &-item{
        font-size: 12px;
        height: 50px;
        padding: 15px;
        box-sizing: border-box;
        border-bottom: 1px solid #eee;
        &.active, &:hover{
            color: $theme-color;
        }
        &:hover{
            .hubble-page__slider-operate{
                display: unset;
            }
        }
    }
    &-name{
        display: inline-block;
        width: calc(100% - 35px);
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        cursor: pointer;
    }
    &-operate{
        float: right;
        margin-left: 5px;
        display: none;
        i{
            color: $theme-color;
            cursor: pointer;
            & + i{
                margin-left: 5px;
            }
        }
    }
}
</style>
