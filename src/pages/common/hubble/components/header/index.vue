<template>
    <div>
        <div class="hubble-component__header">
            <i :class="`slider-switch el-icon-ssq-Hubblewendangliebiao ${ showSlider && 'active' }`" v-if="!$route.meta.isSharePage" @click="toggleSlider(!showSlider)"></i>
            <b>Hubble</b>
            <div class="button-box" v-if="!$route.meta.isSharePage">
                <el-tooltip v-if="currentPackageDetail.packageName" :open-delay="500" effect="dark" content="账号设置">
                    <i class="el-icon-ssq-Hubbleshezhi" @click="handleSetting"></i>
                </el-tooltip>
                <el-tooltip :open-delay="500" effect="dark" content="分享" v-if="!!topicId">
                    <i class="el-icon-ssq-Hubblefenxiang1" @click="handleShare"></i>
                </el-tooltip>
                <el-tooltip :open-delay="500" effect="dark" content="我的版本">
                    <i class="el-icon-ssq-Hubblewodetaocan" @click="togglePackageDialog(true)"></i>
                </el-tooltip>
                <!-- <el-tooltip :open-delay="500" effect="dark" content="邀请好友">
                    <i class="el-icon-ssq-liwu" @click="inviteDialogVisible = true"></i>
                </el-tooltip> -->
            </div>
            <el-dialog
                class="hubble-component__share-dialog"
                :visible.sync="dialogVisible"
                width="600px"
                title="分享 Hubble 智能文档"
                :close-on-click-modal="false"
            >
                <div class="hubble-component__share-dialog-content">
                    {{ shareData.shareUserName }}邀请您参与Hubble智能文档<br>
                    合同名称：{{ shareData.shareFileName }}<br><br>
                    点击链接直接进入：<br>
                    {{ shareData.shareLink }}<br><br>
                    查看密码：<br>
                    {{ shareData.password }} <br><br>
                    复制该信息，分享给其他人即可
                </div>
                <div class="hubble-component__share-dialog-switch">
                    <el-switch :disabled="shareLoading" v-model="shareLinkVisible" @change="handleSwitch"></el-switch>开启/关闭分享链接，将决定其他人是否可以继续查看文档
                </div>
                <el-button
                    type="primary"
                    @click="handleCopy"
                >复制信息</el-button>
            </el-dialog>
            <el-dialog
                class="hubble-component__invite-dialog"
                :visible.sync="inviteDialogVisible"
                width="340px"
                title="邀请好友"
                :close-on-click-modal="false"
            >
                <p>输入好友手机号，邀请体验：</p>
                <el-input v-model="inviteAccount" placeholder="请输入好友手机号"></el-input>
                <el-button
                    type="primary"
                    @click="handleInvite"
                >发送邀请</el-button>
            </el-dialog>
            <el-dialog
                class="hubble-component__setting-dialog"
                :visible.sync="settingDialogVisible"
                title="账号设置"
                :close-on-click-modal="false"
            >
                <ul class="hubble-component__setting-dialog__tab">
                    <li class="hubble-component__setting-dialog__tab-item">
                        <i class="el-icon-ssq-taocanxiangqing"></i>
                        套餐详情
                    </li>
                </ul>
                <div class="hubble-component__setting-dialog__content">
                    <div class="hubble-component__setting-dialog__package">
                        <p>套餐版本：{{ currentPackageDetail.packageName }}</p>
                        <p>当前套餐：{{ currentPackageDetail.effectiveTime }} ~ {{ currentPackageDetail.expireTime }}（生效及截止日期）</p>
                        <div class="hubble-component__setting-dialog__dosage">
                            <div>
                                <div class="hubble-component__setting-dialog__dosage-progress">
                                    <div class="hubble-component__setting-dialog__dosage-usage" :style="`width: ${currentPackageDetail.documentPercent}%`"></div>
                                </div>
                                <div class="hubble-component__setting-dialog__dosage-text">
                                    已用{{ currentPackageDetail.documentUsage }}份
                                    <span>共{{ currentPackageDetail.initialDocumentCount }}份文档</span>
                                </div>
                            </div>
                            <div>
                                <div class="hubble-component__setting-dialog__dosage-progress">
                                    <div class="hubble-component__setting-dialog__dosage-usage" :style="`width: ${currentPackageDetail.chatPercent}%`"></div>
                                </div>
                                <div class="hubble-component__setting-dialog__dosage-text">
                                    已用{{ currentPackageDetail.chatUsage }}次
                                    <span>共{{ currentPackageDetail.initialChatCount }}次对话</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <p>已购套餐：{{ currentPackage.planOverviewEffectiveTime }} ~ {{ currentPackage.planOverviewExpireTime }} （{{ currentPackage.ownedPlanCount }}期，套餐容量同上） </p>
                </div>
            </el-dialog>
        </div>
    </div>
</template>

<script>
import { mapMutations, mapState } from 'vuex';
export default {
    data() {
        return {
            dialogVisible: false,
            inviteDialogVisible: false,
            settingDialogVisible: false,
            inviteAccount: '',
            shareData: {},
            showPackageDialog: false,
            shareLoading: false,
            shareLinkVisible: false,
        };
    },
    computed: {
        ...mapState('hubble', ['currentPackage', 'showSlider']),
        topicId() {
            return this.$route.params.topicId || '';
        },
        currentPackageDetail() {
            return this.currentPackage.currentUserPlan;
        },
    },
    methods: {
        ...mapMutations('hubble', ['setPackage', 'togglePackageDialog', 'toggleSlider']),
        async handleSetting() {
            await this.$emit('initPackage');
            this.settingDialogVisible = true;
        },
        handleShare() {
            this.shareLoading = true;
            this.$http.post(`/web/hubble/topic/${this.topicId}/share`).then((res) => {
                this.shareData = res.data;
                this.shareLinkVisible = res.data.ifOpen;
                this.dialogVisible = true;
            }).finally(() => {
                this.shareLoading = false;
            });
        },
        handleCopy() {
            const textArea = document.createElement('textarea');
            textArea.value = `${this.shareData.shareUserName}邀请您参与Hubble智能文档\n合同名称：${this.shareData.shareFileName}\n\n点击链接直接进入：\n${this.shareData.shareLink}\n\n查看密码：\n${this.shareData.password}\n\n复制该信息，分享给其他人即可`;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            this.$MessageToast({
                message: '复制成功',
                type: 'success',
            });
        },
        handleInvite() {

        },
        handleSwitch(val) {
            this.shareLoading = true;
            this.$http.post(`/web/hubble/topic/${this.topicId}/share/shutdown?ifShutdown=${!val}`).catch(() => {
                this.shareLinkVisible = !val;
            }).finally(() => {
                this.shareLoading = false;
            });
        },
    },
};
</script>

<style lang="scss">
.hubble-component__header{
    color: #fff;
    height: 60px;
    background: #002B45;
    box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.1);
    font-size: 16px;
    line-height: 60px;
    i.slider-switch{
        cursor: pointer;
        transition: color, 0.3s;
        padding: 0 20px;
    }
    b{
        display: inline-block;
        padding: 0 20px;
        border-left: 1px solid #2A4C66;
    }
    .button-box{
        float: right;
        margin: 15px 20px 0 ;
        display: flex;
        i{
            font-size: 30px;
            padding: 0;
            margin-left: 18px;
            border-radius: 4px;
            cursor: pointer;
            &:hover{
                background: #004466;
            }
        }
    }
}

.hubble-component__share-dialog{
    line-height: 22px;
    .el-dialog{
        width: 600px !important;
    }
    &-content{
        color: #333;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background: #fbfbfb;
    }
    .el-button{
        margin: 20px 0 20px 460px;
        padding: 7px 21px;
    }
    &-switch{
        position: absolute;
        bottom: 53px;
        display: flex;
        .el-switch{
            transform: scale(.8);
        }
    }
}
.hubble-component__setting-dialog{
    .el-dialog{
        width: 600px;
        &__body{
            padding: 0;
            display: flex;
        }
    }
    &__tab{
        width: 140px;
        background: #f8f8f8;
        padding: 10px;
        &-item{
            background: #fff;
            line-height: 40px;
            text-align: center;
            i{
                margin-right: 10px;
            }
            &+&-item{
                margin-top: 10px;
            }
        }
    }
    &__content{
        padding: 10px 20px 30px;
        flex: 1;
        border-left: 1px solid #eee;
        p{
            line-height: 35px;
        }
    }
    &__dosage{
        margin: 20px 0 0;
        background: #f8f8f8;
        padding: 25px;
        font-size: 12px;
        line-height: 12px;
        &-progress{
            height: 6px;
            background: #fff;
            border-radius: 4px;
            overflow: hidden;
        }
        &-usage{
            height: 100%;
            width: 0;
            background-image: linear-gradient(90deg, #76C2FF 0%, #0C8AEE 100%);
            border-radius: 4px;
        }
        &-text{
            margin: 12px 0 25px;
            span{
                float: right;
                color: #999;
            }
        }
    }
}
</style>
