.login-module{
	.inline-block {
		display: inline-block;
	}
	.login-header{
		max-width: 100%;
		.logo-link{
			display: inline-block;
			vertical-align: middle;
			img {
				display: inline;
				vertical-align: middle;
			}
		}
		.login-title{
			display: inline-block;
			margin: 33px 0 33px 20px;
			padding-left: 20px;
			font-size: 24px;
			color: #666;
			border-left: 1px solid #ddd;
			vertical-align: middle;
		}
		.lang-change {
            font-size: 14px;
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            span {
                display: inline-block;
                padding: 0 5px;
            }
            .lang-ru{
                font-family: 'Times New Roman';
            }
        }
	}

	.login-banner{
		position: relative;
		height: 524px;
		overflow: hidden;

		.login-banner-main{
			position: absolute;
			width: 100%;
			height: 524px;
			background: url(~img/login_bg.png) no-repeat;
			background-position: center 0;
			background-size: cover;
		}
	}

	.login-banner-content {
		position: relative;
		height: 100%;
		max-width: 100%;

		.login-banner-text{
			padding-top: 180px;
			color: #fff;
			h2{
				font-size: 48px;
			}
			h3{
				line-height: 48px;
				font-size: 30px;
			}
		}
	}

	.login-box{
		position: absolute;
		right: 0;
		top: 50%;
		transform: translateY(-50%);
		width: 320px;
		padding: 30px;
		min-height: 303px;
		background: #fff;

		.el-input{
			border-radius: 1px;
			input {
				padding-left: 50px;
			}
		}

		.login-i {
			position: absolute;
			top: 1px;
		    left: 1px;
			width: 40px;
		    height: 38px;
		    line-height: 40px;
		    text-align: center;
		    background-color: #F8F9FA;
		    border-right: 1px solid #cfcfcf;
		    font-size: 20px;
		    color: #B7B8B9;
		}
	}

	.login-box-title{
		position: relative;
		padding-bottom: 21px;
		.login-type-con {
			height: 32px;
			border-bottom: 2px solid #e5e5e5;
		}

		.login-type-title{
			padding-bottom: 10px;
			height: 32px;
			font-size: 16px;
			color: #333;

			.switch-item{
				margin-right: 20px;
				padding-bottom: 5px;
				cursor: pointer;

				&.active{
					color: #333;
					border-bottom: 2px solid #127fd2;
				}
			}
		}

		.login-type-switch{
			position: absolute;
			right: -15px;
			top: -15px;
			width: 79px;
			height: 72px;
			cursor: pointer;

			&.login-type-normal{
				background: url("~img/login-type-scan.png") no-repeat;
			}

			&.login-type-scaner{
				background: url("~img/login-type-pwd.png") no-repeat;
			}
		}
			
		.login-type {
			width: 49%;
			height: 100%;
			text-align: center;
			font-size: 16px;
			color: #333;
			cursor: pointer;
			float: left;
			&.active {
				color: #127fd2;
				border-bottom: 2px solid #127fd2;
			}
		}
		.login-error{
			position: absolute;
			bottom: 0;
			width: 100%;
			height: 28px;
			line-height: 28px;

			.login-error-detail{
				color: #f86b26;
				font-size: 12px;
				.link{
					color: #127fd2;
					cursor: pointer;
				}
			}
		}
		
		.login-type-transform{
			position: absolute;
			bottom: 0;
			right: 0;
			line-height: 28px;
			font-size: 12px;
			color: #333;
			cursor: pointer;
		}
	}
	.captcha-login-con {
		.captcha-input-con {
			position: relative;
			margin-top: 20px;
		}
		// .captcha-input {
		// 	width: 208px;
		// 	input {
		// 		width: 208px;
		// 	}
		// }
		.countDown {
			position: absolute;
		    top: 0;
		    right: 0;
		    width: 98px;
		    height: 40px;
		    margin-left: -1px;
		}
	}

	.scaner-login-con{
		position: relative;
		height: 250px;

		.scaner-login-content{
			padding-top: 5px;
			.scaner-login-inProcess{
				padding-bottom: 10px;

				img{
					cursor: pointer;
				}
			}

			.scaner-login-failure{
				p{
					position: relative;
					margin-bottom: 10px;
					
					img{
						opacity: 0.3;
					}

					i{
						position: absolute;
						left: 50%;
						top: 50%;
						transform: translate(-50%,-55%);
						border: 9px solid #fff;
						font-size: 16px;
						color: #137FD2;
					    border-radius: 50%;
					    cursor: pointer;
					}
				}
			}

			.scaner-login-success{
				padding-top: 25px;
				margin-bottom: 25px;
				.el-icon-ssq-right-filling{
					font-size: 28px;
					color: #009987;
				}
				p{
					line-height: 30px;
					font-size: 16px;
				}
			}
			p{
				line-height: 20px;
				font-size: 14px;
				color: #333;
				text-align: center;
			}
		}

		.scaner-login-footer{
			position: absolute;
			left: 0;
			bottom: 0;
			width: 100%;
			text-align: right;

			.scaner-downloadApp{
				margin-right: 30px;
				position: relative;
				display: inline-block;

				span{
					font-size: 12px;
					color: #007DD5;
					cursor: pointer;
				}

				img{
					position: absolute;
					left: 0;
					bottom: -95px;
					display: none;
					border: 5px solid #fff;
					box-shadow: 0px 0px 7px 0px #999;
				}

				&:hover{
					img{
						display: block;
					}
				}
			}

			a{
				font-size: 12px;
			}
		}
	}

	.login-btn{
		width: 100%;
		height: 42px;
		margin-top: 25px;
		margin-bottom: 30px;
		background: #127fd2;
		// border-color: #127fd2;
		border-radius: 1px;
		font-size: 16px;
		border: none;
		&:hover{
			background: #1687dc;
			// border-color: #1687dc;
		}
	}

	.forgetPass-line{
		margin-top: 10px;
		text-align: right;

		a, span{
			font-size: 12px;
			cursor: pointer;
			// color: #127fd2;
		}
	}

	.register-line{
		text-align: center;
		color: #666666;
		font-size: 12px;

		a{
			// color: #127fd2;
		}
	}

	.login-footer{
		background-color: #fff;
	}
}
.login-module.brand input{
	outline: none;
	&:focus, &:hover{
		border-color: #ccc;
		box-shadow: unset;
	}
}
.login-module.russia{
	.login-box{
		width: 480px;
	}
	.countDown{
		width: 110px;
		font-size: 12px;
	}
}