const handleMap = {
    '.brand-color': (el, color) => {
        el.style.color = color;
    },
    '.brand-background': (el, color) => {
        el.style.background = color;
    },
    '.brand-border': (el, color) => {
        el.style.borderColor = color;
    },
    '.brand-hover-background': (el, color, hoverColor) => {
        el.onmouseover = function() {
            this.style.background = hoverColor;
        };
        el.onmouseout = function() {
            this.style.background = color;
        };
    },
    '.el-input__inner': (el, color, hoverColor) => {
        el.onfocus = function() {
            this.style.borderColor = color;
            this.style.boxShadow = `0 0 2px ${hoverColor}`;
        };
        el.onblur = function() {
            this.style.borderColor = '#ccc';
            this.style.boxShadow = 'unset';
        };
    },
};
export function changeColor(type, color, hoverColor) {
    const nodeList = document.querySelectorAll(type);
    for (let i = 0; i < nodeList.length; i += 1) {
        handleMap[type](nodeList[i], `#${color}`, `#${hoverColor}`);
    }
}
