<!-- 扫码登录 -->
<template>
    <div class="scaner-login-con">
        <div class="scaner-login-content">
            <!-- 扫码 -->
            <div class="scaner-login-inProcess" v-show="curStatus === 'scaning'">
                <p><img :src="qrCodePath" width="138" height="138" alt="" @click="handleRefresh" /></p>
            </div>
            <!-- 二维码失效 -->
            <div class="scaner-login-failure" v-show="curStatus === 'failure'">
                <p>{{ $t('login.scanFailure') }}</p>
                <p>
                    <img :src="qrCodePath" width="128" height="128" alt="" @click="handleRefresh" />
                    <i class="el-icon-ssq-huifu" @click="handleRefresh"></i>
                </p>
            </div>
            <!-- 扫码成功 -->
            <div class="scaner-login-success" v-show="curStatus === 'success'">
                <p><i class="el-icon-ssq-right-filling"></i></p>
                <p>{{ $t('login.scanSuccess') }}</p>
            </div>
            <!-- 扫码提示 -->
            <p class="brand-color">{{ curStatus === 'success' ? $t('login.appLoginTip') : $t('login.scanLoginTip') }}</p>
        </div>
        <div class="scaner-login-footer">
            <div class="scaner-downloadApp">
                <span class="brand-color">{{ $t("login.downloadApp") }}</span>
                <img src="~img/app-download-qrcode.png" width="76" alt="">
            </div>
            <router-link class="scan_register_now" :to="registUrl"><span class="brand-color">{{ $t("login.registerNow") }}</span></router-link>
        </div>
    </div>
</template>

<script>
export default {
    // eslint-disable-next-line
    props: ['registUrl'],
    data() {
        return {
            prevQrCodeId: null,
            qrCodeId: null,
            qrCodePath: '',
            curStatus: 'scaning', // 扫码中: scaning, 扫码失败: failure, 扫码成功: success
            interval: null,
            interValCount: 0,
        };
    },
    methods: {
        // 刷新二维码
        handleRefresh() {
            this.prevQrCodeId = this.qrCodeId;
            this.$http(`/users/ignore/appQR/login/qrcode-image?qrCodeId=${this.prevQrCodeId ? this.prevQrCodeId : null}`)
                .then(res => {
                    this.curStatus = 'scaning';
                    this.qrCodePath = `data:image/jpeg;base64,${res.data.value}`;
                    this.qrCodeId = res.data.qrCodeId;
                    this.$nextTick(() => {
                        this.handleInterval();
                    });
                });
        },
        // 轮询
        handleInterval() {
            clearInterval(this.interval);
            this.interval = setInterval(this.requestStatus, 3000);
        },
        // 轮询请求
        requestStatus() {
            // 一分钟后二维码失效状态，清空倒计时，重置计数器
            if (this.interValCount === 20) {
                clearInterval(this.interval);
                this.curStatus = 'failure';
                this.interValCount = 0;
                return;
            } else {
                this.$http.post('/auth-center/user/QRlogin', {
                    qrCodeId: this.qrCodeId,
                })
                    .then(res => {
                        // 1-生成二维码，2-APP扫描成功，3-APP确认登录，4-APP取消操作，5-二维码已过期
                        switch (res.data.QRStatus) {
                            case '1':
                                break;
                            case '2':
                                this.curStatus = 'success';
                                break;
                            case '3':
                                this.$MessageToast.success('登录成功！');
                                clearInterval(this.interval);
                                this.$token.save(res.data.access_token, res.data.refresh_token);
                                setTimeout(() => {
                                    this.$router.push('/account-center/home');
                                }, 1500);
                                break;
                            case '4':
                                this.handleRefresh();
                                break;
                            case '5':
                                this.curStatus = 'failure';
                                clearInterval(this.interval);
                                break;
                        }
                    });
            }

            this.interValCount++;
        },
    },
    activated() {
        this.handleRefresh();
    },
    // 销毁并清除定时器
    deactivated() {
        clearInterval(this.interval);
    },
};
</script>
