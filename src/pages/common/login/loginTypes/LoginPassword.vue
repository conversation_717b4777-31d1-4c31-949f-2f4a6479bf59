<!-- 密码登录 -->
<template>
    <div class="password-login-con">
        <el-input
            :disabled="accountDisable"
            class="ssq-register-input"
            id="login_account_input"
            :placeholder="$t('login.accountPlaceholder')"
            style="margin-bottom:20px;"
            v-model="passwordData.account"
            v-focus
        >
            <i slot="icon" class="el-icon-ssq-user login-i"></i>
            <ElIDelete slot="icon" v-if="!accountDisable"></ElIDelete>
        </el-input>
        <el-input
            class="ssq-register-input password-input"
            id="login_pass_input"
            type="password"
            :placeholder="$t('login.passwordPlaceholder')"
            v-model="passwordData.password"
        >
            <i slot="icon" class="el-icon-ssq-password login-i"></i>
            <ElIEye v-show="passwordData.password" slot="icon"></ElIEye>
            <ElIDelete slot="icon"></ElIDelete>
        </el-input>
        <div class="pictureVer-con" v-if="passwordData.showPictureVerCon">
            <el-input
                class="ssq-register-input pictureVer"
                :placeholder="$t('login.pictureVer')"
                v-model="passwordData.imageVerifyCode"
            >
                <i slot="icon" class="el-icon-ssq-iconfontyanzhengma login-i"></i>
            </el-input>
            <PictureVerify ref="passwordData_pictureVerify" :imageKey="passwordData.imageKey" @change-imageKey="changeImageKey"></PictureVerify>
        </div>
        <p class="forgetPass-line">
            <span class="common-font-color brand-color" @click="toForgot" id="login_forget_btn">{{ $t("login.forgetPsw") }}</span>
        </p>
    </div>
</template>

<script>
import ElIDelete from 'components/el_i_delete/ElIDelete.vue';
import ElIEye from 'components/el_i_eye/ElIEye.vue';
import PictureVerify from 'components/picture_verify/PictureVerify.vue';
import { isUndefined } from 'utils/dom.js';

export default {
    components: {
        ElIDelete,
        ElIEye,
        PictureVerify,
    },
    // eslint-disable-next-line
    props: ['accountDisable', 'account'],
    data() {
        return {
            // 密码登录数据
            passwordData: {
                showErrorMsg: false,
                errorMsg: 'unanimous',
                account: '',
                password: '',
                showPictureVerCon: false,
                imageVerifyCode: '',
                imageKey: '',
            },
        };
    },
    watch: {
        account(val) {
            this.passwordData.account = val;
        },
    },
    methods: {
        /**
         * 忘记密码
         */
        toForgot() {
            if (this.HOST_ENV === 'CCB') {
                this.$router.push(`/forgotPassword?account=${this.passwordData.account.trim()}&companyid=${this.ccbcompanyid}`);
            } else {
                this.$router.push(`/forgotPassword?account=${this.passwordData.account.trim()}`);
            }
        },
        // 获取图形验证码
        changeImageKey(value) {
            this.passwordData.imageKey = value;
        },
        // 同步数据
        syncVal(opt) {
            for (const o in opt) {
                if (!isUndefined(opt[o])) {
                    this.passwordData[o] = opt[o];
                    this.$parent.$parent.passwordData[o] = opt[o];
                }
            }
        },
    },
    created() {
        this.passwordData.account = this.account;
    },
};
</script>
<style lang="scss">
    .password-input .i-eye {
        right: 30px;
    }
</style>
