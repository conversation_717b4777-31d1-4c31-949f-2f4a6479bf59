<!-- 验证码登录 -->
<template>
    <div class="captcha-login-con">
        <el-input
            :disabled="accountDisable"
            class="ssq-register-input account-input"
            id="captcha_login_account_input"
            :placeholder="$t('login.accountPlaceholder')"
            style="margin-bottom:20px;"
            v-model="captchaData.account"
            v-focus
        >
            <i slot="icon" class="el-icon-ssq-user login-i"></i>
            <ElIDelete slot="icon" v-if="!accountDisable"></ElIDelete>
        </el-input>
        <div class="pictureVer-con" v-if="captchaData.showPictureVerCon">
            <el-input
                class="ssq-register-input pictureVer"
                :placeholder="$t('login.pictureVer')"
                :maxlength="4"
                v-model="captchaData.imageVerifyCode"
            >
                <i slot="icon" class="el-icon-ssq-iconfontyanzhengma login-i"></i>
            </el-input>
            <PictureVerify ref="captchaData_pictureVerify" :imageKey="captchaData.imageKey" @change-imageKey="changeImageKey"></PictureVerify>
        </div>
        <div class="captcha-input-con">
            <el-input
                class="ssq-register-input captcha-input"
                id="captcha_login_pass_input"
                auto-complete="new-password"
                type="password"
                :placeholder="$t('login.verifyCodePlaceholder')"
                :maxlength="6"
                v-model="captchaData.verifyCode"
            >
                <i slot="icon" class="el-icon-ssq-iconfontyanzhengma login-i"></i>
                <ElIDelete slot="icon"></ElIDelete>
            </el-input>
            <CountDown class="countDown" ref="btn" :clickedFn="send" :disabled="captchaData.countDownDisabled" :second="60">
            </CountDown>
        </div>
    </div>
</template>

<script>
import resRules from 'utils/regs.js';
import { isPhoneOrMail } from 'utils/reg.js';
import ElIDelete from 'components/el_i_delete/ElIDelete.vue';
import PictureVerify from 'components/picture_verify/PictureVerify.vue';
import CountDown from 'components/countDown/CountDown.vue';
import { isUndefined } from 'utils/dom.js';

export default {
    components: {
        ElIDelete,
        PictureVerify,
        CountDown,
    },
    // eslint-disable-next-line
    props: ['accountDisable', 'account'],
    data() {
        return {
            // 验证码登录数据
            captchaData: {
                showErrorMsg: false,
                errorMsg: 'unanimous',
                account: '',
                countDownDisabled: false,
                verifyCode: '',
                verifyKey: '', // 图形验证码通过的key，提交信息用
                showPictureVerCon: false,
                imageVerifyCode: '',
                imageKey: '', // 图形验证码绑定的key
            },
            // 正则
            resRules: resRules,
        };
    },
    watch: {
        account(val) {
            this.captchaData.account = val;
        },
    },
    methods: {
        // 获取图形验证码
        changeImageKey(value) {
            this.captchaData.imageKey = value;
        },
        // 发送验证码
        send() {
            if (!this.captchaData.account.trim()) {
                this.$MessageToast.error(this.$t('login.lackAccount'));
                return;
            }

            if (
                !(this.resRules.userAccount.test(this.captchaData.account.trim()))
            ) {
                this.$MessageToast.error(this.$t('login.errEmailOrTel'));
                return;
            }

            if (this.captchaData.showPictureVerCon) {
                if (!(this.resRules.imageVerifyCode.test(this.captchaData.imageVerifyCode))) {
                    this.$MessageToast.error(this.$t('login.grapVerCodeErr'));
                    return;
                } else {
                    this.captchaData.showPictureVerCon = false;
                }
            }

            this.captchaData.countDownDisabled = true;
            setTimeout(this.sended, 0);

            // sendVerCode
            this.$http.sendVerCodeNoLogin({
                code: 'B010',
                sendType: isPhoneOrMail(this.captchaData.account) === 'phone' ? 'S' : 'E',
                target: this.captchaData.account.replace(/\s+/g, ''),
                imageCode: this.captchaData.imageVerifyCode,
                imageKey: this.captchaData.imageKey,
                noToast: 1,
            })
                .then(res => {
                    this.$MessageToast.success(this.$t('login.sendSuc'));
                    this.captchaData.showErrorMsg = false;
                    this.captchaData.verifyKey = res.data.value;
                })
                .catch(err => {
                    const res = err.response.data;
                    this.$emit('checkGraphCode', res);
                    this.$refs.btn.reset();
                });
        },
        // 验证码已发送
        sended() {
            this.$refs.btn.run();
            this.captchaData.countDownDisabled = false;
        },
        // 同步数据
        syncVal(opt) {
            for (const o in opt) {
                if (!isUndefined(opt[o])) {
                    this.captchaData[o] = opt[o];
                    this.$parent.$parent.captchaData[o] = opt[o];
                }
            }
        },
    },
    created() {
        this.captchaData.account = this.account;
    },
};
</script>
