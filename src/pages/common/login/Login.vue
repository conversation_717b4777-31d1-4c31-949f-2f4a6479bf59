<!-- 页面组件——登录页 -->
<template>
    <div class="login-module" :class="{'brand': isBrand, 'russia': lang === 'ru'}" @keyup.enter="toLogin" v-loading.fullscreen="loading && !hasChecked" v-show="!loading && hasChecked">
        <el-row class="login-header" :class="{'container': isPC}">
            <router-link :to="$store.state.login ? '/' : '/login'" class="logo-link">
                <img :src="brandInfo.logo" alt="" v-if="isBrand && brandInfo.logo" height="40">
                <img v-else src="~img/bestsign-logo.png" width="88" height="40" alt="上上签logo">
            </router-link>
            <h1 class="login-title">{{ $t("login.login") }}</h1>
            <div class="lang-change" v-if="isSinoRussia">
                <span :class="lang === 'zh' && 'active'" @click="changeLang('zh')">中文</span>|<span @click="changeLang('en')" :class="lang === 'en' && 'active'">English</span>|<span @click="changeLang('ru')" class="lang-ru" :class="lang === 'ru' && 'active'">русский язык</span>
            </div>
        </el-row>

        <el-row class="login-banner">
            <div class="login-banner-main" :class="{'login-banner-main_mobile': !isPC}" :style="brandBanner">
                <div class="login-banner-content" :class="{'container': isPC}">

                    <div class="login-banner-text" v-if="isPC && !isBrand">
                        <h2>{{ $t('login.bestSign') }}</h2>
                        <h3>{{ $t('login.bestSignDescription') }}</h3>
                    </div>

                    <div class="login-box">
                        <div class="login-box-title">
                            <!-- 登录方式标题 -->
                            <template v-if="loginType === 'scaner'">
                                <div class="login-type-title">
                                    {{ $t("login.scanLogin") }}
                                </div>
                            </template>
                            <template v-else>
                                <!-- 密码登录切换验证码登录，验证码登录切换密码登录 -->
                                <div class="login-type-title">
                                    <span class="switch-item" :class="loginType === 'password' && 'active brand-border'" @click="changeLoginType(1)">{{ $t("login.pswLogin") }}</span>
                                    <span class="switch-item" :class="loginType === 'captcha' && 'active brand-border'" @click="changeLoginType(2)">{{ $t("login.verifyLogin") }}</span>
                                </div>
                            </template>

                            <!-- 登录方式切换 -->
                            <div
                                class="login-type-switch"
                                :class="loginType !== 'scaner' ? 'login-type-normal' : 'login-type-scaner'"
                                @click="changeLoginType(loginType !== 'scaner' ? 3 : 1)"
                            ></div>

                            <div class="login-error" v-if="loginType !== 'scaner'">
                                <div class="login-error-detail">
                                    <template
                                        v-if="loginType == 'password' && passwordData.showErrorMsg && $refs.PasswordLogin"
                                    >
                                        <template
                                            v-if="passwordData.errorMsg == 'unanimous'"
                                        >
                                            {{ $t("login.errAccountOrPwdTip") }}
                                            <router-link
                                                :to="`/forgotPassword?account=${$refs.PasswordLogin.passwordData.account.trim()}`"
                                                class="link"
                                                id="login_forget_tip"
                                            >
                                                <span class="brand-color">{{ $t("login.forgetPsw") }}</span>
                                            </router-link>
                                            {{ $t("login.or") }}
                                            <router-link
                                                :to="registUrl"
                                                class="link"
                                                id="login_register_tip"
                                            >
                                                <span class="brand-color">{{ $t("login.noRegister") }}</span>
                                            </router-link>
                                        </template>
                                        <template v-else>
                                            {{ passwordData.errorMsg }}
                                        </template>
                                    </template>
                                    <template
                                        v-if="loginType == 'captcha' && captchaData.showErrorMsg"
                                    >
                                        {{ captchaData.errorMsg }}
                                    </template>
                                </div>
                            </div>
                            <!-- 密码登录切换验证码登录，验证码登录切换密码登录 -->
                            <!-- <div v-if="loginType !== 'scaner'" class="login-type-transform" @click="changeLoginType(loginType == 'password' ? 2 : 1)">
                              {{ loginType == 'password' ? $t("login.useVerifyLogin") : $t("login.usePswLogin")}}
                            </div> -->
                        </div>

                        <!-- 登录方式：密码、验证码、扫码 -->
                        <keep-alive include="ScanerLogin">
                            <component
                                :is="curLoginType"
                                :loginType="loginType"
                                :registUrl="registUrl"
                                :account="redirectAccount"
                                :ref="curLoginType"
                                @checkGraphCode="checkGraphCode"
                            ></component>
                        </keep-alive>

                        <!-- 除扫码外的情况下显示登录按钮 -->
                        <template v-if="loginType !== 'scaner'">
                            <el-button type="primary" @click="toLogin" class="login-btn brand-background brand-hover-background" id="login_btn">{{ $t("login.login") }}</el-button>
                            <p
                                v-if="isPC"
                                class="register-line"
                            >
                                {{ $t("login.noAccount") }}
                                <router-link
                                    class="common-font-color"
                                    :to="registUrl"
                                    id="login_register_now"
                                >
                                    <span class="brand-color">
                                        {{ $t("login.registerNow") }}>>
                                    </span>
                                </router-link>
                            </p>
                        </template>
                    </div>
                </div>
            </div>
        </el-row>
        <RegisterFooter v-if="isPC" class="login-footer"></RegisterFooter>
    </div>
</template>

<script>
import { changeColor } from './changeColor.js';
import RegisterFooter from 'components/register_footer/RegisterFooter.vue';
import ElIDelete from 'components/el_i_delete/ElIDelete.vue';
import resRules from 'utils/regs.js';
// import { isPhoneOrMail } from 'utils/reg.js';
import { isPC } from 'src/common/utils/device.js';
import PictureVerify from 'components/picture_verify/PictureVerify.vue';
import CountDown from 'components/countDown/CountDown.vue';

import PasswordLogin from './loginTypes/LoginPassword.vue';
import CaptchaLogin from './loginTypes/LoginCaptcha.vue';
import ScanerLogin from './loginTypes/LoginScaner.vue';

import { mobileRouteObj } from 'src/common/utils/routeVariable.js';

import Qs from 'qs';
import { isEmpty } from 'lodash';

export default {
    components: {
        RegisterFooter,
        ElIDelete,
        PictureVerify,
        CountDown,
        PasswordLogin,
        CaptchaLogin,
        ScanerLogin,
    },
    data() {
        return {
            HOST_ENV: this.GLOBAL.HOST_ENV,
            ccbcompanyid: '',
            registUrl: '/register',
            redirectAccount: '',
            loginType: 'password', // 'password':密码 'captcha':验证码 'scaner': 扫码
            // 密码登录数据
            passwordData: {
                showErrorMsg: false,
                errorMsg: 'unanimous',
                account: '',
                password: '',
                showPictureVerCon: false,
                imageVerifyCode: '',
                imageKey: '',
            },
            // 验证码登录数据
            captchaData: {
                showErrorMsg: false,
                errorMsg: 'unanimous',
                account: '',
                countDownDisabled: false,
                verifyCode: '',
                verifyKey: '', // 图形验证码通过的key，提交信息用
                showPictureVerCon: false,
                imageVerifyCode: '',
                imageKey: '', // 图形验证码绑定的key
            },
            // 正则
            resRules: resRules,
            isPC: isPC(),
            brandInfo: {},
            loading: true,
            isBrand: false,
            isSinoRussia: false,
            hasChecked: false,
        };
    },
    computed: {
        /**
         * @return {String} [登录方式标题]
         */
        loginTypeTitle() {
            switch (this.loginType) {
                case 'password':
                    return '密码登录';
                case 'captcha':
                    return '验证码登录';
                case 'scaner':
                    return '扫码登录';
                default:
                    return '密码登录';
            }
        },
        /**
         * @return {String} [登录方式]
         */
        curLoginType() {
            switch (this.loginType) {
                case 'password':
                    return 'PasswordLogin';
                case 'captcha':
                    return 'CaptchaLogin';
                case 'scaner':
                    return 'ScanerLogin';
                default:
                    return 'PasswordLogin';
            }
        },
        /**
         * @return {String} 当前表单名称
         */
        formName() {
            return this.loginType === 'password' ? 'passwordData' : 'captchaData';
        },
        brandBanner() {
            if (this.isBrand && this.brandInfo.backPic) {
                return `background: url(${
                    this.brandInfo.backPic
                }) no-repeat; background-size: cover`;
            }
            return '';
        },
        lang() {
            return this.$i18n.locale;
        },
    },
    watch: {
        // 'passwordData.account'(val) {
        // this.captchaData.account = val;
        // },
        // 'captchaData.account'(val) {
        // this.passwordData.account = val;
        // }
    },
    beforeRouteEnter(to, from, next) {
        next(vm => {
            // 如果是直接打开登录页或者从其他未登录页跳转过来，就检测是否已登录
            //   if (
            //     from.meta.noLogin === true ||
            //     from.path === "/" ||
            //     from.path === "*" ||
            //     from.path === ""
            //   ) {
            vm.checkLogined(); // 或者新混合云跳转进入，或者公有云ui登陆了新混合云主体时
            //   }
        });
    },
    methods: {
        checkBrandStatus() {
            this.$http
                .get('users/ignore/ent-brand/check')
                .then(res => {
                    if (res.data.value + '' === '3') {
                        this.isBrand = true;
                        this.$cookie.set('isBrand', 1);
                        this.getBrandInfo();
                    } else {
                        this.loading = false;
                    }
                    if (res.data.time) {
                        this.$cookie.set('copyRightRange', res.data.time);
                    } else {
                        this.$cookie.delete('copyRightRange');
                    }
                })
                .catch(() => {
                    this.loading = false;
                });
        },
        getBrandInfo() {
            this.$http
                .get('users/ignore/ent-brand')
                .then(res => {
                    if (res.data.ssoLink) {
                        location.href = res.data.ssoLink;
                        return;
                    }
                    this.brandInfo = res.data;
                    if (this.brandInfo.title) {
                        document.title = this.brandInfo.title;
                    }

                    if (this.brandInfo.loginLogo) {
                        this.$cookie.set('homeLogoFileId', this.brandInfo.loginLogo);
                    }

                    if (this.brandInfo.russia === '1') {
                        this.registUrl = '/register?sinoRussia=1';
                        this.isSinoRussia = this.brandInfo.russia === '1';
                        this.$cookie.set('sino-russia', this.brandInfo.russia);
                    }

                    this.changeTheme();
                    this.loading = false;
                })
                .catch(() => {
                    this.loading = false;
                });
            // this.brandInfo = {
            //     backPic: 'https://bestsign-static-resource.oss-cn-shanghai.aliyuncs.com/bdb8e1409658d8781756179720f17a6fee67f0b1.jpeg',
            //     color: '000000',
            //     hoverColor: '333333',
            //     logo: 'https://bestsign-static-resource.oss-cn-shanghai.aliyuncs.com/527ddf5c7cfdbd65cd6658789caab1591fa0a298.png',
            //     title: ' 测试企业品牌',
            //     url: 'abbott.bestsign.tech',
            // };
            // document.title = this.brandInfo.title;
            // this.changeTheme();
        },
        changeTheme() {
            const arr = [
                '.brand-color',
                '.brand-background',
                '.brand-border',
                '.brand-hover-background',
                '.el-input__inner',
            ];
            arr.forEach(el => {
                changeColor(
                    el,
                    this.brandInfo.color || '127fd2',
                    this.brandInfo.hoverColor || '1687dc',
                );
            });
        },
        // 通过请求 head-info 判断是否已登录，如果已登录则自动跳转到 home 页
        checkLogined() {
            // cookie中既没有access_token也没有refresh_token时，不发请求
            const accessToken = this.$cookie.get('access_token');
            const refreshToken = this.$cookie.get('refresh_token');
            if (!accessToken && !refreshToken) {
                this.hasChecked = true; return;
            }

            this.$http
                .get('/users/head-info', { noToast: 1 })
                .then(res => {
                    if (res && res.data) {
                        this.$http.headerInfoConfig(res.data);
                        this.goNextPage(res.data);
                    } else {
                        this.hasChecked = true;
                    }
                })
                .catch(() => {
                    this.hasChecked = true;
                });
        },
        /**
         * 切换登录类型
         * @param  {Number}
         * @return
         */
        changeLoginType(param) {
            let curAccountVal;
            // 切换到密码登录
            if (param === 1) {
                const isCaptcha = this.curLoginType === 'CaptchaLogin';

                if (isCaptcha) {
                    curAccountVal = this.$refs.CaptchaLogin.captchaData.account;
                    this.$refs[this.curLoginType].syncVal({
                        showErrorMsg: false,
                    });
                }

                this.loginType = 'password';
            } else if (param === 2) { // 切换到验证码登录
                curAccountVal = this.$refs.PasswordLogin.passwordData.account;

                this.$refs[this.curLoginType].syncVal({
                    showErrorMsg: false,
                });

                this.loginType = 'captcha';
            } else if (param === 3) { // 切换到扫码登录
                this.loginType = 'scaner';
            }
            this.redirectAccount = curAccountVal;
            this.$nextTick(() => {
                if (this.isBrand) {
                    this.changeTheme();
                }
            });
        },
        // 调用登录接口前校验
        checkFormat() {
            if (
                !(
                    this.resRules.userEmail.test(
                        this.$refs[this.curLoginType][this.formName].account.trim(),
                    ) ||
                    this.resRules.userPhone.test(
                        this.$refs[this.curLoginType][this.formName].account.trim(),
                    )
                )
            ) {
                this.$refs[this.curLoginType].syncVal({
                    showErrorMsg: true,
                    errorMsg: this.$t('login.errEmailOrTel'),
                });

                // this.$refs[this.curLoginType][this.formName].showErrorMsg = true;
                // this.$refs[this.curLoginType][this.formName].errorMsg = this.$t('login.errEmailOrTel');
                return false;
            }
            if (
                this.loginType === 'password' &&
                !this.$refs[this.curLoginType][this.formName].password.trim().length
            ) {
                this.$refs[this.curLoginType].syncVal({
                    showErrorMsg: true,
                    errorMsg: this.$t('login.errPwd'),
                });

                // this.$refs[this.curLoginType][this.formName].showErrorMsg = true;
                // this.$refs[this.curLoginType][this.formName].errorMsg = this.$t('login.errPwd');
                return false;
            }
            if (this.$refs[this.curLoginType][this.formName].showPictureVerCon) {
                if (
                    !this.resRules.imageVerifyCode.test(
                        this.$refs[this.curLoginType][this.formName].imageVerifyCode,
                    )
                ) {
                    this.$refs[this.curLoginType].syncVal({
                        showErrorMsg: true,
                        errorMsg: this.$t('login.grapVerCodeFormatErr'),
                    });

                    // this.$refs[this.curLoginType][this.formName].showErrorMsg = true;
                    // this.$refs[this.curLoginType][this.formName].errorMsg = this.$t('login.grapVerCodeFormatErr');
                    return false;
                }
            }
            if (this.loginType === 'captcha') {
                if (
                    !this.resRules.phoneVerifyCode.test(
                        this.$refs[this.curLoginType][this.formName].verifyCode,
                    )
                ) {
                    this.$refs[this.curLoginType].syncVal({
                        showErrorMsg: true,
                        errorMsg: this.$t('login.verCodeFormatErr'),
                    });

                    // this.$refs[this.curLoginType][this.formName].showErrorMsg = true;
                    // this.$refs[this.curLoginType][this.formName].errorMsg = this.$t('login.verCodeFormatErr');
                    return false;
                }
            }
            return true;
        },
        // 图片验证码根据接口返回数据进行不同处理
        checkGraphCode(res) {
            // 如果错误是图形验证码不能为空，就不显示忘记密码和注册提示
            if (res.message === '图片验证码不能为空') {
                // this.$refs[this.curLoginType][this.formName].errorMsg = res.message;

                this.$refs[this.curLoginType].syncVal({
                    errorMsg: res.message,
                });
            } else if (res.message === '图形验证码错误') {
                this.$refs[this.curLoginType].syncVal({
                    errorMsg: this.$t('login.grapVerCodeErr'),
                });
                // this.$refs[this.curLoginType][this.formName].errorMsg = this.$t('login.grapVerCodeErr');
            } else {
                if (this.loginType === 'password') {
                    this.$refs[this.curLoginType].syncVal({
                        errorMsg: 'unanimous',
                    });
                    // this.$refs[this.curLoginType][this.formName].errorMsg = 'unanimous';
                } else {
                    this.$refs[this.curLoginType].syncVal({
                        errorMsg: '',
                    });
                    // this.$refs[this.curLoginType][this.formName].errorMsg = '';
                }
            }

            this.$refs[this.curLoginType].syncVal({
                showErrorMsg: true,
            });
            // this.$refs[this.curLoginType][this.formName].showErrorMsg = true;

            if (res.code === '902' || res.code === '100006') {
                if (this.$refs[this.curLoginType][this.formName].showPictureVerCon) {
                    setTimeout(() => {
                        this.$refs[this.curLoginType].$refs[
                            `${this.formName}_pictureVerify`
                        ].changeImg();
                    }, 20);
                } else {
                    this.$refs[this.curLoginType].syncVal({
                        showPictureVerCon: true,
                    });
                    // this.$refs[this.curLoginType][this.formName].showPictureVerCon = true;
                }

                if (res.message !== '图片验证码不能为空') {
                    this.$MessageToast.error(res.message);
                } else {
                    this.$MessageToast.error(this.$t('login.lackGrapCode'));
                }
            } else {
                this.$MessageToast.error(res.message);
            }
        },
        // 用户是否已经存在登录密码
        checkPwdExistence() {
            return this.$http.get('/users/login-pwd/existence');
        },
        // 登录
        toLogin() {
            if (!this.checkFormat()) {
                return;
            }

            let dataObj;
            const headersObj = {
                'Content-Type': 'application/json; charset=utf-8',
            };
            if (this.loginType === 'password') {
                dataObj = {
                    account: this.$refs[this.curLoginType][this.formName].account.replace(
                        /\s+/g,
                        '',
                    ),
                    pass: this.$refs[this.curLoginType][this.formName].password,
                };
                // 需要填写图形验证码
                if (this.$refs[this.curLoginType][this.formName].showPictureVerCon) {
                    Object.assign(headersObj, {
                        additionalImgVerCode: JSON.stringify({
                            imageCode: this.$refs[this.curLoginType][this.formName]
                                .imageVerifyCode,
                            imageKey: this.$refs[this.curLoginType][this.formName].imageKey,
                        }),
                    });
                }
            } else if (this.loginType === 'captcha') {
                if (this.$refs[this.curLoginType][this.formName].verifyKey === '') {
                    this.$MessageToast.error(this.$t('login.getVerCodeTip'));
                    return;
                }
                dataObj = {
                    account: this.$refs[this.curLoginType][this.formName].account.replace(
                        /\s+/g,
                        '',
                    ),
                    verifyCode: this.$refs[this.curLoginType][this.formName].verifyCode,
                    verifyKey: this.$refs[this.curLoginType][this.formName].verifyKey,
                };
            }
            this.loginType === 'password' && (this.$localStorage.set('isPwdLogin', true));

            // ccb添加参数
            if (this.HOST_ENV === 'CCB') {
                Object.assign(dataObj, {
                    companyFlag: 'CCB',
                    companyId: this.ccbcompanyid,
                });
            }
            this.$http({
                method: 'post',
                url: '/auth-center/user/login',
                data: JSON.stringify(dataObj),
                headers: headersObj,
                noToast: 1,
            })
                .then(res => {
                    if (res.data.token != null) {
                        location.href = res.data.token;
                        return;
                    }

                    // 保存登录状态
                    // eslint-disable-next-line
                        const { access_token, refresh_token } = res.data;
                    this.$token.save(access_token, refresh_token);

                    const redirect = this.$route.query.redirect;
                    if (this.loginType === 'password') {
                        this.goNextPage(res.data);
                    } else if (this.loginType === 'captcha') {
                        this.checkPwdExistence().then(res1 => {
                            if (res1.data.value) {
                                // 存在登录密码
                                this.goNextPage(res.data);
                            } else {
                                if (redirect) {
                                    this.$router.push(
                                        `${this.GLOBAL.rootPathName}/setPassword?redirect=redirect`,
                                    );
                                } else {
                                    this.$router.push('/setPassword');
                                }
                            }
                        });
                    }
                })
                .catch(err => {
                    const res = err.response.data;

                    if (this.loginType === 'password') {
                        if (res.code === '010002') {
                            if (
                                this.$refs[this.curLoginType][this.formName].showPictureVerCon
                            ) {
                                setTimeout(() => {
                                    this.$refs[this.curLoginType].$refs[
                                        `${this.formName}_pictureVerify`
                                    ].changeImg();
                                }, 20);
                            }
                        }
                    }
                    this.checkGraphCode(res);
                });
        },
        goNextPage() {
            let next = this.$route.query.redirect;
            if (this.isPC) {
                next = next || '/account-center/home';
                next += `${next.indexOf('?') > -1 ? '&' : '?'}isPwdLogin=${this.$store.state.isPwdLogin}`;
                this.$router.push(next);
            } else {
                if (this.isPC) {
                    next = next || '/account-center/home';
                    next += `${next.indexOf('?') > -1 ? '&' : '?'}isPwdLogin=${this.$localStorage.get('isPwdLogin')}`;
                    this.$router.push(next);
                } else {
                    // 没有回调地址或者是移动端签署的页面 回到移动端合同列表
                    if (!next || next.includes('mobile')) {
                        // 手机端跳到H5合同列表页
                        next = mobileRouteObj.home;
                        next += `${next.indexOf('?') > -1 ? '&' : '?'}isPwdLogin=${this.$localStorage.get('isPwdLogin')}`;
                    }
                    location.href = next;
                }
                location.href = next;
            }
        },
        // 更换语言
        changeLang(langType) {
            this.$cookie.set('language', langType);
            this.$i18n.locale = langType;
        },
    },
    mounted() {
        //   this.getBrandInfo();
        this.checkBrandStatus();
        // 如果当前环境是CCB，初始化数据
        if (this.HOST_ENV === 'CCB') {
            this.ccbcompanyid = this.$route.query.companyid
                ? this.$route.query.companyid
                : '';
            this.registUrl = `/register?companyid=${this.ccbcompanyid}`;
            this.$cookie.set('ccbcompanyid', this.ccbcompanyid);
        } else {
            const { query } = this.$route;
            this.registUrl = `/register${!isEmpty(query) ? '?' + Qs.stringify(query) : ''}`;
        }
        const redirectStr = decodeURIComponent(this.$route.query.redirect || '');
        if (redirectStr && redirectStr.includes('act=')) { // 合同下载链接会附带account账号
            this.redirectAccount = redirectStr.split('act=')[1];
        }
    },
};
</script>

<style lang="scss">
    @import "./login.scss";

    .container {
        width: 1024px;
        margin: 0 auto;
        .pictureVer-con {
            position: relative;
            margin-top: 20px;
            .picture-verify {
                position: absolute;
                top: 1px;
                right: 1px;
            }
        }
        .el-input {
            display: block;
            width: 100%;
        }

        .el-input__inner {
            height: 40px;
            line-height: 40px;
            padding-top: 0;
            padding-bottom: 0;
            border: 1px solid #cfcfcf;
            border-radius: 0;
        }

        .login-error-i {
            margin: -2px 5px 0 15px;
            vertical-align: middle;
            color: #f76b26;
        }
    }
    .login-module .login-banner .login-banner-main.login-banner-main_mobile {
        background: #ffffff;
        .pictureVer-con {
            position: relative;
            .picture-verify {
                position: absolute;
                top: 1px;
                right: 1px;
            }
        }
    }
    .login-module .login-box {
        box-shadow: 0px 0px 1px #ccc;
        .el-input input {
            height: 40px;
            line-height: 40px;
        }
    }
</style>
