<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-03-07 15:27:36
 * @LastEditTime: 2022-03-25 15:09:32
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @Description: 获取、审批印章转交
 * @FilePath: /middle-platform-auth-front-mobile/src/views/sealApproval/index.vue
-->
<template>
    <div class="seal-approval" v-if="!loading">
        <CommonHeader :headerTitle="$t('sealApproval.requestSeal')" :needLogin="0" />
        <div class="seal-approval__content">
            <template v-if="pageType === 'apportionSeal'">
                <img class="apportion-seal" :src="sealImg(sealList[0])" alt="">
                <p class="seal-approval__remark apportion-remark">{{ transferTxt }}</p>
            </template>
            <template v-else>
                <p class="seal-approval__remark">{{ transferTxt }}</p>
                <ul>
                    <li class="seal-approval__seal" v-for="seal in sealList" :key="seal.sealId">
                        <img :src="sealImg(seal)" alt="">
                        {{ seal.name }}
                    </li>
                </ul>
            </template>
            <template v-if="privilegeType < 2">
                <div class="seal-approval__title">{{ $t('sealApproval.sealRight') }}</div>
                <div class="seal-approval__privilege">
                    <template v-if="isApproval && transferData.status === 0">
                        <van-radio-group v-model="privilegeType">
                            <van-radio :name="0">{{ $t('sealApproval.allEntContract') }}</van-radio>
                            <van-radio :name="1">{{ $t('sealApproval.partEntContract') }}</van-radio>
                        </van-radio-group>
                        <van-field
                            :disabled="privilegeType === 0"
                            class="privilege-ent"
                            v-model="privilegeEntTxt"
                            rows="1"
                            autosize
                            type="textarea"
                            :placeholder="$t('sealApproval.pleaseInputRight')"
                        />
                        <p class="transfer-hint">
                            <i class="el-icon-ssq-hetongxiangqing-chexiao1"></i>
                            {{ $t('sealApproval.successTransfer') }} {{ transferData.name }} {{ $t('sealApproval.getRight') }}
                        </p>
                    </template>
                    <template v-else>
                        <div class="seal-approval__privilege-item" v-if="privilegeType === 0">{{ $t('sealApproval.signAllEntContract') }}</div>
                        <div class="seal-approval__privilege-item" v-for="(ent, i) in privilegeEnt" :key="i">
                            {{ $t('sealApproval.sign') }} {{ ent.entName }} {{ $t('sealApproval.sendContract') }}
                        </div>
                    </template>
                </div>
            </template>
        </div>
        <div class="seal-approval__time">
            <div class="seal-approval__time-title">{{ $t('sealApproval.sealUseTime') }}</div>
            <div>{{ timeText }}</div>
        </div>
        <div class="seal-approval__status" v-if="transferData.status > 0">{{ $t('sealApproval.currentStatus') }} {{ statusText }}</div>

        <div class="seal-approval__btn" v-if="isApproval && transferData.status === 0">
            <van-button type="info" @click="handleTransfer(false)">
                {{ $t('sealApproval.takeBackSeal') }}
            </van-button>
            <van-button type="info" @click="handleTransfer(true)">
                {{ $t('sealApproval.agree') }}
            </van-button>
        </div>
        <CommonFooter></CommonFooter>
    </div>
</template>

<script>
import CommonHeader from 'components/register_header/RegisterHeader.vue';
import CommonFooter from 'components/register_footer/RegisterFooter.vue';
const TYPE_ENUM = {
    '10': 'apportionSeal',
    '11': 'approvalTransferSeal',
    '13': 'transferSeal',
};
export default {
    components: {
        CommonHeader,
        CommonFooter,
    },
    data() {
        return {
            recordId: this.$route.query.recordId,
            transferData: {
                type: this.$route.query.type,
            },
            sealList: [],
            privilegeType: 4,
            privilegeEnt: [],
            privilegeEntTxt: '',
            loading: true,
        };
    },
    computed: {
        statusText() {
            return {
                1: this.$t('sealApproval.hasAgree'),
                2: this.$t('sealApproval.hasReject'),
                3: this.$t('sealApproval.hasDone'),
            }[this.transferData.status];
        },
        pageType() {
            return TYPE_ENUM[this.transferData.type];
        },
        transferTxt() {
            // type apportionSeal(印章分配) approvalTransferSeal(印章转交审批) transferSeal(印章转交)
            const { operatorEmpName, entName, name } = this.transferData;
            console.log('wwww', operatorEmpName);
            return {
                apportionSeal: `${operatorEmpName}${this.$t('sealApproval.ask')}${entName}${this.$t('sealApproval.giveYou')}`,
                approvalTransferSeal: `${operatorEmpName}${this.$t('sealApproval.hopeAsk')}${entName}${this.$t('sealApproval.hopeGive')}${name}`,
                transferSeal: `${operatorEmpName}${this.$t('sealApproval.hopeAsk')}${entName}${this.$t('sealApproval.hopeGiveYou')}`,
            }[this.pageType];
        },
        timeText() {
            const { authorizationStartTime, authorizationEndTime } = this.transferData;
            if (authorizationStartTime && authorizationEndTime) {
                return `${this.formatter(authorizationStartTime)}-${this.formatter(authorizationEndTime)}`;
            }
            return this.$t('sealApproval.noSettingTime');
        },
        isApproval() {
            return this.pageType === 'approvalTransferSeal';
        },
    },
    methods: {
        formatter(timestamp) {
            const date = new Date(timestamp);
            var strDate = date.getFullYear() + '.';
            strDate += date.getMonth() + 1 + '.';
            strDate += date.getDate();
            return strDate;
        },
        sealImg(seal) {
            if (!seal) {
                return '';
            }
            const { sealId, fileId, entId } = seal;
            return `/ents/${sealId}/seal/${fileId}?entId=${entId}&access_token=${this.$cookie.get('access_token')}`;
        },
        getApprovalData() {
            return this.$http.get(`/ents/operate-seal/detail?recordId=${this.$route.query.recordId}`).then(res => {
                const { sealVOS, privilege } = res.data;
                this.transferData = res.data;
                this.sealList = sealVOS;
                if (privilege) {
                    const { contentList, type } = privilege;
                    this.privilegeType = type;
                    this.privilegeEnt = contentList;
                    this.privilegeEntTxt = this.handlePrivilegeEnt(contentList);
                }
            });
        },
        handlePrivilegeEnt(list) {
            return list.map(el => el.entName).join('\n');
        },
        handleTransfer(pass) {
            this.$http.post('/ents/operate-seal/transfer/approval', {
                recordId: this.recordId,
                pass, // 是否同意交接
                finalPrivilege: {
                    type: this.privilegeType,
                    contentList: this.handlePrivilegeEntList(),
                },
            }).then(() => {
                this.getApprovalData();
                this.$MessageToast({
                    message: this.$t('sealApproval.approvalSuccess'),
                    type: 'success',
                });
            });
        },
        handlePrivilegeEntList() {
            if (this.privilegeType === 0) {
                return [];
            }
            return this.privilegeEntTxt.split('\n').map(el => {
                return { entName: el };
            });
        },
        saveSeal() {
            this.$http.post('/ents/operate-seal/save', { recordId: this.recordId }).then(async res => {
                const entId = res.data.value;
                await this.switchEnt(entId);
                this.$MessageToast({
                    message: this.$t('sealApproval.getSealSuccess'),
                    type: 'success',
                });
            });
        },
        switchEnt(entId) {
            return this.$http.post('/authenticated/switch-ent', {
                entId,
                refreshToken: this.$cookie.get('refresh_token'),
            }).then(res => {
                this.$token.save(res.data.access_token, res.data.refresh_token);
            });
        },
    },
    async created() {
        if (!this.isApproval) {
            await this.saveSeal();
        }
        setTimeout(async() => {
            await this.getApprovalData();
            this.loading = false;
        }, 2000);
    },
};
</script>

<style lang="scss">
.seal-approval{
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100vh;
    background: #FFF;
    position: relative;
    font-size: 14px;
    .seal-approval__content {
        padding-bottom: 0;
        background-color: #f8f8f8;
        width: 90%;
        text-align: center;
    }
    .seal-approval__time {
        display: flex;
        justify-content: center;
        width: 90%;
        border-bottom: 1px solid #f8f8f8;
        padding: 10px 0;
        margin-bottom: 10px;
    }
    .apportion-seal{
        display: block;
        width: 220px;
        margin: 60px auto 40px;
    }
    &__remark{
        line-height: 40px;
        padding-left: 40px;
        &.apportion-remark {
            padding: 0 76px;
            margin-bottom: 76px;
        }
    }
    &__title{
        text-align: center;
        border-top: 16px solid #EEE;
    }
    &__seal{
        height: 100px;
        border-top: 1px solid #EEE;
        img{
            width: 60px;
            height: 60px;
            margin: 20px 30px 0;
            float: left;
        }
    }
    &__privilege{
        border-bottom: 1px solid #EEE;
        &-item{
            padding-left: 40px;
            border-top: 1px solid #EEE;
        }
        .van-radio{
            height: 100px;
            line-height: 100px;
            padding-left: 40px;
            border-top: 1px solid #EEE;
        }
        .privilege-ent{
            width: 600px;
            margin-left: 94px;
            border: 1px solid #EEE;
        }
        .transfer-hint{
            font-size: 28px;
            line-height: 40px;
            color: #999;
            background: #FFEBE6;
            padding: 20px 40px 20px 80px;
            position: relative;
            margin-top: 40px;
            i{
                position: absolute;
                left: 24px;
                color: #FF5500;
            }
        }
    }
    &__time{
        padding: 30px 40px;
        line-height: 60px;
        &-title{
            color: #999;
        }
    }
    &__content{
        padding-bottom: 170px;
    }
    &__btn{
        width: 100%;
        height: 170px;
        box-sizing: border-box;
        position: fixed;
        bottom: 0;
        padding: 45px 40px;
        background: #fff;
        .van-button{
            width: 320px;
            &+.van-button{
                margin-left: 22px;
            }
        }
    }
    &__status{
        text-align: center;
        color: #999;
    }
}
</style>
