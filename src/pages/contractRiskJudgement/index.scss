
$--color-white: #FFFFFF;
$--border-color-extra-light: #F8F8F8;
$--border-color-base: #CCCCCC;
$--background-color-base: #F6F6F6;
$--color-primary: #127FD2;
$--color-danger: #FF5500;
$--color-border: #EEEEEE;
$--color-info: #999999;
$--color-text-primary: #333333;
$--color-text-second: #999999;
$--color-border: #EEEEEE;

.contract-review{
    width: 100%;
    min-width: 1280px;
    *{
        box-sizing: border-box;
    }
	&__header{
		width: 100%;
		height: 63px;
		color: $--color-white;
		font-size: 16px;
		background: #002b45;
        display: flex;
        justify-content: space-between;
		.head-left {
            display: flex;
            div {
                height: 100%;
                line-height: 63px;
                text-align: center;
            }
            .icon {
                cursor: pointer;
                width: 62px;
                border-right: 1px solid #26475A;
            }
            .title {
                margin-left: 30px;
                color: $--color-white;
            }
        }
        .head-right {
            margin-right: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .btn {
                width: 30px;
                height: 30px;
                border-radius: 4px;
                margin-left: 10px;
                text-align: center;
                line-height: 30px;
                cursor: pointer;
                font-size: 30px;
                &:hover {
                    background: #004466;
                }
            }
        }
	}
    &__content, &__head{
        font-size: 14px;
        display: flex;
        height: calc(100vh - 118px - 35px);
        position: relative;
        &-box{
            width: calc(100% - 220px);
        }
        &-left, &-right{
            width: 50%;
            min-height: 100%;
            float: left;
            box-sizing: border-box;
            position: relative;
        }
        &-left{
            z-index: 2;
        }
    }
    &__head{
        height: 54px;
        box-shadow: 0 0 8px 0 #0000001a;
        &-left, &-right{
            line-height: 54px;
            padding-left: 20px;
            padding-right: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-right: 1px solid #EEEEEE;
            span {
                width: 0;
                flex: 1;
            }
            .el-button {
                height: 30px;
                line-height: 0;
                vertical-align: middle;
                i {
                    line-height: 0;
                }
            }
            .right-tab {
                span {
                    margin-right: 30px;
                    padding-bottom: 17px;
                    border-bottom: 2px solid #FFFFFF;
                    cursor: pointer;
                }
                .select-tab {
                    border-color: #0C8AEE;
                    .head-tab_title, .head-tab_info {
                        color: #0C8AEE;
                    }
                }
            }
        }
    }
    &__content{
        background-color: $--border-color-extra-light;
        &-left, &-right{
            height: calc(100vh - 118px - 35px);
            padding: 10px;
        }
        &-preview{
            &-box{
                position: relative;
            }
            img{
                width: 100%;
            }
            p{
                font-size: 12px;
                line-height: 20px;
                text-align: center;
                background: $--background-color-base;
            }
        }
    }
    &__upload{
        width: 100%;
        height: 100%;
        .el-upload, .el-upload-dragger {
            width: 100%;
            height: 100%;
            .el-upload-dragger {
                display: flex;
                justify-content: center;
                align-items: center;
                border: 3px dashed #DDDDDD;
                &:hover {
                    border-color: #0C8AEE;
                }
                .upload-icon {
                    padding: 0 60px;
                    text-align: center;
                    color: #999999;
                    i {
                        color: #DDDDDD;
                        font-size: 55px;
                    }
                    .tip1 {
                        font-size: 16px;
                        margin-top: 20px;
                        margin-bottom: 13px;
                    }
                    .tip2 {
                        color: #CCCCCC;
                        font-size: 12px;
                    }
                    .tip3 {
                        width: 120px;
                        height: 34px;
                        border: 1px solid #CCCCCC;
                        background-color: $--color-white;
                        line-height: 32px;
                        color: #666666;
                        margin: auto;
                        margin-top: 28px;
                        border-radius: 2px;
                    }
                    .tip4 {
                        position: relative;
                        border: 1px dashed $--border-color-base;
                        padding: 8px 26px 12px 84px;
                        margin-bottom: 10px;
                        font-size: 12px;
                        line-height: 20px;
                        text-align: left;
                        i {
                            position: absolute;
                            left: 30px;
                            top: calc(50% - 10px);
                            font-size: 20px;
                            color: $--border-color-base;
                        }
                    }
                }
                .upload-drag-info {
                    display: none;
                    color: #0C8AEE;
                    font-size: 16px;
                }
            }

            .is-dragover {
                border-color: #0C8AEE;
                .upload-drag-info {
                    display: block;
                }
                .upload-icon {
                    display: none;
                }
            }
        }
    }
    &__result{
        width: 240px;
        height: 100%;
        right: 0;
        top: 0;
        position: relative;
        background-color: $--color-white;
        .head-tab {
            margin-left: 20px;
            border-bottom: 2px solid $--color-white;
            padding-top: 12px;
            cursor: pointer;
            &_title {
                font-size: 14px;
                color: #333333;
            }
            &_info {
                font-size: 10px;
                color: #CCCCCC; 
            }
        }
        &-content {
            height: calc(100% - 60px);
            overflow: auto;
            margin-bottom: 12px;
            &-item{
                width: 100%;
                min-height: 50px;
                padding: 18px 0 10px 20px;
                font-size: 14px;
                cursor: pointer;
                position: relative;
                [dir="rtl"] & {
                    padding: 18px 20px 10px 0px;
                }
                &.active, &:hover{
                    color: $--color-primary;
                    background-color: #EFF6FF;
                }
                .risk-text {
                    display: none;
                    .risk-content {
                        padding: 10px 10px 0px 0;
                        font-size: 12px;
                        color: $--color-text-primary;
                    }
                    .risk-title {
                        display: flex;
                        justify-content: space-between;
                        .risk-icon {
                            color: $--color-primary;
                            i {
                                margin-right: 4px;
                            }
                        }
                    }
                }
                &.active {
                    .risk-text {
                        display: block;
                    }
                }
                &::after {
                    position: absolute;
                    display: block;
                    content: '';
                    width: 200px;
                    height: 1px;
                    background-color: $--color-border;
                    bottom: 0;
                }
                &.history {
                    height: 70px;
                    .title {
                        width: 200px;
                    }
                    .info {
                        display: flex;
                        justify-content: space-between;
                        align-items: baseline;
                        font-size: 12px;
                        color: $--color-info;
                        margin-top: 8px;
                        margin-right: 25px;
                        span {
                            &:last-of-type {
                                color: $--color-primary;
                            }
                        } 
                    }
                }
            }
            &-position {
                padding: 10px 15px;
                border-bottom: 1px solid $--color-border;
                color: $--color-text-second;
            }
        }
        .el-pagination {
            padding-left: 20px;
        }
    }
    &__difference{
        position: absolute;
        border: 1px solid $--color-danger;
        display: block !important;
        &::before{
            content: '';
            display: block;
            width: 100%;
            height: 100%;
            background: $--color-danger;
            opacity: .1;
        }
        &-line{
            position: fixed;
            border-top: 1px dotted $--color-danger;
            z-index: 999;
            // animation: blink 1s 2 steps(1);
            // @keyframes blink{
            //     50% {
            //         border-color: transparent;
            //     }
            // }
        }
    }
    &__download{
        padding: 0;
        line-height: 30px;
        width: 200px;
        margin-left: 20px;
        text-align: center;
    }
    .compare-loading {
        position: fixed;
        top: calc(50% - 70px);
        left: calc(50% - 105px);
        z-index: 99999;
        &_content {
            width: 210px;
            height: 140px;
            background-color: rgba($color: #000000, $alpha: 0.6);
            font-size: 16px;
            color: $--color-white;
            text-align: center;
            border-radius: 4px;
            img {
                width: 60px;
                height: 60px;
                margin-top: 25px;
            }
        }
    }
    .recognize-loading {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        z-index: 99999;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .flex {
        display: flex;
    }
    .ellipsis {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

.compare-calc-billing {
    .el-message-box__content {
        border-bottom: none;
    }
}
.contract-review-choose {
    .el-dialog--small {
        width: 400px;
    }
    .el-dialog__header {
        padding: 0;
        border-bottom: 1px solid $--color-border;
        .contract-review-choose_header {
            height: 50px;
            padding: 0 20px 0 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .title {
                font-size: 16px;
                color: $--color-text-primary;
            }
            .el-icon-close {
                cursor: pointer;
                font-size: 12px;
                color: $--color-text-second;
            }
        }
        .el-dialog__headerbtn {
            display: none;
        }
    }
    .el-dialog__body {
        padding: 26px 30px 30px;
    }
}  
