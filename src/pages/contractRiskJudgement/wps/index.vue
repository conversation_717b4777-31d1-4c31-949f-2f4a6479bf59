<template>
    <div class="risk-wps" ref="wpsContainer"></div>
</template>

<script>
import WebOfficeSDK from 'js/web-office-sdk-solution-v2.0.7.es.js';
import { WPS_APPID } from 'common_const/wpsAppid.js';
import { mapState } from 'vuex';
export default {
    data() {
        return {
            editor: null,
        };
    },
    computed: {
        ...mapState('hubble', ['multiVersionFileId']),
    },
    methods: {
        async initWpsEditor() {
            const _this = this;
            this.editor = WebOfficeSDK.init({
                officeType: WebOfficeSDK.OfficeType.Writer,
                appId: WPS_APPID,
                fileId: _this.multiVersionFileId,
                mode: 'simple',
                mount: _this.$refs.wpsContainer,
                isManual: true,
                isListenResize: true, // 默认跟随浏览器大小
                Cooperation: false,
                // mode: 'simple', //简洁模式 没有菜单
                token: _this.$cookie.get('access_token'), // 登录时的token
                commonOptions: {
                    isShowTopArea: false, // 隐藏顶部区域（头部和工具栏）
                    isShowHeader: false, // 隐藏头部区域
                    isBrowserViewFullscreen: false, // 是否在浏览器区域全屏true 不允许全屏
                    isIframeViewFullscreen: false, // 是否在 iframe 区域内全屏true 不允许全屏
                    acceptVisualViewportResizeEvent: false, // 控制 WebOffice 是否接受外部的 VisualViewport
                },
                wordOptions: {
                    isShowDocMap: false,
                    isBestScale: true,
                },
            });
            this.$store.state.hubble.riskWpsEditor = this.editor;
            await this.editor.ready();
            this.$store.state.hubble.wpsReady = true;
            await (this.editor.Application.ActiveDocument.TrackRevisions = true);
            // const revisions = this.editor.Application.ActiveDocument.Revisions;
            // const revisionData = await revisions.Json();
            // console.log(revisionData);
            const result = await this.editor.Application.ActiveDocument.ActiveWindow.View.Zoom.Percentage;
            result > 100 && (this.editor.Application.ActiveDocument.ActiveWindow.View.Zoom.Percentage = 100);
        },
        destroy() {
            if (this.editor) {
                this.editor.destroy();
            }
        },
    },
    beforeDestroy() {
        this.destroy();
    },
    mounted() {
        this.initWpsEditor();
    },
};
</script>

<style lang="scss">
.risk-wps{
    width: 100%;
    height: 100%;
}
</style>
