<template>
    <div class="pdf-document" :class="{ 'hide-shadow': !showShadowMode }" ref="pdfDocument" v-loading="loading">
        <div class="pdf-document-wrap" ref="pdfContainer"></div>
    </div>
</template>

<script>
import 'pdfview/build/pdf.js';
import workerSrc from 'pdfview/build/pdf.worker.js';
import 'pdfview/web/pdf_viewer';
import 'pdfview/web/pdf_viewer.css';
PDFJS.workerSrc = workerSrc;

const BASE_WIDTH = 600;

export default {
    props: {
        fileUrl: {
            type: String,
            default: '',
        },
        fragment: {
            type: Array,
            default: () => [],
        },
        type: {
            type: String,
            default: 'left',
        },
        showShadowMode: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            loading: false,
            DPR: 2,
        };
    },
    watch: {
        fragment: {
            deep: true,
            handler(v) {
                this.renderFragment(v);
            },
        },
    },
    methods: {
        init() {
            this.loading = true;
            this.$emit('changeLoading', true);
            this.loadFile().then((pdf) => {
                this.loading = false;
                this.$emit('changeLoading', false);
                const arr = [];
                for (let i = 1; i <= pdf.numPages; i++) {
                    arr.push(this.renderPDF(pdf, i));
                }
                Promise.all(arr)
                    .then(() => {
                        this.renderFragment(this.fragment);
                    });
            }).catch(() => {
                this.loading = false;
                this.$emit('changeLoading', false);
            });
        },
        loadFile() {
            return new Promise((resolve, reject) => {
                const _this = this;
                const xhr = new XMLHttpRequest();
                xhr.open('GET', this.fileUrl, true);
                xhr.responseType = 'blob';
                xhr.send(null);
                xhr.onload = function() {
                    if (xhr.status === 200) {
                        let filename = '';
                        const disposition = xhr.getResponseHeader('Content-Disposition');
                        if (disposition && disposition.indexOf('attachment') !== -1) {
                            const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                            let matches = [];
                            const filenameList = disposition.split(';');
                            if (filenameList.length === 3) {
                                matches = filenameRegex.exec(filenameList[2]);
                            } else {
                                matches = filenameRegex.exec(disposition);
                            }
                            if (matches != null && matches[1]) {
                                filename = matches[1].replace(/^[^'"]*['"]*(.*?)['"]*$/g, '$1');
                            }
                        }
                        _this.$emit('changeFileName', decodeURIComponent(filename));
                        // 使用 PDF.js 加载 PDF 文件并进行其他操作
                        PDFJS.getDocument(window.URL.createObjectURL(xhr.response)).then(function(pdf) {
                            resolve(pdf);
                        })
                            .catch(() => reject());
                    } else {
                        reject();
                        console.error('Failed to load PDF:', xhr.status);
                    }
                };
            });
        },
        async renderPDF(pdf, num) {
            // eslint-disable-next-line no-async-promise-executor
            return new Promise(async(resolve) => {
                const pageDiv = (() => {
                    const newPageDiv = document.createElement('div');
                    newPageDiv.setAttribute('id', `page-${num}`);
                    newPageDiv.setAttribute('class', 'document-page');
                    newPageDiv.setAttribute('style', 'position: relative');
                    this.$refs.pdfContainer.appendChild(newPageDiv);
                    const canvas = document.createElement('canvas');
                    newPageDiv.appendChild(canvas);
                    return newPageDiv;
                })();
                let scale = 1;
                const page = await pdf.getPage(num);
                const preViewport = page.getViewport(this.DPR);
                if (preViewport.viewBox[2] > BASE_WIDTH) {
                    scale = BASE_WIDTH / preViewport.viewBox[2];
                }
                const viewport = page.getViewport(scale * this.DPR);
                const canvas = pageDiv.querySelector('canvas');
                const context = canvas.getContext('2d');
                canvas.height = viewport.height;
                canvas.width = viewport.width;
                pageDiv.style.height = `${viewport.height / this.DPR}px`;
                pageDiv.style.width = `${viewport.width / this.DPR}px`;
                canvas.style.height = `${viewport.height / this.DPR}px`;
                canvas.style.width = `${viewport.width / this.DPR}px`;
                const renderContext = {
                    canvasContext: context,
                    viewport,
                };
                page.render(renderContext).then(() => {
                    return page.getTextContent();
                }).then((textContent) => {
                    const textLayerDiv = (() => {
                        const newDiv = document.createElement('div');
                        pageDiv.appendChild(newDiv);
                        return newDiv;
                    })();
                    const textLayerDivWidth = canvas.style.width;
                    const textLayerDivHeight = canvas.style.height;
                    textLayerDiv.setAttribute('class', 'textLayer');
                    textLayerDiv.setAttribute('style', `width:${textLayerDivWidth};height:${textLayerDivHeight}`);
                    if (textLayerDiv) {
                        textLayerDiv.innerHTML = '';
                        PDFJS.renderTextLayer({
                            textContent,
                            container: textLayerDiv,
                            viewport: page.getViewport(scale),
                        });
                    }
                    resolve();
                });
            });
        },
        renderFragment(list) {
            this.clearShadow();
            list.forEach((arr, num) => {
                arr.forEach((category) => {
                    category.forEach((item) => {
                        const wrap = this.$refs.pdfContainer.querySelector(`#page-${item.page}`);
                        const textLayer = wrap.querySelector('.textLayer');
                        const riskBgColor = 'rgba(255, 0, 0,0.1)';
                        const shadow = (() => {
                            const { width, height } = textLayer.style;
                            const div = document.createElement('div');
                            div.setAttribute('class', `document-shadow document-shadow-${num}`);
                            const top = parseFloat(height) * item.y;
                            div.setAttribute('style', `
                                position: absolute;
                                width: ${parseFloat(width) * item.width}px;
                                height: ${parseFloat(height) * item.height}px;
                                top: ${top}px;
                                left: ${parseFloat(width) * item.x}px;
                                background-color: ${riskBgColor};
                                z-index: 1;
                            `);
                            return div;
                        })();
                        wrap.appendChild(shadow);
                    });
                });
            });
            this.$nextTick(() => {
                if (!this.showShadowMode) {
                    this.showShadow(0);
                }
            });
        },
        clearShadow() {
            const elements = this.$refs.pdfContainer.querySelectorAll('.document-shadow');
            elements.forEach((item) => {
                item.parentNode.removeChild(item);
            });
        },
        showShadow(name) {
            const elements = this.$refs.pdfContainer.querySelectorAll('.document-shadow');
            elements.forEach((item) => {
                if (item.classList.contains(`document-shadow-${name}`)) {
                    item.classList.add('document-shadow-show');
                    const halfHeight = parseFloat(getComputedStyle(this.$refs.pdfDocument).height) / 4;
                    const dom = this.$refs.pdfContainer.querySelector(`.document-shadow-${name}`);
                    const top = dom.offsetTop + dom.parentElement.offsetTop - halfHeight;
                    this.$refs.pdfContainer.scrollTo(0, top);
                } else {
                    item.classList.remove('document-shadow-show');
                }
            });
        },
        reset() {
            const pages = this.$refs.pdfContainer.querySelectorAll('.document-page');
            pages.forEach((item) => {
                item.parentNode.removeChild(item);
            });
        },
        locateShadow(num) {
            const elements = this.$refs.pdfContainer.querySelectorAll(`.document-shadow-${num}`)[0];
            elements.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
            });
        },
    },
    mounted() {
        this.init();
    },
};
</script>

<style lang="scss">
.pdf-document {
    width: 100%;
    height: 100%;
    .document-page{
        margin: 0 auto;
    }
    &-wrap {
        width: 100%;
        height: 100%;
        overflow-y: auto;
        .textLayer {
            z-index: 2;
        }
        .document-shadow {
            display: block;
        }
        .document-shadow-show {
            display: block !important;
        }
    }
    &.hide-shadow {
        .document-shadow {
            display: none;
        }
    }
}
</style>
