<template>
    <el-upload
        class="contract-review__upload"
        :action="uploadUrl"
        :show-file-list="false"
        :http-request="uploadRequest"
        :on-success="handleSuccess"
        :on-error="handleFail"
        :before-upload="beforeUpload"
        v-loading="loading"
        drag
    >
        <div class="upload-icon">
            <i class="el-icon-ssq-shangchuanbendiwenjian"></i>
            <div class="tip1">{{ $t('contractCompare.extractUploadFile') }}</div>
            <div class="tip2">{{ $tc('contractCompare.reviewUpload', 3) }}</div>
            <div class="tip3">{{ $t('contractCompare.documentSelect') }}</div>
        </div>
        <div class="upload-drag-info">{{ $t('contractCompare.dragInfo') }}</div>
    </el-upload>
</template>

<script>
export default {
    props: {
        uploadUrl: {
            type: String,
            default: '',
        },
        type: {
            type: String,
            default: 'left',
        },
    },
    data() {
        return {
            loading: false,
        };
    },
    methods: {
        uploadRequest(opts) {
            const formData = new FormData();
            formData.append('file', opts.file);
            formData.append('fileName', opts.file.name);
            formData.append('paymentPlanType ', null);
            const url = `${this.uploadUrl}?paymentPlanType=&supportInteractionMode=edit`;
            this.$http.post(url, formData)
                .then(res => {
                    opts.onSuccess({ fileName: opts.file.name, data: res.data });
                })
                .catch(err => {
                    opts.onError(err);
                });
        },
        handleSuccess(res) {
            this.loading = false;
            this.$emit('uploadSuccess', res);
        },
        handleFail(err) {
            this.loading = false;
            if (err.status === 401) {
                Vue.$http.get('/users/head-info');
                return;
            }
            try {
                const index = err.message.indexOf('{');
                const errData = JSON.parse(err.message.slice(index));
                errData.message && this.$MessageToast.error(errData.message);
                this.$emit('uploadFail');
            } catch (error) {
                this.$MessageToast.error(err.message);
            }
        },
        beforeUpload(file) {
            const fileTypeArr = file.name.split('.');
            const fileType = fileTypeArr[fileTypeArr.length - 1];
            if (!['pdf', 'doc', 'dot', 'docx'].includes(fileType)) {
                this.$MessageToast.error(this.$t('contractCompare.uploadError'));
                return false;
            }
            this.loading = true;
        },
    },
};
</script>
