<!--enterprise/usercenter/handpainted 用户中心在用-->
<template>
    <HandPainted
        :parentGift="parentGift"
        :initialBusiness="initialBusiness"
        @write-done="onWriteDone"
    >
    </HandPainted>
</template>
<script>
import HandPainted from 'components/signature/handPainted/HandPainted.vue';
export default {
    components: {
        HandPainted,
    },
    data() {
        return {
            initialBusiness: 'sign',
            parentGift: {
                contractId: this.$route.query.contractId,
                labelId: this.$route.query.labelId,
                receiverId: this.$route.query.receiverId,
                handWritingRecognition: this.$route.query.handWritingRecognition === 'true',
                name: this.$route.query.name || '',
                showReplaceAllBtn: JSON.parse(this.$route.query.showReplaceAllBtn || false),
            },
        };
    },
    beforeRouteEnter(to, from, next) {
        // 在渲染该组件的对应路由被 confirm 前调用
        // 不！能！获取组件实例 `this`
        // 因为当守卫执行前，组件实例还没被创建
        if (to.path.includes(`/usercenter`)) {
            next(vm => {
                vm.initialBusiness = 'usercenter';
            });
        } else {
            /* 如果不是从手机签署页过来的话则按照正常的流程往下走就行了，不需要做特殊处理*/
            next(() => {
                // 通过 `vm` 访问组件实例
            });
        }
        //
    },
    beforeRouteLeave(to, from, next) {
        // 导航离开该组件的对应路由时调用
        // 可以访问组件实例 `this`
        document.body.style.backgroundColor = '';
        next();
    },
    methods: {
        onWriteDone() {
            /* handPainted.vue已经做了，为了代码的复用性把这里删了*/
            /* if (window.localStorage && window.localStorage.getItem('transitionHref')) {
                 let transitionHref = window.localStorage.getItem('transitionHref');
                 this.$router.push(transitionHref);
                 } else {
                 this.$router.go(-1);
                 }*/

        },
    },
};
</script>
<style lang="scss">

</style>
