<template>
    <div class="share-view-page">
        <SignHeader
            class="header"
            :rText="$t('shareView.signerMessage')"
            :title="contractTitle"
            :noRight="!remark"
            @to-next="handleShowMessage"
            @to-back="handleBack"
        >
        </SignHeader>
        <div class="main-content" v-loading.fullscreen="loading">
            <div class="doc-wrap">
                <div class="documents-content" id="view-documents">
                    <div class="site-doc-container">
                        <div class="site-doc-scale">
                            <i class="iconfont el-icon-ssq-fangdajing1 scale" @click="zoomBiggerOrSmaller('bigger')"></i>
                            <span></span>
                            <i class="iconfont el-icon-ssq-suoxiaojing scale" @click="zoomBiggerOrSmaller('smaller')"></i>
                        </div>
                        <div class="site-doc-title" v-if="docList.length">{{ docList.length > 1 ? docList[currentDocIndex].fileName : '' }}</div>
                    </div>
                    <div class="documents" ref="docContent" :style="wrapperStyle">
                        <component
                            ref="pdfContent"
                            :is="curDocRenderType"
                            :style="styleObject"
                            :contractTransformScale="contractTransformScale"
                            :scale="scale"
                            :isFromSignPage="true"
                            :currentPageIndex="currentPageIndex"
                            :doc="curDoc"
                            :isShowRidingSeal="isMayShowRidingSeal"
                            :isNoPermissionPage="false"
                            parentElId="view-documents"
                            v-if="curDoc.pdfurl"
                            @updateCurrentPageIndex="handleUpdatePageIndex"
                            @ready="handleReady"
                            @update-thumbnail="updateThumbnail"
                        >
                            <template slot-scope="index">
                                <Labels
                                    v-for="(mark, markIndex) in markList(index.data)"
                                    :key="mark.labelId"
                                    :page="{
                                        width: curDoc.page[index.data].width,
                                        height: curDoc.page[index.data].height,
                                    }"
                                    :initialMark="mark"
                                    :docList="docList"
                                    :docIndex="currentDocIndex"
                                    :pageIndex="mark.pageNumber-1"
                                    :markIndex="markIndex"
                                    :markList="markList(index.data)"
                                    :isHybridCloudContract="isHybridCloudContract"
                                    :scale="scale"
                                />
                                <!-- 1个文档只有1页，不展示骑缝章 -->
                                <RidingSeal
                                    v-if="getCurtentRidingSeal"
                                    :pageHeight="curDoc.page[index.data].height_init"
                                    :ridingSeal="getCurtentRidingSeal"
                                    :contractId="contractId"
                                    :scale="scale"
                                    :hideSwitch="true"
                                ></RidingSeal>

                                <template v-if="curDoc.page.length > 1">
                                    <!-- 合同装饰骑缝章-->
                                    <DecorateRidingSeal
                                        v-for="(decorateRidingSeal,ridingSealIndex) in decorateRidingSealVos"
                                        v-show="decorateRidingSeal.status!=='WAIT_FOR_COMPOSE' && isShowRidingSealByDocumentIds(decorateRidingSeal)"
                                        :key="decorateRidingSeal.labelId"
                                        @switch-riding-seal="() => $emit('switchRridingSeal', {labelId: decorateRidingSeal.labelId, signatureId: decorateRidingSeal.signatureId})"
                                        :labelId="decorateRidingSeal.labelId"
                                        :src="decorateRidingSeal.previewUrl"
                                        :y="decorateRidingSeal.y"
                                        :height="curDoc.page[index.data].height_init"
                                        :scale="scale"
                                        :isFirstRidingSeal="ridingSealIndex===0"
                                        :canShowSwitch="false"
                                        signType="shareView"
                                        :name="decorateRidingSeal.displayName || decorateRidingSeal.displayAccount || ''"
                                    />
                                </template>
                            </template>
                        </component>
                        <!-- 滚动到当前文档的最后一页才展示 -->
                        <div class="signing-bottom-btn" v-if="curDoc.hasParsed && (currentPageIndex>1 || currentPageIndex>=curDoc.page.length)">
                            <p class="next-doc-btn"
                                v-if="currentDocIndex != docList.length -1"
                                @click="handleUpdateDocIndex(currentDocIndex+1)"
                            >
                                {{ $t('sign.enterNextContract') }}
                            </p>
                        </div>
                    </div>
                </div>
                <div class="documents-info">
                    <div class="preview-header">
                        <!-- 非签署情况，不显示签约台 -->
                        <div class="contract-title">
                            <i class="iconfont el-icon-ssq-wenjian"></i>
                            {{ contractTitle }}
                        </div>
                    </div>
                    <div class="mini-documents">
                        <MiniDoc
                            v-if="docList.length"
                            :docList="docList"
                            :currentDocIndex="currentDocIndex"
                            :currentPageIndex="currentPageIndex"
                            @updateCurrentDocIndex="handleUpdateDocIndex"
                            @updateCurrentPageIndex="handleUpdatePageIndex"
                            :hiddenFileName="docList.length === 1"
                        ></MiniDoc>
                    </div>
                </div>
            </div>
        </div>
        <RegisterFooter class="footer"></RegisterFooter>
        <el-dialog
            :title="$t('shareView.signerMessage')"
            size="tiny"
            :before-close="() => showMessage = false"
            :visible.sync="showMessage"
            :close-on-click-modal="false"
            class="signer-message-dialog"
        >
            <div class="message-content">
                {{ remark }}
            </div>
            <div slot="footer">
                <el-button type="primary" @click="showMessage=false">{{ $t('sign.understand') }}</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import SignHeader from '../common/signHeader/SignHeader.vue';
import RegisterFooter from 'src/components/register_footer/RegisterFooter.vue';
import MiniDoc from 'src/pages/foundation/sign/field/fieldMiniDoc/FieldMiniDoc.vue';
import Labels from 'src/pages/foundation/sign/common/labels/LabelsDisplayOnly.vue';
import Pdf from 'components/pdf/Pdf.vue';
import Images from 'components/contractImgPreview/index.vue';
import DecorateRidingSeal from '../signing/decorateRidingSeal/index.vue';
import RidingSeal from '../signing/ridingSeal/index.vue';
import { isPC } from 'utils/device';
import { mobileRouteObj } from 'utils/routeVariable';
import { encodeSearchParams } from 'utils/getQueryString';
export default {
    name: 'ShareView',
    components: {
        SignHeader,
        RegisterFooter,
        MiniDoc,
        Labels,
        Pdf,
        Images,
        DecorateRidingSeal,
        RidingSeal,
    },
    data() {
        return {
            loading: false,
            showMessage: false,
            contractTitle: '',
            remark: '',
            shareId: this.$route.query.shareId || '',
            contractId: '',
            contractData: {},
            docList: [],
            receiverId: '',
            decorateRidingSealVos: [],
            ridingSealData: [],
            currentDocIndex: 0,
            currentPageIndex: 0,
            curDoc: {
                page: [],
            },
            scale: 1,
            contractTransformScale: 1, // 合同上方的放大缩小按钮缩放系数
        };
    },
    computed: {
        styleObject() {
            return {
                'margin-left': this.curDoc.hasParsed && this.isMayShowRidingSeal && !this.hasRidingSeal ? `${55 * this.scale}px` : 0,
                'transform': `scale(${this.contractTransformScale})`,
                'transform-origin': 'top center',
            };
        },
        hasRidingSeal() {
            return this.getCurtentRidingSeal || this.decorateRidingSealVos.length > 0;
        },
        curDocRenderType() {
            return this.isOfdContract || this.isHybridCloudContract ? 'Images' : 'Pdf';
        },
        // 是否是混合云合同
        isHybridCloudContract() {
            return this.contractData.systemType === 'HYBRID_CLOUD';
        },
        markList() {
            return pageIndex => {
                // 当前文档的markList
                if (!this.curDoc.page) {
                    return [];
                }
                return this.curDoc.page[pageIndex]?.marks || [];
            };
        },
        // 返回合同中所有非日期标签 数组
        labels() {
            const labels = [];
            this.docList.forEach(doc => {
                doc.page.forEach((page) => {
                    (page.marks || []).forEach(mark => {
                        if (mark && mark.type !== 'DATE') {
                            labels.push(mark);
                        }
                    });
                });
            });
            return labels;
        },
        // 根据在哪些文档上显示骑缝章字段 showInDocumentIds 决定是否显示合同装饰骑缝章
        isShowRidingSealByDocumentIds() {
            return (decorateRidingSeal) => {
                return (decorateRidingSeal.showInDocumentIds || []).includes(this.curDoc.documentId);
            };
        },
        docHeight() {
            return this.curDoc?.page.reduce((sum, page) => {
                sum += ((page.height || 0) + 20);
                return sum;
            }, 0);
        },
        wrapperStyle() {
            const bottomBtnHeight = 60; // 给文档下方按钮预留的空间
            return {
                height: `${this.docHeight * this.contractTransformScale + bottomBtnHeight}px`,
            };
        },
        // 是否是OFD格式文档合同
        isOfdContract() {
            return this.contractData.contractFileProcessFormat === 'OFD';
        },
        getCurtentRidingSeal() {
            const temp = this.ridingSealData.filter(item => item.documentId === this.curDoc.documentId);
            return temp && temp[0];
        },
        isMayShowRidingSeal() {
            const { curDoc, getCurtentRidingSeal } = this;
            return curDoc.page.length > 1 && (getCurtentRidingSeal || this.decorateRidingSealVos.length);
        },
        // 文档的最大宽度
        maxWidth() {
            // 切换文档时，需要重新计算文档的最大宽高度
            const docMaxWidth = this.curDoc.page.reduce((max, page) => Math.max(max, page.width_init || page.width || 0), 0);
            // return docMaxWidth
            // 除与合同重叠部分以外，骑缝章宽度/阴影背景部分宽度
            return docMaxWidth + (this.isMayShowRidingSeal ? 110 : 0);
        },
        // 合同最大缩放系数
        maxScale() {
            const docPaddingWidth = 140;
            const rightMiniDocWidth = 210;
            const screenWidth = window.innerWidth;
            return (screenWidth - docPaddingWidth - rightMiniDocWidth) / this.maxWidth;
        },
        // 合同最小缩放系数
        minScale() {
            return this.maxScale / 4;
        },
    },
    methods: {
        handleBack() {
            window.location.href = '/sign-flow/doc-manage/list';
        },
        handleShowMessage() {
            this.showMessage = true;
        },
        // 更新page的宽高信息
        handleReady(page) {
            this.$set(this.curDoc, 'page', page);
            this.$set(this.curDoc, 'hasParsed', true);
            this.updateScale();
        },
        updateThumbnail(thumbnail) {
            this.$set(this.curDoc, 'thumbnail', thumbnail);
            this.$set(this.docList, this.currentDocIndex, {
                ...this.curDoc,
                thumbnail,
            });
        },
        zoomBiggerOrSmaller(type) {
            if (type === 'bigger') {
                const docPaddingWidth = 140;
                const rightMiniDocWidth = 210;
                const docWidth = document.querySelector('.preview-pdf-container').getBoundingClientRect().width;
                const maxWidth = window.innerWidth - docPaddingWidth - rightMiniDocWidth;
                if (this.contractTransformScale + 0.1 <= this.maxScale && docWidth < maxWidth) {
                    this.contractTransformScale += 0.1;
                }
            } else {
                if (this.contractTransformScale - 0.1 >= this.minScale) {
                    this.contractTransformScale -= 0.1;
                }
            }
        },
        updateScale() {
            const { maxWidth, docWidth } = this;
            if (!maxWidth || !docWidth) {
                return this.scale =  1;
            }
            this.scale = docWidth / maxWidth;
        },

        handleUpdatePageIndex(currentPageIndex) {
            this.currentPageIndex = currentPageIndex;
        },
        handleUpdateDocIndex(currentDocIndex) {
            this.currentDocIndex = currentDocIndex;
            this.curDoc = this.docList[this.currentDocIndex];
            this.currentPageIndex = 1;
            this.updateScale();
        },
        formatUrl(path, query) {
            const join = path.includes('?') ? '&' : '?';
            if (!this.contractData.hybridServer) {
                return `${path}${join}access_token=${this.$cookie.get('access_token')}${query}`;
            }
            return `${this.contractData.hybridServer}${path}${join}access_token=${this.$cookie.get('access_token')}&shareId=${this.shareId}`;
        },
        /**
         * @param   {object} docList 合同文档信息
         * @return  {object} 返回经过处理的合同文档信息
         * @desc    $hybrid.getContractImg 对混合云合同图片路径重定向，normalMarks: 不需要填写内容的标签，writeLabels: 需要填写内容的标签
         */
        async initDocData(docList) {
            // const pageLength = 0;
            return await Promise.all(docList.map(async(doc) => {
                // 兼容混合云pdf文件预览
                doc.pdfurl = this.contractData.hybridServer
                    ? await this.$hybrid.getPdfPreviewUrl({ url: `${doc.fileStreamUrl}`, hybridServer: this.contractData.hybridServer, hybridTarget: '/contract/part/document/download', params: { contractId: this.contractId, documentIds: doc.documentId } })
                    : `${doc.fileStreamUrl}${doc.fileStreamUrl.includes('?') ? '&' : '?'}access_token=${this.$cookie.get('access_token')}&shareId=${this.shareId}`;
                return Object.assign({}, doc, {
                    page: doc.page.map((page) => {
                        // page标记，为了避免从指定位置页跳过来同一份合同存在的图片缓存问题
                        const imagePreviewUrl = this.formatUrl(page.imagePreviewUrl, `&page=signing&shareId=${this.shareId}`);
                        const highQualityPreviewUrl = this.formatUrl(page.highQualityPreviewUrl, `&page=signing&shareId=${this.shareId}`);
                        return Object.assign({}, page, {
                            imagePreviewUrl,
                            highQualityPreviewUrl,
                            marks: page.marks || [],
                            // normalMarks: this.filterNormalMarks(page.marks || []),
                            // writeLabels: this.filterWriteAbleLabels(page.marks || []),
                        });
                    }),
                });
            }));
        },
        initData() {
            this.loading = true;
            this.$http.get(`/contract-api/share-review/${this.shareId}`)
                .then(async({ data }) => {
                    if (data) {
                        const { receiverId, contract, remark, decorateRidingSeals, documents = [] } = data;
                        this.remark = remark;
                        if (remark) {
                            this.showMessage = true;
                        }
                        this.contractData = contract;
                        this.contractId = contract.contractId;
                        this.receiverId = receiverId;
                        this.decorateRidingSealVos = decorateRidingSeals;
                        this.docList = await this.initDocData(documents.filter(item => item.documentId));

                        const temp = documents.filter(doc => doc.ridingSealLabel);
                        if (temp && temp.length) {
                            // 已经设置了骑缝章
                            this.ridingSealData = temp.map(item => item.ridingSealLabel);
                        }
                        this.$nextTick(() => {
                            this.docWidth = this.$refs.docContent.getBoundingClientRect().width;
                            this.curDoc = this.docList[this.currentDocIndex];
                            this.updateScale();
                        });
                    }
                }).finally(() => {
                    this.loading = false;
                });
        },
    },
    created() {
        this.initData();
    },
    async beforeRouteEnter(to, from, next) {
        // 如果是移动端。自动跳转
        if (!isPC()) {
            location.href = `${window.location.origin}${mobileRouteObj['shareView']}?${encodeSearchParams(to.query)}`;
            return next(false);
        }
        next();
    },
};
</script>

<style lang="scss">
.share-view-page {
    .header  {
        position: fixed;
        top: 0;
        height: 50px;
        overflow: hidden;
    }
    .main-content {
        position: absolute;
        padding-top: 50px;
        padding-bottom: 35px;
        top: 0;
        bottom: 0;
        width: 100%;
        overflow-y: hidden;
        .doc-wrap {
            position: relative;
            height: 100%;
            background-color: #F3F4F6;
            .documents-content {
                position: absolute;
                top: 0;
                right: 210px;
                bottom: 0;
                left: 0;
                overflow: auto;
                overflow-x: hidden;
                padding: 0 70px 0;
                .site-doc-container {
                    width: 100%;
                    .site-doc-scale {
                        text-align: center;
                        .scale {
                            width: 30px;
                            height: 30px;
                            line-height: 30px;
                            font-size: 15px;
                            color: #666;
                            cursor: pointer;
                            &:first-child {
                                margin-right: 5px;
                            }
                            &:last-child {
                                margin-left: 5px;
                            }
                        }
                        span {
                            display: inline-block;
                            position: relative;
                            top: 3px;
                            height: 15px;
                            border-left: 1px solid #ddd;
                        }
                    }
                    .site-doc-title {
                        border: none;
                        font-size: 14px;
                        text-align: center;
                        margin: 3px 0;
                    }
                }
            }
            .documents-info {
                width: 210px;
                position: absolute;
                top: 40px;
                right: 0;
                bottom: 0;
                overflow: auto;
                background-color: #fff;
                border-left: 1px solid #ddd;
                .preview-header {
                    z-index: 99;
                    right: 0;
                    width: 210px;
                    line-height: 40px;
                    font-size: 14px;
                    font-weight: 700;
                    background-color: #fff;
                    color: #404040;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    border-left: 1px solid #ddd;
                    .contract-title {
                        box-sizing: border-box;
                        padding: 0 11px;
                        width: 210px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        border-bottom: 1px solid #ddd;
                        i {
                            font-size: 15px;
                            color: #666;
                            margin-right: 8px;
                        }
                    }
                }
                [dir="rtl"] & {
                    border-left: none;
                    border-right: 1px solid #ddd;
                    right: auto;
                    left: 0;
                    .preview-header {
                        right: auto;
                        left: 0;
                        border-right: 1px solid #ddd;
                        border-left: none;

                    }
                }
            }
        }
    }
    .footer {
        position: fixed;
        bottom: 0;
        box-sizing: border-box;
    }
}
.signer-message-dialog {
    .el-dialog__title {
        color: #333;
    }
}
</style>
