<template>
    <div class="Field-Site-cpn" v-if="receivers.length">
        <!-- 选择收件人菜单 -->
        <div class="site-bar-head" :class="{ ['lang_' + $t('lang')]: true }">
            <span class="order-num">{{ $t('field.step1') }}：</span>
            <span class="head-title">{{ $t('field.selectSigner') }}</span>
        </div>
        <div class="sigers">
            <ul>
                <el-tooltip class="item"
                    effect="dark"
                    v-for="(item, index) in receivers"
                    :key="item.userAccount"
                    :content="item.userName"
                    popper-class="FieldSite-cpn-popper"
                    placement="right"
                >
                    <li @click="switchReceiver(index)">
                        <span class="signers-color" :style="computeBgc(index)"></span>
                        <span class="signers-name etc-sigle">{{ item.userName }}</span>
                        <i class="signers-active el-icon-ssq-qianyuewancheng" v-show="index == receiverIndex"></i>
                    </li>
                </el-tooltip>
            </ul>
        </div>

        <!-- 字段 -->
        <div class="site-bar-head" :class="{ ['lang_' + $t('lang')]: true }">
            <span class="order-num">{{ $t('field.step2') }}：</span>
            <span class="head-title">{{ $t('field.dragSignaturePosition') }}</span>
        </div>
        <div class="site-bar-title">{{ $t('field.signingField') }}</div>
        <div class="site-bar-content">
            <ul>
                <li v-for="item in icons"
                    :key="item.class"
                    v-show="item.show"
                    @mousedown="onDragStart($event, item.type, item.class)"
                >
                    <i :class="item.class" :style="computeBgc(receiverIndex)"></i>
                    <span>{{ item.name }}</span>
                </li>
            </ul>
        </div>

        <!-- 合同装饰：水印和骑缝章 -->
        <template v-if="checkFeat.contractDecoration">
            <div class="site-bar-head" :class="{ ['lang_' + $t('lang')]: true }">
                <span class="order-num">{{ $t('field.decoration') }}</span>
                <span class="order-num-suffix">({{ $t('field.optional') }})</span>
            </div>
            <div class="site-bar-content">
                <ul>
                    <li v-for="item in decorationIcons"
                        :key="item.class"
                        v-show="item.show"
                        @click="addDecoration(item)"
                    >
                        <i :class="item.class" :style="computeBgc(receiverIndex)"></i>
                        <span>{{ item.name }}</span>
                    </li>
                </ul>
            </div>
        </template>
    </div>
</template>
<script>
import { calcRgbColorByIndex } from 'utils/colorInfo.js';
import Bus from 'components/bus/bus.js';
import { mapGetters } from 'vuex';
export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['receivers'],
    data() {
        return {
            receiverIndex: 0,
        };
    },
    computed: {
        ...mapGetters(['checkFeat']),
        icons() {
            const { signType, userType } = this.receivers[this.receiverIndex];
            return [
                {
                    class: 'el-icon-ssq-gongzhang',
                    type: 'SEAL',
                    name: this.$t('field.seal'),
                    show: ['SEAL_AND_SIGNATURE', 'SEAL'].includes(signType),
                },
                {
                    class: 'el-icon-ssq-bi',
                    type: 'SIGNATURE',
                    name: (signType === 'SIGNATURE' && userType === 'PERSON') ? this.$t('field.signature') : this.$t('field.senderSignature'),
                    show: signType !== 'SEAL',
                },
                {
                    class: 'el-icon-ssq-riqi',
                    type: 'DATE',
                    name: this.$t('field.signDate'),
                    show: true,
                },
            ];
        },
        decorationIcons() {
            const userType = this.receivers[this.receiverIndex].userType;
            return [
                {
                    class: 'el-icon-ssq-shuiyin', // 水印
                    type: 'WATERMARK',
                    name: this.$t('field.watermark'),
                    show: true,
                }, {
                    class: 'el-icon-ssq-qifengzhang', // 骑缝章
                    type: 'DECORATE_RIDING_SEAL',
                    name: this.$t('field.ridingStamp'),
                    show: userType === 'ENTERPRISE',
                },
            ];
        },

    },
    methods: {
        // utils
        computeBgc(i) {
            const color = calcRgbColorByIndex(i);
            return `background-color: rgba(${color}, 0.6);
                        border: 1px solid rgb(${color})`;
        },
        // 切换接受人
        switchReceiver(v) {
            this.receiverIndex = v;
            this.$emit('switch-receiver', {
                receiverIndex: v,
            });
        },
        onDragStart(e, type, className) {
            this.$emit('site-drag-start', e, type, className);
        },
        addDecoration({ type }) {
            Bus.$emit('addDecoration', type);
        },
    },
};
</script>
<style lang="scss">
    $site-bgclr: #c9e7ff;
    .Field-Site-cpn {
        .site-bar-head {
            height: 40px;
            line-height: 40px;
            font-size: 16px;
            color: #333;
            padding-left: 15px;
            border-top: 2px solid #127fd2;
            border-bottom: 1px solid #ddd;
            &:first-child {
                border-top: 0;
            }
            .order-num {
                font-size: 16px;
                color: #127fd2;
            }
            &.lang_ru, &.lang_en:nth-child(3) {
                box-sizing: border-box;
                font-size: 12px;
            }
            .order-num-suffix {
                float: right;
                color: #999;
                font-size: 12px;
                margin-right: 12px;
            }

        }
        .sigers {
            height: 143px;
            background-color: #f6f6f6;
            padding-top: 3px;
            overflow-y: auto;
            li {
                height: 30px;
                line-height: 30px;
                cursor: pointer;
                .signers-color {
                    float: left;
                    width: 14px;
                    height: 14px;
                    border-radius: 50%;
                    margin: 7px 7px 0 18px;
                }
                .signers-name {
                    display: inline-block;
                    width: 120px;
                }
                .signers-active {
                    float: right;
                    font-weight: bold;
                    margin-top: 9px;
                    margin-right: 14px;
                }
                &:hover {
                    background-color: #eee;
                }
            }
        }
        .site-bar-title {
            height: 38px;
            line-height: 38px;
            font-size: 14px;
            font-weight: bold;
            color: #333;
            padding-left: 22px;
            border-bottom: 1px solid $border-color;
        }
        .site-bar-content {
            background-color: #fafafa;
            padding-top: 10px;
            padding-bottom: 15px;
            border-bottom: 1px solid $border-color;
            li {
                display: block;
                height: 32px;
                line-height: 32px;
                &:hover {
                    background-color: #eee;
                    cursor: pointer;
                }
                i {
                    width: 24px;
                    height: 24px;
                    line-height: 24px;
                    text-align: center;
                    font-size: 16px;
                    background-color: $site-bgclr;
                    border-radius: 3px;
                    margin-top: 4px;
                    margin-left: 22px;
                    margin-right: 10px;
                }
            }
        }
    }
    .FieldSite-cpn-popper {
        &.is-dark {
            background: #333;
        }
    }
</style>
