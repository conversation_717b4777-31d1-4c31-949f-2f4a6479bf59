<!-- 普通发起-计费弹窗 -->
<template>
    <div class="ChargeVisible-cpn" v-show="!loading" v-loading="loadingDialog">

        <!-- 不限量套餐 https://jira.bestsign.tech/browse/SAAS-7647 -->
        <template v-if="unlimited">
            <div class="no-charge" style="padding: 50px 0;">
                {{ $t('field.unlimitedNotice') }}
            </div>
            <NoNotify v-if="normalFieldPage" ref="noNotify"></NoNotify>
            <ENNotify v-if="normalFieldPage" ref="enNotify"></ENNotify>
            <div class="ChargeVisible-btns">
                <div class="ChargeVisible-btns-container clear">
                    <div class="ChargeVisible-btn ssq-blue-btn" @click="handleClickConfirm">{{ $t('field.confirm') }}</div>
                    <div class="ChargeVisible-btn ssq-blue-btn" @click="handleClickCancel">{{ $t('field.cancel') }}</div>
                </div>
            </div>
        </template>

        <!-- 按照合同相关方的账号数计费 https://jira.bestsign.tech/browse/SAAS-8159 -->
        <template v-else-if="accountCharge !== null">
            <div class="no-charge account-tip">
                {{ `${$t('field.accountCharge.notice')}，${accountCharge ? $t('field.accountCharge.able') : $t('field.accountCharge.unable')}` }}
            </div>
            <NoNotify v-if="normalFieldPage && accountCharge" ref="noNotify"></NoNotify>
            <ENNotify v-if="normalFieldPage && accountCharge" ref="enNotify"></ENNotify>
            <div class="ChargeVisible-btns">
                <div class="ChargeVisible-btns-container clear">
                    <template v-if="accountCharge">
                        <div class="ChargeVisible-btn ssq-blue-btn" @click="handleClickConfirm">{{ $t('field.confirm') }}</div>
                        <div class="ChargeVisible-btn ssq-blue-btn" @click="handleClickCancel">{{ $t('field.cancel') }}</div>
                    </template>
                    <div v-else class="ChargeVisible-btn ssq-blue-btn account-btn" @click="handleClickCancel">{{ $t('field.confirm') }}</div>
                </div>
            </div>
        </template>

        <!-- 余额消费 -->
        <template v-else-if="chargingLineStatus=='ENOUGH' || chargingLineStatus=='TOB_ENOUGH' || (isSignerCharge)">
            <!-- 签署方付费 https://jira.bestsign.tech/browse/SAAS-5445 -->
            <div v-if="isSignerCharge" class="costCount costCount_pay clear">
                <p>该合同由您指定的签署方<template v-if="normalFieldPage">{{ contractPayerAccount.account }}<template v-if="contractPayerAccount.enterpriseName">（{{ contractPayerAccount.enterpriseName }}）</template></template>付费</p>
            </div>
            <!-- 发件方付费 -->
            <template v-else>
                <div v-if="chargingLineStatus=='TOB_ENOUGH'" class="deduct-public-notice">
                    {{ $t('field.deductPublicNotice') }}
                </div>
                <div class="costCount clear" @click="handleClickPrivate">
                    <div class="costCount-title fl" :class="{show: isTipsShow, ['lang_' + $t('lang')]: true }">{{ $t('field.charge') }}：</div>
                    <div class="costCount-content fl">
                        <!-- 不限量套餐，对私对公都是0，都显示 -->
                        <!-- 对私合同 -->
                        <div v-if="toCForUse || !toBForUse" class="private" :class="{show: isTipsShow, ['lang_' + $t('lang')]: true }"><span style="font-weight: bold">{{ $t('field.units', { num: toCForUse }) }}</span>  {{ $t('field.contractToPrivate') }}</div>
                        <!-- 对公合同 -->
                        <div v-if="toBForUse || !toCForUse" class="public" :class="{ ['lang_' + $t('lang')]: true }"><span style="font-weight: bold">{{ $t('field.units', { num: toBForUse }) }}</span>   {{ $t('field.contractToPublic') }}</div>
                    </div>
                    <div v-show="isTipsShow" class="cost-tips">
                        <div>● {{ $t('field.costTips.1') }}</div>
                        <div>● {{ $t('field.costTips.2') }}</div>
                        <div v-if="$route.query.contractId">● {{ $t('field.costTips.3') }}</div>
                        <div v-else>● {{ $t('field.costTips.4') }}</div>
                    </div>
                </div>
            </template>
            <NoNotify v-if="normalFieldPage" ref="noNotify"></NoNotify>
            <ENNotify v-if="normalFieldPage" ref="enNotify"></ENNotify>
            <div class="ChargeVisible-btns">
                <div class="ChargeVisible-btns-container clear">
                    <div class="ChargeVisible-btn ssq-blue-btn" @click="handleClickConfirm">{{ $t('field.confirm') }}</div>
                    <div class="ChargeVisible-btn ssq-blue-btn" @click="handleClickCancel">{{ $t('field.cancel') }}</div>
                </div>
            </div>
            <div v-if="!isEnt" class="cost-info">{{ $t('field.costInfo') }}</div>
        </template>

        <!-- 余额不足，引导充值 -->
        <template v-else>
            <div class="content iconfont el-icon-ssq-fenshubuzu">
            </div>
            <div class="no-charge">{{ text }}</div>
            <div class="ChargeVisible-btns">
                <div v-if="userRole != 'EMPLOYEE'" class="ChargeVisible-btns-container clear">
                    <div class="ChargeVisible-btn ssq-blue-btn" @click="handleClickCharge">{{ $t('field.toCharge') }}</div>
                    <div class="ChargeVisible-btn ssq-blue-btn" @click="handleClickCancel">{{ $t('field.cancel') }}</div>
                </div>
                <div v-else class="ssq-blue-btn" style="margin: 0 auto;" @click="handleClickCharge">{{ $t('field.confirm') }}</div>
            </div>
        </template>
    </div>
</template>
<script>
import NoNotify from './NoNotify';
import ENNotify from './ENNotify';
export default {
    components: {
        NoNotify,
        ENNotify,
    },
    props: {
        contractPayerAccount: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            contractId: this.$route.query.contractId,
            templateId: this.$route.query.templateId,
            templateMatchId: this.$route.query.templateMatchId,

            loading: false, // 解决弹窗闪烁问题
            // isDeductPublic: true,
            isTipsShow: false,

            // ajax
            chargingLineStatus: 'ENOUGH', // EXHAUST，TOB_ENOUGH，ENOUGH
            productType: 'TO_C', // TO_B，TO_C
            toBForUse: '',
            toCForUse: '',
            userRole: 'EMPLOYEE', // PERSON, ENT_ADMIN, EMPLOYEE
            unlimited: false,
            accountCharge: null, // 是否使用按账号计费，null: 不走按账号计费，true：账号数足够，false：账号数不够
            loadingDialog: false, // 防止重复点击
        };
    },
    computed: {
        text() {
            return (this.userRole !== 'EMPLOYEE') ? this.$t('field.contractNeedCharge.1') : this.$t('field.contractNeedCharge.2');
        },
        // 标识本弹窗当前所在的页面为普通发起，此外还在模板发送、动态模板预览页被引用
        normalFieldPage() {
            return this.$route.path.includes('sign/field');
        },
        // 签署方付费
        isSignerCharge() {
            return this.contractPayerAccount && (this.contractPayerAccount.account || this.contractPayerAccount.enterpriseName);
        },
        isEnt() {
            return this.$store.state.commonHeaderInfo.userType === 'Enterprise';
        },
    },
    methods: {
        handleClickPrivate() {
            this.isTipsShow = !this.isTipsShow;
        },

        handleClickCharge() {
            // todo...
            if (this.userRole === 'PERSON') {
                this.$router.push('/usercenter/recharge/selectpackage?pageFrom=send');
            } else if (this.userRole === 'ENT_ADMIN') {
                this.$router.push(`/console/enterprise/account/recharge/selectpackage?pageFrom=send&contractId=${this.contractId}`);
            } else if (this.userRole === 'EMPLOYEE') {
                this.handleClickCancel();
            }
        },
        async handleClickConfirm() {
            const passRes = await this.$hybrid.offlineTip();
            const parameter = {
                selectProductType: this.chargingLineStatus === 'TOB_ENOUGH' ? 'TO_B' : '',
            };
            if (!passRes) {
                return;
            }

            if (this.normalFieldPage) {
                parameter.notAllowNotify = this.$refs.noNotify.notAllowNotify;
                parameter.notifyInEnglish = this.$refs.enNotify.sendENNotify;
            }
            this.loadingDialog = true; // 按钮loading,防止重复点击
            this.$emit('SignCharge-confirm', parameter);
        },

        handleClickCancel() {
            this.$emit('SignCharge-cacel-dialog');
        },

        // ajax
        getCharging() {
            let url;
            const { operateMarkId } = this.$route.query;
            if (this.templateMatchId) {
                url = `${tempPath}/charging/templates/match/${this.templateMatchId}`;
            } else {
                // 模版重新发起既有合同id又有模版id,根据模版id判断收费
                url = this.templateId ? `${tempPath}/charging/templates/multiple-dynamic-signer/${this.templateId}${operateMarkId ? '?operateMarkId=' + operateMarkId : ''}` : `${signPath}/charging/contracts/${this.contractId}`;
            }
            return this.$http.get(url);
        },
    },
    created() {
        if (this.isSignerCharge) {
            // 由签署方付费
            return;
        }
        this.loading = true;
        this.getCharging()
            .then(res => {
                const data = res.data;
                this.chargingLineStatus = data.chargingLineStatus;
                this.productType = data.productType;
                this.toBForUse = data.toBForUse;
                this.toCForUse = data.toCForUse;
                this.userRole = data.userRole;
                this.unlimited = data.unlimited;
                this.accountCharge = data.accountCharge;
            }).catch(() => {})
            .finally(() => {
                this.loading = false;
            });
    },
};
</script>
<style lang="scss">
.ChargeVisible-cpn {
    .deduct-public-notice {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 68px;
        line-height: 76px;
        text-align: center;
        font-size: 14px;
        font-weight: bold;
        color: #333;
    }
    .costCount {
        padding-top: 68px;
        padding-bottom: 40px;
        &_pay {
            word-break: break-all;
            padding: 40px;
        }
        .costCount-title.show {
            &:after {
                top: 8px;
                right: -118px;
                @include solid-triangle(4px, #333, transparent, transparent, transparent);
            }
            &.lang_ru {
                &:after {
                    right: -193px;
                }
            }
        }
        .costCount-title {
            position: relative;
            padding-left: 110px;
            height: 20px;
            &:after {
                content: '';
                position: absolute;
                top: 6px;
                right: -120px;
                @include solid-triangle(4px, transparent, transparent, transparent, #333);
            }
            &.lang_ru {
                padding-left: 60px;
                &:after {
                    right: -195px;
                }
            }
        }
        .costCount-content {
            width: 200px;
            .private, .public {
                width: 125px;
                font-size: 14px;
                color: #333;
                cursor: pointer;
                &.lang_ru {
                    width: 225px;
                }
            }
            .private {
                position: relative;
                padding-bottom: 2px;
            }
        }
        .cost-tips {
            width: 335px;
            padding-top: 10px;
            border-top: 1px solid #ddd;
            margin: 0 auto;
            margin-top: 48px;
            div {
                padding-left: 6px;
                padding-bottom: 2px;
                font-size: 12px;
                color: #999;
            }
        }
    }
    .ChargeVisible-btns {
        height: 34px;
        // padding: 14px 0 14px 85px;
        padding-top: 14px;
        padding-bottom: 14px;
        background-color: #fff;
        border-top: 1px solid #ddd;
        .ChargeVisible-btns-container {
            width: 220px;
            margin: 0 auto;
            .ChargeVisible-btn {
                float: left;
                &:first-child {
                    margin-right: 20px;
                }
                &:not(.account-btn):last-child {
                    background-color: #f8f8f8;
                    color: #000;
                    border: 1px solid $border-color;
                    &:hover {
                        background-color: #fff;
                    }
                }
                &.account-btn {
                    margin-left: 60px;
                    background-color: #127fd2;
                    border-color: #127fd2;
                }
            }
        }
    }

    // 不足
    .content {
        width: 100%;
        height: 108px;
        line-height: 118px;
        font-size: 60px;
        text-align: center;
    }
    .no-charge {
        width: 100%;
        font-size: 14px;
        color: #333;
        text-align: center;
        padding-bottom: 25px;
        &.account-tip {
            width: auto;
            padding: 50px 0;
            margin: 0 20px;
        }
    }

    .notify-item{
        margin: 0 30px 10px;

        .el-checkbox__label{
            color: #666;
        }

        p{
            padding-left: 20px;
            font-size: 12px;
            color: #999;
        }
    }
    .cost-info{
        font-size: 12px;
        color: #999;
        text-align: center;
        margin-bottom: 15px;
        padding-left: 3px;
    }
}
</style>
