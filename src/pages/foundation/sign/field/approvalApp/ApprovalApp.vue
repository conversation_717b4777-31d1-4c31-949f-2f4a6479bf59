<!-- 业务组件：签署流程——指定位置——审批流应用 -->
<!-- todo: 点回来 -->
<template>
    <div class="ApprovalApp-cpn">
        <div class="chooseflow" v-if="step == 1 && definesData.length">
            <el-radio-group v-model="selectedFlowId">
                <el-radio class="radio" v-for="define in definesData" :key="define.defInfoId" :label="define.defInfoId">{{ define.name }}</el-radio>
            </el-radio-group>
            <!-- <el-radio class="radio" v-for="define in definesData" v-model="selectedFlowId" :label="define.defInfoId">{{ define.name }}</el-radio> -->
        </div>
        <div v-if="step == 2">
            <div class="flow-steps-title">{{ definesData.filter( item => item.defInfoId == selectedFlowId )[0].name }}</div>
            <div class="flow-steps-content clear">
                <div class="flow-steps-node fl" v-for="(node, nodeIndex) in flowInfos" :key="nodeIndex">
                    <div v-if="node.empName" class="flow-steps-node-info" :style="(nodeIndex+1)%5===0?'margin-right:0':''">
                        <div class="flow-steps-node-info-img">
                            <img v-if="node.portraitSrc" :src="node.portraitSrc" alt="" width="40" height="40">
                            <i v-else class="iconfont el-icon-ssq-xingming"></i>
                        </div>
                        <div class="flow-steps-node-info-name">
                            {{ node.empName || '' }}
                        </div>

                    </div>
                    <div v-if="!node.empName" class="choose-flow-node" :style="(nodeIndex+1)%5===0?'margin-right:0':''" @click="handleClickAdd(nodeIndex)">
                        <div class="flow-node-addBtn">+</div>
                        <!-- 下面这段要放在flow-steps-node下面 -->
                        <div :id="`member${nodeIndex}`" class="flow-node-list" style="display: none;">
                            <div class="flow-node-list-title">{{ $t('field.chooseApprover') }}</div>
                            <div class="flow-node-list-members">
                                <span v-for="(member, memberIndex) in node.defineApproverVOList" :key="memberIndex" class="flow-node-list-member" @click="handleClickName(nodeIndex, member)">{{ member.empName || '' }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="flow-steps-node-line" v-if="((nodeIndex+1)%5===0 || nodeIndex == flowInfos.length-1) ? false : true"></div>
                </div>
                <div
                    class="flow-steps-lines"
                    :key="index"
                    v-for="(line, index) in flowLinesNum"
                >
                    <div class="flow-steps-lines-top"></div>
                    <div class="flow-steps-lines-bottom"></div>
                </div>
            </div>
            <div
                class="private-message-wrap-outer"
            >
                <PrivateMessage
                    v-if="!disablePrivateMessage"
                    class="private-message-wrap"
                    :maxContentLength="300"
                    :privateLetter="flowInfos[0].privateLetter || ''"
                    :privateLetterFileList="flowInfos[0].privateLetterFileVOList || []"
                    @change="updatePrivateMessage"
                    :titleVisible="false"
                    :saveBtnVisible="true"
                    :type="1"
                ></PrivateMessage>
            </div>
        </div>
        <div class="clear">
            <button class="ssq-blue-btn" @click="handleClickToNextBtn">{{ step == 1 ? $t('field.nextStep') : $t('field.submitApproval') }}</button>
            <div v-if="step == 2" class="notice">{{ tip }}</div>
        </div>
    </div>
</template>
<script>
import PrivateMessage from 'src/pages/foundation/sign/field/approvalApp/PrivateMessage.vue';

export default {
    components: {
        PrivateMessage,
    },
    props: {
        definesData: {
            type: Array,
            default: () => [],
        },
        disablePrivateMessage: {
            type: Boolean,
            default() {
                return false;
            },
        },
        tip: {
            type: String,
            default() {
                return this.$t('field.autoSendAfterApproval');
            },
        },
    },
    data() {
        return {
            step: 1,
            flowInfos: [],
            selectedFlowId: this.definesData[0].defInfoId,
            privateMessageVisible: false,
            currentNodeIndex: null,
        };
    },
    computed: {
        flowLinesNum() {
            const left = this.flowInfos.length % 5;
            if (left === 0) {
                return this.flowInfos.length / 5 - 1;
            } else {
                return Math.floor(this.flowInfos.length / 5);
            }
        },
    },
    methods: {
        hideList() {
            const list = document.querySelectorAll('.flow-node-list');

            for (let i = 0; i < list.length; i++) {
                list[i].style.display = 'none';
            }

            // SAAS-237 ie 不支持 querySelectorAll 的 forEach 写法
            /* list.forEach( item => {
                    item.style.display = 'none';
                } ); */
        },
        handleClickToNextBtn() {
            switch (this.step) {
                case 1:
                    if (!this.selectedFlowId) {
                        this.$MessageToast.error(this.$t('field.chooseApprovalFlow'));
                        return;
                    }
                    this.getflowSteps()
                        .then(res => {
                            res.data.sort((a, b) => a.flowOrderNum > b.flowOrderNum);
                            this.flowInfos = res.data.map(item => {
                                return Object.assign({}, item, {
                                    portraitSrc: `/ents/${item.orgId}/employees/${item.empId}/portrait?access_token=${this.$cookie.get('access_token')}`,
                                });
                            });
                            this.step = 2;
                        });
                    break;
                case 2:
                    // eslint-disable-next-line
                        const reject = this.flowInfos.some(item => {
                        return item.empName == null;
                    });
                    if (reject) {
                        this.$MessageToast.error(this.$t('field.completeApprovalFlow'));
                        return;
                    }
                    this.$emit('apply-approval', this.flowInfos, this.selectedFlowId);
                    break;
            }
        },
        handleClickAdd(index) {
            this.hideList();
            document.querySelector(`#member${index}`).style.display = 'block';
        },
        handleClickName(nodeIndex, member) {
            const node = this.flowInfos[nodeIndex];
            node.empId = member.empId;
            node.empName = member.empName;
            node.userId = member.userId;
            node.portraitSrc = `/ents/${node.orgId}/employees/${node.empId}/portrait?access_token=${this.$cookie.get('access_token')}` || '';
            document.querySelector(`#member${nodeIndex}`).style.display = 'none';
        },

        // ajax
        getflowSteps() {
            return this.$http.get(`/ents/flow/infos/${this.selectedFlowId}/option/steps`);
        },
        // 更新子组件传过来的私信内容
        updatePrivateMessage({ privateLetter, privateLetterFileList }) {
            this.flowInfos.map((recipient) => {
                if (privateLetter === '' || privateLetter) {
                    recipient.privateLetter = privateLetter;
                }
                if (privateLetterFileList && privateLetterFileList.length) {
                    recipient.privateLetterFileList = privateLetterFileList.map(i => i.fileId);
                    recipient.privateLetterFileVOList = privateLetterFileList;
                }
                return recipient;
            });
            this.$forceUpdate();
        },
        // 点击添加私信按钮
        addPrivateMessage(nodeIndex) {
            // this.currentNodeIndex = nodeIndex;
            if (this.currentNodeIndex === nodeIndex) {
                this.privateMessageVisible = !this.privateMessageVisible;
            } else {
                this.currentNodeIndex = nodeIndex;
                this.privateMessageVisible = true;
            }
        },
        // 添加私信的文案
        addPrivateText(recipient) {
            // 有内容的前提下（节点不是当前节点 或者 私信内容隐藏了）
            if ((recipient.privateLetter || (recipient.privateLetteFileList || []).length > 0) && (recipient !== this.flowInfos[this.currentNodeIndex] || !this.privateMessageVisible)) {
                return this.$t('field.viewPrivateLetter');
            }
            return this.$t('field.addPrivateLetter');
        },
    },

    created() {
        // 如果只有1个合同类型，直接到第2步
        if (this.definesData.length === 1) {
            this.getflowSteps()
                .then(res => {
                    res.data.sort((a, b) => a.flowOrderNum > b.flowOrderNum);
                    this.flowInfos = res.data.map(item => {
                        return Object.assign({}, item, {
                            portraitSrc: `/ents/${item.orgId}/employees/${item.empId}/portrait?access_token=${this.$cookie.get('access_token')}`,
                        });
                    });
                    this.step = 2;
                });
        }
    },
};
</script>
<style lang="scss">
    .ApprovalApp-cpn {
        .chooseflow {
            .el-radio {
                display: block;
                margin-left: 0;
                margin-bottom: 20px;
            }
            .el-radio__label {
                font-size: 14px;
                color: #333;
                padding-left: 10px;
                [dir='rtl'] & {
                    padding-left: 0;
                    padding-right: 10px;
                }
            }
        }
        .flow-steps-title {
            font-size: 14px;
            color: #333;
            padding-bottom: 36px;
        }
        .flow-steps-content {
            // display: flex;
            // flex-wrap: wrap;
            // justify-content: center;
            width: 654px;
            .flow-steps-node {
                height: 80px;
                margin-bottom: 45px;
                .flow-steps-node-info {
                    text-align: center;
                    display: inline-block;
                    margin-right: 80px;
                    .flow-steps-node-info-img {
                        width: 40px;
                        padding: 0 10px;
                        border-radius: 2px;
                        img {
                            // font-size: 42px;
                            border-radius: 2px;
                        }
                    }
                    .flow-steps-node-info-name {
                        font-size: 12px;
                        color: #333;
                        text-align: center;
                    }
                }
                .choose-flow-node {
                    position: relative;
                    display: inline-block;
                    width: 36px;
                    height: 36px;
                    border: 2px solid #ccc;
                    margin: 2px 98px 18px 10px;
                    cursor: pointer;

                    .flow-node-addBtn {
                        font-size: 30px;
                        line-height: 30px;
                        text-align: center;
                        color: #2298f1;
                    }
                    .flow-node-list {
                        z-index: 999;
                        position: absolute;
                        top: 50px;
                        left: -76px;
                        width: 166px;
                        min-height: 59px;
                        padding: 12px 0 12px 16px;
                        background-color: #fff;
                        border: 1px solid #ccc;
                        box-shadow: #ccc 0px 0px 6px;
                        &:after {
                            content: '';
                            position: absolute;
                            top: -19px;
                            left: 84px;
                            @include solid-triangle(10px, transparent, transparent, #fff, transparent);
                        }
                        .flow-node-list-title {
                            font-size: 12px;
                            color: #ccc;
                            margin-bottom: 6px;
                        }
                        .flow-node-list-members {
                            display: flex;
                            flex-wrap: wrap;
                            .flow-node-list-member {
                                font-size: 12px;
                                color: #2298f1;
                                margin-right: 16px;
                                margin-bottom: 4px;
                            }
                        }
                    }
                }
                .flow-steps-node-line {
                    // display: inline-block;
                    position: relative;
                    width: 80px;
                    border-top: 1px dashed #999;
                    margin: -41px 8px 0 60px;
                    &:after {
                        position: absolute;
                        top: -5px;
                        right: -11px;
                        content: '';
                        @include solid-triangle(4px, transparent, transparent, transparent, #999);
                    }
                }
            }
            .flow-steps-lines {
                position: relative;
                top: 83px;
                left: 30px;
                width: 595px;
                height: 57px;
                // border: 1px solid red;
                margin-bottom: 58px;
                .flow-steps-lines-top {
                    height: 19px;
                    border-right: 1px dashed #999;
                    border-bottom: 1px dashed #999;
                }
                .flow-steps-lines-bottom {
                    height: 19px;
                    border-left: 1px dashed #999;
                    &:after {
                        position: absolute;
                        bottom: 8px;
                        left: -3px;
                        content: '';
                        @include solid-triangle(4px, #999, transparent, transparent, transparent);
                    }
                }
            }
        }
        .notice {
            float: right;
            height: 34px;
            line-height: 34px;
            font-size: 12px;
            color: #999;
            margin-right: 15px;
        }
        .add-private-message {
            cursor: pointer;
            color: #2298f1;
            width: 60px;
            text-align: center;
            position: absolute;
            margin: 8px 0;
        }
        .private-message-wrap {
            background-color: #f6f9fc;
            padding: 16px 22px;
            font-size: 12px;
        }
        .private-message-wrap-outer {
            float: left;
            width: 100%;
            margin-top: -40px;
            margin-bottom: 20px;
        }
    }

    .ssq-dialog.dialog-approval .el-dialog__body {
        background-color: #fff;
    }
</style>
