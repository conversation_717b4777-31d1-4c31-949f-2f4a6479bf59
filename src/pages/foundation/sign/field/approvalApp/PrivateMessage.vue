<template>
    <div class="private-message-component">
        <h3 class="title" v-if="titleVisible">
            <template v-if="$t('lang') === 'en'">
                {{ $t('field.append') }}{{ letterText }}{{ $t('addReceiver.give') }}<strong>{{ toUser || 'Ta' }}</strong>
            </template>
            <template v-else-if="$t('lang') === 'zh'">
                {{ $t('addReceiver.give') }}<strong>{{ toUser || 'Ta' }}</strong>{{ $t('field.append') }}{{ letterText }}
            </template>
            <template v-else-if="$t('lang') === 'ru'">
                {{ $t('addReceiver.give') }}<strong>{{ toUser || 'Ta' }}</strong>{{ $t('field.append') }}{{ letterText }}
            </template>
        </h3>
        <el-input
            type="textarea"
            v-model="content"
            class="message-content"
            :placeholder="`${$t('addReceiver.saySomething')}...`"
            :rows="4"
            :disabled="!isCanModify"
            :maxlength="maxContentLength"
        >
        </el-input>
        <!-- <div class="privateLetter-footer">{{ maxContentLength - (content || '').length }}</div> -->
        <div class="img-wrap">
            <div class="add-img" v-if="isCanModify">
                <el-upload
                    :action="uploadUrl"
                    :headers="uploadHeaders"
                    :before-upload="beforeUpload"
                    :on-success="onUploadSuccess"
                    :on-error="onUploadError"
                    :on-progress="onProgress"
                    :limit="maxFileLength"
                    multiple
                    accept="image/png, image/jpeg, image/jpeg, application/pdf, application/docx, application/msword, application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                    :show-file-list="false"
                >
                    <span class="add"><b class="el-icon-ssq-jia"></b> {{ $t('addReceiver.addImage') }}</span>
                    <span class="tip">{{ $t('addReceiver.addImageTips') }}</span>
                </el-upload>
            </div>
            <!-- 私信列表 -->
            <ul class="file-list">
                <li class="li-item" v-for="(item, index) in fileList" :key="index">
                    <b class="el-icon-ssq-wenjian1"></b>
                    <span v-if="item.fileId" :data-file-id="item.fileId" :data-file-url="item.previewUrl" @click="handlePreivew(item)">{{ item.fileName }}</span>
                    <el-progress v-else :percentage="item.percentage" :show-text="false"></el-progress>
                    <b class="el-icon-ssq-delete close" @click="removeFile(index)" v-if="isCanModify && item.fileId"></b>
                </li>
            </ul>
        </div>
        <!-- <el-button class="save-btn" v-if="saveBtnVisible">保存</el-button> -->
        <Gallery @close="previewImgInfo.visible = false" :visible="previewImgInfo.visible" :title="previewImgInfo.fileName" :src="previewImgInfo.previewUrl"></Gallery>
    </div>
</template>

<script>
import Gallery from 'components/gallery/Gallery.vue';
import { mapState } from 'vuex';
export default {
    components: {
        Gallery,
    },
    props: {
        titleVisible: {
            type: Boolean,
            default: false,
        },
        saveBtnVisible: {
            type: Boolean,
            default: false,
        },
        type: {
            type: Number,
            default: 0, // 0 签署人 1 审批人
        },
        toUser: {
            type: String,
            default: '',
        },
        maxContentLength: {
            type: Number,
            default: 255,
        },
        maxFileLength: {
            type: Number,
            default: 3,
        },
        privateLetter: {
            type: String,
            default: '',
        },
        privateLetterFileList: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        const query = this.$route.query;
        return {
            content: '',
            url: `/contract-api/file/private-letter/upload/${query.contractId || query.templateId}?type=`,
            uploadType: ['RECEIVER_PRIVATE_LETTER_FILE', 'APPROVER_PRIVATE_LETTER_FILE'], // 签署人，审批人
            uploadHeaders: { Authorization: `bearer ${this.$cookie.get('access_token')}` },
            fileList: [],
            fileLength: 0,
            previewImgInfo: {
                visible: false,
                fileName: '',
                previewUrl: '',
            },
            letterText: this.type === 1 ? this.$t('field.privateLetter') : this.$t('field.signNeedKnow'), // 审批的时候叫私信，签署的都叫签约须知
        };
    },
    computed: {
        uploadUrl() {
            return `${this.url}${this.uploadType[this.type || 0]}`;
        },
        ...mapState('template', ['canModifyWhenUsed']),
        // 是否可以修改私信内容
        isCanModify() {
            return !(this.$route.path === '/template/use/prepare' && !this.canModifyWhenUsed);
        },
    },
    watch: {
        privateLetter: {
            immediate: true,
            handler(newValue) {
                this.content = newValue;
            },
        },
        privateLetterFileList: {
            deep: true,
            immediate: true,
            handler(newValue) {
                this.fileList = newValue;
                this.fileLength = (this.fileList || []).length;
            },
        },
        content() {
            this.$emit('change', {
                privateLetter: this.content,
            });
        },
    },
    methods: {
        beforeUpload(file) {
            if (this.fileLength >= this.maxFileLength) {
                this.$MessageToast.error(this.$t('addReceiver.fileMax'));
                return false;
            }
            if (file.size / 1024 / 1024 > 5) {
                this.$MessageToast.error(this.$t('field.maximum5M'));
                return false;
            }
            this.fileLength++;
            // beforeUpload 存放数据，for 上传loading，在上传成功之前 以uid为标识符去判断文档的唯一性
            this.fileList.push({
                name: file.name,
                uid: file.uid,
                percentage: 0,
            });
        },
        onUploadSuccess(res, file) {
            if (!res) {
                this.$MessageToast.error(this.$t('field.uploadServerFailure'));
                const index = this.fileList.findIndex(f => f.uid === file.uid);
                this.removeFile(index);
                return;
            }
            (typeof this.fileList === 'undefined') && (this.fileList = []);
            this.fileList = this.fileList.map(f => {
                if (file.uid === f.uid) {
                    f.percentage = 100;
                    f = {
                        ...f,
                        ...res,
                    };
                }
                return f;
            });
            this.emitData();
        },
        onUploadError(err, file) {
            this.$MessageToast.error(this.$t('field.uploadFailure'));
            const index = this.fileList.findIndex(f => f.uid === file.uid);
            this.removeFile(index);
        },
        onProgress(event, file) {
            const index = this.fileList.findIndex(f => f.uid === file.uid);
            if (index === -1) {
                this.fileList.push({
                    name: file.name,
                    uid: file.uid,
                    percentage: 0,
                });
            } else {
                this.$set(this.fileList, index, {
                    ...this.fileList[index],
                    percentage: file.percentage,
                });
            }
        },
        removeFile(index) {
            this.fileList.splice(index, 1);
            this.fileLength--;
            this.emitData();
        },
        emitData() {
            this.$emit('change', {
                privateLetterFileList: this.fileList,
            });
        },
        // 预览私信
        handlePreivew(privateLetterFileItem) {
            this.previewImgInfo = {
                visible: true,
                fileName: privateLetterFileItem.fileName,
                previewUrl: privateLetterFileItem.previewUrls,
            };
        },
    },
};
</script>

<style lang="scss" scoped>
    .private-message-component {
        font-size: 12px;
        .title {
            color: #333;
        }
        .message-content {
            margin: 10px 0;
            box-sizing: border-box;
        }
        .privateLetter-footer {
            color: #999;
            padding: 0 0 10px;
        }
        .img-wrap {
            .add-img {
                margin-bottom: 10px;
                .add {
                    color: #2298f1;
                    display: inline-block;
                    padding: 5px 0;
                    cursor: pointer;
                    b {
                        font-weight: bold;
                    }
                }
                .tip {
                    color: #999;
                }
            }
            .file-list {
                width: 305px;
                .li-item {
                    width: 365px;
                    margin-bottom: 10px;
                    position: relative;
                    display: flex;
                    align-items: center;
                    .close {
                        color: #ccc;
                        margin-left: 16px;
                        cursor: pointer;
                        position: absolute;
                        right: 0;
                        &:hover {
                            color: #999;
                        }
                    }
                    span {
                        display: inline-block;
                        width: 300px;
                        overflow : hidden;
                        text-overflow: ellipsis;
                        margin-left:  4px;
                        word-break: break-all;
                        cursor: pointer;
                        &:hover {
                            color: #127FD2;
                        }
                    }
                    .el-progress {
                        margin-left:  4px;
                        width: 240px;
                    }
                    .el-progress-bar__inner {
                        background: #127FD2;
                    }
                }
            }
        }
        .save-btn {
            margin-top: 5px;
            float: none;
        }
    }
</style>
