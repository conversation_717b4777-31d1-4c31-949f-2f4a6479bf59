<template>
    <div class="FieldMiniDoc-cpn">
        <!-- 缩略图区 -->
        <div class="title">{{ $t('field.docTitle') }}</div>
        <div class="doc-edit-view">
            <ul>
                <li class="doc"
                    v-for="(doc, docIndex) in docList"
                    :key="doc.documentId"
                >
                    <div class="doc-title" v-if="!hiddenFileName">{{ doc.fileName }}</div>
                    <div class="doc-thumbnail-container"
                        :class="{ active: docIndex === currentDocIndex }"
                        @click="OnClick(docIndex)"
                    >
                        <div v-if="doc.thumbnail"
                            class="doc-thumbnail"
                            :style="{ backgroundImage: `url(${doc.thumbnail})`}"
                        >
                        </div>
                    </div>
                    <div class="doc-totalPage" :class="{ ['lang_' + $t('lang')]: true }">
                        <el-input
                            v-model="pageArray[docIndex]"
                            :placeholder="$t('field.pager')"
                            @blur="OnBlur(docIndex)"
                            @keyup.enter.native="OnBlur(docIndex)"
                            :disabled="isReading || currentDocIndex !== docIndex"
                        >
                        </el-input>
                        / {{ doc.pageSize }}
                    </div>
                </li>
            </ul>
        </div>
    </div>
</template>
<script>
export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['docList', 'currentDocIndex', 'currentPageIndex', 'hiddenFileName', 'isReading'],
    data() {
        // 保存每个文档的页码
        const pageArray = this.docList.map(() => 0);
        return {
            pageArray,
        };
    },
    watch: {
        currentDocIndex: { // 切换文档时
            handler(newValue) {
                this.pageArray.forEach((item, index) => {
                    const temp = index === newValue ? 1 : '';
                    this.$set(this.pageArray, index, temp);
                });
            },
            immediate: true,
        },
        currentPageIndex: {
            handler(newValue) {
                this.$set(this.pageArray, this.currentDocIndex, newValue);
            },
            immediate: true,
        },
    },
    methods: {
        // 切换页码
        OnBlur(docIndex) {
            // 限制阅读时，不允许切换
            this.$emit('updateCurrentPageIndex', +this.pageArray[docIndex]);
        },
        // 切换文档
        OnClick(docIndex) {
            // 限制阅读时，不允许切换
            if (docIndex === this.currentDocIndex || this.isReading) {
                return;
            }
            this.$emit('updateCurrentDocIndex', +docIndex);
        },
    },
};
</script>
<style lang="scss">
    .FieldMiniDoc-cpn {
        border-left: 1px solid $border-color;
        // 缩略图区
        .title {
            height: 40px;
            line-height: 40px;
            font-size: 14px;
            font-weight: bold;
            padding-left: 20px;
            padding-right: 15px;
            span {
                float: right;
                font-size: 12px;
                color: #3B86D4;
                &:hover {
                    cursor: pointer;
                }
            }
        }
        .doc-edit-view {
            height: calc(100% - 42px);
            overflow-y: auto;
            border-top: 1px solid $border-color;
            .doc {
                border-top: 1px solid $border-color;
                &:first-child {
                    border-top: 0 none;
                }
            }
            .doc-title {
                position: relative;
                line-height: 18px;
                color: #333;
                cursor: pointer;
                padding: 6px 15px;
                word-break: break-all;
            }
            .doc-thumbnail-container {
                margin: 5px auto;
                width: 132px;
                height: 161px;
                background-image: url(~img/doc.png);
                background-position: top left;
                background-repeat: no-repeat;
                background-size: 132px 161px;
                cursor: pointer;
                .doc-thumbnail {
                    width: 115px;
                    height: 144px;
                    background-repeat: no-repeat;
                    background-size: 115px 144px;
                    background-position: 4px 4px;
                }
                &.active {
                    background-image: url(~img/doc-active.png);
                }
            }
            .doc-totalPage {
                margin-bottom: 10px;
                line-height: 30px;
                color: #999;
                text-align: center;
                .el-input {
                    width: 52px;
                    .el-input__inner {
                        box-sizing: border-box;
                        height: 24px;
                        border-radius: 2px;
                        padding: 3px 5px;
                    }
                }
                &.lang_ru {
                    line-height: 10px;
                }
            }
        }
    }
</style>
