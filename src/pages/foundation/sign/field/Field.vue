<!-- 指定位置页重构 -->
<template>
    <div class="FieldNew-page">
        <PageContainer :title="$t('field.fieldTitle', {length: docList.length})" :rText="$t('field.send')" @to-back="toBack" @to-next="toNext">
            <div class="FieldBody-cpn" v-loading.fullscreen.lock="hybridContractServerConnected ? loading : 0">
                <!-- 签约方和字段区域 -->
                <div class="site-content clear">
                    <!-- 选择签约方 & 字段仓库 -->
                    <FieldSite
                        class="FieldSite fl"
                        :receivers="receivers"
                        @switch-receiver="onSwitchReceiver"
                        @site-drag-start="onDragStart"
                    >
                    </FieldSite>
                    <div class="FieldDoc">
                        <div class="site-doc-title" v-if="docList.length">{{ docList[currentDocIndex].fileName }}</div>
                        <!-- 合同内容 -->
                        <FieldDoc v-if="docList.length"
                            :contractId="contractId"
                            :docList="docList"
                            :receivers="receivers"
                            :receiverIndex="receiverIndex"
                            :dragStatus="dragStatus"
                            :floatingType="floatingType"
                            :float="float"
                            :edit="edit"
                            :watermarkList="decorateInfo.watermarkList"
                            :ridingSealList="decorateInfo.ridingSealList"
                            @mark-focus="onMarkFocus"
                            @mark-blur="onMarkBlur"
                            @updateFloatIcon="updateFloatIcon"
                            :currentDocIndex="currentDocIndex"
                            :currentPageIndex="currentPageIndex"
                            @updateCurrentDocIndex="handleUpdateDocIndex"
                            @updateCurrentPageIndex="handleUpdatePageIndex"
                            @to-next="toNext"
                        >
                        </FieldDoc>
                    </div>
                    <!-- 缩略图区 -->
                    <FieldMiniDoc
                        class="FieldMiniDoc"
                        v-if="docList.length"
                        :docList="docList"
                        :currentDocIndex="currentDocIndex"
                        :currentPageIndex="currentPageIndex"
                        @updateCurrentDocIndex="handleUpdateDocIndex"
                        @updateCurrentPageIndex="handleUpdatePageIndex"
                    >
                    </FieldMiniDoc>
                    <!-- 编辑区 -->
                    <FieldEdit
                        class="FieldEdit"
                        :receivers="receivers"
                        :mark="edit.mark"
                        @update-mark="onUpdateMark"
                        @delete-mark="onDeleteMark"
                    >
                    </FieldEdit>
                </div>
                <!-- 鼠标跟随icon -->
                <div
                    id="flying-icon"
                    class="flying-icon"
                    v-show="isFloatIconShow"
                    :class="floatingClass"
                >
                </div>
            </div>
        </PageContainer>

        <!-- 审批流dialog -->
        <el-dialog
            class="ssq-dialog dialog-approval"
            :title="$t('field.contractDispatchApply')"
            :visible.sync="dialogApproval"
            @open="ApprovalAppKey = Math.random()"
        >
            <ApprovalApp
                :key="ApprovalAppKey"
                :definesData="definesData"
                @apply-approval="onApplyApproval"
            >
            </ApprovalApp>
        </el-dialog>

        <!-- 计费dialog -->
        <el-dialog
            v-if="dialogCharge"
            class="ssq-dialog dialog-charge"
            :visible.sync="dialogCharge"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
        >
            <!-- 计费dialog -->
            <SignCharge
                :contractPayerAccount="contractPayerAccount"
                @SignCharge-confirm="onChargeConfirm"
                @SignCharge-cacel-dialog="hideDialogCharge"
            >
            </SignCharge>
        </el-dialog>

        <!-- 缺少签署位置dialog -->
        <FieldTip
            :tipList="tipList"
            :show.sync="dialogTip"
            @confirm="getDefines"
        ></FieldTip>
    </div>
</template>

<script>
import PageContainer from './pageContainer/PageContainer.vue';
import FieldSite from './fieldSite/FieldSite.vue';
import FieldDoc from './fieldDoc/FieldDoc.vue';
import FieldMiniDoc from './fieldMiniDoc/FieldMiniDoc.vue';
import FieldEdit from './fieldEdit/FieldEdit.vue';
import { initDecorateInfo } from 'src/common/utils/decorate.js';
import { editMark, deleteMark } from './common/mark/mark.js';
import ApprovalApp from './approvalApp/ApprovalApp.vue';
import SignCharge from './signCharge/SignCharge.vue';
import FieldTip from './fieldTip/FieldTip.vue';
import { goReturnUrl } from 'src/common/utils/returnUrl.js';
import { textMarkInfo } from 'src/pages/foundation/sign/common/info/info.js';
import { fieldLanBreak } from 'utils/hybrid/hybridBusiness.js';
import { mapGetters } from 'vuex';

export default {
    components: {
        ApprovalApp,
        SignCharge,
        FieldTip,
        PageContainer,
        FieldSite,
        FieldDoc,
        FieldMiniDoc,
        FieldEdit,
        // FieldSelect,
    },
    data() {
        return {
            // 合同信息
            contractId: this.$route.query.contractId,
            returnUrl: ['null', ''].includes(this.$route.query.returnUrl) ? null : this.$route.query.returnUrl,
            hybridContractServerConnected: true,
            signOrdered: false,

            // 审批流
            dialogApproval: false,
            ApprovalAppKey: 0,
            definesData: [],
            commitFlow: [],
            defInfoId: '',

            // 计费
            dialogCharge: false,
            contractPayerAccount: {},
            dialogTip: false,
            tipList: [],

            loading: true,
            receiverIndex: 0,
            // flyingIcon
            isFloatIconShow: false,
            floatingType: '',
            floatingClass: '',
            dragStatus: 'end',
            float: {
                fontSize: 14,
            },
            // edit
            edit: {
                mark: null,
            },

            // ajax
            receivers: [],
            docList: [],
            decorateInfo: [],
            currentDocIndex: 0, // 当前第几份文档
            currentPageIndex: 1, // 第几份文档的第几页
            enterTime: 0,
            contractName: '',
        };
    },
    computed: {
        // 混合云host
        hybridServer() {
            return this.$store.state.commonHeaderInfo.hybridServer;
        },
        hybridAccessToken() {
            return this.$store.state.commonHeaderInfo.hybridAccessToken;
        },
        ...mapGetters(['getUserType']),
    },
    watch: {
        dialogCharge(val) {
            if (val) {
                this.sensorsFn('Ent_ContractSendDetailWindow_PopUp', {
                    window_name: '发送弹窗',
                });
            }
        },
    },
    methods: {
        sensorsFn(eventName, params = {}) {
            if (this.getUserType === 'Person') {
                this.$sensors.track({
                    eventName,
                    eventProperty: {
                        page_name: '本地发送指定签署位置',
                        ...params,
                    },
                });
            }
        },
        // 稍后签署
        signLater() {
            this.$confirm(`${this.$t('field.contractNeedYouSign')}，${this.$t('field.ifSignRightNow')}？`, this.$t('personalAuth.info'), {
                confirmButtonText: this.$t('field.signRightNow'),
                cancelButtonText: this.$t('field.signLater'),
                type: 'info',
                closeOnClickModal: false,
                customClass: 'sign-later-message-box',
            }).then(() => {
                this.$router.push(`/sign/signing?contractId=${this.contractId}`);
            }).catch(() => {
                this.$router.push('/doc/list');
            });
        },
        toBack() {
            this.sensorsFn('Ent_ContractSendDetail_BtnClick', {
                first_category: '顶部导航栏',
                icon_name: '返回',
            });
            // add sso query param for saas-11171
            this.$router.push(`/sign/prepare?contractId=${this.contractId}${this.$route.query.fromSSO === 'true' ? '&isSSOBack=true' : ''}`);
        },
        toNext() {
            this.sensorsFn('Ent_ContractSendDetail_BtnClick', {
                first_category: '顶部导航栏',
                icon_name: '发送',
            });
            const errorList = [];
            // 先判断下是否有签署人没有印章和签名
            this.receivers.forEach((receiver) => {
                const { receiverId, userName, userType, signType } = receiver;
                const fileList = [];
                this.docList.forEach((doc, docIndex) => {
                    const { fileName, marks } = doc;
                    // 文档中是否已经包含该receiver的签名和印章
                    let hasMarked = this.isMarked(marks, receiverId, ['SEAL', 'SIGNATURE']);
                    let hasPartedMarked = false; // 完成了盖章或签字
                    if (signType === 'SEAL_AND_SIGNATURE') {
                        const hasMarkedSeal = this.isMarked(marks, receiverId, ['SEAL']);
                        const hasMarkedSignature = this.isMarked(marks, receiverId, ['SIGNATURE']);
                        hasMarked = hasMarkedSeal && hasMarkedSignature; // 拖了盖章和签字
                        hasPartedMarked = (hasMarkedSeal || hasMarkedSignature); // 拖了章
                    }
                    if (!hasMarked) {
                        fileList.push({
                            fileName,
                            docIndex,
                            hasPartedMarked,
                        });
                    }
                });
                if (fileList.length) {
                    errorList.push({
                        receiverId,
                        userName,
                        fileList,
                        userType,
                        signType,
                    });
                }
            });
            if (!errorList.length) {
                this.getDefines(); // 审批流
            } else {
                const isNone = errorList.some(error => {
                    if ((error.fileList || []).length !== this.docList.length) {
                        return false;
                    }
                    // 如果是签字并盖章，要判断是否是签字和盖章都没有添加
                    return error.signType !== 'SEAL_AND_SIGNATURE' || error.fileList.every(file => !file.hasPartedMarked);
                });
                if (isNone) {
                    return this.$MessageToast.error(this.$t('field.signaturePositionErr'));
                }
                const hasPartedMarked = errorList.some(error => {
                    if (error.signType === 'SEAL_AND_SIGNATURE') {
                        return error.fileList.filter(file => file.hasPartedMarked).length;
                    }
                    return false;
                });
                if (hasPartedMarked) {
                    return this.$MessageToast.error(this.$t('field.partedMarkedError'));
                } else {
                    this.dialogTip = true;
                    this.tipList = errorList;
                }
            }
        },
        // 审批流
        // 接收到应用审批流消息，弹出计费弹窗
        onApplyApproval(commitFlow, defInfoId) {
            this.commitFlow = commitFlow;
            this.defInfoId = defInfoId;
            this.dialogApproval = false;
            this.dialogCharge = true;
        },
        getDefines() {
            this.$loading();
            // 审批流
            this.$http.get(`${signPath}/contracts/${this.contractId}/send-defines`)
                .then(res => {
                    this.definesData = res.data;
                    // 触发审批流程
                    if (this.definesData.length) {
                        this.dialogApproval = true;
                    } else { // 不触发审批流程，弹出计费弹窗
                        this.dialogCharge = true;
                    }
                })
                .catch(() => {})
                .finally(() => {
                    this.$loading().close();
                });
        },
        hideDialogCharge() {
            this.dialogCharge = false;
            this.sensorsFn('Ent_ContractSendDetailWindow_BtnClick', {
                window_name: '发送弹窗',
                icon_name: '取消',
            });
        },
        // 计费
        onChargeConfirm(signChargeRes) {
            this.sensorsFn('Ent_ContractSendDetailWindow_BtnClick', {
                window_name: '发送弹窗',
                icon_name: '确定',
            });
            const commonHeaderInfo = this.$store.state.commonHeaderInfo;
            const senderUserType = commonHeaderInfo.userType;
            const selfUserId = commonHeaderInfo.platformUser.userId;
            const currentEntId = commonHeaderInfo.currentEntId;
            const hasMySelf = this.receivers.some(item => filterHandler(item));
            const selfRouteOrder = this.receivers.findIndex(item => filterHandler(item));
            const marks = this.docList.reduce((total, item) => {
                return total.concat(item.marks);
            }, []);
            function filterHandler(item) {
                if (senderUserType === 'Enterprise' && item.userType === 'ENTERPRISE') {
                    return item.userId === selfUserId && item.enterpriseId === currentEntId;
                } else if (senderUserType === 'Person' && item.userType === 'PERSON') {
                    return item.userId === selfUserId;
                } else {
                    return false;
                }
            }

            this.loading = true;

            this.postSend({
                defInfoId: this.defInfoId, // todo...
                flowStepList: this.commitFlow,
                labels: marks,
                selectProductType: signChargeRes.selectProductType,
                sendPlatform: 'WEB',
                notAllowNotify: signChargeRes.notAllowNotify,
                notifyInEnglish: signChargeRes.notifyInEnglish,
            }, { noToast: 1 })
                .then(() => {
                    this.sensorsFn('Ent_ContractSend_Result', {
                        first_category: '顶部导航栏',
                        contract_id: this.contractId,
                        contract_name: this.contractName,
                        icon_name: '发送',
                        is_success: true,
                    });
                    this.receivers.forEach(item => {
                        this.sensorsFn('Ent_ContractSendSuccessList_Result', {
                            first_category: '顶部导航栏',
                            contract_id: this.contractId,
                            contract_name: this.contractName,
                            company_id: item.enterpriseId,
                            company_name: item.userType === 'ENTERPRISE' ? item.enterpriseName : '个人签署人',
                            signatory_role: item.roleName,
                            icon_name: '发送',
                        });
                    });
                    this.dialogCharge = false;
                    this.$MessageToast.success(this.$t('field.sendSucceed'))
                        .then(() => {
                            this.loading = false;
                            /* 如果客户定义了跳转地址，则首先跳转 */
                            if (this.returnUrl) {
                                return goReturnUrl(this.returnUrl);
                            }
                            // 自己签 & !(顺序签的非第一个)
                            if (!this.definesData.length && hasMySelf && !(this.signOrdered && selfRouteOrder !== 0)) {
                                this.signLater();
                            } else {
                                this.$router.push('/doc/list'); // 发件箱
                            }
                        });
                })
                .catch(err => {
                    this.sensorsFn('Ent_ContractSend_Result', {
                        first_category: '顶部导航栏',
                        contract_id: this.contractId,
                        contract_name: this.contractName,
                        icon_name: '发送',
                        is_success: false,
                        request_url: err.config.url,
                        fail_reason: err.response?.data?.message || err.message,
                        fail_error_code: err.response?.data?.code,
                        fail_http_code: err.response?.status,
                    });
                    const msg = err.response.data.message;
                    this.loading = false;
                    this.dialogCharge = false;
                    this.$alert(msg, {
                        confirmButtonText: this.$t('field.confirm'),
                        customClass: 'field-error-alert',
                    });
                });
        },
        // ajax
        getContractInfo() {
            return this.$http.get(`${signPath}/contracts/${this.contractId}?type=send`);
        },
        // 发送
        postSend(data = {}, opts = {}) {
            // 混3 支持水印 需要调用客户端接口
            if (this.$hybrid.isGamma()) {
                return this.$hybrid.makeRequest({
                    hybridTarget: '/contracts/send',
                    method: 'post',
                    data: {
                        ...data,
                        contractId: this.contractId,
                    },
                });
            }
            return this.$http.post(`${signPath}/contracts/${this.contractId}/send/?time=${new Date().getTime()}`, data, opts);
        },
        // 是否为不在企业内网中的混合云用户
        notInLan() {
            return this.$store.getters.getHybridUserType === 'hybrid' && !this.$store.getters.getInLAN;
        },
        // 每次切换文档时，滚动到顶部，当前页设置为第一页
        handleUpdateDocIndex(currentDocIndex) {
            this.currentDocIndex = currentDocIndex;
            this.currentPageIndex = 1;
        },
        handleUpdatePageIndex(currentPageIndex) {
            this.currentPageIndex = currentPageIndex;
        },
        followCursorWithIcon(e) {
            document.querySelector('#flying-icon').style.left = `${e.clientX + 1}px`;
            document.querySelector('#flying-icon').style.top = `${e.clientY + 1}px`;
        },
        onSwitchReceiver(res) {
            this.receiverIndex = res.receiverIndex;
        },
        onDragStart(e, type, className) {
            const { signType, userType } = this.receivers[this.receiverIndex].userType;
            const mapObject =  {
                'SEAL': '盖章',
                'SIGNATURE': (signType === 'SIGNATURE' && userType === 'PERSON') ? '签字' : '盖章人签字',
                'CONFIRMATION_REQUEST_SEAL': '',
                'DATE': '签署日期',
            };
            this.sensorsFn('Ent_ContractSendDetail_BtnClick', {
                first_category: '合同框内容',
                icon_name: mapObject[type],
            });
            setTimeout(() => {
                this.dragStatus = 'started';
                this.followCursorWithIcon(e);
                this.isFloatIconShow = true;
                this.floatingType = type;
                this.floatingClass = className;
                if (type === 'DATE') {
                    this.float.fontSize = 14;
                    this.float.type = type;
                }
            }, 150);
        },
        onDragMove(event) {
            this.updateFloatIcon(this.isFloatIconShow, event);
        },
        updateFloatIcon(isFloatIconShow, event) {
            if (this.dragStatus !== 'started') {
                return;
            }
            this.isFloatIconShow = isFloatIconShow;
            if (isFloatIconShow) {
                this.followCursorWithIcon(event);
            }
        },
        onDragEnd() {
            this.dragStatus = 'end';
            this.isFloatIconShow = false;
            this.floatingClass = '';
        },
        onMarkFocus(res) {
            this.edit.mark = res.mark;
        },
        onMarkBlur() {
            this.edit.mark = null;
        },
        // 修改签署方或者日期修改字体大小
        onUpdateMark(mark) {
            const { labelId, documentId, pageNumber, receiverId } = mark;
            const markList = this.docList[this.currentDocIndex].marks;
            const fontSize = mark.fontSize || (mark.style || {}).fontSize;
            const param = {
                labelId,
                style: {
                    fontSize,
                },
                documentId,
                receiverId,
                pageNumber, // 限制最大页码
                x: mark.x,
                y: mark.y,
                width: mark.width,
                height: mark.height,
            };
            // DATE类型需要根据字体大小重新计算宽高度
            if (mark.type === 'DATE') {
                const page = this.docList[this.currentDocIndex].page[mark.pageNumber - 1];
                const { width, height } = textMarkInfo(mark);
                param.width = Math.round(width) / page.width;
                param.height = Math.round(height) / page.height;
            }
            editMark(this.contractId, markList, param);
        },
        async onDeleteMark(mark) {
            const markList = this.docList[this.currentDocIndex].marks;
            await deleteMark(this.contractId, markList, mark);
            this.onMarkBlur();
        },
        getRecivers() {
            // isOnlyNeedSigner不要抄送人，displayRuleName按规则格式化接收人名称
            return this.$http.get(`${signPath}/contracts/${this.contractId}/receivers?isOnlyNeedSigner=1&displayRuleName=true`);
        },
        getDocs() {
            // showLabels 取标签信息
            return this.$http.get(`${signPath}/contracts/${this.contractId}/documents?showLabels=1`);
        },
        // 初始化 marks[签名、标签字段] , marks包含 上上签查验二维码【文档右下角固定显示】
        async initDocData(resData) {
            return await Promise.all(resData.map(async(doc) => {
                // 文档缩略图默认为空
                doc.thumbnail = '';
                // 兼容混合云文件预览
                doc.pdfurl = await this.$hybrid.getPdfPreviewUrl({ url: doc.fileStreamUrl, hybridServer: this.hybridServer, hybridTarget: '/contract/part/document/download', params: { contractId: this.contractId, documentIds: doc.documentId }, hybridAccessToken: this.hybridAccessToken });
                doc.marks = (doc.page || []).reduce((total, page, pageIndex) => {
                    // 对每一页的mark坐标进行转换
                    const markList = (page.marks || []).map(mark => {
                        // 老的坐标系，不再兼容，提示用户重新上传文档
                        if (mark.width > 1 || mark.height > 1 || mark.x > 1 || mark.y > 1) {
                            return this.$MessageToast.error({
                                message: '该合同不支持重新发起，您可以重新上传文档发起',
                                callBack: () => this.$router.push('/doc/list'),
                            });
                        }
                        return {
                            ...mark,
                            pageNumber: mark.pageNumber || pageIndex + 1,
                            documentId: doc.documentId,
                        };
                    });
                    return total.concat(markList);
                }, []);
                return doc;
            }));
        },
        isMarked(marks, receiverId, types) {
            return (marks || []).filter(mark => types.includes(mark.type))
                .some(mark => receiverId === mark.receiverId);
        },
    },
    beforeMount() {
        document.addEventListener('mousemove', this.onDragMove);
        document.addEventListener('mouseup', this.onDragEnd);
    },
    beforeDestroy() {
        document.removeEventListener('mousemove', this.onDragMove);
        document.removeEventListener('mouseup', this.onDragEnd);
        this.sensorsFn('Ent_ContractSendDetail_PageLeave', {
            $event_duration: (new Date().getTime() - this.enterTime) / 1000,
        });
    },
    async created() {
        this.loading = true;
        // 判断混合云网络状态
        fieldLanBreak(() => {
            this.loading = false; this.hybridContractServerConnected = false;
        });
        // 获取所有文件和收件人信息并展示
        Promise.all([
            this.getRecivers(),
            this.getDocs(),
            this.getContractInfo(), // 获取合同信息
        ]).then(async([{ data: receiverData }, { data: docData }, { data: contractData }]) => {
            this.receivers = receiverData || [];
            this.docList = await this.initDocData(docData);
            this.decorateInfo = initDecorateInfo(receiverData, this.docList[0].page[0].height);
            this.signOrdered = contractData.signOrdered;
            this.contractPayerAccount = contractData.contractPayerAccount || {};
            this.contractName = contractData.contractTitle;
        }).catch(() => {
            this.$MessageToast.error(this.$t('field.sysError'))
                .then(() => {
                    this.$router.push('/doc/todo');
                });
        }).finally(() => {
            this.loading = false;
        });
    },
    mounted() {
        this.enterTime = new Date().getTime();
        this.sensorsFn('Ent_ContractSendDetail_PageView');
    },
};
</script>

<style lang="scss">
    .FieldNew-page {
        user-select: none;
        position: relative;
        height: 100vh;
        font-size: 12px;
        color: #333;
        background-color: #f6f6f6;

        // 审批流弹窗
        .dialog-approval {
            .el-dialog--small {
                width: auto;
                min-width: 409px;
            }
        }
        // 计费弹窗
        .dialog-charge {
            .el-dialog--small {
                width: 400px;
            }
            .el-dialog__header {
                display: none;
            }
            .el-dialog__body {
                padding: 0;
            }
        }
    }
    .field-error-alert {
        p {
            white-space: pre-wrap;
            word-wrap: break-word;
        }
    }
    .dialog-footer {
        &.lang_ru {
            button {
                width: 148px !important;
                & + button {
                    margin-left: 5px !important;
                }
            }
        }
    }

    // 原有的field-body
    .FieldBody-cpn {
        position: absolute;
        top: 50px;
        bottom: 35px;
        width: 100%;
        overflow: hidden;

        .site-content {
            height: 100%;
            .FieldSite {
                position: relative;
                width: 210px;
                height: 100%;
                border-right: 1px solid $border-color;
                overflow-y: auto;
            }
            .FieldDoc {
                height: 100%;
                overflow: auto;
                margin-left: 211px;
                margin-right: 211px;
                .site-doc-title {
                    width: calc(100% - 211px - 211px);
                    position: fixed;
                    z-index: 100;
                    top: 50px;
                    text-align: center;
                    border-bottom: 1px solid $border-color;
                    height: 40px;
                    line-height: 40px;
                    font-size: 14px;
                    background-color: #f6f6f6;
                }

            }
            .FieldMiniDoc {
                position: absolute;
                top: 0;
                right: 0;
                width: 210px;
                height: 100%;
            }
            .FieldEdit {
                position: absolute;
                top: 0px;
                right: 0;
                width: 211px;
                height: 100%;
                z-index: 101;
            }
        }

        .flying-icon {
            z-index: 9999;
            pointer-events: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 28px;
            height: 28px;
            line-height: 27px;
            text-align: center;
            font-size: 18px;
            background-color: #fff;
            border-radius: 4px;
            box-shadow:1px 1px 13px #ccc, -1px 1px 13px #ccc;
        }
    }
    .sign-later-message-box {
        .el-message-box__status {
            color: #999;
        }
    }
</style>
