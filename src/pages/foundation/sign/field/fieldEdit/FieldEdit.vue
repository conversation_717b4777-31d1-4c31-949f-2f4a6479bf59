<template>
    <transition name="slider">
        <div class="FieldEdit-cpn" v-show="mark">
            <div class="type">
                {{ editTypeName }}
            </div>
            <!-- 更改签约方下拉框 -->
            <div
                class="receiver"
                v-if="mark && mark.type !== 'DATE'"
            >
                <div class="title">
                    {{ $t('field.receiver') }}
                </div>
                <span
                    class="signers-opt-circle"
                    :style="computeStyle(currentReceiverId)"
                >
                </span>
                <el-select
                    class="signers-opt"
                    size="small"
                    v-model="currentReceiverId"
                    @change="changeSigner"
                    popper-class="sign-el-select FieldEdit-signers-selct"
                >
                    <el-option
                        v-for="item in receivers"
                        :disabled="mark.type == 'SEAL' && item.userType == 'PERSON' || mark.type == 'SIGNATURE' && item.userType == 'ENTERPRISE'"
                        :key="item.receiverId"
                        :label="item.userName"
                        :value="item.receiverId"
                    >
                        <span
                            class="signers-name"
                            :style="computeStyle(item.receiverId)"
                        >
                        </span>
                        <span class="signers-cirle">
                            {{ item.userName }}
                        </span>
                    </el-option>
                </el-select>
            </div>
            <!-- 签署日期修改字号 -->
            <el-form
                class="localSend-dateLabel-form"
                v-else
                :model="formData"
            >
                <el-form-item
                    label="名称"
                    class="form-name"
                >
                    <el-input
                        v-model="editTypeName"
                        :disabled="true"
                    >
                    </el-input>
                </el-form-item>
                <el-form-item
                    label="字号"
                >
                    <el-select
                        popper-class="cus-field-editor-select"
                        v-model="formData.fontSize"
                        placeholder="请选择字号"
                        :disabled="fontSizeSelectDisable"
                        @change="onChange"
                    >
                        <el-option
                            v-for="(item, index) in fontSizeRange"
                            :key="index"
                            :label="item.label"
                            :value="convertPtToPx(item.pt, dpi)"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <!-- 删除按钮 -->
            <div class="delete-container">
                <div class="delete"
                    @click="onClick"
                >
                    {{ $t('field.delete') }}
                </div>
            </div>
        </div>
    </transition>
</template>
<script>
import { mapState } from 'vuex';
import { calcRgbColor } from 'utils/colorInfo.js';
import { markInfo } from 'src/pages/foundation/sign/common/info/info.js';
import { convertPtToPx, fontSizeRange } from 'utils/fontSize.js';

export default {
    props: {
        receivers: {
            type: Array,
            default: function() {
                return [];
            },
        },
        mark: {
            type: Object,
            default: function() {
                return {
                    style: {
                        fontSize: 14,
                    },
                };
            },
        },
    },
    data() {
        return {
            formData: {
                fontSize: 14,
            },
            dpi: 96, // 目前写死
            convertPtToPx: convertPtToPx,
            fontSizeRange: fontSizeRange,
            currentReceiverId: '',
        };
    },
    computed: {
        ...mapState({
            hybridServer: state => state.commonHeaderInfo.hybridServer,
        }),
        // 编辑区
        editTypeName() {
            return this.mark ? markInfo(this.mark.type).name : '';
        },
        fontSizeSelectDisable() {
            // 混合云用户禁止修改字号
            return !!this.hybridServer;
        },
    },
    watch: {
        'mark.style': {
            handler(val) {
                if (val && val.fontSize) {
                    this.formData.fontSize = val.fontSize;
                }
            },
            deep: true,
            immediate: true,
        },
        'mark.receiverId': {
            handler(val) {
                this.currentReceiverId = val;
            },
            immediate: true,
        },
    },
    methods: {
        computeStyle(receiverId) {
            const color = calcRgbColor(this.receivers, 'receiverId', receiverId);
            return `background-color: rgba(${color}, 0.6);
                        border: 1px solid rgb(${color})`;
        },
        changeSigner(receiverId) {
            if (this.mark.receiverId === receiverId) {
                return;
            } // 不加此判断，会导致拖动A标签后切换到不同接收人的B标签，导致接口重复请求
            this.$emit('update-mark', {
                ...this.mark,
                receiverId,
            });
        },
        onClick() {
            this.$emit('delete-mark', this.mark);
        },
        onChange() {
            if (this.mark.style.fontSize === this.formData.fontSize) {
                return;
            }
            this.$emit('update-mark', {
                ...this.mark,
                ...this.formData,
            });
        },
    },
};
</script>
<style lang="scss">
    $site-bgclr: #c9e7ff;
    // 编辑区
    .FieldEdit-cpn {
        background-color: #f6f6f6;
        border-left: 1px solid $border-color;
        overflow-y: auto;
        .type {
            height: 40px;
            line-height: 40px;
            padding-left: 22px;
            font-size: 14px;
            color: #333;
            font-weight: bold;
            border-bottom: 1px solid $border-color;
        }
        .receiver {
            position: relative;
            padding: 10px 22px;
            border-bottom: 1px solid $border-color;
            .title {
                font-size: 12px;
                color: #333;
                padding-bottom: 10px;
            }
            .el-input__inner {
                padding-left: 36px;
                border-radius: 1px;
            }
            .el-input__icon {
                color: #333;
            }
            .signers-opt-circle {
                z-index: 99;
                position: absolute;
                top: 45px;
                left: 34px;
                width: 12px;
                height: 12px;
                background-color: $site-bgclr;
                // border: 1px solid #82C9FC;
                border-radius: 50%;
            }
        }
        .delete-container {
            position: absolute;
            bottom: 0;
            width: 167px;
            padding: 15px 22px;
            background-color: #fafafa;
            border-bottom: 1px solid $border-color;
            border-top: 1px solid $border-color;
            .delete {
                width: 100%;
                height: 28px;
                line-height: 28px;
                text-align: center;
                border: 1px solid #ccc;
                border-radius: 1px;
                cursor: pointer;
                &:hover {
                    background-color: #fff;
                }
            }
        }
    }
    .FieldEdit-signers-selct {
        .signers-name {
            display: inline-block;
            position: relative;
            top: 0;
            width: 16px;
            height: 16px;
            border-radius: 50%;
        }
        .signers-cirle {
            display: inline-block;
            position: relative;
            top: -4px;
            margin-left: 8px;
        }
    }
    // 动画
    .slider-enter-active{
        transition: all .3s ease;
    }
    .slider-leave-active {
        transition: all .3s ease;
    }
    .slider-enter, .slider-leave-to /* .fade-leave-active in below version 2.1.8 */ {
        transform: translateX(210px);
    }

    // 签署日期表单样式
    .localSend-dateLabel-form {
        padding: 10px 14px;
        label {
            width: 100%;
            height: 20px;
            font-size: 14px;
            color: #333;
            text-align: left;
            padding-top: 0;
            padding-bottom: 0;
        }

        .form-name .el-form-item__label::after {
            content: "*";
            color: #f86b26;
            margin-left: 4px;
        }

        .el-form-item__content .el-input {
            padding-top: 0;

            .el-input__inner {
                height: 28px;
                line-height: 28px;
                font-size: 12px;
            }

            .el-input__icon {
                color: #333;
            }
        }
    }
</style>
