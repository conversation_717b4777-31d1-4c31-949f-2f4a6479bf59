<template>
    <div class="sign-field-doc">
        <!-- 水印编辑区 -->
        <div class="watermarks-container" ref="watermark">
            <Watermark
                @delete="deleteWatermark(watermark, index)"
                @update="editWatermark"
                v-for="(watermark, index) in watermarkList"
                :key="watermark.labelId"
                :watermark="watermark"
                :color="computeFillColor(watermark)"
                :readonly="templateStatus!=='edit'"
                :templateStatus="templateStatus"
            >
            </Watermark>
        </div>
        <!-- 合同内容区 -->
        <div class="field-doc-content" ref="docContent">
            <Pdf :scale="scale"
                :waterMarkPng="waterMarkPng"
                :currentPageIndex="currentPageIndex"
                :doc="doc"
                :isShowRidingSeal="ridingSealList && ridingSealList.length && doc.page.length > 1"
                @updateCurrentPageIndex="handleUpdatePageIndex"
                @ready="handleReady"
            >
                <template slot-scope="index">
                    <Labels
                        v-for="(mark, markIndex) in markList(index.data)"
                        :key="`${markIndex}_${mark.labelId}_${optCount}`"
                        :pageHeight="doc.page[index.data].height"
                        :pageWidth="doc.page[index.data].width"
                        :scale="scale"
                        :color="computeRgbColor(mark)"
                        :receiverName="computeOwner(mark)"
                        :mark="mark"
                        @mark-start="onMarkStart($event, mark)"
                        @mark-move="onMarkMove($event, mark, doc.page[index.data])"
                        @mark-end="onMarkEnd($event, mark)"
                        @mark-delete="onMarkDelete(mark)"
                        :templateStatus="templateStatus"
                    >
                    </Labels>
                    <div class="riding-seals" v-if="ridingSealList.length && doc.page.length > 1">
                        <div class="riding-seals-bg"></div>
                        <RidingSeal
                            v-for="(ridingseal, sealIndex) in ridingSealList"
                            :key="ridingseal.labelId"
                            :ridingseal="ridingseal"
                            :pageHeight="doc.page[index.data].height"
                            :color="computeRgbColor(ridingseal)"
                            :canDrag="true"
                            @update-server="updateRidingSeal($event, sealIndex)"
                            @delete="deleteRidingSeal(ridingseal, sealIndex)"
                            :scale="scale"
                            :templateStatus="templateStatus"
                        >
                        </RidingSeal>
                    </div>
                </template>
            </Pdf>

            <div v-if="currentDocIndex === docList.length-1" class="done-btn" @click="$emit('to-next')">{{ $t('sign.send') }}</div>
            <div v-else class="next-doc-btn" @click="handleNextDoc">{{ $t('sign.enterNextContract') }}</div>
        </div>
    </div>
</template>
<script>
import Pdf from 'components/pdf/Pdf.vue';
import Labels from 'components/label/Label.vue';
import { throttle, markCoordinateTransform } from 'utils/fn.js';
import { markInfo, textMarkInfo } from 'src/pages/foundation/sign/common/info/info.js';
import RidingSeal from 'components/ridingSeal/RidingSeal.vue';
import Bus from 'components/bus/bus.js';
import Watermark from 'components/watermarkEdit/WatermarkEdit.vue';
import { RideSealMixin } from 'src/mixins/RideSealMixin.js';
import { calcRgbColor, calcFillColor } from 'utils/colorInfo.js';
import { createWaterMark, RIDESEAL_SHADOW_WIDTH } from 'utils/decorate.js';
import { PDF_PAGE_DISTANCE } from 'src/common/const';
import { editMark, deleteMark } from '../common/mark/mark.js';

export default {
    components: {
        Pdf,
        Labels,
        RidingSeal,
        Watermark,
    },
    mixins: [RideSealMixin],
    props: [
        // eslint-disable-next-line vue/require-prop-types
        'contractId', 'docList', 'receivers', 'receiverIndex', 'dragStatus', 'float', 'floatingType', 'edit', 'watermarkList', 'ridingSealList', 'marks', 'currentDocIndex', 'currentPageIndex', 'templateStatus',
    ],
    data() {
        return {
            doc: null,
            pdfDocument: null,
            container: null,
            numPages: 0,
            creation: {
                status: 'pedding',
            },
            tempMark: null,
            move: {},
            docWidth: 0,
            focus: {
                mark: this.edit.mark,
            },
            optCount: 0, // 为字段增加操作标记，修正deleteMark时视图不更新问题，CFD-19298
        };
    },
    computed: {
        waterMarkPng() {
            const watermarkText = (this.watermarkList || []).map(item => item.watermarkText);
            if (!watermarkText.length) {
                return null;
            }
            return createWaterMark(watermarkText, 300, 200);
        },
        markList() {
            return (pageIndex) => {
                // TODO 优化计算
                console.log('markList');
                // 当前文档的markList
                const marks = this.doc.marks;
                const markList = (marks || []).concat(this.tempMark || []).filter(mark => mark.pageNumber === pageIndex + 1);
                return markList;
            };
        },
        // 计算页面的缩放比
        scale() {
            const { maxWidth, docWidth } = this;
            // if (!maxWidth || !docWidth) return 96 / 72;
            // return (docWidth / maxWidth) * (72 / 96);
            if (!maxWidth || !docWidth) {
                return 1;
            }
            return docWidth / maxWidth;
        },
        // 文档的最大宽度
        maxWidth() {
            // 切换文档时，需要重新计算文档的最大宽高度
            const docMaxWidth = this.doc.page.reduce((max, page) => Math.max(max, page.width || 0), 0);
            // 除与合同重叠部分以外，骑缝章宽度/阴影背景部分宽度
            return docMaxWidth + RIDESEAL_SHADOW_WIDTH;
        },
    },
    watch: {
        edit: {
            handler: function(v) {
                this.focus = v;
            },
            deep: true,
        },
        focus: {
            handler: function(v) {
                if (v.mark) {
                    this.$emit('mark-focus', v);
                }
            },
            deep: true,
        },
        // 每个文档挂载自己的印章、签名，水印和骑缝章是公有的，当前文档的最大宽度
        currentDocIndex: {
            handler(newValue, oldValue) {
                if (newValue !== oldValue) {
                    this.doc = this.docList[this.currentDocIndex];
                }
            },
            immediate: true,
        },
    },
    methods: {
        // 更新page的宽高信息
        handleReady(page) {
            this.$set(this.doc, 'page', page);
        },
        hideFocusMark(event) {
            const target = event.target;
            if (target.closest('.send-label-container') || target.closest('.FieldEdit-cpn')) {
                return;
            }
            this.focus.mark = null;
        },
        handleUpdatePageIndex(currentPageIndex) {
            this.$emit('updateCurrentPageIndex', currentPageIndex);
        },
        // 新增或者编辑水印
        editWatermark({ labelId = 0, watermarkText = '' } = {}) {
            // 如果是新建水印，每个用户只能有一个水印
            const watermarkList = this.watermarkList;
            const { receiverId, inputUserName, userType, userAccount, userName, inputEnterpriseName } = this.receivers[this.receiverIndex] || {};
            if (!labelId) {
                // 防止水印重复添加
                if (watermarkList.find(item => receiverId === item.receiverId)) {
                    return;
                }
                if (userType === 'PERSON') {
                    watermarkText = inputUserName ? `${userAccount}(${inputUserName})` : userAccount;
                } else {
                    watermarkText = inputEnterpriseName;
                }
            }
            this.$http.post(`${signPath}/contracts/labels/save-watermark`, {
                contractId: this.contractId,
                labelId,
                receiverId,
                watermarkText: watermarkText.slice(0, 20),
            }).then(({ data }) => {
                const index = watermarkList.findIndex(item => labelId === item.labelId);
                if (index < 0) { // 新增水印
                    watermarkList.push({
                        labelId: data.labelId,
                        receiverId: data.receiverId,
                        watermarkText: data.watermarkText,
                        roleName: userName,
                    });
                } else { // 编辑水印
                    watermarkList[index].watermarkText = data.watermarkText;
                }
            }).catch(() => {

            });
        },
        deleteWatermark(watermark, index) {
            this.$http.delete(`${signPath}/contracts/${this.contractId}/labels/${watermark.labelId}`)
                .then(() => {
                    this.watermarkList.splice(index, 1);
                });
        },
        computeRgbColor(mark) {
            return calcRgbColor(this.receivers, 'receiverId', mark.receiverId);
        },
        //  TODO 这里模板也要用水印组件，待模板更改后，统一改为computeRgbColor
        computeFillColor(mark) {
            return calcFillColor(this.receivers, 'receiverId', mark.receiverId);
        },
        computeOwner(mark) {
            let receiver;
            if (this.templateStatus !== '' && this.templateStatus !== 'edit') {
                receiver = (this.receivers || []).find(receiver => receiver.receiverId === mark.receiverId) || {};
            } else if (this.templateStatus === '') {
                receiver = (this.receivers || []).find(receiver => receiver.roleId === mark.roleId) || {};
            }
            return receiver.userName || '';
        },
        // 获取鼠标相对于目标元素左/上边界的距离
        getMarkPostion(event, targetEl) {
            const { left, top } = targetEl.getBoundingClientRect();
            return {
                x: event.clientX - Math.floor(left),
                y: event.clientY - Math.floor(top),
            };
        },
        handleMousemove: throttle(function(event) {
            if (this.dragStatus !== 'started') {
                return;
            }
            // const el = event.target.closest('.pdfpage-wrapper');
            const el = event.target.closest('.contract-page-content');
            if (!el) {
                this.creation.status = 'pedding';
                this.tempMark = null;
                this.$emit('updateFloatIcon', true, event);
                return;
            }
            this.$emit('updateFloatIcon', false);
            const pageIndex = el.getAttribute('page-index');
            const { documentId, page } = this.doc;
            const pageSize = page.length;
            const position = this.getMarkPostion(event, el);
            const x = position.x / this.scale;
            const y = position.y / this.scale;
            const { width: pageWidth, height: pageHeight } = page[pageIndex];
            // 判断如果是日期标签的话，取自适应宽高 markInfo中返回的是常量值
            const { width: markWidth, height: markHeight } = this.floatingType === 'DATE' ? textMarkInfo(this.float) : markInfo(this.floatingType);
            const percentCoordinate = markCoordinateTransform(
                {
                    x,
                    y,
                    width: markWidth,
                    height: markHeight,
                },
                pageWidth,
                pageHeight,
            );
            // 拖拽过程中的边界修正
            percentCoordinate.y = this.fixBoundary(percentCoordinate.y, percentCoordinate.height);
            percentCoordinate.x = this.fixBoundary(percentCoordinate.x, percentCoordinate.width);
            // pageNumber值比pageIndex大1，限制最大页码
            const pageNumber = pageIndex > pageSize ? pageSize : parseFloat(pageIndex) + 1;
            if (this.creation.status === 'ready' && this.tempMark) {
                this.tempMark = {
                    ...this.tempMark,
                    documentId,
                    pageNumber,
                    ...percentCoordinate,
                };
            } else {
                this.tempMark = {
                    receiverId: this.receivers[this.receiverIndex].receiverId,
                    type: this.floatingType,
                    documentId,
                    pageNumber,
                    ...percentCoordinate,
                    style: {
                        fontSize: this.float.fontSize,
                    },
                };
                this.creation.status = 'ready';
            }
        }, 24),
        async handleMouseup() {
            if (this.creation.status !== 'ready') {
                return;
            }
            this.creation.status = 'done';
            try {
                await editMark(this.contractId, this.doc.marks, {
                    labelId: '', // 新建标签，labelId为空
                    contractId: this.contractId,
                    ...this.tempMark,
                });
            } finally {
                this.tempMark = null;
            }
        },
        // 标签拖拽移动
        onMarkStart(e, mark) {
            if (!mark.labelId) {
                return;
            }
            this.move.eX = e.clientX;
            this.move.eY = e.clientY;
            // 记录当前移动标签的初始位置
            this.move.markX = mark.x;
            this.move.markY = mark.y;
            // 聚焦
            if (mark.type !== 'QR_CODE') {
                this.focus.mark = mark;
            }
        },
        // 标签拖动过程中，计算新的标签位置
        onMarkMove(e, mark, page) {
            if (!mark.labelId) {
                return;
            }
            const distanceX = (e.clientX - this.move.eX) / this.scale;
            const distanceY = (e.clientY - this.move.eY) / this.scale;
            mark.x = this.move.markX + distanceX / (page.width);
            mark.y = this.move.markY - distanceY / (page.height);
        },
        // 计算向上拖动时移动到第几页
        computedYUp(mark) {
            const page = this.doc.page || [];
            const pageNumber = mark.pageNumber;
            // 获取初始时标签所在页面的宽高
            const { width: oriWidth, height: oriHeight } = page[pageNumber - 1];
            // 超出时的默认值为第一页的最顶部
            let newPageIndex = 0;
            let height = 0;
            let y = 1 - mark.height;
            const distanceY = Math.abs(mark.y - 1) * oriHeight;
            const pageList = page.slice(0, pageNumber - 1).reverse();
            pageList.some((page, pageIndex) => {
                height += (page.height + PDF_PAGE_DISTANCE);
                if (distanceY <= height) {
                    newPageIndex = pageNumber - pageIndex - 2;
                    // 计算百分比
                    y = 1 - (height - distanceY) / page.height;
                }
                return distanceY <= height;
            });

            const { width: pageWidth, height: pageHeight } = page[newPageIndex];
            const x = (oriWidth * mark.x - (oriWidth - pageWidth) / 2) / pageWidth;
            return {
                pageNumber: newPageIndex + 1,
                y,
                x,
                width: mark.width * oriWidth / pageWidth, // 根据新页面的宽高，重新计算标签的宽高比
                height: mark.height * oriHeight / pageHeight,
            };
        },
        // 计算向上拖动时移动到第几页
        computedYDown(mark) {
            const page = this.doc.page || [];
            const pageNumber = mark.pageNumber;
            // 获取初始时标签所在页面的宽高
            const { width: oriWidth, height: oriHeight } = page[pageNumber - 1];
            const distanceY = Math.abs(mark.y) * oriHeight;
            // 超出时的默认值为最后一页的最底部
            let newPageIndex = page.length - 1;
            let y = 0;
            let height = 0;
            // 从当前文档的当前页的下一页开始计算高度
            page.slice(pageNumber).some((page, pageIndex) => {
                height += (page.height + PDF_PAGE_DISTANCE);
                if (distanceY <= height) {
                    newPageIndex = pageIndex + pageNumber;
                    // 计算百分比
                    y = (height - distanceY) / (page.height);
                }
                return distanceY <= height;
            });
            const { width: pageWidth, height: pageHeight } = page[newPageIndex];
            const x = (oriWidth * mark.x - (oriWidth - pageWidth) / 2) / pageWidth;

            return {
                pageNumber: newPageIndex + 1,
                y,
                x,
                width: mark.width * oriWidth / pageWidth, // 根据新页面的宽高，重新计算标签的宽高比
                height: mark.height * oriHeight / pageHeight,
            };
        },

        // 标签拖动结束，注意边界修正
        async onMarkEnd(e, mark) {
            // 修复弱网情况下，用户创建标签接口未返回，又快速拖动标签导致标签重复创建的问题
            if (!mark.labelId) {
                return;
            }
            let pageNumber, y, x, width, height;

            // 未拖出本页面时，直接修正
            if (mark.y >= -mark.height && mark.y <= 1) { // 当前页，直接修正边界
                pageNumber = mark.pageNumber;
                width = mark.width;
                height = mark.height;
                y = mark.y;
                x = mark.x;
            } else if (mark.y < -mark.height) { // 向下拖动跨页
                ({ pageNumber, x, y, width, height } = this.computedYDown(mark));
            } else if (mark.y > 1) { // 向上拖动，超出当前页面
                ({ pageNumber, x, y, width, height } = this.computedYUp(mark));
            }
            try {
                await editMark(this.contractId, this.doc.marks, {
                    labelId: mark.labelId,
                    receiverId: mark.receiverId,
                    contractId: this.contractId,
                    documentId: this.doc.documentId,
                    pageNumber,
                    type: mark.type, // SIGNATURE(1), DATE(2), SEAL(3),
                    x: this.fixBoundary(x, width),
                    y: this.fixBoundary(y, height),
                    width,
                    height,
                    style: {
                        fontSize: (mark.style || {}).fontSize,
                    },
                });
            } catch (err) {
                mark.x = this.move.markX;
                mark.y = this.move.markY;
            }
        },
        // 订正坐标的边界
        fixBoundary(ratio, space) {
            if (ratio < 0) {
                return 0;
            }
            if (ratio > 1 - space) {
                return 1 - space;
            }
            return ratio;
        },
        // 删除标签
        async onMarkDelete(mark) {
            await deleteMark(this.contractId, this.doc.marks, mark);
            this.$nextTick(() => {
                this.optCount += 1;
            });
            this.$emit('mark-blur');
        },
        handleNextDoc() {
            this.$emit('updateCurrentDocIndex', this.currentDocIndex + 1);
        },
    },
    created() {
        Bus.$on('addDecoration', type => {
            if (type === 'WATERMARK') {
                return this.editWatermark();
            }
            if (type === 'DECORATE_RIDING_SEAL') {
                return this.createRidingSeal();
            }
        });
    },
    async mounted() {
        this.docWidth = this.$refs.docContent.getBoundingClientRect().width;
        this.doc = this.docList[this.currentDocIndex];
        document.addEventListener('mousemove', this.handleMousemove);
        document.addEventListener('mouseup', this.handleMouseup);
        document.addEventListener('click', this.hideFocusMark);
    },
    beforeDestroy() {
        document.removeEventListener('mousemove', this.handleMousemove);
        document.removeEventListener('mouseup', this.handleMouseup);
        document.removeEventListener('click', this.hideFocusMark);
        Bus.$off('addDecoration'); // 移除事件避免多次监听
    },
};
</script>
<style lang="scss">
.sign-field-doc {
    margin-top: 41px;
    padding: 0px 30px 0px;
    height: calc(100% - 41px);
    overflow-x: auto;
    overflow-y: auto;
    box-sizing: border-box;
    .field-doc-content {
        margin-top: 20px;
        transform-origin: 0 0 0;
        .next-doc-btn {
            color: #127fd2;
            margin: 15px 0;
            text-align: center;
            font-weight: 700;
            font-size: 14px;
            cursor: pointer;
        }
        .done-btn {
            height: 30px;
            line-height: 30px;
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            color: #fff;
            background-color: #127fd2;
            margin:15px 0;
            cursor: pointer;
            border-radius: 2px;
            display: inline-block;
            padding: 0 10px;
            position: relative;
            left: 50%;
            transform: translateX(-50%);

            &:hover {
                background-color: #1687dc;
            }
        }
    }
}
</style>
