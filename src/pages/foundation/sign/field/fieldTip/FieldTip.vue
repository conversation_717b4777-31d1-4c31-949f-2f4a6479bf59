<!--
 * @Author: fan_liu
 * @Date: 2020-11-23 17:36:41
 * @LastEditors: fan_liu
 * @LastEditTime: 2021-02-22 13:52:09
 * @Description: Do not edit
-->
// 未指定签署位置弹窗提示
<template>
    <el-dialog
        :title="$t('field.fieldTip.title')"
        :visible.sync="show"
        size="tiny"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :before-close="handleClose"
        class="field-dialog-tip el-dialog-bg"
    >
        <div v-for="item in tipList" :key="item.receiverId" class="dialog-tip-content">
            <p class="dialog-tip-title"><span>{{ item.userName }}</span>
                <template v-if="item.signType ==='SEAL_AND_SIGNATURE'">
                    {{ $t('field.fieldTip.error', {type: $t('addReceiver.Ss')}) }}
                </template>
                <template v-else>
                    {{ $t('field.fieldTip.error', {type: item.userType === 'ENTERPRISE' ? $t('addReceiver.stamp') : $t('addReceiver.signature')}) }}
                </template>
            </p>
            <ul class="dialog-tip-list">
                <li v-for="(file, fileIndex) in item.fileList" :key="fileIndex">
                    {{ file.fileName }}
                </li>
            </ul>
        </div>
        <div slot="footer" class="dialog-footer">
            <el-button @click="handleClose">{{ $t('field.fieldTip.add') }}</el-button>
            <el-button type="primary" @click="handleSubmit">{{ confirmButtonText || $t('field.fieldTip.continue') }}</el-button>
        </div>
    </el-dialog>
</template>
<script>
export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['tipList', 'show', 'confirmButtonText'],
    data() {
        return {};
    },
    methods: {
        handleClose() {
            this.$emit('update:show', false);
        },
        handleSubmit() {
            this.$emit('update:show', false);
            this.$emit('confirm');
        },
    },
};
</script>

<style lang="scss">
    .field-dialog-tip {
        .dialog-tip-content {
            .dialog-tip-title {
                color: #333;
                font-size: 12px;
                span {
                    font-weight: bold;
                }
            }
            .dialog-tip-list {
                color: #666;
                font-size: 12px;
                line-height: 1.5;
                li {
                    margin: 10px 0;
                    padding-left: 10px;
                    position: relative;
                    &::before {
                        background-color: #666;
                        position: absolute;
                        top: 50%;
                        left: 0;
                        width: 4px;
                        height: 4px;
                        border-radius: 50%;
                        content: '';
                        margin-top: -2px;
                    }
                }
            }
        }
    }

</style>
