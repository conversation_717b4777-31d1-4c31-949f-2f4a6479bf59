// 普通发起合同标签的增删改查，多个地方用到
// 新增或者编辑标签
function editMark(contractId, markList, mark, opts = {}) {
    const { labelId } = mark;
    return new Promise((resolve, reject) => {
        Vue.$http.post(`${signPath}/contracts/${contractId}/labels/create-and-modify/`, [mark], opts)
            .then(({ data }) => {
                const newMark = data[0];
                // 新建标签
                if (!labelId) {
                    markList.push(newMark);
                } else { // 修改标签
                    const markIndex = markList.findIndex(item => labelId === item.labelId);
                    Vue.set(markList, markIndex, {
                        ...mark,
                        ...newMark,
                    });
                }
                resolve();
            })
            .catch(err => {
                reject(err);
            });
    });
}

// 删除标签
function deleteMark(contractId, markList, mark) {
    const { labelId } = mark;
    return Vue.$http.delete(`${signPath}/contracts/${contractId}/labels/${labelId}`)
        .then(() => {
            const markIndex = markList.findIndex(item => item.labelId === labelId);
            markList.splice(markIndex, 1);
        })
        .catch(() => {});
}

export {
    editMark,
    deleteMark,
};
