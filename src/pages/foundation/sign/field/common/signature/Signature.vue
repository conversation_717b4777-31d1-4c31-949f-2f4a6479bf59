<template>
    <g class="Signature-cop"
        :class="{ 'qrcode': mark.type=='QR_CODE' }"
        :type="mark.type"
        :transform="`matrix(1,0,0,1, ${x}, ${y})`"
    >
        <!-- 头部 -->
        <g
            class="owner-title"
            v-if="mark.type == 'SEAL' || mark.type == 'SIGNATURE'"
        >
            <rect
                x="0"
                y="-26"
                :width="mark.width"
                height="24"
                fill="#fff7b6"
            />
            <text
                x="6"
                y="-9"
                fill="#333"
                font-size="12"
                text-anchor="start"
            >
                {{ owner || '' }}
            </text>
        </g>
        <!-- 矩形背景 -->
        <rect
            class="mark"
            fill-opacity="0.75"
            :fill="fill"
            :stroke="reactStyle.stroke"
            :stroke-width="reactStyle.strokeWidth"
            :width="mark.width"
            :height="mark.height"
            :rx="reactStyle.rx"
            :ry="reactStyle.rx"
            @mousedown="onDragStart"
            @contextmenu.prevent="openContextmenu"
        >
            <title v-if="mark.type == 'QR_CODE'">
                {{ $t('field.qrCodeTips') }}
            </title>
        </rect>
        <!-- 签署日期 -->
        <text
            v-if="mark.type === 'DATE'"
            x="0"
            y="0"
            fill="#333"
            style="pointer-events: none;"
            :font-size="mark.style.fontSize"
        >
            <tspan
                x="1"
                :dy="textPaddingTop"
            >
                {{ $t('field.signDate') }}
            </tspan>
        </text>
        <!-- 印章 -->
        <template
            v-else-if="mark.type=='SEAL'"
        >
            <circle
                cx="134"
                cy="102"
                r="81"
                fill="#fff"
                opacity=".3"
                style="pointer-events: none;"
            />
            <svg
                class="icon"
                aria-hidden="true"
            >
                <use
                    style="pointer-events: none;"
                    x="58"
                    y="52"
                    width="154"
                    height="96"
                    :xlink:href="`#${style.type}`"
                >
                </use>
            </svg>
        </template>
        <!-- 签名 -->
        <template
            v-else-if="mark.type=='SIGNATURE'"
        >
            <svg
                class="icon"
                aria-hidden="true"
            >
                <use
                    style="pointer-events: none;"
                    x="0"
                    :y="-6"
                    width="134"
                    height="83"
                    :xlink:href="`#${style.type}`"
                >
                </use>
            </svg>
        </template>
        <!-- 二维码 -->
        <template v-else-if="mark.type=='QR_CODE'">
            <image
                style="pointer-events: none;"
                :xlink:href="`${mark.value}?access_token=${$cookie.get('access_token')}`"
                :width="mark.width"
                :height="mark.height"
            >
            </image>
        </template>
        <!-- 删除按钮 -->
        <g
            class="del-btn"
            v-if="mark.type!='QR_CODE'"
            @click="clickdelBtn"
        >
            <circle
                :cx="mark.width"
                cy="0"
                r="11"
                fill="#333"
                opacity=".75"
            />
            <use
                style="pointer-events: none;"
                :x="mark.width - 5"
                :y="-5"
                width="10"
                height="10"
                fill="#fff"
                xlink:href="#el-icon-ssq-guanbi"
            >
            </use>
        </g>

    </g>
</template>
<script>
import { markIconInfo } from 'src/pages/foundation/sign/common/info/info.js';

export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['owner', 'focusing', 'mark', 'x', 'y', 'fill', 'contractId'],
    data() {
        // let isQRCode = this.mark.type == 'QR_CODE';
        return {
            dragging: false,
            // 点击还是拖拽的判断
            startTime: '',
            endTime: '',
            isClick: false,
            reactStyle: {
                stroke: this.focusing ? '#127fd2' : 'transparent',
                strokeWidth: '2',
                rx: '2',
            },
        };
    },
    computed: {
        style() {
            return markIconInfo(this.mark.type);
        },
        textPaddingTop() {
            return (this.mark.style.fontSize || 12) + 1;
        },
    },
    methods: {
        onDragStart(e) {
            this.startTime = new Date().getTime();
            this.dragging = true;
            document.addEventListener('mousemove', this.onDragMove);
            document.addEventListener('mouseup', this.onDragEnd);
            // 二维码需要跳转到二维码合同信息页
            if (e.button === 0) {
                this.$emit('mark-start', e);
            }
        },
        onDragMove(e) {
            if (!this.dragging) {
                return;
            }
            this.$emit('mark-move', e);
        },
        onDragEnd(e) {
            this.endTime = new Date().getTime();
            this.isClick = this.endTime - this.startTime < 200;
            this.dragging = false;
            document.removeEventListener('mousemove', this.onDragMove);
            document.removeEventListener('mouseup', this.onDragEnd);
            if (this.isClick) {
                return;
            }
            this.$emit('mark-end', e);
        },
        clickdelBtn(e) {
            this.$emit('delete-signature', e);
        },
        openContextmenu(e) {
            this.$emit('openContextmenu', e);
        },
    },
};
</script>
<style lang="scss">
    .Signature-cop {
        cursor: move;
        &.qrcode {
            cursor: pointer;
        }
        .owner-title {
            cursor: default;
        }
        .del-btn {
            cursor: pointer;
        }
    }
</style>
