
<template>
    <div class="sign-container">
        <SignHeader
            :title="title"
            :rText="rText"
            @to-back="toBack"
            @to-next="toNext"
        >
        </SignHeader>

        <slot></slot>

        <!-- 页脚 -->
        <RegisterFooter></RegisterFooter>
    </div>
</template>

<script>
import SignHeader from '../../common/signHeader/SignHeader.vue';
import RegisterFooter from 'components/register_footer/RegisterFooter.vue';
export default {
    components: {
        SignHeader,
        RegisterFooter,
    },
    // eslint-disable-next-line vue/require-prop-types
    props: ['title', 'rText'],
    data() {
        return {};
    },
    methods: {
        toBack() {
            this.$emit('to-back');
        },
        toNext() {
            this.$emit('to-next');
        },
    },
};
</script>

<style lang="scss">
    .sign-container {
        .register-footer {
            position: fixed;
            bottom: 0;
        }
        .sign-local-header {
            height: 50px;;
        }
    }

</style>
