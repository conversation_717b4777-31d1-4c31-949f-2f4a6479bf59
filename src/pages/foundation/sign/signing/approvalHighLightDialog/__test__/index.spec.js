import ApprovalHighLightDialog from '../index.vue';
import { initWrapper } from 'src/testUtils';
jest.mock('src/common/assets/img/contractHighLight1.png', () => {});
jest.mock('src/common/assets/img/contractHighLight2.png', () => {});
jest.mock('src/common/assets/img/approvalAnnotateImg1.png', () => {});
jest.mock('src/common/assets/img/approvalAnnotateImg2.png', () => {});
describe('ApprovalHighLightDialog', () => {
    const mockEmitFun = jest.fn();
    const mockSetFun = jest.fn();
    const baseStoreOptions = {};
    const baseWrapperOptions = {
        mocks: {
            $emit: mockEmitFun,
            $localStorage: {
                set: mockSetFun,
            },
            $sensors: {
                track: jest.fn(),
            },
        },
    };
    const wrapper = initWrapper(ApprovalHighLightDialog, { ...baseStoreOptions }, { ...baseWrapperOptions });
    test('点击确定', () => {
        wrapper.vm.handleClick();
        expect(wrapper.vm.visible).toBeFalsy();
        expect(mockSetFun).toHaveBeenCalledWith('isHighLightAndAnnotateTipShow', true);
    });
});
