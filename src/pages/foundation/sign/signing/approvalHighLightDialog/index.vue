<template>
    <div>
        <el-dialog
            class="approval-high-light-container"
            :visible.sync="visible"
            :title="$t('sign.approvalFeatures.dialogTitle')"
        >
            <ul>
                <li v-if="!isHybridCloudContract">
                    <h2>1.{{ $t('sign.approvalFeatures.feature1') }}</h2>
                    <p class="high-light-tip">
                        {{ $t('sign.approvalFeatures.tip4') }}
                    </p>
                    <div class="high-light-tip-container">
                        <div class="high-light-tip-img">
                            <img :src="approvalAnnotateImg1" alt="">
                            <p>{{ $t('sign.approvalFeatures.tip5') }}</p>
                        </div>
                        <div class="high-light-tip-img">
                            <img :src="approvalAnnotateImg2" alt="">
                            <p>{{ $t('sign.approvalFeatures.tip6') }}</p>
                        </div>
                    </div>
                </li>
                <li>
                    <h2>{{ isHybridCloudContract ? '' : '2.' }}{{ $t('sign.approvalFeatures.feature2') }}</h2>
                    <p class="high-light-tip">
                        {{ $t('sign.approvalFeatures.tip1') }}
                    </p>
                    <div class="high-light-tip-container">
                        <div class="high-light-tip-img">
                            <img :src="contractHighLightImg1" alt="">
                            <p>{{ $t('sign.approvalFeatures.tip2') }}</p>
                        </div>
                        <div class="high-light-tip-img">
                            <img :src="contractHighLightImg2" alt="">
                            <p>{{ $t('sign.approvalFeatures.tip3') }}</p>
                        </div>
                    </div>
                </li>
            </ul>
            <div slot="footer">
                <el-button
                    type="primary"
                    @click="handleClick"
                >{{ $t('sign.approvalFeatures.understand') }}</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import contractHighLight1 from 'src/common/assets/img/contractHighLight1.png';
import contractHighLight2 from 'src/common/assets/img/contractHighLight2.png';
import approvalAnnotateImg1 from 'src/common/assets/img/approvalAnnotateImg1.png';
import approvalAnnotateImg2 from 'src/common/assets/img/approvalAnnotateImg2.png';
export default {
    name: 'ApprovalHighLight',
    props: {
        dialogVisible: {
            default: false,
            type: Boolean,
        },
        sensorsTrackContractInfo: {
            default: () => {},
            type: Object,
        },
        isHybridCloudContract: {
            type: Boolean,
        },
    },
    data() {
        return {
            contractHighLightImg1: contractHighLight1,
            contractHighLightImg2: contractHighLight2,
            approvalAnnotateImg1: approvalAnnotateImg1,
            approvalAnnotateImg2: approvalAnnotateImg2,
        };
    },
    computed: {
        visible: {
            set(v) {
                this.$emit('update:dialogVisible', v);
            },
            get() {
                return this.dialogVisible;
            },
        },
    },
    methods: {
        handleClick() {
            this.$sensors.track({
                eventName: `Ent_ContractApprovalWindow_BtnClick`,
                eventProperty: {
                    page_name: `合同审批页`,
                    window_name: '合同高亮提醒',
                    icon_name: '我知道了',
                    ...this.sensorsTrackContractInfo,
                },
            });
            this.visible = false;
            // 高亮提示是否展示过
            this.$localStorage.set('isHighLightAndAnnotateTipShow', true);
        },
    },
};
</script>

<style lang="scss">
.approval-high-light-container{
    .el-dialog{
        width: 600px;
        .high-light-tip{
            i {
                color: #F2A93E;
                margin-right: 5px;
            }
        }
        .high-light-tip-container{
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
            .high-light-tip-img{
                width: 250px;
                font-size: 12px;
                color: #999999;
                p{
                    margin-top: 5px;
                }
                img{
                    width: 250px;
                }
            }
        }
        h2{
            color: #000;
            font-size: 16px;
            line-height: 20px;
            margin-bottom: 10px;
        }
        li + li{
            padding-top: 20px;
            border-top: 1px solid #d8d8d8;
            margin-top: 20px;
        }
    }
}
</style>
