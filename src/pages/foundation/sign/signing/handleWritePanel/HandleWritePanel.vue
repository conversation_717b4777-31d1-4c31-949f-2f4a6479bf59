<template>
    <div class="HandleWritePanel">
        <div class="clear tab-container">
            <div class="handWrite-tab fl"
                v-for="(tab, index) in handWriteTabs"
                v-show="tab.show"
                :key="index"
                :class="{active: handWriteActive === index}"
                @click="handleClickHandWriteTab(tab, index)"
            >
                {{ tab.title }}
            </div>
        </div>
        <div class="handWrite-panels">
            <component :is="handWriteComponent"
                :initialBusiness="initialBusiness"
                :operateAction="operateAction"
                :parentGift="parentGift"
                :class="handWriteComponent"
                ref="handWritePanels"
                :canvasHeight="220"
                :canvasWidth="720"
                @close="handleClose"
                @updateSignature="updateSignature"
            >
            </component>
        </div>
    </div>
</template>
<script>
import QRCodeTab from '../qrCodeTab/QRCodeTab.vue';
import HandPainted from 'components/signature/handPainted/HandPainted.vue';
import UploadSignaturePicture from '@/pages/foundation/sign/signing/uploadSignaturePicture/UploadSignaturePicture';
export default {
    components: {
        QRCodeTab,
        HandPainted,
        UploadSignaturePicture,
    },
    props: {
        parentGift: {
            type: Object,
            default: function() {
                return {
                    contractId: '',
                    labelId: '',
                    receiverId: '',
                    handWritingRecognition: false, // 是否需要笔迹识别
                    name: '',
                    showUploadSignaturePicture: false, // 是否展示上传图片签名的tab， 仅企业签字人展示
                };
            },
        },
        initialBusiness: {
            type: String,
            default: 'sign-single',
        },
        operateAction: {
            type: String,
            default: 'click',
        },
    },
    data() {
        return {
            handWriteTabs: [
                {
                    type: 'QRCodeTab',
                    title: this.$t('handwrite.QRCode'),
                    show: true,
                },
                {
                    type: 'HandPainted',
                    title: this.$t('handwrite.title'),
                    show: true,
                },
                {
                    type: 'UploadSignaturePicture',
                    title: this.$t('handwrite.upload'),
                    show: this.parentGift.showUploadSignaturePicture && !this.parentGift.handWritingRecognition,
                },
            ],
            handWriteActive: 0,
            handWriteComponent: 'QRCodeTab',
        };
    },
    methods: {
        handleClickHandWriteTab(tab, index) {
            this.handWriteComponent = tab.type;
            this.handWriteActive = index;
        },
        handleClose() {
            this.$emit('close');
        },
        updateSignature(val) {
            this.$emit('updateSignature', val);
        },
    },
};
</script>
<style lang="scss">
	.HandleWritePanel {
		.tab-container {
			background-color: #f6fafd;
		}
		.handWrite-tab {
			margin-left: 15px;
			height: 36px;
			line-height: 46px;
			font-size: 14px;
			color: #333;
			text-align: center;
			cursor: pointer;
		}
		.active {
			color: #127fd2;
			border-bottom: 2px solid #127fd2;
		}
		.handWrite-panels {
			height: 300px;
			background-color: #F6FAFD;
			border-top: 1px solid #EBEEEE;
			.HandPainted {
				width: 740px;
				/*height: 300px;*/
			}
		}

		// 补丁...
		.HandPainted#hand-painted {
			font-size: 14px;
			.title {
				display: none;
			}
			canvas {
				//background-color: #fff;
			}
			.bottom {
				height: 50px;
				line-height: 50px;
				background-color: #F6FAFD;
			}
			.btn {
				width: 55px;
				height: 25px;
				line-height: 25px;
				margin-top: 13px;
			}
		}
	}
</style>
