<template>
    <div class="paper-sign-va-form">
        <el-form label-width="80px">
            <el-form-item class="countdown-item" :label="sendType === 'E' ? $t('sign.email') : $t('sign.phoneNumber')">
                <span class="phone">{{ notice || $t('sign.setNotificationInUserCenter') }}</span>
            </el-form-item>
            <el-form-item :label="sendType == 'E' ? $t('sign.mailVerificationCode') : $t('sign.verificationCode')">
                <div class="verify-flex">
                    <el-input
                        class="verify-input"
                        v-model="verifyCode"
                        :maxlength="6"
                    >
                        <ElIDelete slot="icon"></ElIDelete>
                    </el-input>
                    <CountDown class="countDown" :clickedFn="handleClickSendCode" :disabled="countDownDisabled" ref="btn" :second="60"></CountDown>
                </div>
            </el-form-item>
            <div class="switch">
                <!-- 一直收不到短信？试试 -->
                <span v-if="phoneNotice" class="voiceCodeLabel">{{ $t('sign.msgTip') }}</span>
                <span v-if="phoneNotice" class="highlight" @click="handleClickVoice">{{ $t('sign.voiceVerCode') }}</span>
                <span v-if="mailNotice && phoneNotice">
                    {{ $t('sign.or') }}
                    <span v-show="phoneNotice && sendType === 'E'" class="highlight" @click="handleClickMailAndPhone">
                        {{ $t('sign.SMSVerCode') }}
                    </span>
                    <span v-show="mailNotice && (sendType === 'S' || sendType === 'V') " class="highlight" @click="handleClickMailAndPhone">
                        {{ $t('sign.emailVerCode') }}
                    </span>
                </span>
            </div>
        </el-form>
    </div>
</template>
<script>
import ElIDelete from 'components/el_i_delete/ElIDelete.vue';
import CountDown from 'components/countDown/CountDown.vue';
export default {
    components: {
        CountDown,
        ElIDelete,
    },
    data() {
        return {
            verifyCode: '',
            notice: '',
            mailNotice: '',
            phoneNotice: '',
            countDownDisabled: false,
            sendType: 'S', // S：手机短信；E：邮件；V：语音
            contractId: this.$route.query.contractId,
            submitDiable: false,
        };
    },
    inject: ['goAfterSign'],
    methods: {
        getNoticeType() {
            // 获取用户联系方式
            this.$http.get('/users/notifications')
                .then(res => {
                    this.mailNotice = res.data.filter(item => item.type === 1)[0] || '';
                    this.phoneNotice = res.data.filter(item => item.type === 2)[0] || '';
                    this.notice = this.phoneNotice.code || this.mailNotice.code;
                    this.sendType = this.phoneNotice.code ? 'S' : 'E';
                })
                .then(() => {
                    this.$watch('sendType', function() {
                        if (this.$refs.btn.time === 0) {
                            this.send();
                        }
                    });
                });
        },
        // 发送验证码
        handleClickSendCode() {
            this.countDownDisabled = true;
            setTimeout(this.sended, 0);
            // sendVerCode
            this.$http.sendVerCode({ // 不传target，
                code: 'B008',
                sendType: this.sendType,
                bizTargetKey: this.contractId, // contractId
            })
                .then((res) => {
                    this.verifyKey = res.data.value;
                    this.$MessageToast.success(this.$t('sign.SentSuccessfully'));
                })
                .catch(() => {
                    this.$refs.btn.reset();
                });
        },
        // 发送语音验证码
        handleClickVoice() {
            if (this.$refs.btn.time > 0) {
                this.$MessageToast.error(this.$t('sign.intervalTip'));
                return;
            }
            this.sendType = 'V';
            this.notice = this.phoneNotice.code;
        },
        // 发送短信、邮件验证码
        handleClickMailAndPhone() {
            if (this.$refs.btn.time > 0) {
                this.$MessageToast.error(this.$t('sign.intervalTip'));
                return;
            }
            if (this.sendType === 'E') {
                this.sendType = 'S';
                this.notice = this.phoneNotice.code;
            } else {
                this.sendType = 'E';
                this.notice = this.mailNotice.code;
            }
        },
        // 发送成功，开始倒计时
        sended() {
            this.$refs.btn.run();
            this.countDownDisabled = false;
        },
        // 签约校验
        async handleClickConfirmSign() {
            if (this.submitDiable) {
                return;
            }
            const res = await this.$hybrid.offlineTip({ operate: this.$t('sign.signVerification') });
            if (!res) {
                return;
            }
            this.submitDiable = true;
            const loading = this.$loading();
            this.postConfirmSign().then(() => {
                this.$MessageToast.success(this.$t('signPC.operationCompleted')).then(() => {
                    this.goAfterSign();
                });
            }).catch(err => {
                this.isErrorShow = true;
                this.errorMsg = err.response.data.message;
            }).finally(() => {
                loading.close();
                this.submitDiable = false;
            });
        },
        postConfirmSign() {
            return this.$http.post(`/contract-api/contracts/${this.contractId}/papersign/confirm`, {
                verifyCode: this.verifyCode,
                verifyKey: this.verifyKey,
                verifyWay: this.notice,
                signPlatform: 'WEB',
            });
        },
    },
    created() {
        this.getNoticeType();
    },
};
</script>
<style lang="scss">
.paper-sign-va-form{
    .el-form-item{
        margin-bottom: 10px;
        .el-form-item__content{
            line-height: 20px;
        }
        .el-form-item__label{
            line-height: 20px;
            padding: 0;
            text-align: left;
        }
        .verify-flex {
            position: relative;
            display: block;
            line-height: 28px;
            font-size: 0;
            .countDown {
                position: absolute;
                top: 0;
                right: 0;
                width: 120px;
                height: 30px;
                line-height: 30px;
                border: 1px solid #ccc;
                border-radius: 1px;
                color: #333;
            }
            .verify-input{
                input{
                    line-height: 30px;
                    height: 30px;
                }
            }
        }
    }
    .switch {
        text-align: right;
        color: #000;
        font-size: 12px;
        .highlight {
            color: #127fd2;
            cursor: pointer;
        }
    }
}
</style>
