<template>
    <!-- 使用纸质方式签署 -->
    <el-dialog
        :title="$t('paperSign.title')"
        :visible.sync="dialogVisible"
        class="paper-sign-dialog"
        v-loading.fullScreen.lock="pageLoading"
    >
        <div v-if="step === 0" class="paper-sign-step0">
            <h2><i class="el-icon-ssq-hetongxiangqing-chexiao1"></i>{{ $t('paperSign.step0.title') }}</h2>
            <ul>
                <!-- 邮寄地址 -->
                <li><span>{{ $t('paperSign.step0.address') }}</span>{{ paperSignInfo.paperSignMailInfo.mailAddress || $t('paperSign.step0.defaultValue') }}</li>
                <!-- 接收人姓名：  -->
                <li><span>{{ $t('paperSign.step0.contactName') }}</span>{{ paperSignInfo.paperSignMailInfo.mailContactName || $t('paperSign.step0.defaultValue') }}</li>
                <!-- 接收人联系方式： -->
                <li><span>{{ $t('paperSign.step0.contactPhone') }}</span>{{ paperSignInfo.paperSignMailInfo.mailContactPhone || $t('paperSign.step0.defaultValue') }}</li>
            </ul>
        </div>
        <div v-else-if="step === 1" class="paper-sign-step1">
            <!-- 第一步：下载&打印纸质合同 -->
            <h2>{{ $t('paperSign.step1.title0') }}</h2>
            <p>{{ $t('paperSign.step1.title0Desc')[0] }}<span @click="handleDownload" class="paper-sign-step1__download-btn">{{ $t('paperSign.step1.title0Desc')[1] }}</span></p>
            <!-- 第二步：加盖印章 -->
            <h2>{{ $t('paperSign.step1.title1') }}</h2>
            <p>{{ $t('paperSign.step1.title1Desc') }}</p>
            <h2>{{ $t('paperSign.step1.title2')[0] }}{{ paperSignInfo.ifMustScanDocument ? $t('paperSign.step1.title2')[1] : "" }}{{ $t('paperSign.step1.title2')[2] }}</h2>
            <p>{{ paperSignInfo.ifMustScanDocument ? $t('paperSign.step1.title2Desc')[0]:"" }}{{ $t('paperSign.step1.title2Desc')[1] }}</p>
        </div>
        <div v-else-if="step === 2" class="paper-sign-step2">
            <h2><i class="el-icon-ssq-hetongxiangqing-chexiao1"></i>&nbsp;{{ paperSignInfo.ifMustScanDocument ? $t('paperSign.step2.title')[0] : $t('paperSign.step2.title')[1] }}</h2>
            <section>
                <div class="paper-sign-step2__get-file">
                    <!-- 获取纸质签文件 -->
                    <el-button type="primary" class="btn-type-one" size="small" @click="handleDownload">{{ $t('paperSign.downloadPaperFile') }}</el-button>
                </div>
                <template v-if="paperSignInfo.ifMustScanDocument">
                    <!-- 上传扫描件 -->
                    <el-upload
                        class="paper-sign-upload"
                        action=""
                        accept=".pdf"
                        :limit="1"
                        :http-request="handleCacheOpts"
                        :show-file-list="false"
                        :before-upload="beforeUpload"
                    >
                        <el-button type="primary" size="small" :loading="isUploading" class="btn-type-one">{{ isUploading ? $t("paperSign.step2.isUploading") : $t('paperSign.step2.uploadFile') }}
                        </el-button>
                    </el-upload>
                    <p class="paper-sign-step2-file" v-if="file">{{ file }}</p>
                </template>
            </section>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button @click="handleClose">{{ $t('paperSign.cancel') }}</el-button>
            <el-button type="primary" @click="next">{{ stepText[step] }}</el-button>
        </span>
    </el-dialog>
</template>
<script>
import { paperSignFn } from 'src/api/sign.js';
import { mapState } from 'vuex';
import { prepareUploadRequest } from 'utils/hybrid/hybridBusiness.js';
import { invokeDownloadHelper } from 'src/common/utils/download.js';
import { docGetSingleDownloadUrl } from 'utils/hybrid/hybridBusiness.js';
import _get from 'lodash/get';
export default {
    props: {
        paperSignInfo: {
            type: Object,
            default: () => ({
                paperSignMailInfo: {},
            }),
        },
        contractData: {
            type: Object,
            default: () => ({
                systemType: '',
                deltaHybridVersion: '',
                contractId: '',
            }),
        },
        value: {
            type: Boolean,
            default: false,
        },
        contractId: {
            type: String,
            default: '',
        },
        sensorsTrackContractInfo: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            step: 0,
            stepText: this.$t('paperSign.stepText'),
            uploadURL: `/contract-api/papersign/${this.contractId}/upload`,
            dialogVisible: false,
            file: '',
            cacheOpts: null,
            isUploading: false,
            pageLoading: false,
            uploadHeaders: { Authorization: `bearer ${this.$cookie.get('access_token')}` },
        };
    },
    inject: ['goAfterSign'],
    computed: {
        ...mapState({
            hybridServer: state => state.commonHeaderInfo.hybridServer,
        }),
        downloadUrl() {
            return `/contract-api/contracts/${this.contractId}/documents-download?needMergeLabel=true&needAttachment=true&access_token=${this.$cookie.get('access_token')}&ifPaperSign=true`;
        },
        hybridVersionIsGama() {
            return this.hybridServer && this.$hybrid.isGamma(); // 是否是混3
        },
    },
    watch: {
        value(newValue) {
            if (newValue) {
                this.step = 0;
            }
            this.dialogVisible = newValue;
        },
        dialogVisible(value) {
            if (value !== this.value) {
                this.$emit('input', value);
            }
        },
    },
    methods: {
        async beforeUpload(file) {
            // CFD-25087:windows手动切换格式限制后，这里补充强制限制
            const fileTypeArr = file.name.split('.');
            const fileType = fileTypeArr[fileTypeArr.length - 1];
            if (!['pdf'].includes(fileType)) {
                this.$MessageToast.error(this.$t('prepare.usePdfFile'));
                return Promise.reject(new Error());
            }
            // 文件大小最多10MB
            const MaxFileSize = 1024 * 1024 * 10;
            const isAcceptSize = file.size < MaxFileSize;
            if (!isAcceptSize) {
                this.$MessageToast.error(this.$t('sign.fileLessThan', { num: 10 }));
                return Promise.reject(new Error());
            }
            this.isUploading = true;
        },
        handleCacheOpts(opts) {
            this.cacheOpts = opts;
            this.file = opts.file.name;
            this.isUploading = false;
        },
        handleUploadRequest(opts) {
            if (this.hybridVersionIsGama) {
                this.hybridFileUpload(opts);
            } else {
                this.normalFileUpload(opts);
            }
        },
        // 公有云接口上传文件
        normalFileUpload(opts) {
            const formData = new FormData();
            formData.append('file', opts.file);
            this.$http.post(this.uploadURL, formData).then(({ data: { code } }) => {
                if (code === '140001') {
                    this.file = opts.file.name;
                    this.handleConfirmSign();
                } else {
                    this.file = '';
                    this.pageLoading = false;
                    this.cacheOpts = null;
                    this.$MessageToast.error(`${opts.file.name + this.$t('paperSign.uploadError')}`);
                }
            }).catch(() => {
                this.pageLoading = false;
            });
        },
        // 混3接口上传文件
        async hybridFileUpload(opts) {
            const hybridTarget = '/contract/paper-sign-import';
            const data = {
                contractId: this.contractId,
            };
            await new Promise((resolve, reject) => {
                this.$hybrid.makeHeader({
                    url: this.hybridServer + '/hybrid/file',
                    method: 'POST',
                    hybridTarget: hybridTarget,
                    requestData: data,
                    isFormType: 1,
                }).then(res => {
                    const resHeaders = res.data;
                    this.uploadHeaders = {
                        ...this.uploadHeaders,
                        ...resHeaders,
                    };
                    resolve();
                }).catch((err) => {
                    this.pageLoading = false;
                    reject(err);
                });
            });
            prepareUploadRequest({
                hybridServer: this.hybridServer,
                hybridTarget,
                data,
                headers: this.uploadHeaders,
                opts,
                noToast: 0,
            }).then(({ data: result }) => {
                if (result.errorMessage) {
                    this.file = '';
                    this.cacheOpts = null;
                    this.pageLoading = false;
                    this.$MessageToast.error(`${opts.file.name + this.$t('paperSign.uploadError')}`);
                } else {
                    this.file = opts.file.name;
                    this.handleConfirmSign();
                }
            }).catch((err) => {
                this.pageLoading = false;
                const msg = _get(err, 'response.data.message', '');
                this.$MessageToast.error(msg);
            });
        },
        // 单文档下载
        handleDownload() {
            this.handleClickEventTrack(3);
            const { contractId, systemType, deltaHybridVersion } = this.contractData;
            const commonPath = `/contract-api/contracts/${contractId}/documents-download`;
            this.$loading();
            this.invokeDownload('single', {
                contractId,
                systemType,
                commonPath,
                query: '&needAttachment=true',
                documentIds: null,
                deltaHybridVersion,
            }).finally(() => {
                this.$loading().close();
            });
        },
        // 执行合同下载
        invokeDownload(
            type,
            {
                contractId,
                systemType,
                commonPath,
                query,
                documentIds,
                deltaHybridVersion,
            },
        ) {
            return docGetSingleDownloadUrl(type, {
                contractId,
                systemType,
                path: commonPath,
                query,
                documentIds,
                hybridVersion: deltaHybridVersion,
                ifPaperSign: true,
            })
                .then((url) => {
                    if (url && this.hybridVersionIsGama) {
                        return this.hybridVersionIsGama && invokeDownloadHelper(url);
                    }
                    invokeDownloadHelper(this.downloadUrl);
                })
                .catch(() => {});
        },
        next() {
            this.handleClickEventTrack(this.step);
            switch (this.step) {
                case 0:
                case 1: this.step++; break;
                case 2: this.handleConfirm(); break;
            }
        },
        handleConfirm() {
            if (this.paperSignInfo.ifMustScanDocument) {
                if (!this.file) {
                    // 请先上传扫描件
                    return this.$MessageToast.error(this.$t('paperSign.needUploadFile'));
                }
                this.pageLoading = true;
                this.normalFileUpload(this.cacheOpts);
            } else {
                this.pageLoading = true;
                this.handleConfirmSign();
            }
        },
        async handleConfirmSign() {
            paperSignFn(this.contractId).then(() => {
                this.$sensors.track({
                    eventName: 'Ent_ContractSignWindow_Result',
                    eventProperty: {
                        page_name: '合同签署页',
                        window_name: '允许纸质签',
                        is_success: true,
                        icon_name: '确定',
                        contract_id: this.contractId,
                        request_url: `/contract-api/contracts/${this.contractId}/paper-sign/sign`,
                    },
                });

                this.$MessageToast.success(this.$t('signPC.operationCompleted')).then(() => {
                    this.goAfterSign();
                });
            })
                .catch((err) => {
                    const errMsg = err.response?.data?.message;
                    const status = err.response?.status;
                    const code = err.response?.data?.code;
                    this.$sensors.track({
                        eventName: 'Ent_ContractSignWindow_Result',
                        eventProperty: {
                            page_name: '合同签署页',
                            window_name: '允许纸质签',
                            is_success: false,
                            fail_http_code: status,
                            fail_error_code: code,
                            fail_reason: errMsg,
                            contract_id: this.contractId,
                            icon_name: '确定',
                            request_url: `/contract-api/contracts/${this.contractId}/paper-sign/sign`,
                        },
                    });
                })
                .finally(() => {
                    this.pageLoading = false;
                });
        },
        handleClose() {
            this.handleClickEventTrack(4);
            this.$emit('input', false);
        },
        handleClickEventTrack(index) {
            const eventMap = {
                0: '下一步',
                1: '确认纸质签',
                2: '确定',
                3: '获取纸质签文件',
                4: '取消',
            };
            this.$sensors.track({
                eventName: 'Ent_ContractSignWindow_BtnClick',
                eventProperty: {
                    page_name: '合同签署页',
                    window_name: '使用纸质方式签署',
                    icon_name: eventMap[index],
                    contract_id: this.contractId,
                    ...this.sensorsTrackContractInfo,
                },
            });
        },
    },
};
</script>
<style lang="scss">
.paper-sign-dialog {
    .el-dialog {
        width: 600px;
    }
    .paper-sign-step0 {
        font-size: 14px;

        h2{
            margin-bottom: 20px;
            color: #333;
            font-weight: bold;
            position: relative;
            .el-icon-ssq-hetongxiangqing-chexiao1{
                margin-right: 5px;
            }
        }
        li{
            color: #999;
            line-height: 26px;
            span{
                display: inline-block;
                width: 116px;
                color: #333;
            }
        }
    }
    .paper-sign-step1 {
        line-height: 20px;
        h2{
            font-size: 14px;
            color: #333333;
            font-weight: bold;
        }
        p{
            font-size: 12px;
            color: #999999;
            padding: 8px 0 20px;
        }
        &__download-btn {
            color: $btn-primary-color;
            cursor: pointer;
        }
    }
    .paper-sign-step2 {
        h2{
            margin-bottom: 14px;
            padding-top: 20px;
        }
        h2, section{
            margin-bottom: 14px;
        }
        .paper-sign-step2__get-file, .paper-sign-upload{
            display: inline-block;
            margin-right: 5px;
        }
        .paper-sign-step2-file{
            margin-top: 10px;
            line-height: 14px;
            font-size: 12px;
            color: #666666;
        }
    }
}
</style>
