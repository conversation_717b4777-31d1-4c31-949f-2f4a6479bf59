import ApprovalValidation from '../ApprovalValidation.vue';
import { initWrapper } from 'src/testUtils';
import flushPromises from 'flush-promises';

jest.mock('src/components/el_i_delete/ElIDelete.vue', () => ({
    template: '<div></div>',
}));
describe('ApprovalValidation', () => {
    const mockGetFun = jest.fn().mockResolvedValue({ data: [{ type: 1, code: 'test' }] });
    const mockPostFun = jest.fn().mockResolvedValue({});
    const mockSuccessFun = jest.fn().mockResolvedValue({});
    const mockPushFun = jest.fn();
    const mockOfflineTipFun = jest.fn().mockResolvedValue(true);
    const baseStoreOptions = {};
    const baseWrapperOptions = {
        propsData: {
            channel: 'notice',
        },
        mocks: {
            $http: {
                get: mockGetFun,
                post: mockPostFun,
            },
            $hybrid: {
                offlineTip: mockOfflineTipFun,
            },
            $MessageToast: {
                success: mockSuccessFun,
            },
            $router: {
                push: mockPushFun,
            },
            $sensors: {
                track: jest.fn(),
            },
        },
    };
    const wrapper = initWrapper(ApprovalValidation, { ...baseStoreOptions }, { ...baseWrapperOptions });
    test('审批校验', async() => {
        wrapper.vm.handleClickConfirmApproval();
        await flushPromises();
        expect(mockPushFun).toHaveBeenCalledWith('/sign/sign-tip?status=4&contractId=undefined&type=approval');
        expect(wrapper.vm.loading).toBeFalsy();
    });
    test('获取通知类型', async() => {
        wrapper.vm.getNoticeType();
        await flushPromises();
        expect(wrapper.vm.sendType).toEqual({ 'type': 'E' });
    });
});
