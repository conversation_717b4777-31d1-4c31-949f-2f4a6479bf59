<template>
    <div class="ApprovalValidation-comp clear" v-loading.fullscreen.lock="loading">
        <div class="result">
            <i :class="approvalRes == 'APPROVAL_RESULT_PASS'
                ? 'el-icon-ssq-tongguo'
                : 'el-icon-ssq-tishi1'"
            ></i>
            <span>{{ approvalRes == 'APPROVAL_RESULT_PASS' ? $t('sign.approveAgree') : $t('sign.approveReject') }}</span>
        </div>
        <!-- 阻止默认按enter会提交表单刷新页面的事件 -->
        <el-form @submit.native.prevent>
            <el-form-item :label="$t('sign.approvePlace_1')">
                <el-input
                    type="textarea"
                    @keyup.enter.native="handleClickConfirmApproval"
                    :placeholder="$t('sign.approvePlace_2')"
                    :rows="4"
                    :maxlength="255"
                    v-model="approvalOpinion"
                >
                    <ElIDelete slot="icon"></ElIDelete>
                </el-input>
            </el-form-item>
        </el-form>
        <div class="dialog-footer-btns-wrap clear">
            <div class="dialog-footer-btns fr">
                <button class="ssq-btn-confirm"
                    @click="handleClickConfirmApproval"
                    :disabled="false"
                >
                    {{ $t('sign.submit') }}
                </button>
                <button class="ssq-btn-cancel"
                    @click="handleClickCancelApproval"
                >
                    {{ $t('sign.cancel') }}
                </button>
            </div>
        </div>
    </div>
</template>

<script>
import ElIDelete from 'components/el_i_delete/ElIDelete.vue';
export default {
    components: {
        ElIDelete,
    },
    // eslint-disable-next-line vue/require-prop-types
    props: ['channel', 'contractId', 'receiverId', 'approvalResult', 'userAccount', 'sensorsTrackContractInfo', 'contractData'],
    data() {
        return {
            approvalOpinion: '', // 审批意见
            approvalRes: this.approvalResult,
            loading: false,
        };
    },
    watch: {
        approvalResult(v) {
            this.approvalRes = v;
        },
    },
    methods: {
        // 审批校验
        async handleClickConfirmApproval() {
            const res = await this.$hybrid.offlineTip({ operate: this.$t('sign.approved') });
            if (!res) {
                return;
            }
            this.loading = true;
            this.postApprovalConfirm({
                receiverId: this.receiverId,
                approvalResult: this.approvalRes,
                approvalOpinion: this.approvalOpinion,
                signPlatform: 'WEB',
            })
                .then((res) => {
                    this.$sensors.track({
                        eventName: `Ent_ContractApproval_Result`,
                        eventProperty: {
                            page_name: `合同审批页`,
                            is_success: true,
                            content: this.approvalOpinion,
                            icon_name: this.approvalResult === 'APPROVAL_RESULT_PASS' ? '同意' : '驳回',
                            request_url: res.config?.url,
                            contract_sender: this.contractData?.sendAccount || null,
                            ...this.sensorsTrackContractInfo,
                        },
                    });
                    if (this.channel === 'notice') {
                        return this.$MessageToast.success(this.$t('sign.approveDone'))
                            .then(() => {
                                // 先退出登录
                                const status = this.approvalResult === 'APPROVAL_RESULT_PASS' ? 5 : 4;
                                this.$router.push(`/sign/sign-tip?status=${status}&contractId=${this.contractId}&type=approval`);
                            });
                    }
                    this.$emit('close');
                    this.$emit('operate-next-contract');
                })
                .catch((err) => {
                    const errMsg = err.response?.data?.message;
                    const status = err.response?.status;
                    const code = err.response?.data?.code;
                    this.$sensors.track({
                        eventName: `Ent_ContractApproval_Result`,
                        eventProperty: {
                            page_name: `合同审批页`,
                            is_success: false,
                            content: this.approvalOpinion,
                            icon_name: this.approvalResult === 'APPROVAL_RESULT_PASS' ? '同意' : '驳回',
                            request_url: err.config?.url,
                            fail_http_code: status,
                            fail_error_code: code,
                            fail_reason: errMsg,
                            ...this.sensorsTrackContractInfo,
                        },
                    });
                })
                .finally(() => this.loading = false);
        },
        handleClickCancelApproval() {
            this.$emit('close');
        },

        // Ajax
        getNoticeType() {
            return this.$http.get(`/users/notifications`);
        },
        postApprovalConfirm(data) {
            return this.$http.post(`${signPath}/contracts/${this.contractId}/approval`, data);
        },
    },
    created() {
        this.getNoticeType()
            .then(res => {
                this.mailNotice = res.data.filter(item => {
                    return +item.type === 1;
                })[0] || '';
                this.phoneNotice = res.data.filter(item => {
                    return +item.type === 2;
                })[0] || '';
                this.notice = this.phoneNotice ? this.phoneNotice.code : this.mailNotice.code;
                this.sendType = this.phoneNotice ? { type: 'S' } : { type: 'E' };
            })
            .then(() => {
                this.$watch('sendType', function() {
                    this.send();
                });
            });
    },
};
</script>

<style lang="scss">
	.ApprovalValidation-comp {
		.result {
			margin-top: 25px;
			margin-bottom: 38px;
			text-align: center;
			font-size: 16px;
			i {
				font-size: 24px;
				&.el-icon-ssq-tongguo {
					color: #2baa3f;
				}
				&.el-icon-ssq-tishi1 {
					color: #f86c27;
				}
			}
			i, span {
				vertical-align: middle;
			}
		}
		input {
			float: left;
			width: 330px;
			height: 32px;
		}
		.forgetPass-form-item {
			float: right;
			width: 60px;
			text-align: right;
			color: #127fd2;
			margin-top: -4px;
			margin-bottom: 5px;
			cursor: pointer;
		}
		form {
			padding: 0 33px;
      border-bottom: 1px solid #eee;
      margin-bottom: 15px;
		}
		.dialog-footer-btns-wrap {
			padding-right: 33px;
			padding-bottom: 18px;
			background-color: #fff;
			border-radius: 4px;
		}
		.dialog-footer-btns {
			margin-top: 16px;
			button {
				font-size: 14px;
			}
			.ssq-btn-confirm {
				width: 100px;
				height: 34px;
				border: 0;
				margin-right: 6px;
			}
			.ssq-btn-cancel {
				width: 64px;
				height: 34px;
				border: 1px solid #ccc;
			}
		}
		.el-form-item__label {
			font-size: 14px;
			text-align: left!important;
			padding: 0 0 10px 0px;
		}
		.el-form-item {
			margin-bottom: 0;
			// &:last-child {
			// 	margin-top: 22px;
			// }
		}
    }
    .ApprovalValidation-comp .el-form .el-form-item .el-form-item__label {
        display: block;
        width: auto;
    }
</style>
