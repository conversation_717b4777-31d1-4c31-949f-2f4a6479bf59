<!-- 签署——签署页 -->
<template>
    <div class="signing" v-loading="isLoading" id="signPage">
        <SignHeader
            v-if="docList.length"
            :contractId="contractId"
            :signType="signType"
            :receiver="receiver"
            :parentReceiverId="approvalDetailReceiverId"
            :hdVisible="hdVisible"
            :ssoSigning="ssoSigning"
            :contractData="contractData"
            :hybridContractServerConnected="hybridContractServerConnected"
            :signPlaceBySigner="signPlaceBySigner"
            :signerDragLabelTypeList="signerDragLabelTypeList"
            :signatureLabels="signatureLabels"
            :dateLabels="dateLabels"
            :isEnt="isEnt"
            :rText="rText"
            :returnUrl="returnUrl"
            :hidePass="hidePass"
            :channel="channel"
            :noRight="!!permission"
            :isShowRidingSeal="isShowRidingSeal"
            :isShowRidingSealBtn="isShowRidingSealBtn"
            :isReading.sync="isReading"
            :isScrollLimitSign="isScrollLimitSign"
            :contractAlias="contractAlias"
            :sensorsTrackContractInfo="sensorsTrackContractInfo"
            :ifSignerViewApprovalDetail="ifSignerViewApprovalDetail"
            @closeIfSignerViewApprovalDetail="ifSignerViewApprovalDetail = false"
            @open-sign-place-dialog="openSignPlaceGuideDialog"
            @ridigSeal-click="handleRridingSealClick"
            @approval-reject-click="handleClickReject"
            @to-next="beforeNext"
            @drag-label="handleDragLabelBySigner"
            :isFromSignAuthorization="isFromSignAuthorization"
            :showShareViewBtn="canShareReview && !(isHybridCloudContract && $hybrid.isAlpha())"
            @toggleToRenderImg="toggleRenderImg=true"
        ></SignHeader>

        <div class="wrap" :class="{'hd-hidden':!hdVisible, 'ft-hidden': !ftVisible}" v-if="docList.length && hybridContractServerConnected">
            <Doc
                ref="doc"
                :contractId="contractId"
                :docList="docList"
                :signType="signType"
                :isReading="isReading"
                :isScrollLimitSign="isScrollLimitSign"
                :isHybridCloudContract="isHybridCloudContract"
                :receiver="receiver"
                :labels="labels"
                :hasRidingSeal="isShowRidingSeal || decorateRidingSealVisible"
                :signerDragAble="signerDragAble"
                :signPlaceBySigner="signPlaceBySigner"
                :signerDragLabelTypeList="signerDragLabelTypeList"
                :signDecision="signDecision"
                :activeTab.sync="activeTab"
                :contractData="contractData"
                :attachmentList="attachmentList"
                :bottomSignBtnVisible="bottomSignBtnVisible"
                :decorateRidingSealVisible="decorateRidingSealVisible"
                :decorateRidingSealVos="decorateRidingSealVos"
                :isEnt="isEnt"
                :subjectName="subjectName"
                :otherEntSubject="otherEntSubject"
                :mySeals="mySeals"
                :sealsLength="sealsLength"
                :signatures="signatures"
                :isShowRidingSeal="isShowRidingSeal"
                :ridingSealData="ridingSealData"
                :isRenderPdf="isRenderPdf"
                :isNoPermissionPage="!!permission"
                :signOperationType="signOperationType"
                :ifPersonalCanPhraseSegmentation="ifPersonalCanPhraseSegmentation"
                :jaEntHasNotAuthenticated="jaEntHasNotAuthenticated"
                @checkSignQualified="checkSignQualified"
                @handleClickHandWrite="handleClickHandWrite"
                @handleClickLabels="handleClickLabels"
                @handleClickTransfer="handleClickTransfer"
                @handleClickCancel="handleClickCancel"
                @to-next="beforeNext"
                @to-auth="goToAuth"
                @clickOtherEntSubjectBtn="clickOtherEntSubjectBtn"
                @handleClickAddHandWrite="handleClickAddHandWrite"
                @handleAddSeal="handleAddSeal"
                @addRridingSeal="addRridingSeal"
                @updateRridingSealData="updateRridingSealData"
                @updateRridingSealToServer="updateRridingSealToServer"
                @switchRridingSeal="switchRridingSeal"
                :sensorsTrackContractInfo="sensorsTrackContractInfo"
            ></Doc>
        </div>
        <div class="wrap"
            :class="{'hd-hidden':!hdVisible, 'ft-hidden': !ftVisible}"
            v-else-if="!hybridContractServerConnected"
        >
            <div class="net-error-container">
                <div class="tip-img"></div>
                <h4>{{ $t('sign.cannotReview') }}</h4>
                <p>{{ $t('sign.connectFail') }}</p>
                <p>{{ $t('sign.connectFailTip') }}</p>
                <p>{{ $t('sign.connectFailTip1') }}</p>
                <p>{{ $t('sign.connectFailTip2') }}</p>
                <p>{{ $t('sign.connectFailTip3') }}</p>
            </div>
        </div>
        <RegisterFooter v-if="ftVisible" class="footer"></RegisterFooter>

        <!-- 对话框：选择签名 -->
        <el-dialog class="signatures" :title="$t('sign.selectSignature')" :visible.sync="signaturesListVisible" size="middle">
            <div class="signatures-select" v-if="showReplaceAllBtn">
                <el-checkbox v-model="replaceAllSealAndSignature">{{ $t('handwrite.replaceAllSignature') }}</el-checkbox>
            </div>
            <div class="signatures-container">
                <div
                    v-for="(signature, signatureIndex) in signatures"
                    class="signature"
                    :class="signature.sigId === curSelectedSignature.signatureId ? 'active': ''"
                    :key="signatureIndex"
                    @click="handleClickSignature(signature.sigId, action)"
                >
                    <i class="el-icon-ssq-danxuanxuanzhong" v-if="signature.sigId === curSelectedSignature.signatureId"></i>
                    <img :src="signature.ImgUrl" :alt="$t('sign.signature')" width="100%" height="100%">
                </div>
            </div>
        </el-dialog>

        <!-- 对话框：手写签名面板 -->
        <el-dialog
            class="handWrite-dialog"
            :title="$t('sign.signature')"
            size="tiny"
            :close-on-click-modal="false"
            :visible.sync="handleWriteTabVisible"
            @open="HandleWritePanelKey=Math.random()"
        >
            <!--这里有两种场景，一种是正常已经实名的签署合同'sign-single'，另一张是新注册未实名的进来则会走'sign-all'-->
            <HandleWritePanel
                :key="HandleWritePanelKey"
                :initialBusiness="handWriteBusiness"
                :parentGift="HandleWritePanelParentGift"
                @write-done="onWriteDone(data)"
                :operateAction="action"
                @close="handleWriteTabVisible = false"
                @updateSignature="updateSignature"
            >
            </HandleWritePanel>
        </el-dialog>

        <!-- 对话框：印章列表 -->
        <SealSelectDialog
            v-if="sealListVisible"
            :contractId="contractId"
            :docLen="docList.length"
            :focusingMark="focusingMark"
            :my-seals="mySeals"
            :other-seals="otherSeals"
            :sealListVisibleFromAction="sealListVisibleFromAction"
            :visible="sealListVisible"
            :signType="receiver.signType"
            :curRidingSealId="curRidingSealId"
            :sensorsTrackContractInfo="sensorsTrackContractInfo"
            :receiver="receiver"
            @handleClickSeal="handleClickSeal"
            @postSeal="handleClickOwner"
            @close="sealListVisible=false"
        ></SealSelectDialog>

        <!-- 对话框：转给其他人 -->
        <el-dialog class="transferDialog"
            :title="$t('sign.selectSigner')"
            size="small"
            :visible.sync="transferDialogVisible"
            @close="CompanyDeptAndMemberKey=Math.random()"
        >
            <CompanyDeptAndMember
                :key="CompanyDeptAndMemberKey"
                selectType="radio"
                :onlyActive="true"
                @choose="onCompanyDeptAndMemberChoose"
            >
            </CompanyDeptAndMember>
        </el-dialog>

        <!-- 对话框：签约校验 -->
        <el-dialog
            v-if="ifShowSignVaDialog"
            class="ssq-form sign-el-dialog sign-va-dialog"
            :class="{'sign-el-dialog_bigger': $i18n.locale === 'ru'}"
            :title="$t('signPC.contractVerification')"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :visible.sync="signVaDialogVisible"
            :before-close="handleValidateClose"
        >
            <SignValidation ref="SignValidation"
                :receiver="receiver"
                :contractAliasText="contractAliasText"
                :visible="signVaDialogVisible"
                :channel="channel"
                :isHybridCloudContract="isHybridCloudContract"
                :signerFillLabels="textLabels"
                :returnUrl="returnUrl"
                :contractId="contractId"
                :receiverId="receiver.receiverId"
                :hidePass="hidePass"
                :userAccount="receiver.userAccount"
                :isAllSignaturesInactive="isAllSignaturesInactive"
                :isApplySeal="isApplySeal"
                :signType="receiver.signType"
                :isReuseVerifyCode="isReuseVerifyCode"
                :showSignPasswordPriority="showSignPasswordPriority"
                :sensorsTrackContractInfo="sensorsTrackContractInfo"
                :showSignPasswordEntry="showSignPasswordEntry"
                :isSignPasswordPriorityOpen="isSignPasswordPriorityOpen"
                :shouldSignVaSecondCheck="shouldSignVaSecondCheck"
                :contractData="contractData"
                @loading="onLoading"
                @toFace="changeValidationToFace"
                @openSetSignPwdDialog="ifOpenSetSignPwdDialog(true)"
            >
            </SignValidation>
        </el-dialog>

        <SetSignPwdDialog
            :visible.sync="showSetSignPwdDialog"
            @signPwdSetSuccess="ifOpenSetSignPwdDialog(false)"
            @openSignVaDialog="signVaDialogVisible = true"
        >
        </SetSignPwdDialog>

        <!-- 对话框：审批校验 -->
        <el-dialog class="ssq-form sign-el-dialog ApprovalValidationDialog"
            :title="approvalResult == 'APPROVAL_RESULT_PASS' ? $t('sign.approveAgree') : $t('sign.approveReject')"
            :visible.sync="approvalVaDialogVisible"
        >
            <ApprovalValidation ref="ApprovalValidation"
                :channel="channel"
                :contractId="contractId"
                :receiverId="receiver.receiverId"
                :approvalResult="approvalResult"
                :userAccount="receiver.userAccount"
                :returnUrl="returnUrl"
                :sensorsTrackContractInfo="sensorsTrackContractInfo"
                :contractData="contractData"
                @operate-next-contract="handleOperateNextContract"
                @close="handleCancelApproval"
            >
            </ApprovalValidation>
        </el-dialog>
        <!-- 对话框：风险提醒 -->
        <RiskTip
            :visible.sync="riskTipDialogVisible"
            :contractAliasText="contractAliasText"
            :riskFieldNumber="receiver.riskFieldNumber"
            @handleHighLight="handleContractHighLight"
            @confirm="handleConfirmRisk"
            :isOfdContract="isOfdContract"
        ></RiskTip>

        <!-- 对话框：二维码打开 -->
        <FaceQrValidDialog
            v-if="qrSignDialogVisible"
            :receiver="receiver"
            :changeFaceToValidationShow="changeFaceToValidationShow"
            @close="qrSignDialogVisible=false"
            @showServiceDetail="personInfoProtectDialogShow=true"
            @switch-validate="changeFaceToValidation"
            @faceDone="toSign"
        ></FaceQrValidDialog>
        <!-- 对话框：双录二维码打开 -->
        <DualDialog
            v-if="dualSignDialogVisible"
            :receiver="receiver"
            @close="dualSignDialogVisible=false"
            @showServiceDetail="personInfoProtectDialogShow=true"
            @dualDone="toSign"
        ></DualDialog>
        <PersonInfoProtectDialog
            :visible.sync="personInfoProtectDialogShow"
            :is-sign-face="true"
            @close="qrSignDialogVisible=true"
        ></PersonInfoProtectDialog>

        <!-- 已认证企业还未添加任何印章时生成的默认电子章 -->
        <CreateElectronicSeal
            :has-register="hasRegister"
            :channel="channel"
            :createElectronicSealShow.sync="showDefaultElectronicSeal"
            @createElectronicSealDone="createElectronicSealDoneFun"
        >
        </CreateElectronicSeal>

        <!-- 未认证企业章 -->
        <SsqDialog class="appsigning-unauthseal-dialog" v-model="unauthSeal">
            <UnauthSeal slot="content"
                @click-unauth-seal="onClickUnauthSeal"
                @to-auth="goToAuth"
            >
            </UnauthSeal>
        </SsqDialog>

        <!-- 签约条件相关弹窗 -->
        <SignQualifiedDialog
            :visible.sync="signQualifiedDialogVisible"
            ref="signQualifiedDialog"
            :receiver="receiver"
            :isEnt="isEnt && signDecision !== 'NOT_MATCH_ENTER_AUTHENTICATE'"
            :subjectName="subjectName"
            :contractData="contractData"
            :signDecision="signDecision"
            :contractAliasText="contractAliasText"
            :isNoPermissionPage="!!permission"
            @to-auth="goToAuth"
            @use-default-identity="handleUseDefaultIdentity"
        >
        </SignQualifiedDialog>
        <JaSignQualifiedDialog
            :isEnt="isEnt"
            :receiver="receiver"
            :signDecision="signDecision"
            :visible.sync="jaSignQualifiedDialogVisible"
        >
        </JaSignQualifiedDialog>
        <JaSettingTwoFactorDialog
            v-if="showJaSettingTwoFactorDialog"
            :visible.sync="showJaSettingTwoFactorDialog"
            @confirmSaveSetting="showJaEncryptionAndTwoFactorDialog = true"
        ></JaSettingTwoFactorDialog>
        <JaEncryptionAndTwoFactorDialog
            v-if="showJaEncryptionAndTwoFactorDialog"
            :visible.sync="showJaEncryptionAndTwoFactorDialog"
            :encryptionType="encryptionType"
            @confirmSavePassword="handleEncryptionSign"
        ></JaEncryptionAndTwoFactorDialog>
        <!-- 证书续期弹窗 -->
        <CerticicationRenewalDialog
            :visible.sync="renewalDialogVisible"
            :certificationInfos="certificationInfos"
            :renewalType="renewalType"
            @renewal-confirm="handleClickRenewal"
            @reAuthHandle="reAuthHandle"
        >
        </CerticicationRenewalDialog>

        <!--证书续期二要素失败 && 【无账户管理权限】弹窗-->
        <el-dialog class="sign-dialog-ca-fail"
            :title="$t('signIdentityGuide.title')"
            :visible.sync="caFailDialogVisible"
        >
            <div class="ca-fail">
                <p>{{ $t('certificationRenewalDialog.tip7') }}</p>
                <p>{{ $t('certificationRenewalDialog.tip8', { adminEmpName: caFailedInfo.adminEmpName,adminAccount: caFailedInfo.adminAccount}) }} </p>
                <el-button type="primary" class="copy-btn" @click="handleCopyBtnClick">{{ $t('certificationRenewalDialog.tip9') }}</el-button>
            </div>
            <div slot="footer">
                <el-button type="primary" @click="caFailDialogVisible = false">{{ $t('sealInconformityDialog.confirm') }}</el-button>
            </div>
        </el-dialog>

        <!-- 集团代认证 -->
        <signIdentityGuide
            class="normal-padding-dialog"
            :isEnt="isEnt"
            :senderName="senderName"
            :sendAccount="contractData.sendAccount"
            :contractTitle="contractData.contractTitle"
            :receiver="receiver"
            ref="signIdentityGuide"
        ></signIdentityGuide>

        <!-- 签署前审批 -->
        <el-dialog class="ssq-dialog sign-dialog-approval"
            :title="$t('sign.applyToSign')"
            :visible.sync="dialogApproval"
        >
            <ApprovalApp
                :definesData="definesData"
                :disablePrivateMessage="true"
                :tip="$t('sign.autoRemindAfterApproval')"
                @apply-approval="onApplyApproval"
            >
            </ApprovalApp>
        </el-dialog>

        <!-- 实名信息变更检测弹窗 -->
        <AuthInfoChangeDialog
            :authChangeData="authChangeData"
            v-if="authInfoChangeDialogShow"
            :dialogVisible="authInfoChangeDialogShow"
            @closeAuthInfoChangeDialog="handleCloseAuthInfoChangeDialog"
        ></AuthInfoChangeDialog>

        <!-- 合同作废消息弹窗 -->
        <invalidContractDialog
            v-if="invalidContractDialogShow"
            :dialogVisible="invalidContractDialogShow"
            @closeInvalidContractDialog="handleCloseInvalidContractDialog"
        ></invalidContractDialog>
        <!-- 申请恢复专用章弹窗 -->
        <RecoverSpecialSealDialog
            :dialog-visible.sync="showRecoverSealPostDialog"
            :special-seal-info="specialSealInfo"
            :contract-title="contractData.contractTitle"
            :contract-alias="contractAlias"
            :sender-ent-name="contractData.sendUserEnterpriseName"
        ></RecoverSpecialSealDialog>
        <!-- 合同高亮 -->
        <ApprovalHighLight
            :isHybridCloudContract="isHybridCloudContract"
            :dialog-visible.sync="showApprovalHighLightDialog"
            :sensorsTrackContractInfo="sensorsTrackContractInfo"
        ></ApprovalHighLight>
        <!-- 纸质签署引导弹窗 -->
        <PaperSignDialog
            v-model="paperSignDialogVisible"
            :paperSignInfo="paperSignInfo"
            :contractData="contractData"
            :contractId="contractId"
            :sensorsTrackContractInfo="sensorsTrackContractInfo"
        ></PaperSignDialog>
        <AllowPaperSignDialog :visible.sync="allowPaperSignDialogShow" @confirm="paperOrElectronSign" :senderName="senderName" :receiverName="subjectName" :contractId="contractId"></AllowPaperSignDialog>
        <!--第三方审批不通过弹窗-->
        <ThirdApprovalMessageBox
            :visible.sync="thirdPartApprovalDialogShow"
            :cancelBtnText="$t('thirdPartApprovalDialog.cancelBtnText')"
            :confirmBtnText="thirdApprovalDialog.confirmBtnText"
            :dialogTitle="thirdApprovalDialog.title"
            :dialogContent="thirdApprovalDialog.content"
            @cancel="handleThirdPartApproval"
            @confirm="thirdPartApprovalDialogShow = false"
            :showCancelBtn="!viewThirdApproval"
        >
        </ThirdApprovalMessageBox>
        <SealInconformityDialog
            v-model="isErrorDialogShow"
            :ocrEntData="ocrEntData"
            @confirm="toSign"
        />
        <!--提前结束签署弹窗-->
        <EndSignEarlyPrompt
            :visible.sync="showEndSignEarlyPrompt"
            :cancelBtnText="$t('endSignEarlyPrompt.cancel')"
            :confirmBtnText="$t('endSignEarlyPrompt.confirm')"
            :dialogTitle="$t('endSignEarlyPrompt.signPrompt')"
            :dialogContent="thirdApprovalDialog.content"
            @cancel="showEndSignEarlyPrompt = false"
            @confirm="afterEndSignEarlyPrompt"
        >
            <div slot="content" v-if="showEndSignEarlyPrompt">
                <p>{{ $t('endSignEarlyPrompt.signatureTip',{count: endSignEarlyData.sumSignature}) }}</p>
                <div class="prompt-center">
                    <p>{{ $t('endSignEarlyPrompt.hasSigned',{count: endSignEarlyData.doneSignature}) }}</p>
                    <p>{{ $t('endSignEarlyPrompt.hasNotSigned',{count: endSignEarlyData.undoneSignature}) }}</p>
                </div>
                <div v-if="endSignEarlyData.extraTip">{{ $t('endSignEarlyPrompt.noNeedSealTip') }} </div>
            </div>
        </EndSignEarlyPrompt>
        <!-- 合同高亮提示按钮 -->
        <div v-if="isShowHighLight" class="contract-high-light-tip-btn" @click="handleContractHighLight">
            <i class="el-icon-ssq-gaoliangtishiweb"></i>
        </div>
        <el-dialog
            :title="$t('signJa.createSeal.title')"
            class="create-seal-dialog"
            :visible.sync="createJaSealDialogVisible"
            width="width"
            :before-close="closeCreateSealDialog"
        >
            <div>
                <span>{{ $t('signJa.createSeal.tip') }}</span>
                <el-input v-model="personSealName"></el-input>
            </div>
            <div slot="footer">
                <el-button @click="closeCreateSealDialog">{{ $t('sealInconformityDialog.cancel') }}</el-button>
                <el-button type="primary" @click="createPersonSeal">{{ $t('sealInconformityDialog.confirm') }}</el-button>
            </div>
        </el-dialog>
        <!-- 【新版本】签署方指定签署位置弹窗 本期仅支持个人签字 -->
        <SignPlaceBySignerGuide
            :isEnt="isEnt"
            :step="signPlaceGuideStep"
            :visible="isShowSignPlaceGuideDialog"
            @close="closeSignPlaceGuideDialog"
        ></SignPlaceBySignerGuide>
        <signPlaceBySignerTip
            :visible="isShowSignPlaceTipDialog"
            :signatureLabels="signatureLabels"
            @close="closeSignPlaceTipDialog"
        ></signPlaceBySignerTip>
        <!-- 对话框:继续审批下一份合同 -->
        <ContinueOperateNextDialog
            v-if="isShowContinueOperateNextDialog"
            :visible="isShowContinueOperateNextDialog"
            :contractId="contractId"
            :approvalDetailReceiverId="approvalDetailReceiverId"
            :defType="receiver.defType"
            @close="closeContinueOperateNextDialog"
        ></ContinueOperateNextDialog>
        <Hubble v-if="showChatting" @handleChat="handleChat"></Hubble>
        <NewFunctionInstruct class="news-enty ja-page-hidden" :sensorsTrackContractInfo="sensorsTrackContractInfo"></NewFunctionInstruct>
        <!-- 签署方付费 快捷支付弹窗 -->
        <QuickPay
            v-if="showSignerQuickPayDialog"
            :dialogVisible="showSignerQuickPayDialog"
            type="signerPay"
            :signerPayOrderInfo="signerPayOrderInfo"
            @handlePaySuccess="showSignerQuickPayDialog = false"
            @handlePayCancel="showSignerQuickPayDialog = false"
        >
        </QuickPay>
        <AddSealDialog :visible.sync="addSealVisible" :addSealParam="addSealParam" @click-label="handelAddSeal"></AddSealDialog>
    </div>
</template>
<script>
import {  mapState, mapGetters } from 'vuex';
import authPath from 'src/common/utils/authPath';
import { signCodeStatusMap } from 'src/common/utils/dictionary';
import SignHeader from './header';
import RegisterFooter from 'src/components/register_footer/RegisterFooter.vue';
import PersonInfoProtectDialog from 'src/components/infoProtectDialog';
import HandleWritePanel from './handleWritePanel/HandleWritePanel.vue';
import SignValidation from './signValidation/SignValidation.vue';
import ApprovalValidation from './approvalValidation/ApprovalValidation.vue';
// import ApprovalDetail from './approvalDetail/ApprovalDetail.vue';
import CompanyDeptAndMember from 'src/pages/enterprise/console/common/CompanyDeptAndMember.vue';
import CreateElectronicSeal from './createElectronicSeal/CreateElectronicSeal.vue';
import FaceQrValidDialog from './faceQrValidateDialog/FaceQrValidate';
import DualDialog from './dualDialog/index.vue';
import Bus from 'components/bus/bus.js';
import RiskTip from './riskTipDialog/RiskTipDialog.vue';
import SealSelectDialog from './sealSelectDialog';
import SignQualifiedDialog from './signQualifiedDialog/SignQualifiedDialog';
import UnauthSeal from './unauthSeal/UnauthSeal.vue';
import SsqDialog from 'components/ssqDialog/SsqDialog.vue';
import { hasPrivilege } from 'src/common/utils/headInfo.js';
import { scrollToYSmooth } from 'src/common/utils/dom.js';
import { dateGenerator } from 'src/common/utils/fn.js';
import { goReturnUrl } from 'src/common/utils/returnUrl.js';
import CerticicationRenewalDialog from 'components/certificationRenewalDialog/CertificationRenewalDialog';
import { createEnterpriseMinxin } from 'src/mixins/index.js';
import signIdentityGuide from 'src/components/signIdentityGuide/index.vue';
import { isPC } from 'utils/device.js';
import { defaultSealWidth } from 'utils/commonVar.js';
import { encodeSearchParams, joinPathnameAndQueryObj } from 'utils/getQueryString.js';
import ApprovalApp from 'foundation_pages/sign/field/approvalApp/ApprovalApp.vue';
import { mobileRouteObj } from 'src/common/utils/routeVariable.js';
import Doc from './doc';
import AuthInfoChangeDialog from 'components/authInfoChangeDialog';
import invalidContractDialog from 'components/invalidContractDialog';
import RecoverSpecialSealDialog from 'foundation_pages/sign/signing/recoverSpecialSealDialog';
import ApprovalHighLight from './approvalHighLightDialog/index';
import { needToAuthInterceptPage } from 'src/utils/pageIntercept.js';
import PaperSignDialog from './paperSignDialog/index.vue';
import AllowPaperSignDialog from './allowPaperSignDialog/index.vue';
import ThirdApprovalMessageBox from './messageBox/index.vue';
import SealInconformityDialog from './sealInconformityDialog/index.vue';
import { getSealOcrRecognition, getEntSeals, syncAllSignLabel } from 'src/api/sign.js';
import EndSignEarlyPrompt from './messageBox/index.vue';
// 日文版签署前实名认证弹窗
import JaSignQualifiedDialog from 'components/jaSignQualifiedDialog';
import JaSettingTwoFactorDialog from 'components/jaSettingTwoFactorDialog';
import JaEncryptionAndTwoFactorDialog from 'components/jaEncryptionAndTwoFactorDialog';
// 【新版本】签署方指定签署位置引导弹窗
import SignPlaceBySignerGuide from 'components/signPlaceBySignerGuide';
import signPlaceBySignerTip from './signPlaceBySignerTip';
import { markInfo } from 'src/pages/foundation/sign/common/info/info.js';
import { markCoordinateTransform } from 'src/common/utils/fn.js';
import ContinueOperateNextDialog from './continueOperateNextDialog';
import Hubble from 'src/pages/common/hubble/components/entrance';
import SetSignPwdDialog from './setSignPwdDialog/index.vue';
import NewFunctionInstruct from './newFunctionInstruct';
import QuickPay from 'src/components/QuickPay';
import AddSealDialog from './addSealDialog/index.vue';

export default {
    components: {
        QuickPay,
        SetSignPwdDialog,
        JaSettingTwoFactorDialog,
        JaEncryptionAndTwoFactorDialog,
        RecoverSpecialSealDialog,
        ApprovalHighLight,
        SignHeader,
        RegisterFooter,
        HandleWritePanel,
        SignValidation,
        ApprovalValidation,
        // ApprovalDetail,
        CompanyDeptAndMember,
        CreateElectronicSeal,
        RiskTip,
        UnauthSeal,
        SsqDialog,
        SignQualifiedDialog,
        CerticicationRenewalDialog,
        signIdentityGuide,
        ApprovalApp,
        Doc,
        SealSelectDialog,
        FaceQrValidDialog,
        AuthInfoChangeDialog,
        PersonInfoProtectDialog,
        invalidContractDialog,
        PaperSignDialog,
        AllowPaperSignDialog,
        ThirdApprovalMessageBox,
        SealInconformityDialog,
        EndSignEarlyPrompt,
        JaSignQualifiedDialog,
        SignPlaceBySignerGuide,
        signPlaceBySignerTip,
        ContinueOperateNextDialog,
        Hubble,
        NewFunctionInstruct,
        DualDialog,
        AddSealDialog,
    },
    mixins: [createEnterpriseMinxin],
    data() {
        return {
            // url的token信息
            urlToken: this.$route.query.token || '',
            // 是否需要签署/审批校验
            needVerify: true,
            status: 0,
            // 常量
            SPACE_HEIGHT: 20,

            isLoading: 1,
            hybridContractServerConnected: true,
            contractId: this.$route.query.contractId,
            archiveCollectContractId: this.$route.query.archiveCollectContractId,
            proxySignNominalEntId: this.$route.query.proxySignNominalEntId,
            returnUrl: ['null', ''].includes(this.$route.query.returnUrl) ? null : this.$route.query.returnUrl,
            signType: this.$route.query.type || 'sign', // send sign approval view
            channel: this.$route.query.channel || 'login', // 通过短信链接还是登录 notice login
            permission: this.$route.query.permission || '', // 是否为无权限审批
            approvalResult: '',
            contractAlias: 'CONTRACT', // 合同文本别名
            isOtherBtnShow: false,
            rText: (this.$route.query.type === 'sign' || this.$route.query.type === undefined) ? this.$t('signPC.commonSign') : this.signType === 'view' ? '' : this.$t('sign.approve'),

            changeFaceToValidationShow: false,

            // 角色基本信息
            account: '',
            signatures: [], // 我的签名
            mySeals: [],    // 我有权限使用的印章
            otherSeals: [], // 无权使用的印章

            // 印章拥有者
            owners: [
                {
                    account: '<EMAIL>', // 账号  string
                    empId: '', //   员工编号    number
                    empName: '111', // 员工名称    string
                    id: '', //  员工主键id  number
                },
            ],
            // 需要填写内容的标签类型
            writeRequiredLables: ['TEXT', 'BIZ_DATE', 'TEXT_NUMERIC', 'SINGLE_BOX', 'MULTIPLE_BOX', 'COMBO_BOX', 'PICTURE', 'NUMERIC_VALUE', 'DATE_TIME'],
            // 印章章或签名类型,询证章
            signRequiredLables: ['SEAL', 'SIGNATURE', 'CONFIRMATION_REQUEST_SEAL'],

            // 签名列表交互信息
            signaturesListVisible: false,
            handleWriteTabVisible: false,
            handWriteActive: 0,
            handWriteComponent: '',
            action: 'click',

            // 印章列表交互信息
            sealListVisible: false,
            sealListVisibleFromAction: 1, // 唤起印章弹窗/已实名无章弹窗 动作，1:普通印章切换点击，2:骑缝章印章点击
            selectedDocIdx: 0,
            selectedPageIdx: 0,
            selectedMarkIndex: 0,
            selectedLabelId: 0,
            selectedLabelValue: '',

            // 合同信息数据
            contractData: {},
            // 接收者和文档基本信息
            receiver: {},
            docList: [],

            // dialog
            isTagManageVisible: false,
            transferDialogVisible: false,
            CompanyDeptAndMemberKey: 0,
            signVaDialogVisible: false,
            HandleWritePanelKey: 0,
            approvalVaDialogVisible: false,
            approvalDetailDialogVisible: false,
            approvalDetailKey: 0,
            // fastLoginDialogVisible: false,
            qrSignDialogVisible: false,
            personInfoProtectDialogShow: false, // 个人信息保护提示弹窗
            qrSignKey: 0,
            riskTipDialogVisible: false,
            confirmRiskTipNextStep: false, // 是否点击确认风险提示下一步-继续签署
            riskDetailVisible: false,
            invalidContractDialogShow: false, // 点击签署时合同作废消息弹窗
            invalidContractNextStep: false, // 是否点击作废合同弹窗的下一步

            // watch
            newMark: {},
            focusingMark: null,
            // 已实名企业系统生成的默认电子章
            showDefaultElectronicSeal: false,
            // 未认证企业印章
            unauthSeal: false,
            // 非跨平台的合同，日本环境下的签署方企业是否实名
            jaEntHasNotAuthenticated: false,

            // 是否需要上传附件
            attachmentList: [],
            signerDragAble: false,     // 是否是自定义签署位置的合同
            signerDragLabelTypeList: null, // 新版本自定义签署位置合同可以拖动的标签类型列表
            signPlaceGuideStep: 1,
            signPlaceGuideCallback: null,

            // 签署条件
            signDecision: 'NORMAL',
            // 签署条件判断结果的弹窗
            signQualifiedDialogVisible: false,
            jaSignQualifiedDialogVisible: false,
            // 选择的签约主体id和名称
            switchEntId: '',
            switchName: '',
            ssoSigning: {}, // 单点登录配置
            needRenewal: true, // 是否需要续期
            ifJustUseBestSignCa: false, // 是否使用上上签Ca
            renewalType: {
                personNeedRenewal: false,
                entNeedRenewal: false,
            },
            renewalDialogVisible: false,
            caFailedInfo: {}, // 证书续期失败信息
            caFailDialogVisible: false, // 证书续期失败弹窗
            certificationInfos: [],
            // {
            //     realName: '',       // 个人主体
            //     entName: '',        // 企业主体
            //     caOrgCN: '',        // 证书颁发机构
            //     stopTime: '',       // 证书有效期
            //     serialNumber: '',    // 证书序列号
            // },
            // 所有印章都需要申请用印
            isAllSignaturesInactive: false,
            // 右侧tab， 'FILL'填写内容区，'PREVIEW'缩略图区
            activeTab: 'PREVIEW',
            // isApplySeal: false, // 申请用印
            isOtherSeal: false, // 申请他人盖章
            isSinoRussia: this.$cookie.get('sino-russia') === '1',
            isShowRidingSeal: false, // 是否展示合同的骑缝章模块
            ridingSealData: [], // 骑缝章 array，多文档页面高度可能不一样大，需要传数组形式
            hasRrePay: false, // 是否已经预扣
            isReading: false, // 展示签署前阅读头部展示
            isScrollLimitSign: false, // 是否是滚动限制阅读头部类型，1 时间要求15s，2 滑动到底部
            dialogApproval: false, // 签署前审批
            definesData: [], // 审批流
            decorateRidingSealVos: [], // 装饰骑缝章
            archiveToken: '', // 档案柜对应token
            transitionHrefBeforCollect: '', // 来源于签署的授权书，其原始签署url
            answerType: null, // 询证章选中的值

            authInfoChangeDialogShow: false, // 实名信息变更检测弹窗显示
            isFinishCheckAuthInfoChange: false, // 实名信息变更检测是否完成
            showRecoverSealPostDialog: false,
            showApprovalHighLightDialog: false,
            specialSealInfo: {}, // 待恢复专用章信息
            paperSignDialogVisible: false, // 纸质签署弹窗
            paperSignInfo: {
                paperSignMailInfo: {},
            }, // 纸质签署相关信息
            allowPaperSignDialogShow: false, // 允许纸质签署选择框
            ifExistThirdPartyApprove: false, // 是否存在第三方审批
            thirdPartApprovalDialogShow: false, // 第三方审批提示弹窗
            viewThirdApproval: false,
            // 印章校验
            isErrorDialogShow: false,
            ocrEntData: {},
            errorDialogType: 1,
            canShareReview: true, // 发件方乐高城是否开启转发审阅
            isReuseVerifyCode: false, // 发件方乐高城是否开启复用验证码
            isSignPasswordPriorityOpen: false, // 签署人是否打开优先签约密码校验
            isNotValidateSignVerificationCode: false, // 扫码签字开启无需签署校验
            showEndSignEarlyPrompt: false,  // 是否展示提前结束签署提示框
            ifInternalDecision: false, // 是否是提前结束签署（内部决议）合同,
            endSignEarlyType: '', // 提前结束签署签署人类型
            endSignEarlyData: {
                sumSignature: 0, // 签名总数
                doneSignature: 0, // 已完成签名数
                undoneSignature: 0, // 未完成签名数
                extraTip: '', // 提示信息
            },
            isContractHighLight: false, // 合同是否需要高亮
            createJaSealDialogVisible: false,
            personSealName: '',
            showJaSettingTwoFactorDialog: false,
            showJaEncryptionAndTwoFactorDialog: false,
            ifHasBindingTwoFactor: false,
            encryptionType: {
                encryptionSignConfig: null,
                twoFactorAuthentication: null,
            },
            encryptionSignParam: {
                password: '',
                dynamicCode: '',
            },
            isShowSignPlaceGuideDialog: false,
            isShowSignPlaceTipDialog: false,
            isShowContinueOperateNextDialog: false,
            showChatting: false,
            hasCrossPlatformConfirm: false, // 跨平台合同签署前确认
            curSelectedSignature: {},
            replaceAllSealAndSignature: false,
            curRidingSealId: '',
            authChangeData: {},
            sensorsTrackContractInfo: {},
            enterTime: 0,
            showSetSignPwdDialog: false,
            toggleRenderImg: false,
            isOtherSig: false, // 模板是否开启跨平台加签
            ifSignerViewApprovalDetail: false, // 是否是签署人查看审批详情
            showSignerQuickPayDialog: false,
            signerPayOrderInfo: {},
            hasGetReturn: false,
            dualSignDialogVisible: false,
            addSealVisible: false,
            addSealParam: {},
        };
    },
    provide() {
        return {
            checkSignQualified: this.checkSignQualified,
            goAfterSign: this.goAfterSign,
            updateSignatureData: this.updateSignatureData,
            showAddSealDialog: this.showAddSealDialog,
        };
    },
    computed: {
        ...mapState(['features', 'commonHeaderInfo', 'currentSignDocumentId']),
        ...mapGetters(['hasManager']),
        needAttach() {
            return this.attachmentList.length && this.attachmentList.some(item => !(item.fileList || []).length && item.necessary);
        },
        isShowRidingSealBtn() {
            // 乐高城开启老骑缝章&&签署合同&&签署操作&&未添加骑缝章&&页面loading完成
            if (!this.checkFeat.ridingSeal || this.signType !== 'sign' || this.signDecision !== 'NORMAL' || this.decorateRidingSealVisible || this.isLoading) {
                return false;
            }
            // 新模板必须开启跨平台加签
            return !this.commonHeaderInfo.openNewContractTemplate || this.isOtherSig;
        },
        isRenderPdf() {
            // const unsupportedBrowsers = isEdge() || isIE();
            // return !this.isOfdContract && !this.toggleRenderImg && !unsupportedBrowsers;
            return !this.isOfdContract && !this.toggleRenderImg;
        },
        sensorsEventName() {
            return this.signType === 'sign' ? 'ContractSign' : (this.signType === 'approval' ? 'ContractApproval' : '');
        },
        sensorsPageName() {
            return this.signType === 'sign' ? '合同签署页' : (this.signType === 'approval' ? '合同审批页' : '');
        },
        showReplaceAllBtn() {
            return !!this.curSelectedSignature.value;
        },
        ifShowSignVaDialog() {
            return this.signVaDialogVisible && !(this.encryptionType.encryptionSignConfig || this.encryptionType.twoFactorAuthentication);
        },
        // 可以拖动的标签类型列表长度不为0,表明可以拖动
        signPlaceBySigner() {
            return this.signerDragLabelTypeList && !!this.signerDragLabelTypeList.length;
        },
        thirdApprovalDialog() {
            return {
                content: this.viewThirdApproval ? this.$t('thirdPartApprovalDialog.content2') : this.$t('thirdPartApprovalDialog.content1'),
                title: this.viewThirdApproval ? this.$t('thirdPartApprovalDialog.title2') : this.$t('thirdPartApprovalDialog.title1'),
                confirmBtnText: this.viewThirdApproval ? this.$t('thirdPartApprovalDialog.iKnow') : this.$t('thirdPartApprovalDialog.confirmBtnText'),
            };
        },
        senderName() {
            const { contractData } = this;
            return contractData.sendUserEnterpriseId
                ? contractData.sendUserEnterpriseName
                : contractData.sendUserName;
        },
        // 签署类型：ONLY_PAPER_SIGN纸质签署,CHOOSE_PAPER_SIGN电子签/纸质签署,NO_PAPER_SIGN电子签署
        signOperationType() {
            const isHasPaperSign = ['ONLY_PAPER_SIGN', 'CHOOSE_PAPER_SIGN'].includes(this.paperSignInfo.operationEnum);
            // 作废合同，签署授权书不支持纸质签署
            if (isHasPaperSign && (this.isFromSignAuthorization || this.contractData.invalidContractId)) {
                return 'NO_PAPER_SIGN';
            }
            return this.paperSignInfo.operationEnum;
        },
        canPaperSign() {
            return ['ONLY_PAPER_SIGN', 'CHOOSE_PAPER_SIGN'].includes(this.signOperationType);
        },
        // 混合云host
        hybridServer() {
            return this.$store.state.commonHeaderInfo.hybridServer;
        },
        hybridAccessToken() {
            return this.$store.state.commonHeaderInfo.hybridAccessToken;
        },
        // 接收人信息
        userType() {
            return this.receiver.userType || '';
        },
        // 是否为企业用户
        isEnt() {
            return this.userType.toLowerCase() === 'enterprise';
        },
        isCanCreate() {
            return this.isCanCreateEnterprise || false;
        },
        // 合同别名文案
        contractAliasText() {
            return this.$t(`consts.contractAlias.${this.contractAlias.toLowerCase()}`);
        },
        // 签约主体名称
        subjectName() {
            return this.isEnt ? this.receiver.enterpriseName : this.receiver.userName;
        },
        otherEntSubject() {
            const ary = this.$store.state.commonHeaderInfo.enterprises;
            return ary.filter(item => item.entId !== '0');
        },
        // 表示签约主体有没有注册过（个人：账号，企业：企业）
        hasRegister() {
            return this.receiver.hasRegister;
        },
        // 账号是否已注册
        hasAccountRegister() {
            return this.$store.state.commonHeaderInfo.platformUser.userStatus === 1;
        },
        // 是否已经实名（也自动区分企业和个人）
        hasAuth() {
            return this.$store.getters.getAuthStatus === 2;
        },
        // 个人是否已实名
        hasUserAuth() {
            return this.receiver.hasUserAuthenticated;
        },
        // 是否有印章管理权限,印章权限分为分配权限与创建权限，同时有的用户可以在签署页面创建印章，并直接授权给创建者
        isSealManager() {
            return hasPrivilege('SEAL_MAINTENANCE') && hasPrivilege('SEAL_ALLOCATION');
        },
        // 签约提交的限制
        signStumblingBlock() {
            return this.receiver.signStumblingBlock;
        },
        // 签约校验配置
        shouldSignVaSecondCheck() {
            return this.$store.getters.SIGN_SECOND_CHECK;
        },
        // 是否隐藏校验密码
        hidePass() {
            return !this.hasAccountRegister || !this.shouldSignVaSecondCheck;
        },
        // 返回合同中所有非日期标签 数组
        labels() {
            const labels = [];
            this.docList.forEach(doc => {
                doc.page.forEach((page) => {
                    (page.marks || []).forEach(mark => {
                        if (mark && mark.type !== 'DATE') {
                            labels.push(mark);
                        }
                    });
                });
            });
            return labels;
        },
        // 返回合同中所有日期标签数组
        dateLabels() {
            const labels = [];
            this.docList.forEach(doc => {
                doc.page.forEach((page) => {
                    (page?.marks || []).forEach(mark => {
                        if (mark && mark.type === 'DATE') {
                            labels.push(mark);
                        }
                    });
                });
            });
            return labels;
        },
        writeLabels() {
            return this.labels.filter(label => this.writeRequiredLables.includes(label.type));
        },
        // 筛选处模板type=text标签的labelId和value，混合云模板发送的合同字段不出门
        textLabels() {
            const textLabels = this.labels.filter(label => label.type === 'TEXT');
            return textLabels.map(label => ({
                labelId: label.labelId,
                value: label.value,
                name: label.name,
            }));
        },
        signatureLabels() {
            return this.labels.filter(label => this.signRequiredLables.includes(label.type));
        },
        sealLabelsTip() {
            const sealLabels = this.labels.filter(label => label.type === 'SEAL');
            console.log(sealLabels.length);
            const otherPerson = new Set();
            let otherSealLen = 0;
            sealLabels.map(label => {
                if (label.status === 'INACTIVE') {
                    otherPerson.add(label.receiverName);
                    otherSealLen++;
                }
            });
            return  this.$t('sign.sealLabelsTip', { sealLabelslen: sealLabels.length, personStr: [...otherPerson].join('、'), otherSealLen: otherSealLen, mySealLen: sealLabels.length - otherSealLen });
        },
        // 签署日期
        signDate() {
            // 返回今天的日期 格式：2018年11月11日
            return dateGenerator();
        },
        HandleWritePanelParentGift() {
            const { userType, requireEnterIdentityAssurance, requireIdentityAssurance, userAuthName } = this.receiver;
            // 是否取name取决于要求个人或者企业经办人实名，不要求实名即使实名了也不取名字  CFD-19071
            const needAuth =  (userType === 'ENTERPRISE' && requireEnterIdentityAssurance) || (userType === 'PERSON' && requireIdentityAssurance);
            const handWritingName = needAuth && userAuthName || '';
            return {
                contractId: this.contractId,
                labelId: this.selectedLabelId,
                labelValue: this.selectedLabelValue,
                receiverId: this.receiver.receiverId,
                showUploadSignaturePicture: this.receiver.signType === 'ENTERPRISE_SIGNATURE', // 仅企业签字人开放上传图片签名入口
                handWritingRecognition: this.receiver.handWritingRecognition ? this.receiver.handWritingRecognition : false, // 是否配置了笔迹识别
                name: encodeURIComponent(handWritingName) || '',
            };
        },
        // 企业的所有印章数
        sealsLength() {
            return this.mySeals.length + this.otherSeals.length;
        },
        // 是否是混合云合同
        isHybridCloudContract() {
            return this.contractData.systemType === 'HYBRID_CLOUD';
        },
        // 是否是OFD格式文档合同
        isOfdContract() {
            return this.contractData.contractFileProcessFormat === 'OFD';
        },
        // 是否连接了混合云合同内网
        isInLan() {
            return this.$store.getters.getInLAN;
        },
        // 手写签名面板的initialBusiness参数
        handWriteBusiness() {
            const businessMap = {
                click: 'sign-all',
                switch: 'sign-single',
                addSignature: 'addSignature',
            };
            return businessMap[this.action];
        },
        ...mapGetters([
            'getSsoConfig',
            'getUserPermissons',
            'checkFeat',
            'getIsForeignVersion',
        ]),
        /**
         * 单点登录配置
         * signing_1 头部header栏
         * signing_1_back   左上角返回按钮
         * signing_agree    右上角签署按钮
         * signing_disagree 拒签按钮
         * signing_ft       底栏
         * signing_agree_bottom  底部签署按钮
         *
         * 当处于签署预览状态时，头尾和按钮不显示
         */
        bottomSignBtnVisible() {
            return !this.isPreview && (!this.ssoSigning.signing_agree_bottom || this.ssoSigning.signing_agree_bottom.visible);
        },
        hdVisible() {
            return !this.isPreview && (!this.ssoSigning.signing_1 || this.ssoSigning.signing_1.visible);
        },
        ftVisible() {
            return !this.isPreview && (!this.ssoSigning.signing_ft || this.ssoSigning.signing_ft.visible);
        },
        /* 是否是预览页面 */
        isPreview() {
            return this.$router.history.current.meta.view;
        },
        // 审批详情需要该字段
        approvalDetailReceiverId() {
            // 10: 发送前审批, 11: 签署前审批
            // 配合后端，兼容 APP，签署前审批取 parentReceiverId
            if (~~this.receiver.defType === 11) {
                return this.receiver.parentReceiverId;
            }
            return this.receiver.receiverId;
        },
        // 合同装饰骑缝章
        decorateRidingSealVisible() {
            return this.decorateRidingSealVos.length > 0;
        },
        isFromSignAuthorization() { // 从签署合同过来的签署采集授权书
            return !!this.transitionHrefBeforCollect;
        },
        isNeedSignerPayer() {
            const { contractData: {
                        contractPayerAccount,
                    },
                    receiver,
            } = this;
            return contractPayerAccount && contractPayerAccount.account === receiver.userAccount && contractPayerAccount.enterpriseName === receiver.enterpriseName;
        },
        isApplySeal() {
            // 判定申请用印的条件必须是印章标签数，与申请用印的印章数一样多
            const totalSealMarks = (this.docList || []).reduce((total, doc) => {
                const docSeals = (doc.page || []).reduce((docTotal, page) => {
                    return docTotal.concat((page.marks || []).filter(mark => ['SEAL', 'CONFIRMATION_REQUEST_SEAL'].includes(mark.type)));
                }, []);
                return total.concat(docSeals);
            }, []);
            return totalSealMarks.length > 0 && totalSealMarks.every(mark => mark.status === 'INACTIVE');
            // INACTIVE表示申请用印接口调用，签署tosign还没走的mark
        },
        showSignPasswordPriority() {
            // 是否展示优先签约密码校验：用户打开优先签约密码校验，并且未开启复用验证码,未开启无需签署校验，并且签署要求中有必须刷脸、刷脸优先、刷脸+验证码的配置，则展示，否则不展示
            return this.isSignPasswordPriorityOpen && !this.isReuseVerifyCode && !this.isNotValidateSignVerificationCode && !(this.receiver.faceVerify || this.receiver.faceFirst || this.receiver.messageAndFaceVerify);
        },
        showSignPasswordEntry() {
            // 是否展示签约密码校验入口：用户未开启复用验证码,未开启无需签署校验，并且签署要求中有必须刷脸、刷脸优先、刷脸+验证码的配置，则展示，否则不展示
            return !this.isReuseVerifyCode && !this.isNotValidateSignVerificationCode && !(this.receiver.faceVerify || this.receiver.faceFirst || this.receiver.messageAndFaceVerify);
        },
        isShowHighLight() { // 合同高亮入口
            return this.signType === 'approval' && !this.isOfdContract &&  this.features.includes('184');
        },
        isNewGroup() { // 是否为新集团
            return +this.commonHeaderInfo.groupVersion === 2;
        },
        isVisitor() { // 访客模式
            return this.commonHeaderInfo.platformUser.visitor;
        },
    },
    watch: {
        ifShowSignVaDialog(val) {
            if (val) {
                this.sensorsEventName && this.$sensors.track({
                    eventName: `Ent_${this.sensorsEventName}Window_PopUp`,
                    eventProperty: {
                        page_name: `${this.sensorsPageName}`,
                        window_name: this.isReuseVerifyCode ? '验证码复用' : '签约校验',
                        ...this.sensorsTrackContractInfo,
                    },
                });
            }
        },
        sealListVisible(val) {
            if (val) {
                this.sensorsEventName && this.$sensors.track({
                    eventName: `Ent_${this.sensorsEventName}Window_PopUp`,
                    eventProperty: {
                        page_name: `${this.sensorsPageName}`,
                        window_name: '选择印章',
                        ...this.sensorsTrackContractInfo,
                    },
                });
            }
        },
        handleWriteTabVisible(val) {
            if (val) {
                this.sensorsEventName && this.$sensors.track({
                    eventName: `Ent_${this.sensorsEventName}Window_PopUp`,
                    eventProperty: {
                        page_name: `${this.sensorsPageName}`,
                        window_name: '手写签名',
                        ...this.sensorsTrackContractInfo,
                    },
                });
            }
        },
        signQualifiedDialogVisible(val) {
            if (val) {
                if (['MORE_AUTHENTICATE', 'MORE_AUTHENTICATE_WITH_PROXY_AUTH', 'USE_MY_IDENTITY', 'NOT_MATCH_AUTHENTICATE'].includes(this.signDecision)) {
                    this.sensorsEventName && this.$sensors.track({
                        eventName: `Ent_${this.sensorsEventName}Window_PopUp`,
                        eventProperty: {
                            page_name: `${this.sensorsPageName}`,
                            window_name: ['MORE_AUTHENTICATE', 'MORE_AUTHENTICATE_WITH_PROXY_AUTH'].includes(this.signDecision) ? '需要补充实名' : (!this.isEnt ? '个人实名与指定实名不一致' : null),
                            ...this.sensorsTrackContractInfo,
                        },
                    });
                }
            }
        },
        newMark: {
            handler(v) {
                this.setNewMarkIntoDocList(v);
            },
            deep: true,
        },
        focusingMark: {
            handler(v) {
                this.assignSelectedData(v.mark, v.docIndex, v.pageIndex, v.markIndex);
            },
            deep: true,
        },
        // 监听签名或印章的状态，改变按钮文字
        signatureLabels: {
            handler() {
                // 审批时不需要监听标签状态改变右上角按钮
                if (this.signType === 'approval') {
                    return;
                }
                this._setConfrimSignButtonText();
            },
            deep: true,
        },
        rejectRadio(value) {
            if (value !== 6) {
                this.refuseReason = '';
            }
        },
    },
    methods: {
        showAddSealDialog(addSealParam) {
            this.addSealVisible = true;
            this.addSealParam = addSealParam;
        },
        handelAddSeal() {
            const { mark, docIndex, pageIndex, markIndex, action, params } = this.addSealParam;
            this.handleClickLabels(mark, docIndex, pageIndex, markIndex, action, params);
        },
        handleCopyBtnClick() {
            const tip = this.$t('certificationRenewalDialog.tip10', { currentEmpName: this.caFailedInfo.currentEmpName, link: `${location.host}/console/enterprise/account/certification` });
            this.$copyText(tip).then(() => {
                this.$MessageToast.success(this.$t('certificationRenewalDialog.tip11'));
            }).catch(() => {
                this.$MessageToast.error('复制失败，请手动复制');
            });
        },
        ifOpenSetSignPwdDialog(open) {
            this.showSetSignPwdDialog = open;
            this.signVaDialogVisible = !open;
            if (!open) {
                // 用户首次设置完签约密码后，后端直接帮用户自动打开优先签约密码校验开关,并关闭双重校验开关
                this.isSignPasswordPriorityOpen = true;
            }
        },
        async handleChat() {
            const { contractId, currentSignDocumentId } = this;
            const { data: { chatTopicId } } = await this.$http.post(`/contract-api/contracts/${contractId}/document/${currentSignDocumentId}/init-chat-topic`);
            return window.open(`${location.origin}/hubble/chat/${chatTopicId}`);
        },
        handleCancelApproval() {
            this.approvalVaDialogVisible = false;
            this.sensorsEventName && this.$sensors.track({
                eventName: `Ent_${this.sensorsEventName}Window_BtnClick`,
                eventProperty: {
                    page_name: `${this.sensorsPageName}`,
                    window_name: this.approvalResult === 'APPROVAL_RESULT_PASS' ? '审批结果：同意' : '审批结果：驳回',
                    icon_name: '取消',
                    ...this.sensorsTrackContractInfo,
                },
            });
        },
        handleOperateNextContract() {
            this.sensorsEventName && this.$sensors.track({
                eventName: `Ent_${this.sensorsEventName}Window_BtnClick`,
                eventProperty: {
                    page_name: `${this.sensorsPageName}`,
                    window_name: this.approvalResult === 'APPROVAL_RESULT_PASS' ? '审批结果：同意' : '审批结果：驳回',
                    icon_name: '确定',
                    ...this.sensorsTrackContractInfo,
                },
            });
            this.isShowContinueOperateNextDialog = true;
            this.sensorsEventName && this.$sensors.track({
                eventName: `Ent_${this.sensorsEventName}Window_PopUp`,
                eventProperty: {
                    page_name: `${this.sensorsPageName}`,
                    window_name: '驳回提示',
                    ...this.sensorsTrackContractInfo,
                },
            });
        },
        // 关闭继续审批弹窗
        closeContinueOperateNextDialog() {
            this.isShowContinueOperateNextDialog = false;
        },
        // 保存标签到后端
        saveMark(data) {
            return this.$http.post(
                `${signPath}/contracts/${this.contractId}/signer/labels/signer-create-and-modify/`,
                data,
                { noToast: 1 },
            );
        },
        handleDragLabelBySigner({ type }) {
            // 点击生成标签插入文档
            this.generateMarkIntoDocPage(type);
        },
        getRandomIntInclusive(min, max) {
            min = Math.ceil(min);
            max = Math.floor(max);
            return Math.floor(Math.random() * (max - min + 1)) + min; // 含最大值，含最小值
        },
        generateMarkIntoDocPage(type) {
            // 前端模拟生成一个当前页的 label
            // 拿到所有合同文档
            const doc = this.$refs['doc'];
            // 拿到当前要操作的文档
            const currentDoc = doc.curDoc;
            // 当前是文档的哪一页
            const currentPageNumber = doc.currentPageIndex;
            // 当前页的索引
            const currentPageIndex = currentPageNumber - 1;
            // 拿到当前文档对应的当前页
            const curDocPage = doc.curDoc.page[currentPageIndex];
            let labelWidth = '';
            let labelHeight = '';
            const markInfoType = type.toUpperCase();
            labelWidth = markInfo(markInfoType).width;
            labelHeight = markInfo(markInfoType).height;
            // 先固定日期为 x年x月x日的形式宽高
            if (markInfoType === 'DATE') {
                labelWidth = 140;
                labelHeight = 20;
            }
            // 印章/签名出现在页面的位置
            const x = (curDocPage.width / 2) + this.getRandomIntInclusive(-labelWidth, labelWidth);
            const y = markInfo(markInfoType).height * 3 + this.getRandomIntInclusive(-labelHeight, labelHeight);
            // 新建一个mark
            const newMark = {
                contractId: this.contractId,
                documentId: currentDoc.documentId,
                labelId: '', // 新建标签，labelId为空
                receiverId: this.receiver.receiverId,
                pageNumber: currentPageNumber,
                type: markInfoType,
                x,
                y,
                width: labelWidth,
                height: labelHeight,
            };
            // 坐标转换
            const percentCoordinate = markCoordinateTransform({
                ...newMark,
                width: newMark.width * doc.scale,
                height: newMark.height * doc.scale,
            }, curDocPage.width, curDocPage.height);
            // 使用转换后的值覆盖 newMark
            const markObj = Object.assign({}, newMark, percentCoordinate);
            this.saveMark([markObj]).then(res => {
                // 将后端回显的标签插入当前页的标签中
                curDocPage.marks.push(res.data[0]);
            });
        },
        // 打开签署引导弹窗
        openSignPlaceGuideDialog({ step }) {
            this.signPlaceGuideStep = step;
            this.isShowSignPlaceGuideDialog = true;
        },
        // 关闭签署引导弹窗
        closeSignPlaceGuideDialog() {
            this.isShowSignPlaceGuideDialog = false;
        },
        closeSignPlaceTipDialog() {
            this.isShowSignPlaceTipDialog = false;
        },
        async createPersonSeal() {
            if (!this.personSealName.trim()) {
                return this.$MessageToast(this.$t('signJa.createSeal.emptyErr'));
            }
            await this.handleClickSignature('', this.personSealName, true);
            if (['NOT_AUTHENTICATE', 'NOT_MATCH_AUTHENTICATE'].includes(this.signDecision)) {
                await this.$http.put('/users/name', { newName: this.personSealName });
                this.getReceiverCallback();
            }
            this.closeCreateSealDialog();
        },
        closeCreateSealDialog() {
            this.createJaSealDialogVisible = false;
            this.personSealName = '';
        },
        // 处理合同高亮
        handleContractHighLight() {
            let highLightAction = 'approval';// 高亮处理动作
            if (this.signType === 'sign') { // 签署直接高亮
                highLightAction = 'sign';// 签署的高亮处理动作为 sign
                this.isContractHighLight = true;
            } else if (this.signType === 'approval') { // 审批切换
                highLightAction = 'approval';
                this.isContractHighLight = !this.isContractHighLight;
            }
            // 重新加载合同信息
            this.docList.forEach(async(doc) => {
                doc.pdfurl = await this.$hybrid.getPdfPreviewUrl({
                    url: `${doc.fileStreamUrl}&permission=${this.permission}&action=${highLightAction}&receiverId=${this.receiver.receiverId}&highLight=${this.isContractHighLight}`,
                    hybridServer: this.hybridServer,
                    hybridTarget: '/contract/part/document/download',
                    params: {
                        contractId: this.contractId,
                        documentIds: doc.documentId,
                        permission: this.permission,
                        action: highLightAction,
                        receiverId: this.receiver.receiverId,
                        highLight: this.isContractHighLight,
                    },
                    hybridAccessToken: this.hybridAccessToken,
                });
            });
            this.$refs.doc.handleUpdateDocIndex(this.$refs.doc.currentDocIndex);
        },
        handleThirdPartApproval() {
            if (this.ifExistThirdPartyApprove) {
                this.viewThirdApproval = true;
            } else {
                this.ifSignerViewApprovalDetail = true;
            }
        },
        refreshAccout(entId) {
            this.$http
                .post(`/authenticated/switch-ent`, {
                    entId,
                    refreshToken: this.$cookie.get('refresh_token'),
                })
                .then(({ data }) => {
                    this.$token.save(data.access_token, data.refresh_token);
                });
        },
        // 审批点击驳回
        handleClickReject() {
            this.approvalResult = 'APPROVAL_RESULT_REJECT';
            this.approvalVaDialogVisible =  true;
            this.sensorsEventName && this.$sensors.track({
                eventName: `Ent_${this.sensorsEventName}Window_PopUp`,
                eventProperty: {
                    page_name: `${this.sensorsPageName}`,
                    window_name: '审批结果：驳回',
                    ...this.sensorsTrackContractInfo,
                },
            });
        },
        // 获取装饰骑缝章
        getDecorateRidingSeal() {
            this.isLoading = 1;
            return this.$http.get(`/contract-api/contracts/${this.contractId}/${this.receiver.receiverId}/decorate-riding-seal`)
                .then(res => {
                    return this.decorateRidingSealVos = res.data || [];
                }).finally(() => {
                    this.isLoading = 0;
                });
        },
        // 判断是否符合签约条件
        checkSignQualified() {
            const signDecision = this.signDecision;
            const ifExistThirdPartyApprove = this.ifExistThirdPartyApprove;
            if (ifExistThirdPartyApprove) {
                this.thirdPartApprovalDialogShow = true;
                return false;
            } else if (signDecision === 'WAIT_SIGN_APPROVAL_COMPLETE') {
                // this.$MessageToast.error(this.$t('sign.cannotSignBeforeApproval'));
                this.thirdPartApprovalDialogShow = true;
                return false;
            } else if (signDecision === 'NEED_SET_APPROVAL_FLOW') {
                this.dialogApproval = true;
                return false;
            } else if (signDecision === 'NORMAL' || this.signOperationType === 'ONLY_PAPER_SIGN') {
                return true;
            } else if (signDecision === 'ARCHIVE_COLLECT') {
                location.href = `${location.origin}/damp/pages/redirect/guideRedirect?pageToken=${this.archiveToken}&archiveId=${this.receiver.archiveId}&fromSign=true`;
                return false;
            } else if (signDecision === 'ENTERPRISE_SEALS_NOT_MATCH_TEMPLATE_SPECIAL_SEAL') {
                // 企业能使用的印章，不满足模板专用章设置
                return true;
            } else if (signDecision === 'SIGNER_SEALS_NOT_MATCH_TEMPLATE_SPECIAL_SEAL') {
                // 签署人能使用的印章，不满足模板专用章设置
                return true;
            } else if (signDecision === 'USER_IN_SIGN_BLACKLIST' || signDecision === 'USER_NOT_IN_SIGN_WHITELIST') {
                // 企业已实名，用户已加入，无签署权限
                this.saveApplyPermissionInfo();
                this.$router.push(`/sign-flow/sign/un-permission-remind/noSign?canPaperSign=${this.canPaperSign}&contractId=` + this.contractId);
                return false;
            } else if (this.isEnt && (signDecision === 'NOT_AUTHENTICATE'  || signDecision === 'NOT_MATCH_AUTHENTICATE')) {
                if (this.getIsForeignVersion) {
                    return this.handleJaNotAuth();
                }
                // 企业未实名
                this.saveApplyPermissionInfo();
                this.$router.push(`/sign-flow/sign/un-permission-remind/noAuth?canPaperSign=${this.canPaperSign}&contractId=` + this.contractId);
                return false;
            } else if (signDecision === 'NOT_ADD') {
                // 未加入该企业
                // this.postApplyForJoin();
                if (this.getIsForeignVersion) {
                    setTimeout(() => {
                        this.$messageBox({
                            message: this.$t('sign.notJoinTip'),
                        });
                    }, 300);
                    return false;
                }
                this.saveApplyPermissionInfo();
                this.$router.push(`/sign-flow/sign/un-permission-remind/noJoin?canPaperSign=${this.canPaperSign}&contractId=` + this.contractId);
                return false;
            } else {
                const notAuthEnt = signDecision === 'USE_DEFAULT_IDENTITY' && !this.receiver.hasAuthenticated && !this.receiver.requireIdentityAssurance;
                if (this.jaEntHasNotAuthenticated || notAuthEnt) {
                    return true;
                }
                if (this.getIsForeignVersion) {
                    return this.handleJaNotAuth();
                }
                if (signDecision !== 'NEED_SET_APPROVAL_FLOW') {
                    this.signQualifiedDialogVisible = true;
                }
            }
            return false;
        },
        handleJaNotAuth() {
            const signDecision = this.signDecision;
            const notAuthDecisionList = ['NOT_AUTHENTICATE', 'NOT_MATCH_AUTHENTICATE'];
            const flag = notAuthDecisionList.includes(signDecision);
            if (this.isEnt) {
                this.$http.get('/ents/auth/info').then(res => {
                    const { auth } = res.data;
                    // auth.authinfoStatus: 0-未审核；1-审核通过；2-未完成；3-自动审核通过；4-审核驳回；5-重新提交审核；
                    if (auth && auth.authinfoStatus === 0) {
                        // 未实名,已提交待审核
                        this.$MessageToast.info(this.$t('signJa.waitApprove'));
                    } else {
                        // 日文版创建企业弹窗
                        this.jaSignQualifiedDialogVisible = flag;
                    }
                });
            } else {
                this.createJaSealDialogVisible = flag;
            }
        },
        saveApplyPermissionInfo() {
            sessionStorage.setItem('applyPermission', JSON.stringify({
                senderEntName: this.senderName, receiverEntName: this.subjectName, receiverId: this.receiver.receiverId, returnUrl: this.returnUrl, from: 'sign', channel: this.channel }));
        },
        // 使用默认'未认证企业'身份签约的处理
        handleUseDefaultIdentity() {
            return this.postSwitchIdentity(this.$store.state.commonHeaderInfo.currentEntId || '')
                .then(res => {
                    this.signDecision = res.data.signDecision;
                })
                .catch(() => {});
        },
        // 实名信息变更检测弹窗消失toAuth: 是否跳转到实名认证页面
        handleCloseAuthInfoChangeDialog(toAuth) {
            this.authInfoChangeDialogShow = false;
            if (toAuth) {
                this.$localStorage.set('transitionHref', this.$route.fullPath);
                this.$router.push('/auth-p/enterprise/sign/stage');
            }
        },
        // 请求数据查看企业实名信息是否变更
        checkAuthChange() {
            this.$http.get(`/ents/auth/real-name-changed`, {
                params: {
                    contractId: this.contractId,
                },
            }).then(res => {
                if (res.data.ifChanged) {
                    this.authChangeData = res.data;
                    this.authInfoChangeDialogShow = true;
                } else {
                    this.isFinishCheckAuthInfoChange = true;
                    this.toNext();
                }
            }).catch(() => {
                // console.error(error);
            });
        },
        handleCloseInvalidContractDialog(from) {
            this.invalidContractDialogShow = false;
            if (from === 'nextStep') {
                this.invalidContractNextStep = true;
                this.toNext();
            }
        },
        beforeNext() {
            if (this.signType === 'sign') {
                // 允许纸质签署不考虑实名情况
                if (this.signOperationType === 'CHOOSE_PAPER_SIGN') {
                    this.allowPaperSignDialogShow = true;
                    this.sensorsEventName && this.$sensors.track({
                        eventName: `Ent_${this.sensorsEventName}Window_PopUp`,
                        eventProperty: {
                            page_name: `${this.sensorsPageName}`,
                            window_name: '允许纸质签',
                            ...this.sensorsTrackContractInfo,
                        },
                    });
                    return;
                }
                const isSignQualified = this.checkSignQualified();
                if (!isSignQualified) {
                    return;
                }
            }
            this.confirmRiskTipNextStep = false;
            this.invalidContractNextStep = false;
            this.toNext();
        },
        saveTwoFactorAuthentication(code) {
            return this.$http.post('/users/authenticator/save', {
                code,
            });
        },
        async handleEncryptionSign(param) {
            this.encryptionSignParam = param;
            if (!this.ifHasBindingTwoFactor && this.encryptionSignParam?.dynamicCode) {
                const res = await this.saveTwoFactorAuthentication(this.encryptionSignParam.dynamicCode);
                if (!res.data.value) {
                    return this.$MessageToast.info(this.$t('twoFactor.dynamicVerifyInfo'));
                }
            }
            this.toSign();
        },
        paperOrElectronSign(type) {
            if (type === 'paper') {
                this.paperSignDialogVisible = true;
                this.sensorsEventName && this.$sensors.track({
                    eventName: `Ent_${this.sensorsEventName}Window_PopUp`,
                    eventProperty: {
                        page_name: `${this.sensorsPageName}`,
                        window_name: '使用纸质方式签署',
                        ...this.sensorsTrackContractInfo,
                    },
                });
            } else {
                this.toNext();
            }
        },
        /**
         * @desc 点击'同意'或'提交'或'确认签署'，
         * 立即签署判断步骤:
         * 1: 判断是否满足签署条件(signDecision)
         * 2: 是否有必填字段内容未填写
         * 3: 是否完成了盖章或签名
         * 4: 进入签约校验，若验证码登录+未开启二次校验则跳过签约校验直接签署
         */
        async toNext() {
            if (this.isEnt && !this.isFinishCheckAuthInfoChange) {
                // 企业实名信息变更，弹窗提示
                this.checkAuthChange();
                return;
            }

            if (this.signType === 'view') {
                // 查看
                return;
            }
            if (this.signType === 'approval') {
                // 审批
                this.approvalResult = 'APPROVAL_RESULT_PASS';
                this.approvalVaDialogVisible = true;
                this.sensorsEventName && this.$sensors.track({
                    eventName: `Ent_${this.sensorsEventName}Window_PopUp`,
                    eventProperty: {
                        page_name: `${this.sensorsPageName}`,
                        window_name: '审批结果：同意',
                        ...this.sensorsTrackContractInfo,
                    },
                });
                return;
            }
            // 必须纸质签署
            if (this.signType === 'sign' && this.signOperationType === 'ONLY_PAPER_SIGN') {
                this.paperSignDialogVisible = true;
                this.sensorsEventName && this.$sensors.track({
                    eventName: `Ent_${this.sensorsEventName}Window_PopUp`,
                    eventProperty: {
                        page_name: `${this.sensorsPageName}`,
                        window_name: '使用纸质方式签署',
                        ...this.sensorsTrackContractInfo,
                    },
                });
                return;
            }
            // 签约
            if (!this.checkSignQualified()) {
                return;
            }
            // 配置了加密签署或者二要素验证
            if (this.encryptionType.encryptionSignConfig || this.encryptionType.twoFactorAuthentication) {
                this.handleShowEncryptionSign();
                return;
            }
            // 拉取混合云1 和 公有云 最新的label，并让用户判断是否覆盖
            await this.fetchAndCoverAlphaHybridContractBusiLabelsInfo();
            // 必填输入框还未填写
            if (!this.checkoutAllWriteDone().necessDone) {
                return this.$alert(this.$t('sign.fillFirst'), {
                    confirmButtonText: this.$t('sign.submit'),
                    callback: () => {
                        this.$refs.doc.scrollToMark(this.checkoutAllWriteDone().necessList[0]);
                    },
                });
            }
            // 选择了不符合章，存在备注的情况下，就必须要填写
            const hasRequestRemark = this.checkoutNeedRequestRemarkDone();
            if (hasRequestRemark) {
                this.$MessageToast(this.$t('sign.needRemark')); // 您还需要填写备注
                return this.$refs.doc.scrollToMark(hasRequestRemark);
            }
            // 签署人自定义签署位置，但是没有盖章或签名时提示 包含新老版本
            if ((this.signerDragAble || this.signPlaceBySigner) && !this.signatureLabels.length) {
                if (this.signerDragAble) {
                    return this.$alert(this.$t('sign.finishSignatureBeforeSign'), {
                        confirmButtonText: this.$t('sign.submit'),
                    });
                }
                // 查询合同上有没有存在当前签署人已经合并到pdf上的印章或者签名
                const { data } = await this.$http.get(`/contract-api/contracts/${this.contractId}/receivers/${this.receiver.receiverId}/labels/wait-for-compose`);
                if (!data.length) { // 如果没有，才提示缺少印章或签名
                    return this.isShowSignPlaceTipDialog = true;
                }
            }
            // FDA签名需要填写签字理由
            const needSignatureReason = this.labels.find(label => label.type === 'SIGNATURE' && label.supportFDA && !label.signerFDAReason);
            if (needSignatureReason) {
                return this.$MessageToast('您还需要填写签字理由');
            }
            // FDA签名首次需要填写签字的姓名 拼音或者英文名
            const needSignatureSignerName = this.labels.find(label => label.type === 'SIGNATURE' && label.supportFDA && !label.signerFDAName);
            if (needSignatureSignerName) {
                return this.$MessageToast(this.$t('signPC.FDASign.signerNameFillTip'));
            }
            // 不是旧版本的签署方指定签署位置，因为旧版本拖出来就已经填充内容，因此不做签署位置是否填充完毕校验
            if (!this.signerDragAble && !this.checkoutSignDone()) {
                // 如果是新版本的签署人指定签署位置，这个针对有签名位置但是没有填充签名的情况
                if (this.signPlaceBySigner) {
                    // 弹窗提示
                    return this.isShowSignPlaceTipDialog = true;
                }
                const msg = this.receiver.signType === 'SEAL_AND_SIGNATURE' ? '您需要同时添加印章和签名后，才能完成签署' : this.$t('sign.completeSign');
                return this.$alert(msg, {
                    confirmButtonText: '确定',
                    callback: () => {
                        this.$refs.doc.handleClickNavi();
                    },
                });
            }
            if (this.needAttach) {
                return this.$alert(this.$t('sign.uploadFileOnRightSite'), {
                    confirmButtonText: this.$t('sign.submit'),
                    callback: () => {
                        this.activeTab = 'FILL';
                    },
                });
            }
            // 所有内容都填完后，签约校验前，判断未签署的客户中是否还有需要填写内容的签署人。
            // 在后端的 riskTip 为 true 即有风险，
            // 并且没有点过确认风险按钮时显示风险弹框
            // && this.rText !== '提交'
            if (this.receiver.riskTip && !this.confirmRiskTipNextStep) {
                this.riskTipDialogVisible = true;
                return;
            }
            // 判断CA证书是否过期，个人签署方不要求实名的话不需要判断证书是否过期 CFD-23675
            const personNotNeedAuth = this.userType === 'PERSON' && !this.receiver.requireIdentityAssurance;
            if (!this.ifJustUseBestSignCa && this.needRenewal && !personNotNeedAuth) {
                await this.handleCARenewal();
            }
            // 需要该签署方付费，这里作为确认签署前的最后一步
            if (this.isNeedSignerPayer && !this.hasRrePay) {
                await this.handleContractPayer();
            }
            // 签署合同过来的签署授权书
            if (this.isFromSignAuthorization) {
                return window.location.href = this.transitionHrefBeforCollect;
            }
            // 作废合同弹窗
            if (this.contractData.invalidContractId && !this.invalidContractNextStep) {
                this.invalidContractDialogShow = true;
                return;
            }
            // 印章ocr校验
            await this.validSeal();
            await this.getInternalDecisionInfo();
            // 提前结束签署弹窗
            if (this.ifInternalDecision && this.endSignEarlyType === 'SEAL_SIGNER') {
                this.showEndSignEarlyPrompt = true;
                return;
            }
            this.toSign();
        },
        getInternalDecisionInfo() {
            return this.$http.get(`contract-api/contracts/${this.contractId}/receivers/${this.receiver.receiverId}/internal-decision-tips`)
                .then((res) => {
                    const { signType, sealSignerTipVO, ifInternalDecision } = res.data;
                    this.endSignEarlyType = signType; // 盖章人: SEAL_SIGNER，签字人: SIGNATURE_SIGNER，个人签署: PERSONAL_SIGNATURE
                    this.endSignEarlyData = sealSignerTipVO;
                    this.ifInternalDecision = ifInternalDecision;
                }).catch(() => {});
        },
        afterEndSignEarlyPrompt() {
            // 内部决议弹窗提示后的操作
            this.showEndSignEarlyPrompt = false;
            this.toSign();
        },
        getSenderEntConfig() {
            this.$http.post(`contract-api/contracts/${this.contractId}/get-sender-config`, {
                receiverId: this.receiver.receiverId,
            })
                .then((res) => {
                    const { senderContractSignConfiguration } = res.data;
                    this.canShareReview = senderContractSignConfiguration.canShareReview;
                    this.isReuseVerifyCode = senderContractSignConfiguration.verifyCodeMultiplex;
                    this.isSignPasswordPriorityOpen = senderContractSignConfiguration.prioritySigningPassword;
                    this.isNotValidateSignVerificationCode = senderContractSignConfiguration.notValidateSignVerificationCode;
                    this.isOtherSig = senderContractSignConfiguration.otherSig; // 模板是否开启跨平台加签
                });
        },
        validSeal() {
            // eslint-disable-next-line no-async-promise-executor
            return new Promise(async(resolve, reject) => {
                try {
                    // 检测印章ocr校验
                    const { data } = await getSealOcrRecognition(this.contractId, this.receiver.receiverId);
                    if (!data.ifPass) {
                        this.isErrorDialogShow = true;
                        this.ocrEntData = data;
                        reject();
                    } else {
                        resolve();
                    }
                } catch (e) {
                    reject();
                }
            });
        },
        handleCARenewal() {
            // eslint-disable-next-line no-async-promise-executor
            return new Promise(async(resolve, reject) => {
                // 盖章并签字场景需要处理个人与企业主体
                const ifBoth = this.receiver.signType === 'SEAL_AND_SIGNATURE';
                //  企业签字需要获取个人
                const isForPerson = this.receiver.signType === 'ENTERPRISE_SIGNATURE';
                this.$http.get(`users/auth/cacert/status/v2?ifBoth=${ifBoth}&isForPerson=${isForPerson}`)
                    .then(async(res) => {
                        // NO_CA(0), CA_AVALIABLE(1), CA_EXPIRED(2);
                        const personNeedRenewal = res.data.person === 2;
                        const entNeedRenewal = res.data.enterprise === 2;
                        this.needRenewal = personNeedRenewal || entNeedRenewal;
                        this.renewalType = {
                            personNeedRenewal,
                            entNeedRenewal,
                        };
                        if (this.needRenewal) {
                            if (personNeedRenewal) {
                                await this.handleCAcertInfo(false);
                            }
                            if (entNeedRenewal) {
                                await this.handleCAcertInfo(true);
                            }
                            this.renewalDialogVisible = true;
                            reject();
                        } else {
                            resolve();
                        }
                    })
                    .catch(reject);
            });
        },
        handleCAcertInfo(isEnt) {
            return this.getCAcertInfo(isEnt).then(res => {
                // 返回的时间格式'2018-11-25 12:20:50',保留到日
                const dateArr = res.data.stopTime.split(' ');
                res.data.stopTime = dateArr[0];
                // fix SAAS-32905 chrome无痕浏览器 个人续期后，点击浏览器返回键，证书展示不对 只有盖章并签字场景才展示多个证书
                if (this.renewalType.personNeedRenewal && this.renewalType.entNeedRenewal) {
                    this.certificationInfos.push(res.data);
                } else {
                    this.certificationInfos = [res.data];
                }
            });
        },
        // 签署人付费处理
        handleContractPayer() {
            // eslint-disable-next-line no-async-promise-executor
            return new Promise(async(resolve, reject) => {
                try {
                    this.isLoading = 1;
                    const { data } = await this.$http.get(`${signPath}/charging/contracts/${this.contractId}`);
                    if (data.ifContractConsumed) { // CFD-24570：合同已预扣，不需要再付费
                        this.isLoading = 0;
                        resolve();
                        return;
                    }
                    const { data: { toBMaxUnitPrice, toCMaxUnitPrice } } = await this.$http.get(`ents/charging/get-max-unit-price?contractId=${this.contractId}`);
                    this.isLoading = 0;
                    const {
                        chargingLineStatus,
                        productType,
                        needNum,
                        unlimited,
                    } = data;
                    const isEnoughMoney = chargingLineStatus === 'ENOUGH' || chargingLineStatus === 'TOB_ENOUGH';
                    const _this = this;
                    const unit = productType === 'TO_B' ? `${this.$t('sign.units', { num: needNum })}${this.$t('sign.contractToPublic')}` : `${this.$t('sign.units', { num: needNum })}${this.$t('sign.contractToPrivate')}`;
                    const money = productType === 'TO_B' ? `${needNum * toBMaxUnitPrice}` : `${needNum * toCMaxUnitPrice}`;
                    const isCanCharge = !isEnoughMoney; // 是否可以去充值
                    const h = this.$createElement;
                    let msg = '';
                    if (unlimited) {
                        msg = [
                            h('p', { style: 'margin-top: 8px; color: #666666;' }, this.$t('sign.unlimitedNotice')),
                        ];
                    } else {
                        const needPay = `${this.$t('sign.paySum', { sum: unit })}`; // 共 sum份对公/私合同 需要您支付
                        const notEnoughMoney =  `${this.$t('sign.payTotal', { total: money }) + this.$t('sign.fundsLack')}`; // 共计{total}元。您的可用合同份数不足，为确保合同顺利签署，建议立即充值
                        const ifHasRightCharge = `${isCanCharge ? '' : this.$t('sign.contactToRecharge')}`; // 请联系主管理员充值
                        const payMsg = needPay + (isEnoughMoney  ? '。' : ('，' + notEnoughMoney + ifHasRightCharge));
                        msg = [
                            h('p', { style: 'color: #333333;font-weight: bold;' }, payMsg),
                            h('p', null, `${chargingLineStatus === 'TOB_ENOUGH' ? this.$t('sign.deductPublicNotice') : ''}`),
                            h('p', { style: 'margin-top: 8px; color: #666666;' }, this.$t('sign.needSignerPay')),
                        ];
                    }
                    this.sensorsEventName && this.$sensors.track({
                        eventName: `Ent_${this.sensorsEventName}Window_PopUp`,
                        eventProperty: {
                            page_name: `${this.sensorsPageName}`,
                            window_name: '签署方付费',
                            ...this.sensorsTrackContractInfo,
                        },
                    });
                    await this.$msgbox({
                        title: null,
                        message: h('div', null, msg),
                        showCancelButton: true,
                        confirmButtonText: isEnoughMoney || !isCanCharge || unlimited ? this.$t('sign.submit') : this.$t('sign.recharge'),
                        customClass: 'el-message-box_singerpay',
                    });
                    this.sensorsEventName && this.$sensors.track({
                        eventName: `Ent_${this.sensorsEventName}Window_BtnClick`,
                        eventProperty: {
                            page_name: `${this.sensorsPageName}`,
                            window_name: '签署方付费',
                            icon_name: (isEnoughMoney || !isCanCharge || unlimited) ? '确定' : '充值',
                            ...this.sensorsTrackContractInfo,
                        },
                    });

                    if (unlimited) {
                        resolve();
                    } else if (isEnoughMoney) {
                        // 账号里面有钱，预扣 成功之后 直接签署
                        _this.$http.get(`/contract-api/charging/contracts/${_this.contractId}/consume`)
                            .then(() => {
                                _this.sensorsEventName && _this.$sensors.track({
                                    eventName: `Ent_${_this.sensorsEventName}Window_Result`,
                                    eventProperty: {
                                        page_name: `${_this.sensorsPageName}`,
                                        window_name: '签署方付费',
                                        is_success: true,
                                        icon_name: '确定',
                                        request_url: `/contract-api/charging/contracts/${_this.contractId}/consume`,
                                        ..._this.sensorsTrackContractInfo,
                                    },
                                });
                                this.hasRrePay = true;
                                resolve();
                            })
                            .catch((err) => {
                                const errMsg = err.response?.data?.message;
                                const status = err.response?.status;
                                const code = err.response?.data?.code;
                                _this.sensorsEventName && _this.$sensors.track({
                                    eventName: `Ent_${_this.sensorsEventName}Window_Result`,
                                    eventProperty: {
                                        page_name: `${_this.sensorsPageName}`,
                                        window_name: '签署方付费',
                                        is_success: false,
                                        icon_name: '确定',
                                        fail_http_code: status,
                                        fail_error_code: code,
                                        fail_reason: errMsg,
                                        request_url: `/contract-api/charging/contracts/${_this.contractId}/consume`,
                                        ..._this.sensorsTrackContractInfo,
                                    },
                                });
                            });
                    } else if (isCanCharge) {
                        _this.$http.post(`/ents/charging/contract/ordering`, {
                            contractId: _this.contractId,
                            needNumber: needNum,
                            productType,
                            isNewGroup: _this.isNewGroup,
                        })
                            .then((data) => {
                                const { orderId } = data.data;
                                _this.signerPayOrderInfo = {
                                    orderId,
                                    rechargeMoney: money,
                                    products: [],
                                };
                                _this.showSignerQuickPayDialog = true;
                                reject();
                            }).catch(() => {
                                reject();
                            });
                    }
                } catch (err) {
                    this.isLoading = 0;
                }
            });
        },
        // 真正进入签署的流程 alreadyFaceValid标记刷脸完成
        toSign(alreadyFaceValid) {
            // if (this.contractData.ifCrossPlatformContract && !this.hasCrossPlatformConfirm) {
            //     return this.$confirm(this.$t('sign.crossPlatformCofirm.message'), this.$t('sign.crossPlatformCofirm.title'), {
            //         confirmButtonText: this.$t('sign.crossPlatformCofirm.confirmButtonText'),
            //         cancelButtonText: this.$t('sign.crossPlatformCofirm.cancelButtonText'),
            //     }).then(() => {
            //         this.hasCrossPlatformConfirm = true;
            //         this.toSign(alreadyFaceValid);
            //     });
            // }
            const ifEncryptionSign = this.encryptionType.encryptionSignConfig || this.encryptionType.twoFactorAuthentication;
            // 未注册用户，进入页面后1小时内签署的，不校验
            // 申请用印/转签名的，不校验
            // 配置了不签署校验的，不校验uncheck：true
            if (this.signStumblingBlock === 'CLEAN' ||
                this.signStumblingBlock === 'VERIFY_CLEAN' ||
                this.rText === '提交' ||
                this.isApplySeal ||
                alreadyFaceValid ||
                this.isNotValidateSignVerificationCode || this.receiver.uncheck
            ) {
                // 判断二次校验是否开启，申请用印不校验,无需签署校验不需要开启
                // 判断是否是短信验证码加刷脸验证  this.receiver.messageAndFaceVerify
                const ifShowSignVaDialog = !this.receiver.uncheck && !this.isNotValidateSignVerificationCode && this.shouldSignVaSecondCheck && !this.isApplySeal || this.receiver.messageAndFaceVerify;
                if (ifShowSignVaDialog) {
                    this.signVaDialogVisible = true;
                    return;
                }

                this.isLoading = 1;
                const params = {
                    token: this.urlToken,
                    signPlatform: 'WEB',
                    archiveCollectContractId: this.archiveCollectContractId,
                };
                // web二维码刷脸完成后会直接提交
                if (alreadyFaceValid) {
                    params.verifyType = 'FACE_VERIFY';
                }
                return this.postConfirmSign({
                    ...params,
                });
            } else if (['FACE_NOTAUTH', 'VIDEO_AUDIO_NOTAUTH'].includes(this.signStumblingBlock)) { // 企业刷脸签署，经办人未实名，需要切换身份引导去实名
                // FACE_NOTAUTH 企业签署，需要经办人刷脸 并且 经办人未实名
                // VIDEO_AUDIO_NOTAUTH 双录，经办人未实名
                this.toReceiverAuth();
            } else if (this.signStumblingBlock === 'FACE_VERIFY' || this.signStumblingBlock === 'FACE_ISAUTH') {
                /**
                 * FACE_VERIFY 个人签署需要刷脸
                 * FACE_ISAUTH 企业签署，需要经办人刷脸
                 */
                this.qrSignDialogVisible = true;
                // 非大陆人士不支持刷脸
            } else if (this.signStumblingBlock === 'FACE_FAIL') {
                // 非大陆人士，优先刷脸的场景，直接使用验证码验证
                if (this.receiver.faceFirst === true) {
                    this.signVaDialogVisible = true;
                } else {
                    this.$MessageToast(this.$t('sign.noSupportface'));
                }
            } else if (this.signStumblingBlock === 'VIDEO_AUDIO_VERIFY') {
                // this.dualSignDialogVisible = true;
                this.$MessageToast('必须双录校验的合同暂不支持在电脑web上签署');
            } else if (ifEncryptionSign && !this.isOtherSeal) {
                // 加密签署或者二要素认证签署
                this.isLoading = 1;
                const params = {
                    token: this.urlToken,
                    signPlatform: 'WEB',
                    archiveCollectContractId: this.archiveCollectContractId,
                };
                return this.postConfirmSign({
                    ...params,
                });
            } else {
                // 已注册用户，需要校验，根据配置hidePass决定是否校验签约密码 CODE_VERIFY
                this.signVaDialogVisible = true;
            }
        },
        // 引导进入经办人实名认证
        toReceiverAuth() {
            // 这里新做了参数去处理个人-企业的身份切换。考虑到小程序参数带有token会自动更新
            const params = {
                ...this.$route.query,
            };
            delete params.refreshToken;
            delete params.accessToken;
            this.$localStorage.set('transitionHref', location.origin + location.pathname + '?' + encodeSearchParams(params));
            // 前往个人实名
            location.href = `${location.origin}${authPath('sign', {
                contractId: this.$route.query.contractId,
                fromPath: encodeURIComponent(this.$localStorage.get('transitionHref')),
            })}`;
        },
        // 签署操作交互逻辑
        setNewMarkIntoDocList(newMark) {
            let setAttr = null;
            const markIndex = this.docList[this.selectedDocIdx].page[this.selectedPageIdx].marks.findIndex(mark => {
                return mark.labelId === newMark.labelId;
            });

            if (['BIZ_DATE', 'TEXT', 'NUMERIC_VALUE', 'DATE_TIME'].includes(newMark.type)) {
                setAttr = this.docList[this.selectedDocIdx].page[this.selectedPageIdx].marks[this.selectedMarkIndex];
            } else {
                this.$set(this.docList[this.selectedDocIdx].page[this.selectedPageIdx].normalMarks, this.selectedMarkIndex, newMark);
                this.$set(this.docList[this.selectedDocIdx].page[this.selectedPageIdx].marks, this.selectedMarkIndex, newMark);
                setAttr = this.docList[this.selectedDocIdx].page[this.selectedPageIdx].normalMarks[this.selectedMarkIndex];
            }

            this.$set(setAttr, 'status', newMark.status);

            // this.$set(this.docList[this.selectedDocIdx].page[this.selectedPageIdx].marks, markIndex, newMark);

            this.docList[this.selectedDocIdx].page[this.selectedPageIdx].marks.splice(markIndex, 1, {
                ...this.docList[this.selectedDocIdx].page[this.selectedPageIdx].marks[markIndex],
                ...newMark,
            });
            this.docList[this.selectedDocIdx].page.splice(
                this.selectedPageIdx,
                1,
                this.docList[this.selectedDocIdx].page[this.selectedPageIdx],
            );
            this.docList.splice(this.selectedDocIdx, 1, this.docList[this.selectedDocIdx]);
        },
        assignSelectedData(mark, docIndex, pageIndex, markIndex) {
            this.selectedDocIdx = docIndex;
            this.selectedPageIdx = pageIndex;
            this.selectedMarkIndex = markIndex;
            this.selectedLabelId = mark.labelId;
            this.selectedLabelValue = mark.value;
        },
        onLoading(res) {
            this.loading = res;
        },
        // 转给其他人签
        handleClickTransfer(mark, docIndex, pageIndex, markIndex) {
            this.transferDialogVisible = true;
            this.focusingMark = { mark, docIndex, pageIndex, markIndex };
        },
        handleClickCancel(mark, docIndex, pageIndex, markIndex) {
            this.focusingMark = { mark, docIndex, pageIndex, markIndex };
            this.postCancelSignatureLabel(mark.labelId)
                .then(res => {
                    this.newMark = res.data;
                });
        },
        onCompanyDeptAndMemberChoose(members) {
            this.postSignatureLabel('', members[0].empId, members[0].userId)
                .then(res => {
                    this.transferDialogVisible = false;
                    this.newMark = res.data;
                });
        },
        // 查询同文档内标签
        findLabelByDocIdAndType(type, documentId) {
            const docLabels = this.labels.filter(label => label.documentId === documentId);
            return docLabels.find(label => label.type === type);
        },
        // 签署人没有印章的时候
        async handleSealManagerNoSeal(sealListVisibleFromAction = 1) {
            if (this.hasManager) {
                const curEntSeals = await getEntSeals(this.commonHeaderInfo.currentEntId);
                if (curEntSeals.length) {
                    // 主管理员时，查询企业印章，有的话弹窗分配给自己
                    this.sealListVisible = true;
                    this.otherSeals = curEntSeals;
                    return;
                }
            }
            this.showDefaultElectronicSeal = true;
            this.sealListVisibleFromAction = sealListVisibleFromAction;
        },
        // 点击签名内容
        handleClickLabels(mark, docIndex, pageIndex, markIndex, action, params) {
            if (this.signType !== 'sign') {
                return;
            }
            const isSignQualified = this.checkSignQualified();
            if (!isSignQualified) {
                return;
            }
            const isSpecialSeal = mark.templateSpecialSealPreview && mark.templateSpecialSealPreview.useTemplateSpecialSeal;
            // 企业现有印章不满足专用章要求，弹出印章恢复申请弹窗
            if (isSpecialSeal && this.signDecision === 'ENTERPRISE_SEALS_NOT_MATCH_TEMPLATE_SPECIAL_SEAL') {
                this.specialSealInfo = mark.templateSpecialSealPreview;
                this.showRecoverSealPostDialog = true;
                return;
            }
            // 点击询证章
            if (mark.type === 'CONFIRMATION_REQUEST_SEAL') {
                const remark = this.findLabelByDocIdAndType('CONFIRMATION_REQUEST_REMARK', mark.documentId);
                const answerType = params[0];
                if (remark && remark.value && answerType === 'AGREE') {
                    return this.$MessageToast(this.$t('sign.notNeedRemark'));
                } // 您不需要填写备注
                this.answerType = answerType;
            }
            this.focusingMark = { mark, docIndex, pageIndex, markIndex };
            this.action = action;
            this.$nextTick()
                .then(() => {
                    const { hasAuthenticated, requireIdentityAssurance, receiverType } = this.receiver;
                    // 如果标签的类型是印章或者询证章
                    if (mark.type === 'SEAL' || mark.type === 'CONFIRMATION_REQUEST_SEAL') {
                        // 对于第一接收人，可选择印章。对于次位接收人，不可选
                        // parentReceiverId = 0 包含的信息： 1. 第一接收人； 2. 被分配之前的状态
                        // status = 'INACTIVE' 包含的信息：  1. 第一接收人； 2. 被分配之后的状态
                        if (mark.parentReceiverId === '0' || mark.status === 'INACTIVE') {
                            if (this.jaEntHasNotAuthenticated) {
                                const uploadSealFileId = params[1];
                                this.onClickUnAuthJaEntSeal(uploadSealFileId);
                                return;
                            }
                            // 对于未注册企业，且不需要实名，则展示“未认证企业印章”（未盖）
                            if (['CONFIRMED', 'ACTIVE'].includes(mark.status) && !hasAuthenticated && !requireIdentityAssurance) {
                                const uploadSealFileId = params[1];
                                this.onClickUnAuthJaEntSeal(uploadSealFileId);
                                return;
                            }
                            this.isLoading = true;
                            this.getSeals(isSpecialSeal ? mark.templateSpecialSealPreview.sealFileId : '')
                                .then(async() => {
                                    this.isLoading = false;
                                    if (!this.sealsLength) {
                                        if (receiverType === 'PROXY_SIGNER') {
                                            return this.$alert(this.$t('sign.noSealAvailable'), this.$t('sign.noSeal'), {
                                                confirmButtonText: this.$t('sign.submit'),
                                            });
                                        }
                                        // 对于未注册企业，且不需要实名，则展示“未认证企业印章”（已盖）
                                        if (mark.status === 'CONFIRMED' && !hasAuthenticated && !requireIdentityAssurance) {
                                            return this.$confirm(this.$t('sign.noSealToChoose'), this.$t('sign.electronicSeal'), {
                                                confirmButtonText: this.$t('sign.goToVerifyEnt'),
                                                cancelButtonText: this.$t('sign.cancel'),
                                            })
                                                .then(() => this.goToAuth())
                                                .catch(() => {});
                                        }
                                        if (this.isSealManager) {
                                            return this.handleSealManagerNoSeal(1);
                                        }
                                        return this.$alert(this.$t('sign.memberNoSealAvailable'), this.$t('sign.noSeal'), {
                                            confirmButtonText: this.$t('sign.noticeAdminFoSeal'),
                                        }).then(() => {
                                            this.$http.post('/ents/seals/notice-admin-configure-seal');
                                        });
                                    } else if (this.sealsLength === 1) {
                                        if (action === 'click') {
                                            // 盖章
                                            if (this.otherSeals.length === 1) { // 申请用印的印章需要走弹窗逻辑
                                                this.sealListVisible = true;
                                                this.sealListVisibleFromAction = 1;
                                            } else {
                                                const seal = this.mySeals[0];
                                                this.sealListVisibleFromAction === 3 && (this.sealListVisibleFromAction = 1);
                                                this.handleClickSeal(seal.sealId, seal.name);
                                            }
                                        } else if (action === 'switch') {
                                            if (this.receiver.receiverType === 'PROXY_SIGNER') {
                                                return this.$alert(this.$t('sign.noSwitchSealNeedDistribute'), {
                                                    confirmButtonText: this.$t('sign.knew'),
                                                });
                                            }
                                            // 没有可切换印章
                                            if (!this.receiver.hasAuthenticated) {
                                                // 未实名
                                                this.$confirm(this.$t('sign.noSealToChoose'), '', {
                                                    confirmButtonText: this.$t('sign.goToVerifyEnt'),
                                                    cancelButtonText: this.$t('sign.cancel'),
                                                })
                                                    .then(() => this.goToAuth())
                                                    .catch(() => {
                                                    });
                                            } else {
                                                this.sealListVisible = true;
                                                this.sealListVisibleFromAction = 1;
                                                // 已实名
                                                // if (this.isSealManager) {
                                                //     // 有印章权限
                                                //     this.$confirm(this.$t('signPC.toAddSealWithConsole'), '', {
                                                //         confirmButtonText: this.$t('signPC.toAddSeal'),
                                                //         cancelButtonText: this.$t('sign.cancel')
                                                //     })
                                                //         .then(() => {
                                                //             this.$router.push('/console/enterprise/seal/manage');
                                                //         })
                                                //         .catch(() => {
                                                //         })
                                                // } else {
                                                // 没有印章权限
                                                // this.$alert(this.$t('sign.noSwitchSealNeedAppend'), {
                                                //     confirmButtonText: this.$t('sign.knew')
                                                // });
                                                // }
                                            }
                                        }
                                    } else {
                                        this.sealListVisible = true;
                                        this.sealListVisibleFromAction = 1;
                                    }
                                }).finally(() => {
                                    this.isLoading = false;
                                });
                            // 对于被分配人，直接显示印章
                        } else {
                            this.$loading();
                            this.postSealLabel()
                                .then(res => {
                                    // this.newMark = res.data;
                                    this.updateSignatureData(res.data, 'putSeal');
                                })
                                .finally(() => {
                                    this.$loading().close();
                                });
                        }
                    } else if (mark.type === 'SIGNATURE') { // 如果标签类型是签名
                        // 如果标签状态为已确认 并且label动作为点击
                        if (mark.status === 'CONFIRMED' && action === 'click') { // 取消
                            if (this.receiver.handWriteNotAllowed) {
                                this.$MessageToast('发件人要求不允许手写签名');
                            } else {
                                this.handleWriteTabVisible = true;
                            }
                        } else if (mark.status === 'ACTIVE' || action === 'switch') { // 如果标签状态为有效或者动作为切换
                            // 如果指定了必须使用手写签名
                            if (this.receiver.forceHandWrite) {
                                this.handleWriteTabVisible = true;
                            } else {
                                this.curSelectedSignature = mark;
                                this.getSignatures()
                                    .then(res => {
                                        let signatures = res.data.map((item) => {
                                            return Object.assign({}, item, {
                                                ImgUrl: `/users/signatures/${item.sigId}/signature/${item.fileId}?access_token=${this.$cookie.get('access_token')}`,
                                            });
                                        });
                                        // 开启了笔记识别,那么只能选择已经识别通过的签名
                                        if (this.receiver.handWritingRecognition) {
                                            signatures = signatures.filter(item => item.isSameName);
                                        }
                                        // 如果没有签名 那么手写签名生成一个
                                        if (signatures.length === 0) {
                                            // if (this.getIsForeignVersion) {
                                            //     return this.createJaSealDialogVisible = true;
                                            // }
                                            this.handleWriteTabVisible = true;
                                            return;
                                        }
                                        // 发件方开启了‘不允许手写签名必须使用系统签名’选项
                                        if (this.receiver.handWriteNotAllowed) {
                                            signatures = signatures.filter(item => item.isSystemSignature);
                                        }
                                        this.signatures = signatures;
                                        this.signaturesListVisible = true;
                                        this.sensorsEventName && this.$sensors.track({
                                            eventName: `Ent_${this.sensorsEventName}Window_PopUp`,
                                            eventProperty: {
                                                page_name: `${this.sensorsPageName}`,
                                                window_name: '选择签名',
                                                ...this.sensorsTrackContractInfo,
                                            },
                                        });
                                    });
                            }
                        }
                    }
                });
        },
        updataMarkInDoc(docI, pageI, newMark) {
            const marks = this.docList[docI].page[pageI].marks;
            const markI = marks.findIndex(item => item.labelId === newMark.labelId);

            this.docList[docI].page[pageI].normalMarks.splice(markI, 1, newMark);
            this.docList[docI].page[pageI].marks.splice(markI, 1, newMark);
            this.docList[docI].page.splice(pageI, 1, this.docList[docI].page[pageI]);
            this.docList.splice(docI, 1, this.docList[docI]);
        },
        /**
         * @param {Object} mark 需要更新的标签
         * @description 处理标签数据的界面更新和同名字段处理
         */
        updateDocMarksArray(mark) {
            const docI = this.docList.findIndex(item => item.documentId === mark.documentId);

            if (mark.pageType === 'ALL') {
                const pageLength = (this.docList[docI].page || []).length;
                (new Array(pageLength).fill('', 0)).forEach((curPage, pIndex) => {
                    this.updataMarkInDoc(docI, pIndex, {
                        ...mark,
                        pageNumber: pIndex + 1,
                    });
                });
                return;
            }

            const pageI = this.labels.find(item => item.labelId === mark.labelId).pageNumber - 1;
            this.updataMarkInDoc(docI, pageI, mark);
        },
        /**
         * @param {Array} labelData 需要更新的标签list
         * @param {String} type 标签类型
         * @description 统一更新签名和印章数据
         */
        updateSignatureData(labelData, type, isUpdateToServer = false, autoSignSignatureSeal = true) {
            labelData.forEach(mark => {
                this.updateDocMarksArray(mark);
                if (isUpdateToServer) {
                    // 相同业务字段同步。由于pdfjs渲染，一次只展示一个文档，相同业务字段存在跨文档的情况，所有不能在label组件【dom不存在 组件被销毁】里面去做这个更新，手动触发更新
                    // 注意：混合云1.0 业务字段-日期类型上传到云端
                    // 混合云3.0业务字段上传到云端接口
                    if (!this.isHybridCloudContract && (['TEXT', 'TEXT_NUMERIC', 'BIZ_DATE', 'SINGLE_BOX', 'MULTIPLE_BOX', 'COMBO_BOX', 'NUMERIC_VALUE', 'DATE_TIME'].includes(mark.type)) || (this.isHybridCloudContract && mark.type === 'BIZ_DATE') || (this.isHybridCloudContract && this.$hybrid.isGamma())) {
                        this.$http.post(`/contract-api/contracts/${this.contractId}/labels/${mark.labelId}/fill-content`, {
                            value: mark.value,
                        });
                    }
                }

                if (autoSignSignatureSeal) {
                    // 更新装饰骑缝章
                    this.autoSignSignatureSeal().then(() => {
                        this.getDecorateRidingSeal();
                    });
                }
            });
            // 手写签名时下面这个提示会跟手写签名完成的提示重叠，先去掉
            if (labelData.length > 1 && type !== 'signature') {
                //  第一次盖章，会同步所有章
                let str =  this.$t('sign.hasSetTip', { index: labelData.length - 1 });
                switch (type) {
                    case 'signature':
                        str = this.$t('sign.hasSetSignatureTip', { index: labelData.length - 1 });
                        break;
                    case 'putSeal':
                        str = this.$t('sign.hasSetSealTip', { index: labelData.length - 1 });
                        break;
                    case 'applySeal':
                        str = this.$t('sign.hasApplyForSealTip', { index: labelData.length - 1 });
                        break;
                }
                setTimeout(() => {
                    this.$MessageToast.info(`${str}`);
                }, 500);
            }
        },
        // 从签名列表选择一个签名样式
        handleClickSignature(signatureId, sealName, isNew = false) {
            this.sensorsEventName && this.$sensors.track({
                eventName: `Ent_${this.sensorsEventName}Window_BtnClick`,
                eventProperty: {
                    page_name: `${this.sensorsPageName}`,
                    window_name: '选择签名',
                    icon_name: '选中签名',
                    contract_id: this.contractId,
                },
            });
            if (this.action === 'click') {
                const data = {
                    signatureId,
                    labelId: this.selectedLabelId,
                    employeeId: '',
                    userId: '',
                };
                if (this.getIsForeignVersion && isNew) {
                    data.content = sealName;
                }
                return this.postAllSignatureLabel(data).then(res => {
                    this.updateSignatureData(res.data, 'signature');
                    this.signaturesListVisible = false;
                }).then(() => {
                    this.sensorsEventName && this.$sensors.track({
                        eventName: `Ent_${this.sensorsEventName}Window_Result`,
                        eventProperty: {
                            page_name: `${this.sensorsPageName}`,
                            window_name: '选择签名',
                            is_success: true,
                            icon_name: '点击',
                            request_url: `${signPath}/contracts/${this.contractId}/labels/signature`,
                            ...this.sensorsTrackContractInfo,
                        },
                    });
                }).catch((err) => {
                    const errMsg = err.response?.data?.message;
                    const status = err.response?.status;
                    const code = err.response?.data?.code;
                    this.sensorsEventName && this.$sensors.track({
                        eventName: `Ent_${this.sensorsEventName}Window_Result`,
                        eventProperty: {
                            page_name: `${this.sensorsPageName}`,
                            window_name: '选择签名',
                            is_success: false,
                            icon_name: '点击',
                            fail_http_code: status,
                            fail_error_code: code,
                            fail_reason: errMsg,
                            request_url: `${signPath}/contracts/${this.contractId}/labels/signature`,
                            ...this.sensorsTrackContractInfo,
                        },
                    });
                });
            } else if (this.action === 'switch') {
                return this.postSignatureLabel(signatureId)
                    .then(res => {
                        this.sensorsEventName && this.$sensors.track({
                            eventName: `Ent_${this.sensorsEventName}Window_Result`,
                            eventProperty: {
                                page_name: `${this.sensorsPageName}`,
                                window_name: '选择签名',
                                is_success: true,
                                icon_name: '切换',
                                request_url: `${signPath}/contracts/${this.contractId}/labels/signature`,
                                ...this.sensorsTrackContractInfo,
                            },
                        });
                        this.signaturesListVisible = false;
                        this.newMark = res.data;
                        if (this.replaceAllSealAndSignature) {
                            syncAllSignLabel({ contractId: this.contractId, labelId: this.selectedLabelId })
                                .then(({ data: labels }) => {
                                    this.updateSignatureData(labels, 'signature');
                                    this.replaceAllSealAndSignature = false;
                                });
                        }
                    })
                    .catch((err) => {
                        const errMsg = err.response?.data?.message;
                        const status = err.response?.status;
                        const code = err.response?.data?.code;
                        this.sensorsEventName && this.$sensors.track({
                            eventName: `Ent_${this.sensorsEventName}Window_Result`,
                            eventProperty: {
                                page_name: `${this.sensorsPageName}`,
                                window_name: '选择签名',
                                is_success: false,
                                icon_name: '切换',
                                fail_http_code: status,
                                fail_error_code: code,
                                fail_reason: errMsg,
                                request_url: `${signPath}/contracts/${this.contractId}/labels/signature`,
                                ...this.sensorsTrackContractInfo,
                            },
                        });
                    });
            }
        },
        updateSignature(obj) {
            this.newMark = obj.label;
            if (obj.replaceAllSealAndSignature) {
                syncAllSignLabel({ contractId: this.contractId, labelId: obj.label.labelId })
                    .then((res) => {
                        this.updateSignatureData(res.data, 'signature');
                    });
            }
        },
        handleClickSeal(sealId, sealName, replaceAllSealAndSignature) {
            this.getOwners(sealId)
                .then(res => {
                    this.sealListVisible = false; // 关闭弹窗
                    this.owners = res.data.empSealVOS.map(item => {
                        const portraitSrc = `/ents/${item.entId}/employees/${item.empId}/portrait?access_token=${this.$cookie.get('access_token')}`;
                        return { ...item, portraitSrc };
                    });
                    const account = this.$store.state.commonHeaderInfo.platformUser.account;
                    // 两边的账号大小写不一致，所以统一小写之后进行比对
                    const isOwner = this.owners.filter(item => {
                        return item.account.toLowerCase() === account.toLowerCase();
                    });
                    if (isOwner.length) {
                        // 显示章样式
                        const owner = {
                            sealId: isOwner[0].sealId, // sealId
                            empId: isOwner[0].empId, // empId
                        };
                        // 新的骑缝章切换
                        if (this.sealListVisibleFromAction === 3) {
                            this.curRidingSealId = owner.sealId;
                            return this.$http.post(`/contract-api/contracts/${this.contractId}/labels/fill-decorate-riding-seal`, {
                                labelId: this.selectedLabelId,
                                signatureId: owner.sealId,
                            }).then(({ data }) => {
                                this.decorateRidingSealVos = this.decorateRidingSealVos.map(item => {
                                    if (item.labelId === data.labelId) {
                                        return {
                                            ...item,
                                            ...data,
                                        };
                                    }
                                    return item;
                                });
                            });
                        }
                        this.$loading();
                        this.postSealLabel(owner)
                            .then(res => {
                                this.sensorsEventName && this.$sensors.track({
                                    eventName: `Ent_${this.sensorsEventName}Window_Result`,
                                    eventProperty: {
                                        page_name: `${this.sensorsPageName}`,
                                        window_name: '选择印章',
                                        is_success: true,
                                        icon_name: '确定',
                                        request_url: this.answerType ? `${signPath}/contracts/${this.contractId}/labels/${this.selectedLabelId}/confirmation-request-seal` : `${signPath}/contracts/${this.contractId}/labels/${this.selectedLabelId}/seal-all`,
                                        ...this.sensorsTrackContractInfo,
                                    },
                                });
                                // this.newMark = res.data;
                                if (this.sealListVisibleFromAction === 1) {
                                    this.updateSignatureData(res.data, 'putSeal');
                                } else if (this.sealListVisibleFromAction === 2) {
                                    const data  = res.data && res.data[0] || {};
                                    this.updateRridingSealData({
                                        value: data.value,
                                        sealId: data.sealId,
                                    });
                                    this.curRidingSealId = data.sealId;
                                }

                                if (replaceAllSealAndSignature) {
                                    syncAllSignLabel({ contractId: this.contractId, labelId: this.selectedLabelId })
                                        .then((res) => {
                                            this.updateSignatureData(res.data, 'putSeal');
                                        });
                                }
                            })
                            .catch((err) => {
                                const errMsg = err.response?.data?.message;
                                const status = err.response?.status;
                                const code = err.response?.data?.code;
                                this.sensorsEventName && this.$sensors.track({
                                    eventName: `Ent_${this.sensorsEventName}Window_Result`,
                                    eventProperty: {
                                        page_name: `${this.sensorsPageName}`,
                                        window_name: '选择印章',
                                        is_success: false,
                                        icon_name: '确定',
                                        fail_http_code: status,
                                        fail_error_code: code,
                                        fail_reason: errMsg,
                                        request_url: this.answerType ? `${signPath}/contracts/${this.contractId}/labels/${this.selectedLabelId}/confirmation-request-seal` : `${signPath}/contracts/${this.contractId}/labels/${this.selectedLabelId}/seal-all`,
                                        ...this.sensorsTrackContractInfo,
                                    },
                                });
                            })
                            .finally(() => {
                                this.$loading().close();
                            });
                    } else {
                        this.sealListVisible = true;
                    }
                });
        },
        handleClickOwner(owner) {
            // 必填的业务字段还未填写
            if (!this.checkoutAllWriteDone().necessDone) {
                return this.$alert(this.$t('sign.fillFirst'), {
                    confirmButtonText: this.$t('sign.submit'),
                    callback: () => {
                        this.$refs.doc.scrollToMark(this.checkoutAllWriteDone().necessList[0]);
                    },
                });
            }
            // 必须上传的附件为上传
            if (this.needAttach) {
                return this.$alert(this.$t('sign.uploadFileOnRightSite'), {
                    confirmButtonText: this.$t('sign.submit'),
                    callback: () => {
                        this.activeTab = 'FILL';
                    },
                });
            }
            this.$loading();
            // 根据sealId, account获取章样式图片
            this.postSealLabel(owner)
                .then(res => {
                    this.sensorsEventName && this.$sensors.track({
                        eventName: `Ent_${this.sensorsEventName}Window_Result`,
                        eventProperty: {
                            page_name: `${this.sensorsPageName}`,
                            window_name: '他人盖章',
                            is_success: true,
                            icon_name: '确定',
                            request_url: this.answerType ? `${signPath}/contracts/${this.contractId}/labels/${this.selectedLabelId}/confirmation-request-seal` : `${signPath}/contracts/${this.contractId}/labels/${this.selectedLabelId}/seal-all`,
                            ...this.sensorsTrackContractInfo,
                        },
                    });
                    // this.newMark = res.data;
                    this.updateSignatureData(res.data, 'applySeal');
                    // this.isApplySeal = true;
                    this.isOtherSeal = true;
                    const sealLabels = this.labels.filter(label => label.type === 'SEAL' && label.status !== 'INACTIVE');
                    if (!sealLabels.length) { // 自己没有印章
                        this.toSign(); // 申请用印过后直接提示并返回列表页
                    } else {
                        this.$confirm(this.sealLabelsTip,  this.$t('sign.prompt'), {
                            confirmButtonText: this.$t('sign.continue'),
                            cancelButtonText: this.$t('sign.cancel'),
                        }).then(() => {
                            this.toSign();
                        });
                    }
                })
                .catch((err) => {
                    const errMsg = err.response?.data?.message;
                    const status = err.response?.status;
                    const code = err.response?.data?.code;
                    this.sensorsEventName && this.$sensors.track({
                        eventName: `Ent_${this.sensorsEventName}Window_Result`,
                        eventProperty: {
                            page_name: `${this.sensorsPageName}`,
                            window_name: '他人盖章',
                            is_success: false,
                            icon_name: '确定',
                            fail_http_code: status,
                            fail_error_code: code,
                            fail_reason: errMsg,
                            request_url: this.answerType ? `${signPath}/contracts/${this.contractId}/labels/${this.selectedLabelId}/confirmation-request-seal` : `${signPath}/contracts/${this.contractId}/labels/${this.selectedLabelId}/seal-all`,
                            ...this.sensorsTrackContractInfo,
                        },
                    });
                })
                .finally(() => {
                    this.$loading().close();
                });
        },
        /**
         *
         * @param {参数类型} action触发的动作
         * @return {返回值类型} 返回值说明
         * @desc 点击签名上面tab的"手写"触发，这里改变了action为switch，所以从PC版的手动签的签名手写进去的时候，
         */
        handleClickHandWrite(mark, docIndex, pageIndex, markIndex, action) {
            this.action = action;
            this.handleWriteTabVisible = true;
            this.focusingMark = { mark, docIndex, pageIndex, markIndex };
        },
        // 点击添加手绘签名的处理
        handleClickAddHandWrite() {
            this.action = 'addSignature';
            this.handleWriteTabVisible = true;
        },
        // 选择其他签约主体[签署方拖拽逻辑还在用]
        clickOtherEntSubjectBtn(i) {
            this.switchName = this.isEnt ? this.otherEntSubject[i].entName : this.receiver.signerName;
            this.switchEntId = this.isEnt ? this.otherEntSubject[i].entId : '';
            this.signQualifiedDialogVisible = true;
        },
        // 手写完成，若在pc端直接手写，data.resData为图片数据， 若在pc扫码h5手写，data.resData为空
        onWriteDone(data) {
            if (this.action === 'switch') {
                // 更新所有签名--移动端可能勾选了应用在所有印章签名选项
                this.getAllWritedLabel()
                    .then(res => {
                        const filterSignatureData = res.data.filter(label => label.type === 'SIGNATURE');
                        this.updateSignatureData(filterSignatureData, 'signature');
                        this.handleWriteTabVisible = false;
                    });
            }
            if (this.action === 'click') {
                // 多个签名更新
                if (data && data.resData) {
                    this.updateSignatureData(data.resData, 'signature');
                    this.handleWriteTabVisible = false;
                }
                this.getAllWritedLabel()
                    .then(res => {
                        const filterSignatureData = res.data.filter(label => label.type === 'SIGNATURE');
                        this.updateSignatureData(filterSignatureData, 'signature');
                        this.handleWriteTabVisible = false;
                    });
            }
        },
        // 不写了，关闭弹窗
        onWriteClose() {
            this.handleWriteTabVisible = false;
        },
        // 扫码签名保存后更新签名列表
        onUpdateSignature() {
            return this.getSealsAndSignatureList()
                .then(() => {
                    this.onWriteClose();
                    this.$MessageToast({
                        message: this.$t('sign.savedOnLeftSite'),
                        type: 'success',
                    });
                });
        },
        onUpdateAttachment(attachment, index) {
            const old = this.attachmentList[index];
            this.$set(this.attachmentList, index, {
                ...attachment,
                name: old.name,
                comment: old.comment,
                necessary: old.necessary,
            });
        },
        // 校验是否全部签署完毕, 只检查签名或盖章处
        checkoutSignDone() {
            // 检查签名/盖章处中是否有位置为ACTIVE，待填充状态
            return !this.labels.some(item =>
                item.status === 'ACTIVE' && this.signRequiredLables.includes(item.type),
            );
        },
        // 校验所有字段是否全部填写完毕，包括选填
        checkoutAllWriteDone() {
            // const d = document;
            // todo 这里其实应该判断 label 的 status
            const necessary = this.writeLabels.filter(label => {
                // 签署人上传图片做特殊处理 处理value当前的url
                // if (label.type === 'PICTURE') {
                //     return label.required === true && (!label.value.split('/')[6]);
                // }
                if (label.type === 'DATE_TIME') { // 时刻要单独处理
                    return label.required && !label.value;
                }
                return label.required === true && label.value.replace(/[\s\r\n]/g, '') === '';
            });
            const unnecessary = this.writeLabels.filter(label => {
                if (label.type === 'DATE_TIME') { // 时刻要单独处理
                    return !label.required && !label.value;
                }
                return label.required === false && label.value.replace(/[\s\r\n]/g, '') === '';
            });
            return {
                allDone: [...necessary, ...unnecessary].length === 0,
                allList: [...necessary, ...unnecessary],
                necessDone: necessary.length === 0,
                necessList: necessary,
                unnecessDone: unnecessary.length === 0,
                unnecessList: unnecessary,
            };
        },
        // 检查存在备注需要填写（选择了不符合章，必须填备注）
        checkoutNeedRequestRemarkDone() {
            const requestSeals = this.labels.filter(label => label.type === 'CONFIRMATION_REQUEST_SEAL' && label.answerType === 'REFUSE');
            let needRemarkWriteDone = false;
            for (let i = 0; i < requestSeals.length; i++) {
                const requestRemark = this.labels.find(label => label.documentId === requestSeals[i].documentId  && label.type === 'CONFIRMATION_REQUEST_REMARK' && !label.value);
                if (requestRemark) {
                    needRemarkWriteDone = requestRemark;
                    break;
                }
            }
            return needRemarkWriteDone;
        },
        // 获取合同文本别名
        getContractTextAlias() {
            return this.$http.get(`/contract-api/ignore/contracts/${this.contractId}/query-displayed-contract-textAlias`)
                .then((res) => {
                    if (res.data && res.data.displayedTextCode) {
                        this.contractAlias = res.data.displayedTextCode;
                    }
                });
        },
        // 跳到个人或企业认证页面，短链接登录时跳到签署引导页，pc登录时跳实名认证页
        goToAuth(isForcePersonAuth = false/* 强制走个人实名 */) {
            this.$localStorage.set(
                'contractsFaceConfig',
                JSON.stringify({
                    contractIds: [this.contractId],
                }),
            ); // 刷脸供应商根据contractId查询
            // 改为均走签署引导页面
            const entGuideUrl = this.$cookie.get('sino-russia') === '1' ? '/sign/guide/sino-russia' : `/ent/sign/guide?contractId=${this.contractId}`;
            const pushUrl = (this.isEnt && !isForcePersonAuth) ? entGuideUrl : authPath('sign', {
                fromPath: encodeURIComponent(this.$route.fullPath),
                contractId: this.contractId,
                signToAuthIsSupplement: this.receiver.signDecision === 'MORE_AUTHENTICATE',
            });
            this.$router.push(pushUrl);
        },
        // 点击未认证日本企业电子章
        onClickUnAuthJaEntSeal(sealFileId) {
            this.$loading();
            this.$http.post(`${signPath}/contracts/${this.contractId}/labels/${this.selectedLabelId}/seal-all`, {
                fileId: sealFileId,
            }).then(res => {
                this.updateSignatureData(res.data, 'putSeal');
            }).finally(() => {
                this.$loading().close();
            });
        },
        // 点击未认证企业电子章
        onClickUnauthSeal() {
            this.$loading();
            this.$http.post(`${signPath}/contracts/${this.contractId}/labels/${this.selectedLabelId}/seal-all`, {
                signatureId: '0',
            }).then(res => {
                // this.newMark = res.data;
                this.updateSignatureData(res.data, 2);
            }).catch(() => {
            }).finally(() => {
                this.$loading().close();
                this.unauthSeal = false;
            });
        },
        // 获取当前合同接收者相关信息
        getReceiver() {
            const agreedArchiveCollect = !!this.archiveCollectContractId;
            return this.$http.get(`${signPath}/contracts/${this.contractId}/receivers/current-operating?type=${this.signType}&token=${this.urlToken}&agreedArchiveCollect=${agreedArchiveCollect}`, { noToast: 1 });
        },
        // 查看合同是否需要补充登陆
        checkContractViewNeedLogin(smsOnlyMust = false) {
            const passWordTime = this.$cookie.get('login-psd');
            const smsTime = this.$cookie.get('login-sms');
            const fiveMin = 5 * 60 * 1000;
            const now = new Date().getTime();
            const smsHasValidInTime = smsTime && (now - smsTime) < fiveMin;
            const pwdHasValidInTime = passWordTime && (now - passWordTime) < fiveMin;
            // 必须验证码核验：如果是非验证码登录，则需要登录
            if (smsOnlyMust) {
                return !smsTime;
            }
            // 双因子场景，5min内如果没有密码+验证码校验，需要补充登录
            return !(pwdHasValidInTime && smsHasValidInTime);
        },
        async getReceiverCallback() {
            // 常规审批，即非 无权限审批
            const normalApproval = !(this.signType === 'approval'  && this.permission);
            (normalApproval ? this.getReceiver() : Promise.resolve({ abnormalApproval: true }))
                .then(async(res) => {
                    // 非常规审批，即无权限审批，直接return，不需要走后面的逻辑
                    if (res.abnormalApproval) {
                        // 获取合同信息，需要对异常信息特殊处理时，noToast为1，错误不在axios响应拦截捕获
                        return this.getContractInfo({ noToast: 1 });
                    }
                    if (res.data.needDoubleCheckLogin && this.checkContractViewNeedLogin(res.data?.viewVerificationCodeCheck)) {
                        const commonHeaderInfo = this.$store.state.commonHeaderInfo;
                        location.replace(joinPathnameAndQueryObj(`${location.origin}/account-center/lg-supplement`, {
                            from: 'contract-view',
                            redirect: location.href,
                            account: commonHeaderInfo.platformUser.account,
                            chosenEntId: commonHeaderInfo.currentEntId,
                            smsOnly: res.data?.viewVerificationCodeCheck,
                            access_token: this.$cookie.get('access_token'),
                            refresh_token: this.$cookie.get('refresh_token'),
                        }));
                        return;
                    }
                    // 进入时未加入企业，后加入了企业刷新进来，token需要刷新
                    if (res.data.needSwitchEnt) {
                        await this.refreshAccout(res.data.enterpriseId);
                    }
                    const isPageIntercept =  await needToAuthInterceptPage(this.contractId, this.signOperationType === 'CHOOSE_PAPER_SIGN');
                    if (isPageIntercept) {
                        return;
                    }

                    const receiverInfo = res.data;
                    const { mustReadBeforeSign, alreadyReadBeforeSign, readBeforeSignType } = res.data;
                    // 签署页面15s的强制阅读时间，改为每个签署人只需阅读一次
                    if (mustReadBeforeSign && !alreadyReadBeforeSign) {
                        this.isReading = true;
                        this.isScrollLimitSign = readBeforeSignType === '2'; // 是否是滚动限制阅读头部类型，1 时间要求15s，2 滑动到底部
                    }
                    this.receiver = receiverInfo;
                    if (!this.hasGetReturn) {
                        await this.getReturnUrl();
                    }
                    this.signDecision = receiverInfo.signDecision;
                    this.ifExistThirdPartyApprove = receiverInfo.ifExistThirdPartyApprove || false;

                    // 签署者获取发件方企业配置
                    this.getSenderEntConfig();
                    if (this.signDecision === 'NEED_SET_APPROVAL_FLOW') {
                        this.getDefines();
                    }
                    if (this.signDecision === 'ARCHIVE_COLLECT') {
                        this.getArchiveToken();
                    }
                    // 附件要求
                    this.attachmentList = (receiverInfo.attachmentList || []).map(e => {
                        return {
                            ...e,
                            fileList: e.fileList || [],
                        };
                    });
                    this.activeTab = this.needAttach ? 'FILL' : 'PREVIEW';
                    // 是否要求签署人拖章
                    const verifyConfig = receiverInfo.verifyConfig;
                    if (verifyConfig && verifyConfig.dropLabelOperator &&
                        verifyConfig.dropLabelOperator === 'SIGNER'
                    ) {
                        this.signerDragAble = true;
                    }
                    // 是否是新版本的签署人自定义签署位置
                    const signerDragLabelTypeList = receiverInfo.signerDragLabelTypeList;
                    if (signerDragLabelTypeList && Array.isArray(signerDragLabelTypeList)) {
                        // 为新版本的签署人指定签署位置
                        this.signerDragLabelTypeList = signerDragLabelTypeList;
                    } else {
                        this.signerDragLabelTypeList = [];
                    }
                    // 获取合同信息，需要对异常信息特殊处理时，noToast为1，错误不在axios响应拦截捕获
                    return this.getContractInfo({ noToast: 1 });
                })
                // 串行执行，记住return，确保后面的promise在前面的异步操作resolve之后调用
                .then(res => {
                    // this.$refs.signIdentityGuide.setSignWithSameEntityVar();

                    const contractData = res.data;
                    this.contractData = contractData;
                    const { contractId, contractTitle, contractTypeName, contractStatus, sendUserEnterpriseId, sendUserEnterpriseName, businessTags, ifPersonalCanPhraseSegmentation, ifCrossPlatformContract, ifJustUseBestSignCa } = this.contractData;
                    this.ifJustUseBestSignCa = ifJustUseBestSignCa;
                    this.ifPersonalCanPhraseSegmentation = ifPersonalCanPhraseSegmentation;
                    this.jaEntHasNotAuthenticated = !ifCrossPlatformContract && this.getIsForeignVersion && this.userType === 'ENTERPRISE' && this.signDecision === 'USE_DEFAULT_IDENTITY';
                    this.sensorsTrackContractInfo = {
                        contract_id: contractId,
                        contract_name: contractTitle,
                        contract_type: contractTypeName,
                        contract_status: contractStatus,
                        contract_tag_list: (businessTags || []).map(item => item.name),
                        company_id: sendUserEnterpriseId,
                        company_name: sendUserEnterpriseName || '个人发件方',
                        type: this.sensorsEventName === 'ContractApproval' ? (+this.receiver.defType === 10 ? '发件方审批' : '收件方审批') : null,
                    };
                    this.sensorsEventName && this.$sensors.track({
                        eventName: `Ent_${this.sensorsEventName}_PageView`,
                        eventProperty: {
                            page_name: `${this.sensorsPageName}`,
                            ...this.sensorsTrackContractInfo,
                        },
                    });
                    return this.$hybrid.decideContractEnv(contractData.systemType, contractData.deltaHybridVersion, this.contractId);
                }).then(() => {
                    // 如果是混合云合同且不在企业内网中时的处理
                    if (this.contractData.systemType === 'HYBRID_CLOUD' && !this.isInLan) {
                        this.isLoading = 0;
                        this.hybridContractServerConnected = false;
                    }
                    // 签署人自定义签署位置，若接收方满足签署条件则获取印章或签名列表
                    if (this.signerDragAble) {
                        return this.getSealsAndSignatureList();
                    } else {
                        // 判断是签署的情况先自动填充签名、印章,签署人自定义签署位置不需要调用
                        if (this.signType !== 'view' && this.signType !== 'approval') {
                            return this.autoSignSignatureSeal().then(() => {
                                this.getDecorateRidingSeal();
                            });
                        } else if (this.signType === 'approval' && !this.permission) {
                            this.getDecorateRidingSeal();
                        }
                    }
                })
                .then(() => this.getDocs())
                .then(async res => {
                    this.docList = await this.initDocData(res.data.filter(item => item.documentId));
                    this.$store.state.currentSignDocumentId = this.docList[0].documentId;
                    this.$store.state.docList = this.docList;
                    this.checkCanUseAiChat();
                    const temp = res.data.filter(doc => doc.ridingSealLabel);
                    if (temp && temp.length) {
                        // 已经设置了骑缝章
                        this.ridingSealData = temp.map(item => item.ridingSealLabel);
                        this.isShowRidingSeal = true;
                    }
                    this.getContractTextAlias(); // 获取别名接口不影响界面渲染，放在这里
                    // 获取纸质签署的信息
                    if (this.signType === 'sign') {
                        this.getPaperSignInfo();
                    }
                    if (this.isHybridCloudContract && this.$hybrid.isAlpha()) {
                        return this.fetchAlphaHybridContractBusiLabelsInfo()
                            .then(hybridLabels => {
                                this.docList.forEach((doc) => {
                                    doc.page.forEach((page) => {
                                        page.marks.forEach((mark) => {
                                            const label = hybridLabels.find(
                                                (label) => label.name === mark.name,
                                            );
                                            if (label) {
                                                mark.value = label.value;
                                                mark.status = label.status;
                                            }
                                        });
                                    });
                                });
                                return Promise.resolve();
                            });
                    } else {
                        return Promise.resolve();
                    }
                })
                .then(() => this.approvalHighLight())
                .then(() => this.getEncryptionSignConfig())
                .then(() => {
                    this.isLoading = 0;
                })
                .catch(err => {
                    this.isLoading = 0;
                    if (err.response && err.response.data) {
                        const errCode = err.response.data.code;
                        const errMsg = err.response.data.message;
                        let revokeReason;
                        try {
                            revokeReason = JSON.parse(err.response.data.data).cancelReason || '';
                        } catch (err) {
                            revokeReason = '';
                        }
                        // 根据

                        //  无权限审批，只报错不跳转
                        if (!normalApproval) {
                            return this.$MessageToast.error(errMsg);
                        }

                        // 登录进来
                        if (this.channel !== 'notice') {
                            return this.$MessageToast.error(errMsg)
                                .then(() => {
                                    if (errCode === '130536') {
                                        this.$router.push(`/sign/sign-tip/face-sign-qrcode?contractId=${this.contractId}&receiverId=${this.receiver.receiverId}&channel=${this.channel}`);
                                    } else {
                                        /* 如果客户定义了跳转地址，则首先跳转 */

                                        if (this.returnUrl) {
                                            goReturnUrl(this.returnUrl);
                                            return;
                                        }
                                        this.$router.push('/doc/list');
                                    }
                                });
                        }
                        // 链接进来,根据code不同跳到不同状态的合同详情页 SignTip.vue
                        this.status = signCodeStatusMap[errCode];

                        this.$nextTick()
                            .then(() => {
                                if (this.status > 0) {
                                    /* 如果客户定义了跳转地址，则首先跳转 */
                                    if (this.returnUrl) {
                                        this.$MessageToast.error(errMsg).then(() => {
                                            goReturnUrl(this.returnUrl);
                                        });
                                        return;
                                    }
                                    this.$router.push(`/sign/sign-tip?status=${this.status}&contractId=${this.contractId}&type=${this.signType}&receiverId=${this.receiver.receiverId}&revokeReason=${revokeReason}`);
                                } else {
                                    this.$MessageToast.error(errMsg);
                                }
                            });
                    }
                });
        },
        getArchiveToken() {
            this.$http.get(`/octopus-api/box/customer/collection/${this.receiver.archiveId}/${this.contractId}/token?receiverId=${this.receiver.receiverId}`).then(res => {
                this.archiveToken = res.data.value;
            });
        },
        // 获取合同摘要信息
        getContractInfo(opts) {
            return this.$http.get(`${signPath}/contracts/${this.contractId}?type=${this.signType}&signPlatform=WEB&permission=${this.permission}`, opts);
        },
        // 获取合同文档信息，showLabels=1 带标签
        getDocs() {
            return this.$http.get(`${signPath}/contracts/${this.contractId}/documents`, {
                params: {
                    receiver: this.receiver.receiverId || 0,
                    showLabels: '1',
                    permission: this.permission,
                },
            });
        },
        // 用户CA证书信息
        getCAcertInfo(isEnt) {
            const reqURL = isEnt ? '/ents/auth/cacert' : 'users/auth/cacert';
            return this.$http.get(reqURL);
        },
        // 自定义签署位置，获取我的签名或印章
        getSealsAndSignatureList() {
            if (this.isEnt) {
                // 企业身份签署，获取印章
                return this.getSeals();
            } else {
                // 个人身份签署，获取签名
                return this.getSignatures()
                    .then(res => {
                        this.signatures = res.data.reverse();
                    });
            }
        },
        // 创建默认签名（如果已经有，则不会创建）
        generateDefaultSignature() {
            // 只有设置了「不允许手写签名」才会创建默认签名
            if (!this.receiver.handWriteNotAllowed) {
                return Promise.resolve();
            }
            return new Promise((resolve) => {
                return this.$http.post('/users/signatures/default-seal')
                    .catch(() => {
                    })
                    .finally(() => {
                        resolve();
                    });
            });
        },
        // signatures
        getSignatures() {
            // return this.$http.get('/users/signatures');
            // 获取签名前如果没有签名则新建一个默认签名
            return this.generateDefaultSignature()
                .then(() => {
                    return this.$http.get('/users/signatures');
                });
        },
        // 获取公司所有印章列表
        getSeals(sealFileId) {
            // 代理签署时，获取公司被授权的印章
            if (this.receiver.receiverType === 'PROXY_SIGNER') {
                return this.$http.post('/contract-api/contracts/proxy-sign/seals', [this.contractId])
                    .then(res => {
                        const result = res.data.result;
                        this.mySeals = (result[this.contractId] || []).filter(item => item.proxyEntId === this.proxySignNominalEntId).map(seal => {
                            seal.sealImg = `/ents/${seal.sealId}/seal/${seal.fileId}?access_token=${this.$cookie.get('access_token')}`;
                            return seal;
                        });
                        this.otherSeals = [];
                    });
            } else {
                // sealFileId：专用章fileid，专用章场景下需要过滤出专用章图案对应的印章数据
                let url = `/ents/detail/seals-with-owners${sealFileId ? '?fileId=' + sealFileId : ''}`;
                url = url.includes('?') ? `${url}&contractId=${this.contractId}` : `${url}?contractId=${this.contractId}`;
                return this.$http.get(url)
                    .then((res) => {
                        const useAbleSeals = res.data.useAbleSeals;
                        const unUseAbleSeals = res.data.unUseAbleSeals;
                        this.mySeals = Array.isArray(useAbleSeals) ? useAbleSeals.filter(item => item.isUsing).map(item => {
                            item.sealImg = `/ents/${item.sealId}/seal/${item.fileId}?access_token=${this.$cookie.get('access_token')}`;
                            return item;
                        }) : [];
                        this.otherSeals = Array.isArray(unUseAbleSeals) ? unUseAbleSeals.filter(item => item.isUsing).map(item => {
                            item.sealImg = `/ents/${item.sealId}/seal/${item.fileId}?access_token=${this.$cookie.get('access_token')}`;
                            return item;
                        }) : [];
                    });
            }
        },
        getOwners(sealId) {
            return this.$http.get(`/ents/seals/user/${sealId}`);
        },
        postSealLabel(owner) {
            const data = owner ? {
                signatureId: owner.sealId, // sealId
                employeeId: owner.empId, // empId
            } : {};
            if (this.answerType) { // 询证章更新
                data.answerType = this.answerType;
                return this.$http.post(`${signPath}/contracts/${this.contractId}/labels/${this.selectedLabelId}/confirmation-request-seal`, data);
            } else {
                // url + receiverId
                return this.$http.post(`${signPath}/contracts/${this.contractId}/labels/${this.selectedLabelId}/seal-all`, data);
            }
        },
        postSignatureLabel(signatureId = '', employeeId = '', userId = '') {
            return this.$http.post(`${signPath}/contracts/${this.contractId}/labels/${this.selectedLabelId}/signature`, {
                signatureId, // 签名样式Id
                employeeId,
                userId,
            });
        },
        postAllSignatureLabel(data) {
            return this.$http.post(`${signPath}/contracts/${this.contractId}/labels/signature`, data);
        },
        postCancelSignatureLabel(labelId) {
            return this.$http.post(`${signPath}/contracts/${this.contractId}/labels/${labelId}/sign/cancel`);
        },
        getWritedLabel() {
            return this.$http.get(`${signPath}/contracts/${this.contractId}/labels/${this.selectedLabelId}?isSigning=true`);
        },
        getAllWritedLabel() {
            return this.$http.get(`${signPath}/contracts/${this.contractId}/receivers/${this.receiver.receiverId}/labels`);
        },
        // 企业控制台——添加印章
        goEntConsole() {
            window.open('/console/enterprise/seal');
        },
        // 点击添加印章时，若企业有印章，则去印章管理页，若无，显示默认电子章弹窗
        handleAddSeal() {
            if (this.sealsLength) {
                this.goEntConsole();
            } else {
                this.handleSealManagerNoSeal(1);
            }
        },
        // 更换签约主体
        postSwitchIdentity(switchId) {
            const postData = this.isEnt ? { switchEntId: switchId } : { switchUserId: switchId };
            return this.$http.post(`${signPath}/contracts/${this.contractId}/receivers/${this.receiver.receiverId}/switch-identity`, postData);
        },
        // 刚进页面自动填充签名和印章
        autoSignSignatureSeal() {
            // 未实名且需要经办人实名的/个人未实名不自动签名(企业经办人不需要实名也可以自动盖章)
            if (!this.receiver.hasUserAuthenticated && !(this.isEnt && !this.receiver.requireEnterIdentityAssurance)) {
                return Promise.resolve();
            }
            return this.generateDefaultSignature()
                .then(() => {
                    return this.$http.post(`/contract-api/contracts/${this.contractId}/auto-sign`);
                });
        },
        // 点击使用默认电子章的处理
        createElectronicSealDoneFun(resData) {
            // 由签署人拖章时，点击使用默认电子章，更新左边印章栏列表
            if (this.signerDragAble) {
                this.getSeals();
            } else {
                this.handleClickSeal(resData.sealId, '');
            }
            this.showDefaultElectronicSeal = false; // 关闭弹窗
        },
        /**
         * @param  {Object}   data 确认签署提交的密码，验证码等数据
         * @return {promise}
         * @desc   确认签署发送的请求
         */
        postConfirmSign(data) {
            // 混合云合同签署时提交type=text的labels值
            if (this.isHybridCloudContract) {
                data = { signerFillLabels: this.textLabels, ...data };
            }
            // 加密签署或二要素认证签署
            if (this.getIsForeignVersion && !this.isOtherSeal) {
                data = {
                    encryptionSignPassword: this.encryptionSignParam.password || null,
                    twoFactorAuthenticationPassword: this.encryptionSignParam.dynamicCode || null,
                    ...data,
                };
            }
            return this.$hybrid.makeRequest({
                url: `${signPath}/contracts/${this.contractId}/confirm`,
                hybridTarget: '/contracts/confirm',
                method: 'post',
                data: {
                    contractId: this.contractId, // 混3 在 data 中增加 contractId
                    ...data,
                    ...(this.isVisitor && { token: this.urlToken }),
                },
                contractId: this.contractId,
            }).then(() => {
                this.sensorsEventName && this.$sensors.track({
                    eventName: `Ent_${this.sensorsEventName}_Result`,
                    eventProperty: {
                        page_name: `${this.sensorsPageName}`,
                        is_success: true,
                        request_url: `${signPath}/contracts/${this.contractId}/confirm`,
                        icon_name: '确认签署',
                        contract_sender: this.contractData?.sendAccount || null,
                        ...this.sensorsTrackContractInfo,
                    },
                });
                this.handleBizPoint();
                // 操作完成
                return this.$MessageToast.success(this.isApplySeal ? this.$t('sign.appliedSeal') : this.$t('signPC.operationCompleted'))
                    .then(() => {
                        this.goAfterSign();
                    });
            }).catch((err) => {
                const errMsg = err.response?.data?.message;
                const status = err.response?.status;
                const code = err.response?.data?.code;
                this.sensorsEventName && this.$sensors.track({
                    eventName: `Ent_${this.sensorsEventName}_Result`,
                    eventProperty: {
                        page_name: `${this.sensorsPageName}`,
                        is_success: false,
                        fail_http_code: status,
                        fail_error_code: code,
                        fail_reason: errMsg,
                        request_url: `${signPath}/contracts/${this.contractId}/confirm`,
                        icon_name: '确认签署',
                        contract_sender: this.contractData?.sendAccount || null,
                        ...this.sensorsTrackContractInfo,
                    },
                });
            }).finally(() => {
                this.isLoading = 0;
            });
        },
        // 筛选出不需要填写内容的标签
        filterNormalMarks(marks) {
            return marks.filter(mark => !this.writeRequiredLables.includes(mark.type));
        },
        // 筛选出可填写内容的标签
        filterWriteAbleLabels(marks) {
            return marks.filter(mark => this.writeRequiredLables.includes(mark.type));
        },
        // 确认风险后继续签署
        handleConfirmRisk() {
            this.riskTipDialogVisible = false;
            this.confirmRiskTipNextStep = true;
            this.toNext();
        },
        /**
         * @param   {object} docList 合同文档信息
         * @return  {object} 返回经过处理的合同文档信息
         * @desc    $hybrid.getContractImg 对混合云合同图片路径重定向，normalMarks: 不需要填写内容的标签，writeLabels: 需要填写内容的标签
         */
        async initDocData(docList) {
            console.log('docList = ', docList);
            // const pageLength = 0;
            return await Promise.all(docList.map(async(doc) => {
                // 兼容混合云pdf文件预览
                doc.pdfurl = await this.$hybrid.getPdfPreviewUrl({ url: `${doc.fileStreamUrl}&permission=${this.permission}`, hybridServer: this.hybridServer, hybridTarget: '/contract/part/document/download', params: { contractId: this.contractId, documentIds: doc.documentId, permission: this.permission }, hybridAccessToken: this.hybridAccessToken });

                return Object.assign({}, doc, {
                    page: doc.page.map((page) => {
                        // page标记，为了避免从指定位置页跳过来同一份合同存在的图片缓存问题
                        const imagePreviewUrl = this.$hybrid.getContractImg(page.imagePreviewUrl, '&page=signing');
                        const highQualityPreviewUrl = this.$hybrid.getContractImg(page.highQualityPreviewUrl, '&page=signing');
                        // if (pageIndex === 0) {
                        //     page.y = 0;
                        // } else {
                        //     let prePage = doc.page[pageIndex - 1];
                        //     page.y = prePage.y + prePage.height + this.SPACE_HEIGHT;
                        // }
                        // page.marks && page.marks.map(mark => {
                        //     if (mark.status === 'INACTIVE' && mark.type === "SEAL") {
                        //         this.isApplySeal = true;
                        //     }
                        // })
                        return Object.assign({}, page, {
                            imagePreviewUrl,
                            highQualityPreviewUrl,
                            marks: page.marks || [],
                            normalMarks: this.filterNormalMarks(page.marks || []),
                            writeLabels: this.filterWriteAbleLabels(page.marks || []),
                        });
                    }),
                });
            }));
        },
        handleClickRenewal() {
            this.renewalDialogVisible = false;
            this.isLoading = 1;
            const {
                personNeedRenewal,
                entNeedRenewal,
            } = this.renewalType;
            const ifBoth = this.receiver.signType === 'SEAL_AND_SIGNATURE';
            if (entNeedRenewal || ifBoth) {
                // 盖章并签字场景需要处理个人与企业主体
                return this.$http.post(`users/auth/cacert/delay?ifBoth=${ifBoth}`)
                    .then(() => {
                        this.needRenewal = false;
                        this.toNext();
                    })
                    .catch(async(err) =>  {
                        this.needRenewal = true;

                        const res = await this.$http.get(`ents/auth/delay-ca-failed/display-info`);
                        const { groupName,  hasPrivilege } = res.data;
                        this.caFailedInfo = res.data;
                        if (groupName) { // 因“集团群主企业无法直接实名变更”的弹窗文案示例
                            const h = this.$createElement;
                            const content = h('p', null, [
                                h('p', null, this.$t('certificationRenewalDialog.tip12')),
                                h('p', { style: 'margin-top: 25px' }, this.$t('certificationRenewalDialog.tip13', { groupName: groupName })),
                            ]);
                            this.$confirm(content, this.$t('signIdentityGuide.title'), {
                                confirmButtonText: this.$t('certificationRenewalDialog.rejectConfirm'),
                                showCancelButton: false,
                                customClass: 'doc-translation-confirm',
                            });
                            return;
                        }
                        const errCode = err.response.data.code;
                        if (errCode === '090075') { // 企业二要素不一致
                            if (hasPrivilege) { // 有账户管理权限
                                const h = this.$createElement;
                                const content = h('p', null, [
                                    h('p', null, this.$t('certificationRenewalDialog.tip16')),
                                ]);
                                this.$confirm(content, this.$t('signIdentityGuide.title'), {
                                    confirmButtonText: this.$t('certificationRenewalDialog.tip14'),
                                    cancelButtonText: this.$t('certificationRenewalDialog.tip15'),
                                    customClass: 'doc-translation-confirm',
                                }).then(() => {
                                    location.href = '/auth-p/enterprise/stage';
                                });
                                return;
                            } else {
                                this.caFailDialogVisible = true;
                                return;
                            }
                        } else if (errCode === '090076') { // 企业经营状态异常
                            const h = this.$createElement;
                            const content = h('p', null, [
                                h('p', null, this.$t('certificationRenewalDialog.tip17')),
                            ]);
                            this.$confirm(content, this.$t('signIdentityGuide.title'), {
                                confirmButtonText: this.$t('certificationRenewalDialog.rejectConfirm'),
                                showCancelButton: false,
                                customClass: 'doc-translation-confirm',
                            });
                            return;
                        }
                    }).finally(() => {
                        this.isLoading = 0;
                    });
            }
            if (personNeedRenewal) {
                return this.handlePersonCARenewal();
            }
        },
        handlePersonCARenewal() {
            this.isLoading = 0;
            // 非大陆 ||  企业签字需要获取个人
            const isForPerson = this.receiver.signType === 'ENTERPRISE_SIGNATURE';
            if (this.certificationInfos[0] && this.certificationInfos[0].isNonMainland || isForPerson) {
                return this.$http.post(`users/auth/cacert/delay?isForPerson=${isForPerson}`)
                    .then(() => {
                        this.needRenewal = false;
                        this.toNext();
                    })
                    .catch(() => {
                        this.needRenewal = true;
                    });
            }
            // 大陆用户，补做意愿性
            location.href = `${location.origin}${authPath('ca-renewal', {
                fromPath: encodeURIComponent(this.$route.fullPath),
            })}`;
        },
        reAuthHandle() {
            return this.$http.post('/users/auth/restart')
                .then(() => this.goToAuth(true));
        },
        // 签约校验弹窗关闭
        handleValidateClose(done) {
            this.$refs.SignValidation.curVerifyType === 'QrCodeVerify' && (clearInterval(this.$refs.SignValidation.$children[0].interval));
            done();
        },
        /** 签署时添加骑缝章功能，该功能不新增用户使用 start **/
        // 点击添加或者删除骑缝章按钮
        async handleRridingSealClick() {
            if (!this.checkSignQualified()) {
                return false;
            }
            if (this.isShowRidingSeal) {
                // 删除骑缝章
                return this.deleteRridingSeal();
            }
            // 当多文档合同中，同时有超过146页和不超过146但大于1页的文件时，仅给不超过146页但大于1页的文件加盖骑缝章
            if (this.docList.every(doc => doc.page.length === 1)) {
                return this.$MessageToast.info(this.$t('sign.ridingSealMinLimit'));
            }
            if (this.docList.every(doc => doc.page.length > 146)) {
                return this.$MessageToast.info(this.$t('sign.ridingSealMaxLimit'));
            }
            const isCanAdd = this.docList.some(doc => doc.page.length > 1 && doc.page.length <= 146);
            if (!isCanAdd) {
                return this.$MessageToast.info(this.$t('sign.ridingSealMinOrMaxLimit'));
            }
            // 添加骑缝章
            const { sealId, fileId } = await this.addRridingSeal();

            this.ridingSealData = [];

            // 计算scale值，未预览文档的宽高只有初始值，骑缝章的top属性会计算错误。
            const scale = this.docList[0].page[0].width / this.docList[0].page[0].width_init;
            this.docList.map(doc => {
                const l = doc.page.length;
                if (l > 1 && l <= 146) {
                    const { width } = doc.page[0];
                    let { height, width_init, height_init } = doc.page[0];
                    // CFD-22840:doc未渲染时，宽高仍存在原来的key中，通过当前scale计算缩放后的宽高
                    if (!width_init) {
                        width_init = width;
                        height_init = height;
                        height = scale * height_init;
                    }
                    this.ridingSealData.push({
                        type: 'RIDING_SEAL',
                        // contractId: doc.contractId,
                        documentId: doc.documentId,
                        top: (height - defaultSealWidth) / (2 * height),
                        width: defaultSealWidth / width_init,
                        height: defaultSealWidth / height_init,
                        sealId,
                        fileId,
                        contractId: this.contractId,
                        x: 1,
                        y: 1 - (height + defaultSealWidth) / (2 * height),
                        receiverId: this.receiver.receiverId,
                    });
                }
            });
            const { data } = await this.updateRridingSealToServer();
            this.isShowRidingSeal = true;
            // 创建的时候更新labelId
            this.ridingSealData = data;
            // 自动滚动到第一个有多页的文档位置
            const documentsContentDom = document.querySelector('.documents-content');
            let scrollHeight = 0;
            this.docList.some(doc => {
                if (doc.page.length === 1 || doc.page.length > 146) {
                    scrollHeight += doc.page.height + this.SPACE_HEIGHT;
                }
                return doc.page.length > 1 && doc.page.length <= 146;
            });
            scrollToYSmooth(documentsContentDom, scrollHeight * this.$refs.doc.zoomCoefficient);
        },
        // 添加/切换骑缝章。因为需要在乐高成中配置，所以不需要考虑未实名企业
        addRridingSeal(action = 'click') {
            // this.mySeals + this.otherSeals
            return this.getSeals()
                .then(() => {
                    const { mySeals, otherSeals } = this;
                    // 无印章
                    if (!this.sealsLength) {
                        if (this.isSealManager) {
                            this.handleSealManagerNoSeal(2);
                        } else {
                            this.$alert(this.$t('sign.noSealAvailable'), this.$t('sign.noSeal'), {
                                confirmButtonText: this.$t('sign.noSealForRiding'),
                            });
                        }
                        return Promise.reject();
                    }
                    // 如果只有可以申请的印章
                    if (!mySeals.length && otherSeals.length) {
                        this.$alert(this.$t('sign.noSealForRiding'), {
                            confirmButtonText: this.$t('sign.submit'),
                        });
                        return Promise.reject();
                    }
                    // 只有一个可以使用的印章，并且是切换，没有可切换印章->提示
                    if (mySeals.length === 1 && action === 'switch') {
                        if (this.isSealManager) {
                            // 有印章权限
                            this.$confirm(this.$t('sign.noSwitchSealNeedAppendBySelf'), '', {
                                confirmButtonText: this.$t('sign.gotoAppendSeal'),
                                cancelButtonText: this.$t('sign.cancel'),
                            }).then(() => {
                                this.$router.push('/console/enterprise/seal/manage');
                            });
                        } else {
                            // 没有印章权限
                            this.$alert(this.$t('sign.noSwitchSealNeedAppend'), {
                                confirmButtonText: this.$t('sign.knew'),
                            });
                        }
                        return Promise.reject();
                    }
                    // 存在多个可以使用的印章
                    if (action === 'switch') {
                        this.sealListVisible = true;
                        this.sealListVisibleFromAction = 2;
                        this.selectedLabelId = this.ridingSealData[0].labelId;
                    } else {
                        return Promise.resolve(mySeals.filter(item => item.isDefault)[0]);
                    }
                });
        },
        // 添加/切换骑缝章。因为需要在乐高成中配置，所以不需要考虑未实名企业
        switchRridingSeal({ labelId, signatureId }) {
            this.selectedLabelId = labelId;
            this.curRidingSealId = signatureId;

            this.getSeals()
                .then(() => {
                    const { mySeals } = this;
                    if (!this.sealsLength) {
                        if (this.isSealManager) {
                            this.handleSealManagerNoSeal(3);
                        } else {
                            this.$alert(this.$t('sign.noSealAvailable'), this.$t('sign.noSeal'), {
                                confirmButtonText: this.$t('sign.submit'),
                            });
                        }
                    } else if (!mySeals.length) {
                        this.$alert(this.$t('sign.noSealForRiding'), {
                            confirmButtonText: this.$t('sign.submit'),
                        });
                    } else {
                        this.sealListVisible = true;
                        this.sealListVisibleFromAction = 3;
                    }
                });
        },
        // 骑缝章数据更新
        async updateRridingSealData(data) {
            for (let i = 0; i < this.ridingSealData.length; i++) {
                const temp = { ...this.ridingSealData[i] };
                Object.keys(data).map(key => {
                    temp[key] = data[key];
                });
                this.$set(this.ridingSealData, i, temp);
            }
        },
        // 骑缝章数据提交到服务端
        updateRridingSealToServer() {
            return this.$http.post(`/contract-api/contracts/${this.contractId}/labels/ridingSeal-create-and-modify/`, this.ridingSealData);
        },
        // 删除骑缝章
        async deleteRridingSeal() {
            const { contractId } = this;
            this.isLoading = 1;
            try {
                await this.$http.delete(`/contract-api/contracts/${contractId}/ridingSealLabels`);
                this.isLoading = 0;
                this.isShowRidingSeal = false;
                this.$set(this.ridingSealData, 'labelId', '');
            } catch (e) {
                this.isLoading = 0;
            }
        },
        /** 签署时添加骑缝章功能，该功能不新增用户使用 end **/
        // 获取签署前审批流
        getDefines() {
            this.$http.get(`${signPath}/contracts/${this.contractId}/sign-defines`)
                .then(res => {
                    this.definesData = res.data;
                    // 触发审批流程
                    if (this.definesData.length) {
                        this.dialogApproval = true;
                    }
                });
        },
        onApplyApproval(commitFlow, defInfoId) {
            // 避免重复提交
            this.$loading();
            this.$http.post(`${signPath}/contracts/${this.contractId}/sign-defines/start`, {
                defInfoId: defInfoId,
                flowStepList: commitFlow,
            })
                .then(() => {
                    this.$MessageToast.success(this.$t('sign.approvalFlowSuccessfulSet'));
                    this.dialogApproval = false;
                    this.signDecision = 'WAIT_SIGN_APPROVAL_COMPLETE';
                }).finally(() => {
                    this.$loading().close();
                });
        },
        // 获取“确认签署”按钮处的文案：只有点击后真正会弹出签署校验框/刷脸才显示「确认签署」，否则显示「签署」
        _setConfrimSignButtonText() {
            // 签署授权书
            if (this.isFromSignAuthorization) {
                return this.rText = this.$t('sign.mandate');
            }
            try {
                this.isAllSignaturesInactive = this.signatureLabels.every(item => item.status === 'INACTIVE');
                if (!this.signerDragAble && this.isAllSignaturesInactive) {
                    // eslint-disable-next-line no-undef
                    return this.rText = text;
                }

                const isAllTrue = [];
                isAllTrue.push(this.signDecision === 'NORMAL');
                isAllTrue.push(this.checkoutAllWriteDone().necessDone);
                // 签署人自定义签署位置，但是没有盖章或签名时提示
                isAllTrue.push(!this.signerDragAble || this.signatureLabels.length);
                // 新版本签署人自定义签署位置，但是没有盖章或签名时提示
                isAllTrue.push(!this.signPlaceBySigner || this.signatureLabels.length);
                // 请先点击“盖章处”或“签字处”完成签署
                isAllTrue.push(this.signerDragAble || this.checkoutSignDone());
                // 新版本签署人自定义签署位置，但是没有盖章或签名时提示
                isAllTrue.push(this.signPlaceBySigner || this.checkoutSignDone());
                // 附件未完成
                if (this.attachmentList.length) {
                    if (this.attachmentList.some(item =>
                        (!(item.fileList || []).length) && item.necessary)) {
                        isAllTrue.push(false);
                    }
                }
                if (isAllTrue.every(isTrue => isTrue)) {
                    return this.rText = this.$t('signPC.commonSign');
                }
                return this.rText = this.$t('signTip.signed');
            } catch (e) {
                return this.rText = this.$t('signTip.signed');
            }
        },
        // 验证码校验跳转到刷脸
        changeValidationToFace() {
            this.signVaDialogVisible = false;
            this.changeFaceToValidationShow = true;
            this.qrSignDialogVisible = true;
        },
        changeFaceToValidation() {
            this.qrSignDialogVisible = false;
            this.signVaDialogVisible = true;
        },
        // 获取混合云1的云端字段
        fetchAlphaHybridContractBusiLabelsInfo() {
            const url = `/contract-api/contracts/${this.contractId}/labels/${this.receiver.receiverId}?permission=${this.permission}`;
            return this.$hybrid
                .makeHeader({
                    url,
                    hybridTarget: `${this.$store.state.commonHeaderInfo.hybridServer}`,
                    method: 'GET',
                    contractId: this.contractId,
                })
                .then((res) => {
                    return this.$http
                        .get(
                            `${this.$store.state.commonHeaderInfo.hybridServer}${url}`,
                            { headers: res.data },
                        )
                        .then((res) => {
                            // 获取混合云的标签
                            return Promise.resolve(res.data);
                        });
                });
        },
        // 获取混合云1 和 公有云的云端字段并确认是否覆盖
        fetchAndCoverAlphaHybridContractBusiLabelsInfo() {
            // eslint-disable-next-line no-async-promise-executor
            return new Promise(async(resolve, reject) => {
                if (this.isHybridCloudContract && this.$hybrid.isAlpha()) {
                    const diffLabels = [];
                    const hybridLabels = await this.fetchAlphaHybridContractBusiLabelsInfo();
                    // 获取混合云的标签
                    this.docList.forEach((doc) => {
                        doc.page.forEach((page) => {
                            // 只过滤混1情况下上传到本地服务器的字段类型
                            page.marks.filter(mark => ['TEXT', 'TEXT_NUMERIC', 'BIZ_DATE'].includes(mark.type)).forEach((mark) => {
                                const label = hybridLabels.find(
                                    (label) => label.name === mark.name,
                                );
                                // 如果混合云上有的话，查看值是否相同
                                if (label) {
                                    if (mark.value !== label.value) {
                                        diffLabels.push({
                                            name: mark.name,
                                            localValue: mark.value,
                                            hybridValue: label.value,
                                            cover() {
                                                mark.value = '';
                                            },
                                        });
                                    }
                                } else {
                                    // 如果混合云上没有的话，且页面有值, 则按空值处理
                                    if (mark.value !== '') {
                                        diffLabels.push({
                                            name: mark.name,
                                            localValue: mark.value,
                                            hybridValue: '',
                                            cover() {
                                                mark.value = '';
                                            },
                                        });
                                    }
                                }
                            });
                        });
                    });
                    if (diffLabels.length > 0) {
                        const h = this.$createElement;
                        const message = h('div', null, [
                            h('p', null, '页面中以下合同内容字段保存失败：'),
                            h('br'),
                            diffLabels.map(label => h('p', null, `【${label.name}】`))]);
                        await this.$confirm(message, '提示', {
                            confirmButtonText: '重新填写',
                            cancelButtonText: '取消',
                            type: 'warning',
                            closeOnClickModal: false,
                            dangerouslyUseHTMLString: true,
                            customClass: 'sign-later-message-box',
                        });
                        // confirm之后，结束await, 继续下面的逻辑
                        // 将云端值覆盖本地值
                        diffLabels.forEach(({ cover }) => cover());
                        reject();
                    }
                }
                resolve();
            });
        },
        getPaperSignInfo() {
            this.$http.get(`/contract-api/papersign/info/${this.contractId}`).then(res => {
                this.paperSignInfo = res.data;
            });
        },
        handleBizPoint() {
            return this.$http.addBizPoint('SIGN_CONTRACT', this.contractId);
        },
        // 签署完后跳转处理
        goAfterSign() {
            // 当前是档案+采集流程，签署成功跳转采集成功页面
            if (sessionStorage.getItem('entCustomerInfo')) {
                const entCustomerInfo = JSON.parse(sessionStorage.getItem('entCustomerInfo'));
                return location.href = `${location.origin}/damp/pages/success/success?archiveId=${entCustomerInfo.archiveId}`;
            }
            if (this.isApplySeal) {
                this.$router.push(`/doc/list`);
            }
            /* 如果客户定义了跳转地址，则首先跳转 */
            if (this.returnUrl) {
                goReturnUrl(this.returnUrl);
                return;
            }
            if (this.ssoSigning.signing_agree && this.ssoSigning.signing_agree.url) {
                this.$router.push(`${this.ssoSigning.signing_agree.url}?status=6&contractId=${this.contractId}&type=${this.signType}`);
            } else {
                if (this.channel === 'notice') {
                    // 链接进来
                    this.$router.push(`/sign/sign-tip?status=6&receiverId=${this.receiver.receiverId}&contractId=${this.contractId}&type=${this.signType}${this.isAllSignaturesInactive ? '&applySeal=true' : ''}`);
                } else {
                    // 登录进来
                    this.$router.push(`/doc/list`);
                }
            }
        },
        // 单点或者短链接可以通过接口获取returnurl
        async getReturnUrl()  {
            this.hasGetReturn = true;
            // api 发送的设置签署方returnul优先级最高
            try {
                const { data: { result: returnUrl } }  = await this.$http.get(`/contract-api/contracts/${this.contractId}/${this.receiver.receiverId}/return-url`, { noToast: 1 });
                if (returnUrl) {
                    this.returnUrl = returnUrl;
                }
            } catch (error) {
                console.log(error);
            }
            if (this.returnUrl) {
                return;
            }
            if (this.$store.state.commonHeaderInfo.extendFields.loginType === 'SSO' || this.urlToken) {
                try {
                    const { data: { value: returnUrl } }  = await this.$http.get(`/users/api-helper/contract-return-url?contractId=${this.contractId}`);
                    this.returnUrl = returnUrl;
                } catch (error) {
                    console.log(error);
                }
            }
        },
        // 审批时，合同重要字段的高亮处理
        approvalHighLight() {
            // 当前浏览器展示过不再展示，除非清除缓存
            if (this.$localStorage.get('isHighLightAndAnnotateTipShow')) {
                return;
            }
            if (this.isShowHighLight && !this.getIsForeignVersion) {
                this.sensorsEventName && this.$sensors.track({
                    eventName: `Ent_${this.sensorsEventName}Window_PopUp`,
                    eventProperty: {
                        page_name: `${this.sensorsPageName}`,
                        window_name: '合同高亮提醒',
                        ...this.sensorsTrackContractInfo,
                    },
                });
                this.showApprovalHighLightDialog = true;
            }
        },

        // 获取用户是否配置了加密签署或者二要素认证器
        async getEncryptionSignConfig() {
            if (!this.getIsForeignVersion) {
                return;
            }
            const { data: { encryptionSignConfig, twoFactorAuthentication }  } = await this.$http.get(`/contract-api/contracts/${this.contractId}/${this.receiver.receiverId}/encryption-sign-config`);
            this.encryptionType = { encryptionSignConfig, twoFactorAuthentication };
        },
        async handleShowEncryptionSign() {
            // 是否配置了二要素认证
            if (this.encryptionType.twoFactorAuthentication) {
                const { data: { value: ifHasSetTwoFactorAuthentication } } = await this.$http.get('/users/authenticator/check-setting');
                this.ifHasBindingTwoFactor = ifHasSetTwoFactorAuthentication;
                if (!ifHasSetTwoFactorAuthentication) {
                    this.showJaSettingTwoFactorDialog = true;
                    return;
                } else {
                    this.showJaEncryptionAndTwoFactorDialog = true;
                }
            }
            // 是否配置了加密签署
            if (this.encryptionType.encryptionSignConfig) {
                this.showJaEncryptionAndTwoFactorDialog = true;
            }
        },
        checkCanUseAiChat() {
            this.$http(`/contract-api/contracts/${this.contractId}/can-use-ai-chat`).then(res => {
                this.showChatting = res.data.ifCanUseAiChat;
            });
        },
        reportSignPageLeaveEvent() {
            if (document.getElementById('signPage')) {
                this.sensorsEventName && this.$sensors.track({
                    eventName: `Ent_${this.sensorsEventName}_PageLeave`,
                    eventProperty: {
                        page_name: `${this.sensorsPageName}`,
                        $event_duration: (new Date().getTime() - this.enterTime) / 1000,
                        ...this.sensorsTrackContractInfo,
                    },
                });
            }
        },
    },
    async beforeRouteEnter(to, from, next) {
        // 如果是移动端。自动跳转
        if (!isPC()) {
            location.href = `${window.location.origin}${mobileRouteObj['sign']}?${encodeSearchParams(to.query)}`;
            return next(false);
        }
        // 单点登录采集 如果客户定义了跳转地址，则首先跳转 */
        const archiveReturnUrl = Vue.$cookie.get('archiveReturnUrl');
        if (archiveReturnUrl) {
            Vue.$cookie.delete('archiveReturnUrl');
            return next(`${to.fullPath}&returnUrl=${encodeURIComponent(archiveReturnUrl)}`);
        }
        // 企业经办人实名回来，切回到企业身份签署
        // const entId = window.sessionStorage.getItem('transitionHref-switch-entId');
        // if (entId) {
        //     await Vue.$http.switchEntId(entId);
        //     window.sessionStorage.removeItem('transitionHref-switch-entId');
        // }
        next();
    },
    beforeMount() {
        this.ssoSigning = this.getSsoConfig.signing || {};
    },
    async created() {
        // 签署过来的签署授权书，记录下原始签署url
        if (this.$route.query.isAuthorization === 'true') {
            const transitionHrefBeforCollect = this.$cookie.get('transitionHrefBeforCollect');
            this.transitionHrefBeforCollect = `${transitionHrefBeforCollect}&archiveCollectContractId=${this.contractId}`;
        }
        await this.getReceiverCallback();
        Bus.$on('write-done', this.onWriteDone);
        Bus.$on('close-handwrite', this.onWriteClose);
        Bus.$on('save-signature', this.onUpdateSignature);
        Bus.$on('update-attachment', this.onUpdateAttachment);
        // if (this.$route.query.channel == 'notice') {
        this.$localStorage.set('transitionHref', this.$route.fullPath);
        // }
        Bus.$on('ask-auth', () => {
            this.signQualifiedDialogVisible = true;
            this.$refs.signQualifiedDialog.askForSign();
        });
        Bus.$on('notice-admin', () => {
            this.signQualifiedDialogVisible = true;
            this.$refs.signQualifiedDialog.sendNoticeToAdmin();
        });
        this.$http.addBizPoint('SIGN_CONTRACT', this.contractId, true);
    },
    mounted() {
        this.enterTime = new Date().getTime();
        window.addEventListener('beforeunload', this.reportSignPageLeaveEvent);
    },
    beforeDestroy() {
        window.removeEventListener('beforeunload', this.reportSignPageLeaveEvent);
    },
};
</script>
<style lang="scss">
    @import './signing.scss';
    .signing{
        .label-container{
            pointer-events: none !important;
            *{
                pointer-events: auto;
                &.confirm-seal-label, &.confirm-seal-label-con{
                    pointer-events: none;
                }
            }
        }
        .hubble-component__entry{
            top: unset;
            bottom: 100px;
        }
        .contract-high-light-tip-btn{
            position: fixed;
            width: 40px;
            height: 40px;
            z-index: 100;
            bottom: 55px;
            left: 15px;
            background-color: #127FD2;
            border-radius: 50%;
            color: #fff;
            font-size: 20px;
            text-align: center;
            line-height: 40px;
            cursor: pointer;
        }
        .create-seal-dialog .el-dialog{
            width: 500px;
            border-radius: 4px;
            .el-input {
                margin-top: 10px;
            }
            .el-dialog__body{
                padding: 30px 20px;
                text-align: center;
            }
        }
    }
    .news-enty {
        position: absolute;
        right: 10px;
        bottom: 100px;
        z-index: 10001;
    }
    .signing .qrcode-dialog {
        width: 400px;
        border-radius: 14px;
        .el-dialog__header {
            position: relative;
            border-bottom: none;
        }
    }
</style>
