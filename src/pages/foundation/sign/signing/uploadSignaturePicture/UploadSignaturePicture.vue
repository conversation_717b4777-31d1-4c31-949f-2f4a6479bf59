<template>
    <div class="upload-panel">
        <el-upload
            class="upload-wrapper"
            action=""
            :show-file-list="false"
            :before-upload="beforeUpload"
            accept="image/png,image/jpg,image/jpeg"
        >
            <div class="uploadCon" v-if="uploadStatus === 0">
                <i class="el-icon-ssq--bs-jia"></i>
                <p>{{ $t('handwrite.uploadPic') }}</p>
            </div>
            <div class="imgCon" v-else>
                <img :src="uploadImgUrl">
            </div>
            <p class="upload-tips">{{ $t('handwrite.uploadTip1') }} <br> {{ $t('handwrite.uploadTip2') }}</p>
        </el-upload>
        <div class="upload-footer">
            <el-button type="primary" @click="postSignaturePicAjax">{{ $t('handwrite.use') }}</el-button>
            <div class="upload-setting">
                <div>
                    <el-checkbox :label="true" v-model="settingDefault">{{ $t('handwrite.settingDefault') }}</el-checkbox>
                    <el-tooltip popper-class="upload-tooltip" effect="dark" :content="$t('handwrite.moreTip')" placement="top">
                        <i class="el-icon-ssq-icon-bs-bangzhuzhongxin"></i>
                    </el-tooltip>
                </div>
                <div v-if="showReplaceAllBtn"><el-checkbox v-model="replaceAllSealAndSignature">{{ $t('handwrite.replaceAllSignature') }}</el-checkbox></div>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    props: {
        'parentGift': {
            type: Object,
            default: function() {
                return {
                    contractId: '',
                    labelId: '',
                    labelValue: '',
                    receiverId: '',
                    handWritingRecognition: false, // 是否需要笔迹识别
                    name: '',
                };
            },
        },
        operateAction: {
            type: String,
            default: 'click',
        },
    },
    inject: ['updateSignatureData'],
    data() {
        return {
            signType: this.$route.query.type || 'sign', // send sign approval view
            uploadStatus: 0, // 0 未上传 1 上传
            uploadImgUrl: '',
            settingDefault: false,
            signaturePicFile: {
                file: {},
                sigId: '',
            },
            replaceAllSealAndSignature: false,
        };
    },
    computed: {
        showReplaceAllBtn() {
            return !!this.parentGift.labelValue;
        },
        sensorsEventName() {
            return this.signType === 'sign' ? 'ContractSign' : (this.signType === 'approval' ? 'ContractApproval' : '');
        },
        sensorsPageName() {
            return this.signType === 'sign' ? '合同签署页' : (this.signType === 'approval' ? '合同审批页' : '');
        },
    },
    methods: {
        beforeUpload(file) {
            this.signaturePicFile.file = file;
            const isLt5M = file.size / 1024 / 1024 < 5;
            if (!isLt5M) {
                this.$MessageToast({
                    message: this.$t('signeature.uploadLimit'),
                    iconClass: 'el-icon-ssq-yiguoqi',
                });
                return isLt5M;
            }
            if (typeof FileReader !== 'undefined') {
                var reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = () => {
                    this.uploadImgUrl = reader.result;
                    this.uploadStatus = 1;
                };
            }
            return false;
        },
        // 提交手写签名ajax
        postSignaturePicAjax() {
            this.sensorsEventName && this.$sensors.track({
                eventName: `Ent_${this.sensorsEventName}Window_BtnClick`,
                eventProperty: {
                    page_name: `${this.sensorsPageName}`,
                    window_name: '手写签名',
                    icon_name: '使用',
                    contract_id: this.parentGift.contractId,
                },
            });
            if (JSON.stringify(this.signaturePicFile.file) === '{}') {
                this.$MessageToast.info(this.$t('personalAuth.submitPicError'));
                return;
            }
            const option  = {
                file: this.signaturePicFile.file,
                setDefaultSig: this.settingDefault,
                sigId: '',
            };
            // 保存上传签名
            const formData = new FormData();
            Object.keys(option).forEach(key => {
                formData.append(key, option[key]);
            });
            let url = '';
            if (this.operateAction === 'switch') {
                url = `${signPath}/contracts/${this.parentGift.contractId}/labels/${this.parentGift.labelId}/signature`;
            } else if (this.operateAction === 'click') {
                url = `${signPath}/contracts/${this.parentGift.contractId}/labels/signature`;
            }
            // 在用户中心新增个个人签名
            this.$http.post(`/users/signatures`, formData)
                .then((res) => {
                    const { value: signatureId } = res.data;
                    // 更新签名label
                    this.$http.post(url, {
                        signatureId, // 签名样式Id
                        employeeId: '',
                        userId: '',
                        labelId: this.parentGift.labelId,
                    })
                        .then(res => {
                            this.$MessageToast({
                                message: this.$t('handwrite.picSubmitTip'),
                                type: 'success',
                            });
                            if (this.operateAction === 'switch') {
                                this.$emit('updateSignature', { label: res.data, replaceAllSealAndSignature: this.replaceAllSealAndSignature });
                            } else if (this.operateAction === 'click') {
                                this.updateSignatureData(res.data, 'signature');
                            }
                            this.$emit('close');
                        });
                });
        },
    },
};
</script>
<style lang="scss">
.upload-panel {
    width: 100%;
    height: 100%;
    position: relative;
    .el-upload {
        width: 100%;
    }
    .upload-footer {
        position: absolute;
        bottom: 30px;
        width: 100%;
        .el-button {
            width: 60px;
            height: 30px;
            margin-left: 30px;
            line-height: 8px;
            [dir=rtl] & {
                width: 80px;
                margin-left: 0;
                margin-right: 30px;
            }
        }
        .upload-setting {
            position: absolute;
            top: 5px;
            right: 30px;
            [dir=rtl] & {
                right: auto;
                left: 30px;
            }
            span, .el-icon-ssq-icon-bs-bangzhuzhongxin {
                color: #999;
            }
            .el-icon-ssq-icon-bs-bangzhuzhongxin:hover {
                cursor: pointer;
            }
        }
    }
    .upload-wrapper {
        .uploadCon {
            width: 140px;
            margin: 0 auto;
            height: 140px;
            vertical-align: center;
            border: 1px dashed #ccc;
            display: flex;
            flex-direction: column;
            justify-content: center;
            color: #999;
            margin-top: 50px;
            p {
                margin-top: 10px;
            }
        }
        .el-upload {
            width: 100%;
            text-align: center;
            .imgCon {
                width: 262px;
                height: 217px;
                line-height: 217px;
                margin: 0 auto;
                img {
                    max-width: 100%;
                    max-height: 100%;
                    vertical-align: middle;
                }
            }
        }
        .upload-tips{
            font-size: 12px;
            color: #FF5500;
            margin-top: 5px;
        }
    }
}
.upload-tooltip {
    width: 224px;
}
</style>
