<!-- 此组件用于封装签约页面右侧的填写内容功能，主要包含textarea，el-date-picker和附件内容信息 -->
<template>
    <div class="fill-content-sidebar">
        <div class="fill-content-container">
            <!-- 附件文件列表 -->
            <div v-if="attachments.length" class="fill-content-attach">
                <div v-if="isOld" class="fill-attach-explain">
                    <i class="el-icon-ssq-tishi1"></i>{{
                        $t('sign.oldFormatTip') }}
                </div>
                <ul class="fill-list">
                    <li v-for="(attachment, index) in attachments" :key="index" @click.capture="checkSignQualified">
                        <AttachFile
                            :attachment="attachment"
                            :index="index"
                            :isHybridCloudContract="isHybridCloudContract"
                            :receiverId="receiverId"
                        >
                        </AttachFile>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>

<script>
import AttachFile from './AttachFile.vue';

export default {
    components: {
        AttachFile,
    },
    // eslint-disable-next-line vue/require-prop-types
    props: ['attachments', 'receiverId', 'isHybridCloudContract', 'signDecision', 'signOperationType', 'receiver'],
    data() {
        return {
            contractId: this.$route.query.contractId || '',
        };
    },
    computed: {
        isOld() {
            return this.attachments.some(t => t.fileType === null);
        },
    },
    methods: {
        // 列表中每一项触发click事件，校验用户是否有实名认证，未实名认证的用户需要实名认证,纸质签署不用校验
        checkSignQualified(e) {
            const notAuthEnt = this.signDecision === 'USE_DEFAULT_IDENTITY' && !this.receiver.hasAuthenticated && !this.receiver.requireIdentityAssurance;
            if (!notAuthEnt && this.signDecision !== 'NORMAL' && this.signOperationType !== 'ONLY_PAPER_SIGN') {
                this.$emit('check-sign-qualified');
                e.preventDefault();
            }
        },
    },
};
</script>

<style lang="scss">
    .fill-content-sidebar {
        overflow: auto;
        .el-icon-ssq-tishi1{
            margin-right: 5px;
            color: #f86b26;
        }
        .fill-content-container {
            .fill-title {
                color: #333;
                font-size: 13px;
                font-weight: 700;
                height: 39px;
                line-height: 39px;
                background-color: #f8f8f8;
                border-top: 1px solid #ddd;
                padding-left: 20px;
                vertical-align: middle;
            }

            .fill-content-attach {
                line-height: 1.5;
                .fill-attach-explain {
                    margin: 17px 20px 10px;
                    position: relative;
                    color: #888;
                    font-size: 12px;
                    background-color: #f9fbfb;
                    padding: 6px 6px 5px 18px;
                    .el-icon-ssq-tishi1 {
                        position: absolute;
                        top: 9px;
                        left: 3px;
                        width: 13px;
                        height: 13px;
                        color: #ff6c00;
                    }
                }
            }

            .fill-content-desc {
                color: #999;
                font-size: 12px;
                position: relative;
                line-height: 18px;
                .label-order {
                    color: #127fd2;
                }

                .fill-content-order {
                    position: absolute;
                    top: 0;
                    left: 0;
                    display: inline-block;
                    width: 16px;
                    height: 16px;
                    line-height: 16px;
                    text-align: center;
                    background-color: #1280d2;
                    color: #fff;
                    border-radius: 50%;
                }
            }

            .sign-field {
                .doc {
                    border-bottom: 1px solid $border-color;
                }
                .doc-title {
                    position: relative;
                    height: 18px;
                    line-height: 18px;
                    color: #333;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    cursor: pointer;
                    padding: 6px 40px 6px 20px;
                    transition: transform .3s;
                }
                .mark-position {
                    padding-left: 20px;
                    line-height: 20px;
                    color: #127fd2;
                }
            }
            .fill-list {
                padding: 10px 20px 25px;
                .el-upload-list {
                    padding: 4px 0;
                }
                li {
                    margin-top: 6px;
                    &:first-child {
                        margin-top: 0;
                    }
                }
            }
        }
    }
</style>
