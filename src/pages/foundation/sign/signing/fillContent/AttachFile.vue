<!-- 附件的上传 -->
<template>
    <div class="fill-content-attach-item">
        <div class="fill-attach-desc">
            <span class="fill-attach-desc__necessary" v-if="attachment.necessary">*</span>
            <span>{{ $t('addReceiver.attach') }}{{ index + 1 }}：</span>
            <span>{{ attachment.name || $t('sign.senderUnFill') }}</span>
            <p class="comment" v-if="attachment.comment">{{ $t('sign.declare') }}：{{ attachment.comment }}</p>
            <p v-if="!isOld">{{ isImg ? $t('sign.fileFormatImage') : $t('sign.fileFormatFile') }}</p>
        </div>
        <el-upload
            ref="upload"
            :action="uploadURL"
            class="upload-item"
            :headers="uploadHeaders"
            :before-upload="beforeUpload"
            :on-remove="handleRemove"
            :on-success="handleSucccess"
            :on-error="handleError"
            :on-preview="handlePreview"
            :show-file-list="true"
            :file-list="fileList"
            :accept="accept"
            :disabled="isUpLoading"
            :class="{'upload-item__disabled': isUpLoading}"
        >
            <div v-if="fileList.length < fileSizeMax" class="upload-button">
                +
            </div>
        </el-upload>
        <PreviewDoc :previewDoc="previewImg" :contractId="contractId" ref="previewDoc"></PreviewDoc>
        <!-- 预览私信图片（相册） -->
        <Gallery
            @close="previewImgInfo.visible = false"
            :visible="previewImgInfo.visible"
            :title="previewImgInfo.fileName"
            :src="previewImgInfo.previewUrl"
        ></Gallery>
        <template v-if="attachment.sampleFile">
            <span class="example-btn" v-if="attachment.sampleFile.canPreview" @click="showExamplePic">{{ $t('addReceiver.showExamle') }}</span>
            <span class="example-btn" v-else @click="downloadExample">{{ $t('addReceiver.downloadExamle') }}</span>
        </template>
    </div>
</template>

<script>
import Bus from 'components/bus/bus.js';
import PreviewDoc from '../../common/preview/Preview.vue';
// import {joinPathnameAndQueryObj} from 'utils/getQueryString.js';
import { download, invokeDownloadHelper } from 'src/common/utils/download.js';
import Gallery from 'components/gallery/Gallery.vue';

const TYPE = {
    img: ['image/jpeg', 'image/png', 'image/jpg'],
    file: ['application/pdf', 'application/zip',
           'application/x-zip-compressed', 'application/rtf', 'application/x-rtf', 'text/rtf', 'text/plain', 'application/xml', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/xml', 'image/jpeg', 'image/png', 'image/jpg'],
};
// H5支持的附件文件类型
const FILE_TYPE = {
    img: TYPE.img,
    file: TYPE.file,
    all: TYPE.img.concat(TYPE.file),
};
// 附件大小最多10MB
const MAX_FILE_SIZE = {
    img: 20,
    file: 10,
    all: 10,
};
// 补充判断条件，当读取不出文件类型时使用扩展名判断
const FILE_NAME = ['doc', 'docx'];
export default {
    components: {
        PreviewDoc,
        Gallery,
    },
    // eslint-disable-next-line vue/require-prop-types
    props: ['attachment', 'index', 'isHybridCloudContract', 'receiverId'],
    data() {
        return {
            contractId: this.$route.query.contractId || '',
            isUpLoading: false,
            fileList: [],
            uploadHeaders: { Authorization: `bearer ${this.$cookie.get('access_token')}` },
            previewImg: {},
            previewImgInfo: {
                visible: false,
                fileName: '',
                previewUrl: '',
            },
        };
    },
    computed: {
        // 兼容老模版 老合同
        isOld() {
            return this.attachment.fileType === null;
        },
        isImg() {
            return this.attachment.fileType === 'MATERIAL_TYPE_PICTURE';
        },
        accept() {
            if (this.isOld) {
                return 'image/*,.pdf,.txt,.zip,.doc,.docx,.xls,.xlsx,.xml';
            }
            return this.isImg ? 'image/*' : 'image/*,.pdf,.txt,.zip,.xml,.doc,.docx,.xls,.xlsx';
        },
        fileSizeMax() {
            return this.isImg ? 10 : 1;
        },
        // 混合云host
        publicURL() {
            const { customerCollectionId, extendId } = this.attachment;
            const id = customerCollectionId || extendId;
            return `/octopus-api/box/customer/collection/${this.contractId}/receivers/collection?collectionName=${encodeURIComponent(this.attachment.name)}&customerCollectionId=${id}&receiverId=${this.receiverId}`;
        },
        uploadURL() {
            return this.publicURL;
        },
    },
    methods: {
        async beforeUpload(file) {
            const key = this.isOld ? 'all' : (this.isImg ? 'img' : 'file');
            const isAcceptType = FILE_TYPE[key].some(type => type === file.type);
            // 文件大小最多10MB
            // const MaxFileSize = 1024 * 1024 * 10;
            const isNameAcc = FILE_NAME.some((item) => {
                const tmpList = file.name.split('.');
                return tmpList[tmpList.length - 1].toLowerCase() === item;
            });
            const isAcceptSize = (file.size / (1024 * 1024)) < MAX_FILE_SIZE[key];
            if (!isAcceptSize) {
                this.$MessageToast.error(this.$t('sign.fileLessThan', {
                    num: MAX_FILE_SIZE[key],
                }));
                return Promise.reject(new Error());
            }
            if (!isAcceptType && !isNameAcc) {
                this.$MessageToast.error(this.$t('sign.fileNeedUploadImg'));
                return Promise.reject(new Error());
            }
            this.isUpLoading = true;
        },
        handleRemove(file) {
            this.$loading();
            // 删除附件统一调公有云接口
            this.$http.delete(`${this.publicURL}&fileId=${file.fileId}`)
                .then(() => {
                    this.$loading().close();
                    const index = this.fileList.findIndex(e => e.fileId === file.fileId);
                    this.fileList.splice(index, 1);
                    this.isUpLoading = false;
                    Bus.$emit('update-attachment', {
                        ...this.attachment,
                        fileList: this.fileList,
                    }, this.index);
                    this.$forceUpdate();
                })
                .catch(() => {
                    this.$loading().close();
                });
        },
        handleSucccess(res) {
            this.isUpLoading = false;
            this.fileList.push({
                ...res,
                previewUrl: res.previewUrlPrefix,
                name: res.fileName,
            });
            Bus.$emit('update-attachment', {
                ...this.attachment,
                fileList: this.fileList,
                customerCollectionId: res.customerCollectionId,
            }, this.index);
            this.$forceUpdate();
        },
        handleError(err) {
            console.log(err);
            this.isUpLoading = false;
            const errorMsg = this.$t('sign.serverError');
            try {
                if (err.status === 409) {
                    // err为字符串
                    const responseData = JSON.parse(err.message.replace(/^\d+?\s/, ''));
                    this.$MessageToast.error(responseData.message || errorMsg);
                } else {
                    let message = err.message || '';
                    const matchedAry = message && message.match(/{(.+?)}/g);

                    message = message ? JSON.parse(matchedAry[0]) : {
                        message: errorMsg,
                    };
                    this.$MessageToast.error(message);
                }
            } catch (e) {
                this.$MessageToast.error(errorMsg);
            }
        },
        async handlePreview(file) {
            this.previewImg = {
                ...file,
                previewUrlPrefix: file.previewUrl,
            };
            const passRes = await this.$hybrid.offlineTip();
            if (!passRes) {
                return;
            }
            // 文件正在上传中，不支持预览
            if (!this.isUpLoading) {
                if (this.isImg) {
                    this.$refs.previewDoc.open();
                } else {
                    const downloadUrl = `${file.downloadUrl}&access_token=${this.$cookie.get('access_token')}`;
                    download(downloadUrl);
                }
            }
        },
        showExamplePic() {
            this.previewImgInfo.visible = true;
            const { fileName, previewUrls } = this.attachment.sampleFile;
            this.previewImgInfo.fileName = fileName;
            this.previewImgInfo.previewUrl = previewUrls;
        },
        downloadExample() {
            invokeDownloadHelper(`/contract-api/contracts/detail/attachment/${this.contractId}/${this.receiverId}/download/${this.attachment.sampleFile.fileId}`);
        },
    },
    created() {
        this.fileList = this.attachment.fileList.map(file => ({
            ...file,
            name: file.fileName,
            fileId: file.fileId,
            previewUrl: file.previewUrl,
        }));
    },
};
</script>

<style lang="scss">
    .fill-content-attach-item {
        .example-btn{
            color:#1280d2;
            cursor: pointer;
        }
        .fill-attach-desc {
            position: relative;
            color: #999;
            span {
                color: #333;
            }
            .comment {
                color: #999;
            }
            &__necessary {
                color: #D0021B !important;
                font-size: 16px;
            }
        }

        .fill-attach-icon {
            position: absolute;
            top: 0;
            left: 0;
            color: #999;
        }
        .upload-item {
            .el-upload {
                display: block;
            }
            &.upload-item__disabled .upload-button:hover {
                border: 1px dashed #ccc;
                color: #999;
            }
        }
        .upload-button {
            width: 168px;
            height: 30px;
            border: 1px dashed #ccc;
            line-height: 28px;
            margin-top: 4px;
            font-size: 22px;
            color: #999;

            &:hover {
                border: 1px dashed #1280d2;
                color: #1280d2;
            }
        }
        .el-upload-list__item-name {
            font-size: 12px;
            color: #000;
            padding-left: 0;
            .el-icon-document {
                margin-right: 3px;
            }
        }
        .el-upload-list__item-status-label {
            top: -3px;
        }
        .el-upload-list__item {
            &:hover {
                background-color: #f6f6f6;
            }
        }

        .fill-file-list {
            padding: 0;
            list-style: none;

            .fill-file-list-item {
                transition: all .5s cubic-bezier(.55, 0, .1, 1);
                font-size: 14px;
                color: #48576a;
                line-height: 1.5;
                margin-top: 5px;
                box-sizing: border-box;
                border-radius: 4px;
                width: 100%;
                position: relative;
                cursor: pointer;
            }

            .file-item-name {
                color: #333;
                font-size: 12px;
                display: block;
                padding-left: 15px;
                margin-right: 14px;
                line-height: 1.5;
                &:hover {
                    background-color: #f8f8f8;
                }
            }

            .fill-attach-delete {
                position: absolute;
                right: 0;
                top: -2px;
                cursor: pointer;
                color: #333;
                transform: scale(0.8);
            }

            .fill-attach-progress {
                margin-right: 12px;
            }

            .fill-content-neccesary {
                color: #ff5a00;
            }
        }
    }
</style>
