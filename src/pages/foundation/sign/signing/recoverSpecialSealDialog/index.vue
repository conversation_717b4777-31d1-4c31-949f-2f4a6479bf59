<template>
    <el-dialog
        class="recover-special-seal"
        :visible.sync="visible"
        :close-on-click-modal="false"
        :title="$t('recoverSpecialSeal.title')"
    >
        <div class="body-content">
            <div class="body-content__seal-img">
                <img :src="specialSealInfo.previewUrl" alt="" />
            </div>
            <div class="body-content__description">
                <p>{{ $t('recoverSpecialSeal.description1') }}</p>
                <br />
                <p>{{ $t('recoverSpecialSeal.description2') }}</p>
            </div>
        </div>
        <div slot="footer" class="bottom-part">
            <el-button
                type="primary"
                @click="handleRecoverPost"
            >{{ $t('recoverSpecialSeal.postRecover') }}</el-button>
            <p class="bottom-part__opt-note">{{ $t('recoverSpecialSeal.note') }}</p>
        </div>
    </el-dialog>
</template>

<script>
export default {
    name: 'RecoverSpecialSealDialog',
    props: {
        dialogVisible: {
            default: false,
            type: Boolean,
        },
        specialSealInfo: {
            default: () => ({}),
            type: Object,
        },
        contractTitle: {
            default: '',
            type: String,
        },
        contractAlias: {
            default: '',
            type: String,
        },
        senderEntName: {
            default: '',
            type: String,
        },
    },
    computed: {
        visible: {
            set(v) {
                this.$emit('update:dialogVisible', v);
            },
            get() {
                return this.dialogVisible;
            },
        },
    },
    methods: {
        // 提交恢复专用章申请
        handleRecoverPost() {
            this.$http.post('/ents/resume-special-seal/apply', {
                fileId: this.specialSealInfo.sealFileId,
                sendEntName: this.senderEntName,
                contractTitle: this.contractTitle,
                contractAlias: this.contractAlias || 'CONTRACT',
            }).then(() => {
                this.$MessageToast.success(this.$t('recoverSpecialSeal.requestSend'));
                this.visible = false;
            });
        },
    },
};
</script>

<style lang="scss">
.recover-special-seal .el-dialog {
    width: 600px;
    .body-content {
        line-height: 24px;
        &__seal-img {
            text-align: center;
            img {
                width: 170px;
            }
        }
    }
    .bottom-part {
        text-align: center;
        .el-button {
            height: 40px;
            background-color: #127FD2;
            border-color: #127FD2;
        }
        &__opt-note {
            color: #999;
            padding: 15px 0 10px;
        }
    }
}
</style>
