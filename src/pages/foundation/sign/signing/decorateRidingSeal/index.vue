<template>
    <!-- 装饰骑缝章 -->
    <div class="decorate-riding-seal" :style="{'transform': `scale(${scale})`, 'height': `${height}px`, 'right': `${$i18n.locale === 'ar' ? rightOffset : -rightOffset}px`}">
        <div class="decorate-riding-seal-bg" v-if="isFirstRidingSeal" />
        <div class="seal-img-bg" :style="{ bottom: `${y * 100}%`}">
            <div class="switch"
                v-show="showSwitch && canShowSwitch"
                @mouseover="showSwitch=true"
                @mouseleave="showSwitch = false"
                @click="handleSwitch"
            >
                <i class="el-icon-ssq-qiehuan"></i>
                <span>切换</span>
            </div>
            <template v-if="['approval', 'shareView'].includes(signType)">
                <div class="label-header-detail">
                    <span>{{ $t('sign.sealBySomeone', { name }) }}</span>
                </div>
            </template>
            <img v-if="src" :src="`${src}?access_token=${$cookie.get('access_token')}`" @mouseover="showSwitch=true" @mouseleave="showSwitch = false" />
            <div v-else class="seal-icon">
                <i v-if="$t('lang') === 'zh'" class="riding-seal-icon el-icon-ssq-qifengzhang1"></i>
                <i v-else class="riding-seal-icon el-icon-ssq-Ridingstamp"></i>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        src: {
            type: String,
            default: '',
        },
        y: {
            type: Number,
            default: 0.5,
        },
        scale: {
            type: Number,
            default: 1,
        },
        height: {
            type: Number,
            default: 1122,
        },
        labelId: {
            type: String,
            default: '',
        },
        //  WAIT_FOR_COMPOSE 状态的骑缝章不展示切换按钮
        canShowSwitch: {
            type: Boolean,
            default: true,
        },
        signType: {
            type: String,
            default: '',
        },
        name: {
            type: String,
            default: '',
        },
        isFirstRidingSeal: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            showSwitch: false,
        };
    },
    computed: {
        rightOffset() {
            const RIDE_SEAL_COMP = 158; // 骑缝章区域宽度
            const BG_WIDTH = 110; // 多页背景宽度
            const OFFSET_AFTER_TRANSFORM = RIDE_SEAL_COMP * (this.scale - 1); // transform后，RIDE_SEAL_COMP宽度变化
            return Math.floor(BG_WIDTH * this.scale - OFFSET_AFTER_TRANSFORM); // 基于文档右边线做计算，bg的offset要减去由于缩放所产生的宽度变化
        },
    },
    methods: {
        handleSwitch() {
            this.$emit('switch-riding-seal', this.labelId);
        },
    },
};
</script>

<style lang="scss" scoped>
    .decorate-riding-seal {
        width: 158px;
        transform-origin: 0 0;
        // height: 100%;
        position: absolute;
        top: 0;
        border: 1px dashed #127fd2;
        background: rgba(18, 127, 210, 0.05);

        .decorate-riding-seal-bg {
            position: absolute;
            right: 0;
            width: 110px;
            height: 100%;
            background: #FAFAFA;
            background-image: linear-gradient(270deg, rgba(255, 255, 255, 0.5) 0%, rgba(217, 217, 217, 0.5) 100%);
            background-size: 22px;
            transform-origin: left;
        }
        .seal-img-bg {
            z-index: 1;
            width: 158px;
            position: absolute;
            left: 0;
            cursor: pointer;
            transform-origin: left;
            .switch {
                position: absolute;
                left: -1px;
                top: -20px;
                width: 100%;
                height: 20px;
                line-height: 20px;
                background: #1274CC;
                color: #fff;
                text-align: center;
                clear: both;
                font-size: 12px;
                cursor: pointer;
                border-left: 1px solid #1274CC;
                border-right: 1px solid #1274CC;
            }
            .label-header-detail{
				position: absolute;
				left: 0;
				top: -24px;
				padding: 0 5px;
				min-width: 100%;
				min-height: 24px;
				border: 1px solid #959595;
				line-height: 24px;
				box-sizing: border-box;
				background: #fff7b6;
				color: #333;
				font-size: 12px;
				cursor: pointer;

				.detail-content{
					display: inline-block;
					width: calc(100% - 28px);
                    white-space: normal;
				}

				.applicant-seal{
					display: inline-block;
				}
                .applicant-name {
                    padding-left: 10px;
                }

				.cancel{
					font-size: 12px;
					color: #1687dc;
					cursor: pointer;
				}
			}
            img {
                width: 100%;
            }
            .seal-icon{
                height: 158px;
                background: rgba(201, 231, 255, 0.75);
                i{
                    font-weight: normal;
                    font-size: 70px;
                    z-index: 1;
                    position: absolute;
                    top:44px;
                    left: 44px;
                }
                &:after{
                    content: '';
                    display: block;
                    position: absolute;
                    top:10px;
                    left: 10px;
                    width: 138px;
                    height: 138px;
                    border-radius: 50%;
                    background: rgba(255, 255, 255, 0.3);
                }
            }
        }

    }
</style>
