<!-- 视频显示 -->
<template>
    <el-dialog
        :title="title"
        size="big"
        class="uptate-tip-pop el-dialog-bg"
        :visible.sync="visible"
        :before-close="handleClose"
    >
        <div class="content">
            <el-radio-group v-model="documentId">
                <el-radio :label="doc.documentId" v-for="doc in docList" :key="doc.documentId">
                    {{ doc.documentTitle }}
                </el-radio>
            </el-radio-group>
        </div>
        <template slot="footer">
            <el-button @click="handleClose">取消</el-button>
            <el-button type="primary" @click="jump">确认</el-button>
        </template>
    </el-dialog>
</template>
<script>
export default {
    props: {
        type: {
            type: String,
            default: '',
        },
        visible: {
            type: Boolean,
        },
        docList: {
            type: Array,
            default: () => {
                return [];
            },
        },
        currentDocIndex: {
            type: Number,
            default: 0,
        },
        path: {
            type: String,
            default: '',
        },
        sensorsTrackContractInfo: {
            type: Object,
            default: () => {
                return {};
            },
        },
    },
    data() {
        return {
            documentId: '',
        };
    },
    computed: {
        title() {
            return `请选择要${this.type}的文件，仅支持word、pdf格式`;
        },
    },
    watch: {
        currentDocIndex(newIndex) {
            this.documentId = this.docList[newIndex].documentId;
        },
    },
    methods: {
        handleClose() {
            this.$sensors.track({
                eventName: `Ent_HubbleEnterWindow_BtnClick`,
                eventProperty: {
                    page_name: '合同签署页',
                    ...this.sensorsTrackContractInfo,
                    window_name: 'Hubble选择文件弹窗',
                    icon_name: '关闭',
                },
            });
            this.$emit('update:visible', false);
        },
        jump() {
            const documentName = this.docList.find(doc => doc.documentId === this.documentId).documentTitle;
            this.$sensors.track({
                eventName: `Ent_HubbleEnterWindow_BtnClick`,
                eventProperty: {
                    page_name: '合同签署页',
                    ...this.sensorsTrackContractInfo,
                    window_name: 'Hubble选择文件弹窗',
                    document_id: this.documentId,
                    document_name: documentName,
                    icon_name: '确定',
                },
            });
            sessionStorage.setItem('signingPagePath', this.$route.fullPath);
            sessionStorage.setItem('fromDocument', true);
            let path = this.path;
            if (this.type === '抽取') {
                path += `?contractId=${this.docList[this.currentDocIndex].contractId}&documentId=${this.documentId}`;
            } else {
                path += `/${this.docList[this.currentDocIndex].contractId}/${this.documentId}`;
            }
            this.$router.push(path);
            this.handleClose();
        },
    },
    mounted() {
        this.documentId = this.docList[this.currentDocIndex].documentId;
    },
};
</script>
<style lang="scss" scoped>
.content {
    width: 450px;
    &::v-deep .el-radio {
        display: block;
        margin-left: 0;
        margin-bottom: 15px;
        .el-radio__label {
            font-size: 14px;
            color: #333;
        }
    }
}

</style>
