<template>
    <div class="doc-wrap">
        <!-- 自定义签署位置，左侧 签名/印章 列表 -->
        <DragLabelsSidebar
            v-if="signerDragAble"
            v-show="!isReading"
            :isEnt="isEnt"
            :subjectName="subjectName"
            :receiver="receiver"
            :otherEntSubject="otherEntSubject"
            :signQualifiedDialogVisible="false"
            :mySeals="mySeals"
            :sealsLength="sealsLength"
            :signatures="signatures"
            :signDecision="signDecision"
            @on-drag-start="onDragStart"
            @to-auth="$emit('to-auth')"
            @select-other-subject="i => this.$emit('clickOtherEntSubjectBtn',i)"
            @add-hand-write="$emit('handleClickAddHandWrite')"
            @add-seal="$emit('handleAddSeal')"
        />

        <div :class="{'documents-content': true, 'left-add': signerDragAble}" id="sign-documents" @scroll="handlePageScroll">
            <div class="site-doc-container">
                <div class="site-doc-scale">
                    <i class="iconfont el-icon-ssq-fangdajing1 scale" @click="zoomBiggerOrSmaller('bigger')"></i>
                    <span></span>
                    <i class="iconfont el-icon-ssq-suoxiaojing scale" @click="zoomBiggerOrSmaller('smaller')"></i>
                </div>
                <div class="site-doc-title" v-if="docList.length">{{ docList.length > 1 ? docList[currentDocIndex].fileName : '' }}</div>
            </div>
            <!-- 代理签署提醒 -->
            <div class="options-proxy" v-if="receiver && receiver.receiverType === 'PROXY_SIGNER'"><i class="iconfont el-icon-ssq-jingshitanhaox"></i>{{ $t('sign.signIdentityAs', {person: receiver.nominalSignerDTO.enterpriseBasicInfoDTO.enterpriseName}) }}</div>
            <div class="documents" ref="docContent" :style="wrapperStyle">
                <component
                    ref="pdfContent"
                    :is="curDocRenderType"
                    :style="styleObject"
                    :contractTransformScale="contractTransformScale"
                    :scale="scale"
                    :isFromSignPage="true"
                    :currentPageIndex="currentPageIndex"
                    :doc="curDoc"
                    :isShowRidingSeal="isMayShowRidingSeal"
                    :isNoPermissionPage="isNoPermissionPage"
                    :fragment="fragment"
                    parentElId="sign-documents"
                    v-if="curDoc.pdfurl"
                    @updateCurrentPageIndex="handleUpdatePageIndex"
                    @ready="handleReady"
                    @update-thumbnail="updateThumbnail"
                >
                    <template slot-scope="index">
                        <Labels
                            v-for="(mark, markIndex) in markList(index.data)"
                            v-show="!isReading"
                            :key="mark.labelId"
                            :page="{
                                width: curDoc.page[index.data].width,
                                height: curDoc.page[index.data].height,
                            }"
                            :initialMark="mark"
                            :docList="docList"
                            :signerDragAble="signerDragAble"
                            :signPlaceBySigner="signPlaceBySigner"
                            :signerDragLabelTypeList="signerDragLabelTypeList"
                            :dragStatus="dragStatus"
                            :docIndex="currentDocIndex"
                            :pageIndex="mark.pageNumber-1"
                            :markIndex="markIndex"
                            :markList="markList(index.data)"
                            :signType="$route.query.type"
                            :isHybridCloudContract="isHybridCloudContract"
                            :receiver="receiver"
                            :labels="labels"
                            :scale="scale"
                            :jaEntHasNotAuthenticated="jaEntHasNotAuthenticated"
                            @click-transfer="$emit('handleClickTransfer', mark, currentDocIndex, mark.pageNumber-1, markIndex)"
                            @click-cancel="$emit('handleClickCancel',mark, currentDocIndex, mark.pageNumber-1, markIndex)"
                            @click-label="$emit('handleClickLabels', mark, currentDocIndex, mark.pageNumber-1, markIndex, 'click', arguments)"
                            @click-switch="$emit('handleClickLabels',mark, currentDocIndex, mark.pageNumber-1, markIndex, 'switch')"
                            @click-handWrite="$emit('handleClickHandWrite',mark, currentDocIndex, mark.pageNumber-1, markIndex, 'switch')"
                            @del-label="handleDeleteLabel(currentDocIndex, mark.pageNumber-1, markIndex)"
                            @update="(obj, attr, val, isBlur) => handleUpdateWriteLabel(obj, attr, val, isBlur)"
                            @relocation-label="handleRelocationLabel($event, currentDocIndex, mark.pageNumber-1, markIndex)"
                            @check-sign-qualified="$emit('checkSignQualified')"
                        />
                        <!-- 1个文档只有1页，不展示骑缝章 -->
                        <RidingSeal
                            v-show="!isReading"
                            v-if="isShowRidingSeal && getCurtentRidingSeal"
                            :pageHeight="curDoc.page[index.data].height_init"
                            :ridingSeal="getCurtentRidingSeal"
                            :contractId="contractId"
                            :scale="scale"
                            @switch-riding-seal="$emit('addRridingSeal', 'switch')"
                            @update-top="data => $emit('updateRridingSealData', data)"
                            @update-server="$emit('updateRridingSealToServer')"
                        ></RidingSeal>

                        <!-- 合同装饰骑缝章-->
                        <DecorateRidingSeal
                            v-show="curDoc.page.length > 1 && decorateRidingSealVisible && decorateRidingSeal.status!=='WAIT_FOR_COMPOSE' && isShowRidingSealByDocumentIds(decorateRidingSeal)"
                            v-for="(decorateRidingSeal,ridingSealIndex) in decorateRidingSealVos"
                            :key="decorateRidingSeal.labelId"
                            @switch-riding-seal="() => $emit('switchRridingSeal', {labelId: decorateRidingSeal.labelId, signatureId: decorateRidingSeal.signatureId})"
                            :labelId="decorateRidingSeal.labelId"
                            :src="decorateRidingSeal.previewUrl"
                            :y="decorateRidingSeal.y"
                            :height="curDoc.page[index.data].height_init"
                            :scale="scale"
                            :isFirstRidingSeal="ridingSealIndex===0"
                            :canShowSwitch="decorateRidingSeal.status!=='WAIT_FOR_COMPOSE'"
                            :signType="$route.query.type"
                            :name="decorateRidingSeal.displayName || decorateRidingSeal.displayAccount || ''"
                        />
                        <template v-for="annotate in filterAnnotateList(index.data)">
                            <Mgapprovenote
                                v-if="annotate.annotationId === currentAnnotationId"
                                :key="annotate.annotationId"
                                :annotateData="annotate"
                                :sensorsTrackContractInfo="sensorsTrackContractInfo"
                            ></Mgapprovenote>
                        </template>
                    </template>
                </component>
                <div class="signing-bottom-btn" v-if="curDoc.hasParsed">
                    <p class="next-doc-btn" v-if="docIndex != docList.length -1" @click="handleUpdateDocIndex(docIndex+1)">{{ $t('sign.enterNextContract') }}</p>
                    <div
                        v-if="signType == 'sign' && bottomSignBtnVisible && !isReading"
                        v-show="docIndex == docList.length -1 && $refs.pdfContent && !$refs.pdfContent.loading"
                        class="done-btn"
                        @click="$emit('to-next')"
                    >
                        {{ $parent._setConfrimSignButtonText() }}
                    </div>
                </div>

            </div>
        </div>
        <!-- 右侧缩略图栏 -->
        <div class="documents-info">
            <el-tabs class="approval-tabs" v-model="currentApprovalType" v-if="canUseMgapprovenote">
                <el-tab-pane name="annotation">
                    <div class="approval-tabs__label" slot="label">
                        <p>{{ $t('mgapprovenote.history') }}</p>
                        <span>{{ $t('contractCompare.historyLog', {num: curDocAnnotationList.length}) }}</span>
                    </div>
                </el-tab-pane>
                <el-tab-pane name="contract">
                    <div class="approval-tabs__label" slot="label">
                        <p>{{ $t('sign.docView') }}</p>
                        <span>{{ $t('sign.allPage',{num:curDoc.page.length}) }}</span>
                    </div>
                </el-tab-pane>
            </el-tabs>
            <div v-show="currentApprovalType === 'annotation'">
                <ul class="annotation-list">
                    <template v-for="item in curDocAnnotationList">
                        <li
                            v-show="item.approverAnnotationType"
                            :class="{
                                'annotation-list-item': true,
                                active: item.annotationId === currentAnnotationId,
                            }"
                            :key="item.annotationId"
                            @click="setAnnotationId(item)"
                        >
                            <div>{{ annotationShowName(item) }}</div>
                            <span>{{ item.createTime }}</span>
                        </li>
                    </template>
                </ul>
            </div>
            <div v-show="currentApprovalType === 'contract'">
                <div class="preview-header">
                    <!--签署需要添加附件时，展示上传附件区域和缩略图 -->
                    <div v-if="signType === 'sign' && attachmentList.length " class="tab-header">
                        <div class="preview-fill"
                            :class="activeTab === 'PREVIEW' ? 'active' : '' "
                            @click="$emit('update:activeTab','PREVIEW')"
                        >
                            {{ $t('sign.fileList') }}
                        </div>
                        <div class="preview-fill"
                            :class="activeTab === 'FILL' ? 'active' : '' "
                            @click="$emit('update:activeTab', 'FILL')"
                        >
                            {{ $t('sign.addSignerFile') }}
                        </div>
                    </div>
                    <!-- 非签署情况，不显示签约台 -->
                    <div v-else class="contract-title">
                        <i class="iconfont el-icon-ssq-wenjian"></i>
                        {{ contractData.contractTitle }}
                    </div>
                </div>
                <!-- 左侧上传附件组件 -->
                <FillContent
                    v-if="signType === 'sign' && attachmentList.length && activeTab === 'FILL'"
                    :attachments="attachmentList"
                    :signDecision="signDecision"
                    :isHybridCloudContract="isHybridCloudContract"
                    :receiverId="receiver.receiverId"
                    :receiver="receiver"
                    :signOperationType="signOperationType"
                    @check-sign-qualified="$emit('checkSignQualified')"
                >
                </FillContent>
                <!-- 右侧缩略图栏 -->
                <div class="mini-documents" v-if="activeTab === 'PREVIEW'">
                    <MiniDoc
                        v-if="docList.length"
                        :docList="docList"
                        :isReading="isReading"
                        :currentDocIndex="currentDocIndex"
                        :currentPageIndex="currentPageIndex"
                        @updateCurrentDocIndex="handleUpdateDocIndex"
                        @updateCurrentPageIndex="handleUpdatePageIndex"
                        :hiddenFileName="docList.length === 1"
                    ></MiniDoc>
                </div>
            </div>
        </div>
        <!-- navi -->
        <!--  由签署人指定位置的情况暂不显示导航的箭头  -->
        <div v-if="signType == 'sign'&& !signerDragAble"
            id="navi"
            v-show="!isReading"
            :class="{navi: true, 'left-add': signerDragAble}"
            @click="handleClickNavi"
        >
            {{ naviText }}
        </div>

        <!-- 自定义签署位置浮动icon DOM start-->
        <!-- 鼠标跟随 宽高需要和markInfo里标签的默认宽高保持一致-->
        <div id="flying-seal"
            class="flying-icon flying-seal"
            v-show="flyingType === 'SEAL'"
            :style="flyingIconScaleStyle"
        >
            <img :src="flyingSealImgURL" class="seal-img" alt="">
        </div>
        <div id="flying-signature"
            class="flying-icon flying-sig"
            v-show="flyingType === 'SIGNATURE'"
            :style="flyingIconScaleStyle"
        >
            <img :src="flyingSigImgURL" class="sig-img" alt="">
        </div>
        <div id="flying-date"
            class="flying-icon flying-date"
            v-show="flyingType === 'DATE'"
            :style="flyingIconScaleStyle"
        >
            {{ signDate }}
        </div>
        <!-- 自定义签署位置浮动icon DOM end-->

        <!-- hubble入口 -->
        <HubbleApply
            :isHybridCloudContract="isHybridCloudContract"
            :docList="docList"
            :currentDocIndex="currentDocIndex"
            :sensorsTrackContractInfo="sensorsTrackContractInfo"
            @changeAgentType="changeAgentType"
            @handleChangeType="() => showAgent = true"
        ></HubbleApply>
        <ContractExtractSidebar :receiverId="receiver.receiverId"></ContractExtractSidebar>
        <MgapprovenoteDialog></MgapprovenoteDialog>
        <ApprovalAuthDialog :sensorsTrackContractInfo="sensorsTrackContractInfo"></ApprovalAuthDialog>
        <AgentDialog
            :visible.sync="showAgent"
            :contract-id="contractId"
            :receiver-id="receiver.receiverId"
            :riskList="riskList"
            :agentType="agentType"
            :productType="productType"
            :productConfig="productConfig"
            :agentTitle="agentTitle"
            @close="closeAgent"
            @renderFragment="(val) => fragment = val"
            @locateShadow="locateShadow"
            @changeAgentType="changeAgentType"
            @handleChangeType="() => showAgent = true"
            @convertToRisk="convertToRisk"
        />
        <ChargeDialog
            :show="showChargeDialog"
            :onlyPerson="true"
            :productConfig="productConfig"
            :productType="productType"
            @handleClose="getPayResult"
            @hideDialog="showChargeDialog = false"
        />
    </div>
</template>
<script>
import { mapActions, mapGetters, mapMutations, mapState } from 'vuex';
import Pdf from 'components/pdf/Pdf.vue';
import Images from 'components/contractImgPreview/index.vue';
import { markInfo } from 'src/pages/foundation/sign/common/info/info.js';
import { throttle, markCoordinateTransform, dateGenerator, debounce } from 'src/common/utils/fn.js';
import MiniDoc from 'src/pages/foundation/sign/field/fieldMiniDoc/FieldMiniDoc.vue';
import Labels from 'src/pages/foundation/sign/common/labels/Labels.vue';
import FillContent from '../fillContent/FillContent.vue';
import DecorateRidingSeal from '../decorateRidingSeal/index.vue';
import DragLabelsSidebar from '../dragLabelsSidebar/DragLabelsSidebar';
import RidingSeal from '../ridingSeal/index.vue';
import Mgapprovenote from '../mgapprovenote/index.vue';
import MgapprovenoteDialog from '../mgapprovenote/dialog/index.vue';
import ApprovalAuthDialog from '../mgapprovenote/approvalAuthDialog/index.vue';
import ContractExtractSidebar from 'components/contractExtractSidebar/index.vue';
import { scrollToYSmooth } from 'src/common/utils/dom.js';
import { requestFieldValuesByName } from 'src/api/sign.js';

import HubbleApply from '../hubbleApply';
import ChargeDialog from '@/components/charge/index.vue';
const AgentDialog = () => import('@/components/agentDialog/index.vue');
import { acquireDocument } from 'src/api/judgeRisk.js';

export default {
    components: {
        Pdf,
        Images,
        MiniDoc,
        Labels,
        FillContent,
        DecorateRidingSeal,
        DragLabelsSidebar,
        RidingSeal,
        HubbleApply,
        Mgapprovenote,
        ContractExtractSidebar,
        MgapprovenoteDialog,
        ApprovalAuthDialog,
        ChargeDialog,
        AgentDialog,
    },
    props: [
        // eslint-disable-next-line vue/require-prop-types
        'isScrollLimitSign', 'signPlaceBySigner', 'signerDragLabelTypeList', 'contractId', 'docList', 'signType', 'signerDragAble', 'signDecision', 'isReading', 'isHybridCloudContract', 'receiver', 'labels', 'otherEntSubject', 'mySeals', 'sealsLength', 'signatures', 'isShowRidingSeal', 'ridingSealData', 'hasRidingSeal', 'activeTab', 'contractData', 'attachmentList', 'bottomSignBtnVisible', 'decorateRidingSealVisible', 'decorateRidingSealVos', 'isEnt', 'subjectName', 'isRenderPdf', 'isNoPermissionPage', 'signOperationType', 'sensorsTrackContractInfo', 'ifPersonalCanPhraseSegmentation', 'jaEntHasNotAuthenticated',
    ],
    inject: ['updateSignatureData'],
    data() {
        return {
            curDoc: {
                page: [],
            },
            permission: this.$route.query.permission || '', // 是否为无权限审批
            /** 自定义签署位置 相关变量 start */
            dragStatus: 'end',
            flyingSealImgURL: '',
            flyingSigImgURL: '',
            flyingType: '', // 'SEAL','SIGNATURE', 'DATE',
            flyingLabelId: '',
            flyingTypeCopy: '',
            flyingMark: [], // 自定义签署位置，正在拖动的标签信息暂存
            creationStatus: 'pedding', // 拖标签时记录标签的状态
            iconZoom: 1,
            // 签署日期 // 返回今天的日期 格式：2018年11月11日
            signDate: dateGenerator(),
            /** 自定义签署位置 相关变量 end */
            currentDocIndex: 0,
            currentPageIndex: 1,
            // navi
            naviIndex: 0,
            naviText: this.$t('sign.start'),
            // 常量
            SPACE_HEIGHT: 20,
            scrollMark: {}, // 记录滚动到某标签时的标签信息，for 切换文档后 可以自动定位到某标签
            scale: 1,
            curDocRenderType: 'Pdf',
            contractTransformScale: 1, // 合同上方的放大缩小按钮缩放系数
            previewedDocIds: [], // 已经预览的文档id，混合云一次性token需要处理
            lastScrollTop: 0,
            lastScrollTopDocIndex: 0, // 上次滚动时，文档索引，点击底部切换文档时，会自动触发scroll，计算逻辑要区分
            currentApprovalType: 'contract',
            cacheDocumentSize: 0, // 缓存页面宽高，resize判断界面是放大还是缩小
            showXScroll: false, // 是否展示横向滚动条
            showAgent: false,
            agentTitle: '',
            agentType: '',
            riskList: [],
            fragment: [],
            showChargeDialog: false,
            paymentPlanType: null,
            productConfig: {
                title: this.$t('judgeRisk.title'),
                per: this.$t('hubblePackage.copy'),
                unitPer: this.$t('hubblePackage.copy'),
            },
            productType: 22,
        };
    },
    computed: {
        ...mapState(['scrollProgress']),
        ...mapGetters([
            'checkFeat', 'getIsForeignVersion',
        ]),
        ...mapState('approval', ['annotateList', 'currentAnnotationId', 'isMgapprovenoteMove']),
        ...mapState({
            hasAuthorized: state => state.approval.hasAuthorized,
        }),
        markList() {
            return pageIndex => {
                // 当前文档的markList
                if (!this.curDoc.page) {
                    return [];
                }
                return this.curDoc.page[pageIndex]?.marks || [];
            };
        },
        docIndex() {
            return this.currentDocIndex;
        },
        pageIndex() {
            return this.currentPageIndex;
        },
        zoomCoefficient() {
            return this.scale;
        },
        // 文档的最大宽度
        maxWidth() {
            // 切换文档时，需要重新计算文档的最大宽高度
            const docMaxWidth = this.curDoc.page.reduce((max, page) => Math.max(max, page.width_init || page.width || 0), 0);
            // return docMaxWidth
            // 除与合同重叠部分以外，骑缝章宽度/阴影背景部分宽度
            return docMaxWidth + (this.isMayShowRidingSeal ? 110 : 0);
        },
        // 合同最大缩放系数
        maxScale() {
            const docPaddingWidth = 140;
            const rightMiniDocWidth = 210;
            const screenWidth = window.innerWidth;
            return (screenWidth - docPaddingWidth - rightMiniDocWidth) / this.maxWidth;
        },
        // 合同最小缩放系数
        minScale() {
            return this.maxScale / 4;
        },
        docHeight() {
            return this.curDoc.page.reduce((sum, page) => {
                sum += ((page.height || 0) + 20);
                return sum;
            }, 0);
        },
        // 渲染的文档所有页面的最大高度，切换文档时，当缩放的scale小于1时，会导致高度留白，因此要设置高度
        sumHeight() {
            return Math.min(this.docHeight, this.docHeight * this.scale);
        },
        wrapperStyle() {
            const bottomBtnHeight = 60; // 给文档下方按钮预留的空间
            return {
                height: `${this.docHeight * this.contractTransformScale + bottomBtnHeight}px`,
                overflowX: this.showXScroll ? 'auto' : 'hidden',
            };
        },
        styleObject() {
            // this.hasRidingSeal
            const marginKey = this.$i18n.locale === 'ar' ?  'margin-right' : 'margin-left';
            return {
                [marginKey]: this.curDoc.hasParsed && this.isMayShowRidingSeal && !this.hasRidingSeal ? `${55 * this.scale}px` : 0,
                'transform': `scale(${this.contractTransformScale})`,
                'transform-origin': 'top center',
            };
        },
        // 返回当前文档中所有非日期标签 数组
        curDocLabels() {
            const labels = [];
            this.curDoc.page.forEach((page) => {
                page.marks.forEach(mark => {
                    if (mark.type !== 'DATE') {
                        labels.push(mark);
                    }
                });
            });
            return labels;
        },
        // 根据在哪些文档上显示骑缝章字段 showInDocumentIds 决定是否显示合同装饰骑缝章
        isShowRidingSealByDocumentIds() {
            return (decorateRidingSeal) => {
                return (decorateRidingSeal.showInDocumentIds || []).includes(this.curDoc.documentId);
            };
        },
        flyingIconScaleStyle() {
            return {
                'transform-origin': 'top left',
                transform: `scale(${this.zoomCoefficient})`,
            };
        },
        getCurtentRidingSeal() {
            const temp = this.ridingSealData.filter(item => item.documentId === this.curDoc.documentId);
            return temp && temp[0];
        },
        isMayShowRidingSeal() {
            const { curDoc, decorateRidingSealVisible, checkFeat, isShowRidingSeal, getCurtentRidingSeal } = this;
            return (curDoc.page.length > 1 && (decorateRidingSealVisible || checkFeat.ridingSeal)) || (isShowRidingSeal && getCurtentRidingSeal);
        },
        docListTotalPages() {
            return this.docList.reduce((sum, doc) => {
                sum += doc.pageSize;
                return sum;
            }, 0);
        },
        canUseMgapprovenote() {
            return (this.checkFeat.mgapprovenote || this.ifPersonalCanPhraseSegmentation) && !this.isHybridCloudContract && !this.permission && !this.getIsForeignVersion;
        },
        curDocAnnotationList() {
            return this.annotateList.filter(el => {
                if (el.approverAnnotationType === 'RISK_JUDGMENT' && el.annotationResultContent !== null) {
                    return el;
                } else if (el.documentId === this.curDoc.documentId && el.annotationResultContent !== null) {
                    return el;
                }
            });
        },
    },
    watch: {
        isRenderPdf: {
            handler(val) {
                this.curDocRenderType = val ? 'Pdf' : 'Images';
            },
            immediate: true,
        },
        hasAuthorized(val) {
            if (val) {
                this.checkAnalysisId().then(() => {
                    if (this.agentType) {
                        this.showAgent = true;
                    }
                });
            }
        },
    },
    methods: {
        ...mapMutations(['setSignPageScrollProgress']),
        ...mapMutations('approval', ['pushAnnotateList', 'setCurrentAnnotationId']),
        ...mapActions('approval', ['getAnnotationConfig', 'getAnnotationHistory', 'checkApprovalAuth']),
        handlePageScroll: debounce(function(e) {
            const scrollTop = e.target.scrollTop;
            // 底部下一份文档按钮切换时，会自动触发两次scroll事件，并且scrollTop不一定为0
            if (this.lastScrollTopDocIndex !== this.currentDocIndex || !scrollTop) {
                this.lastScrollTopDocIndex = this.currentDocIndex;
                this.lastScrollTop = 0;
                return;
            }
            // 滚动到底部签署限制时更新滚动条数据、向上滚动不处理、已触底阅读后不处理
            if (!this.isScrollLimitSign || this.lastScrollTop > scrollTop || this.scrollProgress === 100) {
                return;
            }
            this.updateScrollProgress(scrollTop);
        }, 50),
        updateScrollProgress(scrollTop) {
            this.lastScrollTop = scrollTop;
            const container = document.getElementById('sign-documents');
            let pagePercent = 0;

            // console.log('updateScrollProgress = ', scrollTop, container.scrollHeight, container.clientHeight);
            if (container.scrollHeight === container.clientHeight) {
                pagePercent = 1;
            } else {
                // 计算滚动百分比，相对于滚动区域总高 - 容器高度
                pagePercent = scrollTop / (container.scrollHeight - container.clientHeight);
            }

            if (this.docList.length > 1) {
                const docPercent = this.curDoc.pageSize / this.docListTotalPages;
                const lastDocsList = this.docList.filter((a, i) => i < this.currentDocIndex);
                let lastDocsPercent = 0;
                if (lastDocsList.length) { // 计算历史文档的百分比
                    lastDocsPercent = lastDocsList.reduce((sum, a) => {
                        sum += a.pageSize;
                        return sum;
                    }, 0) / this.docListTotalPages;
                }
                pagePercent = pagePercent * docPercent + lastDocsPercent;
            }
            const finnalPercent = Math.min(Math.ceil(pagePercent * 100), 100);
            // 来回滚动时避免节流时长导致的反向变小的问题
            if (finnalPercent < this.scrollProgress) {
                return;
            }
            this.setSignPageScrollProgress(finnalPercent);
        },
        async handelInitAnnotate() {
            if (!this.canUseMgapprovenote) {
                this.currentApprovalType = 'contract';
                return;
            }
            this.$store.state.approval.contractId = this.contractId;
            this.$store.state.approval.receiverId = this.receiver.receiverId;
            this.$store.state.approval.documentId = this.curDoc.documentId;
            await this.getAnnotationHistory();
            this.initAnnotate();
            this.checkApprovalAuth();
        },
        filterAnnotateList(index) {
            return this.annotateList.filter(el => (el.documentId === this.curDoc.documentId && el.contractContentPosition?.page === index + 1));
        },
        initAnnotate() {
            document.getElementById('sign-documents').scrollTop = 0;
            const domList = this.curDocRenderType === 'Pdf' ? document.getElementsByClassName('contract-page-content') : document.getElementsByClassName('contract-img-content');
            if (!domList.length) {
                return setTimeout(() => {
                    this.initAnnotate();
                }, 100);
            }
            // 定义鼠标按下的坐标和鼠标抬起的坐标
            let startPos = { x: 0, y: 0 };
            let endPos = { x: 0, y: 0 };
            let isMouseDown = false;
            let moveDom = null;
            // 遍历每个dom，添加事件
            for (let i = 0; i < domList.length; i++) {
                const dom = domList[i];
                const rect = dom.getBoundingClientRect();
                let realTop;
                dom.addEventListener('mousedown', (event) => {
                    realTop = document.getElementById('sign-documents').scrollTop - rect.top;
                    isMouseDown = true;
                    // 计算起始点百分比坐标
                    startPos = {
                        x: (event.clientX - rect.left) / rect.width,
                        y: (event.clientY + realTop) / rect.height,
                    };
                    endPos = {
                        x: (event.clientX - rect.left) / rect.width,
                        y: (event.clientY + realTop) / rect.height,
                    };
                    if (this.isMgapprovenoteMove) {
                        return;
                    }
                    moveDom = document.createElement('div');
                    moveDom.className = 'mgapprovenote__box disabled';
                    dom.appendChild(moveDom);
                });

                dom.addEventListener('mousemove', (event) => {
                    if (isMouseDown) {
                        endPos = {
                            x: (event.clientX - rect.left) / rect.width,
                            y: (event.clientY + realTop) / rect.height,
                        };
                        if (this.isMgapprovenoteMove) {
                            return;
                        }
                        moveDom.style.top = `${Math.min(startPos.y, endPos.y) * 100}%`;
                        moveDom.style.left = `${Math.min(startPos.x, endPos.x) * 100}%`;
                        moveDom.style.width = `${Math.abs(startPos.x - endPos.x) * 100}%`;
                        moveDom.style.height = `${Math.abs(startPos.y - endPos.y) * 100}%`;
                    }
                });

                dom.addEventListener('mouseup', () => {
                    if (this.isMgapprovenoteMove) {
                        return;
                    }
                    isMouseDown = false;
                    const width = Math.abs(startPos.x - endPos.x);
                    const height = Math.abs(startPos.y - endPos.y);
                    if (width > 0 && height > 0) {
                        if (this.annotateList[0] && !this.annotateList[0].annotationId) {
                            return;
                        }
                        this.pushAnnotateList({
                            annotationId: '',
                            documentId: this.curDoc.documentId,
                            approverAnnotationType: '',
                            annotationResultContent: '',
                            contractContentPosition: {
                                x: Math.min(startPos.x, endPos.x),
                                y: Math.min(startPos.y, endPos.y),
                                width: width,
                                height: height,
                                page: i + 1,
                            },
                        });
                        this.$store.state.approval.currentRect = rect;
                        this.$store.state.approval.currentAnnotationId = '';
                    }
                });
                document.addEventListener('mouseup', () => {
                    if (moveDom) {
                        moveDom && dom.removeChild(moveDom);
                        moveDom = null;
                    }
                });
            }
        },
        annotationShowName(annotation) {
            if (annotation.approverAnnotationType === 'CONTRACT_ELEMENTS_EXTRACTION') {
                return this.$t('keyInfoExtract.operate');
            } else if (annotation.approverAnnotationType === 'RISK_JUDGMENT') {
                return this.$t('judgeRisk.title');
            } else {
                return {
                    CASE_SEARCH: this.$t('mgapprovenote.case'),
                    EXPERIENCE: this.$t('mgapprovenote.analyze'),
                    COMMENT: this.$t('mgapprovenote.annotate'),
                    TRANSLATION: this.$t('mgapprovenote.translate'),
                    LEGAL_ADVICE: this.$t('mgapprovenote.law'),
                    MARK: this.$t('mgapprovenote.mark'),
                    RELATED_CONTENT_QUERY: this.$t('mgapprovenote.content'),
                    TABLE_SUMMARY: '表格解读',
                    CONTRACT_ELEMENTS_EXTRACTION: this.$t('keyInfoExtract.operate'),
                    CONTRACT_INTERPRETATION: this.$t('judgeRisk.aiInterpret'),
                }[annotation.approverAnnotationType] + (annotation.contractContent ? ` - ${annotation.contractContent}` : '');
            }
        },
        zoomBiggerOrSmaller(type) {
            if (type === 'bigger') {
                const docPaddingWidth = 140;
                const rightMiniDocWidth = 210;
                const docWidth = document.querySelector('.preview-pdf-container').getBoundingClientRect().width;
                const maxWidth = window.innerWidth - docPaddingWidth - rightMiniDocWidth;
                if (this.contractTransformScale + 0.1 <= this.maxScale && docWidth < maxWidth) {
                    this.contractTransformScale += 0.1;
                }
            } else {
                if (this.contractTransformScale - 0.1 >= this.minScale) {
                    this.contractTransformScale -= 0.1;
                }
            }
            if (this.contractTransformScale - 1 < 0) {
                this.$localStorage.set('contractScale', (this.contractTransformScale - 1).toFixed(1));
            } else {
                this.$localStorage.remove('contractScale');
            }
        },
        // 更新page的宽高信息
        handleReady(page) {
            this.$set(this.curDoc, 'page', page);
            this.$set(this.curDoc, 'hasParsed', true);
            this.updateScale();
            this.$nextTick(() => {
                if (this.scrollMark.labelId) {
                    this.scrollToMark(this.scrollMark);
                }
            });
        },
        updateThumbnail(thumbnail) {
            this.$set(this.curDoc, 'thumbnail', thumbnail);
            this.$set(this.docList, this.currentDocIndex, {
                ...this.curDoc,
                thumbnail,
            });
        },
        handleUpdatePageIndex(currentPageIndex) {
            this.currentPageIndex = currentPageIndex;
        },
        async handleUpdateDocIndex(currentDocIndex) {
            this.currentDocIndex = currentDocIndex;
            const { documentId, fileStreamUrl } = this.docList[this.currentDocIndex];
            // 已经预览的文档重新获取token
            if (this.isHybridCloudContract && this.$hybrid.isGamma() && this.previewedDocIds.includes(documentId)) {
                this.docList[this.currentDocIndex].pdfurl = await this.$hybrid.getPdfPreviewUrl({ url: `${fileStreamUrl}&permission=${this.permission}`, hybridServer: this.$store.state.commonHeaderInfo.hybridServer, hybridTarget: '/contract/part/document/download', params: { contractId: this.contractId, documentIds: documentId, permission: this.permission } });
            } else {
                // 第一次访问的记录下来
                this.previewedDocIds.push(documentId);
            }
            // 切换在url设置之后，防止pdfurl重复
            this.curDoc = this.docList[this.currentDocIndex];
            this.currentPageIndex = 1;
            this.$store.state.currentSignDocumentId = this.curDoc.documentId;
            this.updateScale();
            this.handelInitAnnotate();
        },
        // navi
        handleClickNavi() {
            const isSignQualified = this.$parent.checkSignQualified();
            if (!isSignQualified) {
                return;
            }

            const documentsContentDom = document.querySelector('.documents-content');

            this.naviText = this.$t('sign.nextStep');

            const { allDone } = this.$parent.checkoutAllWriteDone();
            if (this.$parent.checkoutSignDone() && allDone) {
                if (this.bottomSignBtnVisible) {
                    scrollToYSmooth(documentsContentDom, this.docHeight * this.scale);
                    setTimeout(() => {
                        const marginTop = document.querySelector('#sign-documents').getBoundingClientRect().height - 80;
                        document.querySelector('#navi').style.marginTop = `${marginTop}px`;
                    }, 320);
                    return;
                }
                this.$MessageToast.success(this.$t('sign.signatureFinish'));
                return;
            }
            if (+this.naviIndex === 0) {
                this.naviIndex = this.labels.slice(this.naviIndex).findIndex(item => {
                    return item.status === 'ACTIVE';
                });
            }

            while (this.labels[this.naviIndex] && this.labels[this.naviIndex].status !== 'ACTIVE') {
                this.naviIndex++;
                if (this.naviIndex > this.labels.length - 1) {
                    this.naviIndex = 0;
                }
            }
            this.scrollToMark(this.labels[this.naviIndex]);

            this.naviIndex++;
            if (this.naviIndex > this.labels.length - 1) {
                this.naviIndex = 0;
            }
        },
        // 滚动至标签
        scrollToMark(label) {
            if (!label) {
                return;
            }
            this.scrollMark = {};
            const mark = label;
            const documentsContentDom = document.querySelector('.documents-content');
            const doc = this.docList.filter(item => {
                return item.documentId === mark.documentId;
            })[0];

            // 如果标签不在当前文档，切换到下一个需要填充的文档, 等待pdf下载完成之后，再跳转到该标签
            if (mark.documentId !== this.curDoc.documentId) {
                this.handleUpdateDocIndex(this.docList.findIndex(doc => doc.documentId === mark.documentId));
                this.scrollMark = mark; // 标志 自动跳转的标签
                return;
            }
            const pageHeight = this.sumAryHeight(doc.page, mark.pageNumber - 1);

            // mark.y为小数，坐标百分比。滚动时转换为数值坐标
            const labelHeight = mark.height > 1 ? mark.height : mark.height * doc.page[mark.pageNumber - 1].height;
            const markY = mark.y <= 1 ? (1 - mark.y) * doc.page[mark.pageNumber - 1].height - labelHeight : mark.y;

            scrollToYSmooth(documentsContentDom, pageHeight + markY, () => {
                const targetEleId = `p${label.pageNumber}_${label.labelId}`;
                if (!document.getElementById(targetEleId)) {
                    return;
                }
                const rect = document.getElementById(targetEleId).getBoundingClientRect();
                const topVal = Math.max(rect.top - 80/* 页面顶部所占高度*/ - 30/* navi的marginTop*/, -20);
                document.querySelector('#navi').style.marginTop = `${topVal}px`;
            });
        },
        /**
         * @param  {Array} ary 数组
         * @return {Number} 数组项height之和
         */
        accHeight(ary) {
            return ary.length ? ary.reduce((a, b) => {
                return {
                    height: parseFloat(a.height) + parseFloat(b.height) + this.SPACE_HEIGHT,
                };
            }).height : 0;
        },
        // 计算在这份文档内，在index对应的页之前的所有页数高度之和
        sumAryHeight(ary, index) { // 如果一份pdf各图片高度不等，就会有bug，ruaruarua...
            const sliceAry = ary.slice(0, index);
            sliceAry.push({ height: 0 }); // todo
            const res = parseFloat(this.accHeight(sliceAry));
            sliceAry.splice(sliceAry.length - 1, 1);
            return res;
        },
        /* 自定义签署位置相关 start */
        onDragStart(e, type, markId = '', fileId = '') {
            this.iconZoom = this.zoomCoefficient;
            e.preventDefault(); // 取消img默认拖拽事件
            setTimeout(() => {
                this.dragStatus = 'started';
                this.followCursorWithIcon(e, type);
                this.flyingType = type;
                this.flyingLabelId = markId;
                if (type === 'SEAL') {
                    this.flyingSealImgURL = this.sealImgURL(markId, fileId);
                } else if (type === 'SIGNATURE') {
                    this.flyingSigImgURL = this.signatureImgURL(markId, fileId);
                }
            }, 150);
        },
        onDragMove: throttle(function(e) {
            e.preventDefault(); // 取消img默认拖拽事件
            if (this.dragStatus === 'started') {
                const $clt = e.target.closest('.contract-page-content');
                this.followCursorWithIcon(e, this.flyingType);
                if ($clt) {
                    this.flyingTypeCopy = this.flyingType;
                    this.iconZoom = this.zoomCoefficient;

                    const docI = this.currentDocIndex;
                    const pageI = $clt.getAttribute('page-index');
                    const x = this.getMarkPostion(e).x;
                    const y = this.getMarkPostion(e).y;
                    console.log('onDragMove', e, e.target.getBoundingClientRect());

                    if (this.creationStatus === 'ready') {
                        const page = this.docList[docI].page[pageI];
                        const mark = this.flyingMark[this.flyingMark.length - 1];
                        mark.docI = docI;
                        mark.pageI = pageI;
                        mark.x = x;
                        mark.y = y;
                        this.markReposition(page, mark, x, y);
                    } else {
                        this.createMark({
                            type: this.flyingType,
                            docI,
                            pageI,
                            x,
                            y,
                            width: markInfo(this.flyingTypeCopy).width,
                            height: markInfo(this.flyingTypeCopy).height,
                            sealId: this.flyingType === 'SEAL' ? this.flyingLabelId : '',
                            signatureId: this.flyingType === 'SIGNATURE' ? this.flyingLabelId : '',
                        });
                        this.creationStatus = 'ready';
                    }
                } else {
                    this.iconZoom = 1;
                }
            }
        }, 11),
        onDragEnd(e) {
            this.dragStatus = 'end';
            // 如果不在文档内部放下来 盖章无效
            const $clt = e.target.closest('.contract-page-content');
            if (!$clt || this.creationStatus !== 'ready') {
                this.flyingType = '';
                return;
            }
            this.creationStatus = 'done';
            const i = this.flyingMark.length - 1;
            const mark = this.flyingMark[i];
            const markType = this.flyingTypeCopy;
            const postData = {
                type: markType,
                labelId: '', // 新建标签，labelId为空
                contractId: this.contractId,
                documentId: this.docList[mark.docI].documentId,
                receiverId: this.receiver.receiverId,
                pageNumber: parseFloat(mark.pageI) + 1,
            };
            // 标签坐标转换为以页面左下角为原点基于页面的百分比
            const page = this.docList[mark.docI].page[mark.pageI];
            const percentCoordinate = markCoordinateTransform({
                ...mark,
                width: mark.width * this.scale,
                height: mark.height * this.scale,
            }, page.width, page.height);
            const markObj = Object.assign({}, mark, postData, percentCoordinate);

            return this.saveMark([markObj])
                .then(res => {
                    // 拖标签完成，悬浮标签消失，更新页面上的标签
                    this.flyingType = '';
                    this.docList[mark.docI].page[mark.pageI].marks.push(res.data[0]);
                })
                .catch(() => {
                    this.flyingType = '';
                    this.deleteMark(i);
                });
        },
        followCursorWithIcon(e, type) {
            type = type.toLowerCase();
            document.querySelector(`#flying-${type}`).style.left = `${e.clientX + 1}px`;
            document.querySelector(`#flying-${type}`).style.top = `${e.clientY + 1}px`;
        },
        createMark(data) {
            this.flyingMark.push(data);
        },
        deleteMark(i) {
            this.flyingMark.splice(i, 1);
        },
        saveMark(data) {
            return this.$http.post(
                `${signPath}/contracts/${this.contractId}/labels/signer-create-and-modify/`,
                data,
                { noToast: 1 },
            );
        },
        markReposition(page, mark, pX, pY) {
            const pageViewWidth = page.width;
            const pageViewHeight = page.height;
            const markViewHeight = markInfo(mark.type).height * this.zoomCoefficient;
            const markViewWidth = markInfo(mark.type).width * this.zoomCoefficient;
            const tooRight = pX >= pageViewWidth - markViewWidth && pX <= pageViewWidth;
            // 标签太靠下啦
            const tooDown = pY >= pageViewHeight - markViewHeight && pY <= pageViewHeight;
            if (tooRight) {
                mark.x = page.width - markViewWidth;
                if (tooDown) {
                    mark.y = page.height - markViewHeight;
                } else {
                    mark.y = pY;
                }
            } else if (tooDown) {
                mark.x = pX;
                mark.y = page.height - markViewHeight;
            } else {
                mark.x = pX;
                mark.y = pY;
            }
        },
        // 签名图片
        signatureImgURL(sigId, fileId) {
            return `/users/signatures/${sigId}/signature/${fileId}?access_token=${this.$cookie.get('access_token')}`;
        },
        sealImgURL(sealId, fileId) {
            if (sealId && fileId) {
                return `/ents/${sealId}/seal/${fileId}?access_token=${this.$cookie.get('access_token')}`;
            }
        },
        // 获取鼠标相对于目标元素左/上边界的距离
        getMarkPostion(e) {
            return {
                // e.x IE11有兼容性问题，本地发起未发现此问题，推测跟写了这边写了很多事件阻止冒泡有关 😒
                // x: e.x - Math.floor(e.target.getBoundingClientRect().left),
                // y: e.y - Math.floor(e.target.getBoundingClientRect().top)
                x: e.clientX - Math.floor(e.target.getBoundingClientRect().left),
                y: e.clientY - Math.floor(e.target.getBoundingClientRect().top),
            };
        },
        // 标签拖拽事件结束后，生成新的标签坐标位置，同页面拖拽，替换标签；跨页拖拽，删除原来的，push到新的
        handleRelocationLabel(data, docI, pageI, markI) {
            if (docI === data.docI && pageI === data.pageI) {
                this.docList[docI].page[pageI].marks.splice(markI, 1, data.mark);
            } else {
                this.docList[docI].page[pageI].marks.splice(markI, 1);
                this.docList[data.docI].page[data.pageI].marks.push(data.mark);
            }
        },
        // 删除标签
        handleDeleteLabel(docIndex, pageIndex, labelIndex) {
            this.docList[docIndex].page[pageIndex].marks.splice(labelIndex, 1);
            this.docList[docIndex].page.splice(pageIndex, 1, this.docList[docIndex].page[pageIndex]);
            this.docList.splice(docIndex, 1, this.docList[docIndex]);
        },
        /* 自定义签署位置相关 end */
        updateScale() {
            const { maxWidth, docWidth } = this;
            if (!maxWidth || !docWidth) {
                return this.scale =  1;
            }
            this.scale = docWidth / maxWidth;
        },
        initScale() {
            const contractScale = this.$localStorage.get('contractScale');
            if (contractScale) {
                this.contractTransformScale = 1 + Number(contractScale);
            }
        },
        // 更新可输入标签的值和状态
        handleUpdateWriteLabel(obj, attr, val, isBlur = false) {
            // 更新图片状态
            if (obj.type === 'PICTURE') {
                this.labels.forEach(label => {
                    if (label.labelId === obj.labelId) {
                        label.value = val;
                    }
                });
            }
            obj[attr] = val;
            const isPagefulLabel = obj.pageType === 'ALL';
            let pagefulLabelUpdated = false;

            // 业务字段更新时通过调用单一接口批量更新同名字段的值
            // 可输入的字段类型记得都走一遍这个逻辑，确保同名字段的值是一致的
            if (attr === 'value') {
                // 改成防抖
                this.updateLocalFieldValuesByName(obj, attr, val);
                isBlur ? this.requestUpdateFields(obj, val) : this.debounceRequestUpdateFields(obj, val);
                // 这里判断是不是blur任务，blur任务立即执行，清空队列并重置定时器
                // throttleQueue.execute({
                //     func: () => {
                //         this.updateLocalFieldValuesByName(obj, attr, val);
                //         requestFieldValuesByName(this.contractId, obj, val).catch(() => {
                //             // 原逻辑，请求失败时重置status，其实这里把值也给清掉会比较好，因为后端值没保存成功，但是考虑一个问题是用户已经输入的值会丢失，暂时先不重置value
                //             // 如果更新了值为空的话，其实status也会被更新掉
                //             this.updateLocalFieldValuesByName(obj, 'status', '');
                //         });
                //     },
                // }, isBlur ? 0 : 500);
                return false;
            }

            // 下面的逻辑应该不会走了，labels里面更新value的字段在上面拦截了，剩下图片字段attr是uploadImgUrl，type是PICTURE，看起来也进不到下面的执行
            // cfd-2119相同业务字段同步、按页渲染字段同步
            this.labels &&  this.labels.map((item) => {
                if ((item.name === obj.name && obj.type !== 'PICTURE') || (isPagefulLabel && !pagefulLabelUpdated && item.labelId === obj.labelId)) {
                    item[attr] = val;
                    // 日期类型里面执行了好几次update，所以是否要发请求需要判断attr === 'value'， fix 提交到服务器为“”报错
                    this.updateSignatureData([{
                        ...item,
                        status: isPagefulLabel ? obj.status : item.status,
                        [attr]: val,
                    }], item.type, attr === 'value', false);
                    pagefulLabelUpdated = true;
                }
            });
        },
        debounceRequestUpdateFields: debounce(function(obj, val) {
            this.requestUpdateFields(obj, val);
        }, 300),
        requestUpdateFields(obj, val) {
            requestFieldValuesByName(this.contractId, obj, val).catch(() => {
                // 原逻辑，请求失败时重置status，其实这里把值也给清掉会比较好，因为后端值没保存成功，但是考虑一个问题是用户已经输入的值会丢失，暂时先不重置value
                // 如果更新了值为空的话，其实status也会被更新掉
                this.updateLocalFieldValuesByName(obj, 'status', '');
            });
        },
        // 前端轮询更新所有同名字段的值
        updateLocalFieldValuesByName(mark, type = 'value', resultValue) {
            this.labels &&  this.labels.forEach((item) => {
                if ((item.name === mark.name) || (item.labelId === mark.labelId)) {
                    if (type === 'value' || type === '') {
                        item['value'] = resultValue;
                        item['status'] = resultValue === '' ? 'ACTIVE' : 'CONFIRMED';
                    }
                    if (type === 'status') {
                        item['status'] = resultValue;
                    }
                }
            });
        },
        handlePageResize() {
            this.showXScroll = document.body.clientWidth < this.cacheDocumentSize;
        },
        setAnnotationId(item) {
            if (item.approverAnnotationType === 'CONTRACT_ELEMENTS_EXTRACTION') {
                this.$store.state.step = 4;
                this.$store.state.commonHeaderInfo.annotationId = item.annotationId;
                this.$store.state.commonHeaderInfo.extractDialog = true;
            } else if (item.approverAnnotationType === 'RISK_JUDGMENT') {
                this.$store.state.commonHeaderInfo.annotationId = item.annotationId;
                this.$store.state.commonHeaderInfo.judgeDialog = true;
            } else {
                this.setCurrentAnnotationId(item.annotationId);
            }
        },
        checkAnalysisId() {
            return acquireDocument(this.contractId, this.receiver.receiverId).then(res => {
                this.riskList = res.data;
            });
        },
        getPayResult() {
            this.showChargeDialog = false;
            this.showAgent = true;
        },
        closeAgent() {
            this.fragment = [];
            this.showAgent = false;
            this.agentType = '';
            this.checkAnalysisId();
        },
        locateShadow(num) {
            this.$refs.pdfContent.locateShadow(num);
        },
        changeAgentType(type) {
            this.agentType = type;
            switch (type) {
                case 'RISK_JUDGEMENT':
                    this.agentTitle = this.$t('judgeRisk.title');
                    this.productConfig.title = this.$t('judgeRisk.title');
                    this.productType = 22;
                    break;
                case 'EXTRACT':
                    this.agentTitle = this.$t('keyInfoExtract.operate');
                    this.productConfig.title = this.$t('keyInfoExtract.operate');
                    this.productType = 23;
                    break;
                case 'CONTRACT_INTERPRETATION':
                    this.agentTitle = this.$t('judgeRisk.aiInterpret');
                    this.productConfig.title = this.$t('judgeRisk.aiInterpret');
                    this.productType = 27;
                    break;
                default:
                    break;
            }
        },
        convertToRisk() {
            this.closeAgent();
            this.$nextTick(() => {
                this.changeAgentType('RISK_JUDGEMENT');
                this.showAgent = true;
            });
        },
    },
    async mounted() {
        document.addEventListener('mousemove', this.onDragMove);
        document.addEventListener('mouseup', this.onDragEnd);
        this.docWidth = this.$refs.docContent.getBoundingClientRect().width;
        this.curDoc = this.docList[this.currentDocIndex];
        console.log(this.curDoc);
        this.cacheDocumentSize = document.body.clientWidth;
        window.addEventListener('resize', this.handlePageResize);
        // 默认进入的文档记录下来
        this.previewedDocIds.push(this.curDoc.documentId);
        console.log(this.scale);
        this.updateScale();
        this.initScale();
        this.handelInitAnnotate();
    },
    beforeDestroy() {
        document.removeEventListener('mousemove', this.onDragMove);
        document.removeEventListener('mouseup', this.onDragEnd);
        window.removeEventListener('resize', this.handlePageResize);
    },
};
</script>
<style lang="scss">
.approval-tabs{
    .el-tabs__header{
        padding-left: 20px;
        margin: 0;
    }
    &__label{
        display: flex;
        flex-direction: column;
        margin-top: 7px;
        text-align: center;
        p, span{
            line-height: 1;
        }
        span{
            font-size: 10px;
            color: #ccc;
            margin-top: 2px;
        }
    }
    [dir="rtl"] &{
        .el-tabs__header{
            padding-right: 20px;
            padding-left: 0;
        }

        .el-tabs__active-bar {
            display: none;
        }
        .el-tabs__item.is-active {
            border-bottom: 3px solid #20a0ff;
        }
    }
}
.annotation-list{
    padding: 0 15px;
    &-item{
        padding: 15px 0;
        cursor: pointer;
        border-bottom: 1px solid #eee;
        div{
            font-size: 14px;
            width: 100%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            margin-bottom: 8px;
        }
        span{
            font-size: 12px;
            color: #999;
        }
        &:hover, &.active{
            color: $theme-color;
            span{
                color: $theme-color;
            }
        }
    }
}
</style>
