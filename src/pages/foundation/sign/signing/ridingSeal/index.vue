<template>
    <div class="riding-seal" :style="{'transform': `scale(${scale})`, 'height': `${pageHeight}px`, 'right': `${-Math.floor(40*scale)}px`}">
        <div class="riding-seal-bg"></div>
        <div
            class="riding-seal-img"
            @mousedown="handleRidingSealMouseStart"
            @mousemove="handleRidingSealMouseMove"
            @mouseup="handleRidingSealMouseEnd"
            @mouseover="hasOverLabel=1"
            @mouseleave="onMouseLeave('hasOverLabel')"
            :style="{'top': `${sealTop}%`,'background-image': `url(${sealImg})`, 'height': `${sealHeight}px`, 'width': `${sealHeight}px`}"
            :class="{'riding-seal-img_hover': hasOverLabel || hasOverHead}"
        >
            <p
                @mouseover="hasOverHead=1"
                @mouseleave="onMouseLeave('hasOverHead')"
                @click="handleSwitch"
                v-if="!hideSwitch"
                :class="{'top': Number(sealTop) > 80}"
            >{{ $t('sign.switch') }}</p>
        </div>
    </div>
</template>

<script>
import { defaultSealWidth } from 'utils/commonVar.js';
export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['pageHeight', 'isSealManager', 'ridingSeal', 'scale', 'contractId', 'hideSwitch'],
    data() {
        return {
            sealHeight: defaultSealWidth,
            top: 0,
            moveRealtiveY: 0, // 记录上一次移动的位置
            startY: 0, // 记录每次movedown的位置
            isMoveing: false,
            hasOverLabel: 0,
            hasOverHead: 0,
            sealTop: 0,
        };
    },
    computed: {
        sealImg() {
            const { ridingSeal, $cookie } = this;
            if (ridingSeal.value) {
                return `${location.origin}${ridingSeal.value}?access_token=${$cookie.get('access_token')}`;
            }
            if (!ridingSeal.fileId) {
                return '';
            }
            return `${location.origin}/ents/${ridingSeal.sealId}/seal/${ridingSeal.fileId}?access_token=${$cookie.get('access_token')}`;
        },
    },
    watch: {
        // CFD-22840:骑缝章在切换文档时，位置遗产问题修正
        'ridingSeal.top': {
            handler(value) {
                if (!value) {
                    return;
                }
                // fix SAAS-5010 多文档，文档的高度大小存在不一致的情况，不超过最短高度的边界值
                if (value * this.pageHeight + defaultSealWidth > this.pageHeight) {
                    this.top = this.pageHeight - defaultSealWidth;
                    this.sealTop = `${this.top / this.pageHeight * 100}`;
                    return;
                }
                this.sealTop = `${value * 100}`;
                this.top = value * this.pageHeight;
            },
            immediate: true,
        },
    },
    methods: {
        onMouseLeave(key) {
            setTimeout(() => {
                this[key] = 0;
            }, 1000);
        },
        handleRidingSealMouseStart(e) {
            this.isMoveing = true;
            this.moveRealtiveY = e.y;
            this.startY = this.ridingSeal.y;
            document.addEventListener('mouseup', this.handleWindowMouseup);
        },
        handleRidingSealMouseMove(e) {
            this.hasOverLabel = 1;
            if (!this.isMoveing) {
                return false;
            }
            this.positionUpdate(e);
        },
        handleRidingSealMouseEnd(e) {
            this.positionUpdate(e, true);
            this.isMoveing = false;
            document.removeEventListener('mouseup', this.handleWindowMouseup);
        },
        positionUpdate(e, isEnd = false) {
            this.top = this.top + (e.y - this.moveRealtiveY);
            this.moveRealtiveY = e.y; // 更新相对位置

            const [mixHeight, maxHeight] = [0, this.pageHeight];
            // 下边界
            if (this.top + defaultSealWidth >= maxHeight) {
                this.top = maxHeight - defaultSealWidth;
            }
            // 上边界
            if (this.top <= mixHeight) {
                this.top = mixHeight;
            }
            const y = 1 - (this.top + defaultSealWidth) / this.pageHeight;
            // 鼠标移动、释放，实时更新当前页面的骑缝章位置
            this.sealTop = `${this.top / this.pageHeight * 100}`;
            this.$emit('update-top', {
                y,
            });
            // 移动结束 更新
            if (isEnd) {
                // opz SAAS-4811，合同有146页时，骑缝章拖动卡动
                // 只有在鼠标释放的时候，才会去同步其他页面的骑缝章
                this.$emit('update-top', {
                    top: this.top / this.pageHeight,
                });
                this.updateToServer();
            }
        },
        handleSwitch() {
            this.$emit('switch-riding-seal');
        },
        handleWindowMouseup() {
            if (this.isMoveing) {
                this.isMoveing = false;
                // 快速移动的时候，鼠标移除目标，通过window的鼠标释放事件去更新位置到server
                if (this.startY !== this.ridingSeal.y) {
                    this.$emit('update-top', {
                        top: this.top / this.pageHeight,
                    });
                    this.updateToServer();
                }
            }
        },
        updateToServer() {
            this.$emit('update-server');
        },
    },
    mounted() {
        const { ridingSeal } = this;
        // 已盖骑缝章初始化
        if (ridingSeal.y || ridingSeal.y === 0) {
            this.top = this.pageHeight * (1 - ridingSeal.y) - defaultSealWidth;
            this.$emit('update-top', {
                top: this.top / this.pageHeight,
            });
            return;
        }
        // 未盖骑缝章初始化
        this.top = (this.pageHeight - defaultSealWidth) / 2;
        this.$emit('update-top', {
            top: this.top / this.pageHeight,
            y: 1 - (this.top + defaultSealWidth) / this.pageHeight,
        });
    },
};
</script>

<style lang="scss">
.riding-seal {
    width: 158px;
    position: absolute;
    right: -110px;
    top: 0;
    border: 1px dashed #127fd2;
    height: 100%;
    background: rgba(18,127,210,0.05);
    transform-origin: 0 0;
    &-bg {
        position: absolute;
        right: 0;
        width: 110px;
        height: 100%;
        background: #FAFAFA;
        background-image: linear-gradient(270deg, rgba(255,255,255,0.50) 0%, rgba(217,217,217,0.50) 100%);
        background-size: 22px;
    }
    &-img {
        position: absolute;
        top: 0;
        left: 0;
        background-size: 100% 100%;
        background-position: center;
        cursor: move;
        p {
            position: absolute;
            bottom: -40px;
            left: 50%;
            transform: translateX(-50%);
            background: #127FD2;
            border-radius: 2px;
            width: 80px;
            height: 30px;
            line-height: 30px;
            color: #ffffff;
            text-align: center;
            font-size: 14px;
            display: none;
            cursor: pointer;
            &.top {
                bottom: 175px;
            }
        }
        &_hover {
            p {
                display: block;
            }
        }
    }
}

</style>
