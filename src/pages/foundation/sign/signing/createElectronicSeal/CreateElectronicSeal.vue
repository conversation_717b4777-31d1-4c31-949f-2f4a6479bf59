<template>
    <div
        v-if="createElectronicSealShow"
        class="CreateElectronicSeal"
        :class="{'isPC' : isPC, 'isPhone' : !isPC}"
    >
        <template v-if="!nameConflict.visible">
            <div class="bg"></div>
            <div class="pop-con">
                <div class="top-con">
                    <span class="title">{{ $t('sign.electronicSeal') }}</span>
                    <i class="el-icon-ssq-guanbi" @click="handleClose"></i>
                </div>
                <div class="bottom-con">
                    <template v-if="sealImg.length">
                        <el-carousel v-if="sealImg.length > 1"
                            ref="carousel"
                            class="carousel"
                            height="210px"
                            :autoplay="false"
                            trigger="click"
                            arrow="never"
                            indicator-position="none"
                        >
                            <button class="arrow arrow-left" @click="go('prev')"><i class="el-icon-ssq-youjiantou1"></i></button>
                            <button class="arrow arrow-right" @click="go('next')"><i class="el-icon-ssq-youjiantou1"></i></button>
                            <el-carousel-item>
                                <img :src="sealSrc[0]" class="seal">
                            </el-carousel-item>
                            <el-carousel-item>
                                <img :src="sealSrc[1]" class="seal">
                            </el-carousel-item>
                        </el-carousel>
                        <img v-else :src="sealSrc[0]" class="seal">
                    </template>
                    <p
                        v-if="isPC"
                        class="title"
                    >
                        {{ $t('signPC.toAddSealWithConsole') }}
                    </p>
                    <p
                        v-else
                        class="title"
                    >
                        {{ $t('sign.loginToAppendSeal') }}
                    </p>
                    <div class="btn-con">
                        <div class="btn-center">
                            <p
                                class="btn btn-type-one"
                                @click="saveSeal"
                                v-if="!getIsForeignVersion"
                            >
                                {{ $t('signPC.use') }}
                            </p>
                            <p
                                v-if=" isPC && (channel !== 'notice' || (channel == 'notice' && hasRegister))"
                                class="btn btn-type-four"
                                @click="goEntConsole"
                            >
                                {{ $t('signPC.toAddSeal') }}
                            </p>
                            <p
                                v-if="!isPC"
                                class="btn-type-two"
                                @click="handleClose"
                            >
                                {{ $t('signPC.cancel') }}
                            </p>
                        </div>
                    </div>
                </div>

            </div>
        </template>
        <el-dialog
            title="印章更新"
            v-model="nameConflict.visible"
            class="name-conflict-dialog"
            width="40px"
            :modal-append-to-body="true"
            @confirm="handleConflictConfirm"
        >
            <p>检测到您企业信息变更了，是否需要更换原同名印章的样式？请选择对应的样式：</p>
            <el-radio-group v-model="nameConflict.radio">
                <el-row justify="center">
                    <el-col :span="12">
                        <img class="seal" alt="" :src="nameConflict.oldSealImg">
                        <el-radio :label="1">旧印章样式</el-radio>
                    </el-col>
                    <el-col :span="12">
                        <img class="seal" alt="" :src="sealSrc.length > 1 ? sealSrc[(this.$refs.carousel && this.$refs.carousel.activeIndex) || 0] : sealSrc[0]">
                        <el-radio :label="2">新印章样式</el-radio>
                    </el-col>
                </el-row>
            </el-radio-group>
            <div slot="footer" class="dialog-footer">
                <el-button @click="nameConflict.visible = false">取 消</el-button>
                <el-button type="primary" @click="handleConflictConfirm">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import { isPC } from 'src/common/utils/device.js';
import { mapGetters } from 'vuex';

export default {
    props: {
        'hasRegister': {
            type: Boolean,
            default: false,
        },
        'createElectronicSealShow': {
            type: Boolean,
            default: false,
        },
        'channel': {
            type: String,
            default: '',
        },
        // wap签署人拖章
        signAtWill: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            isPC: isPC(),
            sealImg: [],
            sealSrc: [],
            sealId: '',
            nameConflict: {
                visible: false,
                oldSealImg: '',
                radio: 2,
            },
        };
    },
    computed: {
        ...mapGetters(['getIsForeignVersion']),
    },
    watch: {
        createElectronicSealShow(val) {
            if (val && !this.getIsForeignVersion) {
                this.getSeal();
            }
        },
    },
    methods: {
        getSeal: function() {
            const autoSeal = this.$http.put('/ents/seals/autoSeal', { name: '电子公章' });
            let rusSeal;
            if (this.$cookie.get('sino-russia') === '1' && this.isPC) { // 设置俄文且为pc端
                rusSeal = this.$http.get('/ents/seals/rus');
            }
            Promise.all([autoSeal, rusSeal]).then(res => {
                for (let i = 0; i < res.length; i++) {
                    if (res[i] && res[i].data) {
                        this.sealImg.push(res[i].data.value);
                        this.sealSrc.push(`data:image/jpeg;base64,${res[i].data.value}`);
                    }
                }
            });
        },
        saveSeal: function() {
            this.$http.post('/ents/seals/autoSeal', {
                file: this.sealImg.length > 1 ? this.sealImg[this.$refs.carousel.activeIndex] : this.sealImg[0],
                name: '电子公章',
                type: 3,
                allocateToSelf: true,
            }, { noToast: 1 })
                .then(res => {
                    // wap签署人拖章
                    if (this.signAtWill) {
                        this.$parent.$parent.handleCreateLabel('SEAL', res.data.value);
                        this.$parent.$parent.$refs.AppBottom.getSealList();
                        this.$emit('close');
                    } else {
                        this.sealId = res.data.value;
                        this.$emit('createElectronicSealDone', {
                            sealId: res.data.value,
                        });
                    }
                }).catch(e => {
                    const { code, data, message } = e.response.data;
                    if (code === '120261') {
                        // SAAS-26060
                        this.nameConflict = {
                            visible: true,
                            oldSealImg: `/ents/${data.sealId}/seal/${data.fileId}?access_token=${this.$cookie.get('access_token')}`,
                            radio: 2,
                            oldSealId: data.sealId,
                        };
                    } else {
                        this.$MessageToast.error(message);
                    }
                });
        },
        handleConflictConfirm() {
            const { radio, oldSealId } = this.nameConflict;
            const formData = new FormData();
            let opt = {
                oldSealId,
                fileStr: null,
                file: null,
                useNewSeal: false,
            };
            if (radio === 2) {
                opt = {
                    ...opt,
                    fileStr: this.sealImg.length > 1 ? this.sealImg[this.$refs.carousel.activeIndex] : this.sealImg[0],
                    file: null,
                    useNewSeal: true,
                };
            }
            Object.keys(opt).map(key => {
                formData.append(key, opt[key]);
            });
            this.$http.post('/ents/seals/updateDuplicateSeal', formData).then((res) => {
                this.$emit('createElectronicSealDone', {
                    sealId: res.data.value,
                });
            });
        },
        goEntConsole: function() {
            window.open('/console/enterprise/seal'); // 企业控制台——添加印章
        },
        handleClose() {
            this.$emit('update:createElectronicSealShow', !this.createElectronicSealShow);
        },
        go(type) { // 印章切换 prev,next
            if (type === 'prev') {
                this.$refs.carousel.prev();
            } else {
                this.$refs.carousel.next();
            }
        },
    },
    created: function() {

    },
};
</script>
<style lang="scss">
	.CreateElectronicSeal {
		width: 100%;
		height: 100%;
		z-index: 2000;
		.bg {
			z-index: 2000;
			position: fixed;
			left: 0;
			top: 0;
			width: 100%;
			height: 100%;
			opacity: .5;
			background: #000;
		}
		.pop-con {
			z-index: 2001;
			position: relative;
			top: 50%;
			left: 50%;
			transform: translateX(-50%) translateY(-50%);
			width: 400px;
			background-color: #fff;
            [dir="rtl"] & {
                right: 50%;
                transform: translateX(50%) translateY(-50%);
            }
			.top-con {
				height: 50px;
				line-height: 50px;
				padding: 0 30px;
				font-size: 16px;
                position: relative;
				i {
					position: absolute;
					right: 30px;
                    top: 14px;
					color: #ddd;
                    font-size: 14px;
                    cursor: pointer;
                    [dir="rtl"] & {
                        left: 30px;
                    }
                    &:hover{
                        color: #999;
                    }
				}
			}
			.bottom-con {
                padding-bottom: 20px;
				border-top: 1px solid #e0e0e0;
				.title {
					padding-top: 20px;
					margin: 0 30px;
					color: #333;
					font-size: 14px;
					text-align: center;
				}
				img {
					display: block;
					width: 190px;
					height: 190px;
					margin: 15px auto 0;
				}
			}
			.btn-con {
				margin-top: 13px;
				text-align: center;
				.btn-center {
					display: inline-block;
					p {
						font-size: 14px;
					}
					p:nth-child(2) {
						margin-left: 15px;
					}
				}
				.btn {
					display: inline-block;
					width: 125px;
					height: 35px;
					line-height: 35px;
				}
			}
		}
        .carousel{
            .arrow{
                height: 30px;
                width: 30px;
                cursor: pointer;
                color: #ccc;
                position: absolute;
                top: 50%;
                z-index: 10;
                transform: translateY(-50%);
                text-align: center;
                font-size: 26px;
                outline: none;
                border-radius: 50%;
                border: 0;
                background: none;
                &.arrow-left{
                    left:40px;
                    transform: translateY(-50%) rotateY(180deg);
                }
                &.arrow-right{
                    right:40px;
                }
                &:hover{
                    color: #127fd2;
                }
            }
        }

		&.isPhone {
			.pop-con {
				width: 310px;
				height: 360px;
				border-radius: 2px;
				.top-con {
					height: 45px;
					line-height: 45px;
					i {
						display: none;
					}
				}
				.bottom-con {
					height: 315px;
					border-bottom-left-radius: 2px;
					border-bottom-right-radius: 2px;
					.title {
						padding-top: 23px;
						margin-left: 15px;
						margin-right: 15px;
						color: #333;
						font-size: 12px;
					}
					img {
						display: block;
						width: 155px;
						height: 155px;
						margin: 20px auto 0;
					}
				}
				.btn-con {
					height: 46px;
					line-height: 46px;
					margin-top: 20px;
					.btn-center {
						&{
							width: 100%;
						}
						p {
							display: inline-block;
							width: 125px;
							height: 35px;
							line-height: 35px;
							font-size: 15px;
							text-align: center;
							vertical-align: middle;
						}
					}
				}
			}
		}
	}
.name-conflict-dialog {
	.el-dialog {
		width: 400px;
	}
	.el-dialog__body {
		padding: 10px 20px !important;
	}
    img {
        width: 100px;
        height: 100px;
        display: block;
        margin: 0 auto;
    }
    p {
        margin: 20px 0;
        text-indent: 20px;
        line-height: 1.6;
    }
	.el-radio-group {
		display: block;
	}
    .el-radio {
        margin: 10px auto;
    }
    .el-col {
        display: flex;
        flex-direction: column;
    }
}
</style>
