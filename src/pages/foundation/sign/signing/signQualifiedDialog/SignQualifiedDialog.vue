<!--
根据signDecision字段，来判断实名相关的判断，引导弹窗
PC签署、老的移动端签署流程 都有使用到
-->
<template>
    <div id="signQualifiedDialog">
        <div v-if="visible" class="qualified-dialog-wrap">
            <DialogApplyJoinEnt
                v-if="signerIdentityStatus == 6"
                :subjectName="subjectName"
                :entAdminName="receiver.entAdminName"
                :entAdminAccount="receiver.entAdminAccount"
                :appliedEntId="contractData.sendUserEnterpriseId"
                :applyUserId="receiver.userId"
                :account="receiver.userAccount"
                :contractAliasText="contractAliasText"
                @open="askSignVis = false"
                @close="handleClose"
            >
            </DialogApplyJoinEnt>

            <!--签约条件检测相关弹窗-->
            <el-dialog
                v-else
                class="sign-qualified-dialog"
                top="25%"
                :visible.sync="dialogVisible"
                :title="$t('signTip.tips')"
                @open="askSignVis = false"
                @close="handleClose"
            >
                <AskSign
                    v-if="askSignVis"
                    :entName="subjectName"
                    @close="askSignVis = false"
                ></AskSign>
                <div v-if="!askSignVis">
                    <div class="messageBox">
                        <!-- 实名不满足 -->
                        <template
                            v-if="
                                signerIdentityStatus === 2 ||
                                    signerIdentityStatus === 3 ||
                                    signerIdentityStatus === 4 ||
                                    signerIdentityStatus === 10
                            "
                        >
                            <p>
                                {{ $t('sign.senderRequire') }}<label v-html="personText"></label>{{ authText }}
                            </p>
                            <p class="message-note">{{ noteText }}</p>
                        </template>

                        <p v-else-if="signerIdentityStatus == 5">
                            {{ $t('sign.suggestToAuth') }}
                        </p>

                        <div
                            v-else-if="signerIdentityStatus == 6 || noticeContentShow"
                            style="line-height: 20px"
                        >
                            <span>{{ $t('sign.alreadyExists') }}&nbsp;<span class="strong">{{
                                subjectName
                            }}</span>,</span>
                            <span>{{ $t('sign.contactEntAdmin')
                                  }}<span class="strong">{{ receiver.entAdminName }}-{{
                                      receiver.entAdminAccount
                                  }}</span>{{ $t('sign.setYourAccount') }}}
                                <span class="strong">{{ receiver.userAccount }}</span>
                                {{ $t('sign.addToEnt') }}。</span>
                        </div>
                        <div v-else-if="signerIdentityStatus == 7 || signerIdentityStatus == 11">
                            <p>
                                {{ $t('sign.senderRequire') }}<label v-html="personText"></label>{{ $t(isAuth ? 'sign.authInfoNoSame' : 'sign.authInfoNoSame2') }}
                            </p>
                            <div class="intercept-container-reAuth">
                                <p class="intercept-container-tips1">{{ $t(isAuth ? 'sign.authInfo': 'sign.authInfo2') }}<strong>{{ isAuth ? realName : commonHeaderInfo.platformUser.fullName }}</strong></p>
                                <p class="intercept-container-tips2" v-if="isAuth">{{ $t('sign.in') }}{{ authTime }}{{ $t('sign.finishAuth') }}</p>
                                <br>
                                <p>{{ $t('sign.ask') }}</p>
                                <div class="message-reAuth-wrapper">
                                    <el-button class="message-reAuth-btn" type="primary" @click="goToReAuth">{{ $t('sign.reAuthBtnText') }}</el-button>
                                    <el-button class="message-reAuth-btn" @click="goToRemind">{{ buttonCopyWriting?buttonCopyWriting:$t('sign.changePhoneText') }}</el-button>
                                </div>
                            </div>
                            <p class="message-note">{{ noteText }}</p>
                        </div>
                        <div v-else-if="signerIdentityStatus === 12">
                            <p>{{ $t('sign.senderRequireUseFollowIdentity') }}</p>
                            <br>
                            <p v-for="(item,index) in receiver.multiEmployeeSignaturesVerifyConfig.employeeRealNameAuthConfigList" :key="index">
                                <label>{{ item.employeeRealName }}</label><label>{{ item.idNumber ? `: ${getMaskIdCard(item.idNumber)}` : '' }}</label>
                            </p>
                            <br>
                            <p>{{ $t('sign.authInfoUnMatchNeedResend') }}</p>
                            <p class="message-note">{{ noteText }}</p>
                        </div>
                        <div v-else-if="signerIdentityStatus == 8">
                            <p>#{{ senderName }}#{{ $t('sign.noEntNameNeedResend') }}。</p>
                        </div>
                        <div v-if="signerIdentityStatus == 9">
                            <div class="operator-no-auth">
                                <i class="el-icon-ssq-hetongxiangqing-chexiao1"></i>
                                <p>
                                    {{ senderName }} {{ $t('sign.authGuide.tip_3') }}
                                    {{ contractData.contractTitle }}
                                </p>
                            </div>
                            <!--如果经办人姓名（entUserName）和身份证号（idNumberForVerify）都未填写-->
                            <div v-if="!receiver.idNumberForVerify && !receiver.entUserName" class="operator-no-auth-tip">
                                <p>{{ $t('sign.authGuide.new_tip_1') }}</p>
                            </div>
                            <!--如果经办人姓名（entUserName）和身份证号（idNumberForVerify）两者填写其一-->
                            <div v-else class="operator-no-auth-tip">
                                <p>{{ $t('sign.authGuide.new_tip_2') }}</p>
                                <p class="no-auth-tip-user-info">
                                    <span v-if="receiver.entUserName">{{ $t('sign.authGuide.entUserName' ) }}{{ maskEntUserName }}</span>
                                    <span v-if="receiver.idNumberForVerify">{{ $t('sign.authGuide.idNumberForVerify' ) }}{{ maskIdNumberForVerify }}</span>
                                </p>
                                <p>{{ $t('sign.authGuide.new_tip_3') }}</p>
                            </div>
                            <div class="operator-no-auth-step">
                                <Steps :steps="stepsArr"></Steps>
                            </div>
                        </div>
                    </div>
                    <!-- 要求经办人实名但经办人没有实名时 -->
                    <span v-if="signerIdentityStatus === 9" class="btn-line-operator-no-auth-tip">{{ $t('sign.authGuide.new_tip_4') }}</span>
                    <div class="btn-line">
                        <!-- 主操作前面的按钮 -->
                        <!-- requestSomeone 请求他人认证 -->
                        <el-button
                            v-if="signerIdentityStatus == 2 && isEnt"
                            class="is-default"
                            @click.stop.prevent="askForSign"
                        >{{ $t('sign.requestSomeone') }}</el-button>
                        <el-button
                            v-else-if="
                                [3,10].includes(signerIdentityStatus) &&
                                    isEnt &&
                                    !commonHeaderInfo.hasAccountM
                            "
                            class="is-default"
                            @click.stop.prevent="notifyAdmin"
                        >{{ $t('sign.requestOthersToContinue') }}</el-button>
                        <el-button
                            v-else-if="signerIdentityStatus == 11 || signerIdentityStatus == 12"
                            class="is-default"
                            @click.stop.prevent="handleTransfer"
                        >{{ $t('sign.chooseTransferPerson') }}</el-button>

                        <!-- 主操作按钮 -->
                        <el-button type="primary" @click="handleMainOperate" class="verify-and-sign-main-btn">{{
                            buttonText
                        }}</el-button>
                        <!-- 主操作后面的按钮 -->
                        <el-button
                            v-if="suffixButtonText"
                            type="default"
                            class="cancel-btn"
                            @click.stop.prevent="handleClose"
                        >{{ suffixButtonText }}
                        </el-button>
                    </div>
                </div>
            </el-dialog>
            <ContractsTransferDialog v-if="transferDialogParams.show" :params="transferDialogParams" @close="transferDialogParams.show = false" />
        </div>
        <ReAuthDialog :visible.sync="reAuthDialogVisible" :infoData="reAuthDialogData" @confirm="reAuthHandle"></ReAuthDialog>
    </div>
</template>

<script>
import DialogApplyJoinEnt from 'src/components/dialogApplyJoinEnt/dialogApplyJoinEnt.vue';
import ContractsTransferDialog from 'components/contractsTransferDialog';
import AskSign from 'src/components/askSign/index.vue';
import { mapGetters } from 'vuex';
import { createEnterpriseMinxin } from 'src/mixins/index.js';
import { getMaskName, getMaskIdCard } from 'src/common/plugins/validation/validation-config.js';
import ReAuthDialog from 'components/reAuthDialog/index.vue';
import Steps from 'components/steps/Steps.vue';
import dayjs from 'dayjs';
import { getAuthChangeCopyWriting } from 'src/api/sign';
export default {
    name: 'SignQualifiedDialog',
    components: {
        DialogApplyJoinEnt,
        AskSign,
        ContractsTransferDialog,
        ReAuthDialog,
        Steps,
    },
    mixins: [createEnterpriseMinxin],
    props: [
        // eslint-disable-next-line vue/require-prop-types
        'visible', 'signDecision', 'receiver', 'contractData', 'isEnt', 'subjectName', 'contractAliasText', 'isNoPermissionPage',
    ],
    data() {
        return {
            channel: this.$route.query.channel || 'login', // 通过短信链接还是登录 notice login
            contractId: this.$route.query.contractId,
            authorizationVis: true,
            askSignVis: false,
            dialogVisible: false,
            askSignInput: {
                entName: '',
                account: '',
            },
            noticeContentShow: false,
            transferDialogParams: {},
            reAuthDialogVisible: false,
            authTime: '',
            realName: '',
            stepsArr: [
                this.$t('sign.authGuide.realNameAuth'),
                this.$t('sign.authGuide.applySeal'),
                this.$t('sign.authGuide.signContract'),
            ],
            reAuthDialogData: {}, // 实名不一致，重新实名弹窗内容
            buttonCopyWriting: '',
            popCopyWriting: '',
        };
    },
    computed: {
        ...mapGetters(['getIsLoginFromDeveloper', 'isAuth']),
        commonHeaderInfo: function() {
            return this.$store.state.commonHeaderInfo;
        },
        inputName() {
            const { inputEnterpriseName, inputUserName } = this.receiver;
            return this.isEnt ? inputEnterpriseName : inputUserName;
        },
        maskInputName() {
            const { receiver } = this;
            if (receiver.inputUserName) {
                return getMaskName(receiver.inputUserName);
            }
            return '';
        },
        maskEntUserName() {
            const { receiver } = this;
            return getMaskName(receiver.entUserName);
        },
        maskIdNumberForVerify() {
            const { receiver } = this;
            return getMaskIdCard(receiver.idNumberForVerify);
        },
        personText() {
            const { inputName, receiver } = this;
            if (this.isEnt) {
                return `${this.$t(
                    'sign.inTheName',
                )}<span> ${inputName} </span>${this.$t('sign.of')}${this.$t(
                    'sign.identity',
                )}`;
            }
            const [m, n] = [
                `${this.$t('sign.nameIs')}<span> ${getMaskName(inputName)} </span>`,
                `${this.$t('sign.IDNumIs')}<span> ${
                    getMaskIdCard(receiver.idNumberForVerify)
                } </span>`,
            ];
            let message = '';
            if (inputName && receiver.idNumberForVerify) {
                message = `${this.$t('sign.inTheName')}${m}，${n}`;
            } else if (inputName && !receiver.idNumberForVerify) {
                message = `${this.$t('sign.inTheName')}${m}`;
            } else if (!inputName && receiver.idNumberForVerify) {
                message = `${this.$t('sign.inTheName')}${n}`;
            } else {
                message = '';
            }
            return message;
        },
        // leadToAuthBeforeSign 继续认证后可签署合同
        authText() {
            return this.signerIdentityStatus === 3
                ? `${this.$t('sign.provideMoreAuthData')}：${
                    this.receiver.authRequirement
                }，${this.$t('sign.leadToAuthBeforeSign')}。`
                : `${this.$t('sign.goOnAuth.2')}${
                    this.signerIdentityStatus === 10
                        ? this.$t('sign.groupProxyAuthNeedMore')
                        : this.$t('sign.signContractAfterAuth.1')
                }` + this.$t('sign.contactSender');
        },
        // 是否是企业多人签字
        isMultiSignatures() {
            return this.receiver.multiEmployeeSignaturesVerifyConfig && this.receiver.multiEmployeeSignaturesVerifyConfig.useMultiEmployeeSignatures;
        },
        // 签署人身份状态 根据状态显示不同弹窗
        signerIdentityStatus() {
            if (this.isEnt && !this.receiver.inputEnterpriseName) {
                // 如果企业名称没有填写，提示不能签署
                return 8;
            }
            const signDecisionStatusMap = {
                UNREGISTERED: 2,
                NORMAL: 1, // 满足签署条件
                NOT_AUTHENTICATE: 2, // 需要实名但未实名认证
                SIMPLE_AUTHENTICATE: 2, // 可以进行简单（刷脸）实名，但未实名
                MORE_AUTHENTICATE: 3, // 需要补充实名,在实名流程中走签署引导
                USE_MY_IDENTITY: this.isEnt ? 4 : 7, // 已实名，主体不一致, 新的key,
                NOT_MATCH_AUTHENTICATE: this.isEnt ? 4 : 7, // 主体不一致
                USE_DEFAULT_IDENTITY: 5, // 未实名使用默认'未实名'身份签署
                NOT_ADD: 6, // 不是发起方指定的签约主体公司成员
                ENTER_NOT_AUTHENTICATE: 9, // 企业 经办人需要实名
                MORE_AUTHENTICATE_WITH_PROXY_AUTH: 10, // 集团代认证
                NOT_MATCH_ENTER_AUTHENTICATE: 11, // 经办人已实名但是不匹配
                NOT_MATCH_MULTI_EMPLOYEE_SIGNATURE_AUTHENTICATE: 12, // 多人签字情况下，经办人已实名但和所有符合项均不匹配
            };
            return signDecisionStatusMap[this.signDecision];
        },
        buttonText() {
            let text = null;
            if (this.noticeContentShow) {
                text = this.$t('sign.noticeAdmin');
            }
            switch (this.signerIdentityStatus) {
                case 2:
                case 4:
                case 9:
                case 10:
                    text = this.$t('sign.goToVerify'); // 去实名认证
                    break;
                case 3:
                    text = this.$t('sign.continueVeri'); // 继续认证
                    break;
                case 5:
                    text = this.$t('sign.goToVerifyEnt');
                    break;
                case 6:
                    text = this.$t('sign.applyJoin');
                    break;
                case 7:
                case 8:
                case 11:
                case 12:
                    text = this.$t('sign.readLetter');
                    break;
            }
            return text;
        },
        // 继续签署
        suffixButtonText() {
            return this.signerIdentityStatus === 5 ? this.$t('sign.signAgain') : '';
        },
        noteText() {
            return `${this.$t('sign.note')}${
                this.isEnt ? this.$t('sign.entName') : this.$t('sign.identityInfo')
            }${this.$t('sign.signNeedCoincidenceInfo')}`;
        },
        // 发送人的名字
        senderName() {
            const { contractData } = this;
            return contractData.sendUserEnterpriseId
                ? contractData.sendUserEnterpriseName
                : contractData.sendUserName;
        },
    },
    watch: {
        visible(value) {
            this.dialogVisible = value;
        },
    },
    methods: {
        getMaskIdCard,
        // SAAS-12513
        notifyAdmin() {
            // request remote to notify the the admin person
            this.$http
                .post(`/ents/auth/notice-admin-todo/${this.contractId}`)
                .then(() => {
                    this.$MessageToast.success(
                        this.$t('sign.requestOthersToContinueSucceed'),
                    );
                })
                .catch(({ data: { message } }) => {
                    console.log(message);
                });
        },
        getAuthInfo() {
            this.$http.get('/users/auth/passed-simple-info')
                .then((res) => {
                    this.authTime = dayjs(res.data.authPassTime).format('YYYY年MM月DD日 HH:mm');
                    this.realName = res.data.realName;
                })
                .catch(() => {});
        },
        goToRemind() {
            this.handleClickEventTrack(2);
            let msg = this.$t('sign.changePhoneTip1') + this.senderName + this.$t('sign.changePhoneTip2');
            if (this.popCopyWriting) {
                msg = this.popCopyWriting;
            }
            this.$alert(msg, '', { confirmButtonText: '确定' });
        },
        handleMainOperate() {
            // SAAS-12513
            if ([3, 10].includes(this.signerIdentityStatus)) {
                this.handleClickEventTrack(this.signerIdentityStatus);
                // 补充实名认证，查询用户是否具有账号管理权限
                if (this.isEnt && !this.commonHeaderInfo.hasAccountM) {
                    // notify remote
                    this.$http
                        .post('/ents/auth/notice-admin-acknowledge')
                        .catch(({ data: { message } }) => {
                            console.log(message);
                        });
                }
                this.goToAuth();
            } else if (
                this.signerIdentityStatus === 5 ||
                this.signerIdentityStatus === 2
            ) {
                return this.goToAuth();
            } else if (this.signerIdentityStatus === 4) {
                return this.switchIdentityAndAuth().then(() => {
                    this.goToAuth();
                });
            } else if (this.signerIdentityStatus === 6 || this.noticeContentShow) {
                this.postApplyForJoin();
            } else if (this.signerIdentityStatus === 9) {
                this.goToPersonAuth();
            }
            return this.handleClose();
        },
        goToAuth() {
            // CFD-20114: 后端手动清空inputUserName时，第二次走这个逻辑会获取到浏览器缓存的designatedIdentityData，这里进行清空
            if (!this.isEnt && (this.inputName || this.receiver.idNumberForVerify)) {
                this.$localStorage.set(
                    'designatedIdentityData',
                    JSON.stringify({
                        name: this.inputName || '',
                        idCard: this.receiver.idNumberForVerify || '',
                        contractId: this.contractId,
                    }),
                );
            } else {
                this.$localStorage.remove('designatedIdentityData');
            }
            this.$emit('to-auth');
        },
        // 请求他人认证
        askForSign() {
            // 加nextTick是因为在父组件调用此方法时渲染不是实时的
            this.$nextTick(() => {
                this.askSignVis = true;
                this.askSignInput.entName = this.subjectName;
            });
        },
        // 给管理员发通知
        postApplyForJoin() {
            return this.$http
                .post(
                    `${signPath}/contracts/${this.contractId}/receivers/${this.receiver.receiverId}/apply-for-join`,
                )
                .then(() => {
                    this.handleClose();
                    this.$MessageToast.success(this.$t('sign.successfulSent'));
                })
                .catch(() => {});
        },
        sendNoticeToAdmin() {
            this.$nextTick(() => {
                this.noticeContentShow = true;
            });
        },
        handleClose() {
            if (this.signerIdentityStatus === 5) {
                this.$emit('use-default-identity');
            }
            this.askSignInput.account = '';
            this.$emit('update:visible', false);
        },
        async goToPersonAuth() {
            this.$emit('to-auth', true);
        },
        handleTransfer() {
            this.transferDialogParams = {
                show: true,
                selectedContracts: [this.contractId],
                callback: () => {
                    this.$MessageToast.success(this.$t('docContentTable.transferSucess'))
                        .then(() => {
                            this.$router.push('/doc/list');
                        });
                },
            };
        },
        goToReAuth() {
            this.handleClickEventTrack(1);
            this.dialogVisible = false;
            this.reAuthDialogData = {
                'inputUserName': this.inputName, // 个人
                'letterSender': this.senderName,
            };
            this.reAuthDialogVisible = true;
        },
        reAuthHandle() {
            this.$http.post('/users/auth/restart')
                .then(() => {
                    this.goToPersonAuth();
                    this.$sensors.track({
                        eventName: 'Ent_ContractSignWindow_Result',
                        eventProperty: {
                            page_name: '合同签署页',
                            window_name: '个人实名与指定实名不一致',
                            is_success: true,
                            icon_name: '是的，我要驳回实名',
                            request_url: `/users/auth/restart`,
                            contract_id: this.contractId,
                        },
                    });
                }).catch((err) => {
                    const errMsg = err.response?.data?.message;
                    const status = err.response?.status;
                    const code = err.response?.data?.code;
                    this.$sensors.track({
                        eventName: 'Ent_ContractSignWindow_Result',
                        eventProperty: {
                            page_name: '合同移交页',
                            window_name: '个人实名与指定实名不一致',
                            is_success: false,
                            icon_name: '是的，我要驳回实名',
                            fail_http_code: status,
                            fail_error_code: code,
                            fail_reason: errMsg,
                            request_url: `/users/auth/restart`,
                            contract_id: this.contractId,
                        },
                    });
                });
        },
        async getAuthNoSameText() {
            try {
                const { data: { buttonCopyWriting = '', popCopyWriting = '' } } = await getAuthChangeCopyWriting(this.contractId);
                this.buttonCopyWriting = buttonCopyWriting;
                this.popCopyWriting = popCopyWriting;
            } catch (e) {
                this.buttonCopyWriting = '';
                this.popCopyWriting = '';
            }
        },
        handleClickEventTrack(index) {
            const eventMap = {
                1: '是的，我要用本账号继续实名签署',
                2: '不是，联系发件方更改手机号',
                3: '继续认证',
                10: '去实名认证',
            };
            [3, 10, 7].includes(this.signerIdentityStatus) && this.$sensors.track({
                eventName: 'Ent_ContractSignWindow_BtnClick',
                eventProperty: {
                    page_name: '合同签署页',
                    window_name: [3, 10].includes(this.signerIdentityStatus) ? '需要补充实名' : (!this.isEnt ? '个人实名与指定实名不一致' : null),
                    icon_name: eventMap[index],
                    contract_id: this.contractId,
                },
            });
        },
    },
    async created() {
        if (!this.isNoPermissionPage) {
            await this.getAuthNoSameText();
            this.getAuthInfo();
        }
    },
};
</script>

<style lang="scss">
.verify-and-sign-main-btn {
  margin-top: 10px;
}
.qualified-dialog-wrap {
  .sign-qualified-dialog {
    display: flex;
    align-items: center;
    .operator-no-auth{
      p{
        display: inline-block;
      }
    }
    .operator-no-auth-tip{
      margin-top: 10px;
      color: #333;
      .no-auth-tip-user-info{
        display: flex;
        justify-content: space-between;
      }
    }
    .operator-no-auth-step{
      margin-top: 20px;
    }
    .el-dialog {
      max-width: 483px;
      border-radius: 4px;
      overflow: hidden;
      .el-dialog__headerbtn {
        font-size: 12px;
      }
    }
    @media (max-width: 768px) {
      .el-dialog {
        width: 90%;
        border-radius: 10px !important;
      }
    }
    .el-dialog__header {
      height: 44px;
      border-bottom: 1px solid #eee;
      padding-bottom: 0;
    }
    .el-dialog__body {
      overflow: hidden;
      padding: 23px 30px 30px;
    }
    p {
      font-size: 14px;
      line-height: 20px;
      word-break: break-all;
      span {
        color: #000000;
      }
    }

    .messageBox {
      // padding: 10px 22px;
      line-height: 18px;
      color: #666;
      &-7 {
        background: transparent;
        padding: 0;
      }

      .el-icon-ssq-tishi1 {
        margin-left: -10px;
        margin-right: 5px;
        font-size: 18px;
        vertical-align: top;
        color: #f86b26;
      }
      .strong {
        font-size: 14px;
        color: #333;
        font-weight: bold;
      }
      .highLight {
        margin-left: 5px;
        margin-right: 5px;
        color: #3d6786;
      }
    }

    .subject-name-tip {
      color: #666;
      font-size: 12px;
    }

    .subject-name {
      margin: 15px 0;
      color: #333;
      font-size: 16px;
      font-weight: bold;
    }
    .input-label {
      font-size: 12px;
      display: inline-block;
      width: 75px;
    }
    .message-input {
      flex: 1;
    }
    .ent-auth-btn {
      margin-top: 40px;
      overflow: hidden;
      float: right;
    }
    .btn-line-operator-no-auth-tip{
      font-size: 12px;
      color: #999;
      position: absolute;
      bottom: 36px;
    }
    .btn-line {
      margin-top: 40px;
      clear: both;
      overflow: hidden;
      float: right;
      .el-button--primary {
        padding: 0 16px;
      }
      .cancel-btn {
        margin-left: 10px;
        &:hover {
          color: #333;
          border-color: #ccc;
        }
      }
    }
    .el-button {
      height: 32px;
      line-height: 32px;
      padding: 0 16px;
      font-size: 14px;
    }
    .el-button.is-default {
      color: #108ee8;
      border-color: #108ee8;
    }
    .el-button.ml {
      margin-left: 25px;
    }
    .el-button--primary {
      background-color: #108ee8;
      border-color: #108ee8;
    }
    .message-note {
      margin-top: 20px;
    }
    &_mobile {
      .el-dialog {
        border-radius: 10px;
      }
    }
  }
  .message-reAuth {
    margin-top: 20px;
    p {
      font-size: 12px;
    }
  }
  .message-reAuth-wrapper {
    text-align: center;
      .message-reAuth-btn {
        width: 300px;
        margin: 0 auto;
        cursor: pointer;
        font-size: 12px;
        margin-top: 20px;
        padding: 0 8px;
      }
  }
  .intercept-container-tips1 {
    margin-top: 20px;
  }
  .intercept-container-tips2 {
    font-size: 12px;
    color: #999;
  }
}
</style>
