<template>
    <el-dialog
        :visible.sync="visible"
        class="add-seal__dialog"
        :title="$t('addSealDialog.title')"
        size="tiny"
        :before-close="handleClose"
    >

        <template v-if="!addSealParam.seal">
            <p>{{ $t('addSealDialog.dec1') }}</p>
            <p>{{ $t('addSealDialog.dec2') }}</p>
            <div slot="footer">
                <el-button @click="handleClose">{{ $t('sign.cancel') }}</el-button>
                <el-button type="primary" @click="updateNewSeal">{{ $t('sign.continue') }}</el-button>
            </div>
        </template>
        <template v-else>
            <img :src="addSealParam.seal">
            <div slot="footer">
                <el-button @click="updateNewSeal">{{ $t('addSealDialog.updateNewSeal') }}</el-button>
                <el-button type="primary" @click="useTheSeal">{{ $t('signPC.use') }}</el-button>
            </div>
        </template>
    </el-dialog>

</template>

<script>
export default {

    props: {
        visible: {
            type: Boolean,
        },
        addSealParam: {
            type: Object,
            default: () => {},
        },
    },
    methods: {
        handleClose() {
            this.$emit('update:visible', false);
        },
        updateNewSeal() {
            this.addSealParam.updateEL.click();
            this.handleClose();
        },
        useTheSeal() {
            this.$emit('click-label', this.addSealParam);
            this.handleClose();
        },
    },
};
</script>
<style lang="scss">
 .add-seal__dialog{
        p{
                font-size: 12px;
                margin-bottom: 10px;
                line-height: 20px;
            }
            img{
                width: 80%;
                margin: 0 auto;
                display: block;
            }

    }
</style>
