// 印章ocr识别 与企业名字不一致dialog
<template>
    <div>
        <el-dialog
            :visible.sync="isErrorDialogShow"
            :title="$t('sealInconformityDialog.errorSeal.title')"
            append-to-body
            class="seal-ocr-error-dialog"
        >
            <template v-if="ocrEntData.recognitionEnterpriseName"> <!-- 签署时有一个印章不一致 -->
                <!-- 印章图片中的企业名称与企业不相符 -->
                <template v-if="!ocrEntData.entNameMatched">
                    <p>{{ $t('sealInconformityDialog.errorSeal.tip') }}</p>
                    <h5>{{ ocrEntData.recognitionEnterpriseName }}</h5>
                    <p>{{ $t('sealInconformityDialog.errorSeal.tip3') }}</p>
                    <h5>{{ ocrEntData.enterpriseName }}{{ $t('sealInconformityDialog.errorSeal.tip4') }}</h5>
                    <p>{{ $t('sealInconformityDialog.errorSeal.tip5') }}</p>
                    <p v-if="ocrEntData.testKeywords"> {{ $t('sealInconformityDialog.errorSeal.tip7',{keyWord:ocrEntData.testKeywords }) }}</p>
                </template>
                <template v-else-if="ocrEntData.testKeywords">
                    <p> {{ $t('sealInconformityDialog.errorSeal.tip8',{keyWord:ocrEntData.testKeywords }) }}</p>
                </template>
            </template>
            <template v-else> <!-- 签署时有多个印章不一致 -->
                <!-- 印章图片中的企业名称与企业不相符 -->
                <template v-if="!ocrEntData.entNameMatched">
                    <p>{{ $t('sealInconformityDialog.errorSeal.tip1') }}</p>
                    <h5>{{ ocrEntData.enterpriseName }}</h5>
                    <p>{{ $t('sealInconformityDialog.errorSeal.tip6') }}</p>
                    <p v-if="ocrEntData.testKeywords"> {{ $t('sealInconformityDialog.errorSeal.tip7',{keyWord:ocrEntData.testKeywords }) }}</p>
                </template>
                <template v-else-if="ocrEntData.testKeywords">
                    <p> {{ $t('sealInconformityDialog.errorSeal.tip8',{keyWord:ocrEntData.testKeywords }) }}</p>
                </template>
            </template>
            <p class="guide" @click="isExampleDialogShow = true">{{ $t('sealInconformityDialog.errorSeal.guide') }}</p>
            <span slot="footer" class="dialog-footer">
                <el-button @click="close">{{ $t('sealInconformityDialog.cancel') }}</el-button>
                <el-button type="primary" @click="goContinue">{{ $t('sealInconformityDialog.errorSeal.next') }}</el-button>
            </span>
        </el-dialog>
        <el-dialog
            :visible.sync="isExampleDialogShow"
            :title="$t('sealInconformityDialog.exampleSeal.title')"
            append-to-body
            class="seal-example-dialog"
        >
            <h5>{{ $t('sealInconformityDialog.exampleSeal.way1')[0] }}</h5>
            <el-row>
                <el-col :span="12">
                    <p>{{ $t('sealInconformityDialog.exampleSeal.way1')[1] }}</p>
                    <img src="~img/sealExample/way1_1.png" alt="" height="80px">
                </el-col>
                <el-col :span="12">
                    <p>{{ $t('sealInconformityDialog.exampleSeal.way1')[2] }}</p>
                    <img src="~img/sealExample/way1_2.png" alt="" height="80px">
                </el-col>
            </el-row>
            <h5>{{ $t('sealInconformityDialog.exampleSeal.way2')[0] }}</h5>
            <p>{{ $t('sealInconformityDialog.exampleSeal.way2')[1] }}</p>
            <el-row>
                <el-col :span="12">
                    <img src="~img/sealExample/way2_1.png" alt="" height="80px">
                </el-col>
                <el-col :span="12">
                    <img src="~img/sealExample/way2_2.png" alt="" height="80px">
                </el-col>
            </el-row>

            <h5>{{ $t('sealInconformityDialog.exampleSeal.errorWay')[0] }}</h5>
            <el-row :gutter="20">
                <el-col :span="8">
                    <p>{{ $t('sealInconformityDialog.exampleSeal.errorWay')[1] }}</p>
                    <img src="~img/sealExample/right.png" alt="" height="80px">
                </el-col>
                <el-col :span="8">
                    <p>{{ $t('sealInconformityDialog.exampleSeal.errorWay')[2] }}</p>
                    <img src="~img/sealExample/error.png" alt="" height="80px">
                </el-col>
                <el-col :span="8">
                    <p>{{ $t('sealInconformityDialog.exampleSeal.errorWay')[3] }}</p>
                    <img src="~img/sealExample/businessLinese.png" alt="" height="80px">
                </el-col>
            </el-row>
            <span slot="footer" class="dialog-footer">
                <el-button @click="isExampleDialogShow = false">{{ $t('sealInconformityDialog.cancel') }}</el-button>
                <el-button type="primary" @click="isExampleDialogShow = false">{{ $t('sealInconformityDialog.confirm') }}</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
export default {
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        ocrEntData: {
            default: () => ({}),
            type: Object,
        },
    },
    data() {
        return {
            isExampleDialogShow: false,
        };
    },
    computed: {
        isErrorDialogShow: {
            set(value) {
                this.$emit('input', value);
            },
            get() {
                return this.value;
            },
        },
    },
    methods: {
        close() {
            this.$emit('input', false);
        },
        goContinue() {
            this.close();
            this.$emit('confirm');
        },
    },
};
</script>
<style lang="scss">
$--color-primary: #127fd2 !default;
.seal-ocr-error-dialog {
    .el-dialog {
        position: relative;
        width: 500px;
        h5 {
            font-size: 14px;
            color:  #333333;
            font-weight: 500;
        }
        .guide {
            position: absolute;
            bottom: 50px;
            left: 30px;
            color: $--color-primary;
            cursor: pointer;
        }
    }
}
.seal-example-dialog {
    .el-dialog {
        width: 600px;
        h5 {
            font-size: 14px;
            color:  #333333;
            font-weight: 500;
            margin: 20px 0 15px 0;
        }
        p {
            font-size: 12px;
            color:  #333333;
            margin-bottom: 10px;
            font-weight: 400;
        }
    }
}
</style>
