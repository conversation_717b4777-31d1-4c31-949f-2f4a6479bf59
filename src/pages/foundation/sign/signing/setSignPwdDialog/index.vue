<!--设置签约密码弹窗-->
<template>
    <el-dialog
        :title="$t('sign.setSignPsw')"
        class="set-sign-pwd-dialog"
        :visible.sync="dialogVisible"
        :modal-append-to-body="false"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
    >
        <div>
            <div class="dialog-account">
                <div>{{ $t('sign.account') }}</div>
                <div class="dialog-account__account">{{ currentAccount }}</div>
                <span
                    class="dialog-account__btn"
                    v-if="ifShowChangeBtn"
                    @click="handleChangeVerifyMethod"
                >
                    {{ currentAccountType === 'S'? $t('setSignPwdDialog.changeEmailVerify'): $t('setSignPwdDialog.changePhoneVerify') }}
                </span>
            </div>
            <el-form @submit.native.prevent>
                <el-form-item :label="$t('sign.mailVerificationCode')" v-if="!noNeedPhoneVerify">
                    <div class="verify-flex">
                        <el-input
                            class="verify-input"
                            v-model="verifyCode"
                            type="number"
                            :maxlength="6"
                            :placeholder="$t('common.signPwdType')"
                        >
                            <ElIDelete slot="icon"></ElIDelete>
                        </el-input>
                        <CountDown class="countDown" :clickedFn="send" :disabled="countDownDisabled" ref="btn" :second="60"></CountDown>
                    </div>
                </el-form-item>
                <el-form-item :label="$t('sign.signPsw')">
                    <div class="verify-flex">
                        <el-input
                            :maxlength="6"
                            type="password"
                            auto-complete="new-password"
                            v-model="signPassword"
                            @input.native="signPassword = signPassword.replace(/^\.+|[^\d.]/g, '')"
                            :placeholder="$t('common.signPwdType')"
                        >
                            <ElIDelete slot="icon"></ElIDelete>
                        </el-input>
                    </div>
                </el-form-item>
            </el-form>
            <div class="dialog-tip">{{ $t('setSignPwdDialog.tip') }}</div>
        </div>
        <div slot="footer">
            <el-button @click="handleCancel">{{ $t('localCommon.cancel') }}</el-button>
            <el-button type="primary" @click="formSubmit">{{ $t('setSignPwdDialog.saveAndReturnSign') }}</el-button>
        </div>
    </el-dialog>
</template>
<script>
import resRules from 'src/common/utils/regs.js';
import ElIDelete from 'components/el_i_delete/ElIDelete.vue';
import CountDown from 'components/countDown/CountDown.vue';
import { mapGetters } from 'vuex';
export default {
    components: {
        ElIDelete,
        CountDown,
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
    },

    data() {
        return {
            verifyKey: '',
            verifyCode: '',
            signPassword: '',
            countDownDisabled: false,
            canReUsePhoneVerify: false,
            notifications: [],
            showChangePhoneVerify: false,
            changedAccount: '',
        };
    },
    computed: {
        ...mapGetters(['getUserAccount']),
        dialogVisible: {
            set(value) {
                this.$emit('update:visible', value);
            },
            get() {
                return this.visible;
            },
        },
        currentAccountType() {
            return resRules.userPhone.test(this.currentAccount) ? 'S' : 'E';
        },
        ifShowChangeBtn() {
            // 当前账号是邮箱，不展示切换按钮，当前是手机，展示切换按钮，切换为邮箱后还能切换回来手机号验证
            return (this.currentAccountType === 'S' && this.notifications.length === 2) || this.showChangePhoneVerify;
        },
        currentAccount() {
            return this.changedAccount || this.getUserAccount;
        },
        noNeedPhoneVerify() {
            return this.currentAccountType === 'S' && this.canReUsePhoneVerify;
        },
    },
    methods: {
        handleCancel() {
            this.dialogVisible = false;
            this.$emit('openSignVaDialog');
        },
        formSubmit() {
            if (!this.noNeedPhoneVerify && (!this.verifyKey || !this.verifyCode)) {
                const msg =  !this.verifyKey ? this.$t('sign.getVerCodeFirst') :  this.$t('sign.inputVerifyCodeTip');
                this.$MessageToast.error(msg);
            } else if (!this.signPassword || this.signPassword.length < 6) {
                const msg = !this.signPassword ? this.$t('sign.inputSignPwdTip') : this.$t('sign.signPwdType');
                this.$MessageToast.error(msg);
            } else {
                this.setSignPassword();
            }
        },
        setSignPassword() { // 更改签约密码
            this.$http.put(`/users/sign-pwd/setting`, {
                signPwd: this.signPassword,
                verifyKey: this.verifyKey,
                verifyCode: this.verifyCode,
                account: this.currentAccount,
            })
                .then(() => {
                    this.$emit('signPwdSetSuccess');
                    this.$MessageToast.success(this.$t('home.setSuccess'));
                });
        },
        // 发送成功，开始倒计时
        sended() {
            this.$refs.btn.run();
            this.countDownDisabled = false;
        },
        // 发送验证码
        send() {
            this.countDownDisabled = true;
            setTimeout(this.sended, 0);
            this.$http.sendVerCode({ // 不传target，
                code: 'B003',
                sendType: this.currentAccountType,
                target: this.currentAccount,
            })
                .then((res) => {
                    this.verifyKey = res.data.value;
                    this.$MessageToast.success(this.$t('sign.SentSuccessfully'));
                })
                .catch(() => {
                    this.$refs.btn.reset();
                });
        },
        getIfCanReUsePhoneVerify() {
            // 判断是否在10分钟内有短信验证码登录过，如果有则可以复用，不需要再次验证
            this.$http.get(`/users/captcha/exist-verify-code`)
                .then((res) => {
                    this.canReUsePhoneVerify = res.data;
                });
        },
        getNotifications() {
            this.$http.get('/users/detail').then((res) => {
                this.notifications = res.data.notifications;
            });
        },
        handleChangeVerifyMethod() {
            this.changedAccount = this.notifications.find((item) => {
                return item.type === (this.currentAccountType === 'S' ? 1 : 2);  // 1:邮箱 2:手机
            })?.code || '';
            // 切换为邮箱后，还能切换回来手机号验证的标识
            this.showChangePhoneVerify = true;
        },
    },
    created() {
        this.getIfCanReUsePhoneVerify();
        this.getNotifications();
    },
};
</script>
<style lang="scss">
.set-sign-pwd-dialog {
    .dialog-account {
        display: flex;
        height: 30px;
        line-height: 30px;
        margin-bottom: 20px;
        div:first-child {
            width: 80px;
            text-align: left;
            padding: 0 10px;
            box-sizing: border-box;
        }
        &__account {
            width: 150px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        &__btn {
            color: #127fd2;
            margin-left: 10px;
            cursor: pointer;
        }
    }
    .dialog-tip {
        color: #999;
        font-size: 12px;
        padding-left: 10px;
    }
    .el-form-item {
        &__label {
            display: inline-block;
            width: 80px;
            text-align: left;
            padding: 0 10px;
            height: 30px;
            line-height: 30px;
        }
        &__content {
            line-height: 30px;
        }
        .verify-flex {
            display: flex;
            .el-input__inner {
                height: 30px;
                line-height: 30px;
            }
            .countDown {
                position: absolute;
                top: 0;
                right: 0;
                width: 92px;
                height: 30px;
                line-height: 30px;
                border: 1px solid #ccc;
                border-radius: 1px;
                color: #127fd2;
            }
        }
    }
    .el-dialog {
        width: 400px;
        .el-dialog__header {
            padding: 14px 30px;
            border-bottom: 1px solid $border-color;
            .el-dialog__title {
                font-size: 16px;
                font-weight: normal;
                line-height: 22px;
            }
            .el-icon-close{
                color: #ddd;
                font-size: 14px;
                &:hover{
                    color: #999;
                }
            }
        }

        .el-dialog__body {
            padding: 20px 30px !important;
            background-color: #FFF !important;
        }
        .el-dialog__footer {
            padding: 0px 30px 30px;
            .el-button {
                width: auto;
                height: 30px;
                padding: 0 20px;
                font-size: 14px;
                border-radius: 2px;
            }
            .el-button--default {
                background-color: #F6F7F8;

                &:hover {
                    color: #333;
                    background-color: #FFF;
                    border-color: #ccc;
                }
            }
            .el-button--primary {
                background-color: #127fd2;
                border-color: #127fd2;

                &:hover {
                    color: #fff;
                    background-color: #1687dc;
                    border-color: #1687dc;
                }
            }
        }
    }
}
.en-page, .ja-font {
    .set-sign-pwd-dialog {
        .dialog-account {
            div:first-child {
                width: 135px;
            }
            div:last-child {
                width: 205px;
            }
        }
        .el-form-item__label {
            width: 135px;
        }
        .countDown {
            font-size: 9px;
        }
    }
}
</style>
