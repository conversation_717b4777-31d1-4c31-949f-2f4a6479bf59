<template>
    <el-dialog
        class="mgapprovenote__dialog"
        :visible.sync="visible"
        :before-close="close"
        :title="$t('sign.approvalFeatures.operateTitle')"
    >
        <el-input type="textarea" v-model="annotateContent" :maxlength="255" :placeholder="$t('sign.approvalFeatures.placeholder')"></el-input>
        <div slot="footer">
            <el-button @click="close">{{ $t('sign.cancel') }}</el-button>
            <el-button type="primary" @click="save">{{ $t('sign.submit') }}</el-button>
        </div>
    </el-dialog>
</template>

<script>
import { mapActions, mapState } from 'vuex';

export default {
    data() {
        return {
            cacheContent: '',
            annotateContent: '',
            visible: false,
        };
    },
    computed: {
        ...mapState('approval', ['currentEditAnnotateData', 'showEditDialog']),
    },
    watch: {
        showEditDialog(val) {
            this.visible = val;
        },
        currentEditAnnotateData: {
            handler(val) {
                this.annotateContent = this.cacheContent = val.annotationResultContent;
            },
            deep: true,
            immediate: true,
        },
    },
    methods: {
        ...mapActions('approval', ['saveAnnotation']),
        close() {
            this.$store.state.approval.showEditDialog = false;
            this.annotateContent = this.cacheContent;
        },
        save() {
            this.saveAnnotation({
                ...this.currentEditAnnotateData,
                annotationContent: this.annotateContent,
            }).then(res => {
                this.currentEditAnnotateData.annotationId = res.data.annotationId;
                this.currentEditAnnotateData.annotationResultContent = this.annotateContent;
                this.$store.state.approval.showEditDialog = false;
            });
        },
    },
};
</script>
