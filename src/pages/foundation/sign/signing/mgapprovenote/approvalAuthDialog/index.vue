<template>
    <el-dialog
        class="hubble-page__authorize"
        :visible.sync="visible"
        :before-close="handleClose"
        :title="$t('authorize.title')"
    >
        <p>{{ $t('authorize.content') }}</p>
        <a target="_blank" href="/哈勃产品使用须知.pdf" @click="sendSensor">{{ $t('authorize.contract') }}</a>
        <span slot="footer">
            <el-button @click="handleClose">{{ $t('authorize.cancel') }}</el-button>
            <el-button type="primary" @click="handleAuthorize">{{ $t('authorize.confirm') }}</el-button>
        </span>
    </el-dialog>
</template>

<script>
import { mapState } from 'vuex';
export default {
    props: {
        sensorsTrackContractInfo: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            visible: false,
        };
    },
    computed: {
        ...mapState('approval', ['showAuthDialog']),
    },
    watch: {
        showAuthDialog(val) {
            this.visible = val;
            if (val) {
                this.$sensors.track({
                    eventName: `Ent_ContractApprovalWindow_PopUp`,
                    eventProperty: {
                        page_name: '云平台_合同审批页弹窗_弹出',
                        contract_id: this.$route.query.contractId,
                        ...this.sensorsTrackContractInfo,
                        window_name: '授权协议',
                    },
                });
            } else {
                this.$sensors.track({
                    eventName: `Ent_ContractApprovalWindow_BtnClick`,
                    eventProperty: {
                        page_name: '云平台_合同审批页弹窗_按钮点击',
                        contract_id: this.$route.query.contractId,
                        ...this.sensorsTrackContractInfo,
                        window_name: 'Hubble授权协议弹窗',
                        icon_name: '关闭',
                    },
                });
            }
        },
    },
    methods: {
        handleClose() {
            this.$store.state.approval.showAuthDialog = false;
            this.$sensors.track({
                eventName: `Ent_ContractApprovalWindow_BtnClick`,
                eventProperty: {
                    page_name: '云平台_合同审批页弹窗_按钮点击',
                    contract_id: this.$route.query.contractId,
                    ...this.sensorsTrackContractInfo,
                    window_name: 'Hubble授权协议弹窗',
                    icon_name: '取消',
                },
            });
        },
        handleAuthorize() {
            this.$http.post('/contract-api/grant-approval-operation').then(() => {
                this.$store.state.approval.hasAuthorized = true;
                this.$store.state.approval.showAuthDialog = false;
            });
            this.$sensors.track({
                eventName: `Ent_ContractApprovalWindow_BtnClick`,
                eventProperty: {
                    page_name: '云平台_合同审批页弹窗_按钮点击',
                    contract_id: this.$route.query.contractId,
                    ...this.sensorsTrackContractInfo,
                    window_name: 'Hubble授权协议弹窗',
                    icon_name: '同意',
                },
            });
        },
        sendSensor() {
            this.$sensors.track({
                eventName: `Ent_ContractApprovalWindow_BtnClick`,
                eventProperty: {
                    page_name: '云平台_合同审批页弹窗_按钮点击',
                    contract_id: this.$route.query.contractId,
                    ...this.sensorsTrackContractInfo,
                    window_name: 'Hubble授权协议弹窗',
                    icon_name: '合同授权协议',
                },
            });
        },
    },
};
</script>

<style lang="scss">
.hubble-page__authorize .el-dialog{
    font-size: 14px;
    width: 500px;
    .el-dialog__body {
        padding: 20px 20px;
        p {
            margin-bottom: 14px;
        }
    }
}
</style>
