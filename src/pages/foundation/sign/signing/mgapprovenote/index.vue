<template>
    <div class="mgapprovenote mgapprovenote__box" ref="mgapprovenote" :class="`mgapprovenote-${annotateData.annotationId}`" :style="boxStyle" v-loading.fullscreen.lock="deleteLoading">
        <i class="el-icon-ssq-guanbi1 mgapprovenote__box-close" @click="deleteItem" v-show="canInit"></i>
        <div class="mgapprovenote__tabs" v-if="canInit && !loading">
            <div class="mgapprovenote__tabs-SAQ" @click="toSAQ" v-if="annotateList.length === 1">
                <i class="el-icon-ssq-qucaiji"></i>
                <span>{{ $t('mgapprovenote.SAQ') }}</span>
            </div>
            <div class="mgapprovenote__tabs-item" @click="changeType('EXPERIENCE')" v-if="canUseExperience">
                <el-tooltip
                    placement="top"
                    :content="$t('mgapprovenote.analyze')"
                >
                    <i>
                        <svg class="sel-mark-icon" aria-hidden="true">
                            <use
                                class="sel-mark-icon-use"
                                style="pointer-events: none;"
                                width="700"
                                height="700"
                                xlink:href="#el-icon-ssq-analyze"
                            >
                            </use>
                        </svg>
                    </i>
                </el-tooltip>
            </div>
            <p class="line" v-if="canUseExperience"></p>
            <div class="mgapprovenote__tabs-item" @click="changeType('COMMENT')">
                <el-tooltip
                    placement="top"
                    :content="$t('mgapprovenote.annotate')"
                >
                    <i class="el-icon-ssq-duineiwenjiandepizhu"></i>
                </el-tooltip>
            </div>
            <p class="line"></p>
            <!-- <div class="mgapprovenote__tabs-item" @click="changeType('LEGAL_ADVICE')">
                <el-tooltip
                    placement="top"
                    :content="$t('mgapprovenote.law')"
                >
                    <i class="el-icon-ssq-falv"></i>
                </el-tooltip>
            </div>
            <p class="line"></p>
            <div class="mgapprovenote__tabs-item" @click="changeType('CASE_SEARCH')">
                <el-tooltip
                    placement="top"
                    :content="$t('mgapprovenote.case')"
                >
                    <i class="el-icon-ssq-anli"></i>
                </el-tooltip>
            </div>
            <p class="line"></p> -->
            <div class="mgapprovenote__tabs-item" v-if="canUseFindReleventParagraph" @click="changeType('RELATED_CONTENT_QUERY')">
                <el-tooltip
                    placement="top"
                    :content="$t('mgapprovenote.content')"
                >
                    <i class="el-icon-ssq-anli"></i>
                </el-tooltip>
            </div>
            <p class="line" v-if="canUseFindReleventParagraph"></p>
            <div class="mgapprovenote__tabs-item" @click="changeType('TRANSLATION')">
                <el-tooltip
                    placement="top"
                    :content="$t('mgapprovenote.translate')"
                >
                    <i class="el-icon-ssq-Hubbleqiehuanyuyan"></i>
                </el-tooltip>
            </div>
        </div>
        <div class="mgapprovenote__operate" v-else-if="annotationType === 'COMMENT'">
            <span class="mgapprovenote__operate-item" @click="handleEdit">
                <i class="el-icon-ssq-xiugaihetongzhuangtai"></i>
                <span>{{ currentEditAnnotateData.annotationResultContent ? $t('sign.approvalFeatures.edit') : $t('sign.approvalFeatures.annotate') }}</span>
            </span>
            <span class="mgapprovenote__operate-item" @click="deleteItem">
                <i class="el-icon-ssq-guanbi1"></i>
                <span>{{ $t('sign.approvalFeatures.delete') }}</span>
            </span>
        </div>
        <el-popover
            width="200"
            trigger="click"
            ref="popover"
            v-model="popoverVisible"
            popper-class="mgapprovenote__popover"
            v-if="annotationType && annotationType !== 'COMMENT'"
        >
            <div v-if="annotationType !== 'EXPERIENCE'" v-loading="loading">
                <pre :style="{'max-width': maxWidth}" v-html="annotateData.annotationResultContent"></pre>
                <br>
                <p v-show="hasContent">{{ $t('mgapprovenote.tips') }}</p>
            </div>
            <div v-else v-loading="loading">
                <div v-if="loading">
                    <pre :style="{'max-width': maxWidth}"><br /><br /><br /></pre>
                </div>
                <div v-else class="mgapprovenote__popover-experience">
                    <div class="mgapprovenote__popover-experience-header">
                        <span>{{ $t('mgapprovenote.experience') }}</span>
                        <el-form inline>
                            <el-form-item style="margin-bottom: 0" :label="$t('workspaceIndex.title')" v-if="!!workspaceIds.length">
                                <el-select v-model="workspaceId" placeholder="请选择">
                                    <el-option
                                        v-for="item in workspaceIds"
                                        :key="item"
                                        :label="item"
                                        :value="item"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item style="margin-bottom: 0" :label="$t('workspaceIndex.title') + ':'" v-if="workspaceId && !workspaceIds.length">
                                <p>{{ workspaceId }}</p>
                            </el-form-item>
                        </el-form>
                    </div>
                    <div class="mgapprovenote__popover-experience-body">
                        <div class="statistics">
                            <p class="statistics-title">{{ $t('mgapprovenote.datas') }}：</p>
                            <div class="statistics-body" id="chart_container">
                                <!-- {{ Object.prototype.toString.call(annotateData.annotationResultContent) }} -->
                                <div
                                    :id="'chart_' + index"
                                    class="statistics-body-item"
                                    v-for="(item, index) in formatContent(annotateData.annotationResultContent)"
                                    :key="item.termId"
                                    @click="getTerms(item.termId)"
                                ></div>
                            </div>
                        </div>
                        <div class="terms">
                            <div class="terms-title">
                                <p>{{ $t('mgapprovenote.terms') }}：</p>
                            </div>
                            <div class="terms-list" v-loading="termsLoading">
                                <div class="terms-list-item" v-for="(term, index) in terms" :key="index">
                                    <p class="terms-list-item-content">
                                        {{ term.referenceData }}
                                    </p>
                                    <div class="terms-list-item-origin">
                                        <p>{{ $t('mgapprovenote.original') }}：{{ term.agreementFileName }}{{ term.agreementFileName }}{{ term.agreementFileName }}</p>
                                        <el-button type="text" @click="exportTerms(term.agreementId)">{{ $t('mgapprovenote.export') }}</el-button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mgapprovenote__content" slot="reference"></div>
        </el-popover>
        <div class="mgapprovenote__move" v-if="canInit" @mousedown="handleDrag">
            <div class="mgapprovenote__move-top"></div>
            <div class="mgapprovenote__move-right"></div>
            <div class="mgapprovenote__move-bottom"></div>
            <div class="mgapprovenote__move-left"></div>
            <div class="mgapprovenote__move-all"></div>
        </div>
    </div>
</template>

<script>
import { mapActions, mapMutations, mapState, mapGetters } from 'vuex';
import * as echarts from 'echarts';
import { download } from 'src/common/utils/download.js';

export default {
    props: {
        annotateData: {
            type: Object,
            default: () => {},
        },
        sensorsTrackContractInfo: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            annotateContent: '',
            loading: false,
            popoverVisible: false,
            deleteLoading: false,
            showAuthDialog: true,
            workspaceIds: [],
            workspaceId: '',
            chartmap: {},
            terms: [],
            termsLoading: false,
        };
    },
    computed: {
        ...mapState('approval', ['isMgapprovenoteMove', 'currentRect', 'annotateList', 'currentEditAnnotateData', 'hasAuthorized']),
        ...mapGetters([
            'checkFeat',
        ]),
        canUseFindReleventParagraph() {
            return this.$store.state.commonHeaderInfo.userType === 'Person' || this.checkFeat.findReleventParagraph;
        },
        canUseExperience() {
            return this.checkFeat.mgapproveAnalyze;
        },
        boxStyle() {
            const { x, y, width, height } = this.annotateData.contractContentPosition;
            return {
                top: `${y * 100}%`,
                left: `${x * 100}%`,
                width: `${width * 100}%`,
                height: `${height * 100}%`,
            };
        },
        maxWidth() {
            return document.querySelector('#pdfContainer').offsetWidth + 'px';
        },
        annotationType() {
            // COMMENT-批注 TRANSLATION-翻译 CASE_SEARCH-案例 LEGAL_ADVICE-法规 MARK 标记
            return this.annotateData.approverAnnotationType;
        },
        hasContent() {
            const { annotationResultContent } = this.annotateData;
            return annotationResultContent && annotationResultContent !== '\n\n\n\n';
        },
        canInit() {
            return !this.annotationType || this.annotationType === 'MARK';
        },
    },
    watch: {
        'currentEditAnnotateData.annotationResultContent'(val) {
            console.log(val);
            this.annotateData.annotationResultContent = val;
        },
        popoverVisible(val) {
            if (val && this.hasContent && this.annotateData.approverAnnotationType === 'EXPERIENCE') {
                setTimeout(() => {
                    const content = this.formatContent(this.annotateData.annotationResultContent);
                    this.initChart(content);
                }, 50);
            }
        },
    },
    methods: {
        ...mapMutations('approval', ['deleteAnnotateList', 'setMgapprovenoteMove']),
        ...mapActions('approval', ['saveAnnotation', 'deleteAnnotation', 'handleOtherTask', 'getAnnotationHistory']),
        async changeType(type) {
            let icon_name = '';
            switch (type) {
                case 'COMMENT':
                    icon_name = '批注';
                    break;
                case 'TRANSLATION':
                    icon_name = '翻译';
                    break;
                case 'CASE_SEARCH':
                    icon_name = '案例';
                    break;
                case 'LEGAL_ADVICE':
                    icon_name = '法规';
                    break;
                case 'EXPERIENCE':
                    icon_name = '分析';
                    break;
                case 'RELATED_CONTENT_QUERY':
                    icon_name = '查找相关段落';
                    break;
                default:
                    icon_name = '';
                    break;
            }
            this.$sensors.track({
                eventName: `Ent_ContractApproval_BtnClick`,
                eventProperty: {
                    page_name: '云平台_合同审批页_按钮点击',
                    contract_id: this.$route.query.contractId,
                    ...this.sensorsTrackContractInfo,
                    first_category: '划词框选',
                    icon_name: icon_name,
                },
            });
            if (type === 'COMMENT' || type === 'MARK') {
                return this.save(type);
            }
            if (type === 'EXPERIENCE' && !this.workspaceId) {
                await this.getWorkspace();
                if (!this.workspaceIds.length) {
                    this.$message({
                        message: '暂无可用工作空间',
                        type: 'error',
                    });
                    this.popoverVisible = false;
                    this.loading = false;
                    return;
                }
            }
            if (!this.hasAuthorized) {
                this.$store.state.approval.showAuthDialog = true;
                return;
            }
            this.annotateData.approverAnnotationType = type;
            this.annotateData.annotationResultContent = '\n\n\n\n';
            setTimeout(() => {
                this.popoverVisible = true;
            }, 50);
            this.loading = true;
            /*
            setTimeout(() => {
                this.loading = false;
                this.popoverVisible = false;
                this.$nextTick(() => {
                    this.popoverVisible = true;
                });
             }, 5000);
            */
            const { contractContentPosition, documentId } = this.annotateData;
            this.handleOtherTask({ type, data: {
                ...contractContentPosition,
                documentId,
                annotationId: this.annotateData.annotationId,
                workspaceId: this.workspaceId,
                preferredModel: this.$route.query.preferredModel,
            } }).then(res => {
                if (!res.data.annotationId) {
                    return;
                }
                this.annotateData.annotationId = res.data.annotationId;
                this.$store.state.approval.currentAnnotationId = res.data.annotationId;
                if (type === 'EXPERIENCE') {
                    // const content = this.formatContent(res.data.annotationResultContent);
                    this.annotateData.approverAnnotationType = type;
                }
                this.annotateData.annotationResultContent = res.data.annotationResultContent;
                this.popoverVisible = false;
                this.$nextTick(() => {
                    this.popoverVisible = true;
                });
                this.getAnnotationHistory();
            }).catch(err => {
                this.annotateData.approverAnnotationType = '';
                this.annotateData.annotationResultContent = '';
                this.popoverVisible = false;
                const { code, message } = err.response?.data;
                if (code && code === '140001') {
                    return this.$confirm(this.$t('mgapprovenote.limit'), {
                        confirmButtonText: this.$t('mgapprovenote.confirmTxt'),
                        showCancelButton: false,
                    }).then(() => {
                        this.toSAQ();
                    });
                }
                message && this.$MessageToast.error(message);
            }).finally(() => {
                this.loading = false;
            });
        },
        handleEdit() {
            this.$store.state.approval.currentEditAnnotateData = this.annotateData;
            this.$store.state.approval.showEditDialog = true;
        },
        save(type) {
            this.deleteLoading = true;
            this.saveAnnotation({
                ...this.annotateData,
                annotationContent: '',
                approverAnnotationType: type,
            }).then(res => {
                if (!res.data.annotationId) {
                    return;
                }
                this.annotateData.approverAnnotationType = type;
                this.annotateData.annotationId = res.data.annotationId;
                this.$store.state.approval.currentAnnotationId = res.data.annotationId;
                type === 'COMMENT' && this.handleEdit();
                this.getAnnotationHistory();
            }).catch(() => {
                this.annotateData.approverAnnotationType = '';
            }).finally(() => {
                this.workspaceIds = [];
                this.workspaceId = '';
                this.terms = [];
                this.deleteLoading = false;
            });
        },
        deleteItem() {
            if (!this.annotateData.annotationId) {
                return this.deleteAnnotateList(this.annotateData.annotationId);
            }
            this.deleteLoading = true;
            this.deleteAnnotation(this.annotateData.annotationId).then(() => {
                this.deleteAnnotateList(this.annotateData.annotationId);
            }).finally(() => {
                this.deleteLoading = false;
            });
        },
        toSAQ() {
            window.open('https://jinshuju.net/f/FzPQZS');
        },
        handleDrag(e) {
            this.startDrag(e);
        },
        startDrag(event) {
            this.setMgapprovenoteMove(true);
            const realTop = document.getElementById('sign-documents').scrollTop - this.currentRect.top;
            const moveType = event.target.className.split('-')[1];
            const { x, y, width, height } = this.annotateData.contractContentPosition;
            const startPos = { x, y };
            const endPos = { x, y };
            switch (moveType) {
                case 'top':
                    startPos.y = y + height;
                    break;
                case 'left':
                    startPos.x = x + width;
                    break;
                case 'all':
                    startPos.x = event.clientX;
                    startPos.y = event.clientY;
                    break;
            }
            const handleMove = (moveEvent) => {
                if (!this.isMgapprovenoteMove) {
                    return;
                }
                switch (moveType) {
                    case 'all':
                        this.annotateData.contractContentPosition = {
                            ...this.annotateData.contractContentPosition,
                            x: x + (moveEvent.clientX - event.clientX) / this.currentRect.width,
                            y: y + (moveEvent.clientY - event.clientY) / this.currentRect.height,
                        };
                        break;
                    case 'top':
                    case 'bottom':
                        endPos.y = (moveEvent.clientY + realTop) / this.currentRect.height;
                        this.annotateData.contractContentPosition = {
                            ...this.annotateData.contractContentPosition,
                            y: Math.min(startPos.y, endPos.y),
                            height: Math.abs(startPos.y - endPos.y),
                        };
                        break;
                    case 'left':
                    case 'right':
                        endPos.x = (moveEvent.clientX - this.currentRect.left) / this.currentRect.width;
                        this.annotateData.contractContentPosition = {
                            ...this.annotateData.contractContentPosition,
                            x: Math.min(startPos.x, endPos.x),
                            width: Math.abs(startPos.x - endPos.x),
                        };
                        break;
                }
            };
            const handleUp = () => {
                this.setMgapprovenoteMove(false);
                this.changeType('MARK');
                document.removeEventListener('mousemove', handleMove);
                document.removeEventListener('mouseup', handleUp);
            };
            document.addEventListener('mousemove', handleMove);
            document.addEventListener('mouseup', handleUp);
        },
        async getWorkspace() {
            await this.$http.get('/contract-api/approval/get-workspace').then(res => {
                this.workspaceIds = res.data.workspaceIds || [];
                this.workspaceId = this.workspaceIds[0] || '';
            });
        },
        // getNewExperience(val) {
        //     console.log(val);
        //     this.annotateData.annotationResultContent = '';
        //     this.popoverVisible = false;
        //     this.loading = false;
        //     this.changeType('EXPERIENCE');
        // },
        formatContent(content) {
            if (Object.prototype.toString.call(content) === '[object String]') {
                const annotationResult = content;
                const start = '"termId\":';
                const end = ',\"termName';
                const regex = new RegExp(start + '(.*?)' + end, 'g');
                const replaceString = annotationResult.replace(regex, (match, ele) => {
                    return start + '"' + ele + '"' + end;
                });
                const result = JSON.parse(replaceString);
                return result;
            } else {
                return content;
            }
        },
        initChart(annotationResultContent) {
            if (Object.keys(this.chartmap).length) {
                for (const key in this.chartmap) {
                    this.chartmap[key].dispose();
                }
                this.chartmap = {};
            }
            for (let index = 0; index < annotationResultContent.length; index++) {
                const element = annotationResultContent[index];
                const myChart = echarts.init(document.getElementById('chart_' + index));
                this.chartmap[`chart_${index}`] = myChart;
                if (element.displayType === 'BAR') {
                    const xData = element.termStatisticsValues.map(item => {
                        return item.termFieldValue;
                    });
                    const valueData = element.termStatisticsValues.map(item => {
                        return item.count;
                    });
                    this.renderBarChart(myChart,  element.termName, xData, valueData);
                } else if (element.displayType === 'PIE') {
                    this.renderPieChart(myChart, element.termName, element.termStatisticsValues);
                }
            }
            // const popover = this.$refs.popover;
            // const popoverContent = document.querySelector('.mgapprovenote__popover');
            // const popoverRect = popover.$el.getBoundingClientRect();
            // const contentRect = popoverContent.getBoundingClientRect();
            // const isHorizontalCenter = Math.abs(contentRect.left + contentRect.width / 2 - popoverRect.left - popoverRect.width / 2) < 1;
            this.getTerms(annotationResultContent[0].termId);
            // if (isHorizontalCenter) {
            //     return;
            // } else if (contentRect.left >= (popoverRect.left - popoverRect.width / 2 - 100)) {
            //     document.getElementById('sign-documents').scrollTop -= 1;
            // } else {
            //     return;
            // }
        },
        renderBarChart(myChart, title, xData, valueData) {
            const gridWidth = myChart.getWidth();
            const wordNum = parseInt((gridWidth / xData.length) / 12) - 1;
            myChart.setOption({
                title: {
                    text: title,
                    left: 'center',
                    textStyle: {
                        color: '#999',
                        fontWeight: 'normal',
                        fontSize: 14,
                    },
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow',
                    },
                },
                xAxis: {
                    type: 'category',
                    data: xData,
                    axisLabel: {
                        overflow: 'breakAll',
                        interval: 0,
                        formatter: (value) => {
                            const strs = value.split('');
                            let str = '';
                            str = strs?.length > wordNum ? value.substring(0, wordNum.toFixed(0) - 1) + '...' : value;
                            return str;
                        },
                    },
                },
                yAxis: {
                    type: 'value',
                },
                series: [
                    {
                        data: valueData,
                        type: 'bar',
                    },
                ],
            });
        },
        renderPieChart(myChart, title, statistics) {
            const datas = statistics.map(statistic => {
                return {
                    name: statistic.termFieldValue,
                    value: statistic.count,
                };
            });
            const option = {
                title: {
                    text: title,
                    left: 'center',
                    textStyle: {
                        color: '#999',
                        fontWeight: 'normal',
                        fontSize: 14,
                    },
                },
                tooltip: {
                    trigger: 'item',
                },
                series: [
                    {
                        type: 'pie',
                        radius: [30, 60],
                        left: 'center',
                        width: myChart.getWidth() * 0.9,
                        itemStyle: {
                            borderColor: '#fff',
                            borderWidth: 1,
                        },
                        label: {
                            alignTo: 'edge',
                            formatter: '{b}\n{d} %',
                            minMargin: 5,
                            edgeDistance: 10,
                            lineHeight: 15,
                        },
                        labelLine: {
                            length: 15,
                            length2: 0,
                            maxSurfaceAngle: 80,
                        },
                        labelLayout: (params) => {
                            const isLeft = params.labelRect.x < myChart.getWidth() / 2;
                            const points = params.labelLinePoints;
                            // Update the end point.
                            points[2][0] = isLeft
                                ? params.labelRect.x
                                : params.labelRect.x + params.labelRect.width;
                            return {
                                labelLinePoints: points,
                            };
                        },
                        data: datas,
                    },
                ],
            };
            myChart.setOption(option);
        },
        getTerms(termId) {
            if (!this.termsLoading) {
                this.termsLoading = true;
                this.$http.get(`/contract-api/approval/experience/${this.annotateData.annotationId}/term-reference/${termId}`).then(res => {
                    this.terms = res.data.referenceInfos;
                    this.workspaceId = res.data.workspaceId;
                }).finally(() => {
                    this.termsLoading = false;
                    this.$nextTick(() => {
                        this.$refs.popover.updatePopper();
                    });
                });
            } else {
                return;
            }
        },
        exportTerms(agreementId) {
            const downloadUrl = `/contract-api/approval/workspace/${this.workspaceId}/agreement-download/${agreementId}`;
            download(downloadUrl);
        },
    },
    created() {
        if (!this.annotateData.annotationId) {
            this.changeType('MARK');
        }
        setTimeout(() => {
            this.annotateData.annotationResultContent && (this.popoverVisible = true);
            if (this.annotateData.approverAnnotationType === 'EXPERIENCE') {
                document.querySelector(`.mgapprovenote-${this.annotateData.annotationId}`).scrollIntoView({ behavior: 'smooth', block: 'start' });
            } else {
                document.querySelector(`.mgapprovenote-${this.annotateData.annotationId}`).scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }, 100);
    },
};
</script>

<style lang="scss">
.mgapprovenote{
    font-size: 14px;
    z-index: 99;
    &__box{
        position: absolute;
        border: 1px dashed $theme-color;
        border-radius: 3px;
        background: rgba($color: #E7F3FB, $alpha: .1);
        cursor: pointer;
        &-close{
            cursor: pointer;
            position: absolute;
            top: -6px;
            right: -6px;
            background: #fff;
            z-index: 9;
        }
        &.disabled{
            cursor: default;
        }
    }
    &__tabs{
        font-size: 12px;
        position: absolute;
        top: -45px;
        right: 0;
        border-radius: 5px;
        opacity: 1;
        background: #FFFFFF;
        box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: row;
        align-items: center;
        .line{
            height: 30px;
            border-right: 1px solid #D8D8D8;
        }
        &-item{
            cursor: pointer;
            .sel-mark-icon {
                margin-top: 2px;
                width: 13px;
                height: 13px;
            }
            i{
                padding: 10px 24px;
            }
            &:hover{
                color: $theme-color;
                .sel-mark-icon-use {
                    fill: $theme-color;
                }
            }
        }
        &-SAQ{
            position: absolute;
            top: -40px;
            right: 0;
            padding: 7px;
            border-radius: 5px;
            background: #f4f4f4;
            display: flex;
            flex-direction: row;
            cursor: pointer;
            align-items: center;
            box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
            border: 1px solid #ddd;
        }
    }
    &__operate{
        color: #fff;
        line-height: 28px;
        background: $theme-color;
        position: absolute;
        top: -30px;
        right: 0;
        display: flex;
        flex-direction: row;
        &-item{
            padding: 0 8px;
            display: flex;
            cursor: pointer;
            span{
                white-space: nowrap;
            }
            i{
                display: block;
                line-height: 28px;
                margin-right: 5px;
            }
            & + span{
                border-left: 1px solid #fff;
            }
        }
    }
    &__popover{
        width: unset !important;
        padding-top: 0;
        pre{
            padding: 20px 0;
            white-space: pre-wrap;
            max-height: 40vh;
            overflow: auto;
        }
        p{
            font-size: 12px;
            color: #ccc;
            text-align: right;
        }
        &-experience {
            width: 80vw;
            max-height: 80vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            &-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 5px 20px;
                color: #333333;
                font-size: 14px;
                font-weight: 500;
                line-height: 18px;
                border-bottom: 1px solid #F1F1F1;
            }
            &-body {
                display: flex;
                width: 100%;
                flex-grow: 1;
                overflow: hidden;
                .statistics {
                    flex: 9;
                    display: flex;
                    flex-direction: column;
                    &-title {
                        padding-left: 17px;
                        padding-top: 12px;
                        font-size: 12px;
                        line-height: 18px;
                        color: #666666;
                        text-align: left;
                        flex-shrink: 0;
                    }
                    &-body {
                        display: flex;
                        width: 100%;
                        flex-wrap: wrap;
                        align-items: center;
                        justify-content: center;
                        padding: 10px;
                        flex-grow: 1;
                        overflow-y: auto;
                        &-item {
                            width: 49%;
                            height: 300px;
                        }
                    }
                }
                .terms {
                    flex: 3;
                    background: #F8F8F8;
                    padding: 10px 19px;
                    display: flex;
                    flex-direction: column;
                    &-title {
                        margin-bottom: 10px;
                        color: #666;
                        p {
                            text-align: left;
                            color: #666;
                        }
                    }
                    &-list {
                        flex-grow: 1;
                        overflow-y: auto;
                        &-item {
                            padding: 12px 14px;
                            background: #fff;
                            line-height: 18px;
                            font-weight: normal;
                            margin-bottom: 10px;
                            word-break: break-word;
                            p {
                                text-align: left;
                            }
                            &-content {
                                font-size: 12px;
                                color: #333333;
                            }
                            &-origin {
                                display: flex;
                                align-items: center;
                                justify-content: space-between;
                                p {
                                    font-size: 12px;
                                    color: #979797;
                                    max-width: 190px;
                                    overflow: hidden;
                                    white-space: nowrap;
                                    text-overflow: ellipsis;
                                }
                                .el-button {
                                    font-size: 12px;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    &__content{
        width: 100%;
        height: 100%;
        position: absolute;
    }
    &__move{
        div{
            position: absolute;
            // background: red;
        }
        &-top, &-bottom{
            height: 2px;
            right: 0;
            left: 0;
            cursor: row-resize;
        }
        &-right, &-left{
            width: 2px;
            top: 2px;
            bottom: 2px;
            cursor: col-resize;
        }
        &-top{
            top: 0;
        }
        &-bottom{
            bottom: 0;
        }
        &-right{
            right: 0;
        }
        &-left{
            left: 0;
        }
        &-all{
            top: 2px;
            right: 2px;
            bottom: 2px;
            left: 2px;
            cursor: move;
        }
    }
}
</style>
