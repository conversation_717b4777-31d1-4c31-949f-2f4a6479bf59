<template>
    <div>
        <el-popover
            placement="left"
            width="160"
            v-model="popVisible"
            trigger="manual"
            popper-class="popover-list"
        >
            <div>
                <div
                    :class="{'new-function': true, 'new-function-active': activeIndex === index}"
                    v-for="(item, index) in functions"
                    :key="item.name"
                    @click="showContent(item, index)"
                >
                    <img :src="item.activeUrl" :alt="item.name" v-if="activeIndex === index">
                    <img :src="item.url" :alt="item.name" v-else>
                    <span>{{ item.name }}</span>
                </div>
            </div>
            <div slot="reference" class="instruction-enty" @click.stop="showList">
                <img class="light" src="~img/news-light.png" alt="新功能">
                <p>新</p>
                <p>功</p>
                <p>能</p>
                <p>介</p>
                <p>绍</p>
            </div>
        </el-popover>

        <el-dialog :visible.sync="contentVisible" :title="functionName" top="63px" :before-close="sendSensor">
            <div>
                <p class="title">功能介绍</p>
                <p class="func-desciption">{{ description }}</p>
            </div>
            <div>
                <p class="title">功能演示</p>
                <video :src="video" loop muted autoplay style="width: 100%;"></video>
            </div>
            <div class="btn-container">
                <div class="operate">
                    <el-button icon="arrow-left" @click="getFuncDesc('pre')" :disabled="activeIndex === 0"></el-button>
                    <el-button icon="arrow-right" @click="getFuncDesc('next')" :disabled="activeIndex === functions.length - 1"></el-button>
                </div>
                <div class="feedback">
                    <el-button @click="toPage">问卷反馈</el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>

export default {
    props: {
        sensorsTrackContractInfo: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            popVisible: false,
            contentVisible: false,
            functions: [
                {
                    name: '划句批注',
                    url: require('img/newFunctions/xinxi.png'),
                    activeUrl: require('img/newFunctions/xinxi-active.png'),
                    description: '使用划句批注功能，你可以在合同内容中选中指定区域的内容，对这段内容进行批注评论。当你进行审批和签署时留下你评论的内容，方便其他用户可以了解你的想法。',
                    video: 'https://ssqian-homepage-dev.obs.cn-east-2.myhuaweicloud.com:443/1a1991f6023fd77f3319ce435f869810163ad712.mp4',
                },
                {
                    name: '划句翻译',
                    url: require('img/newFunctions/fanyi.png'),
                    activeUrl: require('img/newFunctions/fanyi-active.png'),
                    description: '使用划句翻译功能，你可以在合同内容中选中指定区域的内容，系统会对这段内容进行翻译。当你进行审批和签署时，方便你对多语言合同内容进行理解。',
                    video: 'https://ssqian-homepage-dev.obs.cn-east-2.myhuaweicloud.com:443/bb521a1438190bc1bb1a4e413b6177f7e1222e82.mp4',
                },
                // {
                //     name: '划句类案检索',
                //     url: require('img/newFunctions/anli.png'),
                //     activeUrl: require('img/newFunctions/anli-active.png'),
                //     description: '使用划句相关案例检索功能，你可以在合同内容中选中指定区域的内容，系统会查找匹配裁判文书网中与选中内容有关的案例。当你进行审批和签署时，方便你在自己的立场上对合同内容进行风险评估。',
                //     video: 'https://ssqian-homepage-dev.obs.cn-east-2.myhuaweicloud.com:443/25b361425041a97da6cafa6a7fb4ed07d59ca292.mp4',
                // },
                // {
                //     name: '划句法规检索',
                //     url: require('img/newFunctions/falv.png'),
                //     activeUrl: require('img/newFunctions/falv-active.png'),
                //     description: '使用划句相关法律条款检索功能，你可以在合同内容中选中指定区域的内容，系统会查找匹配选中内容相关的法律条款。当你进行审批和签署时，方便你了解合同内容有关的法律条款。',
                //     video: 'https://ssqian-homepage-dev.obs.cn-east-2.myhuaweicloud.com:443/385132bba76f7127fa2864894a7e5285f0eb7fc3.mp4',
                // },
            ],
            activeIndex: '',
            functionName: '',
            description: '',
            video: null,
        };
    },
    computed: {
        showEntry() {
            return this.$route.query.type === 'approval';
        },
    },
    methods: {
        showList() {
            if (this.contentVisible) {
                this.contentVisible = true;
            }
            this.popVisible = !this.popVisible;
            if (!this.popVisible && !this.contentVisible) {
                this.activeIndex = '';
                this.functionName = '';
                this.description = '';
                this.video = null;
                // cancelAnimationFrame(this.rafId);
            }
        },
        // 点击菜单项展示弹窗
        showContent(item, index) {
            this.activeIndex = index;
            this.functionName = item.name;
            this.description = item.description;
            this.video = item.video;
            this.contentVisible = true;
            if (this.$route.query.type === 'approval') {
                this.$sensors.track({
                    eventName: `Ent_ContractApproval_BtnClick`,
                    eventProperty: {
                        page_name: '云平台_合同审批页_按钮点击',
                        contract_id: this.$route.query.contractId,
                        ...this.sensorsTrackContractInfo,
                        first_category: '新功能介绍',
                        icon_name: item.name,
                    },
                });
                this.$sensors.track({
                    eventName: `Ent_ContractApprovalWindow_PopUp`,
                    eventProperty: {
                        page_name: '云平台_合同审批页弹窗_弹出',
                        contract_id: this.$route.query.contractId,
                        ...this.sensorsTrackContractInfo,
                        window_name: item.name,
                    },
                });
            }
        },
        // 点击弹窗中的 上一个 / 下一个 切换展示内容
        getFuncDesc(type) {
            if (this.activeIndex === 0 && type === 'pre') {
                return;
            }
            if (this.activeIndex === this.functions.length - 1 && type === 'next') {
                return;
            }
            if (type === 'pre') {
                this.activeIndex -= 1;
            }
            if (type === 'next') {
                this.activeIndex += 1;
            }
            this.video = this.functions[this.activeIndex].video;
            this.functionName = this.functions[this.activeIndex].name;
            this.description = this.functions[this.activeIndex].description;
        },
        toPage() {
            if (this.$route.query.type === 'approval') {
                this.$sensors.track({
                    eventName: `Ent_ContractApprovalWindow_BtnClick`,
                    eventProperty: {
                        page_name: '云平台_合同审批页弹窗_按钮点击',
                        contract_id: this.$route.query.contractId,
                        ...this.sensorsTrackContractInfo,
                        window_name: this.functionName,
                        icon_name: '问卷反馈',
                    },
                });
            }
            window.open('https://jsj.top/f/FzPQZS');
        },
        sendSensor(done) {
            if (this.$route.query.type === 'approval') {
                this.$sensors.track({
                    eventName: `Ent_ContractApprovalWindow_BtnClick`,
                    eventProperty: {
                        page_name: '云平台_合同审批页弹窗_按钮点击',
                        contract_id: this.$route.query.contractId,
                        ...this.sensorsTrackContractInfo,
                        window_name: this.functionName,
                        icon_name: '关闭',
                    },
                });
            }
            done();
        },
    },
};
</script>
<style lang="scss" scoped>
    .instruction-enty {
        width: 32px;
        height: 138px;
        padding: 8px 0;
        background-color: #3a8be7;
        border-radius: 16px;
        text-align: center;
        box-sizing: border-box;
        color: #fff;
        font-size: 11px;
        position: absolute;
        right: 0;
        bottom: 0;
        z-index: 10001;
        cursor: pointer;

        .light {
            width: 32px;
        }

    }
    .new-function {
        display: flex;
        align-items: center;
        padding: 7px 0;
        cursor: pointer;
        img {
            width: 18px;
            margin-right: 20px;
        }
        &-active {
            color: #3a8be7;
        }
    }

    .title {
        font-size: 15px;
        font-weight: 500;
        margin-bottom: 10px;
    }

    .func-desciption {
        font-size: 13px;
        color: #999;
        line-height: 20px;
        margin-bottom: 20px;
    }

    .btn-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;
        .operate button {
            width: 30px;
            height: 30px;
            border-radius: 20px;
            padding: 0px;
            font-size: 12px;
        }
        .feedback button {
            background-color: #3a8be7;
            border-radius: 5px;
            border: none;
            color: #fff;
            padding: 12px 20px;
            font-size: 14px
        }
    }

    /deep/ .el-dialog {
        padding: 0 30px;
        .el-dialog__header .el-dialog__title {
            font-size: 18px !important;
            font-weight: 500 !important;
        }
        .el-dialog__header, .el-dialog__body {
            padding-left: 0 !important;
            padding-right: 0 !important;
        }
    }
</style>
<style>
.el-popover.popover-list {
    z-index: 10001 !important;
}
</style>
