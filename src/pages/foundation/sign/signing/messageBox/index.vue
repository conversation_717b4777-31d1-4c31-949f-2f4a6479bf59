<!--取消确认弹窗-->
<template>
    <el-dialog
        :title="dialogTitle"
        class="common-dialog"
        :visible.sync="dialogVisible"
        :modal-append-to-body="true"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
    >
        <div class="common-dialog-content">
            <i class="el-icon-warning"></i>
            <slot name="content"><span>{{ dialogContent }}</span></slot>
        </div>
        <div slot="footer">
            <el-button v-if="showCancelBtn" @click="handleCancel">{{ cancelBtnText }}
            </el-button>
            <el-button type="primary" @click="handleConfirm">{{ confirmBtnText }}
            </el-button>
        </div>
    </el-dialog>
</template>
<script>
export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        dialogTitle: {
            type: String,
            default: '',
        },
        dialogContent: {
            type: String,
            default: '',
        },
        confirmBtnText: {
            type: String,
            default: '',
        },
        cancelBtnText: {
            type: String,
            default: '',
        },
        showCancelBtn: {
            type: Boolean,
            default: true,
        },
    },
    computed: {
        dialogVisible: {
            set(value) {
                this.$emit('update:visible', value);
            },
            get() {
                return this.visible;
            },
        },
    },
    methods: {
        handleCancel() {
            this.$emit('cancel');
        },
        handleConfirm() {
            this.$emit('confirm');
        },
    },
};
</script>
<style lang="scss">
.common-dialog .el-dialog {
    width: 400px;
    border-radius: 4px;
}
.common-dialog-content {
    display: flex;
}
.el-icon-warning {
    color: #F2A93E;
    margin-right: 5px;
    line-height: 20px;
}
.common-dialog-cancel-btn {
    background: #F8F8F8;
    color: #666;
}
</style>
