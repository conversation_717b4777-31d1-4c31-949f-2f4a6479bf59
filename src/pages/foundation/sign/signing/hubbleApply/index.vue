<template>
    <div v-if="showEntry">
        <div class="hubble-apply-entry" :class="{'foreignEntry': !isZh}">
            <i class="close el-icon-ssq--bs-guanbi" @click="toggleFloat"></i>
            <div class="info">
                <p>Hubble</p>
                <p>{{ $t('hubbleEntry.smartAdvisor') }}</p>
            </div>
            <div v-for="item in entryList" :key="item.name" class="menu">
                <div
                    :class="[
                        'entry',
                        item.openNewPage ? 'new-page' : 'clickable',
                        item.videoPath || isZh ? '' : 'fullContent',
                    ]"
                    @click="jump(item)"
                >
                    <span class="icon"><i :class="item.icon"></i></span>
                    <span>{{ item.name }}</span>
                </div>
                <div v-show="item.openNewPage || !item.openNewPage && item.videoPath " :class="['icon-play', item.videoPath ? '' : 'new-page']" @click="showVideo(item)"><img src="~img/hubble/player.png" /></div>
            </div>
        </div>
        <div class="hubble-apply-entry-hidden hidden" @click="toggleFloat">
            <img src="~img/AIBot.png" alt="">
            <i class="el-icon-ssq-biaoqiannew"></i>
        </div>
        <el-dialog :visible.sync="videoVisible" :title="type" top="63px" :before-close="handleVideoClose">
            <video
                id="video"
                controls="controls"
                controlsList="nodownload"
                autoplay
                :src="videoPath"
            ></video>
        </el-dialog>
        <DocDialog
            :type="type"
            :visible.sync="docVisible"
            :docList="docList"
            :currentDocIndex="currentDocIndex"
            :path="path"
            :sensorsTrackContractInfo="sensorsTrackContractInfo"
        >
        </DocDialog>
        <el-dialog
            :visible.sync="qrcodeVisible"
            custom-class="qrcode-dialog"
            :show-close="false"
        >
            <div slot="title" class="qrcode-dialog__header">
                <i class="el-icon-ssq-guanbi1" @click="qrcodeVisible = false"></i>
            </div>
            <div class="qrcode-dialog__body">
                <p>{{ $t('hubbleEntry.tooltips') }}</p>
                <img src="~img/consultantQRcode.png" alt="上上签电子签约顾问">
                <div class="qrcode-dialog__body-btn">
                    <el-button type="text" @click="qrcodeVisible = false">{{ $t('hubbleEntry.confirm') }}</el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
// import VideoDialog from 'enterprise_pages/template/common/VideoDialog.vue';
import DocDialog from '../docDialog';
import { hubblePermissionCheck } from '@/api/sign.js';
import { buttonDisplay, getRiskOpen } from 'src/api/judgeRisk.js';
import { isIE } from 'src/common/utils/device.js';

export default {
    components: {
        // VideoDialog,
        DocDialog,
    },
    props: {
        isHybridCloudContract: {
            type: Boolean,
            default: false,
        },
        docList: {
            type: Array,
            default: () => {
                return [];
            },
        },
        currentDocIndex: {
            type: Number,
            default: 0,
        },
        sensorsTrackContractInfo: {
            type: Object,
            default: () => {
                return {};
            },
        },
    },
    data() {
        return {
            videoVisible: false,
            docVisible: false,
            type: '',   // 操作类型: 合同对比/合同翻译/合同抽取
            path: '',   // 路由跳转地址
            videoPath: '',  // 视频地址
            enterPageDate: '', // 进视频弹窗时间
            judgeBtnDisplay: false,
            canUseRisk: true,
            qrcodeVisible: false,
        };
    },
    computed: {
        ...mapGetters([
            'checkFeat',
            'getIsForeignVersion',
            'isPerson',
        ]),
        ...mapState({
            hasAuthorized: state => state.approval.hasAuthorized,
        }),
        entryList() {
            return [
                {
                    name: this.$t('contractCompare.title'),
                    icon: 'el-icon-ssq-hubblexingongnengbidui',
                    path: '/hubble-apply/contract-comparison',
                    show: this.isPerson || this.checkFeat.hubbleContractCompare,
                    videoPath: 'https://ssqian-homepage-dev.obs.cn-east-2.myhuaweicloud.com:443/4e380a9b36fe27208bee531194bcdc2d9d6c378b.mp4',
                },
                {
                    name: this.$t('contractCompare.translate'),
                    icon: 'el-icon-ssq-hubblexingongnengfanyi',
                    path: '/hubble-apply/doc-translation',
                    show: this.isPerson || this.checkFeat.hubbleContractTranslate,
                    videoPath: 'https://ssqian-homepage-dev.obs.cn-east-2.myhuaweicloud.com:443/800be0995a1d48e8b670d88b55cd9c4b3edfcdb9.mp4',
                    // openNewPage: true,
                },
                {
                    name: this.$t('agent.extractTitle'),
                    icon: 'el-icon-ssq-tiqu',
                    path: '',
                    show: this.isPerson || this.checkFeat.hubbleContractCompare,
                    openNewPage: false,
                    videoPath: '',
                    // videoPath: 'https://ssqian-homepage-dev.obs.cn-east-2.myhuaweicloud.com:443/34105ee765019110ac474cb5ae95002e4c725e74.mp4',
                },
                {
                    name: this.$t('judgeRisk.title'),
                    icon: 'el-icon-ssq-fengxianpinggu',
                    path: '',
                    show: this.canUseRisk && this.canUseJudgeRisk,
                    openNewPage: false,
                    videoPath: '',
                    // videoPath: 'https://ssqian-homepage-dev.obs.cn-east-2.myhuaweicloud.com:443/34105ee765019110ac474cb5ae95002e4c725e74.mp4',
                },
                {
                    name: this.$t('judgeRisk.aiInterpret'),
                    icon: 'el-icon-ssq-AIjiedu-xian',
                    path: '',
                    show: this.canUseContractInfoExtract,
                    openNewPage: false,
                    videoPath: '',
                    // videoPath: 'https://ssqian-homepage-dev.obs.cn-east-2.myhuaweicloud.com:443/34105ee765019110ac474cb5ae95002e4c725e74.mp4',
                },
                // 临时隐藏 合同抽取
                // {
                //     name: '合同抽取',
                //     icon: 'el-icon-ssq-hubblexingongnnegchouqu',
                //     path: '/hubble-apply/contract-extract',
                //     show: this.isPerson || this.checkFeat.hubbleContractCompare,
                //     videoPath: 'https://ssqian-homepage-dev.obs.cn-east-2.myhuaweicloud.com:443/c63b925b327f54e90c29c1db6209d0aab0a66833.mp4',
                // },
                {
                    name: this.$t('agreement.title'),
                    icon: 'el-icon-ssq-hubblexingongnengshencha',
                    path: '',
                    show: this.isPerson || this.checkFeat.hubbleContractCompare,
                    openNewPage: false,
                    videoPath: '',
                    // videoPath: 'https://ssqian-homepage-dev.obs.cn-east-2.myhuaweicloud.com:443/34105ee765019110ac474cb5ae95002e4c725e74.mp4',
                },
                // {
                //     name: '风险判断',
                //     icon: 'el-icon-ssq-hubblexingongnengshencha',
                //     path: '/hubble-apply/risk-judgement',
                //     show: this.isPerson || this.checkFeat.contractJudgeRisk,
                //     // openNewPage: true,
                //     videoPath: '',
                //     // videoPath: 'https://ssqian-homepage-dev.obs.cn-east-2.myhuaweicloud.com:443/34105ee765019110ac474cb5ae95002e4c725e74.mp4',
                // },
            ].filter((item) => item.show);
        },
        isZh() {
            return this.$i18n.locale === 'zh';
        },
        showEntry() {
            return !this.getIsForeignVersion && this.entryList.length && !this.isHybridCloudContract;
        },
        canUseContractInfoExtract() {
            return !isIE() && !this.$hybrid.isGamma() && (this.isPerson || this.checkFeat.contractInterpretation);
        },
        canUseJudgeRisk() {
            return !isIE() && !this.$hybrid.isGamma() && this.judgeBtnDisplay;
        },
    },
    methods: {
        toggleFloat() {
            const float = document.querySelectorAll('.hubble-apply-entry');
            const aside = document.querySelectorAll('.hubble-apply-entry-hidden');
            let hubbleIcon = false;
            Array.prototype.forEach.call(float, function(item) {
                if (item.classList.contains('hidden')) {
                    hubbleIcon = true;
                }
                item.classList.toggle('hidden');
            });
            Array.prototype.forEach.call(aside, function(item) {
                item.classList.toggle('hidden');
            });
            this.$sensors.track({
                eventName: `Ent_HubbleEnterWindow_BtnClick`,
                eventProperty: {
                    page_name: '合同签署页',
                    ...this.sensorsTrackContractInfo,
                    window_name: 'Hubble入口弹窗',
                    icon_name: hubbleIcon ? 'Hubble图标' : '关闭',
                },
            });
        },
        showVideo(item) {
            if (item.name === this.$t('hubbleEntry.agreementManagement')) {
                return;
            }
            this.$sensors.track({
                eventName: `Ent_HubbleEnterWindow_BtnClick`,
                eventProperty: {
                    page_name: '合同签署页',
                    ...this.sensorsTrackContractInfo,
                    window_name: 'Hubble入口弹窗',
                    icon_name: `${item.name}视频`,
                },
            });
            this.type = item.name;
            this.videoPath = item.videoPath;
            if (!this.videoVisible) {
                this.videoVisible = !this.videoVisible;
            }
            this.enterPageDate = Date.parse(new Date());
        },
        jump(item) {
            this.$sensors.track({
                eventName: `Ent_HubbleEnterWindow_BtnClick`,
                eventProperty: {
                    page_name: '合同签署页',
                    ...this.sensorsTrackContractInfo,
                    window_name: 'Hubble入口弹窗',
                    icon_name: item.name,
                },
            });
            if (item.openNewPage) {
                window.open(item.path);
                return;
            }
            this.checkHubblePermission(item);
        },
        handleVideoClose(done) {
            const duration = (Date.parse(new Date()) - this.enterPageDate) / 1000;
            const video = document.querySelector('#video');
            video.pause();
            done();
            if (Object.keys(this.sensorsTrackContractInfo).length) {
                this.$sensors.track({
                    eventName: `Ent_HubbleEnterWindow_BtnClick`,
                    eventProperty: {
                        page_name: '合同签署页',
                        ...this.sensorsTrackContractInfo,
                        window_name: 'Hubble视频弹窗',
                        video_name: `${this.type}视频`,
                        icon_name: '关闭',
                        $event_duration: duration,
                    },
                });
            }
            this.videoVisible = false;
        },
        checkHubblePermission(menu) {
            let hubbleToolType = '';
            switch (menu.name) {
                case this.$t('contractCompare.title'):
                    hubbleToolType = 'COMPARE';
                    break;
                case this.$t('contractCompare.translate'):
                    hubbleToolType = 'TRANSLATION';
                    break;
                case '合同抽取':
                    hubbleToolType = 'EXTRACTION';
                    break;
                case this.$t('agent.extractTitle'):
                    hubbleToolType = 'EXTRACT';
                    break;
                case this.$t('judgeRisk.title'):
                    hubbleToolType = 'RISK_JUDGEMENT';
                    break;
                case this.$t('judgeRisk.aiInterpret'):
                    hubbleToolType = 'CONTRACT_INTERPRETATION';
                    break;
                case this.$t('agreement.title'):
                    hubbleToolType = 'REVIEW';
                    break;
                default:
                    break;
            }
            if (hubbleToolType === 'REVIEW') {
                this.qrcodeVisible = true;
            } else if (
                hubbleToolType === 'RISK_JUDGEMENT' ||
                hubbleToolType === 'CONTRACT_INTERPRETATION' ||
                hubbleToolType === 'EXTRACT'
            ) {
                this.$emit('changeAgentType', hubbleToolType);
                if (!this.hasAuthorized) {
                    this.$store.state.approval.showAuthDialog = true;
                    return;
                }
                this.$emit('handleChangeType');
            } else {
                hubblePermissionCheck(this.docList[0].contractId, hubbleToolType).then(({ data: { canUpload, reason } }) => {
                    if (canUpload) {
                        this.handleHubble(menu);
                    } else {
                        this.$confirm(reason, {
                            title: this.$t('templateCommon.tip'),
                            type: 'warning',
                            confirmButtonText: this.$t('batchImport.iKnow'),
                            showCancelButton: false,
                        }).then(() => {}).catch(() => {});
                    }
                });
            }
        },
        handleHubble(menu) {
            if (this.docList.length > 1) {
                this.type = menu.name.slice(2, 4);
                this.path = menu.path;
                this.docVisible = true;
                return;
            }
            let path = menu.path;
            if (menu.name.indexOf('抽取') !== -1) {
                path += `?contractId=${this.docList[0].contractId}&documentId=${this.docList[0].documentId}`;
            } else {
                path += `/${this.docList[0].contractId}/${this.docList[0].documentId}`;
            }
            sessionStorage.setItem('signingPagePath', this.$route.fullPath);
            sessionStorage.setItem('fromDocument', true);
            this.$router.push(path);
        },
        async getBtnDisplay() {
            await buttonDisplay(this.docList[0].contractId).then((res) => {
                this.judgeBtnDisplay = res.data?.display || false;
            }).catch(() => {
                this.judgeBtnDisplay = false;
            });
        },
        getJudgeConfig() {
            getRiskOpen(this.docList[0].contractId).then((res) => {
                const openRisk = this.isPerson || this.checkFeat.contractJudgeRisk;
                const { pmsEnt, openPms } = res.data;
                this.canUseRisk = (!pmsEnt || pmsEnt && openPms) && openRisk;
            });
        },
    },
    created() {
        this.getBtnDisplay();
        this.getJudgeConfig();
    },
};
</script>
<style lang="scss" scoped>
    $--background-color-base: #F6F6F6;
    $--color-white: #FFFFFF;
    $--color-danger: #FF5500;
    @keyframes rotate {
        0% {
            transform: rotate(0deg);
        }
        25% {
            transform: rotate(15deg);
        }
        50% {
            transform: rotate(0deg);
        }
        75% {
            transform: rotate(-15deg);
        }
        100% {
            transform: rotate(0deg);
        }
    }
    .hubble-apply-entry {
        position: fixed;
        top: 200px;
        left: 10px;
        z-index: 99999;
        width: 106px;
        padding: 9px 7px 7px;
        transition: all .5s ease;
        background-color: #1699FF;
        border-radius: 3px;
        box-sizing: border-box;
        &.hidden {
            transform: translateX(-200px);
        }
        .close {
            font-size: 7px;
            position: absolute;
            right: 10px;
            cursor: pointer;
            color: $--background-color-base;
        }
        .info {
            line-height: 17px;
            color: $--color-white;
            font-size: 12px;
            margin-bottom: 9px;
        }
        .menu {
            width: 90px;
            height: 24px;
            display: flex;
            align-items: center;
            margin-top: 7px;
        }
        .entry {
            width: 70px;
            height: 24px;
            background-color: $--color-white;
            color: #0C8AEE;
            font-size: 12px;
            line-height: 24px;
            filter: drop-shadow(0 4px 12px #1699ff4d);
            border-radius: 3px;
            cursor: pointer;
            &.fullContent {
                width: 100%;
            }
            .icon {
                display: inline-block;
                margin: 0 1px 0 5px;
                width: 12px;
                height: 12px;
                border-radius: 2px;
                background: #1699FF;
            }
            i {
                position: relative;
                top: -6px;
                left: 0.5px;
                color: $--color-white;
                transform: scale(0.7);
            }
        }
        &.foreignEntry {
            width: 140px;
            .menu {
                width: 120px;
                .entry {
                    flex-grow: 1;
                    word-break: break-all;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
                .icon-play {
                    flex-shrink: 0;
                }
            }
        }
        .icon-play{
            display: flex;
            width: 20px;
            height: 24px;
            line-height: 24px;
            text-align: center;
            border-radius: 3px;
            border-left: 1px dashed #1699FF;
            background: #e8f4fe;
            color: #1699FF;
            cursor: pointer;
            align-items: center;
            justify-content: center;
            &:hover {
                background: linear-gradient(0deg, #B5DFFF 0%, $--color-white 100%);
                box-shadow: 0 2px 10px 0 #065cb280;
            }
            &.new-page:hover {
                cursor: default;
                background: #7FC7FF;
                box-shadow: none;
            }
            img {
                height: 14px;
                width: 14px;
            }
        }
        .clickable {
            &:hover {
                background: linear-gradient(0deg, #B5DFFF 0%, $--color-white 100%);
                box-shadow: 0 2px 10px 0 #065cb280;
            }
        }
        .new-page {
            background: #7FC7FF;
        }
    }
    .hubble-apply-entry-hidden {
            word-break: break-all;
            position: fixed;
            left: 10px;
            top: 200px;
            width: 30px;
            height: 30px;
            cursor: pointer;
            border-radius: 4px;
                background: #1699FF;
                box-shadow: 0 4px 12px 0 #1699ff4d;
            transition: all .5s ease;
            z-index: 99999;
            box-sizing: border-box;
            &.hidden {
                transform: translateX(-100px);
            }
        img{
            width: 20px;
            height: 20px;
            margin-top: 5px;
            margin-left: 5px;
            float: left;
            animation: rotate 1s linear infinite;
        }
        i {
            top: -11px;
            left: 20px;
            position: absolute;
            border-radius: 30px;
            border-color: $--color-danger;
            color: $--color-danger;
            background-color: $--color-white;
            font-size: 14px;
        }
    }
    #video {
        width: 100%;
    }
    .qrcode-dialog {
        .el-icon-ssq-guanbi1 {
            position: absolute;
            right: 20px;
            top: 14px;
            font-size: 34px;
            color: #999;
        }
        .qrcode-dialog__body {
            font-size: 18px;
            font-weight: 500;
            line-height: 28px;
            p {
                margin-bottom: 14px;
            }
            img {
                display: block;
                height: 350px;
                width: auto;
                margin: auto;
            }
            &-btn {
                text-align: center;
                .el-button--text {
                    font-size: 18px;
                }
            }
        }
    }
</style>
