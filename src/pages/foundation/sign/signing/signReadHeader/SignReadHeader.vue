<template>
    <div class="sign-read-header">
        <i class="el-icon-ssq-shangyiji" @click="toBack"></i>
        <el-checkbox v-model="isReaded" :disabled="uncompleteRead">{{ $t('sign.iHadReadContract', { alias: aliasText }) }}</el-checkbox>
        <el-button type="primary" :disabled="uncompleteRead || !isReaded" @click="handleRead">{{ buttonText }}</el-button>
        <span class="scroll-tip" v-if="isScrollLimitSign">* {{ $t('sign.scrollToBottomTip') }}</span>
    </div>
</template>

<script>
import { mapMutations, mapState } from 'vuex';

export default {
    props: {
        contractAlias: {
            type: String,
            default: 'CONTRACT',
        },
        isScrollLimitSign: {
            default: false,
            type: Boolean,
        },
    },
    data() {
        return {
            isReaded: false,
            time: 15,
            timeInterVal: null,
        };
    },
    computed: {
        ...mapState({
            scrollProgress: state => state.scrollProgress,
        }),
        uncompleteRead() {
            // 滚动触底限制
            if (this.isScrollLimitSign) {
                return this.scrollProgress < 100;
            }
            return this.time > 0;
        },
        buttonText() {
            if (this.isScrollLimitSign) {
                return this.$t(`${this.$t('sign.nextStep')}（${this.scrollProgress}%）`);
            }
            return this.time > 0 ? `${this.$t('sign.nextStep')}（${this.time}s）` : this.$t('sign.nextStep');
        },
        aliasText() {
            return this.$t(`consts.contractAlias.${this.contractAlias.toLowerCase()}`);
        },
    },
    methods: {
        ...mapMutations(['setSignPageScrollProgress']),
        toBack() {
            this.$router.go(-1);
        },
        handleRead() {
            this.$emit('handleRead');
        },
    },
    created() {
        if (!this.isScrollLimitSign) {
            this.timeInterVal = setInterval(() => {
                this.time -= 1;
                this.time === 0 && clearInterval(this.timeInterVal);
            }, 1000);
        } else {
            this.setSignPageScrollProgress(0);
        }
    },
    beforeDestroy() {
        clearInterval(this.timeInterVal);
    },
};
</script>

<style lang="scss">
    .sign-read-header{
        height: 50px;
        line-height: 50px;
        text-align: center;
        position: relative;
        background: #fff;
        z-index: 999;
        box-shadow: 0 2px 4px 0 rgba(0,0,0,0.10);
        >i{
            position: absolute;
            left: 20px;
            line-height: 50px;
            font-size: 16px;
        }
        >.el-checkbox .el-checkbox__label{
            font-size: 14px;
        }
        .scroll-tip {
            margin-left: 10px;
        }
        button{
            margin-left: 30px;
            padding: 6px 0;
            width: 125px;
            text-align: center;
            background: #127FD2;
            &:hover, &:active, &:visited, &:focus{
                background: #127FD2;
            }
            &.is-disabled{
                background: #127FD2;
                &:hover, &:active{
                    background: #127FD2;
                }
                span{
                    opacity: 0.5;
                }
            }
        }
    }
</style>
