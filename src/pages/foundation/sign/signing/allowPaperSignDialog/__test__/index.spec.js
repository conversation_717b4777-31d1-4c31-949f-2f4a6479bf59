import AllowPaperSignDialog from '../index.vue';
import { initWrapper } from 'src/testUtils';
describe('AllowPaperSignDialog', () => {
    const mockEmitFun = jest.fn();
    const baseStoreOptions = {};
    const baseWrapperOptions = {
        mocks: {
            $emit: mockEmitFun,
            $sensors: {
                track: jest.fn(),
            },
        },
    };
    const wrapper = initWrapper(AllowPaperSignDialog, { ...baseStoreOptions }, { ...baseWrapperOptions });
    test('点击确定', () => {
        wrapper.vm.handleConfirm('electron');
        expect(mockEmitFun).toHaveBeenCalledWith('confirm', 'electron');
        expect(wrapper.vm.dialogVisible).toBeFalsy();
    });
});
