<!--允许纸质签弹窗-->
<template>
    <el-dialog
        class="allow-paper-sign-dialog"
        v-model="dialogVisible"
        :title="$t('allowPaperSignDialog.title')"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
    >
        <div class="content">
            <div class="desc">
                <i class="el-icon-warning"></i>
                <span>{{ $t('allowPaperSignDialog.content', {senderName, receiverName}) }}</span>
                <div class="tip">{{ $t('allowPaperSignDialog.tip') }}</div>
                <div class="go-paper" @click="handleConfirm('paper')">
                    {{ $t('allowPaperSignDialog.icon') }}
                </div>
            </div>
        </div>
        <span slot="footer">
            <el-button @click="handleClose">{{ $t('allowPaperSignDialog.cancel') }}</el-button>
            <el-button type="primary" @click="handleConfirm('electron')">{{ $t('allowPaperSignDialog.goSign') }}</el-button>
        </span>
    </el-dialog>
</template>

<script>
export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        senderName: {
            type: String,
            default: '',
        },
        receiverName: {
            type: String,
            default: '',
        },
        contractId: {
            type: String,
            default: '',
        },
    },
    computed: {
        dialogVisible: {
            set(value) {
                this.$emit('update:visible', value);
            },
            get() {
                return this.visible;
            },
        },
    },
    methods: {
        handleConfirm(type) {
            this.$emit('confirm', type);
            this.dialogVisible = false;
            this.$sensors.track({
                eventName: 'Ent_ContractSignWindow_BtnClick',
                eventProperty: {
                    page_name: '合同签署页',
                    window_name: '允许纸质签',
                    icon_name: type === 'paper' ? '转纸质签署' : '去电子签',
                    contract_id: this.contractId,
                },
            });
        },
        handleClose() {
            this.dialogVisible = false;
            this.$sensors.track({
                eventName: 'Ent_ContractSignWindow_BtnClick',
                eventProperty: {
                    page_name: '合同签署页',
                    window_name: '允许纸质签',
                    icon_name: '取消',
                    contract_id: this.contractId,
                },
            });
        },
    },
};
</script>

<style lang="scss">
    .allow-paper-sign-dialog .el-dialog {
        width: 400px;
        .content{
             position: relative;
            .desc{
                .el-icon-warning {
                    display: inline-block;
                }
                font-size: 14px;
                color: #333333;
                line-height: 20px;
                padding-bottom: 20px;
                .tip {
                    padding-top: 10px;
                    color: #999;
                }
                .go-paper {
                    float: right;
                    color: #127fd2;
                    text-align: right;
                    cursor: pointer;
                    &:hover {
                        color: #1687dc;
                    }
                }
            }
        }
    }

</style>
