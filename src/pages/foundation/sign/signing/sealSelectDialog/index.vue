<template>
    <el-dialog class="seals-select-dialog" size="tiny" :visible.sync="visible" @close="handleClose" id="seals-select-dialog">
        <h3 slot="title">
            <i v-show="step === 2" class="el-icon-arrow-left" @click="handleTabBack"></i>
            {{ step === 1 ? $t('signPC.selectSeal') :( sealOptType === 'otherSign' ? $t('sign.chooseApplyPerson'): $t('sign.chooseAdminSign')) }}</h3>
        <div v-if="hasManager && !mySeals.length && otherSeals.length" class="seals-part">
            <p class="seals-part-tip">{{ $t('signPC.adminGuideTip') }}</p>
            <div class="seal"
                :key="seal.sealId"
                v-for="seal in otherSeals"
            >
                <p class="seal-name">{{ seal.name }}</p>
                <img :src="seal.sealImg" alt="">
                <div class="seal-opts">
                    <span class="seal-opt" @click="handleAdminDistributeSeal(seal)">{{ $t('signPC.use') }}</span>
                </div>
            </div>
        </div>
        <template v-else-if="step === 1">
            <el-tabs v-model="activeTabName">
                <!-- 印章不存在时和从骑缝章点击过来的不展示该选项 -->
                <div class="seals-part-select" v-if="showReplaceAllBtn">
                    <el-checkbox v-model="replaceAllSealAndSignature">{{ $t('handwrite.replaceAllSeal') }}</el-checkbox>
                </div>
                <el-tab-pane :label="$t('handwrite.canUseSeal')" name="canUseSeal" v-if="mySeals.length">
                    <div class="seals-part">
                        <div
                            class="seal"
                            :class="curSelectedSealId === seal.sealId ? 'activeSeal' : ''"
                            :key="seal.sealId"
                            v-for="seal in mySeals"
                        >
                            <div class="seal-container" @click="handleSealUse(seal)">
                                <p class="seal-name">{{ seal.name }}</p>
                                <i class="el-icon-ssq-danxuanxuanzhong" v-if="curSelectedSealId === seal.sealId"></i>
                                <img :src="seal.sealImg" alt="">
                            </div>
                        </div>
                    </div>
                </el-tab-pane>
                <el-tab-pane :label="$t('handwrite.applyForSeal')" name="applySeal" v-if="otherSeals.length && sealListVisibleFromAction === 1">
                    <!-- 骑缝章切换不展示 需要申请用印的印章 -->
                    <div class="seals-part">
                        <div class="seal"
                            :key="seal.sealId"
                            v-for="seal in otherSeals"
                        >
                            <p class="seal-name">{{ seal.name }}</p>
                            <img :src="seal.sealImg" alt="">
                            <div class="seal-opts">
                                <template v-if="isCanOtherSign">
                                    <span class="seal-opt" @click="handleToSecondTab(seal, 'otherSign')">{{ $t('sign.useSealByOther') }}</span>
                                    <span class="seal-gap"> | </span>
                                </template>
                                <span class="seal-opt" @click="handleToSecondTab(seal, 'sealAccess')">{{ $t('sign.getSeal') }}</span>
                            </div>
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </template>
        <div class="require-part" v-show="step === 2">
            <div class="left-container">
                <p class="title">{{ sealOptType === 'otherSign' ? $t('sign.nowApplySealList') : $t('sign.nowAdminSealList') }}</p>
                <div class="seal">
                    <p class="seal-name">{{ targetSeal.name }}</p>
                    <img :src="targetSeal.sealImg" alt="">
                </div>
            </div>
            <div class="right-container">
                <div class="select-body">
                    <template v-if="peopleList.length">
                        <p class="title">
                            {{ sealOptType === 'otherSign' ? $t('sign.chooseApplyPersonToDeal') : $t('sign.chooseApplyPersonToMandate') }}
                        </p>
                        <p v-if="sealOptType ==='sealAccess'"> {{ $t('sign.sealScope') }}：<el-radio v-model="sealRangeType" label="1">{{ $t('sign.currentContract') }}</el-radio>
                            <el-radio v-model="sealRangeType" label="0">{{ $t('sign.allContract') }}</el-radio>
                        </p>
                        <br>
                        <div class="select-options">
                            <div class="ent-item"
                                :class="{'active-item': index === selectEntIndex}"
                                v-for="(ent, index) in peopleList"
                                :key="ent.empId"
                                @click="handleSelectEnt(index)"
                            >
                                <p>
                                    {{ ent.empName }}
                                    <span>{{ ent.account }}</span>
                                    <span class="select-icon" v-show="index === selectEntIndex"><i class="el-icon-ssq-xuanzhong"></i></span>
                                </p>
                            </div>
                        </div>
                    </template>
                    <p class="title" v-else>
                        {{ $t('sign.contactGroupAdminToDistributeSeal') }}
                    </p>
                </div>
                <div class="select-opts">
                    <el-button @click="handleClose">{{ $t('sign.cancel') }}</el-button>
                    <el-button type="primary" :disabled="!peopleList.length" @click="handleConfirm">{{ $t('sign.confirm') }}</el-button>
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
export default {
    name: 'SealSelectDialog',
    props: {
        sealListVisibleFromAction: {
            type: Number,
            default: 1,
        },
        mySeals: {
            type: Array,
            default: () => [],
        },
        otherSeals: {
            type: Array,
            default: () => [],
        },
        signType: {
            type: String,
            default: '',
        },
        focusingMark: {
            type: Object,
            default: () => {},
        },
        docLen: {
            type: Number,
            default: 0,
        },
        curRidingSealId: {
            type: String,
            default: '',
        },
        sensorsTrackContractInfo: {
            type: Object,
            default: () => {},
        },
        contractId: {
            type: String,
            default: '',
        },
        receiver: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            visible: true,
            step: 1,
            targetSeal: {
                empSealVOS: [],
            },
            sealOptType: 'otherSign', // otherSign: 他人签署，sealAccess：获取印章
            selectEntIndex: 0,
            sealAuthors: <AUTHORS>
            activeTabName: 'canUseSeal',
            replaceAllSealAndSignature: false,
            sealRangeType: '0',
        };
    },
    computed: {
        ...mapState(['commonHeaderInfo']),
        ...mapGetters(['hasManager']),
        peopleList() {
            return this.sealOptType === 'otherSign' ? this.targetSeal.empSealVOS : this.sealAuthors;
        },
        // 是否可以他人签署
        isCanOtherSign() {
            // 配置了无须签署校验的，不能他人签署
            if (this.receiver.uncheck) {
                return false;
            }
            // 签字并盖章
            if (this.signType === 'SEAL_AND_SIGNATURE') {
                return false;
            }
            // 询证函
            if (this.focusingMark.mark.type === 'CONFIRMATION_REQUEST_SEAL' && this.docLen > 1) {
                return false;
            }
            return true;
        },
        curSelectedSealId() {
            return this.focusingMark?.mark?.signatureId || this.curRidingSealId || '';
        },
        showReplaceAllBtn() {
            // 非骑缝章&&已默认盖上章
            return this.sealListVisibleFromAction === 1 && !!this.focusingMark?.mark.value && this.activeTabName === 'canUseSeal';
        },
    },
    methods: {
        // 主管理员给自己分配印章
        async handleAdminDistributeSeal(selectSeal) {
            this.handleClickEventTrack(1);
            await this.$http.post(`/ents/seals/${selectSeal.sealId}/owners`, {
                entId: this.commonHeaderInfo.currentId,
                empIds: [this.commonHeaderInfo.platformUser.empId],
            });
            this.handleSealUse(selectSeal);
        },
        handleSealUse(seal) {
            this.$sensors.track({
                eventName: 'Ent_ContractSignWindow_BtnClick',
                eventProperty: {
                    page_name: '合同签署页',
                    window_name: '选择印章',
                    icon_name: '选中印章',
                    ...this.sensorsTrackContractInfo,
                },
            });
            this.$emit('handleClickSeal', seal.sealId, seal.name, this.replaceAllSealAndSignature);
        },
        handleToSecondTab(seal, type) {
            // 刷脸相关开启，不支持他人盖章，否则存证页有问题
            const cantOtherSign = this.receiver.faceFirst || this.receiver.faceVerify || this.receiver.messageAndFaceVerify;
            if (type === 'otherSign' && cantOtherSign) {
                return this.$MessageToast.error(this.$t('sign.cannotOtherSealReason'));
            }
            this.handleClickEventTrack(type === 'otherSign' ? 2 : 3);
            this.targetSeal = seal;
            this.sealOptType = type;
            this.step = 2;
            if (type === 'sealAccess') {
                this.$http.get(`/ents/apply-seal/seal-managers?sealId=${this.targetSeal.sealId}`)
                    .then(res => {
                        this.sealAuthors = res.data;
                    });
            }
        },
        handleTabBack() {
            this.sealOptType = '';
            this.step = 1;
            this.targetSeal =  {
                empSealVOS: [],
            };
            this.selectEntIndex = 0;
            this.sealAuthors = [];
            this.$sensors.track({
                eventName: 'Ent_ContractSignWindow_BtnClick',
                eventProperty: {
                    page_name: '合同签署页',
                    window_name: '选择印章',
                    icon_name: '返回',
                    ...this.sensorsTrackContractInfo,
                },
            });
        },
        handleSelectEnt(index) {
            this.$sensors.track({
                eventName: 'Ent_ContractSignWindow_BtnClick',
                eventProperty: {
                    page_name: '合同签署页',
                    window_name: '选择印章',
                    icon_name: '选择企业',
                    ...this.sensorsTrackContractInfo,
                },
            });
            this.selectEntIndex = index;
        },
        handleConfirm() {
            this.handleClickEventTrack(5);
            const { empId } = this.peopleList[this.selectEntIndex];
            if (this.sealOptType === 'sealAccess') {
                this.$http.post(`/ents/apply-seal/apply`, {
                    approveEmpId: empId,
                    sealId: this.targetSeal.sealId,
                    contractId: this.contractId,
                    sealRangeType: this.sealRangeType,
                }).then(() => {
                    this.$sensors.track({
                        eventName: 'Ent_ContractSignWindow_Result',
                        eventProperty: {
                            page_name: '合同签署页',
                            window_name: '申请用印',
                            is_success: true,
                            icon_name: '确定',
                            request_url: `/ents/apply-seal/apply`,
                            ...this.sensorsTrackContractInfo,
                        },
                    });
                    this.$MessageToast({
                        type: 'fail',
                        className: 'seal-accessing-tip',
                        message: this.$t('sign.sealApplySentPleaseWait'),
                    });
                    this.handleClose();
                })
                    .catch((err) => {
                        const errMsg = err.response?.data?.message;
                        const status = err.response?.status;
                        const code = err.response?.data?.code;
                        this.$sensors.track({
                            eventName: 'Ent_ContractSignWindow_Result',
                            eventProperty: {
                                page_name: '合同签署页',
                                window_name: '申请用印',
                                is_success: false,
                                icon_name: '确定',
                                fail_http_code: status,
                                fail_error_code: code,
                                fail_reason: errMsg,
                                request_url: `/ents/apply-seal/apply`,
                                ...this.sensorsTrackContractInfo,
                            },
                        });
                    });
            } else {
                this.$emit('postSeal', {
                    sealId: this.targetSeal.sealId,
                    empId,
                });
                this.handleClose();
            }
        },
        handleClose() {
            this.handleClickEventTrack(4);
            this.visible = false;
            this.$emit('close');
        },
        handleClickEventTrack(index) {
            const eventMap = {
                1: '使用',
                2: '他人盖章',
                3: '获取印章',
                4: '取消',
                5: '确定',
            };
            this.$sensors.track({
                eventName: 'Ent_ContractSignWindow_BtnClick',
                eventProperty: {
                    page_name: '合同签署页',
                    window_name: '选择印章',
                    icon_name: eventMap[index],
                    ...this.sensorsTrackContractInfo,
                },
            });
        },
    },
    created() {
        if (!this.mySeals.length && this.otherSeals.length && this.sealListVisibleFromAction === 1) {
            this.activeTabName = 'applySeal';
        }
    },
};
</script>

<style lang="scss">
    // 印章列表
    .seals-select-dialog .el-dialog {
        width: 675px;
        [dir=rtl] & {
            .el-tabs__nav {
                float: right;
                .el-tabs__item.is-active {
                    border-bottom: 2px solid #127FD2;
                }
            }
            .el-tabs__active-bar {
                display: none;
            }
        }
        &__header h3 {
            display: inline-block;
            font-size: 16px;
            line-height: 22px;
            i {
                padding-right: 8px;
            }
        }
        .seal {
            display: inline-block;
            width: 130px;
            height: 165px;
            margin-left: 8px;
            margin-bottom: 20px;
            padding-top: 10px;
            border: 1px solid #EEEEEE;
            border-radius: 8px;
            text-align: center;
            img {
                margin-top: 8px;
                margin-bottom: 6px;
                width: 100px;
                height: 100px;
            }
            &-opts {
                color: #127FD2;
                font-size: 12px;
                .seal-opt {
                    cursor: pointer;
                }
            }
            &-gap {
                color: #aeb3b9;
            }
            &:hover {
                background: #F6F9FC;
            }
        }
        .seals-part {
            padding: 20px 30px 10px 20px;
            display: flex;
            flex-wrap: wrap;
            &-tip {
                margin-bottom: 15px;
                font-size: 12px;
            }
            &-select {
                width: 543px;
                margin-left: 30px;
                background-color: #F6F6F6;
                height: 40px;
                line-height: 40px;
                padding-left: 15px;
                border-radius: 4px;
                .el-checkbox {
                    margin-right: 8px;
                }
                [dir=rtl] & {
                    margin-left: 0;
                    margin-right: 30px;
                    .el-checkbox {
                        margin-left: 8px;
                        margin-right: 0;
                    }
                }
            }
            .seal-name {
                line-height: 15px;
            }
            .seal-container:hover {
                cursor: pointer;
            }
            .activeSeal {
                border: 1px solid #127FD2;
                background-color: #F6F9FC;
                position: relative;
                .el-icon-ssq-danxuanxuanzhong {
                    color: #127FD2;
                    position: absolute;
                    top: 8px;
                    right: 8px;
                    [dir=rtl] & {
                        right: auto;
                        left: 8px;
                    }
                }
            }
        }
        .require-part {
            font-size: 12px;
            height: 400px;
            .left-container {
                width: 190px;
                height: 100%;
                background: #F8F8F8;
                text-align: center;
                float: left;
                [dir=rtl] & {
                    float: right;
                }
                .title {
                    padding: 25px 0 18px;
                }
                .seal {
                    margin-left: 0;
                    background: #fff;
                    height: 150px;
                    [dir=rtl] & {
                        margin-left: auto;
                        margin-right: 0;
                    }
                }
            }
            .right-container {
                float: left;
                width: 424px;
                height: 100%;
                padding: 20px 30px;
                box-sizing: border-box;
                [dir=rtl] & {
                    float: right;
                }
                .select-body {
                    height: 330px;
                    .select-options {
                        height: 265px;
                        overflow-y: auto;
                    }
                    .title {
                        padding: 5px 0 15px;
                    }
                    .ent-item {
                        padding-left: 15px;
                        margin-bottom: 10px;
                        height: 38px;
                        line-height: 38px;
                        border: 1px solid #DDD;
                        border-radius: 4px;
                        cursor: pointer;
                        [dir=rtl] & {
                            padding-right: 15px;
                            padding-left: 0;
                        }
                        span {
                            color: #999;
                        }
                        p {
                            position: relative;
                        }
                        .select-icon {
                            position: absolute;
                            top: 0;
                            right: 0px;
                            color: #fff;
                            width: 12px;
                            height: 12px;
                            border: 12px solid;
                            border-color: #127fd2 #127fd2 transparent transparent;
                            padding: 0;
                            box-sizing: border-box;
                            border-radius: 2px;
                            [dir=rtl] & {
                                right: auto;
                                left: 0px;
                                border-color: transparent transparent #127fd2 #127fd2;
                            }
                            i {
                                color: #fff;
                                position: absolute;
                                top: -11px;
                            }
                        }
                        &.active-item,
                        &:hover {
                            background: #F6F9FC;
                            border-color: #127FD2;
                        }
                    }
                }
                .select-opts {
                    text-align: right;
                    [dir=rtl] & {
                        text-align: left;
                    }
                    .el-button {
                        width: unset;
                        height: 30px;
                        padding: 0 10px;
                        &--default {
                            background: #F8F8F8;
                            border: 1px solid #CCCCCC;
                        }
                        &--primary {
                            background: #127FD2;
                        }
                    }
                }
            }
        }

    }
    .seals-select-dialog .el-tabs {
        .el-tabs__nav-scroll {
            padding-left: 10px;
        }
        .el-tabs__header {
            margin: 0 0 20px;
        }
    }
</style>
