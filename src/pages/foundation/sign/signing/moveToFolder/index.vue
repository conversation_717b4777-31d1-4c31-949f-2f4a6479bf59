<template>
    <div class="move-folder-single" v-show="folderList && folderList.length">
        <p>{{ $t('sign.archiveTo') }}</p>
        <el-radio-group v-model="curFolderId">
            <el-radio
                :label="item.folderId"
                v-for="item in folderList"
                :key="item.folderId"
            >{{ item.folderName }}</el-radio>
        </el-radio-group>
    </div>
</template>
<script>
export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['contractId', 'name', 'sensorsTrackContractInfo', 'sensorsPageName', 'sensorsEventName'],
    data() {
        return {
            folderList: [],
            curFolderId: -1,
        };
    },
    watch: {
        async curFolderId(newVal, oldVal) {
            if (oldVal === -1) {
                return;
            }
            try {
                this.$loading();
                await this.$http.post(`/contract-center-bearing/folder/${newVal}/addContractsForNew`, {
                    contractIds: [this.contractId],
                });
                this.$loading().close();
                const [{ folderName }] = this.folderList.filter(v => newVal === v.folderId);
                this.$message({
                    message: `${this.$t('sign.hadArchivedToFolder', { who: this.name, folderName: folderName })}`,
                    type: 'success',
                });
                this.sensorsEventName && this.$sensors.track({
                    eventName: `Ent_${this.sensorsEventName}_BtnClick`,
                    eventProperty: {
                        page_name: `${this.sensorsPageName}`,
                        first_category: '其他操作',
                        icon_name: '归档',
                        ...this.sensorsTrackContractInfo,
                    },
                });
            } catch (e) {
                this.$loading().close();
            }
        },
    },
    async created() {
        // 获取文件夹列表
        const { data: { data: { myFolders, presetFolders }, code } } = await this.$http.get('/contract-center-bearing/folder/list');
        if ('' + code !== '0') {
            return;
        }
        this.folderList = myFolders.concat(presetFolders);
        // 获取当前contractId所在的folderId
        const res = await this.$http.get('/contract-center-bearing/folderForNew', {
            noToast: 1,
            params: {
                contractId: this.contractId,
            },
        });
        this.curFolderId = res.data.data.folderId || this.folderList[0].curFolderId;
    },
};
</script>
<style lang="scss">
.move-folder-single {
    background: #ffffff;
    text-align: left;
    max-height: 300px;
    overflow-y: auto;
    p {
        font-size: 12px;
        color: #999999;
        border-top: 1px solid #E1E1E1;
        padding: 10px 12px;
    }
    .el-radio-group {
        padding: 0 12px;
        .el-radio__label {
            color: #666666;
        }
    }
    .el-radio {
        display: block;
        margin: 0;
        text-align: left;
        margin-bottom: 10px;
        height: 22px;
    }

    [dir="rtl"] & {
        text-align: right;
        .el-radio {
            text-align: right;
            padding-right: 5px;
        }
    }
}
</style>
