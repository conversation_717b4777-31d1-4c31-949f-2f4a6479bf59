<template>
    <div class="handWrite-panel qrcodetab">
        <QRCode class="qrcode-container" ref="QRCode" :initialBusiness="initialBusiness" :parentGift="parentGift"></QRCode>
        <div class="des" v-if="desShow">{{ $t('sign.pleaseScanToHandleWrite') }}</div>
        <div v-if="addBtnshow" class="btn-line">
            <el-button type="primary" @click.stop.prevent="onSaveSignature">{{ $t('sign.save') }}</el-button>
        </div>
    </div>
</template>
<script>
import QRCode from 'components/qRCode/QRCode.vue';
import Bus from 'components/bus/bus.js';
export default {
    components: {
        QRCode,
    },
    props: {
        parentGift: {
            type: Object,
            default: function() {
                return {
                    contractId: '',
                    labelId: '',
                    receiverId: '',
                    labelValue: '',
                    handWritingRecognition: false, // 是否需要笔迹识别
                };
            },
        },
        initialBusiness: {
            type: String,
            default: 'usercenter',
        },
    },
    data() {
        return {
            desShow: true,
            addBtnshow: false,
            signType: this.$route.query.type || 'sign', // send sign approval view
        };
    },
    computed: {
        sensorsEventName() {
            return this.signType === 'sign' ? 'ContractSign' : (this.signType === 'approval' ? 'ContractApproval' : '');
        },
        sensorsPageName() {
            return this.signType === 'sign' ? '合同签署页' : (this.signType === 'approval' ? '合同审批页' : '');
        },
    },
    methods: {
        onWriteDone() {
            if (this.initialBusiness === 'addSignature') {
                this.desShow = false;
                this.addBtnshow = true;
            }
        },
        // 自定义签署位置，扫码签名后保存
        onSaveSignature() {
            this.sensorsEventName && this.$sensors.track({
                eventName: `Ent_${this.sensorsEventName}Window_BtnClick`,
                eventProperty: {
                    page_name: `${this.sensorsPageName}`,
                    window_name: '手写签名',
                    icon_name: '保存',
                    contract_id: this.parentGift.contractId,
                },
            });
            return this.$http.post(`/users/signatures/qrcode`, {
                file: this.$refs.QRCode.signatureImgData,
                sigId: '',
            })
                .then(() => Bus.$emit('save-signature'))
                .catch(() => {});
        },
    },
    created() {
        Bus.$on('write-done', this.onWriteDone);
    },
};
</script>
<style lang="scss">
	.qrcodetab {
        position: relative;
        height: 100%;
		.qrcode-container {
            position: absolute;
            top: 40%;
            left: 50%;
            transform: translate(-50%, -50%);
		}
        .des {
            position: absolute;
            width: 100%;
            bottom: 40px;
            text-align: center;
            color: #666;
        }
        .btn-line {
            position: absolute;
            width: 100%;
            bottom: 20px;
            .el-button--primary {
                display: block;
                margin: 0 auto;
                padding: 10px 50px;
                background-color: #127fd2;
                border-color: #127fd2;
            }
        }
	}
</style>
