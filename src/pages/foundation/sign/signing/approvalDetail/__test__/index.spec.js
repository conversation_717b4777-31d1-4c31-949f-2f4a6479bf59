import ApprovalDetail from '../ApprovalDetail.vue';
import { initWrapper } from 'src/testUtils';
import flushPromises from 'flush-promises';
describe('ApprovalDetail', () => {
    const mockGetFun = jest.fn();
    mockGetFun.mockResolvedValue({ data: {
        sender: {},
        signers: [],
        approvers: [],
    } });
    const baseStoreOptions = {};
    const baseWrapperOptions = {
        mocks: {
            $http: {
                get: mockGetFun,
            },
            $route: {
                query: {
                    contractId: 'testId',
                },
            },
        },
    };
    const wrapper = initWrapper(ApprovalDetail, { ...baseStoreOptions }, { ...baseWrapperOptions });
    test('获取详情', async() => {
        wrapper.vm.getDetail();
        await flushPromises();
        expect(mockGetFun).toHaveBeenCalledWith('true/contracts/testId/approval');
    });
    test('获取不同图标', () => {
        const icon1 = wrapper.vm.icon('APPROVAL_PASS');
        expect(icon1).toEqual('el-icon-ssq-tongguo green');
        const icon2 = wrapper.vm.icon('APPROVAL_WAIT');
        expect(icon2).toEqual('el-icon-ssq-dengdaitarenqianshu blue');
        const icon3 = wrapper.vm.icon('APPROVAL_DENY');
        expect(icon3).toEqual('el-icon-ssq-beijuqian red');
        const icon4 = wrapper.vm.icon('NOT_START');
        expect(icon4).toEqual('el-icon-ssq-dengdaitarenqianshu blue');
    });
});
