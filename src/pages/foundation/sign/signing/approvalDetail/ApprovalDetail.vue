<template>
    <div class="approvalDetail-cpn">
        <div class="title">{{ $t('sign.submitter') }}</div>
        <div class="approvalDetail-wrap clear">
            <div class="singer-content fl">
                <Portrait :src="sender.photoHref"></Portrait>
                <div class="intr">
                    <div class="name">{{ sender.userName || '' }}</div>
                    <div class="account">{{ sender.userAccount || '' }}</div>
                </div>
            </div>
        </div>
        <div class="title">{{ $t('sign.signatory') }}</div>
        <div class="approvalDetail-wrap clear">
            <div class="singer-content fl" v-for="(item,index) in signers" :key="index">
                <Portrait :src="item.photoHref"></Portrait>
                <div class="intr">
                    <div class="name">{{ item.userName || '' }}</div>
                    <div class="account">{{ item.userAccount || '' }}</div>
                </div>
            </div>
        </div>
        <div class="title">{{ $t('sign.reviewSchedule') }}</div>
        <div class="approvalDetail-wrap clear">
            <div class="wrap-node fl" v-for="(item, index) in approvers" :key="index">
                <div class="img-wrap">
                    <el-tooltip placement="top-start" effect="light" :disabled="item.signStatus == 'APPROVAL_WAIT' || item.signStatus == 'NOT_START'">
                        <div slot="content" style="line-height: 20px;">
                            {{ formatDateToString({ date: item.finishTime, format: 'YYYY年MM月DD日 hh:mm:ss' }) }}<br />
                            {{ item.approvalOpinion || '' }}
                        </div>
                        <Portrait :src="item.photoHref"></Portrait>
                    </el-tooltip>
                    <div class="node-name">{{ item.userName || '' }}</div>
                    <div class="node-account">{{ item.userAccount || '' }}</div>
                    <i class="i-status" :class="icon(item.signStatus)"></i>
                </div>
                <div class="arrow" v-if="( ( index + 1 ) % 5 === 0 || index == approvers.length-1 ) ? false : true"></div>
            </div>
        </div>
    </div>
</template>
<script>
import { formatDateToString } from 'src/common/utils/date.js';
import Portrait from './portrait/Portrait.vue';
export default {
    components: { Portrait },
    props: {
        'receiverId': {
            type: String,
            default: '',
        },
        // eslint-disable-next-line vue/require-default-prop
        'approvalType': {
            type: String,
            validator(value) {
                // 10: 发送前审批, 11: 签署前审批
                return ['10', '11'].indexOf(`${value}`) > -1;
            },
        },
    },
    data() {
        return {
            contractId: this.$route.query.contractId || this.$route.params.contractId,
            sender: {

            },
            signers: [

            ],
            approvers: [

            ],
        };
    },
    methods: {
        // ajax
        getDetail() {
            // 发送前审批
            let url = `${signPath}/contracts/${this.contractId}/approval`;
            // 签署前审批
            if (this.approvalType === '11') {
                url = `${signPath}/contracts/${this.contractId}/sign-approval/${this.receiverId}`;
            }
            return this.$http.get(url);
        },
        formatDateToString(date) {
            return formatDateToString(date);
        },
        icon(type) {
            switch (type) {
                case 'APPROVAL_PASS':
                    return 'el-icon-ssq-tongguo green';
                case 'APPROVAL_WAIT':
                    return 'el-icon-ssq-dengdaitarenqianshu blue';
                case 'APPROVAL_DENY':
                    return 'el-icon-ssq-beijuqian red';
                case 'NOT_START':
                    return 'el-icon-ssq-dengdaitarenqianshu blue';
            }
        },
    },
    created() {
        this.getDetail()
            .then(res => {
                const data = res.data;
                this.sender = data.sender;
                this.signers = data.signers;
                this.approvers = data.approvers;
            });
    },
};
</script>
<style lang="scss">
	.approvalDetail-cpn {
		.title {
			font-size: 14px;
			font-weight: bold;
			padding-top: 17px;
		}
		.approvalDetail-wrap {
			padding: 0 2px 19px;
			border-bottom: 1px solid $border-color;
			&:last-child {
				border-bottom: 0;
			}
		}
		.singer-content {
			margin-top: 19px;
			margin-right: 42px;
			img {
				border-radius: 2px;
				margin-right: 8px;
			}
		}
		.wrap-node {
			margin-top: 19px;
			cursor: pointer;
		}
		.intr {
			display: inline-block;
			position: relative;
			top: -4px;
		}
		.img-wrap {
			position: relative;
            margin-right: 10px;
			.green {
				color: #2eab42;
			}
			.blue {
				color: #2298f1;
			}
			.red {
				color: #f76b26;
			}
		}
		.arrow {
			position: relative;
			width: 80px;
			border-top: 1px dashed #999;
			margin: -42px 18px 0 50px;
			&:after {
				position: absolute;
				top: -5px;
				right: -11px;
				content: '';
				@include solid-triangle(4px, transparent, transparent, transparent, #999);
			}
		}
		.i-img {
			width: 39px;
			height: 39px;
			font-size: 39px;
		}
		.i-status {
			position: absolute;
			top: -10px;
			left: 28px;
			font-size: 25px;
			background-color: #fff;
			border-radius: 50%;
		}
	}
</style>
