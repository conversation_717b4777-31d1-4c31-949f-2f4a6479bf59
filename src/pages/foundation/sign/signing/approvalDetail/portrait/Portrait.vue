<template>
    <div style="display: inline-block">
        <img v-if="src" :src="`${src}?access_token=${token}`" alt="" :width="size" :height="size">
        <template v-else>
            <i class="el-icon-ssq-user-filling i-img"></i>
        </template>
    </div>
</template>
<script>
export default {
    props: {
        src: {
            type: String,
            default: '',
        },
        size: {
            type: Number,
            default: 39,
        },
    },
    data() {
        return {
            token: this.$cookie.get('access_token'),
        };
    },
};
</script>
<style lang="scss">
	.i-img {
		width: 39px;
		height: 39px;
		font-size: 39px;
	}
</style>
