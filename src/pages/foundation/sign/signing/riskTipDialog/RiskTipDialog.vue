<template>
    <!-- 对话框：风险提醒 -->
    <div class="risk-tip-dialog">
        <el-dialog class="riskTipDialog"
            :title="$t('sign.remind')"
            :visible.sync="visible"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :beforeClose="handleClose"
        >
            <div>
                <p>{{ $t('sign.stillSignTip', {alias: contractAliasText}) }}</p>
                <div class="high-light-view-container" :class="isEn ? 'high-light-view-container_en': ''" v-if="!isOfdContract">
                    <span>{{ $t('sign.signHighLightTip', {alias: contractAliasText,count:riskFieldNumber}) }}</span>
                    <span
                        class="high-light-view-container-btn"
                        @click="handleView"
                    >{{ $t('sign.view') }}</span>
                </div>
                <div>
                    <p>
                        <span
                            class="showRiskBtn"
                            @click="riskDetailVisible = !riskDetailVisible"
                            :class="riskDetailVisible && 'active'"
                        >
                            {{ $t('sign.riskTip') }}
                            <i class="el-icon-ssq-youjiantou1"></i>
                        </span>
                    </p>
                    <div class="riskDetail" v-if="riskDetailVisible">
                        <i class="el-icon-ssq-tishi1"></i>
                        <span>{{ $t('sign.noviewDifference', {alias: contractAliasText}) }}</span>
                    </div>
                </div>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button
                    type="primary"
                    @click="handleConfirm"
                >{{ $t('sign.signAgain') }}</el-button>
                <el-button
                    @click="handleClose"
                >{{ $t('sign.cancel') }}</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['visible', 'contractAliasText', 'riskFieldNumber', 'isOfdContract'],
    data() {
        return {
            riskDetailVisible: false,
        };
    },
    computed: {
        isEn() {
            return this.$i18n.locale === 'en';
        },
    },
    methods: {
        handleConfirm() {
            this.$emit('confirm');
        },
        handleClose() {
            this.$emit('update:visible', false);
        },
        // 点击查看风险字段
        async handleView() {
            this.handleClose();
            await this.$alert(`${this.$t('sign.highLightTip')}`, this.$t('sign.commonTip'), {
                confirmButtonText: this.$t('sign.understand'),
                type: 'warning',
            });
            this.$emit('handleHighLight');
        },
    },
};
</script>

<style lang="scss">
.risk-tip-dialog {
    .riskTipDialog {
        .el-dialog {
            width: 500px;
            .el-dialog__body {
                .high-light-view-container{
                    margin-top: 20px;
                    background-color: #F6F6F6;
                    height: 54px;
                    line-height: 54px;
                    padding: 0 20px;
                    box-sizing: border-box;
                    position: relative;
                    &_en {
                        line-height: 27px;
                        padding: 0 75px 0 20px;
                    }
                    .high-light-view-container-btn{
                        position: absolute;
                        right: 20px;
                        top: 50%;
                        transform: translateY(-50%);
                        display: inline-block;
                        width: 50px;
                        height: 24px;
                        line-height: 24px;
                        text-align: center;
                        background-color: #127FD2;
                        color: #fff;
                        font-size: 10px;
                        cursor:pointer;
                    }
                }
                .showRiskBtn{
                    display: inline-block;
                    margin-top: 15px;
                    cursor: pointer;
                    color: #127fd2;
                    font-size: 14px;

                    i{
                        font-weight: bold;
                        transition: 0.3s all;
                    }

                    &.active i{
                        transform: rotate(90deg);
                    }
                }

                .riskDetail{
                    padding: 10px 30px;
                    margin-top: 15px;
                    line-height: 18px;
                    background: #ebf1f6;
                    color: #666;

                    .el-icon-ssq-tishi1{
                        margin-left: -22px;
                        font-size: 18px;
                        vertical-align: top;
                        color: #f86b26;
                    }
                }
            }
        }
    }
}
</style>
