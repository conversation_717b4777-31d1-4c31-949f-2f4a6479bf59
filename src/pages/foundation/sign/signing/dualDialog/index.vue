<template>
    <!-- 对话框：二维码打开 -->
    <div>
        <el-dialog
            size="samll"
            class="qr-sign-dialog register ssq-dialog"
            :title="title"
            :visible.sync="visible"
            @close="handleClose"
            @open="qrSignKey=Math.random()"
        >
            <div class="face-content">
                <template v-if="!dualFailed">
                    <p>{{ receiver.signType === 'SEAL_AND_SIGNATURE' ? $t('signPC.verifyAllTip') : $t('sign.digitalCertificateTip') +'，'+ $t('sign.signDes') }}</p>
                    <p><i class="el-icon-ssq-tishi1"></i>&nbsp;{{ $t('sign.requiredDualSign') }}</p>

                    <qriously class="code" :class="{'invalid': showRefresh}" :value="qrSignImgUrl" :size="186" />
                    <template v-if="showRefresh">
                        <p class="refresh-tip">{{ $t('sign.qrcodeInvalid') }}</p>
                        <i @click="initDualQrcode" class="el-icon-ssq--bs-shuaxincopy"></i>
                    </template>
                </template>
                <template v-if="dualFailed">
                    <p>{{ $t('sign.verifyTry') }}</p>

                    <div class="account-info">
                        <span class="label">{{ $t('sign.nameIs') }}:&nbsp;</span>
                        <span>{{ name }}</span>
                    </div>
                    <div class="account-info">
                        <span class="label">{{ $t('sign.IDNumIs') }}:&nbsp;</span>
                        <span class="IDNumber">{{ IDNumber }}</span>
                    </div>

                    <div class="opt-button">
                        <el-button type="primary" @click="handleRetry">{{ $t('sign.retry') }}</el-button>
                    </div>
                </template>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'FaceQrValidate',
    props: {
        dialogVisible: {
            type: Boolean,
            default: false,
        },
        receiver: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            contractId: this.$route.query.contractId,
            qrSignKey: '',
            visible: false,
            qrSignImgUrl: '',
            interval: '', // 双录结果查询轮询
            timeCount: 0, // 通过累计轮询次数来估算链接是否过期
            dualFailed: false, // 双录对比失败
            showRefresh: true, // 是否展示刷新二维码
            name: '',
            IDNumber: '',
        };
    },
    computed: {
        title() {
            if (this.dualFailed) {
                return this.$t('sign.dualFailed');
            } else {
                return this.$t('sign.pleaseScanToSign');
            }
        },

    },
    methods: {
        handleRetry() {
            this.dualFailed = false;
            this.clearDualResult().then(() => {
                this.initDualQrcode();
            });
        },
        handleClose() {
            this.visible = false;
            clearInterval(this.interval);
            this.$emit('close');
        },
        clearDualResult() {
            return this.$http.delete(`/users/auth/face/clear-cache?contractId=${this.contractId}`); // 清除刷脸结果缓存
        },
        // 获取刷脸二维码图片
        getQrSignImgUrl() {
            const paramsData = {
                targetUrl: `/auth-p/person/sign/face/redirect`, // 要跳转的地址，不包含域名
                attachment: `?contractId=${this.contractId}&isNonMainland=${this.receiver.nonMainLander}`,
                contractId: this.contractId,
            };
            return this.$http({
                method: 'get',
                url: '/users/face-auto-login/url',
                params: paramsData,
            });
        },
        getFacedInfo() {
            return this.$http.get(`/users/auth/face/result-polling?contractId=${this.contractId}${this.receiver.faceFirst ? '&faceFirst=true' : ''}`, {
                noToast: 1,
            });
        },
        // 轮询刷脸结果
        intervalFace() {
            const _this = this;
            this.getFacedInfo()
                .then((res) => {
                    this.timeCount = this.timeCount + 1;
                    if (this.timeCount > 600) {
                        this.timeCount = 0;
                        clearInterval(_this.interval);
                        this.showRefresh = true;
                    }
                    const { faceAuthDone, faceAuthPass } = res.data || {};
                    if (!faceAuthDone) { // 尚未进行刷脸操作，通过是否进入mobile/face/result标记
                        return;
                    } else if (faceAuthPass) { // 刷脸是否通过
                        this.handleClose();
                        this.$emit('faceDone', true);
                    } else { // 刷脸失败
                        clearInterval(_this.interval);
                        this.dualFailed = true;
                    }
                });
        },
        initDualQrcode() {
            // 双录校验，展示二维码，移动端打开
            this.getQrSignImgUrl()
                .then(res => {
                    this.qrSignImgUrl = res.data.url;
                    this.visible = true;
                    this.interval = setInterval(this.intervalFace, 3000); // 轮询刷脸结果
                    setTimeout(() => {
                        clearInterval(this.interval);
                    }, 3600000);
                    this.showRefresh = false;
                })
                .catch(() => {
                    this.handleClose();
                });
        },
        initAccountInfo() {
            return this.$http.get(`/users/auth/info`)
                .then((res) => {
                    const resData = res.data;
                    const baseInfo = resData.personAuthVOList[0];
                    this.name = baseInfo.realName;
                    this.IDNumber = baseInfo.idNumber;
                });
        },
    },
    created() {
        this.clearDualResult();
        this.initDualQrcode();
        this.initAccountInfo();
    },
};
</script>

<style lang="scss">
    // 扫码签名
    .qr-sign-dialog {
        .el-dialog {
            width: 400px;
            .el-dialog__body {
                .face-content {
                    color: #999;
                    p {
                        font-size: 14px;
                        color: #999;
                        margin-bottom: 15px;
                    }
                    .highlight {
                        color: #127fd2;
                        cursor: pointer;
                    }

                    .account-info {
                        text-align: left;
                        margin-bottom: 10px;
                        padding-left: 50px;
                        .label {
                            width: 80px;
                            display: inline-block;
                        }
                    }
                    .code {
                        text-align: center;
                    }
                    .invalid.code {
                        opacity: 0.1;
                    }

                    .el-icon-ssq--bs-shuaxincopy {
                        position: relative;
                        display: inline-block;
                        top: -120px;
                        font-size: 24px;
                        cursor: pointer;
                    }
                    .refresh-tip {
                        margin-bottom: 0;
                    }
                    .opt-button {
                        overflow: auto;
                        margin-top: 30px;
                        .el-button {
                            float: none;
                        }
                    }

                }

            }
        }
    }
</style>
