$dialogBC: #F3F9FD;

.signing {
    user-select: none;
    position: relative;
    height: 100vh;
    font-size: 12px;
    color: #333;
    background-color: #F3F4F6;

    .header {
        position: fixed;
        top: 0;

        .otherBtn {
            position: relative;
            padding: 0 20px 0 20px;
            height: 30px;
            line-height: 28px;
            text-align: center;
            font-size: 14px;
            color: #fff;
            background-color: #003b5f;
            // border: 1px solid #fff;
            border-radius: 2px;
            display: inline-block;
            margin-right: 10px;
            margin-top: 7px;
            cursor: pointer;
            [dir="rtl"] & {
                margin-right: 0;
                margin-left: 10px;
            }

            &:hover {
                background-color: #004772;
            }

            &:after {
                position: absolute;
                top: 12px;
                right: 8px;
                content: '';
                @include solid-triangle(4px, #fff, transparent, transparent, transparent);
                [dir="rtl"] & {
                    right: auto;
                    left: 8px;
                }
            }

            &.no-triangle:after {
                display: none;
            }

            .other-option-wrap {
                min-width: 100%;
                position: absolute;
                top: 31px;
                left: 0;
                border: 1px solid #ddd;
                [dir="rtl"] & {
                    right: 0;
                    left: auto;
                }
            }

            .other-option {
                width: 100%;
                height: 28px;
                color: #666;
                font-size: 12px;
                background-color: #fff;
                border-radius: 0;
                padding: 0 12px;
                text-align: left;
                box-sizing: border-box;
                [dir="rtl"] & {
                    text-align: right;
                }

                &:hover {
                    background-color: #f6f6f6;
                }
            }
        }

        .detailBtn {
            position: relative;
            padding: 0 20px 0 20px;
            height: 30px;
            line-height: 28px;
            text-align: center;
            font-size: 14px;
            color: #fff;
            background-color: #003b5f;
            // border: 1px solid #fff;
            border-radius: 2px;
            display: inline-block;
            margin-right: 10px;
            margin-top: 7px;
            cursor: pointer;
            [dir="rtl"] & {
                margin-right: 0;
                margin-left: 10px;
            }

            a {
                color: #fff;
            }
        }

        .privateLetterBtn {
            // position: relative;
            // width: 60px;
            // padding-right: 0;
            // padding-left: 0;
            // display: inline-block;

            &:after {
                display: none;
            }
        }
    }

    .wrap {
        position: absolute;
        padding-top: 50px;
        padding-bottom: 35px;
        top: 0;
        bottom: 0;
        width: 100%;
        overflow-y: hidden;

        &.hd-hidden {
            padding-top: 0;

            .options-container {
                top: 0;
            }

            .doc-wrap .documents-info .preview-header {
                top: 0;
            }
        }

        &.ft-hidden {
            padding-bottom: 0;
        }


        .options-proxy {
            position: fixed;
            z-index: 100;
            top: 55px;
            background-color: #ffe6e6;
            padding: 0 40px 0 20px;
            line-height: 30px;

            .el-icon-ssq-jingshitanhaox {
                font-size: 14px;
                color: #FF8A8A;
            }

            &::after {
                position: absolute;
                top: 0;
                right: -30px;
                content: '';
                border: 15px solid transparent;
                border-top: 15px solid transparent;
                border-right: 15px solid transparent;
                border-bottom: 15px solid transparent;
                border-left: 15px solid #ffe6e6;
                width: 0;
                height: 0px;
            }
        }

        .doc-wrap {
            position: relative;
            height: 100%;

            .documents-content {
                position: absolute;
                top: 0px;
                right: 210px;
                bottom: 0;
                left: 0;
                overflow: auto;
                overflow-x: hidden;
                padding: 0 70px 0;

                .signing-bottom-btn {
                    position: fixed;
                    bottom: 40px;
                    left: 0;
                    width: calc(100% - 210px);
                    text-align: center;
                    .next-doc-btn {
                        color: #127fd2;
                        text-align: center;
                        font-weight: bold;
                        font-size: 14px;
                        cursor: pointer;
                    }

                    .done-btn {
                        height: 30px;
                        line-height: 30px;
                        text-align: center;
                        font-size: 14px;
                        font-weight: bold;
                        color: #fff;
                        background-color: #127fd2;
                        margin: 5px 0;
                        cursor: pointer;
                        border-radius: 2px;
                        display: inline-block;
                        padding: 0 10px;
                        &:hover {
                            background-color: #1687dc;
                        }
                    }
                }
                [dir=rtl] & {
                    right: 0;
                    left: 210px;
                }


                .zoomContainer {
                    margin: 0 auto;
                    overflow: hidden;
                }

                .zoomContainer,
                .documents,
                .document,
                .page,
                .page-content {
                    position: relative;
                }

                .documents {
                    z-index: 101;
                    transform-origin: 0 0 0;
                    width: 100%;
                    margin-bottom: 10px;
                    overflow: hidden;

                    .pdfpage-number {
                        background: transparent;
                    }
                }

                .site-doc-container {
                    width: 100%;

                    .site-doc-scale {
                        text-align: center;

                        i {
                            font-size: 15px;
                            color: #666;
                            cursor: pointer;

                            &:first-child {
                                margin-right: 5px;
                            }

                            &:last-child {
                                margin-left: 5px;
                            }
                        }

                        i.scale {
                            width: 30px;
                            height: 30px;
                            line-height: 30px;
                        }

                        span {
                            display: inline-block;
                            position: relative;
                            top: 3px;
                            height: 15px;
                            border-left: 1px solid #DDDDDD;
                        }
                    }

                    .site-doc-title {
                        background-color: transparent;
                        border: none;
                        font-size: 14px;
                        text-align: center;
                        margin: 3px 0;
                    }
                }
            }

            .navi {
                position: fixed;
                top: 110px;
                left: auto;
                width: 80px;
                height: 30px;
                line-height: 30px;
                text-align: center;
                color: #fff;
                background-color: #0fb396;
                margin-top: 30px;
                cursor: pointer;
                transition: 1s all ease;
                z-index: 101;

                &:hover {
                    background-color: #00ac8e;

                    &:after {
                        @include solid-triangle(15px, transparent, transparent, transparent, #00ac8e)
                    }
                }

                &:after {
                    position: absolute;
                    top: 0;
                    right: -30px;
                    content: '';
                    @include solid-triangle(15px, transparent, transparent, transparent, #0fb396)
                }
            }

            .left-add {
                left: 210px
            }

            .preview-header {
                z-index: 99;
                right: 0;
                width: 210px;
                line-height: 40px;
                font-size: 14px;
                font-weight: bold;
                background-color: #FFF;
                color: #404040;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;

                i {
                    font-size: 15px;
                    color: #666;
                    margin-right: 8px;
                }

                .tab-header {
                    border-bottom: 1px solid #ddd;
                    max-height: 40px;
                    cursor: pointer;
                }

                .preview-fill {
                    display: inline-block;
                    width: 50%;
                    text-align: center;

                    &.active {
                        color: #127fd2;
                        border-bottom: 1px solid #127fd2;
                    }
                }

                .contract-title {
                    box-sizing: border-box;
                    padding: 0 11px;
                    width: 210px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    border-bottom: 1px solid #ddd;
                }

            }


            .documents-info {
                width: 210px;
                position: absolute;
                top: 0;
                right: 0;
                bottom: 0;
                overflow: auto;
                background-color: #fff;
                border-left: 1px solid $border-color;
                [dir=rtl] & {
                    right: auto;
                    left: 0;
                    border-left: none;
                    border-right: 1px solid $border-color;
                }
                .mini-documents {
                    .FieldMiniDoc-cpn {
                        border: none;

                        .title {
                            display: none;
                        }
                    }
                }
            }
        }

        .net-error-container {
            position: relative;
            margin: 0 auto;
            text-align: center;
            width: 82%;
            height: 100%;
            background-color: #fff;
            border: 1px solid $border-color;
            overflow: auto;

            .tip-img {
                margin: 185px auto 0;
                width: 60px;
                height: 70px;

                background: {
                    image: url("~img/net-error.png");
                    repeat: no-repeat;
                    size: 100% auto
                }
            }

            h4 {
                margin: 15px 0 10px 0;
                font-size: 18px;
            }

            p {
                font-size: 14px;
                line-height: 30px;
                width: 550px;
                text-align: left;
                margin: 0 auto;
            }
        }
    }

    .footer {
        box-sizing: border-box;
        position: fixed;
        bottom: 0;
        z-index: 1111;
    }

    .flying-icon {
        z-index: 9999;
        pointer-events: none;
        position: fixed;
        top: 0;
        left: 0;
        //background-color: #fff;
        border-radius: 2px;

        img {
            pointer-events: none;
        }
    }

    .flying-seal {
        width: 222px;
        height: 203px;
        background: rgba(247, 248, 249, 0.75);
        border: 1px dashed #127fd2;

        // box-shadow:1px 1px 13px #ccc, -1px 1px 13px #ccc;
        .seal-img {
            display: block;
            margin: 0 auto;
            padding: 22px 0;
            width: 159px;
        }
    }

    .flying-sig {
        width: 134px;
        height: 71px;
        border: 1px dashed #127fd2;

        .sig-img {
            display: block;
            margin: 0 auto;
            padding: 12px;
            height: 47px;
        }
    }

    .flying-date {
        width: 134px;
        height: 34px;
        line-height: 34px;
        margin: 0 auto;
        font-family: "SimSun", "STSong";
        font-weight: bold;
        font-size: 18px;
        background-color: rgba(191, 221, 255, 0.5);
        white-space: nowrap;
    }

    // 手写面板对话框
    .handWrite-dialog .el-dialog__body {
        padding: 0 !important;
    }
    .handWrite-dialog .el-dialog--tiny {
        left: 50%;
        margin-left: -373px;
        width: 746px;
        transform: none;
    }

    .handWrite-dialog .el-dialog__header {
        border-bottom: 1px solid #EBEEEE;
    }

    // 签名列表
    .signatures {
        .el-dialog {
            min-width: 500px;
            min-height: 300px;
            .el-dialog__header {
                max-width: 640px;
                box-sizing: border-box;
            }
        }

    }

    .signatures-select {
        height: 40px;
        line-height: 40px;
        background-color: $dialogBC;
        padding: 0 25px;
        .el-checkbox {
            margin-right: 6px;
        }
    }
    .signatures-container {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        max-width: 640px;
        min-height: 200px;
        box-sizing: border-box;

        font-size: 12px;
        color: #333;
        background-color: $dialogBC;
        padding: 11px 0px 11px 13px;

        .signature {
            width: 183px;
            height: 87px;
            text-align: center;
            border: 1px solid #ccc;
            border-radius: 4px;
            margin-right: 13px;
            margin-bottom: 15px;
            cursor: pointer;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;

            &:hover {
                background-color: #fff;
                border-color: #127fd2;
            }
        }
        .active {
            background-color: #fff;
            border-color: #127fd2;
            position: relative;
            .el-icon-ssq-danxuanxuanzhong {
                color: #127FD2;
                position: absolute;
                top: 5px;
                right: 5px;
                font-size: 14px;
            }
        }
    }

    .el-dialog {
        .el-dialog__header {
            padding: 14px 30px;
            border-bottom: 1px solid $border-color;
            .el-dialog__title {
                font-size: 16px;
                font-weight: normal;
                line-height: 22px;
            }
            .el-icon-close{
                color: #ddd;
                font-size: 14px;
                &:hover{
                    color: #999;
                }
            }
        }

        .el-dialog__body {
            padding: 20px 30px;
        }
        .el-dialog__footer {
            padding: 0px 30px 30px;
            .el-button {
                height: 30px;
                padding: 0 20px;
                font-size: 14px;
                border-radius: 2px;
            }
            .el-button--default {
                background-color: #F6F7F8;

                &:hover {
                    color: #333;
                    background-color: #FFF;
                    border-color: #ccc;
                }
            }
            .el-button--primary {
                background-color: #127fd2;
                border-color: #127fd2;

                &:hover {
                    color: #fff;
                    background-color: #1687dc;
                    border-color: #1687dc;
                }
            }
        }
    }

    .auth-change-dialog .el-dialog {
        .el-dialog__body {
            padding: 20px 35px;
        }
    }

    .sign-dialog-approval {
        .el-dialog__body {
            padding: 33px;
        }
    }

    .sign-dialog-ca-fail {
        .ca-fail{
            p{
                padding: 10px;
            }
            .copy-btn{
                padding: 10px 50px;
                margin-top: 15px;
                display: block;
                margin-left: auto;
                margin-right: auto;
            }
        }
    }
    .normal-padding-dialog {
        .el-dialog__body {
            padding: 25px 33px;
        }
    }

    // 盖章人列表
    .owners .el-dialog--tiny {
        width: 410px;

        .el-dialog__header {
            padding: 25px 33px;
        }

        .owner-title {
            padding: 14px 37px;
            font-size: 14px;
            font-weight: bold;
            color: #333;
            background-color: $dialogBC;
        }

        .owner-list {
            background-color: $dialogBC;
            padding: 0 37px 17px;

            .owner {
                display: inline-flex;
                width: 340px;
                height: 42px;
                line-height: 42px;
                border: 1px solid #ccc;
                border-radius: 1px;
                font-size: 12px;
                margin-bottom: 17px;
                cursor: pointer;

                &:hover {
                    background-color: #fff;
                    border-color: #127fd2;
                }

                img {
                    position: relative;
                    top: 7px;
                    left: 12px;
                }

                .owner-name,
                .owner-account {
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }

                .owner-name {
                    color: #333;
                    margin-left: 30px;
                }

                .owner-account {
                    color: #666;
                    margin-left: 8px;
                }
            }
        }
    }

    // 转给其他人签对话框
    .transferDialog {
        .el-dialog__header {
            border-bottom: 1px solid $border-color;
        }

        .el-dialog__body {
            border-bottom: 1px solid $border-color;
        }

        .el-dialog--small {
            width: 550px;
        }

        .company-inner-cloumn {
            height: 200px;
        }

        .company-inner-cloumn:first-child {
            width: 227px;
            background-color: #F6FAFD;
            padding: 20px;
            padding-bottom: 50px;
            border-right: 1px solid $border-color;
        }

        .el-tree {
            background-color: #F6FAFD;
        }

        .el-tree-node__content {
            &:hover {
                background-color: #F6FAFD;
                color: #127fd2;
            }
        }

        .is-current>.el-tree-node__content {
            background-color: #F6FAFD;
            color: #127fd2;
        }

        .department-member-cloumn {
            width: 242px;
            padding: 20px;
        }
    }

    // 审批校验对话框
    .ApprovalValidationDialog {
        .el-dialog {
            width: 396px;
        }

        .el-dialog__header {
            display: none;
        }

        .el-dialog__body {
            padding: 25px 0 0;
        }
    }

    // 审批详情
    .ApprovalDetailDialog {
        .el-dialog {
            min-width: 600px;
        }
    }

    // 对话框：用现有主体签约
    .useMyIdentityDialog {
        .el-dialog {
            width: 384px;
        }

        .subject-name-tip {
            color: #666;
            font-size: 12px;
        }

        .subject-name {
            margin-top: 5px;
            margin-bottom: 10px;
            border-bottom: 1px solid #e9e9e9;
            line-height: 30px;
            font-weight: bold;
            font-size: 16px;
        }

        ul {
            padding-top: 20px;
        }

        li {
            max-height: 34px;
            line-height: 17px;
            border: 1px solid #127fd2;
            text-align: center;
            margin-bottom: 10px;
            cursor: pointer;
            border-radius: 2px;
            color: #127fd2;
            overflow: hidden;
            padding: 10px 0;

            span {
                display: block;
                max-height: 34px;
                padding: 0;
                overflow: hidden;
                text-overflow: -o-ellipsis-lastline;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                line-clamp: 2;
                -webkit-box-orient: vertical;
            }

            &:hover {
                background-color: #127fd2;
                color: #fff;
            }
        }

        .el-dialog__body {
            p {
                color: #333;
            }

            button {
                display: block;
                width: 100%;
                float: none;
                margin: 0 auto;
                font-size: 14px;
                margin-top: 10px;
            }
        }
    }

    // 动画
    .fadeIn-enter-active {
        transition: all .2s ease;
    }

    .fadeIn-leave-active {
        transition: all .2s ease;
    }

    .fadeIn-enter,
    .fadeIn-leave-to

    /* .fade-leave-active in below version 2.1.8 */
        {
        opacity: 0;
    }

}

.sign-common-el-alert {
    p {
        white-space: pre;
        font-size: 12px;
        line-height: 20px;
    }
}

.signing-cusMessageBox.el-message-box {
    .el-message-box__header {
        height: 44px;
        border-bottom: 1px solid #eee;

        .el-message-box__title {
            font-weight: normal;
        }
    }

    .el-message-box__content,
    .el-message-box__btns {
        background: #f6f9fc;
    }

    div.el-message-box__content {
        padding: 30px 30px 24px;
        border: none;

        .el-message-box__message {
            p {
                font-size: 14px;
                line-height: 24px;
                word-break: break-all;
            }

            .messageBox {
                padding: 10px 30px;
                line-height: 18px;
                background: #ebf1f6;
                color: #666;

                .el-icon-ssq-tishi1 {
                    margin-left: -10px;
                    margin-right: 5px;
                    font-size: 18px;
                    vertical-align: top;
                    color: #f86b26;
                }

                .highLight {
                    margin-left: 5px;
                    margin-right: 5px;
                    color: #3d6786;
                }
            }

            .subject-name-tip {
                color: #666;
                font-size: 12px;
            }

            .subject-name {
                margin: 15px 0;
                color: #333;
                font-size: 16px;
                font-weight: bold;
            }
        }
    }

    .el-message-box__btns {
        padding: 13px 34px 30px;

        .el-button--default {
            float: right;
            margin-left: 10px;
        }

        .el-button--primary {
            float: none;
            padding-left: 35px;
            padding-right: 35px;
        }
    }
}

.sign-va-dialog {
    .el-dialog__body {
        padding: 0 !important;
    }
}
.en-page {
    .sign-va-dialog, .set-sign-pwd-dialog {
        .el-dialog {
            width: 450px !important;
        }
    } 
}

.el-popover[x-placement^=bottom] {
    &.private-letter-popover {
        top: 40px !important;
        // margin-top: 12px;
        color: #666666;
        font-size: 14px;
        padding: 30px 30px 25px 35px;
        width: 312px !important;
        box-sizing: border-box;
        color: #333;
        user-select: text;
        z-index: 1000 !important;
        margin-top: 10px;
        right: 210px !important;

        .content {
            word-break: break-all;
            color: #666;
        }

        .line {
            border: 1px solid #eee;
            display: block;
            margin: 10px 0;
        }

        .file-list-title {
            i {
                color: #999;
            }

            margin-bottom: 10px;
        }

        .file-list {
            word-wrap: break-word;

            b {
                color: #999;
                margin-right: 3px;
            }

            .li-item {
                cursor: pointer;
                height: auto;
                margin-bottom: 8px;
            }
        }

    }

    .popover-title {
        margin-bottom: 9px;
        color: #000;
    }

    .content {
        p {
            word-break: break-all;
        }
    }

    .read-btn {
        float: right;
        color: #fff;
        width: 85px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        font-size: 14px;
        background-color: #127fd2;
        margin-top: 10px;
        cursor: pointer;
        border-radius: 2px;

        &:hover {
            background-color: #1687dc;
        }
    }

    // .arrow {
    //     position: absolute;
    //     left: 255px;
    //     overflow: hidden;
    //     width: 0;
    //     height: 0;
    //     border-width: 8px;
    //     border-style: solid dashed dashed dashed;
    // }
    // .arrow-border {
    //     top: -16px;
    //     border-color: transparent transparent #D1DBE5 transparent;
    // }
    // .arrow-bg {
    //     top: -15px;
    //     border-color: transparent transparent #fff transparent;
    // }
    .popper__arrow {
        border-width: 10px;
        top: -20px;

        &:after {
            border-width: 10px;
            margin-left: -10px;
            top: -10px;
        }
    }
}

.el-message-box_singerpay.el-message-box {
    border-radius: 6px;

    .el-message-box__header {
        display: none;
    }

    .el-message-box__content {
        border: none;
        padding: 40px 30px 20px 30px;
    }

    .el-message-box__btns {
        button {
            float: none;
            border-radius: 4px;

            &:first-child {
                background: #FFFFFF;
            }
        }
    }
}

.prompt-center {
    margin: 10px 0;
}
