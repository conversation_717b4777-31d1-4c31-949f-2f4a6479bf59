<template>
    <el-dialog
        class="continue-operate-next-dialog"
        width="480px"
        :title="$t('signPC.continueOperation.tip')"
        :visible.sync="dialogVisible"
        :show-close="false"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
    >
        <div class="continue-operate-next-content">
            <p class="continue-operate-next-success"><em class="el-icon-ssq-tongguo1"></em></p>
            <p class="continue-operate-success">{{ $t('signPC.continueOperation.success') }}</p>
            <p>{{ $t('signPC.continueOperation.approvalProcess',{ totalNum:totalApproversCount,passNum:passApproversCount}) }}</p>
        </div>
        <span slot="footer" class="continue-operate-next-dialog-footer">
            <el-button class="exit-approve" @click="handleExit">{{ $t('signPC.continueOperation.exitApproval') }}</el-button>
            <el-button class="continue-approve" v-if="nextContractInfo && nextContractInfo.contractTitle" type="primary" @click="handleContinue">{{ $t('signPC.continueOperation.continueApproval') }}</el-button>
            <p class="next-contract-tip" v-if="nextContractInfo && nextContractInfo.contractTitle">{{ $t('signPC.continueOperation.next') }} {{ nextContractInfo.contractTitle }}({{ $t('signPC.continueOperation.receiver') }}{{ nextContractInfo.receiver }})</p>
            <p class="next-contract-tip" v-else>{{ $t('signPC.continueOperation.next') }}{{ $t('signPC.continueOperation.none') }} </p>
        </span>
    </el-dialog>
</template>

<script>
import { docOperationMixin } from 'src/mixins/docOperation';

export default {
    name: 'ContinueOperateNextDialog',
    mixins: [docOperationMixin],
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        contractId: {
            type: String,
            default: '',
        },
        approvalDetailReceiverId: {
            type: String,
            default: '',
        },
        defType: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            nextContractInfo: {},
            approvers: [],
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                // 如果是关闭弹窗
                if (!value) {
                    this.$emit('close');
                }
            },
        },
        totalApproversCount() {
            return this.approvers.length;
        },
        passApproversCount() {
            return this.approvers.filter(item => item.signStatus === 'APPROVAL_PASS').length;
        },
    },
    methods: {
        handleExit() {
            this.$sensors.track({
                eventName: `Ent_ContractApprovalWindow_BtnClick`,
                eventProperty: {
                    page_name: `合同审批页`,
                    window_name: '驳回提示',
                    icon_name: '退出审批',
                    ...this.sensorsTrackContractInfo,
                },
            });
            window.close();// 关闭当前窗口
        },
        handleContinue() {
            this.$sensors.track({
                eventName: `Ent_ContractApprovalWindow_BtnClick`,
                eventProperty: {
                    page_name: `合同审批页`,
                    window_name: '驳回提示',
                    icon_name: '继续审批',
                    ...this.sensorsTrackContractInfo,
                },
            });
            this.dialogVisible = false;
            this.handleSwitchSubject({ type: 'APPROVAL', from: 'approvalPage' }, this.nextContractInfo.contractId);
        },
        getContinueInfo() {
            const shortcutType = 'NEED_MY_APPROVAL';
            this.$http.get(`/contract-search/next-contract?lastContractId=${this.contractId}&shortcutType=${shortcutType}`).then(res => {
                this.nextContractInfo = res.data && res.data.data;
            });
            this.getDetail()
                .then(res => {
                    const data = res.data;
                    this.approvers = data.approvers;
                });
        },
        getDetail() {
            // 发送前审批
            let url = `${signPath}/contracts/${this.contractId}/approval`;
            // 签署前审批
            if (this.defType === '11') {
                url = `${signPath}/contracts/${this.contractId}/sign-approval/${this.approvalDetailReceiverId}`;
            }
            return this.$http.get(url);
        },
    },
    mounted() {
        this.getContinueInfo();
    },
};
</script>

<style lang="scss">
.continue-operate-next-dialog {
    .el-dialog{
        width: 600px;
        .el-dialog__body{
            .continue-operate-next-content{
                margin-top: 40px;
                margin-bottom: 30px;
                text-align: center;
                color: #333;
                .continue-operate-next-success{
                    font-size: 50px;
                    color: #00AA64;
                }
                .continue-operate-success{
                    margin: 20px 0;
                    font-size: 16px;
                }
            }
        }
        .el-dialog__footer{
            text-align: center;
            .continue-operate-next-dialog-footer{
                .exit-approve,.continue-approve{
                    width: 180px;
                    height: 34px;
                }
                .continue-approve.el-button--primary{
                    background: #127FD2;
                    border-color: #127FD2;
                }
                .exit-approve.el-button--default{
                    background: #F8F8F8;
                    color: #666666;
                    border-color: #CCC;
                }
            }
            .next-contract-tip{
                margin-top: 20px;
                margin-bottom: 10px;
            }
        }
    }
}
</style>
