<!-- 业务组件：签署——签约拒绝校验 -->
<template>
    <div class="SignReject-comp clear">
        <p>{{ $t('sign.refuseConfirmTip', {reason: reasonText}) }}</p>

        <div class="reject-footer">
            <el-button class="confirm-sign-btn"
                type="primary"
                @click="handleClickConfirmRejectSign"
            >
                {{ $t('sign.submit') }}
            </el-button>

            <el-button class="confirm-sign-btn"
                type="default"
                @click="handleCancel"
            >
                {{ $t('sign.waitAndThink') }}
            </el-button>
        </div>
    </div>
</template>

<script>
import { goReturnUrl } from 'src/common/utils/returnUrl.js';
import { mapGetters } from 'vuex';

export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['channel', 'contractId', 'returnUrl', 'rejectCheckbox', 'receiver', 'sensorsTrackContractInfo', 'sensorsPageName', 'sensorsEventName', 'contractData'],
    data() {
        return {
            refuseType: 'REJECT', //	类型	string	REJECT；REVOKE_CANCEL
            ssoSigning: {}, // 单点登录配置
            urlToken: this.$route.query.token || '',
        };
    },
    computed: {
        /* 验证码 和 签约密码（若需要）没有完整提供的话，为true */
        ...mapGetters(['getSsoConfig']),
        reasonText() {
            const splitStr = this.$i18n.locale === 'zh' ? '；' : ';';
            return this.rejectCheckbox.map(a => a.refuseReason).join(splitStr);
        },
    },
    methods: {
        handleCancel() {
            this.$emit('cancel');
        },
        // 签约拒绝确认按钮
        async handleClickConfirmRejectSign() {
            const res = await this.$hybrid.offlineTip({ operate: this.$t('sign.refuseConfirm') });
            if (!res) {
                return;
            }
            this.$emit('loading', 1);
            this.postRejectSign({
                refuseType: this.refuseType,
                signPlatform: 'WEB',
                refuseReasons: this.rejectCheckbox,
            })
                .then(() => {
                    this.sensorsEventName && this.$sensors.track({
                        eventName: `Ent_${this.sensorsEventName}_Result`,
                        eventProperty: {
                            page_name: `${this.sensorsPageName}`,
                            is_success: true,
                            request_url: `/contract-api/contracts/${this.contractId}/break`,
                            icon_name: '拒签',
                            contract_sender: this.contractData?.sendAccount || null,
                            ...this.sensorsTrackContractInfo,
                        },
                    });
                    this.signRejectDialogVisible = false;
                    this.$MessageToast.success(this.$t('sign.refuseSuc'))
                        .then(() => {
                            /* 如果客户定义了跳转地址，则首先跳转 */
                            // 如果跳转地址是面对大甲方的贷款数据测评结果，拒签则跳转到合同列表
                            if (this.returnUrl && this.returnUrl.indexOf('converge/loans-evaluate/loans') === -1) {
                                goReturnUrl(this.returnUrl);
                                return;
                            }
                            if (this.ssoSigning.signing_disagree && this.ssoSigning.signing_disagree.url) {
                                this.$router.push(`${this.ssoSigning.signing_disagree.url}?status=2&contractId=${this.contractId}&type=sign`);
                            } else {
                                if (this.channel === 'notice') {
                                    this.$router.push(`/sign/sign-tip?status=2&contractId=${this.contractId}&type=sign&token=${this.urlToken}`);
                                } else {
                                    this.$router.push('/doc/list');
                                }
                            }
                        });
                })
                .catch(err => {
                    const errorMsg = err.response?.data?.message;
                    const status = err.response?.status;
                    const code = err.response?.data?.code;
                    this.sensorsEventName && this.$sensors.track({
                        eventName: `Ent_${this.sensorsEventName}_Result`,
                        eventProperty: {
                            page_name: `${this.sensorsPageName}`,
                            request_url: `/contract-api/contracts/${this.contractId}/break`,
                            icon_name: '拒签',
                            is_success: false,
                            fail_http_code: status,
                            fail_error_code: code,
                            fail_reason: errorMsg,
                            contract_sender: this.contractData?.sendAccount || null,
                            ...this.sensorsTrackContractInfo,
                        },
                    });
                })
                .finally(() => {
                    this.$emit('loading', 0);
                });
        },

        // Ajax
        getNoticeType() {
            return this.$http.get(`/users/notifications`);
        },
        postRejectSign(data) {
            return this.$http.post(`/contract-api/contracts/${this.contractId}/break`, data, { noToast: 1 });
        },
    },
    beforeMount() {
        this.ssoSigning = this.getSsoConfig.signing || {};
    },
};
</script>

<style lang="scss">
.SignReject-comp {
    * {
        font-size: 12px;
        // color: #333;
    }

    .reject-footer {
        margin: 20px 10px 0px;
        text-align: center;
        .confirm-sign-btn {
            min-width: 80px;
        }
    }

}
.ja-page .SignReject-comp{
    .confirm-sign-btn {
        float: right;
    }
}
</style>
