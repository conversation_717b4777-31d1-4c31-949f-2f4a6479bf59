<template>
    <div class="AppUnauthSeal-cpn">
        <h1 class="title">{{ $t('sign.electronicSeal') }}</h1>
        <div class="content">
            <img v-if="isEn" src="~img/unauth_seal_en.png" width="134" height="134" @click="clickSeal">
            <img v-else src="~img/unauth_seal.png" width="134" height="134" @click="clickSeal">
            <p>{{ $t('sign.changeTheSeal') }}</p>
            <div class="b-jump"><a class="common-font-color" @click="goToAuth">{{ $t('sign.goToVerify') }} ></a></div>
        </div>
    </div>
</template>
<script>
export default {
    computed: {
        isEn() {
            return this.$i18n.locale === 'en'; // 设置了语言为英语
        },
    },
    methods: {
        clickSeal() {
            this.$emit('click-unauth-seal');
        },
        goToAuth() {
            this.$emit('to-auth');
        },
    },
};
</script>
<style lang="scss">
	.AppUnauthSeal-cpn {
		text-align: center;
		.title {
			padding: 20px;
			text-align: left;
		}
		.content {
			background-color: #f6fbfc;
			padding-bottom: 30px;
			img {
				padding-top: 14px;
				padding-bottom: 7px;
				cursor: pointer;
			}
			p {
				font-size: 14px;
				color: #666;
				margin-bottom: 10px;
			}
			.b-jump {
				text-align: right;
				padding-right: 33px;
				a {
					font-size: 10px;
					cursor: pointer;
				}
			}

		}
	}
</style>
