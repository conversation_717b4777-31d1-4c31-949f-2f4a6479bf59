<!-- 签约校验弹窗-短信校验方式 -->
<template>
    <div class="validation-verCode" id="validation-verCode">
        <div class="error" v-show="isErrorShow">
            <i class="login-error-i iconfont el-icon-ssq-prohibit-filling"></i>
            <div class="login-error-r">
                <span class="login-error-detail">
                    {{ errorMsg }}<span v-show="forgetPassShow">{{ $t('sign.if') }}<span class="highlight" @click="handleClickForget">{{ $t('sign.forgetPassword') }}</span></span>
                </span>
            </div>
        </div>
        <div class="certificate-tip" v-if="!getIsForeignVersion">
            {{ signType === 'SEAL_AND_SIGNATURE' ? $t('signPC.verifyAllTip'): $t('signPC.verifyTip') }}
        </div>
        <div class="sign-va-form">
            <el-form @submit.native.prevent>
                <div class="confirm-sign-tip" v-if="isReuseVerifyCode">
                    <p> {{ $t('sign.signConfirmTip.1', {
                        contract:contractAliasText
                    }) }}</p>
                    <p>{{ $t('sign.signConfirmTip.2', {
                        contract:contractAliasText
                    }) }}</p>
                </div>
                <div v-else class="verify-container">
                    <el-form-item class="countdown-item" :label="sendType === 'E' ? $t('sign.email') : $t('sign.phoneNumber')">
                        <span class="phone">{{ notice ? maskNotice : $t('sign.setNotificationInUserCenter') }}</span>
                    </el-form-item>
                    <template v-if="isVerifyCodeValid">
                        <el-form-item :label="sendType == 'E' ? $t('sign.mailVerificationCode') : $t('sign.verificationCode')">
                            <div class="verify-flex">
                                <el-input
                                    class="verify-input"
                                    v-model="verifyCode"
                                    :maxlength="6"
                                >
                                    <ElIDelete slot="icon"></ElIDelete>
                                </el-input>
                                <CountDown class="countDown" :clickedFn="handleClickSendCode" :disabled="countDownDisabled" ref="btn" :second="60"></CountDown>
                            </div>
                        </el-form-item>
                        <div class="switch" v-if="phoneNotice">
                            <!-- 一直收不到短信？试试 -->
                            <span class="voiceCodeLabel">{{ $t('sign.msgTip') }}</span>
                            <span class="highlight" @click="handleClickVoice">{{ $t('sign.voiceVerCode') }}</span>
                            <span v-if="mailNotice">
                                {{ $t('sign.or') }}
                                <span v-show="phoneNotice && sendType === 'E'" class="highlight" @click="handleClickMailAndPhone(3)">
                                    {{ $t('sign.SMSVerCode') }}
                                </span>
                                <span v-show="mailNotice && (sendType === 'S' || sendType === 'V') " class="highlight" @click="handleClickMailAndPhone(4)">
                                    {{ $t('sign.emailVerCode') }}
                                </span>
                            </span>
                            <span v-if="$i18n.locale === 'ja'">{{ $t('sign.tryMore') }}</span>
                        </div>
                        <!-- 签约密码优先展示 -->
                        <p v-if="showSignPasswordPriority" class="valid-type-switch" @click="switchValidType(5)">{{ $t('sign.useSignPsw') }}</p>

                        <!-- 非签约密码优先下展示，设置/使用签约密码 -->
                        <div v-if="!isSignPasswordPriorityOpen && !shouldSignVaSecondCheck && showSignPasswordEntry" class="forgetPass-form-item">
                            <span v-if="signPwdExistence" class="valid-type-switch" @click="switchValidType">{{ $t('sign.useSignPsw') }}</span>
                            <span v-else class="valid-type-switch" @click="$emit('openSetSignPwdDialog')">{{ $t('sign.setSignPsw') }}</span>
                        </div>
                    </template>
                </div>
                <template v-if="!isVerifyCodeValid || !hidePass">
                    <el-form-item :label="$t('sign.signPsw')">
                        <el-input
                            :maxlength="6"
                            type="password"
                            auto-complete="new-password"
                            v-model="signPass"
                            v-focus
                            :placeholder="$t('common.signPwdType')"
                        >
                            <ElIDelete slot="icon"></ElIDelete>
                        </el-input>
                    </el-form-item>
                    <div class="forgetPass-form-item">
                        <span v-if="!isVerifyCodeValid" class="valid-type-switch inline-gap" @click="switchValidType(6)">
                            {{ $t('sign.useVerCode') }}</span>
                        <span @click="handleClickForget">{{ $t('sign.forgetPassword') }}</span>
                    </div>
                </template>
            </el-form>
            <el-button
                class="confirm-sign-btn"
                type="primary"
                @click="handleClickConfirmSign(isReuseVerifyCode ? 7 : 8)"
                :loading="submitDiable"
            >
                {{ isReuseVerifyCode ? $t('sign.signConfirmTip.confirm') : $t('sign.submit') }}
            </el-button>
        </div>
        <div class="sign-pwd-remind" v-if="showSignPwdRemind">
            <div class="sign-pwd-remind__mask"></div>
            <img
                class="sign-pwd-remind__img"
                :src="imgUrl"
            />
            <img class="sign-pwd-remind__icon"
                src="~src/common/assets/img/sign/closeIcon.png"
                @click="handleCloseRemind"
            />
        </div>
    </div>
</template>

<script>

import ElIDelete from 'components/el_i_delete/ElIDelete.vue';
import CountDown from 'components/countDown/CountDown.vue';
import { mapGetters, mapState } from 'vuex';
import { goReturnUrl } from 'src/common/utils/returnUrl.js';
import { getMaskPhone, getMaskEmail } from 'utils/reg.js';

export default {
    components: {
        ElIDelete,
        CountDown,
    },

    // eslint-disable-next-line vue/require-prop-types
    props: ['ssoSigning', 'channel', 'contractId', 'hidePass', 'userAccount',
            // eslint-disable-next-line vue/require-prop-types
            'signerFillLabels', 'isHybridCloudContract', 'isAllSignaturesInactive', 'returnUrl', 'isApplySeal',
            // eslint-disable-next-line vue/require-prop-types
            'signType', 'receiverId', 'isReuseVerifyCode', 'showSignPasswordPriority', 'showSignPasswordEntry', 'isSignPasswordPriorityOpen', 'shouldSignVaSecondCheck', 'contractAliasText', 'sensorsTrackContractInfo', 'receiver', 'contractData'],
    data() {
        return {
            signPass: '',
            verifyCode: '',

            notice: '',
            mailNotice: '',
            phoneNotice: '',
            countDownDisabled: false,
            verifyKey: '',
            sendType: 'S', // S：手机短信；E：邮件；V：语音

            isErrorShow: false,
            errorMsg: '',
            forgetPassShow: false,
            // 确定按钮的状态
            submitDiable: false,
            senderFaceSignOpen: false, // 发件方乐高城是否开通刷脸签署
            isVerifyCodeValid: true, // true：验证码校验，false：签约密码校验
            signPwdExistence: false, // 用户是否配置了签约密码
            showSignPwdRemind: false, // 是否展示签约密码提醒
            pageType: this.$route.query.type || 'sign', // send sign approval view
            imgUrl: '',
            // url的token信息
            urlToken: this.$route.query.token || '',
        };
    },
    computed: {
        ...mapGetters(['getIsForeignVersion']),
        ...mapState(['commonHeaderInfo']),
        sensorsEventName() {
            return this.pageType === 'sign' ? 'ContractSign' : (this.pageType === 'approval' ? 'ContractApproval' : '');
        },
        sensorsPageName() {
            return this.pageType === 'sign' ? '合同签署页' : (this.pageType === 'approval' ? '合同审批页' : '');
        },
        toFaceBtnShow() {
            // 去刷脸入口展示逻辑：当前登录账号的个人身份已实名；单份签署页面；发件方乐高城开通刷脸签署；
            return this.$store.state.commonHeaderInfo.platformUser.authStatus === 2 && this.senderFaceSignOpen && !this.isHybridCloudContract;
        },
        // 邮箱或者手机号脱敏
        maskNotice() {
            return this.sendType === 'E' ? getMaskEmail(this.notice) : getMaskPhone(this.notice);
        },
        isVisitor() { // 访客模式
            return this.commonHeaderInfo.platformUser.visitor;
        },
    },
    watch: {
        showSignPasswordPriority: {
            handler(val) {
                this.isVerifyCodeValid = !val;
            },
            immediate: true,
        },
    },
    methods: {
        getContractSenderConfig() {
            // 合同发件方乐高城配置
            if (this.$route.query.contractId) {
                this.$http.get(`/contract-api/contracts/send-ent-config/${this.$route.query.contractId}`)
                    .then(res => {
                        this.senderFaceSignOpen = ((res && res.data) || {}).canFacesign;
                    });
            }
        },
        /**
         * 获取用户是否存在签约密码
         */
        getSignPwdExist() {
            return this.$http.get(`/users/sign-pwd/exist`);
        },
        /**
         * 获取用户是否展示签约密码提醒
         */
        getIfTriggerSignPwd() {
            return this.$http.get(`/users/prompt/if-trigger?ruleName=SET_SIGN_PWD`);
        },
        handleCloseRemind() {
            this.$http.post('/users/prompt/interrupt', {
                ruleName: 'SET_SIGN_PWD',
                interruptDays: 1,
            }).finally(() => {
                this.showSignPwdRemind = false;
            });
        },
        // 获取用户联系方式
        getNoticeType() {
            return this.$http.get('/users/notifications');
        },
        switchValidType(index) {
            this.handleClickEventTrack(index);
            this.signPass = '';
            this.verifyCode = '';
            this.verifyKey = '';
            this.isErrorShow = '';
            this.errorMsg = '';
            this.isVerifyCodeValid = !this.isVerifyCodeValid;
        },
        // 点击发送验证码，不知道为什么要包一层
        handleClickSendCode() {
            this.send();
        },
        recordVerifyCode({ contractId = null, receiverId = null }) {
            return Vue.$http
                .post(`/contract-api/contracts/add-verify-code-record`, {
                    contractId,
                    receiverId,
                });
        },
        // 发送验证码
        send() {
            this.countDownDisabled = true;
            setTimeout(this.sended, 0);
            // sendVerCode
            this.$http.sendVerCode({ // 不传target，
                code: 'B008',
                sendType: this.sendType,
                bizTargetKey: this.$route.query.contractId, // contractId
            })
                .then((res) => {
                    this.verifyKey = res.data.value;
                    this.$MessageToast.success(this.$t('sign.SentSuccessfully'));
                })
                .catch(() => {
                    this.$refs.btn.reset();
                })
                .finally(() => {
                    this.recordVerifyCode({ contractId: this.$route.query.contractId, receiverId: this.receiverId }).then(() => {
                        this.getIfShowSignPwdRemind();
                    });
                });
        },
        // 发送成功，开始倒计时
        sended() {
            this.$refs.btn.run();
            this.countDownDisabled = false;
        },
        // 发送语音验证码
        handleClickVoice() {
            this.handleClickEventTrack(2);
            if (this.$refs.btn.time > 0) {
                this.$MessageToast.error(this.$t('sign.intervalTip'));
                return;
            }
            this.sendType = 'V';
            this.notice = this.phoneNotice.code;
        },
        // 发送短信、邮件验证码
        handleClickMailAndPhone(index) {
            this.handleClickEventTrack(index);
            if (this.$refs.btn.time > 0) {
                this.$MessageToast.error(this.$t('sign.intervalTip'));
                return;
            }
            if (this.sendType === 'E') {
                this.sendType = 'S';
                this.notice = this.phoneNotice.code;
            } else {
                this.sendType = 'E';
                this.notice = this.mailNotice.code;
            }
        },
        // 忘记签约密码
        handleClickForget() {
            this.handleClickEventTrack(1);
            window.localStorage && window.localStorage.setItem('ForgetPassReturnSignHref', this.$route.fullPath);
            this.$router.push(`/resetSignPassword?userAccount=${this.userAccount}`);
        },
        // 签约校验
        async handleClickConfirmSign(index) {
            this.handleClickEventTrack(index);
            const res = await this.$hybrid.offlineTip({ operate: this.$t('sign.signVerification') });
            if (!res) {
                return;
            }
            // 修改验证码复用时无法签署问题
            if (this.isVerifyCodeValid && !this.isReuseVerifyCode && !this.verifyCode) {
                return this.$MessageToast.error(this.$t('sign.inputVerifyCodeTip'));
            } else if ((!this.isVerifyCodeValid || !this.hidePass) && !this.signPass) {
                return this.$MessageToast.error(this.$t('sign.inputSignPwdTip'));
            }
            this.$parent.$emit('loading', 1);
            this.submitDiable = true;
            const iconName = (!this.verifyCode && this.signPass) ? '使用签约密码签署' : ((this.verifyCode && this.signPass) ? '双重校验' : '使用验证码校验');
            let signConfirmMessage = '';
            if (this.isReuseVerifyCode) {
                signConfirmMessage = this.$t('sign.signConfirmTip.1', { contract: this.contractAliasText }) + this.$t('sign.signConfirmTip.2', { contract: this.contractAliasText });
            }
            this.postConfirmSign({
                signPass: this.signPass,
                verifyCode: this.verifyCode,
                verifyKey: this.verifyKey,
                notice: this.notice,
                signPlatform: 'WEB',
                archiveCollectContractId: this.$route.query.archiveCollectContractId,
                signConfirmMessage,
            })
                .then(() => {
                    this.$emit('verifyDone');
                    this.$MessageToast.success(this.isApplySeal ? this.$t('sign.appliedSeal') : this.$t('signPC.operationCompleted'))
                        .then(() => {
                            this.sensorsEventName && this.$sensors.track({
                                eventName: `Ent_${this.sensorsEventName}_Result`,
                                eventProperty: {
                                    page_name: `${this.sensorsPageName}`,
                                    is_success: true,
                                    request_url: `${signPath}/contracts/${this.contractId}/confirm`,
                                    icon_name: iconName,
                                    contract_sender: this.contractData?.sendAccount || null,
                                    ...this.sensorsTrackContractInfo,
                                },
                            });
                            this.sensorsEventName && this.$sensors.track({
                                eventName: `Ent_${this.sensorsEventName}Window_Result`,
                                eventProperty: {
                                    page_name: `${this.sensorsPageName}`,
                                    window_name: '签约校验',
                                    is_success: true,
                                    request_url: `${signPath}/contracts/${this.contractId}/confirm`,
                                    icon_name: iconName,
                                    ...this.sensorsTrackContractInfo,
                                },
                            });
                            this.isReuseVerifyCode && this.sensorsEventName && this.$sensors.track({
                                eventName: `Ent_${this.sensorsEventName}Window_Result`,
                                eventProperty: {
                                    page_name: `${this.sensorsPageName}`,
                                    window_name: '验证码复用',
                                    is_success: true,
                                    request_url: `${signPath}/contracts/${this.contractId}/confirm`,
                                    icon_name: '确认签署',
                                    ...this.sensorsTrackContractInfo,
                                },
                            });
                            // 当前是档案+采集流程，签署成功跳转采集成功页面
                            if (sessionStorage.getItem('entCustomerInfo')) {
                                const entCustomerInfo = JSON.parse(sessionStorage.getItem('entCustomerInfo'));
                                return location.href = `${location.origin}/damp/pages/success/success?archiveId=${entCustomerInfo.archiveId}`;
                            }
                            /* 如果客户定义了跳转地址，则首先跳转 */
                            if (this.returnUrl) {
                                goReturnUrl(this.returnUrl);
                                return;
                            }
                            if (this.ssoSigning.signing_agree && this.ssoSigning.signing_agree.url) {
                                this.$router.push(`${this.ssoSigning.signing_agree.url}?status=6&contractId=${this.contractId}&type=sign`);
                            } else {
                                // 链接进来
                                if (this.channel === 'notice') {
                                    this.$router.push(`/sign/sign-tip?status=6&contractId=${this.contractId}&type=sign${this.isAllSignaturesInactive ? '&applySeal=true' : ''}&receiverId=${this.receiverId}`);
                                } else { // 登录进来
                                    this.$router.push('/doc/list');
                                }
                            }
                        });
                })
                .catch(err => {
                    this.isErrorShow = true;
                    this.forgetPassShow = err.response.data.code === '010051';
                    this.errorMsg = err.response?.data.message;
                    const status = err.response?.status;
                    const code = err.response?.data?.code;
                    // 如果复用验证码失败
                    if (Number(err.response.data.code) === 130176) {
                        this.isReuseVerifyCode = false;
                        this.sensorsEventName && this.$sensors.track({
                            eventName: `Ent_${this.sensorsEventName}Window_Result`,
                            eventProperty: {
                                page_name: `${this.sensorsPageName}`,
                                window_name: '验证码复用',
                                is_success: false,
                                fail_http_code: status,
                                fail_error_code: code,
                                fail_reason: this.errorMsg,
                                request_url: `${signPath}/contracts/${this.contractId}/confirm`,
                                icon_name: '确认签署',
                                ...this.sensorsTrackContractInfo,
                            },
                        });
                    }
                    this.sensorsEventName && this.$sensors.track({
                        eventName: `Ent_${this.sensorsEventName}_Result`,
                        eventProperty: {
                            page_name: `${this.sensorsPageName}`,
                            is_success: false,
                            fail_http_code: status,
                            fail_error_code: code,
                            fail_reason: this.errorMsg,
                            request_url: `${signPath}/contracts/${this.contractId}/confirm`,
                            icon_name: iconName,
                            contract_sender: this.contractData?.sendAccount || null,
                            ...this.sensorsTrackContractInfo,
                        },
                    });
                    this.sensorsEventName && this.$sensors.track({
                        eventName: `Ent_${this.sensorsEventName}Window_Result`,
                        eventProperty: {
                            page_name: `${this.sensorsPageName}`,
                            window_name: '签约校验',
                            is_success: false,
                            fail_http_code: status,
                            fail_error_code: code,
                            fail_reason: this.errorMsg,
                            request_url: `${signPath}/contracts/${this.contractId}/confirm`,
                            icon_name: iconName,
                            ...this.sensorsTrackContractInfo,
                        },
                    });
                })
                .finally(() => {
                    this.submitDiable = false;
                    this.$parent.$emit('loading', 0);
                });
        },
        /**
         * @param  {Object}   data 确认签署提交的密码，验证码等数据
         * @return {promise}
         * @desc   确认签署发送的请求
         */
        postConfirmSign(data) {
            const contractId = this.contractId || this.$route.query.contractId; // 部分场景取不到contractId
            // return this.$http.post(`${signPath}/contracts/${this.contractId}/confirm`, data, { noToast: 1 });

            // 混合云合同签署时提交type=text的labels值
            if (this.isHybridCloudContract) {
                data = { signerFillLabels: this.signerFillLabels, ...data };
            }
            return this.$hybrid.makeRequest({
                url: `${signPath}/contracts/${contractId}/confirm`,
                hybridTarget: '/contracts/confirm',
                method: 'post',
                data: {
                    contractId, // 混3在 data 中增加 contractId
                    ...data,
                    ...(this.isVisitor && { token: this.urlToken }),
                },
                contractId,
            });
        },
        /**
         * @description 跳转去刷脸
         */
        handleToFace() {
            this.$emit('toFace');
        },
        initType() {
            this.isVerifyCodeValid = !this.showSignPasswordPriority;
        },
        handleClickEventTrack(index) {
            const eventMap = {
                1: '忘记密码',
                2: '语音验证码',
                3: '短信验证码',
                4: '邮箱验证码',
                5: '使用签约密码校验',
                6: '使用验证码校验',
                7: '确认签署',
                8: '确定',
            };
            this.sensorsEventName && this.$sensors.track({
                eventName: `Ent_${this.sensorsEventName}Window_BtnClick`,
                eventProperty: {
                    page_name: `${this.sensorsPageName}`,
                    window_name: '签约校验',
                    icon_name: eventMap[index],
                    ...this.sensorsTrackContractInfo,
                },
            });
        },
        getIfShowSignPwdRemind() {
            if (!this.signPwdExistence) {
                this.getIfTriggerSignPwd()
                    .then((res) => {
                        this.showSignPwdRemind = res.data?.value;
                    });
            }
        },
        initImgUrl() {
            this.imgUrl =  require(`src/common/assets/img/sign/signPwdRemind${this.$i18n.locale === 'ja' ? '-ja' : (this.$i18n.locale === 'en' ? '-en' : '')}.png`);
        },
    },
    created() {
        this.getNoticeType()
            .then(res => {
                this.mailNotice = res.data.filter(item => item.type === 1)[0] || '';
                this.phoneNotice = res.data.filter(item => item.type === 2)[0] || '';
                this.notice = this.phoneNotice.code || this.mailNotice.code;
                this.sendType = this.phoneNotice.code ? 'S' : 'E';
            }).then(() => {
                this.$watch('sendType', function() {
                    if (this.$refs.btn.time === 0) {
                        this.send();
                    }
                });
            });
        this.getSignPwdExist()
            .then((res) => {
                this.signPwdExistence = res.data?.value;
                this.getIfShowSignPwdRemind();
            });
    },
    mounted() {
        this.getContractSenderConfig();
        this.initImgUrl();
    },
};
</script>
<style lang="scss">
.sign-pwd-remind {
    &__mask {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: #000;
        opacity: 0.5;
        z-index: 9998;
    }
    &__img {
        width: 384px;
        position: absolute;
        z-index: 9999;
        bottom: 66px;
        right: 32px;
    }
    &__icon {
        position: absolute;
        width: 24px;
        z-index: 9999;
        bottom: 33px;
        right: 200px;
        cursor: pointer;
    }
}
</style>
