<!-- 签约校验弹窗-二维码校验方式 -->
<template>
    <div class="validation-qrCode">
        <div class="certificate-tip">
            {{ signType === 'SEAL_AND_SIGNATURE' ? $t('signPC.verifyAllTip'): $t('signPC.verifyTip') }}
        </div>
        <!-- web、app 登录后第一次需要扫码 -->
        <div v-if="isFirstTime" class="qrCodeFirstTime">
            <!-- 扫码中 -->
            <div class="verify-inProcess" v-if="qrCodeStatus === 'scaning'">
                <p>
                    <img class="qrcodeImg" :src="qrCodePath" @click="getQrCodeImg" alt="" />
                </p>
                <p>{{ $t('sign.appScanVerify') }}</p>
                <p class="line-downloadApp">
                    <span>{{ $t('sign.downloadBSApp') }}</span>
                    <img src="~img/app-download-qrcode.png" width="76" alt="">
                </p>
            </div>
            <!-- 扫码成功 -->
            <div class="verify-success" v-if="qrCodeStatus === 'success'">
                <p><i class="el-icon-ssq-right-filling"></i></p>
                <p>{{ $t('sign.scanned') }}</p>
                <p>{{ $t('sign.confirmInBSApp') }}</p>
            </div>
            <!-- 扫码失败 -->
            <div class="verify-faliure" v-if="qrCodeStatus === 'failure'">
                <p>{{ $t('sign.qrCodeExpired') }}</p>
                <p>
                    <img src="~img/app-download-qrcode.png" width="118" height="118" alt="" @click="getQrCodeImg" />
                    <i class="el-icon-ssq-huifu" @click="getQrCodeImg"></i>
                </p>
            </div>
        </div>
        <!-- web、app 未重新登录，直接在 app 中点击校验 -->
        <div v-else class="qrCodeVerified">
            <template v-if="!isWaitingAPP">
                <el-button @click="startAppVerify">{{ $t('sign.appKey') }}</el-button>
            </template>
            <template v-else>
                {{ $t('sign.confirmInBSApp') }}
            </template>
            <p><span @click="getQrCodeImg(true)">{{ $t('sign.goToScan') }}</span></p>
        </div>
    </div>
</template>

<script>
import { goReturnUrl } from 'src/common/utils/returnUrl.js';
export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['channel', 'contractId', 'receiverId', 'ssoSigning', 'signerFillLabels', 'isHybridCloudContract', 'isAllSignaturesInactive', 'returnUrl', 'signType'],
    data() {
        return {
            isFirstTime: true,
            isWaitingAPP: false,
            qrCodeStatus: 'scaning', // 扫码中: scaning, 扫码失败: failure, 扫码成功: success
            qrCodeId: null,
            qrCodePath: '',
            qrCodeToken: null,
            interval: null,
            interValCount: 0,
        };
    },
    methods: {
        // 获取二维码地址
        getQrCodeImg(isHandover) {
            this.$http(`/contract-api/contracts/${this.contractId}/receivers/${this.receiverId}/app-confirm-qrcode-url${isHandover ? '?refresh=true' : ''}`)
                .then(res => {
                    if (res.data.needShowingQR) {
                        this.isFirstTime = true;
                        this.qrCodeStatus = 'scaning';
                        this.qrCodePath = `data:image/jpeg;base64,${res.data.qrCodeBase64}`;
                        this.qrCodeToken = res.data.token;

                        this.$nextTick(() => {
                            this.handleInterval();
                        });
                    } else {
                        this.isFirstTime = false;
                    }
                });
        },
        // 轮询
        handleInterval() {
            clearInterval(this.interval);
            this.interval = setInterval(this.requestStatus, 3000);
        },
        // 点击 APP 安全校验
        startAppVerify() {
            this.isWaitingAPP = true;

            this.$http(`/contract-api/contracts/${this.contractId}/receivers/${this.receiverId}/send-app-confirm-notice`)
                .then(res => {
                    this.qrCodeToken = res.data.token;
                    this.$nextTick(() => {
                        this.handleInterval();
                    });
                });
        },
        // 轮询请求
        requestStatus() {
            // 一分钟后二维码失效状态，清空倒计时，重置计数器
            if (+this.interValCount === 20) {
                clearInterval(this.interval);
                this.interValCount = 0;

                if (this.isFirstTime) {
                    this.qrCodeStatus = 'failure';
                } else {
                    this.isWaitingAPP = false;
                }

                return;
            } else {
                this.$http.get(`/contract-api/contracts/${this.contractId}/receivers/${this.receiverId}/status-loop/${this.qrCodeToken}`)
                    .then(res => {
                        switch (res.data.tokenStatus) {
                            case 'INIT':
                                break;
                            case 'USED':
                                this.qrCodeStatus = 'success';
                                break;
                            case 'CONFIRMED':
                                this.$emit('verifyDone');
                                clearInterval(this.interval);
                                this.$MessageToast.success(this.$t('sign.signSuc'))
                                    .then(() => {
                                        // 当前是档案+采集流程，签署成功跳转采集成功页面
                                        if (sessionStorage.getItem('entCustomerInfo')) {
                                            const entCustomerInfo = JSON.parse(sessionStorage.getItem('entCustomerInfo'));
                                            return location.href = `${location.origin}/damp/pages/success/success?archiveId=${entCustomerInfo.archiveId}`;
                                        }
                                        /* 如果客户定义了跳转地址，则首先跳转 */
                                        if (this.returnUrl) {
                                            goReturnUrl(this.returnUrl);
                                            return;
                                        }
                                        if (this.ssoSigning.signing_agree && this.ssoSigning.signing_agree.url) {
                                            this.$router.push(`${this.ssoSigning.signing_agree.url}?status=6&contractId=${this.contractId}&type=sign`);
                                        } else {
                                            // 链接进来
                                            if (this.channel === 'notice') {
                                                this.$router.push(`/sign/sign-tip?status=6&contractId=${this.contractId}&type=sign${this.isAllSignaturesInactive ? '&applySeal=true' : ''}&receiverId=${this.receiverId}`);
                                            } else { // 登录进来
                                                this.$router.push('/doc/list'); // 跳到收件箱
                                            }
                                        }
                                    });
                                break;
                            case 'CANCELED':
                                if (!this.isFirstTime) {
                                    clearInterval(this.interval);
                                    this.isWaitingAPP = false;
                                }
                                break;
                            case 'EXPIRED':
                                clearInterval(this.interval);
                                if (this.isFirstTime) {
                                    this.qrCodeStatus = 'failure';
                                } else {
                                    this.isWaitingAPP = false;
                                }
                                break;
                        }
                    }).catch(err => {
                        console.log(err);
                        clearInterval(this.interval);
                    });
            }

            this.interValCount++;
        },
    },
    created() {
        this.getQrCodeImg();
    },
    // 销毁并清除定时器
    destroyed() {
        clearInterval(this.interval);
    },
};
</script>
