<!-- 业务组件：签署——签约校验 样式更改需注意，批量签署页 Doc-Batch-Check-Sign.vue 用到了此组件！-->
<template>
    <div class="SignValidation-comp clear">
        <div class="signvalidation-comtainer">
            <!-- 复用验证码时不显示签署校验上方的 tabs -->
            <ul class="validationNavList ja-page-hidden" v-if="showTabs">
                <li v-for="(item, index) in verifyTypes" :class="activeClass(index)" :key="item.key" @click="chooseVerifyType(index)"> {{ item.name }} </li>
            </ul>
            <div class="signvalidation-types">
                <component
                    :is="curVerifyType"
                    :ssoSigning="ssoSigning"
                    :channel="channel"
                    :contractId="contractId"
                    :returnUrl="returnUrl"
                    :receiverId="receiverId"
                    :hidePass="hidePass"
                    :userAccount="userAccount"
                    :signerFillLabels="signerFillLabels"
                    :isHybridCloudContract="isHybridCloudContract"
                    :isAllSignaturesInactive="isAllSignaturesInactive"
                    :isApplySeal="isApplySeal"
                    :signType="signType"
                    :isReuseVerifyCode="isReuseVerifyCode"
                    :sensorsTrackContractInfo="sensorsTrackContractInfo"
                    :showSignPasswordPriority="showSignPasswordPriority"
                    :showSignPasswordEntry="showSignPasswordEntry"
                    :isSignPasswordPriorityOpen="isSignPasswordPriorityOpen"
                    :shouldSignVaSecondCheck="shouldSignVaSecondCheck"
                    :contractData="contractData"
                    ref="component"
                    @toFace="handleToFace"
                    @verifyDone="handleDone"
                    @openSetSignPwdDialog="$emit('openSetSignPwdDialog')"
                    :contractAliasText="contractAliasText"
                    :receiver="receiver"
                ></component>
            </div>
        </div>
    </div>
</template>

<script>
import VerCodeVerify from './VerCodeVerify.vue';
import QrCodeVerify from './QrCodeVerify.vue';
import { mapGetters } from 'vuex';

export default {
    components: {
        VerCodeVerify,
        QrCodeVerify,
    },
    // eslint-disable-next-line vue/require-prop-types
    props: ['visible', 'channel', 'contractId', 'receiverId', 'hidePass', 'userAccount', 'signerFillLabels', 'isHybridCloudContract', 'isAllSignaturesInactive', 'returnUrl', 'isApplySeal', 'signType', 'isReuseVerifyCode', 'showSignPasswordPriority', 'showSignPasswordEntry', 'isSignPasswordPriorityOpen', 'shouldSignVaSecondCheck', 'contractAliasText', 'sensorsTrackContractInfo', 'receiver', 'contractData'],
    data() {
        return {
            curVerifyType: 'VerCodeVerify',
            verifyTypes: [{
                key: 'VerCodeVerify', // 验证码、签约密码校验
                name: this.$t('signPC.VerCodeVerify'),
            }, {
                key: 'QrCodeVerify',
                name: this.$t('signPC.QrCodeVerify'),
            }],
            ssoSigning: {}, // 单点登录配置
        };
    },
    computed: {
        ...mapGetters(['getSsoConfig']),
        showTabs() {
            // 隐藏密码校验、非验证码复用、非展示优先签约密码校验的场景
            return  this.hidePass && !this.isReuseVerifyCode && !this.showSignPasswordPriority;
        },
    },
    watch: {
        visible: {
            handler(val) {
                if (val) {
                    this.curVerifyType = 'VerCodeVerify';
                    this.$nextTick(() => {
                        this.$refs.component.initType();
                    });
                }
            },
            immediate: true,
        },

    },
    methods: {
        // 当前激活选项卡的 class
        activeClass(index) {
            return this.curVerifyType === this.verifyTypes[index].key ? 'active' : '';
        },
        // 选择校验方式
        chooseVerifyType(index) {
            this.curVerifyType = this.verifyTypes[index].key;
        },
        /**
         * @description 跳转去刷脸
         */
        handleToFace() {
            this.$emit('toFace');
        },
        handleDone() {
        },
    },
    beforeMount() {
        this.ssoSigning = this.getSsoConfig.signing || {};
    },
};
</script>

<style lang="scss">
   .sign-el-dialog.sign-el-dialog_bigger {
       .el-dialog {
           width: 500px;
       }
       .SignValidation-comp {
           .el-form-item .verify-flex {
                width: calc(100% - 100px);
                padding-left: 100px;
                .countDown {
                    width: 178px;
                }
           }
           .el-form-item .el-form-item__label {
                width: 100px !important;
            }
       }
   }

	.SignValidation-comp {
		* {
			font-size: 12px;
			// color: #333;
		}

        .signvalidation-comtainer {

            .validationNavList {
                box-sizing: border-box;
                margin-bottom: 5px;
                width: 100%;
                // height: 25px;
                padding: 0 33px;
                line-height: 25px;
                list-style: none;

                li {
                    // float: left;
                    // margin-right: 30px;
                    width: 49%;
                    text-align: center;
                    display: inline-block;
                    font-size: 12px;
                    color: #353535;
                    cursor: pointer;

                    &.active {
                        color: #659FF1;
                        border-bottom: 1px solid #659ff1;
                    }
                }
            }

            .signvalidation-types {
                .validation-qrCode {
                    position: relative;
                    height: 250px;

                    .qrCodeFirstTime {
                        padding-top: 10px;
                        text-align: center;

                        p{
                            margin-bottom: 10px;
                        }

                        .verify-inProcess{

                            .qrcodeImg{
                                cursor: pointer;
                            }

                            .line-downloadApp {
                                position: relative;
                                display: inline-block;
                                margin: 5px auto 0;
                                width: 200px;
                                cursor: pointer;
                                color: #007DD5;

                                img {
                                    position: absolute;
                                    display: none;
                                    left: 13px;
                                    bottom: -85px;
                                    box-shadow: 0px 0px 7px 0px #999;
                                }

                                &:hover{
                                    img {
                                        display: block;
                                    }
                                }
                            }
                        }

                        .verify-success {
                            padding-top: 20px;

                            p {
                                font-size: 14px;

                                &:last-child {
                                    font-size: 12px;
                                }
                            }

                            .el-icon-ssq-right-filling{
                                font-size: 28px;
                                color: #009987;
                            }
                        }

                        .verify-faliure {
                            p {
                                position: relative;
                            }

                            img{
                                opacity: 0.3;
                                border: 3px solid #fff;
                            }

                            i{
                                position: absolute;
                                left: 50%;
                                top: 50%;
                                transform: translate(-50%,-55%);
                                border: 9px solid #fff;
                                font-size: 16px;
                                color: #137FD2;
                                border-radius: 50%;
                                cursor: pointer;
                            }
                        }
                    }

                    .qrCodeVerified {
                        position: absolute;
                        left: 50%;
                        top: 50%;
                        transform: translate(-50%,-50%);
                        font-size: 12px;
                        color: #3F3F3F;

                        .el-button{
                            border: none;
                            width: 127px;
                            height: 31px;
                            padding: 0;
                            line-height: 31px;
                            background: #659FF1;
                            border-radius: 4px;
                            span {
                                color: #fff;
                                font-size: 14px;
                                font-weight: normal;
                            }
                        }

                        p{
                            margin-top: 10px;
                            text-align: center;

                            span{
                                font-size: 14px;
                                cursor: pointer;
                                color: #007DD5;
                            }
                        }
                    }
                }
            }
        }
        .certificate-tip {
            padding: 8px 45px;
            background-color: #ebf1f6;
            &:before {
                width: 10px;
                height: 12px;
                display: inline-block;
                content: '';
                vertical-align: middle;
                background: {
                    image: url("~img/secure-icon.png");
                    size: 10px 12px;
                    position: center;
                    repeat: no-repeat;
                };
            }
        }
        .sign-va-form {
            padding: 25px 33px;
            overflow: hidden;
        }

		.error {
			position: absolute;
			top: -40px;
			left: 0;
			width: 382px;
			background-color: #fffaf7;
			border: 1px solid #f76b26;
			.login-error-i{
				position: absolute;
				top: 14px;
				font-size: 14px;
				margin: -2px 5px 0 15px;
				vertical-align: middle;
				color: #f76b26;
			}
			.login-error-r {
				height: 38px;
				line-height: 38px;
				padding-left: 40px;
				span {
					font-size: 12px;
					color: #666;
				}
				.highlight {
					color: #f76b26;
				}
			}
		}

		.el-form-item {
            margin-bottom: 0 !important;
			.el-form-item__label {
				width: 70px!important;
				margin-bottom: 2px;
			}
			.el-input__inner {
				width: 248px;
				height: 28px;
				line-height: 28px;
				padding-left: 6px;
			}
            .verify-flex {
                position: relative;
                display: block;
                line-height: 28px;
                font-size: 0;
                .verify-input {
                    line-height: 28px;
                    font-size: 0;
                    input {
                        height: 28px;
                        [dir="rtl"] & {
                            padding-right: 98px;
                        }
                    }
                }
                .countDown {
                    position: absolute;
                    top: 0;
                    right: 0;
                    width: 92px;
                    height: 28px;
                    line-height: 28px;
                    border: 1px solid #ccc;
                    border-radius: 1px;
                    color: #333;
                }
            }
		}

		.forgetPass-form-item {
            margin-top: -1px;
            margin-bottom: 5px;
			text-align: right;
            span {
                cursor: pointer;
                color: #127fd2;
            }
		}
    .confirm-sign-tip{
          p{
              text-align: center;
          }
     }
		.switch {
			text-align: right;
            color: #000;
		}
		.highlight {
			color: #127fd2;
			cursor: pointer;
		}
		.countdown-item {
			position: relative;
		}
		.phone {
			font-size: 14px;
			font-weight: bold;
		}
		.reason {
			.el-form-item__label {
				width: 44px!important;
			}
		}
        .valid-type-switch {
            cursor: pointer;
            text-align: right;
            color: #127fd2;
            &.inline-gap {
                padding-right: 5px;
                margin-right: 5px;
                border-right: 1px solid #666;
            }
        }
		.confirm-sign-btn {
			float: right;
			margin-top: 16px;
            background-color: #127fd2;
            border: none;

			&:hover{
				background-color: #1687dc;
			}

			&.el-button {
				width: 100px;
				height: 34px;
				font-size: 12px;

				span{
					font-weight: normal;
					font-size: 12px;
				}
			}
		}
	}

</style>
