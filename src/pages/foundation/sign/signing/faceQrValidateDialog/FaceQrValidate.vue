<template>
    <!-- 对话框：二维码打开 -->
    <div>
        <el-dialog
            size="samll"
            :class="{'qr-sign-dialog register ssq-dialog':!faceFirstExceed,'face-first-exceed':faceFirstExceed}"
            :title="title"
            :visible.sync="visible"
            @close="handleClose"
            @open="qrSignKey=Math.random()"
        >
            <template v-if="faceFirstExceed">
                <div>
                    {{ $t('sign.faceFirstExceed') }}
                </div>
                <span slot="footer" class="dialog-footer">
                    <el-button type="primary" @click="changeFaceToValidation">{{ $t('sign.continue') }}</el-button>
                </span>
            </template>
            <div v-else class="face-content">
                <template v-if="!faceFailed">
                    <p>{{ receiver.signType === 'SEAL_AND_SIGNATURE' ? $t('signPC.verifyAllTip') : $t('sign.digitalCertificateTip') +'，'+ $t('sign.signDes') }}</p>
                    <p><i class="el-icon-ssq-tishi1"></i>&nbsp;{{ $t('sign.requiredFaceSign') }}</p>
                    <p>{{ $t('infoProtectDialog.userAuth') }}<span class="highlight" @click="showServiceDetail">{{ $t('infoProtectDialog.titleWithSeperator') }}</span></p>
                    <!--超过次数-->
                    <template v-if="hasExceed">
                        <span>{{ $t(receiver.faceVerify ? 'sign.upSignReq' : 'sign.faceLimit') }} </span>
                    </template>
                    <template v-else>
                        <qriously class="code" :class="{'invalid': showRefresh}" :value="qrSignImgUrl" :size="186" />
                        <template v-if="showRefresh">
                            <p class="refresh-tip">{{ $t('sign.qrcodeInvalid') }}</p>
                            <i @click="initFaceQrcode" class="el-icon-ssq--bs-shuaxincopy"></i>
                        </template>
                    </template>
                    <p v-if="changeFaceToValidationShow && !receiver.faceVerify">
                        {{ $t('sign.switchTo') }}
                        <span class="highlight" @click="changeFaceToValidation">{{ $t('sign.verCodeVerify') }}</span>
                    </p>
                </template>
                <template v-if="faceFailed">
                    <p>{{ $t('sign.verifyTry') }}</p>

                    <div class="account-info">
                        <span class="label">{{ $t('sign.nameIs') }}:&nbsp;</span>
                        <span>{{ name }}</span>
                    </div>
                    <div class="account-info">
                        <span class="label">{{ $t('sign.IDNumIs') }}:&nbsp;</span>
                        <span class="IDNumber">{{ IDNumber }}</span>
                    </div>

                    <div class="opt-button">
                        <el-button type="primary" @click="handleRetry">{{ $t('sign.retry') }}</el-button>
                    </div>
                </template>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'FaceQrValidate',
    props: {
        dialogVisible: {
            type: Boolean,
            default: false,
        },
        receiver: {
            type: Object,
            default: () => {},
        },
        changeFaceToValidationShow: { // 标记是否是验证码切换过来
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            contractId: this.$route.query.contractId,
            qrSignKey: '',
            visible: false,
            qrSignImgUrl: '',
            interval: '', // 刷脸结果查询轮询
            hasExceed: false,
            timeCount: 0, // 通过累计轮询次数来估算链接是否过期
            faceFailed: false, // 刷脸对比失败
            showRefresh: true, // 是否展示刷新二维码
            name: '',
            IDNumber: '',
            faceWay: '0', // 0：默认，1：腾讯，2：支付宝
        };
    },
    computed: {
        // 刷脸提示标题
        faceWayTitle() {
            const map = {
                0: this.$t('sign.pleaseScanToSign'),
                // 1: this.$t('sign.pleaseScanWechat'),
                // 2: this.$t('sign.pleaseScanAliPay'),
            };
            return  Object.keys(map).includes(`${this.faceWay}`) ? map[this.faceWay] : map['0'];
        },
        title() {
            if (this.faceFirstExceed) {
                return this.$t('sign.faceFailedtips');
            } else if (this.faceFailed) {
                return this.$t('sign.faceFailed');
            } else {
                return this.faceWayTitle;
            }
        },
        faceFirstExceed() {
            return this.hasExceed && this.receiver.faceFirst;
        },
    },
    methods: {
        handleRetry() {
            this.faceFailed = false;
            this.clearFaceResult().then(() => {
                this.initFaceQrcode();
            });
        },
        handleClose() {
            this.visible = false;
            clearInterval(this.interval);
            this.$emit('close');
        },
        clearFaceResult() {
            return this.$http.delete(`/users/auth/face/clear-cache?contractId=${this.contractId}`); // 清除刷脸结果缓存
        },
        // 展示合规提示详情
        showServiceDetail() {
            this.$emit('showServiceDetail');
            this.handleClose();
        },
        changeFaceToValidation() {
            this.handleClose();
            this.$emit('switch-validate');
        },
        // 获取刷脸二维码图片
        getQrSignImgUrl() {
            const paramsData = {
                targetUrl: `/auth-p/person/sign/face/redirect`, // 要跳转的地址，不包含域名
                attachment: `?contractId=${this.contractId}&isNonMainland=${this.receiver.nonMainLander}${this.receiver.faceFirst ? '&faceFirst=true' : ''}`,
                contractId: this.contractId,
            };
            return this.$http({
                method: 'get',
                url: '/users/face-auto-login/url',
                params: paramsData,
            });
        },
        getFacedInfo() {
            return this.$http.get(`/users/auth/face/result-polling?contractId=${this.contractId}${this.receiver.faceFirst ? '&faceFirst=true' : ''}`, {
                noToast: 1,
            });
        },
        // 轮询刷脸结果
        intervalFace() {
            const _this = this;
            this.getFacedInfo()
                .then((res) => {
                    this.timeCount = this.timeCount + 1;
                    if (this.timeCount > 600) {
                        this.timeCount = 0;
                        clearInterval(_this.interval);
                        this.showRefresh = true;
                    }
                    const { faceAuthDone, faceAuthPass, remainsForALIPAY = 0, remainsForWEBANK = 0 } = res.data || {};
                    if (!faceAuthDone) { // 尚未进行刷脸操作，通过是否进入mobile/face/result标记
                        return;
                    } else if (faceAuthPass) { // 刷脸是否通过
                        this.handleClose();
                        this.$emit('faceDone', true);
                    } else { // 刷脸失败
                        clearInterval(_this.interval);
                        this.hasExceed = !remainsForALIPAY || !remainsForWEBANK; // 某类刷脸次数超出即展示验证码入口
                        if (!this.hasExceed) { // 次数剩余则展示重试
                            this.faceFailed = true;
                        }
                    }
                });
        },
        initFaceQrcode() {
            // 刷脸校验，展示二维码，移动端打开
            this.getQrSignImgUrl()
                .then(res => {
                    this.faceWay = res.data.faceWay;
                    this.qrSignImgUrl = res.data.url;
                    this.hasExceed = false;
                    this.visible = true;
                    this.interval = setInterval(this.intervalFace, 3000); // 轮询刷脸结果
                    setTimeout(() => {
                        clearInterval(this.interval);
                    }, 3600000);
                    this.showRefresh = false;
                })
                .catch(() => {
                    this.handleClose();
                });
        },
        initAccountInfo() {
            return this.$http.get(`/users/auth/info`)
                .then((res) => {
                    const resData = res.data;
                    const baseInfo = resData.personAuthVOList[0];
                    this.name = baseInfo.realName;
                    this.IDNumber = baseInfo.idNumber;
                });
        },
    },
    created() {
        this.clearFaceResult();
        this.getFacedInfo()
            .then((res) => {
                const facedInfo = res.data || {};
                if (facedInfo.remainsForWEBANK > 0 && facedInfo.remainsForALIPAY > 0) { // 当日仍有刷脸次数
                    this.initFaceQrcode();
                } else { // 如果刷脸超过3次，显示验证码校验入口
                    this.hasExceed = true;
                    this.visible = true;
                }
            });
        this.initAccountInfo();
    },
};
</script>

<style lang="scss">
    // 扫码签名
    .qr-sign-dialog {
        .el-dialog {
            width: 400px;
            .el-dialog__body {
                .face-content {
                    color: #999;
                    p {
                        font-size: 14px;
                        color: #999;
                        margin-bottom: 15px;
                    }
                    .highlight {
                        color: #127fd2;
                        cursor: pointer;
                    }

                    .account-info {
                        text-align: left;
                        margin-bottom: 10px;
                        padding-left: 50px;
                        .label {
                            width: 80px;
                            display: inline-block;
                        }
                    }
                    .code {
                        text-align: center;
                    }
                    .invalid.code {
                        opacity: 0.1;
                    }

                    .el-icon-ssq--bs-shuaxincopy {
                        position: relative;
                        display: inline-block;
                        top: -120px;
                        font-size: 24px;
                        cursor: pointer;
                    }
                    .refresh-tip {
                        margin-bottom: 0;
                    }
                    .opt-button {
                        overflow: auto;
                        margin-top: 30px;
                        .el-button {
                            float: none;
                        }
                    }

                }

            }
        }
    }
    .face-first-exceed{
        .el-dialog {
            width: 320px;
            .el-dialog__body {
                padding: 30px 20px;
                color: #666;
                font-size: 14px;
                word-break: break-all;
            }
            .el-dialog__footer{
                .el-button{
                    padding: 7px 15px;
                    font-size: 14px;
                    border-radius: 2px;
                    background-color: $theme-color;
                &:hover {
                    background-color: $btn-primary-hover-color;
                }
            }
        }
        }
    }
</style>
