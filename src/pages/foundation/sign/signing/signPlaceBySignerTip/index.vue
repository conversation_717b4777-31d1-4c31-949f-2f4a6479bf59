<template>
    <el-dialog
        class="sign-place-by-signer-tip"
        :title="tipTitle"
        :visible.sync="dialogVisible"
    >
        <div v-if="isShowNormal">
            <p class="top-tip">{{ $t('sign.finishSignatureBeforeSign') }}</p>
            <div class="main-tip">
                <p class="main-tip-item">{{ $t('signPC.signPlaceBySigner.signTip.one') }}</p>
                <img class="main-tip-item-img1" src="~img/sign/clickPosition.png" alt="">
                <p class="main-tip-item">{{ $t('signPC.signPlaceBySigner.signTip.two') }}</p>
                <img class="main-tip-item-img2" src="~img/sign/clickSignatureOrSeal.png" alt="">
            </div>
        </div>
        <div v-else>
            <p class="top-tip">{{ $t('signPC.signPlaceBySigner.finishSignatureBeforeSign') }}</p>
            <div class="main-tip">
                <img class="main-tip-item-img1" src="~img/sign/howDragSignature.png" alt="">
            </div>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button class="i-know" type="primary" @click="dialogVisible = false">{{ $t('sign.submit') }}</el-button>
        </span>
    </el-dialog>
</template>

<script>
export default {
    name: 'SignPlaceBySignerTip',
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        signatureLabels: {
            type: Array,
            default: () => [],
        },
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                // 如果是关闭弹窗
                if (!value) {
                    this.$emit('close');
                }
            },
        },
        // 合同中的个人签名
        signatureLabelList() {
            return this.signatureLabels.filter(signature => ['SIGNATURE', 'SEAL'].includes(signature.type));
        },
        tipTitle() {
            return this.$t('sign.tip');
        },
        // 如果没有拖签名 那么显示点击签署按钮显示指导签名
        isShowNormal() {
            return !!this.signatureLabelList.length;
        },
    },
};
</script>

<style lang="scss">
.sign-place-by-signer-tip{
    .el-dialog{
        width: 400px;
        .el-dialog__body{
            padding: 20px 30px;
            .top-tip{
                margin-bottom: 22px;
            }
            .main-tip{
                img{
                    width: 340px;
                }
                .main-tip-item-img1{
                    margin-bottom: 10px;
                }
                .main-tip-item{
                    margin-bottom: 10px;
                }
            }
        }
    }
}
</style>
