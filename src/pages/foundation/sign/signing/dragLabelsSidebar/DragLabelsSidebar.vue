<template>
    <div class="seals-list fl">
        <div class="des">
            <span v-if="satisfySignDecision">{{ $t('sign.dragSignatureTip') }}</span>
        </div>
        <div v-if="needAdded" class="have-no-seal">
            <div class="subject-name-des">
                <p v-if="subjectName">{{ $t('sign.contractPartyFilled') }}</p>
                <p v-if="subjectName" class="signer-name">{{ subjectName }}</p>
                <div class="message-box">
                    <i class="el-icon-ssq-tishi1"></i>
                    <span>{{ $t('sign.alreadyExists') }}&nbsp;<span class="strong">{{
                        subjectName
                    }}</span>,</span>
                    <span>{{ $t('sign.contratAdmin') }}}
                        <span class="highLight">{{ receiver.userAccount }}</span>{{ $t('sign.addToEnt') }}</span>
                    <p>
                        {{ $t('sign.admin') }}: <span>{{ receiver.entAdminName }}</span>&nbsp;<span class="highLight">{{ receiver.entAdminAccount }}</span>
                    </p>
                </div>
            </div>
            <div class="auth-btn notice-btn">
                <el-button type="primary" @click.stop.prevent="handleNoticeAdmin">{{
                    $t('sign.noticeToManager')
                }}</el-button>
            </div>
        </div>
        <div v-if="needToAuth" class="have-no-seal">
            <img v-if="isEnt" src="~img/have-no-seal.png" class="no-seal-icon" />
            <img v-else src="~img/have-no-signature.png" class="no-sig-icon" />
            <div class="subject-name-des">
                <p v-if="subjectName">{{ $t('sign.contractPartyFilled') }}</p>
                <p v-if="subjectName" class="signer-name">{{ subjectName }}</p>
                <p v-if="!needMoreAuth">
                    <i class="el-icon-ssq-tishi1"></i>{{ $t('sign.needVerification') }}
                </p>
                <p v-if="needMoreAuth">
                    <i class="el-icon-ssq-tishi1"></i>{{ $t('sign.personalMaterials') }}
                </p>
            </div>
            <div v-if="isEnt" class="auth-btn">
                <el-button type="primary" @click.stop.prevent="goToAuth">{{
                    $t('sign.goToVerifyEnt')
                }}</el-button>
                <el-button
                    v-if="needNotifyAdmin"
                    class="is-default"
                    @click.stop.prevent="notifyAdmin"
                >{{ $t('sign.requestOthersToContinue') }}</el-button>

                <el-button
                    v-else
                    class="is-default"
                    @click.stop.prevent="handleAskForAuth"
                >{{ $t('sign.requestSomeone') }}</el-button>
            </div>
            <div v-else class="auth-btn">
                <el-button
                    v-if="!needMoreAuth"
                    type="primary"
                    @click.stop.prevent="goToAuth"
                >{{ $t('sign.gotoAuthPerson') }}</el-button>
                <el-button
                    v-if="needMoreAuth"
                    type="primary"
                    @click.stop.prevent="goToAuth"
                >{{ $t('sign.continueVeri') }}</el-button>
            </div>
        </div>
        <div v-if="needSwitchIdentity" class="subject-select-wrap">
            <div class="subject-name-des subject-name-wrap">
                <p v-if="subjectName">{{ $t('sign.contractPartyFilled') }}</p>
                <p v-if="subjectName" class="signer-name">{{ subjectName }}</p>
                <p>{{ $t('sign.youCanChooseIdentityBlow') }}</p>
            </div>
            <ul v-if="isEnt">
                <li
                    class="subject-item"
                    v-for="(item, i) in otherEntSubject"
                    :key="i"
                    @click="clickOtherEntSubjectBtn(i)"
                >
                    <el-radio class="radio" v-model="selectedSubjuect" :label="i">{{
                        item.entName
                    }}</el-radio>
                </li>
            </ul>
            <ul v-else>
                <li @click="clickOtherEntSubjectBtn()" class="subject-item">
                    <el-radio class="radio" v-model="selectedSubjuect" label="1">{{
                        receiver.signerName
                    }}</el-radio>
                </li>
            </ul>
        </div>
        <ul v-if="satisfySignDecision" class="normal-list">
            <div v-if="needAddSeal" class="have-no-seal">
                <div>{{ $t('sign.lacksSealNeedAppend') }}</div>
                <div class="auth-btn">
                    <el-button type="primary" @click.stop.prevent="handleAddSeal">{{
                        $t('sign.gotoAppendSeal')
                    }}</el-button>
                </div>
            </div>
            <div v-if="needDistributeSeal" class="have-no-seal">
                <div>{{ $t('sign.needDistributeSealToSelf') }}</div>
                <div class="auth-btn">
                    <el-button type="primary" @click.stop.prevent="handleAddSeal">{{
                        $t('sign.manageSeal')
                    }}</el-button>
                </div>
            </div>
            <div v-if="haveNoUseableSeals" class="have-no-seal">
                <img v-if="isEnt" src="~img/have-no-seal.png" class="no-seal-icon" />
                <div>{{ $t('sign.noSealAvailable') }}</div>
            </div>
            <li
                v-for="(seal) in mySeals"
                :key="seal.sealId"
                class="side-seal"
                @mousedown="onDragStart($event, 'SEAL', seal.sealId, seal.fileId)"
            >
                <img
                    :src="sealImgURL(seal.sealId, seal.fileId)"
                    class="seal-img"
                    :alt="$t('sign.seal')"
                />
                <div class="side-label-des">{{ seal.name }}</div>
            </li>
            <div v-if="!isEnt && !signatures.length" class="no-sig-des">
                {{ $t('sign.needDrawSignatureFirst') }}
            </div>
            <li
                v-for="(signature) in signatures"
                :key="`${signature.sigId}-${signature.fileId}`"
                class="side-signature"
                @mousedown="
                    onDragStart($event, 'SIGNATURE', signature.sigId, signature.fileId)
                "
            >
                <img
                    :src="signatureImgURL(signature.sigId, signature.fileId)"
                    alt=""
                    class="sig-img"
                />
                <div class="side-label-des">{{ signature.name }}</div>
            </li>
            <li class="sign-date" @mousedown="onDragStart($event, 'DATE')">
                <div class="date">{{ signDate }}</div>
                <div class="side-label-des">{{ $t('sign.signDate') }}</div>
            </li>
        </ul>
        <div v-if="showUnauthSeal" class="unauth-seal-tip">
            {{ $t('sign.chooseSealAfterAuth') }}
            <div class="auth-btn">
                <el-button type="primary" @click.stop.prevent="goToAuth">{{
                    $t('sign.goToVerifyEnt')
                }}</el-button>
            </div>
        </div>
        <div v-if="satisfySignDecision && !isEnt" class="hand-write-wrap">
            <el-button class="is-default" @click.stop.prevent="handleAddHandWrite">{{
                $t('sign.appendDrawSignature')
            }}</el-button>
        </div>
    </div>
</template>

<script>
import { hasPrivilege } from 'src/common/utils/headInfo.js';
import { dateGenerator } from 'src/common/utils/fn.js';
import Bus from 'components/bus/bus.js';

export default {
    name: 'DragLabelsSiderbar',
    props: [
        // eslint-disable-next-line vue/require-prop-types
        'signDecision', 'mySeals', 'sealsLength', 'signatures', 'isEnt', 'subjectName', 'receiver', 'otherEntSubject', 'authorizationVis',
    ],
    data() {
        return {
            selectedSubjuect: '',
            contractId: this.$route.query.contractId,
        };
    },
    computed: {
        // 签署日期
        signDate() {
            // 返回今天的日期 格式：2018年11月11日
            return dateGenerator();
        },
        // 企业未实名且不要求实名签署时显示'未认证企业印章'
        showUnauthSeal() {
            return (
                this.isEnt &&
                !this.receiver.hasAuthenticated &&
                !this.receiver.requireIdentityAssurance
            );
        },
        // 是否有印章管理权限
        haSealManagerPri() {
            return hasPrivilege('SEAL_AND_SIGNATURE');
        },
        // 要求实名而未实名
        needToAuth() {
            const reg = /NOT_AUTHENTICATE|SIMPLE_AUTHENTICATE|MORE_AUTHENTICATE/i;
            return reg.test(this.signDecision);
        },
        // 需要补充认证
        needMoreAuth() {
            return this.signDecision === 'MORE_AUTHENTICATE';
        },
        // 未加入到企业中
        needAdded() {
            return this.signDecision === 'NOT_ADD';
        },
        // 需要切换签约主体
        needSwitchIdentity() {
            return this.signDecision === 'USE_MY_IDENTITY';
        },
        // 满足签署条件
        satisfySignDecision() {
            return this.signDecision === 'NORMAL' || this.showUnauthSeal;
        },
        // 已实名的企业
        authenticatedEnt() {
            return (
                this.signDecision === 'NORMAL' &&
                this.receiver.hasAuthenticated &&
                this.isEnt
            );
        },
        // 已实名企业用户没有印章并且有管理员权限
        needAddSeal() {
            return (
                this.authenticatedEnt && !this.sealsLength && this.haSealManagerPri
            );
        },
        // 企业有印章，当前用户有印章管理权限但是无印章
        needDistributeSeal() {
            return (
                this.authenticatedEnt &&
                this.sealsLength &&
                !this.mySeals.length &&
                this.haSealManagerPri
            );
        },
        // 已实名企业用户没有印章也没有印章管理权限
        haveNoUseableSeals() {
            return (
                this.authenticatedEnt && !this.mySeals.length && !this.haSealManagerPri
            );
        },
        needNotifyAdmin() {
            return (
                this.signDecision === 'MORE_AUTHENTICATE' &&
                !this.commonHeaderInfo.hasAccountM
            );
        },
    },
    methods: {
        notifyAdmin() {
            // request remote to notify the the admin person
            this.$http
                .post(`/ents/auth/notice-admin-todo/${this.contractId}`)
                .then(() => {
                    this.$MessageToast.success(
                        this.$t('sign.requestOthersToContinueSucceed'),
                    );
                })
                .catch(({ data: { message } }) => {
                    console.log(message);
                });
        },
        goToAuth() {
            if (this.needNotifyAdmin) {
                // 在去验证之前告知管理员
                this.$http
                    .post('/ents/auth/notice-admin-acknowledge')
                    .catch(({ data: { message } }) => {
                        console.log(message);
                    });
            }
            this.$emit('to-auth');
        },
        // 印章图片路径
        sealImgURL(sealId, fileId) {
            return `/ents/${sealId}/seal/${fileId}?access_token=${this.$cookie.get(
                'access_token',
            )}`;
        },
        // 签名图片路径
        signatureImgURL(sigId, fileId) {
            return `/users/signatures/${sigId}/signature/${fileId}?access_token=${this.$cookie.get(
                'access_token',
            )}`;
        },
        // 拖拽事件开始
        onDragStart(e, type, markId, fileId) {
            this.$emit('on-drag-start', e, type, markId, fileId);
        },
        // 请求他人实名
        handleAskForAuth() {
            // this.$emit('update:signQualifiedDialogVisible', true)
            // this.$parent.$refs.signQualifiedDialog.askForSign();
            Bus.$emit('ask-auth');
        },
        // 签署人拖章 点击左边给管理员发通知
        handleNoticeAdmin() {
            // this.$emit('update:signQualifiedDialogVisible', true)
            // this.$parent.$refs.signQualifiedDialog.sendNoticeToAdmin();
            Bus.$emit('notice-admin');
        },
        handleAddSeal() {
            this.$emit('add-seal');
        },
        // 选择我的签约主体
        clickOtherEntSubjectBtn(i) {
            this.$emit('select-other-subject', i);
        },
        handleAddHandWrite() {
            this.$emit('add-hand-write');
        },
    },
};
</script>

<style lang="scss">
.seals-list {
  box-sizing: border-box;
  position: relative;
  border-right: 1px solid $border-color;
  width: 210px;
  height: 100%;
  padding-bottom: 74px;
  overflow-y: scroll;
  .des {
    padding: 10px;
    min-height: 20px;
  }
  .normal-list {
    padding: 20px 10px 0;
    border-top: 1px solid $border-color;
    li {
      &:hover {
        cursor: pointer;
        box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
        border: 1px solid #127fd2;
      }
    }
    .no-sig-des {
      text-align: center;
      margin-bottom: 20px;
    }
  }
  .hand-write-wrap {
    box-sizing: border-box;
    position: fixed;
    z-index: 999;
    background-color: #f3f4f6;
    bottom: 35px;
    height: 74px;
    width: 210px;
    border-top: 1px solid $border-color;
    border-right: 1px solid $border-color;
    .el-button.is-default {
      display: block;
      width: 165px;
      height: 34px;
      margin: 20px auto;
      color: #127fd2;
      border-color: #127fd2;
      border-radius: 1px;
    }
  }
  .side-seal {
    width: 170px;
    background-color: #fff;
    margin: 0 auto 20px;
    border: 1px solid #ddd;
    .seal-img {
      display: block;
      margin: 0 auto;
      padding: 10px 0;
      width: 108px;
    }
  }
  .side-signature {
    width: 170px;
    background-color: #fff;
    border: 1px solid #ddd;
    margin: 0 auto 20px;
    .sig-img {
      display: block;
      height: 60px;
      padding: 5px 0;
      margin: 0 auto;
    }
  }
  .side-label-des {
    line-height: 19px;
    padding: 4px 12px;
    text-align: center;
    background-color: #fbfbfb;
    border-top: 1px solid #eeeeee;
    color: #666;
  }
  .sign-date {
    width: 170px;
    background-color: #fff;
    border: 1px solid #ddd;
    text-align: center;
    margin: 0 auto 30px;
    .date {
      font-family: 'SimSun', 'STSong';
      font-weight: bold;
      padding: 16px 0;
      font-size: 16px;
      white-space: nowrap;
    }
  }
  .unauth-seal-tip {
    border-top: 1px dashed #dddddd;
    padding: 10px 19px 0;
  }
  .have-no-seal {
    width: 170px;
    margin: 0 auto 20px;
    .no-seal-icon {
      display: block;
      margin: 0 auto 12px;
      width: 110px;
    }
    .no-sig-icon {
      display: block;
      margin: 0 auto 20px;
      width: 100px;
    }
    .notice-btn {
      margin-top: 15px;
    }
  }
  .auth-btn {
    margin-top: 10px;
    .el-button {
      width: 100%;
      height: 32px;
      font-size: 12px;
    }
    .is-default {
      margin: 10px auto;
      color: #127fd2;
      border-color: #127fd2;
    }
    .el-button--primary {
      background-color: #127fd2;
      border-color: #127fd2;
      &:hover {
        background-color: #1687dc;
      }
    }
  }
  .message-box {
    padding: 10px;
    line-height: 18px;
    background: #ebf1f6;
    color: #666;
    .strong {
      font-size: 12px;
      color: #333;
      font-weight: bold;
    }
    .highLight {
      margin-left: 5px;
      margin-right: 5px;
      color: #3d6786;
    }
  }
  .subject-name-des {
    font-size: 12px;
    line-height: 18px;
    color: #999999;
    .signer-name {
      color: rgba(0, 0, 0, 0.8);
      line-height: 20px;
      font-size: 12px;
      text-decoration: underline;
      font-weight: bold;
      margin: 5px 5px 15px;
      word-break: break-all;
    }
    .el-icon-ssq-tishi1 {
      color: #f86b26;
      margin-right: 3px;
    }
  }
  .el-icon-ssq-tishi1 {
    margin-right: 5px;
    color: #f86b26;
  }
  .subject-select-wrap {
    .subject-name-wrap {
      margin: 0 20px 5px 20px;
    }
    li {
      position: relative;
    }
    .subject-item {
      padding: 5px 20px;
      line-height: 20px;
      .select-name {
        margin-left: 25px;
      }
      .el-radio {
        width: 100%;
        white-space: normal;
      }
      .el-radio__input {
        position: absolute;
        top: 0;
        left: 0;
      }
      .el-radio__label {
        display: inline-block;
        margin-left: 18px;
      }
      &:hover {
        color: #127fd2;
        background-color: #eeeeee;
      }
    }
  }
}
</style>
