<template>
    <section>
        <sign-read-header v-if="needRead" :isScrollLimitSign="isScrollLimitSign" :contractAlias="contractAlias" @handleRead="handleRead"></sign-read-header>
        <SignHeader
            class="header"
            v-if="hdVisible && !needRead"
            :backConfig="ssoSigning.signing_1_back"
            :title="contractData.contractTitle"
            :rText="rText"
            :noRight="noRight"
            :signatureLabels="signatureLabels"
            :dateLabels="dateLabels"
            :isEnt="isEnt"
            :signerDragLabelTypeList="signerDragLabelTypeList"
            :receiverId="receiver.receiverId"
            :signType="signType"
            :rShow="signType != 'view' && hybridContractServerConnected"
            :sensorsTrackContractInfo="sensorsTrackContractInfo"
            @to-back="toBack"
            @to-next="$emit('to-next')"
            @drag-label="handleDragLabel"
            @open-sign-place-dialog="handleOpenSignPlaceDialog"
        >
            <!-- 私信按钮 -->
            <li v-if="privateLetterBtnShow" class="otherBtn privateLetterBtn" slot="otherBtn" @click="handleClickPriLetterBtn">{{ signType === 'approval' ? $t('sign.approvalInfo'):$t('sign.signNeedKnow') }}</li>
            <!-- 签署时header上的按钮 显示条件：1.签署场景;2.不属于新版本签署人指定签署位置-->
            <template v-if="signType === 'sign' && !signPlaceBySigner" slot="otherBtn">
                <li v-if="showShareViewBtn" class="otherBtn no-triangle" @click="handleShareView">{{ $t('shareView.title') }}</li>
                <li v-if="showTagManageBtn" class="otherBtn no-triangle" @click="onOpenTagManage">{{ $t('sign.setLabel') }}</li>
                <div
                    v-if="signDecision !== 'NEED_SET_APPROVAL_FLOW' && hybridContractServerConnected && !contractData.ifCrossPlatformContract"
                    class="otherBtn"
                    @click="handleOtherOperate"
                >{{ $t('sign.otherOperations') }}
                    <transition name="fadeIn">
                        <div class="other-option-wrap" v-show="isOtherBtnShow">
                            <div class="ssq-btn-confirm other-option" @click="handleClickSignReject" v-if="!contractData.hideRejectButton && !isFromSignAuthorization">{{ $t('sign.refuseSign') }}</div>
                            <div class="ssq-btn-confirm other-option" @click="toggleToRenderImg">{{ $t('sign.fixTextDisplay') }}</div>
                            <!-- 移动到文件夹 -->
                            <MoveToFolder :contractId="contractId"
                                :name="receiver.enterpriseName || receiver.userName"
                                :sensorsTrackContractInfo="sensorsTrackContractInfo"
                                :sensorsEventName="sensorsEventName"
                                :sensorsPageName="sensorsPageName"
                            ></MoveToFolder>
                        </div>
                    </transition>
                </div>
            </template>
            <!-- 审批时header上的按钮 -->
            <template v-else-if="signType === 'approval' && hybridContractServerConnected" slot="otherBtn">
                <div class="detailBtn">
                    <a target="_blank" :href="`/doc/detail/${contractId}`" @click="handleViewDetail">
                        {{ $t('sign.viewContractDetail') }}
                    </a>
                </div>

                <div class="otherBtn" @click="handleOtherOperate">{{ $t('sign.otherOperations') }}
                    <transition name="fadeIn">
                        <div class="other-option-wrap" v-show="isOtherBtnShow">
                            <div class="ssq-btn-confirm other-option" @click="approvalDetailDialogVisible = true">{{ $t('sign.reviewDetails') }}</div>
                            <div class="ssq-btn-confirm other-option" @click="handleReject">{{ $t('sign.disapprove') }}</div>
                            <!-- 移动到文件夹 -->
                            <MoveToFolder :contractId="contractId"
                                :name="receiver.enterpriseName || receiver.userName"
                                :sensorsTrackContractInfo="sensorsTrackContractInfo"
                                :sensorsEventName="sensorsEventName"
                                :sensorsPageName="sensorsPageName"
                            ></MoveToFolder>
                        </div>
                    </transition>
                </div>
            </template>

            <!--合同标签tag-->
            <template slot="subTitle">
                <div class="tags-container">
                    <ContractTag
                        v-for="tag in contractTagsInfo"
                        :key="tag.tagId"
                        :tag="tag"
                        :isCursorDef="true"
                    ></ContractTag>
                </div>
            </template>
            <!-- 添加/删除骑缝章，只有签署的时候才展示，合同装饰骑缝章存在的时候不展示  -->
            <div v-show="isShowRidingSealBtn" slot="otherBtn" class="next" style="font-weight: normal;margin-right: 6px;" @click="$emit('ridigSeal-click')">{{ isShowRidingSeal ? $t('sign.delRidingSeal') : $t('sign.addRidingSeal') }}</div>
        </SignHeader>

        <!-- 预览私信图片（相册） -->
        <Gallery
            @close="previewImgInfo.visible = false"
            :visible="previewImgInfo.visible"
            :title="previewImgInfo.fileName"
            :src="previewImgInfo.previewUrl"
        ></Gallery>

        <!--设置标签-->
        <TagManage
            v-if="isTagManageVisible"
            :params="selectedTagParams"
            :sensorsTrackContractInfo="sensorsTrackContractInfo"
            @close="isTagManageVisible = false"
        ></TagManage>

        <!-- 对话框：审批详情 -->
        <el-dialog
            class="ApprovalDetailDialog"
            :title="$t('sign.reviewDetails')"
            :visible.sync="approvalDetailDialogVisible"
            @open="approvalDetailKey=Math.random()"
        >
            <ApprovalDetail
                ref="ApprovalDetail"
                :approvalType="receiver.defType"
                :receiverId="approvalDetailReceiverId"
                :key="approvalDetailKey"
            >
            </ApprovalDetail>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="approvalDetailDialogVisible=false">
                    {{ $t('sign.close') }}
                </el-button>
            </div>
        </el-dialog>

        <!-- 签署页拒签弹窗 -->
        <SignRejectDialog
            v-if="signRejectDialogVisible"
            :visible.sync="signRejectDialogVisible"
            :channel="channel"
            :contractId="contractId"
            :returnUrl="returnUrl"
            :hidePass="hidePass"
            :userAccount="receiver.userAccount"
            :sensorsTrackContractInfo="sensorsTrackContractInfo"
            :sensorsEventName="sensorsEventName"
            :sensorsPageName="sensorsPageName"
            :receiver="receiver"
            :contractData="contractData"
        >
        </SignRejectDialog>

        <!-- 审批须知和签约须知的弹窗 -->
        <el-dialog
            :close-on-click-modal="false"
            class="privateLetter-dialog"
            :title="signType === 'approval'?$t('sign.approvalInfo'):$t('sign.signNeedKnow')"
            :before-close="handleClickReadLetter"
            :visible.sync="privateLetterVisible"
        >
            <h2 class="privateLetter-title">{{ signType === 'approval' ? $t('sign.approveNeedKnowFrom', {sender: letterSender,sendEmployeeName: sendRealEmployeeName,approvalType:approvalTypeText}):$t('sign.signNeedKnowFrom', {sender: letterSender}) }}：</h2>
            <!--审批时，当审批资料文本内容，附件内容都不存在时展示-->
            <div class="privateLetter-content" v-if="approvalTypeNullContent">{{ $t('sign.noApproveContent') }}</div>
            <!--审批须知或者签约须知中，内容存在时展示-->
            <div class="privateLetter-content private-letter-format" v-if="privateLetter" v-slink>{{ privateLetter }}</div>
            <!--审批须知中，附件存在时展示-->
            <!--签约须知中，附件存在或者压缩文件存在时展示-->
            <template v-if="(receiver.privateLetterFileVOList || []).length">
                <h2 class="privateLetter-title">{{ $t('sign.attachmentContent') }}：</h2>
                <ul class="file-list">
                    <li
                        v-for="(item, index) in receiver.privateLetterFileVOList || []"
                        :key="index"
                        class="li-item"
                    >
                        <p class="li-item-title">{{ item.fileName }} <el-button v-if="item.canDownload" type="text" class="download-file_btn" @click="downloadFile(receiver, item)">{{ $t('sign.downloadFile') }}</el-button> </p>
                    </li>
                    <template v-for="(item,index) in receiver.privateLetterFileVOList || []">
                        <span v-if="item.canPreview" :key="index+'privateLetterFileVOList'">
                            <img
                                v-lazy="imgSrc"
                                v-for="imgSrc in privateLetterImgList(item.previewUrls)"
                                :key="imgSrc"
                            >
                        </span>
                    </template>

                </ul>
            </template>
            <div slot="footer">
                <el-button v-if="signType === 'approval'" :class="{'en-btn': isEn}" @click="viewApprovalProcess">{{ $t('sign.viewApproveProcess') }}</el-button>
                <el-button type="primary" @click="handleClickReadLetter">{{ signType === 'approval'?$t('sign.knew'):$t('sign.nextStep') }}</el-button>
            </div>
        </el-dialog>
        <el-dialog
            class="approval-process-dialog"
            :title="$t('sign.approveProcess')"
            :visible.sync="approvalProcessVisible"
            append-to-body
        >
            <div class="approval-process-wrap">
                <ApprovalProcess
                    :sender="approvalSender"
                    :approvers="approvalApprovers"
                />
            </div>
        </el-dialog>
        <ShareViewDialog
            :visible.sync="shareViewDialogOpen"
            :contract-id="contractId"
            :receiver-id="receiver.receiverId"
        />
    </section>
</template>
<script>
import ContractTag from 'components/contractTag/ContractTag.vue';
import TagManage from 'components/tagManage/TagManage.vue';
import Gallery from 'components/gallery/Gallery.vue';
import ApprovalProcess from 'components/approvalProcess/index.vue';

import SignHeader from '../../common/signHeader/SignHeader.vue';
import MoveToFolder from '../moveToFolder/index.vue';
import ApprovalDetail from '../approvalDetail/ApprovalDetail.vue';
import signReadHeader from '../signReadHeader/SignReadHeader.vue';
import SignRejectDialog from './SignRejectDialog/index.vue';
import ShareViewDialog from './ShareViewDialog/index.vue';
import { tagManageMixin } from 'src/mixins/tagManage.js';
import { invokeDownloadHelper } from 'src/common/utils/download.js';
import { stashViewContractId, checkContractIdViewed } from 'src/utils/letterView.js';
import regRules from 'src/common/utils/regs';
import { mapState, mapGetters } from 'vuex';
export default {
    components: {
        SignHeader,
        MoveToFolder,
        ContractTag,
        TagManage,
        ApprovalDetail,
        Gallery,
        signReadHeader,
        ApprovalProcess,
        SignRejectDialog,
        ShareViewDialog,
    },
    mixins: [tagManageMixin],
    directives: {
        // 高亮文本中超链接
        slink: {
            inserted: function(el) {
                const content = el.textContent;
                const reg = regRules.normalUrl;
                el.innerHTML = content.replace(reg, function() {
                    const matchedStr = arguments[0];
                    return `<span class="content-link" targetHref="${matchedStr}" style="color: #127fd2;cursor: pointer;">${matchedStr}</span>`;
                });
                const linkList = el.querySelectorAll('.content-link');
                linkList.forEach(link => {
                    link.onclick = function() {
                        window.open(link.getAttribute('targetHref'));
                    };
                });
            },
        },
    },
    props: {
        contractId: {
            default: '',
            type: String,
        },
        signType: {
            default: 'sign',
            type: String,
        },
        receiver: {
            default: () => {},
            type: Object,
        },
        parentReceiverId: {
            default: () => '',
            type: String,
        },
        hdVisible: {
            default: false,
            type: Boolean,
        },
        ssoSigning: {
            default: () => {},
            type: Object,
        },
        contractData: {
            default: () => {},
            type: Object,
        },
        hybridContractServerConnected: {
            default: true,
            type: Boolean,
        },
        isEnt: {
            default: false,
            type: Boolean,
        },
        rText: {
            default: '',
            type: String,
        },
        returnUrl: {
            default: '',
            type: String,
        },
        hidePass: {
            default: false,
            type: Boolean,
        },
        channel: {
            default: '',
            type: String,
        },
        isShowRidingSeal: {
            default: false,
            type: Boolean,
        },
        isShowRidingSealBtn: {
            default: false,
            type: Boolean,
        },
        isFromSignAuthorization: {
            default: false,
            type: Boolean,
        },
        isReading: {
            default: false,
            type: Boolean,
        },
        isScrollLimitSign: {
            default: false,
            type: Boolean,
        },
        noRight: {
            default: false,
            type: Boolean,
        },
        signPlaceBySigner: {
            default: false,
            type: Boolean,
        },
        signatureLabels: {
            default: () => [],
            type: Array,
        },
        dateLabels: {
            default: () => [],
            type: Array,
        },
        signerDragLabelTypeList: {
            default: () => [],
            type: Array,
        },
        contractAlias: {
            type: String,
            default: '',
        },
        sensorsTrackContractInfo: {
            default: () => {},
            type: Object,
        },
        showShareViewBtn: {
            default: true,
            type: Boolean,
        },
        ifSignerViewApprovalDetail: {
            default: false,
            type: Boolean,
        },
    },
    data() {
        return {
            signDecision: 'NORMAL', // 签署条件
            // 签约须知、审批须知是否已查看
            viewedLetter: true,
            // 是否显示私信按钮
            privateLetterBtnShow: false,
            // 是否显示私信弹窗
            privateLetterVisible: false,
            privateLetter: '',
            letterSender: '',
            isOtherBtnShow: false,
            isTagManageVisible: false,
            previewImgInfo: {
                visible: false,
                fileName: '',
                previewUrl: '',
            },
            approvalDetailDialogVisible: false,
            approvalDetailKey: 0,
            /** 审批流程相关 start */
            // 审批流程是否可见
            approvalProcessVisible: false,
            approvalSender: {},
            approvalApprovers: [],
            /** 审批流程相关 end */
            /** 拒签相关 start */
            signRejectDialogVisible: false,
            /** 拒签相关 end */

            shareViewDialogOpen: false,
        };
    },
    computed: {
        ...mapState({
            hasAuthorized: state => state.approval.hasAuthorized,
        }),
        ...mapGetters([
            'checkFeat',
            'getIsJa',
            'isPerson',
        ]),
        sensorsEventName() {
            return this.signType === 'sign' ? 'ContractSign' : (this.signType === 'approval' ? 'ContractApproval' : '');
        },
        sensorsPageName() {
            return this.signType === 'sign' ? '合同签署页' : (this.signType === 'approval' ? '合同审批页' : '');
        },
        needRead() {
            return this.isReading && !this.privateLetterVisible;
        },
        // 签署前审批申请人
        sendRealEmployeeName() {
            if (!this.receiver.sendRealEmployeeName) {
                return this.$t('sign.employeeDefault');
            }
            return this.receiver.sendRealEmployeeName;
        },
        // 审批详情需要该字段
        approvalDetailReceiverId() {
            // 10: 发送前审批, 11: 签署前审批
            // 配合后端，兼容 APP，签署前审批取 parentReceiverId
            if (~~this.receiver.defType === 11 && !this.ifSignerViewApprovalDetail) {
                return this.receiver.parentReceiverId;
            }
            return this.receiver.receiverId;
        },
        approvalTypeText() {
            // 10: 发送前审批, 11: 签署前审批
            if (this.receiver.defType === '10') {
                return this.$t('sign.approveBeforeSend');
            }
            return  this.$t('sign.approveBeforeSign');
        },
        // 审批时，审批须知的文件和内容都为空
        approvalTypeNullContent() {
            return this.signType === 'approval' && !this.privateLetter && !(this.receiver.privateLetterFileVOList || []).length;
        },
        isLocalLetterViewed() {
            return checkContractIdViewed(this.contractId);
        },
        isEn() {
            return this.$i18n.locale === 'en'; // 设置了语言为英语
        },
    },
    watch: {
        approvalDetailDialogVisible(val) {
            this.sensorsEventName && this.$sensors.track({
                eventName: val ? `Ent_${this.sensorsEventName}_BtnClick` : `Ent_${this.sensorsEventName}Window_BtnClick`,
                eventProperty: {
                    page_name: `${this.sensorsPageName}`,
                    window_name: val ? null : '审批详情',
                    first_category: '其他操作',
                    icon_name: val ? '审批详情' : '关闭',
                    ...this.sensorsTrackContractInfo,
                },
            });
            val && this.sensorsEventName && this.$sensors.track({
                eventName: `Ent_${this.sensorsEventName}Window_PopUp`,
                eventProperty: {
                    page_name: `${this.sensorsPageName}`,
                    window_name: '审批详情',
                    ...this.sensorsTrackContractInfo,
                },
            });
            if (!val) {
                this.$emit('closeIfSignerViewApprovalDetail');
            }
        },
        privateLetterVisible(val) {
            if (val) {
                this.sensorsEventName && this.$sensors.track({
                    eventName: `Ent_${this.sensorsEventName}Window_PopUp`,
                    eventProperty: {
                        page_name: `${this.sensorsPageName}`,
                        window_name: this.signType === 'approval' ? '审批须知' : '签约须知',
                        ...this.sensorsTrackContractInfo,
                    },
                });
            }
        },
        'receiver.receiverId': {
            handler() {
                const v = this.receiver;
                this.signDecision = v.signDecision;
                this.viewedLetter = this.isLocalLetterViewed;
                // 审批时，不限制审批须知按钮的显示
                if (this.signType === 'approval') {
                    this.privateLetterBtnShow = true;
                    this.letterSender = v.letterSender;
                }
                // 私信：具体指的是签约须知或者审批须知
                // 存在私信时，显示私信按钮；
                if (
                    v.privateLetter ||
                    (v.privateLetterFileVOList || []).length ||
                    (v.instructionsAppendixInfo && v.instructionsAppendixInfo.fileId)
                ) {
                    this.privateLetterBtnShow = true;
                    this.letterSender = v.letterSender;
                    this.privateLetter = v.privateLetter;
                }
                // 私信未读时显示私信弹窗
                this.initTagInfo(this.contractId).then(() => {
                    if (
                        this.signType === 'approval' ||
                        v.privateLetter ||
                        (v.privateLetterFileVOList || []).length ||
                        (v.instructionsAppendixInfo && v.instructionsAppendixInfo.fileId)
                    ) {
                        // 私信的展开在设置标签后，防止设置标签出现呢后导致位置偏移
                        // SAAS-29701 审批须知的文件和内容都为空时不展示弹窗
                        this.privateLetterVisible = !this.viewedLetter && !this.approvalTypeNullContent;
                    }
                    if (!this.privateLetterVisible && this.signPlaceBySigner && !this.$localStorage.get('notShowSignGuideDialog')) {
                        this.$emit('open-sign-place-dialog', { step: 1 });
                    }
                });
            },
            immediate: true,
        },
        ifSignerViewApprovalDetail(val) {
            if (val) {
                this.approvalDetailDialogVisible = true;
            }
        },
    },
    methods: {
        handleShareView() {
            this.shareViewDialogOpen = true;
        },
        toggleToRenderImg() {
            this.$emit('toggleToRenderImg');
        },
        handleDragLabel({ type }) {
            this.$emit('drag-label', { type });
        },
        handleOpenSignPlaceDialog(params) {
            this.$emit('open-sign-place-dialog', params);
        },
        handleReject() {
            this.sensorsEventName && this.$sensors.track({
                eventName: `Ent_${this.sensorsEventName}_BtnClick`,
                eventProperty: {
                    page_name: `${this.sensorsPageName}`,
                    first_category: '其他操作',
                    icon_name: '驳回',
                    ...this.sensorsTrackContractInfo,
                },
            });
            this.$emit('approval-reject-click');
        },
        handleViewDetail() {
            this.sensorsEventName && this.$sensors.track({
                eventName: `Ent_${this.sensorsEventName}_BtnClick`,
                eventProperty: {
                    page_name: `${this.sensorsPageName}`,
                    first_category: '顶部导航栏',
                    icon_name: '查看合同详情',
                    ...this.sensorsTrackContractInfo,
                },
            });
        },
        viewApprovalProcess() {
            this.sensorsEventName && this.$sensors.track({
                eventName: `Ent_${this.sensorsEventName}Window_BtnClick`,
                eventProperty: {
                    page_name: `${this.sensorsPageName}`,
                    window_name: this.signType === 'approval' ? '审批须知' : '签约须知',
                    icon_name: '查看审批流程',
                    ...this.sensorsTrackContractInfo,
                },
            });
            this.approvalProcessVisible = true;
            this.sensorsEventName && this.$sensors.track({
                eventName: `Ent_${this.sensorsEventName}Window_PopUp`,
                eventProperty: {
                    page_name: `${this.sensorsPageName}`,
                    window_name: '审批流程',
                    ...this.sensorsTrackContractInfo,
                },
            });
            // 获取审批流程数据
            this.getApprovalProcessData().then((res) => {
                const data = res.data;
                this.approvalSender = data.sender;
                this.approvalApprovers = data.approvers;
            });
        },
        privateLetterImgList(previewUrls) {
            if (typeof previewUrls === 'string') {
                return [previewUrls];
            }
            return previewUrls.map(item => item);
        },
        // 头部交互
        toBack() {
            this.$router.go(-1);
        },
        handleRead() {
            this.$http.post(`${signPath}/contracts/${this.contractId}/receivers/${this.receiver.receiverId}/finish-contract-read`);
            this.$emit('update:isReading', false);
            setTimeout(() => {
                if (this.privateLetterBtnShow) {
                    this.privateLetterVisible = !this.viewedLetter;
                }
            }, 500);
        },
        // 预览私信图片
        preivewFile(item) {
            this.previewImgInfo.visible = true;
            this.previewImgInfo.fileName = item.fileName;
            this.previewImgInfo.previewUrl = item.previewUrls;
        },
        // 点击私信按钮
        handleClickPriLetterBtn() {
            this.sensorsEventName && this.$sensors.track({
                eventName: `Ent_${this.sensorsEventName}_BtnClick`,
                eventProperty: {
                    page_name: `${this.sensorsPageName}`,
                    first_category: '顶部导航栏',
                    icon_name: this.signType === 'approval' ? '审批须知' : '签约须知',
                    ...this.sensorsTrackContractInfo,
                },
            });
            this.privateLetterVisible = true;
        },
        // 私信弹窗点击'知道了', 若私信此前未读过(viewedLetter=false)，改变状态，若已读过，无需改变
        handleClickReadLetter() {
            this.sensorsEventName && this.$sensors.track({
                eventName: `Ent_${this.sensorsEventName}Window_BtnClick`,
                eventProperty: {
                    page_name: `${this.sensorsPageName}`,
                    window_name: this.signType === 'approval' ? '审批须知' : '签约须知',
                    icon_name: this.signType === 'approval' ? '知道了' : '下一步',
                    ...this.sensorsTrackContractInfo,
                },
            });
            if (!this.viewedLetter) {
                // 且为签署人指定位置 那么 关闭签约须知弹窗之后 弹出签署引导
                if (this.signPlaceBySigner && this.signType === 'sign') {
                    this.$emit('open-sign-place-dialog', { step: 1 });
                }
            }
            this.viewedLetter = true;
            this.privateLetterVisible = false;
            stashViewContractId(this.contractId);
        },
        handleOtherOperate() {
            this.sensorsEventName && this.$sensors.track({
                eventName: `Ent_${this.sensorsEventName}_BtnClick`,
                eventProperty: {
                    page_name: `${this.sensorsPageName}`,
                    first_category: '顶部导航栏',
                    icon_name: '其他操作',
                    ...this.sensorsTrackContractInfo,
                },
            });
            this.isOtherBtnShow = !this.isOtherBtnShow;
        },
        onOpenTagManage() {
            this.sensorsEventName && this.$sensors.track({
                eventName: `Ent_${this.sensorsEventName}_BtnClick`,
                eventProperty: {
                    page_name: `${this.sensorsPageName}`,
                    first_category: '顶部导航栏',
                    icon_name: '设置标签',
                    ...this.sensorsTrackContractInfo,
                },
            });
            // 判断有没有合同标签，没有的话弹出提示
            if (this.selectedTagParams.tagOptions.length < 1) {
                return this.$MessageToast.info(this.$t('sign.noLabelPleaseAppend'));
            }
            this.selectedTagParams.addedTagIds = this.contractTagsInfo.map(a => a.tagId);
            this.isTagManageVisible = true;
        },
        handleClickSignReject() {
            this.sensorsEventName && this.$sensors.track({
                eventName: `Ent_${this.sensorsEventName}_BtnClick`,
                eventProperty: {
                    page_name: `${this.sensorsPageName}`,
                    first_category: '其他操作',
                    icon_name: '拒签',
                    ...this.sensorsTrackContractInfo,
                },
            });
            this.signRejectDialogVisible = true;
        },
        downloadCompressedFile(fileId) {
            const downloadUrl = `/contract-api/contracts/instructions-appendix/${this.contractId}/download/${fileId}`;
            // console.log(downloadUrl);
            invokeDownloadHelper(downloadUrl, false);
        },
        // 下载文件
        downloadFile(receiver, item) {
            // 通用下载方法
            invokeDownloadHelper(`/contract-api/contracts/detail/attachment/${receiver.contractId}/${receiver.receiverId}/download/${item.fileId}`);
        },
        getApprovalProcessData() {
            // 发送前审批
            let url = `${signPath}/contracts/${this.contractId}/approval`;
            // 签署前审批
            if (this.receiver.defType === '11') {
                url = `${signPath}/contracts/${this.contractId}/sign-approval/${this.parentReceiverId}`;
            }
            return this.$http.get(url);
        },
        handleAgent(type) {
            this.$emit('changeAgentType', type);
            if (!this.hasAuthorized) {
                this.$store.state.approval.showAuthDialog = true;
                return;
            }
            this.$emit('handleChangeType');
        },
    },
    async created() {
    },
};
</script>
<style lang="scss">
@import './index.scss';
</style>
<style lang="scss" scoped>
.news-in-btn {
    display: flex;
    align-items: center;
    height: 100%;
    .news {
        height: 14px;
        margin-left: 4px;
    }
}
</style>
