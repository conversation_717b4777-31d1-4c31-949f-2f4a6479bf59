<!-- 业务组件：签署页拒签弹窗，拒签理由+拒签校验 -->
<template>
    <div>
        <!-- 对话框：拒签理由 -->
        <el-dialog
            class="reject-reason-dialog ssq-form"
            :title="$t('sign.refuseSign')"
            :visible.sync="signRejectDialogVisible"
            :before-close="handleRejectDialogClose"
        >
            <p>{{ $t('sign.refuseTip') }}</p>
            <el-checkbox-group v-model="rejectCheckbox" v-if="rejectReasonList.length!==0">
                <el-checkbox
                    v-for="item in rejectReasonList"
                    :label="item.id"
                    :key="item.id"
                    :disabled="item.reasonType === 6 && item.checkFlag"
                    :class="item.reasonType === 6 && item.checkFlag ? 'is-checkbox-disabled': ''"
                >
                    {{ item.reasonType!==6 ? item.refuseReason: (isOtherReasonMust ? $tc('sign.refuseReasonOther',2) : $tc('sign.refuseReasonOther',1)) }}
                </el-checkbox>
            </el-checkbox-group>
            <div class="reject-reason-dialog-input" v-if="otherReason.length!==0">
                <el-input
                    :maxlength="refuseMaxLength"
                    type="textarea"
                    :rows="4"
                    v-model="refuseReason"
                    :placeholder="otherReason[0].refuseReason"
                >
                    <el-i-delete slot="icon"></el-i-delete>
                </el-input>
                <div class="refuse-description">{{ refuseMaxLength - refuseReason.length }}</div>
            </div>
            <div class="confirm-sign-error" v-if="isMust && refuseReason === ''">{{ $t('sign.reasonWriteTip') }}</div>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="handleReject">{{ $t('sign.refuseSign') }}</el-button>
            </span>

        </el-dialog>
        <!-- 对话框：拒签校验 -->
        <el-dialog
            class="reject-check-dialog"
            :title="$t('sign.refuseSign')"
            :visible.sync="signRejectDialog2Visible"
            :before-close="handleRejectDialogClose"
        >
            <SignReject
                ref="signReject"
                v-if="rejectReasonList.length"
                :rejectCheckbox="rejectReasonResultList"
                :channel="channel"
                :contractId="contractId"
                :returnUrl="returnUrl"
                :hidePass="hidePass"
                :userAccount="userAccount"
                :sensorsTrackContractInfo="sensorsTrackContractInfo"
                :sensorsEventName="sensorsEventName"
                :sensorsPageName="sensorsPageName"
                :receiver="receiver"
                :contractData="contractData"
                @cancel="handleRejectDialogClose"
            >
            </SignReject>
        </el-dialog>
    </div>
</template>
<script>
import SignReject from '../../signReject/SignReject.vue';
export default {
    name: 'SignRejectDialog',
    components: {
        SignReject,
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        channel: {
            type: String,
            default: '',
        },
        contractId: {
            type: String,
            default: '',
        },
        returnUrl: {
            type: String,
            default: '',
        },
        hidePass: {
            type: Boolean,
            default: false,
        },
        userAccount: {
            type: String,
            default: '',
        },
        sensorsEventName: {
            type: String,
            default: '',
        },
        sensorsPageName: {
            type: String,
            default: '',
        },
        receiver: {
            type: Object,
            default: () => {},
        },
        sensorsTrackContractInfo: {
            type: Object,
            default: () => {},
        },
        contractData: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            /** 拒签相关 start */
            signRejectDialogVisible: false,
            rejectReasonList: [],
            rejectCheckbox: [],
            refuseReason: '',
            refuseMaxLength: 100,
            signRejectDialog2Visible: false,
            otherReason: [],
            isMust: false,
            /** 拒签相关 end */
        };
    },
    computed: {
        isOtherReasonMust() {
            const otherReasonMap = this.otherReason[0];
            if (otherReasonMap && otherReasonMap.checkFlag && otherReasonMap.reasonType === 6) {
                return true;
            }
            return false;
        },
        rejectReasonResultList() {
            const list = this.rejectReasonList.filter(item => this.rejectCheckbox.includes(item.id)).map(item => {
                return {
                    reasonType: item.reasonType,
                    refuseReason: item.reasonType === 6 ? this.refuseReason : item.refuseReason,
                };
            });
            return list;
        },
    },
    watch: {
        visible: {
            handler(val) {
                this.signRejectDialogVisible = val;
            },
            immediate: true,
        },
    },
    methods: {
        init() {
            this.isMust = false;
            this.$http.get(`/contract-api/contracts/refuse-reasons?contractId=${this.contractId}`).then(res => {
                this.rejectReasonList = res.data;
                this.rejectReasonList.forEach(item => {
                    if (item.reasonType === 6) {
                        this.otherReason.push(item);
                        if (item.checkFlag) {
                            this.rejectCheckbox = [item.id];
                        }
                    }
                });
            });
        },
        handleReject() {
            if (this.rejectCheckbox.length <= 0) {
                return this.$MessageToast.error(this.$t('sign.refuseTip'));
            }
            const isSelectOtherReason = this.rejectReasonList.filter(item => this.rejectCheckbox.includes(item.id)).some(e => e.reasonType === 6);
            if (this.otherReason[0].checkFlag && isSelectOtherReason && this.refuseReason === '') {
                this.isMust = true;
            } else {
                this.signRejectDialogVisible = false;
                this.signRejectDialog2Visible = true;
            }
        },
        handleRejectDialogClose() {
            this.signRejectDialog2Visible = false;
            this.signRejectDialogVisible = false;
            this.$emit('update:visible', false);
        },
    },
    created() {
        this.init();
    },
};
</script>
<style>
    .confirm-sign-error{
        color: red;
        display: inline-block;
        margin-top: 20px;
        margin-left: 100px;
        line-height: 30px;
    }
</style>
