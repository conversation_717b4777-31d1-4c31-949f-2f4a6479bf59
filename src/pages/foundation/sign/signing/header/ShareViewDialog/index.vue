<template>
    <el-dialog
        :title="$t('shareView.title')"
        :visible.sync="dialogVisible"
        size="tiny"
        append-to-body
        class="share-view-dialog"
    >
        <div class="share-content" v-loading="loading">
            <template v-if="step === 1">
                <el-form ref="linkForm" :model="linkForm" :rules="rules" label-width="100px">
                    <el-form-item :label="$t('shareView.account')" prop="reviewerAccount">
                        <el-input v-model="linkForm.reviewerAccount" :placeholder="$t('shareView.inputAccount')"></el-input>
                        <p class="input-tip">{{ $t('shareView.accountInputTip') }}</p>
                    </el-form-item>
                    <el-form-item :label="$t('shareView.role')" prop="reviewerRole">
                        <el-input v-model="linkForm.reviewerRole" :placeholder="$t('shareView.rolePlaceholder')"></el-input>
                    </el-form-item>
                    <el-form-item :label="$t('shareView.note')" prop="remark">
                        <el-input type="textarea"
                            :autosize="{ minRows: 3, maxRows: 6}"
                            :rows="3"
                            :maxlength="200"
                            v-model="linkForm.remark"
                            :placeholder="$t('shareView.notePlaceholder')"
                        ></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <div v-else class="share-access">
                <p class="share-access__tip"><span class="ja-page-hidden">{{ $t('shareView.shareLinkTip1') }}</span>{{ $t('shareView.shareLinkTip') }}</p>
                <div class="share-access__notice">
                    <p>{{ $t('shareView.linkTip1') }}</p>
                    <p>{{ $t('shareView.linkTip2') }}</p>
                </div>
                <div class="share-access__img">
                    <img class="ja-page-hidden" :src="shareQrCode" alt="" width="200" height="200" />
                </div>
                <div class="share-access__link">
                    <span>{{ $t('shareView.link') }}</span>
                    <el-input class="link-item" size="small" :value="shortUrl" readonly>
                        <template slot="append">
                            <span class="copy-btn"
                                v-clipboard:copy="shortUrl"
                                v-clipboard:success="onCopy"
                                v-clipboard:error="onError"
                            >{{ $t('dataBoxInvite.copy') }}</span>
                        </template>
                    </el-input>
                </div>
            </div>
        </div>
        <div slot="footer" class="share-footer">
            <template v-if="step === 1">
                <el-button
                    @click="handleClose"
                >{{ $t('sign.cancel') }}</el-button>
                <el-button
                    type="primary"
                    :loading="btnLoading"
                    @click="handleGenerate"
                >{{ $t('shareView.generateLink') }}</el-button>
            </template>
            <template v-else>
                <el-button
                    :loading="btnLoading"
                    type="primary"
                    @click="handleRegenerate"
                >{{ $t('shareView.regenerateLink') }}</el-button>
                <el-button class="ja-page-hidden" type="primary" @click="handleDownload">{{ $t('shareView.saveQrcode') }}</el-button>
            </template>
        </div>
    </el-dialog>
</template>

<script>
import regRules from 'utils/regs.js';
export default {
    name: 'ShareViewDialog',
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        contractId: {
            type: String,
            default: '',
        },
        receiverId: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            loading: false,
            step: 2,
            shareId: '',
            shortUrl: '',
            qrCodeBase64: '',
            linkForm: {
                reviewerAccount: '',
                reviewerRole: '',
                remark: '',
            },
            rules: {
                reviewerAccount: [
                    { required: true, message: this.$t('shareView.inputAccount'), trigger: 'blur' },
                    { validator: this.validateAccount, trigger: 'blur' },
                ],
            },
            btnLoading: false,
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(v) {
                this.$emit('update:visible', v);
            },
        },
        shareQrCode() {
            return this.qrCodeBase64 ? URL.createObjectURL(this.base64ToBlob(this.qrCodeBase64, 'image/png')) : '';
        },
    },
    watch: {
        visible: {
            handler(v) {
                if (v) {
                    this.initShareInfo();
                }
            },
            immediate: true,
        },
    },
    methods: {
        handleDownload() {
            const filename = `${this.shareId}.png`;
            const a = document.createElement('a');
            document.body.appendChild(a);
            a.href = this.shareQrCode;
            a.download = filename;
            a.setAttribute('target', '_blank');
            a.click();
            window.URL.revokeObjectURL(this.shareQrCode);
            document.body.removeChild(a);
        },
        base64ToBlob(base64Data, contentType) {
            const raw = window.atob(base64Data);
            const rawLength = raw.length;
            const uInt8Array = new Uint8Array(rawLength);
            for (let i = 0; i < rawLength; ++i) {
                uInt8Array[i] = raw.charCodeAt(i);
            }
            return new window.Blob([uInt8Array], { type: contentType });
        },
        // 复制成功
        onCopy: function() {
            this.$MessageToast.success(this.$t('docDetail.copySucc'));
        },
        // 复制失败
        onError: function() {
            this.$MessageToast.success(this.$t('docDetail.copyFail'));
        },
        validateAccount(rule, value, callback) {
            if (!regRules.userAccount.test(value)) {
                callback(new Error(this.$t('shareView.inputCorrectAccount')));
            } else {
                callback();
            }
        },
        initShareInfo() {
            this.loading = true;
            this.$http.get(`/contract-api/share-review/contracts/${this.contractId}/receivers/${this.receiverId}`, {
                params: {
                    platform: 'WEB',
                },
            }).then(({ data }) => {
                if (data) {
                    this.setShareInfo({
                        step: 2,
                        shareId: data.shareId,
                        shortUrl: data.shortUrl,
                        qrCodeBase64: data.qrCodeBase64,
                    });
                } else {
                    this.resetInfo();
                }
            }).finally(() => {
                this.loading = false;
            });
        },
        setShareInfo({ step, shareId = '', shortUrl = '', qrCodeBase64 = '' }) {
            this.step = step;
            this.shortUrl = shortUrl;
            this.qrCodeBase64 = qrCodeBase64;
            this.shareId = shareId;
        },
        handleRegenerate() {
            this.btnLoading = true;
            this.$http.delete(`/contract-api/share-review/${this.shareId}`)
                .then(() => {
                    this.resetInfo();
                }).finally(() => {
                    this.btnLoading = false;
                });
        },
        handleGenerate() {
            this.$refs.linkForm.validate(async(valid) => {
                if (valid) {
                    this.btnLoading = true;
                    this.$http.post(`/contract-api/share-review/contracts/${this.contractId}/receivers/${this.receiverId}`, {
                        ...this.linkForm,
                        platform: 'WEB',
                    }).then(({ data }) => {
                        this.setShareInfo({
                            step: 2,
                            shareId: data.shareId,
                            shortUrl: data.shortUrl,
                            qrCodeBase64: data.qrCodeBase64,
                        });
                    }).finally(() => {
                        this.btnLoading = false;
                    });
                }
            });
        },
        handleClose() {
            this.dialogVisible = false;
        },
        resetInfo() {
            this.linkForm = {
                reviewerAccount: '',
                reviewerRole: '',
                remark: '',
            };
            this.shortUrl = '';
            this.shareCodeImg = '';
            this.step = 1;
            this.$refs.linkForm && this.$refs.linkForm.resetFields();
        },
    },
};
</script>

<style lang="scss">
.share-view-dialog {
    .el-dialog {
        width: 580px;
        min-height: 300px;

        .el-form {
            [dir=rtl] & .el-form-item {
                &__label {
                    float: right;
                }
                &__content {
                    margin-left: 0;
                    margin-right: 100px;
                }

            }
        }
        .share-content {
            .input-tip {
                height: 20px;
                font-size: 12px;
                line-height: 16px;
                color: #999999;
            }
        }
        .el-textarea {
            min-height: 50px;
        }
        .share-access {
            margin-top: 10px;
            text-align: center;
            &__tip {
                //text-align: center;
                color: #666;
            }
            &__notice {
                margin-top: 10px;
                font-size: 12px;
            }
            &__img {
                margin: 15px auto;
            }
            &__link .link-item{
                width: 320px;
                .el-input-group__append {
                    padding: 0px 20px;
                    cursor: pointer;
                    color: #127FD2;
                    [dir=rtl] & {
                        border-right: 0;
                        border-left: 1px solid #bfcbd9;
                        border-radius: 4px;
                        border-top-right-radius: 0;
                        border-bottom-right-radius: 0;
                    }
                }
            }
        }
    }
    .share-footer {
        text-align: center;
    }
}
</style>
