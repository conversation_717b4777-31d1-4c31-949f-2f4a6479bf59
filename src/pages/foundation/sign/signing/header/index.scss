.privateLetter-dialog {
    .el-dialog {
        min-width: 600px;
        max-height: 80%;
        overflow: hidden;
        display: flex;
        flex-flow: column;

        .el-dialog__body {
            flex: 1;
            padding: 20px 50px;
            color: #333333;
            overflow: auto;
            min-height: 300px;

            &.has-file {
                min-height: 300px;
            }

            .privateLetter-title {
                font-size: 14px;

                font-weight: bold;
                padding-bottom: 8px;

            }

            .privateLetter-content {
                padding-bottom: 16px;
                word-break: break-all;
            }
            .private-letter-format{
                white-space: pre-line;
            }

            .file-list {
                .li-item-title {
                    font-size: 12px;
                    line-height: 20px;
                    border-bottom: 1px solid #eee;

                    .download-file_btn {
                        font-size: 12px;
                        margin: 0 0 0 24px;
                    }

                    .li-item-download {
                        color: #127FD2;
                        cursor: pointer;
                    }

                    .li-item-download-tip {
                        color: #999;
                    }
                }

                img {
                    max-width: 100%;
                    margin: 14px 0;
                }

                .hide-img {
                    width: 0;
                    height: 0;
                }
            }

        }

        .el-button--primary {
            margin: 0 auto;
            background-color: #127FD2;
            border-color: #127FD2;
            line-height: 30px;
            padding: 0;
            display: inline-block;

            &:hover {
                background: #1687dc;
                border-color: #1687dc;
            }
        }

        .el-button--default {
            line-height: 30px;
            padding: 0;
            margin-right: 10px;
            background: #F8F8F8;
            color: #666666;
            border: 1px solid rgba(204, 204, 204, 1);
            border-radius: 2px;

            &:hover {
                background: #F8F8F8;
                border: 1px solid rgba(204, 204, 204, 1);
                color: #666666;
            }
        }
    }
}

.approval-process-dialog {
    .el-dialog {
       min-width: 400px;
    }

    .approval-process-wrap {
        padding: 25px 30px;
    }
}

.en-btn {
    width: 200px !important;
}
