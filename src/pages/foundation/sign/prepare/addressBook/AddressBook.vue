<template>
    <div class="AddressBook-comp">
        <div class="AddressBook-tabs fl">
            <div class="AddressBook-tab" :class="{ current: activeIndex === index }" v-for="(tab, index) in tabs" :key="index" @click="handleClickTab(tab, index)">{{ tab.title }}</div>
        </div>
        <div class="AddressBook-content">
            <component
                :is="component"
                :selectType="selectType"
                :onlyActive="true"
                @choose="onChoose"
            >
            </component>
        </div>
    </div>
</template>
<script>
import CompanyDeptAndMember from 'src/pages/enterprise/console/common/CompanyDeptAndMember.vue';
import OuterContract from '../outerContact/OuterContact.vue';
export default {
    components: {
        CompanyDeptAndMember,
        OuterContract,
    },
    // eslint-disable-next-line vue/require-prop-types
    props: ['selectType'],
    data() {
        return {
            activeIndex: 0,
            component: '',
        };
    },
    computed: {
        tabs() {
            if (this.$store.state.commonHeaderInfo.userType === 'Enterprise') {
                return [
                    { title: this.$t('prepare.innerContact'), cpn: 'CompanyDeptAndMember' },
                    { title: this.$t('prepare.outerContact'), cpn: 'OuterContract' },
                ];
            } else {
                return [{ title: this.$t('prepare.outerContact'), cpn: 'OuterContract' }];
            }
        },
    },
    methods: {
        handleClickTab(tab, index) {
            this.component = tab.cpn;
            this.activeIndex = index;
        },
        onChoose(members, opts) {
            this.$emit('choose', members, opts);
        },
    },
    created() {
        this.component = this.tabs[0].cpn;
    },
};
</script>
<style lang="scss">

	.AddressBook-comp {
		.AddressBook-tabs {
			height: 312px;
			background-color: #F3F9FD;
			padding-top: 18px;
			border-right: 1px solid #eee;
			.AddressBook-tab {
				width: 145px;
				height: 37px;
				line-height: 37px;
				text-align: center;
				margin-bottom: 10px;
				cursor: pointer;
			}
			.current {
				background-color: #E4EEF3;
				border-right: 2px solid #108BF2;
				font-weight: bold;
			}
		}
		// .OuterContract-content, .CompanyDeptAndMember-content {
		// 	.department-member-cloumn {

		// 	}
		// }
		.AddressBook-content {
			height: 330px;
			margin-left: 146px;

			.company-inner-cloumn {
				height: 308px;
			}
			.company-inner-cloumn:first-child {
				width: 226px;
				padding: 22px 19px 0 16px;
				border-right: 1px solid #eee;
			}

			.department-member-cloumn.radio {
				width: 286px;
			}
			.department-member-cloumn {
				width: 206px;
				padding-top: 22px;
				padding-left: 19px;

				.el-checkbox{
					display: block;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
			}
			.member-block.selected-block {
				width: 254px;
			}
			.OuterContract-right {
				width: 197px;
			}
			.OuterContract-right.radio {
				width: 420px;
			}
		}
	}
</style>
