<!-- 签署——上传文件页 -->
<template>
    <div
        class="sign-prepare-page footer-follow-page"
        v-loading.body.fullscreen="isLoading"
        v-if="!pageUnaccessable"
        @click.stop="closeDateTips"
    >
        <SignHeader
            class="header"
            :title="$t('prepare.signHeaderTitle')"
            @to-back="toBack"
            :showVideoBtn="$t('lang') === 'zh'"
            @to-next="toNext"
            @showVideo="clickShowVideo"
        >
        </SignHeader>

        <div class="prepare-container footer-follow-page-container">
            <h1 class="sender-title">
                <span class="order-num">{{ $t('prepare.step1') }}：</span>
                <span>{{ $t('prepare.confirmSender') }}</span>
            </h1>
            <!--是否开启代发合同-->
            <div class="sender-name" :class="{'diseditable': !contractEditable}" v-if="entOptionsInfos.length > 1 && commonHeaderInfo.currentEntId !== '0'">
                <el-select popper-class="ent-select-dropdown" v-model="proxySenderEntId" :disabled="isSSO">
                    <el-option
                        v-for="(ent, index) in entOptionsInfos"
                        :key="index"
                        :value="ent.entId"
                        :label="ent.entName"
                    >
                    </el-option>
                </el-select>
                <el-tooltip effect="dark" placement="right">
                    <div slot="content">{{ $t('prepare.proxyUpload') }}</div>
                    <i class="el-icon-ssq-bangzhu cursor-point"></i>
                </el-tooltip>
            </div>
            <div class="sender-name" v-else>{{ currentEnt }}</div>
            <!-- 上传文件 -->
            <h1 class="upload-title">
                <span class="order-num">{{ $t('prepare.step2') }}：</span>
                <span>{{ $t('prepare.uploadFile') }}</span>
                <span v-if="$store.state.commonHeaderInfo.userType === 'Enterprise'" class="preset" :class="{'diseditable': !contractEditable}" @click="goPresetConfig">{{ $t('prepare.initialValues') }}</span>
            </h1>
            <UpLoad
                ref="upload"
                :files.sync="files"
                :editable="contractEditable"
                :maxNameLength="100"
                @setFilename="setFilename"
                @setContractLifeEnd="setContractLifeEnd"
                @loaded="onLoaded('uploadReady')"
                @hadInitUploadFile="handleUploaded"
            >
            </UpLoad>
            <!-- 文件信息 -->
            <ContractInfo
                ref="ContractInfo"
                v-model="contractTitleErr"
                :editable="contractEditable"
                :getDefaultConfig="getDefaultConfig"
                :contractTypeErr.sync="contractTypeErr"
                @loaded="onLoaded('contractInfoReady')"
                @sign-ordered="onSignOrderedGet"
            >
            </ContractInfo>

            <!-- 添加签约方 -->
            <h1 class="addreciver-title"><span class="order-num">{{ $t('prepare.step3') }}：</span>{{ $t('prepare.addSigner') }}</h1>
            <AddReciver
                class="AddReciver"
                ref="addReciver"
                :editable="contractEditable"
                :getDefaultConfig="getDefaultConfig"
                :initialSignOrdered="signOrdered"
                sendType="localSend"
                @loaded="onLoaded('addReciverReady')"
            >
            </AddReciver>

            <!-- 关联合同 -->
            <LinkContracts v-if="hadUploadFile" :editable="contractEditable" :linkedContractId="contractId"></LinkContracts>
        </div>

        <VideoDialog
            :title="$t('prepare.actionDemo')"
            :visible.sync="videoVisible"
            videoPath="https://download.bestsign.cn/video/%E5%8F%91%E8%B5%B7%E7%AD%BE%E7%BA%A6%E6%BC%94%E7%A4%BA.mp4"
        >
        </VideoDialog>

        <RegisterFooter class="footer footer-follow"></RegisterFooter>

        <el-dialog
            :title="$t('prepare.preSetDialogTitle')"
            :visible.sync="presetConfigShow"
            custom-class="config-dialog"
            size="tiny"
            top="35%"
            :show-close="false"
        >
            <div>
                {{ $t('prepare.preSetDialogInfo') }}
            </div>
            <span slot="footer" class="dialog-footer">
                <span class="tip-text">{{ $t('prepare.preSetDialogContact') }}</span>
                <el-button type="primary" @click="clickPreSetDialogConfirm">{{ $t('prepare.preSetDialogConfirm') }}</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import SignHeader from '../common/signHeader/SignHeader.vue';
import UpLoad from '../common/upLoad/UpLoad.vue';
import ContractInfo from '../common/contractInfo/ContractInfo.vue';
import AddReciver from '../common/addReciver/AddReciver.vue';
import VideoDialog from 'enterprise_pages/template/common/VideoDialog.vue';
import RegisterFooter from 'components/register_footer/RegisterFooter.vue';
import LinkContracts from 'components/linkContracts/LinkContracts.vue';
import { formatDateToTimestamp } from 'src/common/utils/date.js';
import { uniqBy } from 'src/common/utils/ary.js';
import resRules from 'src/common/utils/regs.js';
import { checkEntNameFormat } from 'src/common/utils/reg';
import router from 'src/router/router.js';
import { getCurrentUser } from 'src/common/utils/getCurrentUser.js';
import { mapState, mapGetters, mapActions } from 'vuex';
import { defaultRecipient } from '../common/addReciver/static.js';

export default {
    components: {
        SignHeader,
        UpLoad,
        ContractInfo,
        AddReciver,
        VideoDialog,
        RegisterFooter,
        LinkContracts,
    },
    data() {
        return {
            isLoading: true,
            contractTitle: '',
            contractTitleErr: '',
            contractTypeErr: false,
            expiredDate: '',
            signOrdered: false,
            videoVisible: false,
            files: [],
            contractId: this.$route.query.contractId || '',
            returnUrl: ['null', ''].includes(this.$route.query.returnUrl) ? null : this.$route.query.returnUrl,
            currentEnt: '',
            proxySenderEntId: '',
            entOptionsInfos: [], // 代发合同可选企业
            hadUploadFile: false,
            hasPresetPermissions: this.$store.getters.checkFeat.presetContract,
            presetConfigShow: false,
            presetConfig: {},
            contractEditable: true,
            pageUnaccessable: false, // 界面是否无权限
            // isStandardVersion: false, // 是否是标准版用户
            enterTime: 0,
            showGuideSender: false,

            getDefaultConfig: this.$http('/ents/configs/industry-package-configs/contract').then(res => {
                const result = {};
                if (res && Array.isArray(res.data)) {
                    const entDefaultRecipient = {};
                    const personDefaultRecipient = {};

                    res.data.forEach(item => {
                        switch (item.name) {
                            // 文件设置
                            case 'PACKAGE_CONTRACT_EXPIRE_DAYS':
                                item.value && (result.expiredDate = Date.now() + item.value * 24 * 60 * 60 * 1000);
                                break;
                            case 'PACKAGE_CONTRACT_LIFE_END_DAYS':
                                item.value && (result.contractLifeEnd = Date.now() + item.value * 24 * 60 * 60 * 1000);
                                break;

                            // 签约企业设置
                            case 'PACKAGE_CONTRACT_ENT_FACE':
                                if (item.value === '1') {
                                    entDefaultRecipient.faceVerify = true;
                                    entDefaultRecipient.status_faceVerify = true;
                                } else if (item.value === '2') {
                                    entDefaultRecipient.faceFirst = true;
                                    entDefaultRecipient.status_faceFirst = true;
                                }
                                break;

                            // 签约个人设置
                            case 'PACKAGE_CONTRACT_PERSON_NAME_NEEDED':
                                if (item.value === 'true') {
                                    personDefaultRecipient.personNameNeeded = true;
                                }
                                break;
                            case 'PACKAGE_CONTRACT_PERSON_ID_NUMBER_NEEDED':
                                if (item.value === 'true') {
                                    personDefaultRecipient.personIdNumberNeeded = true;
                                }
                                break;
                            case 'PACKAGE_CONTRACT_PERSON_FACE':
                                if (item.value === '1') {
                                    personDefaultRecipient.faceVerify = true;
                                    personDefaultRecipient.status_faceVerify = true;
                                } else if (item.value === '2') {
                                    personDefaultRecipient.faceFirst = true;
                                    personDefaultRecipient.status_faceFirst = true;
                                }
                                break;
                            case 'PACKAGE_CONTRACT_PERSON_HAND_WRITE':
                                if (item.value === '1') {
                                    personDefaultRecipient.forceHandWrite = true;
                                    personDefaultRecipient.status_forceHandWrite = true;
                                } else if (item.value === '2') {
                                    personDefaultRecipient.handWriteNotAllowed = true;
                                    personDefaultRecipient.status_handWriteNotAllowed = true;
                                }
                                break;
                        }
                    });

                    result.entDefaultRecipient = entDefaultRecipient;
                    result.personDefaultRecipient  = personDefaultRecipient;
                }
                return result;
            }),
        };
    },
    computed: {
        ...mapState({
            commonHeaderInfo: state => state.commonHeaderInfo,
        }),
        ...mapState('sendingPrepare', ['receptionCollection']), // 是否开通前台代收
        ...mapGetters(['getAuthStatus', 'getUserType']),
        fileExists() {
            const $upload = this.$refs.upload;
            return $upload && $upload.docList.length > 0;
        },
        isNewGroup() { // 是否为新集团
            return +this.commonHeaderInfo.groupVersion === 2;
        },
        isSSO() {
            return this.$route.query.fromSSO === 'true' || this.$route.query.isSSOBack === 'true';
        },
    },
    methods: {
        getModuleList(recipient) {
            const { faceFirst, faceVerify, handWriteNotAllowed, forceHandWrite, handWritingRecognition, privateLetter, attachmentRequired, newAttachmentRequired, contractPayer, mustReadBeforeSign } = recipient;
            return [
                faceFirst && '优先刷脸，备用验证码签署',
                faceVerify && '必须刷脸签署',
                handWriteNotAllowed && '不允许手写签名',
                forceHandWrite && '必须手写签名',
                handWritingRecognition && '开启笔迹识别',
                privateLetter && '添加签约须知',
                attachmentRequired && '添加合同附属资料',
                newAttachmentRequired && '提交签约主体资料',
                contractPayer && '付费方',
                mustReadBeforeSign && '阅读完毕再签署',
            ].filter(item => item);
        },
        clickShowVideo() {
            if (!this.videoVisible) {
                this.sensorsFn('Ent_ContractSendDetailWindow_PopUp', {
                    window_name: '操作演示',
                });
            }
            this.videoVisible = !this.videoVisible;
        },
        sensorsFn(eventName, params = {}) {
            if (this.getUserType === 'Person') {
                this.$sensors.track({
                    eventName,
                    eventProperty: {
                        page_name: '添加文件和签约方',
                        ...params,
                    },
                });
            }
        },
        ...mapActions('sendingPrepare', ['getReceptionCollectionConfig']), // 查询是否开通前台代收
        pushErr(ary, key, errMsg) {
            Array.isArray(ary.errors) &&
                !ary.errors.some(item => item.fieldName === key) &&
                ary.errors.push({
                    fieldName: key,
                    errorInfo: errMsg,
                });
        },
        toBack() {
            this.sensorsFn('Ent_ContractSendDetail_BtnClick', {
                first_category: '顶部导航栏',
                icon_name: '返回',
            });
            this.$router.push(this.$route.query.from === 'doc' ? '/doc' : '/account-center/home');
        },
        async toNext() {
            this.sensorsFn('Ent_ContractSendDetail_BtnClick', {
                first_category: '顶部导航栏',
                icon_name: '下一步',
            });
            const { isUploading, docList } = this.$refs.upload;
            const { recipients, signOrdered } = this.$refs.addReciver;
            const contractTitle = this.$refs.ContractInfo.contractTitle;
            const contractTypeId = this.$refs.ContractInfo.contractTypeId;
            const customContractId = this.$refs.ContractInfo.customContractId;
            const expiredDate = formatDateToTimestamp(this.$refs.ContractInfo.expiredDate) * 1000;
            const contractLifeEnd = formatDateToTimestamp(this.$refs.ContractInfo.contractLifeEnd) * 1000;
            const describeFields = this.$refs.ContractInfo.describeFields;

            // 清除保存接收人定时器
            this.$refs.addReciver.clearTimer();

            if (isUploading) {
                this.$MessageToast.error(this.$t('prepare.isUploadingErr'));
                return;
            }
            if (!docList.length) {
                this.$MessageToast.error(this.$t('prepare.noUploadFileErr'));
                return;
            }
            if (!contractTitle) {
                this.$MessageToast.error(this.$t('prepare.noContractTitleErr'));
                return;
            }
            if (this.contractTitleErr) {
                this.$MessageToast.error(this.contractTitleErr);
                return;
            }
            if (this.contractTypeErr) {
                this.$MessageToast.error(this.$t('prepare.contractTypeErr'));
                return;
            }
            if (new Date().getTime() / 1000 >= expiredDate) {
                this.$MessageToast.error(this.$t('prepare.expiredDateErr'));
                return;
            }
            if (!expiredDate) {
                this.$MessageToast.error(this.$t('prepare.noExpiredDateErr'));
                return;
            }
            if (!this.$refs.ContractInfo.checkDescribeFields()) {
                this.$MessageToast.error(this.$t('prepare.describeFieldsErr'));
                return;
            }
            if (!recipients.length) {
                this.$MessageToast.error(this.$t('prepare.noRecipientsErr'));
                return;
            }

            // 清空error
            recipients.forEach((item, index) => {
                this.$set(recipients, index, {
                    ...item,
                    errors: [],
                });
            });
            let isLackAttachmentName = false;
            let hasAddSender = false;
            // 签约方格式详细校验
            recipients.forEach((item) => {
                const recipient = item;
                if (recipient.userType === 'PERSON' && recipient.userAccount === this.commonHeaderInfo.platformUser.account) {
                    hasAddSender = true;
                }
                // 老合同设置附属资料为必填
                this.setAttachmentNecessary(item);
                const { idNumberForVerify } = item;
                // 签约方为企业，开通了前台代收功能，使用了前台代收功能 - 不需要填写任何账号
                delete item['ifProxyClaimer_end'];
                if (
                    item.userType === 'ENTERPRISE' &&
                    this.receptionCollection &&
                    item.ifProxyClaimer
                ) {
                    // do nothing
                } else {
                    if (!recipient.userAccount) {
                        this.pushErr(recipient, 'userAccount', this.$t('prepare.noAccountErr'));
                    }
                    if (
                        recipient.userAccount.split(';').some(t => t && !resRules.userAccount.test(t))
                    ) {
                        this.pushErr(recipient, 'userAccount', this.$t('prepare.accountFormatErr'));
                    }
                }

                if (recipient.userName && !resRules.userName.test(recipient.userName)) {
                    this.pushErr(recipient, 'userName', this.$t('prepare.userNameFormatErr'));
                }

                if (item.userType === 'ENTERPRISE') {
                    const errorMsg = checkEntNameFormat(recipient.enterpriseName);
                    if (errorMsg) {
                        this.pushErr(recipient, 'enterpriseName', errorMsg);
                    }
                }

                if (item.userType === 'PERSON') {
                    if (recipient.personNameNeeded && !recipient.userName) {
                        this.pushErr(recipient, 'userName', this.$t('prepare.noUserNameErr'));
                    }

                    if (recipient.personIdNumberNeeded && recipient.requireIdentityAssurance && !recipient.idNumberForVerify) {
                        this.pushErr(recipient, 'idNumberForVerify', this.$t('prepare.noIDNumberErr'));
                    }
                }

                if (idNumberForVerify && !resRules.IDCardReg.test(idNumberForVerify) && !idNumberForVerify.includes('*')) {
                    return this.pushErr(recipient, 'idNumberForVerify', this.$t('prepare.idNumberForVerifyErr'));
                }
                if (!isLackAttachmentName) {
                    isLackAttachmentName = item.attachmentList.some(item => !item.name);
                }
            });
            if (recipients.some(item =>
                item.errors && item.errors.length,
            )) {
                this.$MessageToast.error(this.$t('prepare.signerErr'));
                return;
            }

            // 判断至少存在一个签署人
            if (!recipients.some(person => {
                return person.receiverType !== 'CC_USER';
            })) {
                this.$MessageToast.error(this.$t('prepare.noSignerErr'));
                return;
            }
            if (isLackAttachmentName) {
                return this.$MessageToast.error(this.$t('prepare.lackAttachmentNameErr'));
            }
            // 非顺序签署，不能重复添加签约方
            const uniqByObj = uniqBy(recipients, 'userAccount', 'enterpriseName');
            const uniqRecipients = uniqByObj.res;
            const repeatRecipients = uniqByObj.repeat;

            repeatRecipients.forEach(item => {
                const error = {
                    errorInfo: this.$t('prepare.repeatRecipientsErr'),
                    fieldName: 'userAccount',
                };
                item.errors.push(error);
            });

            if (!signOrdered && uniqRecipients.length < recipients.length) {
                this.$MessageToast.error(this.$t('prepare.repeatRecipientsErr'));
                return;
            }
            if (this.showGuideSender && !hasAddSender) {
                this.showGuideSender = false;
                this.$confirm(this.$t('prepare.needAddSender'), this.$t('prepare.tip'), {
                    confirmButtonText: this.$t('prepare.addSender'),
                    cancelButtonText: this.$t('prepare.cancel'),
                }).then(() => {
                    this.addSender();
                }).catch(() => {
                    this.toNext();
                });
                return;
            }
            this.isLoading = true;

            // 保存选择的代发企业账号
            if (this.entOptionsInfos.length > 1 && this.commonHeaderInfo.currentEntId !== '0') {
                const params = {
                    entId: this.proxySenderEntId,
                    enterpriseName: this.entOptionsInfos.filter(a => a.entId === this.proxySenderEntId)[0].entName,
                };
                await this.$http.post(`/contract-api/contracts/${this.contractId}/save-sender`, params);
            }

            await this.$http.post(`${signPath}/contracts/${this.contractId}`, {
                contractTitle,
                expiredDate,
                contractLifeEnd,
                contractTypeId,
                signOrdered,
                receivers: recipients.map(r => {
                    /*
                        注意事项：
                        1. 账号 -> 接收手机/邮箱
                        2. 与接口数据交互：签署时 账号传proxyClaimerAccounts，抄送时 账号赋值userAccount
                        3. 页面中账号 input v-model="userAccount"，初始化数据需要做下转换
                        */
                    const userAccount = r.receiverType === 'CC_USER' ? r.userAccount : null;
                    const proxyClaimerAccounts = (r.userType === 'ENTERPRISE'
                        ? (r.userAccount ? r.userAccount.split(';') : [])
                        :                                [r.userAccount]).filter(v => !!v);
                    return {
                        ...r,
                        proxyClaimerAccounts,
                        userAccount,
                    };
                }),
                viewedLetter: 'false',
                describeFields,
                customContractId,
            }, { noToast: 1 })
                .then(res => {
                    this.sensorsFn('Ent_ContractSendDetail_Result', {
                        first_category: '顶部导航栏',
                        is_in_order: signOrdered,
                        icon_name: '下一步',
                        is_success: true,
                    });
                    if (res.data.hasError) {
                        this.$MessageToast({
                            type: 'error',
                            message: res.data.generalError || this.$t('prepare.signerErr'), // err.response.data.receivers[0].errors[0],
                            duration: 5000,
                        });
                        res.data.outerReceivers.forEach((item, index) => {
                            this.$set(recipients, index, {
                                ...recipients[index],
                                ...item,
                            });
                        });
                    } else {
                        if (this.entOptionsInfos.length > 1) {
                            const entName = this.entOptionsInfos.filter(a => a.entId === this.proxySenderEntId)[0].entName;
                            this.$msgbox({
                                title: this.$t('sign.prompt'),
                                message: this.$t('prepare.senderNotice', { entName: entName }),
                                showCancelButton: true,
                                confirmButtonText: this.$t('sign.submit'),
                                cancelButtonText: this.$t('sign.cancel'),
                                customClass: 'message-box-confirm-custom',
                            }).then(() => {
                                this.$router.push(`/sign/field?${this.isSSO ? 'fromSSO=true&' : ''}contractId=${this.contractId}${this.returnUrl ? '&returnUrl=' + encodeURIComponent(this.returnUrl) : ''}`);
                            });
                        } else {
                            this.$router.push(`/sign/field?${this.isSSO ? 'fromSSO=true&' : ''}contractId=${this.contractId}${this.returnUrl ? '&returnUrl=' + encodeURIComponent(this.returnUrl) : ''}`);
                        }
                    }
                })
                .catch(err => {
                    this.sensorsFn('Ent_ContractSendDetail_Result', {
                        first_category: '顶部导航栏',
                        is_in_order: signOrdered,
                        icon_name: '下一步',
                        is_success: false,
                        request_url: err.config.url,
                        fail_reason: err.response?.data?.message || err.message,
                        fail_error_code: err.response?.data?.code,
                        fail_http_code: err.response?.status,
                    });
                    const errMsg = err.response.data.message;
                    this.$MessageToast.error(errMsg);
                    if ('' + err.response.data.code === '130001') {
                        this.contractTitleErr = errMsg;
                    }
                })
                .finally(() => {
                    this.isLoading = false;
                    const signTypeMap = { SIGNATURE: '签字', SEAL: '盖章', SEAL_AND_SIGNATURE: '盖章并签字' };

                    recipients.map(recipient => {
                        const seal_type = recipient.signType ? signTypeMap[recipient.signType] : '抄送';
                        this.sensorsFn('Ent_ContractSendSignInfo_Result', {
                            first_category: '顶部导航栏',
                            company_id: recipient.enterpriseId,
                            company_name: recipient.userType === 'ENTERPRISE' ? recipient.enterpriseName : '个人签署人',
                            signatory_role: recipient.roleName,
                            is_in_order: signOrdered,
                            seal_type: seal_type,
                            more_select_list: this.getModuleList(recipient),
                            icon_name: '下一步',
                        });
                    });
                });
        },
        addSender() {
            const newRecipient = {
                ...JSON.parse(JSON.stringify(defaultRecipient)),
                ...this.getDefaultConfig.personDefaultRecipient,
                userType: 'PERSON',
                routeOrder: this.$refs.addReciver.recipients.length + 1,
                signType: 'SIGNATURE',
                userAccount: this.commonHeaderInfo.platformUser.account,
                participationType: 'SIGNATURE',
            };
            this.$refs.addReciver.recipients.push(newRecipient);
        },
        setFilename(data) {
            this.$refs.ContractInfo.contractTitle = data;
            this.hasPresetPermissions && this.getPresetConfig();
        },

        setAttachmentNecessary(list) {
            list.attachmentList.forEach((item) => {
                item.necessary = true;
            });
        },

        setContractLifeEnd(data) {
            if (data === '') {
                this.$refs.ContractInfo.contractLifeEnd = '';
            } else if (data !== '0') {
                this.$refs.ContractInfo.contractLifeEnd = new Date(Number(data));
                this.$refs.ContractInfo.dateTipsVisible = true;
            }
        },

        closeDateTips() {
            this.$refs.ContractInfo.dateTipsVisible = false;
        },

        onLoaded(component) {
            this[component] = true;
            this.$nextTick()
                .then(() => {
                    if (this.uploadReady && this.contractInfoReady && this.addReciverReady) {
                        this.isLoading = false;
                    }
                });
        },

        onSignOrderedGet(v) {
            if (v !== null) {
                this.signOrdered = v;
            }
        },

        goPresetConfig() {
            this.sensorsFn('Ent_ContractSendDetail_BtnClick', {
                first_category: '上传文档',
                icon_name: '根据合同内容预置初始值',
            });
            if (this.hasPresetPermissions) {
                this.$router.push('/console/enterprise/contract/send-manage');
            } else {
                this.presetConfigShow = true;
                this.sensorsFn('Ent_ContractSendDetailWindow_PopUp', {
                    window_name: '什么是合同预置模板？',
                });
            }
        },
        clickPreSetDialogConfirm() {
            this.sensorsFn('Ent_ContractSendDetailWindow_BtnClick', {
                window_name: '什么是合同预置模板？',
                icon_name: '我知道了',
            });
            this.presetConfigShow = false;
        },
        // 获取代发企业选项信息
        getProxyEntInfo() {
            return this.$http.get('/ents/proxy/contract/subjects');
        },

        // 获取已选发件方信息
        getSelectedProxyEntInfo() {
            return this.$http.get(`/contract-api/contracts/${this.contractId}/get-sender`);
        },

        handleUploaded() {
            this.hadUploadFile = true;
        },

        getPresetConfig() {
            this.$http.post(`/contract-api/preset-config/contract/${this.contractId}/analysis`).then(res => {
                if (res.data) {
                    this.presetConfig = res.data;
                    this.$refs.ContractInfo.resetData(res.data);
                    this.getDefaultConfig.then(({ personDefaultRecipient, entDefaultRecipient }) => {
                        if (Array.isArray(res.data.presetReceiverVO)) {
                            res.data.presetReceiverVO.forEach(item => {
                                if (item.userType === 'PERSON') {
                                    if (item.requireIdentityAssurance) {
                                        Object.assign(item, personDefaultRecipient);
                                    } else {
                                        personDefaultRecipient.personNameNeeded && (item.personNameNeeded = personDefaultRecipient.personNameNeeded);
                                        personDefaultRecipient.personIdNumberNeeded && (item.personIdNumberNeeded = personDefaultRecipient.personIdNumberNeeded);
                                    }
                                } else if (item.userType === 'ENTERPRISE' && item.requireIdentityAssurance) {
                                    Object.assign(item, entDefaultRecipient);
                                }
                            });
                        }
                        this.$refs.addReciver.resetReciver(res.data.presetReceiverVO || []);
                    });
                    this.signOrdered = res.data.isSignOrdered;
                } else {
                    this.$refs.addReciver.resetReciver([]);
                }
            });
        },
        // 是否第一次需要提醒添加发件方为签约方
        async handleGuideSender() {
            const  { data: { value } } = await this.$http.get('/users/configs/default/FIRST_AFTER_SEND_OPTIMIZATION');
            // true表示第一次
            if (value === 'true') {
                this.showGuideSender = true;
                this.$http.post('/users/configs/FIRST_AFTER_SEND_OPTIMIZATION', {
                    name: 'FIRST_AFTER_SEND_OPTIMIZATION',
                    value: false,
                });
            }
        },
    },
    created() {
        if (this.commonHeaderInfo.isLimitedAuth) {
            this.isLoading = true;
            return this.$MessageToast.error({
                message: '请实名后再发送',
                callBack: () => history.length === 1 ? window.close() : history.back(),
            });
        }
        // 获取集团授权的代发企业列表
        if (this.commonHeaderInfo.currentEntId !== '0' && this.isNewGroup) {
            this.getProxyEntInfo().then(res => {
                this.entOptionsInfos = res.data;
                if (this.entOptionsInfos.length > 1) {
                    this.getSelectedProxyEntInfo()
                        .then(res => {
                            this.proxySenderEntId = (res.data && res.data.entId) || this.commonHeaderInfo.currentEntId;
                        });
                }
            });
        }
        this.currentEnt = getCurrentUser(this.commonHeaderInfo, this.getAuthStatus);
        if (this.getUserType === 'Enterprise') {
            this.getReceptionCollectionConfig();
        }
        if (this.commonHeaderInfo.currentEntId === '0') {
            this.handleGuideSender();
        }
    },
    mounted() {
        this.enterTime = new Date().getTime();
        this.sensorsFn('Ent_ContractSendDetail_PageView');
    },
    beforeRouteLeave(to, from, next) {
        // 清除保存接收人定时器
        this.$refs.addReciver.clearTimer();
        next();
    },
    async beforeRouteEnter(to, from, next) {
        let contractId = to.query.contractId;
        if (!contractId) {
            contractId = (await Vue.$http.post(`/contract-api/contracts`)).data.contractId;
            const queryArray = { ...to.query, contractId };
            const queryString = Object.entries(queryArray).reduce((paramsString, currentQueryArray) => {
                paramsString += '&';
                return paramsString + `${currentQueryArray[0]}=${encodeURIComponent(currentQueryArray[1])}`;
            }, `?`);
            router.replace(`/sign/prepare${queryString}`);
            /* 不可调用next(false), 不然replace会失效 */
            // next(false);
        } else {
            next(vm => {
                // 判断是否是草稿合同继续操作及是否直接进入拖章页
                if (to.query.fromSSO === 'true' || to.query.isSSOBack === 'true') {
                    Vue.$http.get(`/contract-api/contracts/${contractId}/forwarding-seal-page`)
                        .then(res => {
                            if (res && res.data && res.data.forwardingSealPageDirectly && to.query.isSSOBack !== 'true') {
                                const returnUrl = to.query.returnUrl ? `&returnUrl=${to.query.returnUrl}` : '';
                                window.location.href = `/sign/field?contractId=${contractId}${returnUrl}&fromSSO=true`;
                            } else {
                                vm.contractEditable = res && res.data && res.data.receiversPageEditable; // 标记合同能否返回编辑发件人信息页
                            }
                        }).catch(() => {
                            vm.pageUnaccessable = true; // 单点进来无编辑权限时，移除loading
                        });
                }
            });
        }
    },
    beforeDestroy() {
        this.sensorsFn('Ent_ContractSendDetail_PageLeave', {
            $event_duration: (new Date().getTime() - this.enterTime) / 1000,
        });
    },
};
</script>

<style lang="scss">

	.sign-prepare-page {
		.header {
			position: fixed;
			top: 0;
		}
		.diseditable {
			pointer-events: none;
		}
		.prepare-container {
			width: 1000px;
			background-color: #fff;
			margin: 0 auto;
			padding-top: 50px;

			.order-num {
				font-size: 18px;
				color: #127fd2;
			}
		}
        //确认发起方
        .sender-title{
            height: 50px;
			line-height: 50px;
			border-bottom: 1px solid $border-color;
			font-size: 18px;
			font-weight: bold;
        }
        .sender-name{
            padding:20px 0;
            color: #333;
        }
		// 上传文件样式
		.upload-title {
			position: relative;
			height: 50px;
			line-height: 50px;
			border-bottom: 1px solid $border-color;
			font-size: 18px;
			font-weight: bold;

			.operate-video{
				position: absolute;
				right: 0;
				color: #2298f1;
				font-size: 14px;
				font-weight: normal;

				i{
					font-size: 18px;
					vertical-align: text-bottom;
				}
			}
            .preset{
                float: right;
                color: #127FD2;
                font-size: 16px;
                font-weight: normal;
                cursor: pointer;
            }
		}
		.addreciver-title {
			width: 1000px;
			height: 50px;
			line-height: 50px;
			border-bottom: 1px solid #ddd;
			font-size: 18px;
			font-weight: bold;
		}

		.footer {
			background-color: #F4F4F4;
		}

		:-moz-placeholder { /* Mozilla Firefox 4 to 18 */
			color: #ccc; opacity:1;
		}

		::-moz-placeholder { /* Mozilla Firefox 19+ */
			color: #ccc;opacity:1;
		}

		input:-ms-input-placeholder{
			color: #ccc;opacity:1;
		}

		input::-webkit-input-placeholder{
			color: #ccc;opacity:1;
		}
	}

	.ent-select-dropdown {
		.el-select-dropdown__item.selected {
			color: #fff !important;
		}
	}
</style>
