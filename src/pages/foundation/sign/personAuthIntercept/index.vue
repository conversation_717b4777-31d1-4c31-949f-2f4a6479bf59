<template>
    <div class="auth-intercept-container">
        <div class="intercept-container-icon">
            <img :src="logoUrl" height="60" alt="logo">
        </div>
        <template v-if="getIsForeignVersion">
            <div class="intercept-container-content">
                <template v-if="!nameConfirmed">
                    <p class="container-content-title">{{ $t('signJa.beforeSignTip2', { signer: signerName }) }}</p>
                    <p class="container-content-desc">{{ $t('signJa.beforeSignTip3') }}</p>
                </template>
                <template v-else>
                    <p>{{ $t('signJa.beforeSignTip4', { currentUser: realName, signer: signerName }) }}</p>
                </template>
            </div>
            <div class="intercept-container-auth-wrapper">
                <el-button v-if="!nameConfirmed" class="intercept-container-btn" type="primary" @click="confirmName">{{ $t('signJa.itsMe') }}</el-button>
                <template v-else>
                    <el-button class="intercept-container-btn" type="primary" @click="confirmName">{{ $t('signJa.confirmChange') }}</el-button>
                    <el-button class="intercept-container-btn" @click="nameConfirmed = false">{{ $t('signJa.communicateSender2') }}</el-button>
                </template>
            </div>
        </template>
        <template v-else>
            <div class="intercept-container-content">
                <p class="container-content-title">{{ entName }} {{ $t('personAuthIntercept.title') }}</p>
                <p class="container-content-name" v-if="signerName"><strong>{{ $t('personAuthIntercept.name') }}{{ signerName }}</strong></p>
                <p class="container-content-id" v-if="signerIdNumber"><strong>{{ $t('personAuthIntercept.id') }}{{ signerIdNumber }}</strong></p>
                <p>{{ $t('personAuthIntercept.descNoSame1') }}</p>
                <div class="container-content-desc" v-if="interceptType === 'noAuth'">
                    <p>{{ $t('personAuthIntercept.descNoAuth1') }}</p>
                    <p>{{ $t('personAuthIntercept.descNoAuth2') }}</p>
                </div>
                <div class="container-content-desc" v-else>
                    <p>{{ $t('personAuthIntercept.descNoSame2') }}</p>
                    <div class="intercept-container-reAuth" v-if="interceptType === 'noSame'">
                        <p>{{ $t('personAuthIntercept.authInfo') }}<strong>{{ realName }}</strong></p>
                        <p class="intercept-container-tips">{{ $t('personAuthIntercept.in') }}{{ authTime }}{{ $t('personAuthIntercept.finishAuth') }}</p>
                        <br>
                        <p>{{ $t('personAuthIntercept.ask') }}</p>
                        <el-button class="intercept-container-btn" type="primary" @click="goToReAuth">{{ $t('personAuthIntercept.reAuthBtnText') }}</el-button>
                        <el-button class="intercept-container-btn" @click="goToRemind">{{ buttonCopyWriting?buttonCopyWriting:$t('personAuthIntercept.changePhoneText') }}</el-button>
                    </div>
                </div>
                <p class="container-content-tip" v-if="interceptType === 'noSame'">{{ $t('personAuthIntercept.tips') }}</p>
            </div>
            <div class="intercept-container-auth-wrapper">
                <p><span @click="confirm" class="intercept-container-auth-btn">{{ btn[interceptType] }}</span></p>
            </div>
            <ReAuthDialog :isIntercept="true" :visible.sync="reAuthDialogVisible" @confirm="reAuthHandle"></ReAuthDialog>
        </template>
    </div>
</template>

<script>
import { mapGetters } from 'vuex';
import Icon from 'src/common/assets/img/auth_warning.png';
import { getMaskName, getMaskIdCard } from 'src/common/utils/reg';
import ReAuthDialog from 'components/reAuthDialog/index.vue';
import dayjs from 'dayjs';
import { getAuthChangeCopyWriting } from 'src/api/sign';
export default {
    name: 'PersonAuthIntercept',
    components: {
        ReAuthDialog,
    },
    data() {
        return {
            // 拦截类型：目前为未认证(noAuth)或者信息不一致(noSame)
            interceptType: '',
            Icon: Icon,
            signerName: '',
            signerIdNumber: '',
            entName: '',
            contractId: '',
            signerNameNoMask: '',
            signerIdNumberNoMask: '',
            supplementInfo: '',
            btn: {
                noAuth: this.$t('personAuthIntercept.goOn'),
                needMore: this.$t('personAuthIntercept.goMore'),
                noSame: this.$t('personAuthIntercept.goHome'),
            },
            reAuthDialogVisible: false,
            authTime: '', // 实名的时间
            realName: '', // 实名的名字
            nameConfirmed: false,
            buttonCopyWriting: '',
            popCopyWriting: '',
            logoUrl: '',
        };
    },
    computed: {
        ...mapGetters(['getIsForeignVersion']),
    },
    methods: {
        confirmName() {
            if (this.interceptType === 'noSame' && !this.nameConfirmed) {
                this.nameConfirmed = true;
                return;
            }
            this.$http.put('/users/name', { newName: this.signerNameNoMask }).then(() => {
                location.href = this.$localStorage.get('transitionHref');
            });
        },
        getAuthInfo() {
            this.$http.get('/users/auth/passed-simple-info')
                .then((res) => {
                    this.authTime = dayjs(res.data.authPassTime).format('YYYY年MM月DD日 HH:mm');
                    this.realName = res.data.realName;
                })
                .catch(() => {});
        },
        getCustomizeLogo(entId) {
            const logoUrl = `/ents/ignore/logo?entId=${entId}`;
            const defaultLogoUrl = require('img/bestsign-logo.png');
            this.$http.get(logoUrl).then(res => {
                const { data } = res;
                this.logoUrl = data ? logoUrl : defaultLogoUrl;
            }).catch(() => {
                this.logoUrl = defaultLogoUrl;
            });
        },
        // 初始化页面数据
        async initPageData() {
            this.getAuthInfo();
            const params = JSON.parse(sessionStorage.getItem('personAuthIntercept'));
            this.signerName = params.inputUserName ? getMaskName(params.inputUserName) : '';
            this.signerIdNumber = params.idNumberForVerify ? getMaskIdCard(params.idNumberForVerify) : '';
            this.signerNameNoMask = params.inputUserName;
            this.signerIdNumberNoMask = params.idNumberForVerify;
            this.entName = params.letterSender;
            this.contractId = params.contractId;
            this.getCustomizeLogo(params.senderEntId);
            // 用户未实名
            if (!params.hasUserAuthenticated) {
                this.interceptType = 'noAuth';
            } else {
                this.interceptType = 'noSame';
            }
            const { data: { buttonCopyWriting = '', popCopyWriting = '' } } = await getAuthChangeCopyWriting(this.contractId);
            this.buttonCopyWriting = buttonCopyWriting;
            this.popCopyWriting = popCopyWriting;
        },
        goToRemind() {
            let msg = this.$t('personAuthIntercept.changePhoneTip1') + this.entName + this.$t('personAuthIntercept.changePhoneTip2');
            if (this.popCopyWriting) {
                msg = this.popCopyWriting;
            }
            this.$alert(msg, '', { confirmButtonText: '确定' });
        },
        confirm() {
            if (this.interceptType === 'noSame') {
                // 如果签署人主体不一致 回到首页
                this.$router.push('/account-center/home');
                return;
            }
            if (this.getIsForeignVersion) {
                return this.$http.put('/users/name', {
                    newName: this.signerNameNoMask,
                }).then(() => {
                    location.href = this.$localStorage.get('transitionHref');
                });
            }
            this.goToAuth();
        },
        goToAuth() {
            this.$localStorage.set(
                'contractsFaceConfig',
                JSON.stringify({
                    contractIds: [this.contractId],
                }),
            ); // 刷脸供应商根据contractId查询
            this.$localStorage.set('designatedIdentityData', JSON.stringify({
                name: this.signerNameNoMask || '',
                idCard: this.signerIdNumberNoMask || '',
                contractId: this.contractId,
            }));
            // 如果签署人未实名 前往个人实名签署引导页
            this.$router.push('/foundation/authentication/guide');
        },
        goToReAuth() {
            this.reAuthDialogVisible = true;
        },
        reAuthHandle() {
            this.$http.post('/users/auth/restart')
                .then(() => {
                    this.goToAuth();
                });
        },
    },
    created() {
        this.initPageData();
    },
};
</script>

<style lang="scss">
.auth-intercept-container{
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #333;
    font-weight: 400;
    font-size: 14px;
    .intercept-container-icon {
        margin: 70px 0;
    }
    .container-content-title {
        margin-bottom: 20px;
    }
    .container-content-desc {
        margin-top: 20px;
    }
    .container-content-tip {
        margin-top: 20px;
        color: #999;
    }
    .intercept-container-btn {
        margin-top: 40px;
    }
    .intercept-container-reAuth {
        margin-top: 20px;
        font-size: 14px;
        .intercept-container-auth-btn {
            color: #127fd2;
            cursor: pointer;
        }
    }
    .intercept-container-auth-wrapper {
      text-align: center;
      margin-top: 20px;
    .intercept-container-auth-btn {
        color: #127fd2;
        font-size: 12px;
        cursor: pointer;
    }
  }
  .intercept-container-tips {
      color: #666;
      font-size: 12px;
      margin: 0 0 10px 0;
  }
}
</style>
