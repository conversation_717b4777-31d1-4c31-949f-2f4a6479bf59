<template>
    <div class="face-qrcode-signtip-page">
        <header class="tip-header">
            <span v-if="channel == 'login'" @click="$router.push('/doc/list')"></span>
            刷脸签署
        </header>
        <div class="main">
            <div class="tip-wrap">
                <p class="p1"><i class="el-icon-ssq-tishi1"></i>合同发件人要求您刷脸签署</p>
                <img class="tip-pic" :src="`data:image/png;base64, ${qrSrc}`" width="188" height="188" />
                <p class="p2">请用支付宝或微信扫一扫签署</p>
            </div>
        </div>
        <AdFooter></AdFooter>
    </div>
</template>

<script>
import AdFooter from 'components/ad_footer/AdFooter.vue';
import { imgLoadingSrc } from 'src/common/assets/base64/base64.js';

export default {
    components: { AdFooter },
    data() {
        return {
            contractId: this.$route.query.contractId || '',
            urlToken: this.$route.query.token || '',
            channel: this.$route.query.channel || '',
            receiverId: this.$route.query.receiverId || 0,
            qrSrc: imgLoadingSrc,
        };
    },
    methods: {
        // ajax
        getQrSignImgUrl() {
            return this.$http.get(`${signPath}/ignore/contracts/${this.contractId}/receivers/${this.receiverId}/sign-qrcode`, {
                params: {
                    token: this.urlToken,
                },
            });
        },
    },
    created() {
        this.getQrSignImgUrl()
            .then(res => {
                this.qrSrc = res.data.value;
            })
            .catch(() => {});
    },
};
</script>

<style lang="scss">
    .face-qrcode-signtip-page {
        position: relative;
        background-color: #ffffff;
        height: 100%;
        .tip-header {
            z-index: 999;
            position: relative;
            width: 100%;
            height: 50px;
            line-height: 50px;
            text-align: center;
            font-size: 18px;
            color: #fff;
            background-color: #00263C;
            span {
                position: absolute;
                left: 0;
                top: 0;
                display: inline-block;
                width: 55px;
                cursor: pointer;
                &:after {
                    content: "";
                    position: absolute;
                    top: 18px;
                    left: 24px;
                    border-left: 2px solid #fff;
                    border-top: 2px solid #fff;
                    padding: 5px;
                    display: inline-block;
                    transform: rotate(-45deg);
                    -webkit-transform: rotate(-45deg);
                }
                &:hover {
                    background-color: #0072CF;
                }
            }
        }
        .main {
            margin-top: 70px;
            width: 100%;
            overflow: auto;
        }
        .tip-wrap {
            width: 272px;
            margin: 0 auto;
            text-align: center;
            .p1 {
                font-size: 14px;
                color: #333;
                i {
                    font-size: 16px;
                    color: #127fd2;
                    margin-right: 3px;
                }
            }
            img {
                position: relative;
                margin-top: 22px;
            }
            .p2 {
                font-size: 14px;
                color: #999;
                margin-top: 8px;
            }
        }
    }
    @media only screen and (min-width: 319px) and (max-width: 374px)  {
       .face-qrcode-signtip-page .main{
            margin-top: 45px;
       }
    }

    @media only screen and (min-width: 415px) {
       .face-qrcode-signtip-page .main{
            margin-top: 150px;
       }
    }
</style>
