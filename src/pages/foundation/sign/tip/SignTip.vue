<template>
    <div class="sign-tip-wrap" v-loading.fullscreen.lock="loading">
        <header class="tip-header" v-if="!isQyWx">
            {{ pageTitle }}
        </header>
        <div class="main" :class="signSuccess ? 'sign-success-main' : ''">
            <div class="tip-wrap">
                <!-- 移动端 合同签署中或签署完成时 可查看和下载合同-->
                <template v-if="tip">
                    <div v-if="iconStatus">
                        <img class="tip-pic" :class="signSuccess ? ' sign-success' : ''" :src="tip.pic" alt="signing status" :width="signSuccess ? 300 : 35" />
                    </div>
                    <div v-else>
                        <img class="tip-pic" :src="tip.pic" alt="signing status" width="75" />
                    </div>
                </template>
                <div class="sign-congratulate" v-if="signSuccess && showSignCount">
                    <template v-if="!getIsForeignVersion">
                        <div class="congratulate-text">{{ $t('signTip.congratulationsCn', ) }}</div>
                        <template v-if="contract.carbonReduction>0">
                            <div class="carbon-saving">{{ $t('signTip.carbonSavingSingleCn',{ num: contract.carbonReduction }) }}</div>
                            <div class="carbon-verification">
                                <span class="verification-text">{{ $t('signTip.carbonVerification') }}</span>
                                <div class="carbon-logo">
                                    <img src="~img/sign-tip/carbonstop-logo.png" alt="碳阻迹" class="carbonstop-icon">
                                </div>
                            </div>
                        </template>
                    </template>
                    <template v-else-if="signContractCount && giftContract">
                        <div>{{ $t('signTip.congratulations', { name: currentName, num: signContractCount }) }}</div>
                        <div>{{ $t('signTip.carbonSaving', { num: (signContractCount * 2002.4).toFixed(1) }) }}</div>
                    </template>
                    <template v-else>
                        <div>{{ $t('signTip.congratulationsSingle', { name: currentName }) }}</div>
                        <div>{{ $t('signTip.carbonSavingSingle') }}</div>
                    </template>
                </div>
                <div class="sign-gift" v-show="giftContract">
                    {{ $t('signTip.signGift', { num: giftContract, limit: giftContractTime }) }}
                </div>
                <div class="tip-des" v-if="signTip">
                    {{ applyForSeal ? $t('signTip.submitCompleted') : tip.des }}
                </div>
                <div v-if="revokeReason">
                    {{ $t('docDetail.reason') +': ' +revokeReason }}
                </div>
                <div class="tip-des acc-del-des"
                    v-else-if="accDelTip"
                >
                    <p>{{ $t('signTip.beenDeleted') }}</p>
                    <p>{{ $t('signTip.unActive') }}</p>
                </div>
                <div class="tip-des connect-err-des"
                    v-else-if="contractConnectErr"
                >
                    <h4>{{ $t('signTip.cannotReview') }}</h4>
                    <p>{{ $t('signTip.privateStorage') }}</p>
                </div>
                <div v-if="signTip">
                    <div class="jump">
                        <div class="status"
                            v-if="status == '5' || status == '6' || status == '11'"
                        >
                            <!--对展示过滤：满足审批中且不是发送前审批 的情况，不展示 -->
                            <template v-if="!(status == '5' && contract.contractStatus !== 'IN_SEND_APPROVAL')">
                                <!--第一行 合同状态-->
                                <p>
                                    <span style="width: 50%">{{ $t('signTip.contratStatusDes', {key: signTypeTranslate}) }}</span>
                                    <span style="width: 45%; text-align: left">{{ signStatus }}</span>
                                </p>
                                <!--第二行 合同签署状态-->
                                <p>
                                    <span style="width: 50%">{{ $t('signTip.contractConditionDes', {key: signTypeTranslate}) }}</span>
                                    <span style="width: 45%; text-align: left"> {{ signDetail }}</span>
                                </p>
                            </template>
                        </div>
                        <div class="view-contract" v-if="ifCanViewContractsOnPC">
                            <span>{{ $t('signTip.operate') }}：</span>
                            <a
                                target="_blank"
                                :href="`/doc/detail/${contractId}`"
                                @click="handleViewContract"
                            >
                                {{ $t('signTip.viewContract', {alias: contractAliasText}) }}
                            </a>
                            <span class="view-contract-tip" v-if="status==='11'">{{ $t('signTip.viewContractTip') }}</span>
                        </div>
                        <template v-if="ifCanViewContractList">
                            <a
                                class="view-contract-list"
                                target="_blank"
                                :href="waitToSignContractNum ? `/doc/shortcut/${needMeSignShortcutId}` : '/doc/list'"
                            >
                                {{ $t('signTip.viewContractList') + (waitToSignContractNum ? $t('signTip.needMeSign', {num: waitToSignContractNum}) : '') }}
                            </a>
                        </template>
                        <div v-if="status == 6 && !isVisitor" class="get-free-contract">
                            <span>{{ $t('signTip.freeContract') }}</span>
                            <div>
                                <img src="~img/sign/sendContract.gif" alt="">
                                <span class="send-contract" @click="$router.push('/account-center/home')">{{ $t('signTip.sendContract') }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 企业微信小程序或链接失效可返回 -->
                <div v-if="isQyWx || status == '7'">
                    <el-button plain class="contract-btn" @click="goHome()">返回首页</el-button>
                </div>
                <div v-if="!isQyWx && !isPC && canDownload && contract.hideSendToMailButton" class="hybrid-tip">
                    登陆上上签网页版www.bestsign.cn可下载该合同
                </div>
                <!-- 签署成功&&在appsdk里边 -->
                <div class="appSdk-btn" v-if="isAppSDK && status == '6'">
                    <el-button class="btn-type-one" @click="exitAppSDK">
                        {{ $t('signTip.back') }}
                    </el-button>
                </div>
            </div>
        </div>
        <template v-if="!isSinoRussia && !getIsForeignVersion">
            <!-- <AdFooter v-if="isPC" class="pc-footer"></AdFooter> -->
            <!-- 小程序不展示广告，手机端依旧展示 -->
            <div v-if="!isPC && isQyWx" class="logo-wrap">
                <el-carousel height="150px" trigger="click" ref="carousel" indicator-position="none">
                    <el-carousel-item>
                        <a :href="downloadAppSrc">
                            <div class="logo" @click="() => this.$refs.carousel.startTimer()">
                                <div class="download-btn">{{ $t('signTip.downloadBtn') }}</div>
                            </div>
                        </a>
                    </el-carousel-item>
                </el-carousel>
            </div>
            <CustomAdFooter v-if="ifShowCustomFooter" :adList="adList" :status="status"></CustomAdFooter>
            <PopupAndIconAd v-if="ifShowPopupOrIconAd" :adList="adList" />
        </template>
        <el-dialog v-model="isDataProduct" class="data-product-dialog">
            <div class="data-product-popup">
                <span class="close" @click="isDataProduct = false"></span>
                <img class="header" src="~img/sign-tip/<EMAIL>" alt="">
                <div class="content">
                    <div class="logo">
                        <img src="~img/sign-tip/wahaha.png" alt="">
                        <span>X</span>
                        <img src="~img/sign-tip/MYbank.png" alt="" class="bank">
                    </div>
                    <p>{{ $t('signTip.dataProduct.tip1', {entName: dataProduct.entName}) }}</p>
                    <p>{{ $t('signTip.dataProduct.tip2', {entName: dataProduct.entName, bankName: dataProduct.bankName}) }}</p>
                    <p class="tip">只需上传曾经在上上签平台签署的对公合同文件，即可快速测评资质。<a :href="dataProduct.path">查看详情 >></a></p>
                </div>
            </div>
        </el-dialog>
        <div class="wx-code" v-show="isPC">
            <div class="wx-code-contain">
                <img src="~img/sign/bestsign-wx-public.jpg" alt="">
                <p>{{ $t('signTip.followPublic') }}</p>
            </div>
        </div>
    </div>
</template>

<script>
import SuccessIcon from 'src/common/assets/img/success-tip-icon.png';
import SignSuccessIcon from 'src/common/assets/img/sign/signSuccess.gif';
import WarnIcon from 'src/common/assets/img/warn-icon.png';
import ApproveIcon from 'src/common/assets/img/approve-icon.png';
import End from 'src/common/assets/img/end.png';
import AddrError from 'src/common/assets/img/addrError.png';
import AccountDeletedTip from 'src/common/assets/img/account_delete_tip.png';
import NetConnectErr from 'img/net-error.png';
import CustomAdFooter from 'components/ad_footer/CustomAdFooter.vue';
import PopupAndIconAd from 'components/popupAndIconAd/index.vue';
import { isPC, isIOS } from 'utils/device.js';
import { mapGetters, mapState } from 'vuex';
import { getCheckAdmin, getAllEntInfo, postCheckAdmin, checkBlackAd } from 'src/api/sign.js';
import { getAdInfo } from 'src/api/sign.js';
import moment from 'dayjs';

export default {
    components: { CustomAdFooter, PopupAndIconAd },
    data() {
        return {
            isPC: isPC(),
            isAppSDK: false,
            contractId: this.$route.query.contractId,
            pageType: this.$route.query.type,
            status: String(this.$route.query.status || '7'),
            applyForSeal: this.$route.query.applySeal,
            loading: false,
            receiverId: this.$route.query.receiverId || '',
            revokeReason: this.$route.query.revokeReason || '',
            downloadAppSrc: isIOS
                ? 'https://itunes.apple.com/us/app/shang-shang-qian/id955123098'
                : 'http://a.app.qq.com/o/simple.jsp?pkgname=com.ssqian.bestsign.sign',
            contract: {
                contractStatus: '',
                ongoing: '',
                finished: '',
                systemType: '',
            },
            urlToken: localStorage.getItem('urlToken'),
            fullPath: this.$route.fullPath,
            isSinoRussia: this.$cookie.get('sino-russia') === '1',
            isDataProduct: false,
            dataProduct: {
                path: `${location.origin}/converge/loans-evaluate/upload`,
                entName: '娃哈哈',
                bankName: '网商银行',
            },
            contractAlias: 'CONTRACT',
            ifBlackUser: false,
            canShowStatusMap: ['2', '6'], // 可以展示广告的合同状态码——签署成功、拒签
            enterTime: 0,
            waitToSignContractNum: 0,
            needMeSignShortcutId: '',
            showSignCount: false,
            signContractCount: 0,
            giftContract: 0,
            giftContractTime: '',
            pageFrom: this.$route.query.pageFrom,
            adList: [],
        };
    },
    computed: {
        ...mapState(['isQyWx', 'commonHeaderInfo']),
        ...mapGetters(['getIsForeignVersion']),
        isVisitor() { // 访客模式登录
            return this.commonHeaderInfo.platformUser.visitor;
        },
        isEnt() {
            return this.commonHeaderInfo.userType === 'Enterprise';
        },
        currentName() {
            const { enterprises, currentEntId, platformUser } = this.commonHeaderInfo;
            return this.isEnt ? enterprises.find((item) => item.entId === currentEntId)?.entName : platformUser.fullName;
        },
        isEn() {
            return this.$i18n.locale === 'en'; // 设置了语言为英语
        },
        isZh() {
            return this.$i18n.locale === 'zh'; // 设置了语言为英语
        },
        // 是否展示投放广告
        ifShowCustomFooter() {
            return (!this.ifBlackUser && this.adList.length && this.canShowStatusMap.includes(this.status) && !this.getIsForeignVersion && this.isZh)  || false;
        },
        // 是否展示页面弹窗广告
        ifShowPopupOrIconAd() {
            return this.ifShowCustomFooter && this.status === '6' && this.adList.some(item => item.showcase === 1);
        },
        signTypeTranslate() {
            const s = this.signType;
            if (this.isEn) {
                if (!this.isPC) {
                    return s.charAt(0).toUpperCase() + s.slice(1, s.length);
                }
                return 'Contract ' + s;
            }
            return s;
        },
        // 合同别名文案
        contractAliasText() {
            return this.$t(`consts.contractAlias.${this.contractAlias.toLowerCase()}`);
        },
        tip() {
            const tipList = {
                1: { des: this.$t('signTip.contractRevoked', { alias: this.contractAliasText }),    pic: WarnIcon }, // 合同被撤销
                2: { des: this.$t('signTip.contractRefused', { alias: this.contractAliasText }),    pic: WarnIcon }, // 合同被拒签
                3: { des: this.$t('signTip.contractClosed', { alias: this.contractAliasText }),     pic: WarnIcon }, // 截止签约
                4: { des: this.$t('signTip.approvalReject', { alias: this.contractAliasText }),   pic: End }, // 审批驳回
                5: { des: this.$t('signTip.ApprovalCompleted'),  pic: SuccessIcon }, // 审批成功
                6: { des: this.$t('signTip.SigningCompleted'),   pic: SignSuccessIcon }, // 签署成功
                7: { des: this.$t('signTip.linkExpired'),  pic: AddrError }, // 链接已失效
                8: { title: this.$t('signTip.tips'),    pic: AccountDeletedTip }, // 账号被删除
                9: { pic: NetConnectErr }, // 无法连上合同所在服务器
                10: { des: this.$t('signTip.approving', { alias: this.contractAliasText }), pic: ApproveIcon }, // 正在审批
                11: { des: this.$t('signTip.submitCompleted'), pic: SuccessIcon },
                12: { des: this.$t('signTip.noTurnSign'), pic: WarnIcon }, // 没有签署权限或登录身份已过期
                13: { des: this.$t('signTip.noRightSign'), pic: WarnIcon }, // 合同签署中-当前用户不允许签署操作
                14: { des: this.$t('signTip.noNeedSign'), pic: WarnIcon },  // 内部决议合同，已无需进行签署
            };
            return tipList[this.status];
        },
        pageTitle() {
            let title =  this.status === '8' ? this.$t('signTip.tips') : this.$t('signTip.contractDetail');
            if (this.pageFrom === 'bindAccount') {
                title = this.$t('signTip.tips');
            }
            return title;
        },
        signTip() {
            const status = ['1', '2', '3', '4', '5', '6', '7', '10', '11', '12', '13', '14'];
            return status.includes(this.status);
        },
        accDelTip() {
            return this.status === '8';
        },
        contractConnectErr() {
            return this.status === '9';
        },
        signType() {
            return this.status === '5' ? this.$t('signTip.approval') : this.$t('signTip.sign');
        },
        signerStatus() {
            return this.status === '5' ? this.$t('signTip.approved') : this.$t('signTip.signed');
        },
        signStatus() {
            if (this.status === '5') {
                // 审批字段
                const temp = this.contract.ongoing ? 'signTip.contractIng' : 'signTip.contractComplete';
                return this.$t(temp, { alias: this.contractAliasText, key: this.$t('signTip.approval') });
            }
            if (this.contract.ongoing) {
                return this.$t('signTip.signOnGoing', { status: this.getIsForeignVersion ?  '処理' : this.signerStatus });
            } else {
                if (this.isEn) {
                    return 'Signed';
                }
                return this.$t('signTip.contractComplete', {
                    alias: this.contractAliasText,
                    key: this.signerStatus,
                });
            }
        },
        // 合同是否还在签署中
        isSigning() {
            return !!this.contract.ongoing;
        },
        signDetail() {
            const hasSigning = Number(this.contract.ongoing) > 0;
            const hasDone = Number(this.contract.finished) > 0;
            let doneStr = '';
            let notDoneStr = '';
            const betweenStr = (hasDone && hasSigning) ? this.$t('signTip.taskStatusBetween') : '';

            if (hasDone) {
                const has = Number(this.contract.finished) > 1 ? this.$t('signTip.personHave') : this.$t('signTip.personHas');
                doneStr = this.$t('signTip.headsTaskDone', {
                    num: this.contract.finished + this.$t('signTip.person'),
                    has: has,
                    done: this.signerStatus,
                });
            }
            if (hasSigning) {
                const not = Number(this.contract.ongoing) > 1 ? this.$t('signTip.personsHavenot') : this.$t('signTip.personHasnot');
                notDoneStr = this.$t('signTip.headsTaskNotDone', {
                    num: this.contract.ongoing + this.$t('signTip.person'),
                    not: not,
                    done: this.signerStatus,
                });
            }
            return doneStr + betweenStr + notDoneStr;
        },
        canDownload() {
            const statusCanDownload = ['6']; // 签署成功时有下载合同按钮
            return statusCanDownload.includes(this.status) && !this.isVisitor;
        },
        iconStatus() {
            const status = ['1', '2', '3', '5', '6', '10', '11', '12', '13'];
            return status.includes(this.status);
        },
        signSuccess() {
            return this.status === '6';
        },
        ifCanViewContractsOnPC() {
            const status = ['2', '4', '5', '6', '11'];
            return this.isPC && this.contractId && status.includes(this.status) && !this.isVisitor;
        },
        ifCanViewContractList() {
            const status = ['2', '6'];
            return this.isPC && this.contractId && status.includes(this.status) && !this.isVisitor;
        },
    },
    methods: {
        handleViewContract() {
            this.status === '6' && this.$sensors.track({
                eventName: 'Ent_ContractSign_BtnClick',
                eventProperty: {
                    page_name: '签署成功页',
                    icon_name: '查看合同',
                    contract_id: this.contractId,
                    contract_status: this.contract.contractStatus,
                },
            });
        },
        showIdentification() {
            const h = this.$createElement;
            const _this = this;
            this.$sensors.track({
                eventName: 'Ent_ContractSignWindow_PopUp',
                eventProperty: {
                    page_name: '签署成功页',
                    icon_name: '主管理员身份确认',
                    contract_id: _this.contractId,
                },
            });
            this.$msgbox({
                title: this.$t('transferAdminDialog.title'),
                message: h('div', [
                    h('i', { class: 'el-icon-warning' }, ''),
                    h('div', { class: 'message-content' }, [
                        h('p', null, this.$tc('transferAdminDialog.content', 1)),
                        h('p', null, this.$tc('transferAdminDialog.content', 2)),
                    ]),
                ]),
                customClass: 'identification-msg',
                showCancelButton: true,
                confirmButtonText: this.$t('transferAdminDialog.confirmAdmin'),
                cancelButtonText: this.$t('transferAdminDialog.transfer'),
                callback: function(action) {
                    _this.$sensors.track({
                        eventName: 'Ent_ContractSignWindow_BtnClick',
                        eventProperty: {
                            page_name: '签署成功',
                            window_name: '主管理员身份确认',
                            icon_name: action === 'cancel' ? '转交' : '我是主管理员',
                            contract_id: _this.contractId,
                        },
                    });
                    if (action !== 'cancel') {
                        _this.$loading();
                        postCheckAdmin()
                            .then(() => {
                                _this.$sensors.track({
                                    eventName: 'Ent_ContractSignWindow_Result',
                                    eventProperty: {
                                        page_name: '签署成功',
                                        window_name: '主管理员身份确认',
                                        is_success: true,
                                        icon_name: '我是主管理员',
                                        contract_id: _this.contractId,
                                        request_url: '/ents/employees/checkAdmin',
                                    },
                                });
                            })
                            .catch((err) => {
                                const errMsg = err.response?.data?.message;
                                const status = err.response?.status;
                                const code = err.response?.data?.code;
                                _this.$sensors.track({
                                    eventName: 'Ent_ContractSignWindow_Result',
                                    eventProperty: {
                                        page_name: '签署成功',
                                        window_name: '主管理员身份确认',
                                        is_success: false,
                                        icon_name: '我是主管理员',
                                        contract_id: _this.contractId,
                                        fail_http_code: status,
                                        fail_error_code: code,
                                        fail_reason: errMsg,
                                        request_url: '/ents/employees/checkAdmin',
                                    },
                                });
                            })
                            .finally(() => {
                                _this.$loading().close();
                                _this.dialogVisible = false;
                            });
                    } else {
                        _this.$sensors.track({
                            eventName: 'Ent_ContractSignWindow_Result',
                            eventProperty: {
                                page_name: '签署成功',
                                window_name: '主管理员身份确认',
                                is_success: true,
                                icon_name: '转交',
                                contract_id: _this.contractId,
                            },
                        });
                        _this.dialogVisible = false;
                        _this.$router.push('/sign-flow/sign/apply-permission/admin?contractId=' + _this.contractId);
                    }
                },
            });
        },
        goHome() {
            if (this.isQyWx) { // 返回企业微信小程序
                uni.reLaunch({
                    url: '/pages/doc/doc',
                });
            }
            if (this.$cookie.get('access_token')) {
                this.$router.push(this.isPC ? '/account-center/home' : '/mobile/doc/list');
            } else { // 未登录走登录
                this.$router.replace('/login');
            }
        },
        async checkLogin(passURL, loginURL) {
            if (this.receiverId && this.receiverId !== 'undefined') {
                return this.$http.get(`/contract-api/ignore/contracts/${this.contractId}/receiver/check?receiverId=${this.receiverId}`)
                    .then(res => {
                        if (+res.data.code === 200) {
                            this.$router.push(passURL);
                        } else if (+res.data.code === 401) {
                            this.$router.push(loginURL);
                        }
                    })
                    .catch(err => {
                        if (err.response && +err.response.status === 401) {
                            this.$router.push(loginURL);
                        }
                    });
            } else {
                this.$router.push(loginURL);
            }
        },
        /**
         * 退出APPSDK
         * @return {[type]} [description]
         */
        exitAppSDK() {
        },
        getSignInfo() {
            return this.$http.get(`${signPath}/ignore/contracts/${this.contractId}/summary-info?type=${this.pageType}`);
        },
        async handleSignInvite() {
            location.href = 'https://www.bestsign.cn/h5/newcomer?utm_source=yaoqing';
        },
        getReceiver() {
            return this.$http.get(`${signPath}/contracts/${this.contractId}/receivers/current-operating?type=${this.pageType}&token=${`${this.urlToken}` === 'null' ? '' : this.urlToken}&alreadyFace=${`${this.$route.query.alreadyFace}` === 'true'}`);
        },
        // SAAS-16307 面向大甲方的相对方的数据产品
        async checkIsDataProduct() {
            try {
                const {
                    contractId,
                    receiverId,
                } = this.$route.query;
                const reg = /^\d+$/;
                if (reg.test(contractId) && reg.test(receiverId)) {
                    const { data } = await this.$http.get(`/octopus-api/micro-finance/contract/${contractId}/${receiverId}/display`);
                    this.isDataProduct = data.value;
                    return Promise.resolve(data.value);
                }
                return Promise.resolve(false);
            } catch (e) {
                return Promise.resolve(false);
            }
        },
        // 获取合同文本别名
        getContractTextAlias() {
            return this.$http.get(`/contract-api/ignore/contracts/${this.contractId}/query-displayed-contract-textAlias`)
                .then((res) => {
                    if (res.data && res.data.displayedTextCode) {
                        this.contractAlias = res.data.displayedTextCode;
                    }
                });
        },
        // 判断是否展示转交主管理员弹窗
        async checkAdminDialogShow() {
            try {
                const { data: { value: checkAdminFlag } } = await getCheckAdmin(this.contractId);
                if (checkAdminFlag) {
                    const { data: allEntList } = await getAllEntInfo();
                    const receiverEntList = (allEntList || []).filter((item) => {
                        return item.entId === this.commonHeaderInfo.currentEntId;
                    });
                    const receiverEntName = receiverEntList[0]?.entName || '';
                    sessionStorage.setItem('applyPermission', JSON.stringify({ receiverEntName: receiverEntName, receiverId: this.receiverId }));
                    this.showIdentification();
                }
            } catch (err) {
                console.log(err);
            }
        },
        // 校验用户是否被加入黑名单，加入黑名单的不展示signTip页的广告
        checkUserIfBlack() {
            checkBlackAd().then((res) => {
                if (res && res.data) {
                    this.ifBlackUser = res.data.value || false;
                    if (!this.ifBlackUser) {
                        this.initAdInfo();
                    }
                }
            });
        },
        getWaitToSignContractNum() {
            return this.$http.get(`/contract-center-bearing/shortcuts?queryCount=true`)
                .then(({ data: { data: result } }) => {
                    const itemNameMap = ['需要我签署', 'Signing required', '署名待ち'];
                    const needMeSignShortcut = result.find(item => itemNameMap.includes(item.name) && item.display);
                    if (needMeSignShortcut) {
                        this.waitToSignContractNum = +needMeSignShortcut.contractsCount;
                        this.needMeSignShortcutId =  needMeSignShortcut.shortcutId;
                    }
                });
        },
        checkSignContractCount() {
            this.$http.post('/contract-search/ent/signed/count').then((res) => {
                this.signContractCount = res.data.data;
                this.showSignCount = true;
                this.checkContractGifts(this.signContractCount);
            });
        },
        checkContractGifts(num) {
            this.$http.post('/ents/do-ent-contract-present', {
                signedContractNum: num,
            }, {
                params: {
                    signedContractNum: num,
                },
            }).then((res) => {
                this.giftContract = res.data.presentNum;
                this.giftContractTime = moment(res.data.endWorkTime).format('YYYY-MM-DD');
            });
        },
        checkNeedShowPresent() {
            return this.$http.get('/ents/check-ent-present-status');
        },
        initAdInfo() {
            const scene = this.status === '6' ? 'signComplete' : (this.status === '2' ? 'signReject' : '');
            // deliveryChannel : 0 全部 1 移动端 2 pc端
            return getAdInfo({ scene, deliveryChannel: 2, contractId: this.contractId }).then((res) => {
                this.adList = res?.data || [];
            });
        },
    },
    async created() {
        this.checkUserIfBlack();
        if (this.fullPath.indexOf('&receiverId=') < 0) {
            this.fullPath += '&receiverId=';
        }
        localStorage.setItem('signTipPath', this.fullPath);
        if (this.status === '6') {
            this.checkIsDataProduct();
            if (this.isEnt) {
                this.checkNeedShowPresent().then((res) => {
                    if (res.data.value) {
                        this.checkSignContractCount();
                    } else {
                        this.signContractCount = 0;
                        this.showSignCount = true;
                    }
                });
            } else {
                this.signContractCount = 0;
                this.showSignCount = true;
            }
            // 没有receiverid的时候，不判断是否需要弹转交主管理员
            if (this.receiverId && this.receiverId !== 'undefined') {
                await this.checkAdminDialogShow();
            }
        }

        this.contractId && this.getContractTextAlias();
        this.ifCanViewContractList && this.getWaitToSignContractNum();
        if (this.status === '5' || this.status === '6' || this.status === '11') {
            // 查询合同签署或审批状态, 后端异步处理请求，等待一会
            this.loading = true;
            return new Promise((resolve) => setTimeout(() => resolve(), 1500))
                .then(() => this.getSignInfo())
                .then(res => {
                    this.contract = res.data;
                    this.status === '6' && this.$sensors.track({
                        eventName: 'Ent_ContractSign_PageView',
                        eventProperty: {
                            page_name: '签署成功页',
                            contract_id: this.contractId,
                            contract_status: this.contract.contractStatus,
                        },
                    });
                })
                .catch(() => {
                })
                .finally(() => this.loading = false);
        }
    },
    mounted() {
        if (this.status === '6') {
            this.enterTime = new Date().getTime();
        }
    },
    beforeDestroy() {
        if (this.status === '6') {
            this.$sensors.track({
                eventName: 'Ent_ContractSign_PageLeave',
                eventProperty: {
                    page_name: '签署成功页',
                    $event_duration: (new Date().getTime() - this.enterTime) / 1000,
                    contract_id: this.contractId,
                    contract_status: this.contract.contractStatus,
                },
            });
        }
    },
};
</script>

<style lang="scss">
.identification-msg{
    width: 460px;
    .el-message-box__content{
        padding-bottom: 0;
        border-bottom:0;
        .el-message-box__message{
        line-height: 20px;
        .el-icon-warning{
            position: absolute;
            font-size: 14px;
            color: #F2A93E;
            line-height: 20px;
        }
        .message-content{
            padding-left: 20px;
            font-size: 14px;
            color: #333;
            p{
                padding-bottom: 10px;
            }
        }
    }
    }
    }
</style>
<style lang="scss" scoped>
    .sign-tip-wrap {
        position: relative;
        background-color: #ffffff;
        height: calc(100% + 100px);
        .tip-header {
            z-index: 999;
            width: 100%;
            height: 50px;
            line-height: 50px;
            text-align: center;
            font-size: 18px;
            color: #fff;
            background-color: #002b45;
        }
        .main {
            margin-top: 50px;
            width: 100%;
            .tip-wrap {
                margin: 0 auto;
                text-align: center;
                overflow: auto;
                .tip-pic {
                    display: block;
                    margin: 0 auto;
                }
                .sign-congratulate {
                    color: #FF5500;
                    font-size: 14px;
                    line-height: 24px;
                    text-align: center;
                    margin-top: 20px;

                    .congratulate-text {
                        font-weight: bold;
                        margin-bottom: 12px;
                        line-height: 1.4;
                    }

                    .carbon-saving {
                        margin-bottom: 12px;
                        font-weight: 500;
                    }

                    .carbon-verification {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border-radius: 20px;
                        margin-left: auto;
                        margin-right: auto;

                        .verification-text {
                            font-size: 12px;
                            color: #666;
                        }

                        .carbon-logo {
                            display: flex;
                            align-items: center;
                            padding-left: 4px;

                            .carbonstop-icon {
                                height: 18px;
                                object-fit: contain;
                            }
                        }
                    }
                }
                .sign-gift {
                    width: 320px;
                    height: 26px;
                    margin: auto;
                    margin-top: 5px;
                    border-radius: 12px;
                    line-height: 26px;
                    background: #e8ffea;
                    font-size: 12px;
                    color: #00b42a;
                }
                .tip-des {
                    margin-top: 20px;
                    font-weight: 400;
                    font-size: 16px;
                    margin-bottom: 8px;
                }
                .acc-del-des{
                    font-weight: normal;
                    font-size: 14px;
                    p{
                        line-height: 25px;
                    }
                }
                .connect-err-des{
                    font-size: 14px;
                    h4{
                        font-weight: bold;
                        font-size: 16px;
                        margin-bottom: 10px;
                    }
                    p{
                        font-weight: normal;
                    }
                }
                .jump {
                    font-size: 14px;
                    color: #8c8c8c ;
                    .tip-mobile {
                        margin-top: 20px;
                    }
                    .download {
                        text-decoration: underline;
                        margin-top: 18px;
                    }
                    a:link {
                        display: inline-block;
                        color: #1396f4;
                        font-weight: bold;
                        margin-bottom: 8px;
                    }
                    a.view-contract-list {
                        display: inline-block;
                        height: 38px;
                        border-radius: 2px;
                        background: #127fd2;
                        color: #FFF;
                        line-height: 38px;
                        padding:0 50px;
                    }
                    &_line {
                        color: #1396f4;
                        margin: 0 5px;
                    }
                }
                .status {
                    p {
                        text-align: left;
                        span {
                            text-align: right;
                            display: inline-block;
                            font-size: 14px;
                            word-wrap: break-word;
                            vertical-align: top;
                            &:first-child {
                                color: #aaa;
                            }
                            &:last-child {
                                color: #000;
                            }
                        }
                    }
                }
                .view-contract {
                    color: #aaa;
                    text-align: left;
                    span {
                        display: inline-block;
                        width: 50%;
                        text-align: right;
                    }
                    .view-contract-tip{
                        width: 100%;
                        text-align: center;
                        color: #000;
                        font-style:italic;
                    }
                }
                .get-free-contract {
                    margin-top: 15px;
                    font-size: 14px;
                    color: #999999;
                    div {
                        color: #127fd2;
                    }
                    img {
                        height: 60px;
                        vertical-align: middle;
                        margin-right: 8px;
                        margin-left: -67px;
                    }
                    .send-contract {
                        cursor: pointer;
                    }
                }
            }
        }
        .btn-wrap {
            margin: 0 auto;
            .contract-btn {
                min-width: 100px;
                padding: 0 5px;
                text-align: center;
                height: 40px;
                border-color: #127fd2;
                color: #127fd2;
                &:hover {
                    background-color: #f3faff;
                }
            }
        }
        .hybrid-tip{
            font-size: 14px;
            padding-top:17px;
            color: #B3B6B5;
        }
        .appSdk-btn {
            position: relative;
            margin: 20px auto 0;
            width: 250px;
            .el-button {
                width: 100%;
                height: 40px;
                line-height: 40px;
                padding: 0;
                margin: 0;
            }
        }
        .pc-footer {
            position: relative;
            margin: 60px auto 0;
            width: 100%;
            height: 95px;
            max-width: 375px;
            background-color: #f2f8fb;
        }
        .logo-wrap {
            margin: 10px auto 0;
            width: 100%;
            max-width: 420px;
            .sign--invite {
                height: 100%;
                width: 100%;
                img {
                    width: 100%;
                }
            }
            a {
                display: block;
                height: 100%;
            }
            .logo {
                position: relative;
                width: 100%;
                margin: 0 auto;
                padding-bottom: 22.1739%;
                top: 50%;
                transform: translateY(-50%);
                background: {
                    image: url("~img/sign-tip-footer.png");
                    size: 100%;
                    repeat: no-repeat;
                };
                .download-btn{
                    max-width: 60px;
                    font-size: 12px;
                    padding: 5px 8px;
                    color: #fff;
                    text-align: center;
                    border-radius: 2px;
                    background: -webkit-linear-gradient(left, rgb(21,181,163), rgb(1,132,118)); /* Safari 5.1 - 6.0 */
                    background: -o-linear-gradient(right, rgb(21,181,163), rgb(1,132,118)); /* Opera 11.1 - 12.0 */
                    background: -moz-linear-gradient(right, rgb(21,181,163), rgb(1,132,118)); /* Firefox 3.6 - 15 */
                    background: linear-gradient(to right, rgb(21,181,163), rgb(1,132,118)); /* 标准的语法（必须放在最后） */
                    position: absolute;
                    top: 50%;
                    right: 15px;
                    transform: translateY(-50%);
                    cursor: pointer;
                }
            }
        }
    }

    @media only screen and (min-width: 319px) and (max-width: 374px)  {
       .sign-tip-wrap .main{
            margin-top: 45px;
       }
    }

    @media only screen and (min-width: 415px) {
       .sign-tip-wrap .main{
            margin-top: 150px;
       }
    }
    .sign-tip-wrap .sign-success-main {
        margin-top: 50px;
    }
    .wx-code {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 100px;
        width: 100%;
        padding-top: 5px;
        background: linear-gradient(to right, #9DE3FC 70%, #D4EDFE);
        .wx-code-contain {
            position: relative;
            width: 350px;
            height: 90px;
            margin: auto;
            img {
                margin-left: 15px;
                width: 90px;
            }
            p {
                position: absolute;
                left: 120px;
                top: 30px;
                font-size: 14px;
                color: #333333;
                max-width: 220px;
                line-height: 20px;
            }
        }
    }

</style>

<style lang="scss">
.el-dialog__wrapper.data-product-dialog {
    .el-dialog__header {
        display: none;
    }
    .el-dialog__body {
        padding: 0;
    }
    .data-product-popup {
        position: relative;
        .close {
            position: absolute;
            top: 0;
            right: 0;
            width: 60px;
            height: 60px;
        }
        .header {
            width: 100%;
        }
        .content{
            padding: 18px 25px;;
            .logo {
                font-size: 0;
                text-align: center;
                padding-right: 40px;
                img {
                    height: 100px;
                    vertical-align: middle;
                    display: inline-block;
                    margin: 0 10px;
                    &.bank {
                        height: 80px;
                    }
                }
                span {
                    font-weight: bolder;
                    vertical-align: middle;
                    font-size: 14px;
                    color: #000;
                }
            }
            p {
                font-size: 16px;
                color: #333333;
                font-weight: 400;
                margin: 10px 0;
                line-height: 1.4;
                &.tip {
                    font-size: 14px;
                    color: #999999;
                }
            }
            button {
                display: block;
                width: 100%;
            }
        }
    }
}
</style>
