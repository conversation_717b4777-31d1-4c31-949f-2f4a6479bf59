<!-- 合同装饰 图片类型 -->
<template>
    <div
        class="image-label"
        :style="{
            width: `${mark.width}px`,
            height: `${mark.height}px`,
            transform: `scale(${scale})`,
            'transform-origin': $i18n.locale === 'ar' ? '100% 0' : '0 0',
        }"
    >
        <el-upload
            class="image-label-upload"
            :action="uploadUrl"
            list-type="picture"
            :show-file-list="false"
            :disabled="disabled"
            :on-success="handleUploadSuccess"
            :before-upload="beforeUpload"
            :on-error="handleUploadError"
        >
            <div class="image-container">
                <!--临时上传的图片-->
                <img v-if="templateUploadUrl" :src="templateUploadUrl" :style="uploadImageStyle" :class="ifFillAllRegion? 'image-container-img__fill': ''" alt="" />
                <!--刷新界面之后依然留存的图片-->
                <img v-else-if="selectedImgUrl && isEffectImgUrl" :src="selectedImgUrl" :style="uploadImageStyle" :class="ifFillAllRegion? 'image-container-img__fill': ''" alt="" />
                <!--上传图标-->
                <img v-else class="upload-icon" src="./images/upload.png" alt="" />
            </div>
        </el-upload>
    </div>
</template>

<script>
export default {
    name: 'ImageLabel',
    props: {
        'mark': {
            type: Object,
            default: () => ({}),
        },
        'labels': {
            type: Array,
            default: () => [],
        },
        'scale': {
            type: Number,
            default: () => 1,
        },
        'selectedImgUrl': {
            type: String,
            default: () => '',
        },
        disabled: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            uploadImageStyle: {
                height: `${this.mark.height}px`,
            },
            // 上传数据之后的临时数据
            tempUploadData: {},
            templateUploadUrl: '',
        };
    },
    computed: {
        // 签署人上传图片 是否是有效图片
        isEffectImgUrl() {
            return this.mark.status === 'CONFIRMED';
        },
        uploadUrl() {
            return `/contract-api/contracts/${this.mark.contractId}/labels/${this.mark.labelId}/fill-picture`;
        },
        ifFillAllRegion() {
            return this.mark?.autoAdjust || false;
        },
    },
    methods: {
        async handleUploadSuccess(res) {
            this.$loading();
            if (res.code === '140001') {
                this.tempUploadData = res.result;
                this.templateUploadUrl = `${this.tempUploadData.value}?access_token=${this.$cookie.get('access_token')}&_t=${new Date().getTime()}`;
                const imgRes = await this.calculateImgSize(this.templateUploadUrl);
                // 根据宽高比自适应显示图片
                this.initImageStyle(imgRes);
                this.$loading().close();
                this.$MessageToast.success('上传图片成功');
                this.$emit('update', res.result, 'uploadImgUrl', res.result.value);
            } else {
                this.$loading().close();
                this.$MessageToast.error(`上传图片失败:${res.message}`);
            }
        },
        beforeUpload(file) {
            const isLt10M = file.size / 1024 / 1024 < 10;
            const acceptType = ['image/png', 'image/jpeg', 'image/jpg', 'image/bmp'];
            const checkFormat =  acceptType.some((type) => {
                return file.type === type;
            });
            // 判断图片格式
            if (!checkFormat) {
                this.$MessageToast.error('图片格式不符合要求');
                return false;
            }
            if (!isLt10M) {
                this.$MessageToast.error('上传图片大小不能超过 10MB!');
                return false;
            }
        },
        handleUploadError() {
            this.$MessageToast.error('图片上传失败');
        },
        calculateImgSize(imageUrl) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = () =>
                    resolve({
                        width: img.width,
                        height: img.height,
                    });
                img.onerror = reject;
                img.src = imageUrl;
            });
        },
        initImageStyle(image) {
            if ((image.width / image.height) > (this.mark.width / this.mark.height)) {
                this.uploadImageStyle = {
                    width: `${this.mark.width}px`,
                };
            } else {
                this.uploadImageStyle = {
                    height: `${this.mark.height}px`,
                };
            }
        },
        async initData() {
            // 只有当selectedImgUrl是有效的图片链接 也就是签署人没上传任何图片的时候不初始化图片样式
            if (this.isEffectImgUrl) {
                const imgRes = await this.calculateImgSize(this.selectedImgUrl);
                this.initImageStyle(imgRes);
            }
        },
    },
    mounted() {
        this.initData();
    },
};
</script>

<style lang="scss">
.image-label{
    background: rgba(191, 221, 255, 0.5);
    border: 1px solid #127fd2;
    .image-label-upload{
        width: 100%;
        height: 100%;
        position: relative;
        overflow: hidden;
        .el-upload{
            width: 100%;
            height: 100%;
        }
        .image-container{
            width: 100%;
            height: 100%;
            text-align: center;
            .upload-icon{
                width: 20px;
            }
            .el-upload__input{
                width: 100%;
                height: 100%;
            }
            img {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }
            img.image-container-img__fill {
                object-fit: fill;
            }
        }
    }
}
</style>
