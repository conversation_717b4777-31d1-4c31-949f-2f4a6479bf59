<!-- 文本标签/数字标签，自适应高度 textarea
 ！！！注意: 此组件中emit update事件直接对mark更新会不起作用，需要找到labels中对应的mark  -->
<template>
    <div class="write-label-con"
        v-if="isShow"
        :style="{
            width: `${mark.width}px`,
            height: `${mark.height}px`,
            transform: `scale(${scale})`,
            'transform-origin': $i18n.locale === 'ar' ? '100% 0' : '0 0',
        }"
        @click="checkSignQualified(mark)"
    >
        <span class="describe-tooltip" v-if="mark.description" :class="['CONFIRMATION_REQUEST_REMARK'].includes(mark.type)? 'request-describe' : ''">{{ mark.description }}</span>
        <textarea
            class="write-label"
            v-model="currentValue"
            rows="1"
            :style="{
                minHeight: `${mark.height}px`,
                fontSize: `${mark.style.fontSize}px`,
                pointerEvents: isViewOnly ? 'none' : 'auto',
                'text-align': textAlign,
            }"
            :id="`text_p${mark.pageNumber}_${mark.labelId}`"
            :disabled="txtDisable || isViewOnly || loading"
            @keyup="handleWriteLabelChange(mark)"
            @focus="getOldValue(mark)"
            @blur="handleWriteLabelBlur($event, mark)"
            droppable="false"
            :placeholder="isViewOnly ? mark.name : fieldPlaceholder"
        >
		</textarea>
    </div>
</template>

<script>
import { isPC } from 'src/common/utils/device.js';
import { numberDecimalReg } from 'utils/reg';
import regRules from 'utils/regs.js';
export default {
    props: {
        'mark': {
            type: Object,
            default: () => {},
        },
        'isHybridCloudContract': {
            type: Boolean,
            default: false,
        },
        'signType': {
            type: String,
            default: 'sign',
        },
        'labels': {
            type: Array,
            default: () => [],
        },
        // 这个看起来也没用了
        'txtDisable': {
            type: Boolean,
            default: false,
        },
        'scale': {
            default: 1,
            type: Number,
        },
    },
    data() {
        return {
            curLabelId: '',
            oldValue: '', // 当前mark上一次失去焦点改动的value
            newValue: '',
            contractId: this.$route.query.contractId,
            currentValue: '',
            loading: false,
        };
    },
    computed: {
        isViewOnly() {
            return ['approval', 'shareView'].includes(this.signType);
        },
        fieldPlaceholder() {
            const { name, required, decimalPlace = 0, type } = this.mark;
            let necessaryText = required ? this.$t('sign.required') : this.$t('sign.optional');
            // 数字类型需要加精度提示
            if (type === 'NUMERIC_VALUE') {
                necessaryText = `${decimalPlace > 0 ? this.$t('sign.decimalLimit', { x: decimalPlace })
                    : this.$t('sign.intLimit')}，${necessaryText}`;
            }
            return `${name || ''}（${necessaryText}）`;
        },
        textAlign() {
            const { alignment } = this.mark.style;
            if (!alignment) {
                return 'left';
            }
            return alignment === 1 ? 'center' : 'right';
        },
        isShow() {
            // 用户选择了符合章的时候，备注不展示
            if (this.mark.type !== 'CONFIRMATION_REQUEST_REMARK') {
                return true;
            }
            const label = this.findLabelByDocIdAndType('CONFIRMATION_REQUEST_SEAL', this.mark.documentId);
            const isShow = !(label && label.answerType === 'AGREE');
            return isShow;
        },
    },
    watch: {
        mark: {
            handler(val) {
                this.initStyle(val);
            },
            deep: true,
            immediate: true,
        },
        'mark.value': {
            handler(val) {
                // 相同业务字段同步
                this.currentValue = val;
            },
            deep: true,
        },
        txtDisable() {
            if (!this.txtDisable) {
                this.$nextTick()
                    .then(() => {
                        const target = document.querySelector(this.markTextId(this.curLabelId));
                        target && target.focus();
                    });
            }
        },
    },
    methods: {
        markTextId(mark) {
            return `#text_p${mark.pageNumber}_${mark.labelId}`;
        },
        // 查询同文档内标签
        findLabelByDocIdAndType(type, documentId) {
            const docLabels = this.labels.filter(label => label.documentId === documentId);
            return docLabels.find(label => label.type === type);
        },

        findLabel(mark) {
            return this.labels.filter(label => {
                return label.labelId === mark.labelId;
            })[0];
        },
        initStyle(mark) {
            if (!mark.style || !mark.style.fontSize) {
                mark.style = {
                    fontSize: 12,
                };
            }
        },
        /**
         * @param  {Object}    mark 标签
         * @desc   标签取得焦点时，自动填写内容
         */
        getOldValue(mark) {
            // 混合云合同不自填充标签
            if (!this.isHybridCloudContract) {
                const target = document.querySelector(this.markTextId(mark));
                this.oldValue = target.value;
            }
        },
        // 可填标签 keydown keyup 事件
        handleWriteLabelChange(mark, adjustHeightOnly) {
            const target = document.querySelector(this.markTextId(mark));
            const labelOperating = this.findLabel(mark);

            if (!target) {
                return;
            }

            const { value, scrollHeight } = target;
            target.style.height = scrollHeight ? `${scrollHeight}px` : 'auto';

            // PC业务字段同步，需要实时同步
            if (isPC() && !adjustHeightOnly) {
                if (mark.type === 'NUMERIC_VALUE' && value === '-') {
                    // CFD-24239直接输入-报错后会自动用旧值覆盖，导致无法输入负数问题，只输入-不做提交
                    return;
                }

                if (!this.checkInputValue(value, mark, true)) {
                    return;
                }

                this.$emit('update', labelOperating, 'value', value, false);
            }
        },
        // 校验新数字类型的数字格式
        checkInputValue(val, mark) {
            let useNewValue = val;
            let userMark = mark || {};

            // 从blur触发的原逻辑是校验this值，从keyup、keydown触发需要校验最新值，兼容两个场景保持不变
            if (!arguments.length) {
                useNewValue = this.newValue;
                userMark = this.mark;
            }

            if (useNewValue === '') {
                return true;
            }
            const { type, decimalPlace = 1, validationType = null } = userMark;
            // 字段为新数字类型
            if (type === 'NUMERIC_VALUE') {
                // 处理最后一位为.情况
                if (useNewValue.charAt(useNewValue.length - 1) === '.') {
                    useNewValue = useNewValue.replace('.', '');
                    this.newValue = useNewValue;
                }
                if (!numberDecimalReg(decimalPlace).test(useNewValue)) {
                    const tipText = decimalPlace > 0 ? this.$t('sign.decimalLimit', { x: decimalPlace }) : this.$t('sign.intLimit');
                    this.$MessageToast.warning(/* '请输入正确的数字'*/`${this.$t('sign.tipRightNumber')}，${tipText}`);
                    this.currentValue = this.oldValue;
                    return false;
                }
                return true;
            } else if (type === 'TEXT' && validationType) {
                let result = true;
                // 校验文本字段格式
                switch (validationType) {
                    case 'CN_ID_FE':
                        result = regRules.IDCardReg.test(useNewValue);
                        break;
                    case 'CN_MOBILE_FE':
                        result = regRules.userPhone.test(useNewValue);
                        break;
                    default:
                        return true;
                }
                if (!result && !mark) { // 只有blur才校验
                    this.$MessageToast.warning(this.$t(validationType === 'CN_ID_FE' ? 'sign.tipRightIdCard' : 'sign.tipRightPhoneNumber'));
                }
                return result;
            }
            return true;
        },
        /**
         * @param  {Object}   e:失去焦点事件，mark:标签
         * @param  {Object}   mark: 操作的标签
         * @desc   对于公有云合同，标签填写内容后失去焦点时会发送请求更新标签信息，
         *         混合云合同不发送请求
         */
        handleWriteLabelBlur(e, mark) {
            // e不存在时，是为同步修改值cfd-2199
            const target = e ? document.querySelector(this.markTextId(mark)) : mark;
            // 必填字段如果没有有效值，置空
            if (target.value.replace(/[\s\r\n]/g, '').length === 0) {
                // this.$MessageToast(this.$t('sign.tipRequired'));
                this.currentValue = '';
                target.value = '';
            }
            this.newValue = target.value;
            // 标签状态，若填写了内容为'CONFIRMED'，否则为'ACTIVE'
            // const labelStatus = this.newValue.length > 0 ? 'CONFIRMED' : 'ACTIVE';
            const labelOperating = this.findLabel(mark);
            /* if (content == '') {
					this.$emit('update', labelOperating, 'status', 'ACTIVE');
					return;
				}*/

            // 业务字段为老版本数字类型
            if (this.mark.type === 'TEXT_NUMERIC') {
                // NUMBER类型，可以输入 数字、小数、负数的人意组合
                const isNumber = target.value.length && /^[-\.\d]*[-\.\d]*[-\.\d]*$/.test(target.value);
                if (target.value === '' || !isNumber) {
                    // 考虑删除为空场景、非数字
                    if (target.value !== '' && !isNumber) {
                        this.$MessageToast(this.$t('sign.tipRightNumber'));
                        target.value = '';
                    }
                    // this.writeLabelBlurCall && this.writeLabelBlurCall();
                    if (!e) {
                        return;
                    }
                    return this.$emit('update', labelOperating, 'value', '', true);
                }
            }

            // 字段为新数字类型，校验数字格式
            if (!this.checkInputValue()) {
                return;
            }

            // 字段为新数字类型
            // if (this.newValue !== '' && this.mark.type === 'NUMERIC_VALUE') {
            //     const { decimalPlace = 1 } = this.mark;
            //     // 处理最后一位为.情况
            //     if (this.newValue.charAt(this.newValue.length - 1) === '.') {
            //         this.newValue = this.newValue.replace('.', '');
            //     }
            //     if (!numberDecimalReg(decimalPlace).test(this.newValue)) {
            //         const tipText = decimalPlace > 0 ? this.$t('sign.decimalLimit', { x: decimalPlace }) : this.$t('sign.intLimit');
            //         this.$MessageToast.warning(/* '请输入正确的数字'*/`${this.$t('sign.tipRightNumber')}，${tipText}`);
            //         this.currentValue = this.oldValue;
            //         return;
            //     }
            // }

            // 只有 当前value更新 并且 input不实在输入状态 而是在blur才更新数据
            if (this.newValue !== this.oldValue) {
                // CFD-8059 由于混1 Jar包不再更新，配合后端修改问题，在文本以及数字的业务字段调用一个H5端的接口，可以在 delta-mobile-frontend 项目找到相应逻辑
                if (this.isHybridCloudContract && this.$hybrid.isAlpha() && ['TEXT', 'TEXT_NUMERIC', 'BIZ_DATE'].includes(mark.type)) {
                    this.loading = true;
                    this.$hybrid.makeRequest({
                        url: `/contract-api/contracts/${this.contractId}/labels/fill-content`,
                        method: 'post',
                        data: {
                            name: mark.name,
                            value: this.newValue,
                            receiverId: mark.receiverId,
                        },
                        contractId: this.contractId,
                    }, 0).finally(() => {
                        this.loading = false;
                    });
                }

                if (!e) {
                    return;
                }
                this.oldValue = this.newValue;
                this.$emit('update', labelOperating, 'value', this.newValue, true);

                // 这个是通用逻辑，公有云或者混3都往云端发送请求了，混2也往云端发送请求，其实判断可以去掉
                // 混合云3.0业务字段上传到云端接口
                // if (!this.isHybridCloudContract || (this.isHybridCloudContract && this.$hybrid.isGamma())) {
                //     this.loading = true;
                //     this.$http.post(`/contract-api/contracts/${this.contractId}/labels/${mark.labelId}/fill-content`, {
                //         value: this.newValue,
                //     }).then(res => {
                //         // this.writeLabelBlurCall && this.writeLabelBlurCall();
                //         if (!e) {
                //             return;
                //         }
                //         this.oldValue = this.newValue;
                //         this.$emit('update', labelOperating, 'status', res.data.status);
                //         this.$emit('update', labelOperating, 'value', this.newValue);
                //     }).catch(() => {
                //         // this.writeLabelBlurCall && this.writeLabelBlurCall();
                //         target.value = '';
                //         this.oldValue = '';
                //         if (!e) {
                //             return;
                //         }
                //         this.$emit('update', labelOperating, 'status', '');
                //     }).finally(() => {
                //         this.loading = false;
                //     });
                // } else {
                //     if (!e) {
                //         return;
                //     }
                //     this.oldValue = this.newValue;
                //     this.$emit('update', labelOperating, 'value', this.newValue);
                //     this.$emit('update', labelOperating, 'status', labelStatus);
                // }
            }
        },
        checkSignQualified(mark) {
            this.$emit('emitOrder', 'check-sign-qualified');
            this.curLabelId = mark.labelId;
        },
    },
    beforeMount() {
        // 初始化，如果以前填充过
        this.oldValue = this.mark.value;
        this.currentValue = this.mark.value;
    },
    mounted() {
        // 初始化时如果标签有内容，自适应已存在内容高度
        if (this.mark.value.length) {
            this.handleWriteLabelChange(this.mark, true);
        }
    },
};
</script>

<style lang="scss">
	.signing, .AppSignDoc, .app-sign-confirm{
		.write-label-con{
			// position: absolute;
			font-size: 0;
			z-index: 2;
			border: 1px solid #127fd2;
        textarea::-webkit-input-placeholder {
            /* WebKit browsers */
            /* placeholder颜色  */
            color: #ddd;
        }
        textarea:-moz-placeholder {
            /* Mozilla Firefox 4 to 18 */
            color: #ddd;
        }
        textarea::-moz-placeholder {
            /* Mozilla Firefox 19+ */
            color: #ddd;
        }
        textarea::-ms-input-placeholder {
            /* Internet Explorer 10+ */
            color: #ddd;
        }
			.write-label{
				width: 100%;
				padding: 3px 6px;
				border: none;
				background-color:rgba(234, 246, 255, 0.5);
				word-wrap: break-word;
				box-sizing: border-box;
				color: #888;
				border-radius: 2px;
				transition: height 0.2s;
				resize: none;
				line-height: 100%;
				outline: 0;

				&:focus{
					background-color: #fff;
					box-shadow: 0 0 2px #47acfc;
				}
			}
			textarea:disabled {
				border: none;
				-webkit-border: none;
			}
			position: relative;
			.describe-tooltip {
				visibility: hidden;
				position: absolute;
				z-index: 9999;
                text-align: left;
				bottom: 22px !important;
				background-color: #FFF7B6 !important;
				opacity: 0.8;
				color: #333333;
				border: 1px solid #999;
				font-size: 12px;
			}
			.request-describe {
				top: -24px;
                height: 20px;
			}
			&:hover .describe-tooltip {
				visibility: visible;
			}
		}
	}
</style>
