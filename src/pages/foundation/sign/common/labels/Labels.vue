<!--业务字段标签组件 pc签署页面使用-->
<template>
    <!-- 外层元素.label-container 的大小 为相对于 已经缩放过的pdf的大小-->
    <div
        class="label-container"
        :type="mark.type"
        :id="`p${pageIndex+1}_${mark.labelId}`"
        :style="{
            left: `${mark.x}px`,
            top: `${mark.y}px`,
            width: `${mark.width}px`,
            height: `${mark.height}px`,
            'pointer-events': dragStatus == 'started' ? 'none' : 'auto',
            zIndex: ['TEXT', 'BIZ_DATE', 'NUMERIC_VALUE', 'SIGNATURE'].includes(mark.type) ? '11': '10',
        }"
        @mousedown="onMarkStart($event, mark,'global')"
    >
        <SealLabel
            v-if="mark.type=='SEAL'"
            :mark="markUnscale"
            :docIndex="docIndex"
            :pageIndex="pageIndex"
            :markIndex="markIndex"
            :receiver="receiver"
            :scale="scale"
            :signerDragAble="signerDragAble"
            :signType="signType"
            :selectedImgUrl="selectedImgUrl"
            :jaEntHasNotAuthenticated="jaEntHasNotAuthenticated"
            @emitOrder="handleEmitOrder"
        >
        </SealLabel>
        <ConfirmSealLabel
            v-if="mark.type=='CONFIRMATION_REQUEST_SEAL'"
            :receiver="receiver"
            :mark="markUnscale"
            :scale="scale"
            :signerDragAble="signerDragAble"
            :signType="signType"
            :selectedImgUrl="selectedImgUrl"
            @emitOrder="handleEmitOrder"
        >
        </ConfirmSealLabel>

        <SinatureLabel
            v-else-if="mark.type=='SIGNATURE'"
            :receiver="receiver"
            :signerDragAble="signerDragAble"
            :signPlaceBySigner="signPlaceBySigner"
            :mark="markUnscale"
            :scale="scale"
            :signType="signType"
            :selectedImgUrl="selectedImgUrl"
            @emitOrder="handleEmitOrder"
        >
        </SinatureLabel>

        <WriteLabel
            v-else-if="['TEXT','TEXT_NUMERIC','CONFIRMATION_REQUEST_REMARK', 'NUMERIC_VALUE'].includes(mark.type)"
            :mark="markUnscale"
            :scale="scale"
            :isHybridCloudContract="isHybridCloudContract"
            :signType="signType"
            :labels="labels"
            @update="handleUpdateLabel"
            @emitOrder="handleEmitOrder"
        >
        </WriteLabel>

        <DateLabel
            v-else-if="mark.type=='DATE'"
            :mark="markUnscale"
            :scale="scale"
            :signerDragAble="signerDragAble"
        >
        </DateLabel>

        <BizDateLabel
            v-else-if="mark.type=='BIZ_DATE'"
            :mark="markUnscale"
            :scale="scale"
            :isHybridCloudContract="isHybridCloudContract"
            :labels="labels"
            :signType="signType"
            @update="handleUpdateLabel"
            @emitOrder="handleEmitOrder"
        >
        </BizDateLabel>
        <DateTimeLabel
            v-else-if="mark.type === 'DATE_TIME'"
            :mark="markUnscale"
            :scale="scale"
            :labels="labels"
            @update="handleUpdateLabel"
        >
        </DateTimeLabel>
        <BoxLabel
            v-else-if="['SINGLE_BOX','MULTIPLE_BOX'].includes(mark.type)"
            :mark="markUnscale"
            :scale="scale"
            @update="handleUpdateLabel"
            :labels="labels"
        >
        </BoxLabel>
        <DropDownLabel
            v-else-if="['COMBO_BOX',].includes(mark.type)"
            :mark="markUnscale"
            :scale="scale"
            :labels="labels"
            @update="handleUpdateLabel"
        >
        </DropDownLabel>
        <ImageLabel
            v-else-if="['PICTURE'].includes(mark.type)"
            :mark="markUnscale"
            :scale="scale"
            :labels="labels"
            :selectedImgUrl="selectedImgUrl"
            @update="handleUpdateLabel"
        >
        </ImageLabel>

        <!-- 删除标签的小图标 新老版本的签署人指定签署位置为显示-->
        <span
            v-if="isMarkCanDrag || isSignPlaceCanDrag"
            class="label-delte"
            :style="{
                left: `${mark.width - 12}px`,
                top: `-15px`
            }"
            @click="handleDeleteLabel"
        >
            <i class="el-icon-ssq-wrong-filling"></i>
        </span>
        <!-- 拖动标签 -->
        <p
            v-if="isSignPlaceCanDrag"
            class="label-drag"
            :style="{
                left: `0px`,
                top: `${mark.height + 5}px`,
                width:`${mark.width}px`
            }"
            @mousedown="onMarkStart($event, mark,'local')"
        >
            <em class="el-icon-ssq-tuodongyangshi drag-icon"></em>
            <span>{{ $t('signPC.signPlaceBySigner.dragPlace') }}</span>
            <em class="el-icon-ssq-tuodongyangshi drag-icon"></em>
        </p>
        <el-tooltip
            effect="dark"
            content="将重叠的顶层置底"
            placement="right"
        >
            <span class="zIndexIcon" v-if="['PICTURE','SEAL'].includes(mark.type) && !isSignPlaceCanDrag" @click="changeZIndex($event)">
                <i class="el-icon-ssq-zhiyudiceng"></i>
            </span>
        </el-tooltip>

    </div>
</template>
<script>
import { markInfo } from 'src/pages/foundation/sign/common/info/info.js';
import { markCoordinateTransform, coordinateReversal } from 'src/common/utils/fn';
import WriteLabel from './WriteLabel.vue';
import DateLabel from './DateLabel.vue';
import SealLabel from './SealLabel.vue';
import SinatureLabel from './SinatureLabel.vue';
import BizDateLabel from './BizDateLabel.vue';
import BoxLabel from './BoxLabel.vue';
import ConfirmSealLabel from './ConfirmSealLabel.vue';
import DropDownLabel from './DropDownLabel.vue';
import ImageLabel from './ImageLabel.vue';
import DateTimeLabel from './DateTimeLabel.vue';

export default {
    components: {
        WriteLabel,
        DateLabel,
        SealLabel,
        SinatureLabel,
        BizDateLabel,
        BoxLabel,
        ConfirmSealLabel,
        DropDownLabel,
        ImageLabel,
        DateTimeLabel,
    },
    props: [
        // eslint-disable-next-line vue/require-prop-types
        'signPlaceBySigner', 'signerDragLabelTypeList', 'docList', 'page', 'initialMark', 'receiver', 'labels', 'isHybridCloudContract', 'signerDragAble', 'markList', 'docIndex', 'pageIndex', 'markIndex', 'dragStatus', 'scale', 'jaEntHasNotAuthenticated',
    ],
    data() {
        return {
            contractId: this.$route.query.contractId,
            signType: this.$route.query.type || 'sign',
            dragMarkList: ['SEAL', 'SIGNATURE', 'DATE'],
            isDraggingMark: false,
            mark: [],
            move: {
                docI: 0,
                pageI: 0,
                status: 'pedding',
            },
            SPACE_HEIGHT: 20,
            markUnscale: {}, // 标签相对 原始大小pdf 的数据，为了pdf高清渲染，mark自己这一层做缩放
            isDeleting: false,
        };
    },
    computed: {
        selectedImgUrl() {
            return `${this.mark.value}?access_token=${this.$cookie.get('access_token')}`;
        },
        isMarkCanDrag() {
            return (this.signerDragAble && (this.dragMarkList.includes(this.mark.type)));
        },
        isSignPlaceCanDrag() {
            return this.signPlaceBySigner && this.signerDragLabelTypeList.includes(this.mark.type);
        },
    },
    watch: {
        initialMark: {
            handler(value) {
                const numberCoordinate = coordinateReversal(value, this.page.width, this.page.height);
                this.mark = {
                    ...value,
                    ...numberCoordinate,
                    x: value.x < 1 ? numberCoordinate.x : value.x * this.scale, // 兼容老的模板发送的合同，为绝对定位的情况
                    y: value.y < 1 ? numberCoordinate.y : value.y * this.scale,
                    width: value.width <= 1 ? numberCoordinate.width : value.width * this.scale,
                    height: value.height <= 1 ? numberCoordinate.height : value.height * this.scale,
                    style: {
                        ...value.style,
                        fontSize: (value.style && value.style.fontSize || 18) * this.scale,
                    },
                };
                this.markUnscale = {
                    ...value,
                    ...coordinateReversal(value, this.page.width / this.scale, this.page.height / this.scale),
                };
            },
            immediate: true,
            deep: true,
        },
    },
    methods: {
        handleEmitOrder(order, type, uploadSealFileId) {
            this.$emit(order, type, uploadSealFileId);
        },
        handleUpdateLabel(obj, attr, val, isBlur = false) {
            this.$emit('update', obj, attr, val, isBlur);
        },
        postMarks(labelAry = [], opts) {
            let url = `${signPath}/contracts/${this.contractId}/labels/signer-create-and-modify/`;
            // 新版本签署人指定签署位置
            if (this.signPlaceBySigner) {
                url = `${signPath}/contracts/${this.contractId}/signer/labels/signer-create-and-modify/`;
            }
            return this.$http.post(
                url,
                labelAry,
                opts,
            );
        },
        saveMark(data) {
            return this.postMarks(data, {
                noToast: 1,
            });
        },
        // 计算出新的标签所在的文档和页面坐标
        // 注意：目前的方案是 当标签右上角位于两页中间时，将标签重新定位在下面一页的顶部。
        computeIndex(y) {
            const SPACE_HEIGHT = this.SPACE_HEIGHT;
            const page = this.docList[this.docIndex].page;
            let pageI = 0;

            // CFD-25052：界面页高度不等时，页码计算问题，acc总高的计算有误
            for (let i = 0, acc = 0; i < page.length; i++) {
                const preY = acc;
                const nextY = acc + page[i].height + SPACE_HEIGHT;
                if (y > preY && y < nextY) {
                    pageI = i;
                    break;
                } else if (y >= nextY - SPACE_HEIGHT) {
                    pageI = i + 1;
                }
                acc = nextY;
            }
            return pageI > -1 ? pageI : 0;
        },
        // 文档内拖标签/印章时，拖到边界的处理
        buildBlackHole(page, mark, pX, pY) {
            // pX,pY为标签在页面内的坐标
            const maxY = page.height - markInfo(mark.type).height * this.scale;  // 允许的标签最大y坐标值
            const maxX = page.width - markInfo(mark.type).width * this.scale; // 允许的标签最大x坐标值
            // 往左 拉出文档
            if (pX < 0) {
                mark.x = 0;
            }
            // 往右 拉出文档
            if (pX > maxX) {
                mark.x  = maxX;
            }
            // 往上 拉出文档
            if (pY < 0) {
                mark.y = 0;
            }
            // 往下 拉出文档
            if (pY > maxY) {
                mark.y  = maxY;
            }

            return {
                markX: mark.x,
                markY: mark.y,
            };
        },
        // 在标签上发生鼠标点击事件
        onMarkStart(e, mark, handleType = 'global') {
            // 新老版本的签署人指定签署位置
            if (!(this.isMarkCanDrag || handleType === 'local' && this.isSignPlaceCanDrag)) {
                return;
            }
            // 开始拖拽
            this.isDraggingMark = true;
            e.preventDefault();
            e.stopPropagation();

            document.addEventListener('mousemove', this.onMarkMove);
            document.addEventListener('mouseup', this.onMarkEnd);

            this.startTime = new Date().getTime();
            const $clt = e.target.closest('.label-container');
            if (!$clt) {
                return;
            }

            // this.move.eX = e.x;
            // this.move.eY = e.y;
            this.move.eX = e.clientX;
            this.move.eY = e.clientY;
            this.move.markX = mark.x;
            this.move.markY = mark.y;
        },
        onMarkMove(e) {
            // 如果不是在拖拽标签 鼠标move监听事件不执行
            if (!this.isDraggingMark) {
                return;
            }
            e.preventDefault(); // 取消img默认拖拽事件
            // this.mark.x = this.move.markX + ( e.x - this.move.eX ) / this.zoom;
            // this.mark.y = this.move.markY + ( e.y - this.move.eY ) / this.zoom;
            // this.mark.x = this.move.markX + ( e.clientX - this.move.eX ) / this.scale;
            // this.mark.y = this.move.markY + ( e.clientY - this.move.eY ) / this.scale;

            this.mark.x = this.move.markX + (e.clientX - this.move.eX);
            this.mark.y = this.move.markY + (e.clientY - this.move.eY);
        },
        // 放开鼠标，拖拽事件结束 自定义签署位置功能
        onMarkEnd(e) {
            // fix CFD-5237 windonw点击删除也会触发move
            if (this.isDeleting || e.target.className === 'el-icon-ssq-wrong-filling') {
                this.isDraggingMark = false;
                return;
            }
            this.isDraggingMark = false;
            const mark = this.mark;

            this.endTime = new Date().getTime();
            this.isClick = this.endTime - this.startTime < 200;

            if (this.isClick) {
                return;
            }
            // 现在的交互 不会跨文档，只会跨页
            const currentDoc = this.docList[this.docIndex];
            const pageIndex = mark.pageNumber - 1;

            // 标签在整个合同区域的纵坐标，左上角为原点
            const y = currentDoc.page.reduce((acc, page, i) => {
                if (pageIndex > i) {
                    acc += ((page.height || 0) + this.SPACE_HEIGHT);
                }
                return acc;
            }, 0) + mark.y;
            // 拖拽之后新的页码
            const pageI  = this.computeIndex(y);
            // 如果跨页需要修正坐标y [x, y是mark相对于页面的坐标]
            if (pageI !== pageIndex) {
                const top = currentDoc.page.slice(0, pageI).reduce((sum, page) => {
                    return sum + page.height + this.SPACE_HEIGHT;
                }, 0);
                mark.y = y - top;
            }
            this.buildBlackHole(currentDoc.page[pageI], this.mark, mark.x, mark.y);

            document.removeEventListener('mousemove', this.onMarkMove);
            document.removeEventListener('mouseup', this.onMarkEnd);
            let markWidth = markInfo(mark.type).width;
            let markHeight = markInfo(mark.type).height;
            // 先固定日期为 x年x月x日的形式宽高
            if (mark.type.toUpperCase() === 'DATE') {
                markWidth = 140;
                markHeight = 20;
            }
            let postMark = {
                labelId: mark.labelId,
                receiverId: mark.receiverId,
                contractId: this.contractId,
                documentId: currentDoc.documentId,
                pageNumber: pageI + 1,
                type: mark.type, // SIGNATURE(1), DATE(2), SEAL(3),
                x: mark.x,
                y: mark.y,
                width: markWidth * this.scale,
                height: markHeight * this.scale,
            };
            // 标签坐标转换为以页面左下角为原点基于页面的百分比,标签宽高转换为与页面宽高的百分比
            const percentCoordinate = markCoordinateTransform(postMark, this.page.width, this.page.height);

            postMark = { ...postMark, ...percentCoordinate };
            // 新老版本的签署人指定位置共用
            if (this.isMarkCanDrag || this.isSignPlaceCanDrag) {
                this.saveMark([postMark])
                    .then(res => {
                        // 更新标签位置信息
                        this.$emit('relocation-label', {
                            docI: this.docIndex,
                            pageI,
                            mark: res.data[0],
                        });
                    })
                    .catch(() => {});
            }
        },
        // 自定义签署位置时允许删除标签
        handleDeleteLabel() {
            this.isDeleting = true;
            let url = `/contract-api/contracts/${this.contractId}/labels/${this.mark.labelId}`;
            // 如果是新版本的签署人指定位置
            if (this.signPlaceBySigner) {
                url = `/contract-api/contracts/${this.contractId}/signer/labels/${this.mark.labelId}`;
            }
            this.$http.delete(url)
                .then(() => {
                    this.$emit('del-label');
                }).finally(() => {
                    this.isDeleting = false;
                });
        },
        changeZIndex(e) {
            const labelContainer = e.target.closest('.label-container');
            labelContainer.style.zIndex = 0;
        },
    },
};
</script>
<style lang="scss">
	.contract-page-content .label-container{
		position: absolute;
        z-index: 10;
        .zIndexIcon{
            position: absolute;
            top: 100%;
            right: 0;
            width: 40px;
            height: 40px;
            background: #E7F3FB;
            border-radius: 2px;
            margin-top: 4px;
            cursor: pointer;
            i{
                font-size: 24px;
                color: #127FD2;
                line-height: 40px;
            }
        }
        .label-delte{
            position: absolute;
            cursor: pointer;

            i{
                color: #515151;
                font-size: 20px;
            }
        }
      .label-drag{
          position: absolute;
          cursor: pointer;
          display: flex;
          justify-content: space-between;
          height: 25px;
          font-size: 12px;
          color: #fff;
          background-color: #1687DC;
          text-align: center;
          line-height: 25px;
          opacity: 0.8;
          padding: 0 10px;
          box-sizing: border-box;
          .drag-icon{
              line-height: 25px;
          }
      }
		.label{
			// position: absolute;

			.confirmed{
				position: absolute;
				left: 0;
				top: -24px;
				width: 100%;
                min-width: 130px;
				height: 24px;
				line-height: 24px;
				background: #1274CC;
				color: #fff;
				text-align: center;
				clear: both;
                display: flex;

				.item{
					position: relative;
                    flex: 1;
					display: inline-block;
                    &:last-child:first-child{
                        width: 100%;
                        &:after{
                            display: none;
                        }
                    }
					&:last-child:after{
						content: '';
						position: absolute;
						left: 0;
						top: 6px;
						display: block;
						width: 1px;
						height: 13px;
						background: #fff;
					}
				}

				i{
					font-size: 13px;
                    line-height: 26px;
					vertical-align: middle;
				}

				span{
					font-size: 11px;
					vertical-align: middle;
				}
			}

			.label-header-detail{
				position: absolute;
				left: 0;
				top: -24px;
				padding: 0 5px;
				min-width: 100%;
				min-height: 24px;
				border: 1px solid #959595;
				line-height: 24px;
				box-sizing: border-box;
				background: #fff7b6;
				color: #333;
				font-size: 12px;
				cursor: pointer;

				.detail-content{
					display: inline-block;
					width: calc(100% - 28px);
                    white-space: normal;
				}

				.applicant-seal{
					display: inline-block;
				}
                .applicant-name {
                    padding-left: 10px;
                }

				.cancel{
					font-size: 12px;
					color: #1687dc;
					cursor: pointer;
				}
			}
		}
	}
</style>
