<template>
    <div class="datetime-label"
        :style="{
            width: `${mark.width}px`,
            height: `${mark.height}px`,
            transform: `scale(${scale})`,
            'transform-origin': $i18n.locale === 'ar' ? '100% 0' : '0 0',
        }"
    >
        {{ mark.dateFieldFormat }}
        <el-time-picker
            ref="timePicker"
            v-model="currentValue"
            :placeholder="mark.name"
            :value-format="mark.dateFieldFormat"
            :format="mark.dateFieldFormat"
            :disabled="disabled"
            :editable="false"
            :style="{
                fontSize: `${mark.style && mark.style.fontSize || 14}px`,
            }"
            @change="handleTimeChange"
        >
        </el-time-picker>
    </div>
</template>

<script>
import moment from 'dayjs';
import { debounce } from 'utils/fn';

export default {
    props: {
        mark: {
            type: Object,
            default: () => ({}),
        },
        labels: {
            type: Array,
            default: () => [],
        },
        scale: {
            type: Number,
            default: 1,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            currentValue: '',
            contractId: this.$route.query.contractId,
        };
    },
    watch: {
        'mark.value': {
            handler(val) {
                this.currentValue = val ? Number(val) : '';
            },
            deep: true,
        },
    },
    methods: {
        handleTimeChange: debounce(function(value) {
            const objectLabel = this.labels.filter(label => {
                return label.labelId === this.mark.labelId;
            })[0];

            let timeResult = this.currentValue;
            if (value) {
                // 时刻默认年月日1970-1-2，（1月2日避免时区导致的负数）
                timeResult = moment(new Date(`1970-01-02 ${value}`)).valueOf();
            }

            this.$emit('update', objectLabel, 'value', timeResult);
        }, 200),
    },
    beforeMount() {
        console.log('mark = ', this.mark);
        // 初始化，如果以前填充过
        if (this.mark.value) {
            this.currentValue = Number(this.mark.value);
        }
    },
};
</script>

<style lang="scss">
.datetime-label .el-date-editor--time {
    width: 100%;
    height: 100%;
    .el-input__inner {
        height: 100%;
        padding: 3px 6px;
        color: #888;
    }
    .el-input__icon {
        font-size: 12px;
        width: 20px;
        transform: scale(0.7);
        right: -2px;
    }
}
</style>
