<!-- 业务字段，日期类型的文本标签 -->
<template>
    <div
        class="bizDateLabelCon"
        @click="checkSignQualified(mark)"
        :style="{
            width: `${mark.width}px`,
            height: `${mark.height}px`,
            transform: `scale(${scale})`,
            'transform-origin': $i18n.locale === 'ar' ? '100% 0' : '0 0',
        }"
    >
        <FormatDatePicker
            :value-str="timeStamp"
            :disabled="['approval', 'shareView'].includes(signType) || disabled || loading"
            :date-field-format="mark.dateFieldFormat || 'yyyy-MM-dd'"
            :show-icon="false"
            :render-style="{
                height: `${mark.height}px`,
                fontSize: `${mark.style.fontSize}px`,
                textAlign: textAlignClass,
            }"
            @updateValue="handleChange"
        ></FormatDatePicker>
        <input :id="`text_${mark.labelId}`" class="timeStampInput" :value="timeStamp" />
    </div>
</template>

<script>
import moment from 'dayjs';
import FormatDatePicker from './FormatDatePicker';
export default {
    components: {
        FormatDatePicker,
    },
    // eslint-disable-next-line vue/require-prop-types
    props: ['mark', 'labels', 'signType', 'disabled', 'scale', 'isHybridCloudContract'],
    data() {
        return {
            contractId: this.$route.query.contractId,
            timeStamp: 0,
            oldValue: '',
            loading: false,
        };
    },
    computed: {
        textAlignClass() {
            const { alignment } = this.mark.style;
            if (!alignment) {
                return 'left';
            }
            return alignment === 1 ? 'center' : 'right';
        },
    },
    watch: {
        'mark.value': {
            handler(val) {
                this.timeStamp = Date.parse(val);
            },
            deep: true,
        },
    },
    methods: {
        // 选择了日期
        handleChange(v) {
            this.timeStamp = v;
            const objectLabel = this.labels.filter(label => {
                return label.labelId === this.mark.labelId;
            })[0];
            // fix CFD-2199残留问题，如果日志输入过，刷新页面，自动调用接口报错。原因是timedateVal为undefined
            if (this.oldValue === this.timeStamp) {
                return;
            }
            this.loading = true;
            const dateVal =  this.timeStamp ? moment(this.timeStamp).format('YYYY-MM-DD') : '';
            // 混1逻辑可以先不动，下面单个字段请求没必要
            ((this.isHybridCloudContract && this.$hybrid.isAlpha()) ? (this.$hybrid.makeRequest({
                url: `/contract-api/contracts/${this.contractId}/labels/fill-content`,
                method: 'post',
                data: {
                    name: this.mark.name,
                    value: dateVal,
                    receiverId: this.mark.receiverId,
                },
                contractId: this.contractId,
            }, 0)) : (new Promise(resolve => resolve()))).then(() => {
                this.oldValue = this.timeStamp;
                this.$emit('update', objectLabel, 'value', dateVal);
            }).finally(() => {
                this.loading = false;
            });
        },
        checkSignQualified() {
            this.$emit('emitOrder', 'check-sign-qualified');
        },
    },
    beforeMount() {
        if (this.mark.value) {
            this.timeStamp = Date.parse(this.mark.value);
            this.oldValue = this.timeStamp;
        }
    },
};
</script>

<style lang="scss">
    .signing, .AppSignDoc, .app-sign-confirm{
        .bizDateLabelCon{
            position: absolute;
            font-size: 0;
            z-index: 2;
            border: 1px solid #127fd2;
            padding-bottom: 1px;

            .el-date-editor{
                width: inherit;
                &.center {
                    input {
                        text-align: center;
                    }
                }
                &.right {
                    input {
                        text-align: right;
                    }
                }

                .el-input__icon {
                    width: 30px;
                    margin-right: 3px;
                }
                .el-icon-date {
                    &::before {
                        content: '';
                    }
                }
                .el-icon-close  {
                    text-align: right;
                     &::before {
                        content: "\E60A";
                        font-size: 12px;

                        color: #97a8be;
                    }
                }
                .el-input__inner{
                    padding: 0 5px;
                    border: none;
                    height: inherit;
                    line-height: inherit;
                    font-size: inherit;
                    background-color:rgba(234, 246, 255, 0.5);
                    color: #757575;
                }
            }

            .timeStampInput{
                display: none;
            }
        }
    }
</style>
