<template>
    <div class="combo-label"
        :style="{
            width: `${mark.width}px`,
            height: `${mark.height}px`,
            transform: `scale(${scale})`,
            'transform-origin': $i18n.locale === 'ar' ? '100% 0' : '0 0',
        }"
    >
        <el-select v-model="currentValue"
            placeholder=""
            @change="handleChange"
            clearable
            popper-class="combo-label-select-popper-class"
            filterable
            :disabled="disabled"
            :style="{
                fontSize: `${mark.style && mark.style.fontSize || 14}px`,
            }"
        >
            <el-option
                v-for="(item, index) in mark.buttons"
                :key="index"
                :label="item.buttonValue"
                :value="item.buttonValue"
            >
            </el-option>
        </el-select>
    </div>
</template>

<script>
export default {
    props: {
        mark: {
            type: Object,
            default: () => ({}),
        },
        labels: {
            type: Array,
            default: () => [],
        },
        scale: {
            type: Number,
            default: 1,
        },
        disabled: {
            type: <PERSON>olean,
            default: false,
        },
    },
    data() {
        return {
            currentValue: '',
            contractId: this.$route.query.contractId,
        };
    },
    inject: ['checkSignQualified'],
    watch: {
        mark(value) {
            this.currentValue = value.value;
        },
    },
    methods: {
        handleChange(value) {
            this.currentValue = value;
            const objectLabel = this.labels.filter(label => {
                return label.labelId === this.mark.labelId;
            })[0];

            this.$emit('update', objectLabel, 'value', this.currentValue);
        },
    },
    beforeMount() {
        // 初始化，如果以前填充过
        this.currentValue = this.mark.value;
    },
};
</script>

<style lang="scss">
  .combo-label {
        .el-select {
            .el-input {
                font-size: inherit !important;
                .el-input__icon {
					width: 14px;
                }
                .el-input__inner {
                    padding-right: 14px; // 避免icon影响内部文案展示
                    padding-left: 6px;
                }
            }
        }
  }
    // .combo-label {
    //     .el-select {
    //         height: 100%;
    //         width: 100%;
    //         position: absolute;
    //         top: 0;
    //         right: 0;
    //         left: 0;
    //         bottom: 0;
    //         z-index: 11;
    //         .el-input {
    //             height: 100%;
    //             width: 100%;
    //             .el-input__inner {
    //                 height: 100%;
    //                 width: 100%;
    //                 padding: 3px 15px 3px 5px;
    //                 color: #fff;
    //                 background-color: transparent;
    //                 font-size: 0;
    //                 border-color: #127fd2;
    //             }
    //         }
    //         .el-input__icon {
    //             width: 20px;
    //         }
    //     }
    //     .combo-label-selected {
    //         color:#333;
    //         padding: 3px 6px;
    //         text-align: left;
    //         line-height: 1;
    //     }
    // }
    // .combo-label-select-popper-class {
    //     max-width: 45%;
    //     .el-select-dropdown__item {
    //         height: auto;
    //         overflow: visible;
    //         white-space: normal;
    //     }
    // }
</style>
