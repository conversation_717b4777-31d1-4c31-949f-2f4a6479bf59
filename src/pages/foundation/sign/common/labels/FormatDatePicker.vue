<template>
    <div class="format-date-com clear" :style="renderStyle">
        <el-date-picker
            v-model="currentValue"
            :editable="false"
            :disabled="disabled"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            :clearable="true"
            :style="renderStyle"
            class="label-item date-label-picker"
            size="mini"
            type="date"
            @change="changeValue"
            @focus="$emit('focus')"
        >
        </el-date-picker>
        <div class="label-mask label-item">
            <i v-if="showIcon" class="el-icon-ssq-riqi1 cur-pointer"></i>
            {{ displayDateStr }}
        </div>
    </div>
</template>

<script>
import dayjs from 'dayjs';

export default {
    name: 'FormatDatePicker',
    props: {
        placeholder: {
            type: String,
            default: '',
        },
        renderStyle: {
            type: Object,
            default: () => ({}),
        },
        valueStr: {
            type: Number,
            default: 0,
        },
        dateFieldFormat: {
            type: String,
            default: '',
        },
        showIcon: {
            type: Boolean,
            default: false,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            currentValue: '',
        };
    },
    computed: {
        displayDateStr() {
            return this.currentValue ? dayjs(this.currentValue).format(this.dateFieldFormat.toUpperCase()) : '';
        },
    },
    watch: {
        valueStr: {
            handler(v) {
                this.currentValue = v ? dayjs(v) : '';
            },
            immediate: true,
        },
    },
    methods: {
        changeValue(val) {
            this.$emit('updateValue', val ? Date.parse(this.currentValue) : '');
        },
    },
};
</script>

<style lang="scss">
.format-date-com {
    position: relative;
    .date-label-picker.label-item {
        position: absolute;
        background-color: transparent;

        .el-input__inner, .el-input__prefix {
            opacity: 0;
        }
        .el-input__prefix {
            padding-top: 20px;
        }
    }
    .label-mask {
    }
}
</style>
