<template>
    <div class="box-label label"
        @click.capture="beforeChoose"
        :style="{
            width: `${mark.width}px`,
            height: `${mark.height}px`,
            transform: `scale(${scale})`,
            'transform-origin': $i18n.locale === 'ar' ? '100% 0' : '0 0',
        }"
    >
        <div v-for="(item,index) in mark.buttons"
            :key="index"
            :style="{
                left: `${item.buttonX}px`,
                top: `${item.buttonY}px`,
                position: 'absolute'
            }"
            @click="choose(item.buttonValue)"
            class="box-label-button"
        >
            <img :width="markButtonStyle.width" :height="markButtonStyle.height" :src="mark.type === 'SINGLE_BOX'?radioImg:checkboxImg" />
            <img :width="markButtonStyle.width" :height="markButtonStyle.height" class="box-label-button-select" v-if="currentValue.split(',').includes(item.buttonValue)" src="~img/label/select.png">
        </div>
        <input :id="`text_${mark.labelId}`" class="box-label-Input" :value="currentValue" />

    </div>
</template>

<script>
import radioImg from 'img/label/signRadio.png';
import checkboxImg from 'img/label/signCheckbox.png';
import { markInfo } from 'src/pages/foundation/sign/common/info/info.js';

export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['mark', 'labels', 'scale', 'disabled'],
    data() {
        return {
            radioImg,
            checkboxImg,
            markButtonStyle: markInfo(this.mark.type).button,
            currentValue: '',
            contractId: this.$route.query.contractId,
        };
    },
    inject: ['checkSignQualified'],
    watch: {
        mark(value) {
            this.currentValue = value.value;
        },
    },
    methods: {
        choose(value) {
            if (this.disabled) {
                return;
            }
            if (this.mark.type === 'SINGLE_BOX') { // 单选框
                this.currentValue = this.currentValue === value ? '' : value;
            } else { // 复选框
                const selectList = this.currentValue === '' ? [] : this.currentValue.split(',');
                const index = selectList.indexOf(value);
                if (index > -1) {
                    selectList.splice(index, 1);
                } else {
                    selectList.push(value);
                }
                this.currentValue = selectList.join(',');
            }
            const objectLabel = this.labels.filter(label => {
                return label.labelId === this.mark.labelId;
            })[0];

            this.$emit('update', objectLabel, 'value', this.currentValue);
        },
        beforeChoose(e) { // 先判断是否符合签约条件
            const canChoose = this.checkSignQualified();
            if (!canChoose) {
                e.stopPropagation();
            }
        },
    },
    beforeMount() {
        // 初始化，如果以前填充过
        this.oldValue = this.mark.value;
        this.currentValue = this.mark.value;
    },
};
</script>

<style lang="scss" scoped>
    .box-label{
        background: rgba(191, 221, 255, 0.5);
        position:relative;
        border: 1px solid #127fd2;
        .box-label-button{
            &:hover{
                 cursor: pointer;
            }
            .box-label-button-select{
                position: absolute;
                left: 0;
                top:0;
            }
        }
        .box-label-Input{
            display: none;
        }
    }
</style>
