<template>
    <!-- 签名 -->
    <div
        class="signature-label label"
        :style="{
            width: `${mark.width}px`,
            height: `${mark.height}px`,
            transform: `scale(${scale})`,
            'transform-origin': $i18n.locale === 'ar' ? '100% 0' : '0 0',
        }"
    >
        <template v-if="!signerDragAble">
            <!-- 审批展示：由xxx签名... -->
            <template v-if="['approval', 'shareView'].includes(signType)">
                <div class="label-header-detail" v-autoH:label>
                    <span>
                        {{ $t('sign.signByPc', {
                            name: mark.receiverName || mark.receiverAccount || ''
                        }) }}
                    </span>
                </div>
            </template>

            <!-- 转签,FDA签名暂时不支持转签 -->
            <template v-else-if="mark.status == 'ACTIVE' && receiver.userType == 'ENTERPRISE' &&!mark.supportFDA">
                <div
                    class="label-header-detail"
                    v-autoH:label
                    v-show="isHeadShow"
                    @mouseover="hasOverHead=1"
                    @mouseleave="onMouseLeave('hasOverHead')"
                >
                    {{ $t('sign.clickToSignature') }}
                    <span v-if="mark.parentReceiverId == '0'">
                        {{ $t('sign.or') }}
                        <span
                            @click="handleEmitOrder('click-transfer')"
                            class="cancel"
                        >{{ $t('sign.transferToOtherToSign') }}</span>
                    </span>
                </div>
            </template>

            <!-- 由xxx签名... -->
            <template v-else-if="mark.status == 'INACTIVE'">
                <div class="label-header-detail" v-autoH:label>
                    <span class="detail-content">{{ $t('sign.signatureBy', {x: mark.receiverName || mark.receiverAccount || ''}) }}</span>
                    <span
                        class="cancel"
                        @click="handleEmitOrder('click-cancel')"
                    >{{ $t('sign.cancel') }}</span>
                    <span class="clear"></span>
                </div>
            </template>

            <!-- 切换 & 手写 tab -->
            <template v-else-if="mark.status == 'CONFIRMED'">
                <div
                    class="confirmed"
                    v-show="isHeadShow"
                    @mouseover="hasOverHead=1"
                    @mouseleave="onMouseLeave('hasOverHead')"
                >
                    <div v-if="!receiver.forceHandWrite" class="item" @click="handleEmitOrder('click-switch')">
                        <i class="el-icon-ssq-qiehuan"></i>
                        <span>{{ $t('sign.switch') }}</span>
                    </div>
                    <div v-if="!receiver.handWriteNotAllowed" class="item" @click="handleEmitOrder('click-handWrite')">
                        <i class="el-icon-ssq-edit"></i>
                        <span>{{ $t('sign.handwrite') }}</span>
                    </div>
                </div>
            </template>

        </template>

        <!-- 已有签名图片 -->
        <div
            class="signature-box"
            v-if="isLabelImageShow"
            @click="handleClickLabel"
            @mouseover="hasOverLabel=1"
            @mouseleave="onMouseLeave('hasOverLabel')"
            :style="{
                width: `${mark.width - 2}px`,
                height: `${mark.height - 2}px`,
            }"
        >
            <img
                :width="mark.width"
                :height="mark.height"
                :src="isLabelImageShow ? selectedImgUrl : ''"
                alt=""
            />
        </div>
        <!-- 没有签名时的样式 -->
        <div
            class="signature-box unfill"
            :style="{
                width: `${mark.width - 2}px`,
                height: `${mark.height - 2}px`,
            }"
            v-else
            @click="handleClickLabel"
        >
            <!-- <div v-if="getIsForeignVersion" class="signature-ja" :style="`transform: rotate(${mark.style.tiltDegrees}deg)`">{{ $t('prepare.sealArea') }}</div> -->
            <!-- <template v-else> -->
            <i
                :style="fdaInfoStyle"
                :class="{
                    zh: 'el-icon-ssq-qianzi',
                    en: 'el-icon-ssq-qianziEn',
                    ja: 'el-icon-ssq-qianziJP',
                    ar: 'el-icon-ssq-signature-uae'
                }[$i18n.locale]"
            ></i>
            <!-- </template> -->
        </div>
        <div class="signature-fda" v-if="mark.supportFDA" :style="fdaInfoStyle">
            <span class="signer-line">Electronically signed by:
                <span v-if="mark.signerFDAName" class="signer-name">{{ mark.signerFDAName }}</span>
                <span v-if="mark.signerFDAName" class="singer-operate" @click="changeSignerFDAName">{{ $t('signPC.FDASign.signerEdit') }}</span>
                <span v-else class="singer-operate" @click="changeSignerFDAName">{{ $t('signPC.FDASign.signerAdd') }}</span>
            </span>
            <p class="reason-line">Reason:
                <el-select
                    v-if="!isEnter"
                    ref="reasonSelect"
                    @click.native="beforeReason(true)"
                    @change="submitValue"
                    @input.native="filterData"
                    v-model="mark.signerFDAReason"
                    :placeholder="$t('signPC.FDASign.plsSelect')"
                    class="signature-fda-reason"
                    :disabled="disableLabel"
                >
                    <el-option v-for="(item,index) in mark.reasons" :label="item" :value="item" :key="index"></el-option>
                </el-select>
                <input v-else
                    @click="beforeReason()"
                    @input="handleInputValChange"
                    v-model="mark.signerFDAReason"
                    :placeholder="$t('signPC.FDASign.plsInput')"
                    :disabled="disableLabel"
                />
            </p>
            <p><span class="fr common-font-color user-enter" @click="isEnter=true" v-if="mark.allowInput && !isEnter">{{ $t('signPC.FDASign.customInput') }}</span></p>
            <p>Date: {{ $t('signPC.FDASign.date') }}</p>
        </div>
    </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { fillFDASignature } from 'src/api/sign.js';
import { labelInfo } from 'src/common/utils/labelStyle.js';
import _get from 'lodash/get';
export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['mark', 'signType', 'receiver', 'selectedImgUrl', 'signerDragAble', 'signPlaceBySigner', 'scale'],
    data() {
        return {
            hasOverLabel: 0,
            hasOverHead: 0,
            isEn: this.$i18n.locale === 'en', // 当前页面是中文
            isEnter: false,
            updateValDebounce: null,
        };
    },
    inject: ['checkSignQualified', 'updateSignatureData'],
    computed: {
        ...mapGetters(['getIsForeignVersion']),
        isLabelImageShow() {
            return this.mark.value && (this.mark.status === 'CONFIRMED' || this.mark.parentReceiverId > 0);
        },
        isHeadShow() {
            return this.hasOverLabel || this.hasOverHead;
        },
        disableLabel() {
            // 审批或者新老版本的签署人指定位置是禁用label
            return ['approval', 'shareView'].includes(this.signType) ||  this.signerDragAble || this.signPlaceBySigner;
        },
        fdaInfoStyle() { // FDA部分随签名的大小进行缩放
            const { width } = labelInfo('SIGNATURE');
            const scale = this.mark.width / width;
            return {
                'transform': `scale(${scale})`,
                'transform-origin': 'left top',
            };
        },
    },
    methods: {
        changeSignerFDAName() {
            this.$alert(this.$t('signPC.FDASign.editTip'), this.$t('signPC.FDASign.inputNameTip'), {
                showInput: true,
                inputPlaceholder: this.$t('signPC.FDASign.inputName'),
            }).then(res => {
                const newMark = {
                    ...this.mark,
                    signerFDAName: res.value,
                    updateType: 'NAME',
                };
                fillFDASignature(newMark).then(({ data }) => {
                    if (!this.updateValDebounce) {
                        this.updateSignatureData(data, 'signature', false, false);
                    }
                });
            }).catch(e => {
                console.log(e);
            });
        },
        onMouseLeave(key) {
            setTimeout(() => {
                this[key] = 0;
            }, 100);
        },
        handleClickLabel() {
            if (this.signType !== 'approval' && !this.signerDragAble) {
                this.handleEmitOrder('click-label');
            }
        },
        handleEmitOrder(order) {
            this.$emit('emitOrder', order);
        },
        handleInputValChange() {
            if (this.updateValDebounce) {
                this.updateValDebounce = clearTimeout(this.updateValDebounce);
            }
            this.debounceSubmitTextValue();
        },
        debounceSubmitTextValue() {
            this.updateValDebounce = setTimeout(() => {
                this.updateValDebounce = clearTimeout(this.updateValDebounce); // 更新完后清除timeout
                this.submitValue();
            }, 400);
        },
        beforeReason(showSelect) {
            const reasonSelect = this.$refs.reasonSelect;
            if (!this.checkSignQualified()) {
                showSelect && (reasonSelect.visible = false);
                return;
            }
            showSelect && (reasonSelect.visible = true);
        },
        filterData() {
            const reason = this.$refs.reasonSelect.$data.selectedLabel;
            if (reason.length > 20) {
                this.$refs.reasonSelect.$data.selectedLabel = reason.substr(0, 20);
            }
        },
        submitValue() {
            fillFDASignature(this.mark).then(({ data }) => {
                if (!this.updateValDebounce) {
                    this.updateSignatureData(data, 'signature', false, false);
                }
            });
        },
    },
    mounted() {
        // 没有设置理由的时候为true
        this.isEnter = _get(this.mark, 'reasons.length', 0) <= 0;
    },
};
</script>

<style lang="scss">
	.contract-page-content{
		.signature-label{
			font-family: "SimSun", "STSong";
			font-size: 18px;
			white-space: nowrap;
			cursor: pointer;
			font-weight: normal;

			.signature-box{
				position: relative;
                width: 132px;
                height: 69px;
				border: 1px dashed #127fd2;
				border-radius: 2px;

				&.unfill{
					border-style: solid;
					background: rgba(201, 231, 255, 0.75);
				}
                &.unfill-en {
                    background: url("~img/signature-en.png");
                    background-size: cover;
                }

				img{
					display: block;
				}

				i.el-icon-ssq-qianzi, i.el-icon-ssq-qianziEn, i.el-icon-ssq-qianziJP, i.el-icon-ssq-signature-uae{
					position: absolute;
                    left: 0px;
                    top: 0px;
                    font-weight: normal;
                    font-size: 70px;
					z-index: 1;
				}
			}
            .signature-fda{
                position: absolute;
                top: 0;
                left: 100%;
                margin-left: 1px;
                background: rgba(201, 231, 255, 0.75);
                font-size:12px;
                padding-left: 5px;
                line-height: 14px;
                text-align: left;
                width: 201px;
                height: 71px;
                .user-enter{
                    display: inline-block;
                    padding-right:10px;
                    line-height: 18px;
                }
                p{
                    height: 14px;
                }
                .signer-line{
                    white-space:normal;
                    height: 28px;
                    overflow: hidden;
                    text-overflow: -o-ellipsis-lastline;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    line-clamp: 2;
                    -webkit-box-orient: vertical;
                    word-break: break-word;
                    .signer-name{
                        display: inline-block;
                        position: relative;
                        top: 1px;
                        min-width: 50px;
                        height: 14px;
                        background-color: #fff;
                        box-sizing: border-box;
                    }
                    .singer-operate{
                        color: #127FD2;
                        margin-left: 3px;
                    }
                }
                .signature-fda-reason{
                    position: relative;
                    input{
                        width: 140px;
                        line-height: 15px;
                        height: 15px;
                        font-size: 12px;
                        padding: 0 10px;
                    }
                }
            }

		}
	}
</style>
