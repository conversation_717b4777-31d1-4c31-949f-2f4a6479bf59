<template>
    <div
        class="date-label label"
        :style="{
            width: `${mark.width}px`,
            height: `${mark.height}px`,
            transform: `scale(${scale})`,
            'transform-origin': $i18n.locale === 'ar' ? '100% 0' : '0 0',
            minHeight: `${mark.height}px`,
            fontSize: `${mark.style && mark.style.fontSize}px`,
            fontFamily:`${mark.dateFieldFontType || 'SIMSUN'}, 'STSong'`,
        }"
    >
        {{ markShowText }}
    </div>
</template>

<script>
import dayjs from 'dayjs';
export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['mark', 'scale'],
    data() {
        return {

        };
    },
    computed: {
        markShowText() {
            const { value } = this.mark;
            const dateFieldFormat = this.mark.dateFieldFormat || 'yyyy-MM-dd';
            let temp;
            if (value) {
                if (value.includes('年')) {
                    // 将“XX年XX月XX日”格式转换成“XX-XX-XX"
                    temp = value.split('年')[0] + '-' + value.split('年')[1].split('月')[0] + '-' + value.split('月')[1].split('日')[0];
                } else {
                    temp = value.replace(/-/g,  '/');
                }
                return dayjs(new Date(temp)).format(dateFieldFormat.toUpperCase());
            }
            return this.$t('sign.signDate');
        },
    },
};
</script>

<style lang="scss">
.contract-page-content{
    .date-label{
        font-family: "SimSun", "STSong";
        font-weight: bold;
        font-size: 18px;
        white-space: nowrap;
        background: rgba(191, 221, 255, 0.5);
        text-align: left;
    }
}
</style>
