<template>
    <!-- 印章 -->
    <div
        class="confirm-seal-label label"
        :style="{
            width: `${mark.width}px`,
            height: `${mark.height}px`,
            transform: `scale(${scale})`,
            'transform-origin': $i18n.locale === 'ar' ? '100% 0' : '0 0',
        }"
    >
        <template v-if="!signerDragAble">
            <!-- 审批展示：由xxx盖xxx章 -->
            <template v-if="['approval', 'shareView'].includes(signType)">
                <div class="label-header-detail" v-autoH:label>
                    <span>{{ $t('sign.sealBySomeone', { name: mark.receiverName || mark.receiverAccount || ''}) }}</span>
                </div>
            </template>

            <!-- 由xxx盖xxx章 -->
            <template v-else-if="mark.status == 'INACTIVE'">
                <div class="label-header-detail" v-autoH:label>
                    <span class="detail-content">{{ $t('sign.needSomeoneToSignature', {x: mark.receiverName || mark.receiverAccount || '', y: mark.name || ''}) }}</span>
                    <span
                        class="cancel"
                        @click="handleEmitOrder('click-cancel')"
                    >{{ $t('sign.cancel') }}</span>
                </div>
            </template>

            <!-- 被申请人打开页面：需盖xxx章  申请人：xxx -->
            <template v-else-if="mark.status === 'ACTIVE' && mark.parentReceiverId !== '0'">
                <div class="label-header-detail" v-autoH:label>
                    <span class="applicant-seal">{{ $t('sign.needToSet') }}{{ mark.name || '' }}</span>
                    <span v-if="mark.parentReceiverName" class="applicant-name">{{ $t('sign.approver') }}{{ mark.parentReceiverName }}</span>
                </div>
            </template>
            <template v-else>
                <div class="label-header-detail" v-autoH:label>
                    <span>{{ $t('sign.chooseFrom2') }}</span>
                </div>
            </template>
        </template>
        <div class="confirm-seal-label-con">
            <template v-if="isLabelImageShow">
                <div v-for="(item,index) in mark.buttons"
                    :key="index"
                    :style="{
                        left: `${item.buttonX}px`,
                        top: `${item.buttonY}px`,
                        position: 'absolute',
                        width: `${markButtonStyle.width}px`,
                        height: `${markButtonStyle.height}px`,
                    }"
                    :class="{'refill': !(mark.answerType === item.buttonValue), 'seal-box': true}"
                    @click="handleClickLabel(item.buttonValue)"
                >
                    <img
                        v-if="mark.answerType === item.buttonValue"
                        :width="markButtonStyle.width"
                        :height="markButtonStyle.height"
                        :src="selectedImgUrl"
                        alt=""
                    />
                    <template v-else>
                        <i :class="iconType(item.buttonValue)"></i>
                        <el-button type="primary" v-if="!isFromApply">{{ $t('sign.reselect') }}</el-button>
                    </template>
                </div>
            </template>
            <template v-else>
                <div v-for="(item,index) in mark.buttons"
                    :key="index"
                    :style="{
                        left: `${item.buttonX}px`,
                        top: `${item.buttonY}px`,
                        position: 'absolute',
                        width: `${markButtonStyle.width}px`,
                        height: `${markButtonStyle.height}px`,
                    }"
                    @click="handleClickLabel(item.buttonValue)"
                    class="unfill seal-box"
                >
                    <i :class="iconType(item.buttonValue)"></i>
                </div>
            </template>
        </div>
    </div>
</template>

<script>
import { markInfo } from 'src/pages/foundation/sign/common/info/info.js';
import { mapGetters } from 'vuex';
export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['mark', 'signType', 'selectedImgUrl', 'signerDragAble', 'scale', 'receiver'],
    data() {
        return {
            hasOverLabel: 0,
            hasOverHead: 0,
            markButtonStyle: markInfo(this.mark.type).button,
        };
    },
    computed: {
        ...mapGetters(['getIsForeignVersion']),
        isLabelImageShow() {
            return this.mark.status === 'CONFIRMED' || this.mark.parentReceiverId > 0;
        },
        isHeadShow() {
            return this.hasOverLabel || this.hasOverHead;
        },
        // 被申请人
        isFromApply() {
            return this.receiver.applyRole === 'EXECUTOR';
        },
    },
    methods: {
        onMouseLeave(key) {
            setTimeout(() => {
                this[key] = 0;
            }, 100);
        },
        handleEmitOrder(order, type) {
            this.$emit('emitOrder', order, type);
        },
        handleClickLabel(type) {
            if (!this.isFromApply && !['approval', 'shareView'].includes(this.signType) && !this.signerDragAble) {
                this.handleEmitOrder('click-label', type);
            }
        },
        iconType(type) {
            if (this.$i18n.locale === 'ja') {
                return type === 'AGREE' ? 'el-icon-ssq-riwenfuhegaizhangchu' : 'el-icon-ssq-riwenbufugaizhangchu';
            }
            return type === 'AGREE' ? 'el-icon-ssq-fuhegaizhangchu' : 'el-icon-ssq-bufugaizhangchu';
        },
    },
};
</script>

<style lang="scss">
.contract-page-content{
    .confirm-seal-label{
        font-family: "SimSun","STSong";
        font-size: 18px;
        white-space: nowrap;
        cursor: pointer;
        font-weight: normal;
        position:relative;
        .confirm-seal-label-con{
            border: 1px dashed #127fd2;
            box-sizing: border-box;
            width: 100%;
            height: 100%;
        }

        .seal-box{
            position: relative;
            border: 1px solid #127fd2;
            border-radius: 2px;
            box-sizing: border-box;
            .el-button{
                position: absolute;
                right: 64px;
                top: 84px;
                z-index: 1;
            }

            &.unfill{
                border:0;
                // border-style: solid;
                background: rgba(201, 231, 255, 0.75);

                &:after{
                    content: '';
                    position: absolute;
                    right: 10px;
                    bottom: 17px;
                    display: block;
                    width: 168px;
                    height: 168px;
                    border-radius: 50%;
                    background: rgba(255, 255, 255 , 0.3);
                }
            }
            &.refill{
                 border:0;
                background: rgba(238, 238, 238, 0.75);
                &:after{
                    content: '';
                    position: absolute;
                    right: 10px;
                    bottom: 17px;
                    display: block;
                    width: 168px;
                    height: 168px;
                    border-radius: 50%;
                    background: rgba(255, 255, 255 , 0.3);
                }
                i{
                    opacity: 0.5;
                }
            }

            img{
                display: block;
            }

            i[class^=el-icon-ssq]{
                position: absolute;
                right: 30px;
                top: 68px;
                font-weight: normal;
                font-size: 70px;
                z-index: 1;
            }
        }

    }
}
</style>
