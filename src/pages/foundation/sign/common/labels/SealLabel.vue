<template>
    <!-- 印章 -->
    <div
        class="seal-label label"
        :style="{
            width: `${mark.width}px`,
            height: `${mark.height}px`,
            transform: `scale(${scale})`,
            'transform-origin': $i18n.locale === 'ar' ? '100% 0' : '0 0',
        }"
    >
        <template v-if="!signerDragAble">
            <!-- 审批展示：由xxx盖xxx章 -->
            <template v-if="['approval', 'shareView'].includes(signType)">
                <div class="label-header-detail" v-autoH:label>
                    <span>{{ $t('sign.sealBySomeone', { name: mark.receiverName || mark.receiverAccount || ''}) }}</span>
                </div>
            </template>

            <!-- 由xxx盖xxx章 -->
            <template v-else-if="mark.status == 'INACTIVE'">
                <div class="label-header-detail" v-autoH:label>
                    <span class="detail-content">{{ $t('sign.needSomeoneToSignature', {x: mark.receiverName || mark.receiverAccount || '', y: mark.name || ''}) }}</span>
                    <span
                        class="cancel"
                        @click="handleEmitOrder('click-cancel')"
                    >{{ $t('sign.cancel') }}</span>
                </div>
            </template>

            <!-- 被申请人打开页面：需盖xxx章  申请人：xxx -->
            <template v-else-if="mark.status == 'ACTIVE' && mark.parentReceiverId!='0'">
                <div class="label-header-detail" v-autoH:label>
                    <span class="applicant-seal">{{ $t('sign.needToSet') }}{{ mark.name || '' }}</span>
                    <span v-if="mark.parentReceiverName" class="applicant-name">{{ $t('sign.approver') }}{{ mark.parentReceiverName }}</span>
                </div>
            </template>

            <!-- 已被盖，专用章不允许切换 -->
            <template v-else-if="mark.status == 'CONFIRMED' && mark.parentReceiverId=='0' && !specialSealImgUrl">
                <div
                    :class="{
                        'confirmed':true,
                        'is-ja-version':getIsForeignVersion
                    }"
                    v-show="isHeadShow"
                    @click="handleEmitOrder('click-switch')"
                    @mouseover="hasOverHead=1"
                    @mouseleave="onMouseLeave('hasOverHead')"
                >
                    <i class="el-icon-ssq-qiehuan"></i>
                    <span>{{ $t('sign.switch') }}</span>
                </div>
            </template>

        </template>
        <!-- 非跨平台合同未实名的日本签约企业未盖印章样式 -->
        <!-- <el-upload
            v-if="jaEntHasNotAuthenticated"
            ref="uploadSealInput"
            :show-file-list="false"
            :action="sealUploadUrl"
            accept="image/png,image/jpg,image/jpeg,image/bmp"
            :before-upload="beforeUpload"
        >
            <div
                v-if="isLabelImageShow"
                class="seal-box"
                :style="{
                    width: `${mark.width}px`,
                    height: `${mark.height}px`
                }"
            >
                <img
                    :width="mark.width"
                    :height="mark.height"
                    :src="isSpecialSealShow ? specialSealImgUrl : selectedImgUrl"
                    alt=""
                />
            </div>
            <div
                v-else
                class="seal-box unfill"
                :style="{
                    width: `${mark.width}px`,
                    height: `${mark.height}px`
                }"
            >
                <div class="seal-content-ja">{{ $t('prepare.sealArea') }}</div>
            </div>
        </el-upload> -->
        <!-- 已盖印章/专用章图片 -->
        <div
            class="seal-box"
            v-if="isLabelImageShow"
            @click="handleClickLabel"
            @mouseover="hasOverLabel=1"
            @mouseleave="onMouseLeave('hasOverLabel')"
            :style="{
                width: `${mark.width}px`,
                height: `${mark.height}px`
            }"
        >
            <img
                :width="mark.width"
                :height="mark.height"
                :src="isSpecialSealShow ? specialSealImgUrl : selectedImgUrl"
                alt=""
            />
        </div>
        <!-- 未盖印章样式 -->
        <div
            @click="handleClickLabel"
            class="seal-box unfill"
            v-else
            :style="{
                width: `${mark.width}px`,
                height: `${mark.height}px`
            }"
        >
            <div v-if="getIsForeignVersion" class="seal-content-ja">{{ $t('prepare.sealArea') }}</div>
            <div v-else class="seal-content-container" :style="containerBgStyle">
                <i :class="{'el-icon-ssq-seal': isEn, 'el-icon-ssq-gaizhang1': isZh, 'el-icon-ssq-gaizhangJP': isJa, 'el-icon-ssq-icon-test1': isRu}" :style="{fontSize: `${100 * labelScale}px`}"></i>
            </div>
        </div>
        <el-upload
            ref="uploadSealInput1"
            :show-file-list="false"
            :action="sealUploadUrl"
            accept="image/png,image/jpg,image/jpeg,image/bmp"
            :before-upload="beforeUpload"
        ></el-upload>
    </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { labelInfo } from 'src/common/utils/labelStyle.js';
export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['mark', 'signType', 'selectedImgUrl', 'signerDragAble', 'scale', 'jaEntHasNotAuthenticated', 'receiver', 'docIndex', 'pageIndex', 'markIndex'],
    data() {
        return {
            hasOverLabel: 0,
            hasOverHead: 0,

            dragging: false,
            startTime: '',
            endTime: '',
            sealUploadUrl: '/ents/seals/temp',
            addSealDialog: {
                seal: '',
                fileId: '',
            },
        };
    },
    inject: ['showAddSealDialog'],
    computed: {
        ...mapGetters(['getIsForeignVersion']),
        isSpecialSealShow() {
            return ['approval', 'shareView'].includes(this.signType) && this.mark.status !== 'CONFIRMED' && this.specialSealImgUrl;
        },
        specialSealImgUrl() {
            if (this.mark.templateSpecialSealPreview && this.mark.templateSpecialSealPreview.useTemplateSpecialSeal) {
                return this.mark.templateSpecialSealPreview.previewUrl;
            }
            return '';
        },
        isLabelImageShow() {
            return this.mark.status === 'CONFIRMED' || this.mark.parentReceiverId > 0 || this.isSpecialSealShow;
            // return this.mark.showSignaturePic;
        },
        isHeadShow() {
            return this.hasOverLabel || this.hasOverHead;
        },
        isZh() {
            return this.$i18n.locale === 'zh';
        },
        isEn() {
            return this.$i18n.locale === 'en';
        },
        isJa() {
            return this.$i18n.locale === 'ja';
        },
        isRu() {
            return this.$i18n.locale === 'ru';
        },
        labelScale() {
            return this.mark.width / labelInfo('SEAL').width;
        },
        containerBgStyle() {
            return {
                width: `${168 * this.labelScale}px`,
                height: `${168 * this.labelScale}px`,
                right: `${10 * this.labelScale}px`,
                bottom: `${17 * this.labelScale}px`,
            };
        },
        notAuthEnt() { // 未注册企业，且不需要实名
            return this.mark.parentReceiverId === '0' && ['CONFIRMED', 'ACTIVE'].includes(this.mark.status) && !this.receiver.hasAuthenticated && !this.receiver.requireIdentityAssurance;
        },
    },
    methods: {
        // 公章上传校验
        checkSealFormat(file) {
            const acceptType = ['image/png', 'image/jpeg', 'image/jpg', 'image/bmp'];
            const checkFormat = acceptType.some((type) => {
                return file.type === type;
            });
            // 判断图片格式
            if (!checkFormat) {
                this.$MessageToast.error(this.$t('uploadFile.imgUnqualified'));
            }
            return checkFormat;
        },
        clearFiles() {
            this.$refs.uploadSealInput.clearFiles();
        },
        beforeUpload(file) {
            if (!this.checkSealFormat(file)) {
                this.clearFiles();
                return false;
            }
            const formData = new FormData();
            formData.append('file', file);
            formData.append('entId', this.$store.state.commonHeaderInfo.currentEntId);

            this.$http.post(this.sealUploadUrl, formData, {
                headers: {
                    'content-type': 'multipart/form-data',
                },
            }).then((res) => {
                const { value: fileId } = res.data;
                this.handleEmitOrder('click-label', '', fileId);
            });
            return false;
        },
        onMouseLeave(key) {
            setTimeout(() => {
                this[key] = 0;
            }, 100);
        },
        handleEmitOrder(order, type = '', uploadSealFileId = '') {
            this.$emit('emitOrder', order, type, uploadSealFileId);
        },
        async handleClickLabel() {
            // 已经盖了专用章的印章不允许再次点击，专用章不能切换
            if (this.specialSealImgUrl && this.mark.status === 'CONFIRMED') {
                return;
            }
            // 对于未注册企业，且不需要实名，走直接上传印章图片
            if (this.notAuthEnt) {
                this.$loading();
                await this.getSeal();
                this.showAddSealDialog({
                    seal: this.addSealDialog.seal,
                    updateEL: this.$refs.uploadSealInput1.$el.querySelector('.el-upload__input'),
                    mark: this.mark,
                    docIndex: this.docIndex,
                    pageIndex: this.pageIndex,
                    markIndex: this.markIndex,
                    action: 'click',
                    params: ['', this.addSealDialog.fileId],
                });
                // this.addSealDialog.show = true;
                // this.addSealHandle();
                return;
            }
            if (!['approval', 'shareView'].includes(this.signType) && !this.signerDragAble) {
                this.handleEmitOrder('click-label');
            }
        },
        getSeal() { // 获取历史章
            return this.$http.get(`/ents/seals/last-temp-seal?entId=${this.$store.state.commonHeaderInfo.currentEntId}`).then(({ data }) => {
                this.addSealDialog.fileId = data.fileId;
                this.addSealDialog.seal = data.file ? `data:image/jpg;base64,${data.file}` : '';
            }).finally(() => {
                this.$loading().close();
            });
        },
    },
};
</script>

<style lang="scss">
	.contract-page-content{
		.seal-label{
			font-family: "SimSun", "STSong";
			font-size: 18px;
			white-space: nowrap;
			cursor: pointer;
			font-weight: normal;

			.seal-box{
				position: relative;
				border: 1px dashed #127fd2;
				border-radius: 2px;
				box-sizing: border-box;
				background: rgba(247, 248, 249, 0.75);

				&.unfill{
					border-style: solid;
					background: rgba(201, 231, 255, 0.75);

					//&:after{
					//	content: '';
					//	position: absolute;
					//	right: 10px;
					//	bottom: 17px;
					//	display: block;
					//	width: 168px;
					//	height: 168px;
					//	border-radius: 50%;
					//	background: rgba(255, 255, 255 , 0.3);
					//}
				}

				img{
					display: block;
				}

                .seal-content-container {
                    position: absolute;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 50%;
                    background: rgba(255, 255, 255 , 0.3);
                }
				i[class^=el-icon-ssq]{
					//position: absolute;
					//right: 43px;
					//top: 53px;
					font-weight: normal;
					z-index: 1;
				}
			}
            .confirmed.is-ja-version{
                width: 73% !important;
            }
		}
	}
</style>
