<!--业务字段标签组件 pc签署页面使用-->
<template>
    <!-- 外层元素.label-container 的大小 为相对于 已经缩放过的pdf的大小-->
    <div
        class="label-container"
        :type="mark.type"
        :id="`p${pageIndex+1}_${mark.labelId}`"
        :style="{
            left: `${mark.x}px`,
            top: `${mark.y}px`,
            width: `${mark.width}px`,
            height: `${mark.height}px`,
            zIndex: ['TEXT', 'BIZ_DATE', 'NUMERIC_VALUE', 'SIGNATURE'].includes(mark.type) ? '11': '10',
        }"
    >
        <SealLabel
            v-if="mark.type=='SEAL'"
            :mark="markUnscale"
            :scale="scale"
            :signType="signType"
            :selectedImgUrl="selectedImgUrl"
        >
        </SealLabel>
        <ConfirmSealLabel
            v-if="mark.type=='CONFIRMATION_REQUEST_SEAL'"
            :receiver="receiver"
            :mark="markUnscale"
            :scale="scale"
            :signType="signType"
            :selectedImgUrl="selectedImgUrl"
        >
        </ConfirmSealLabel>

        <SinatureLabel
            v-else-if="mark.type=='SIGNATURE'"
            :receiver="receiver"
            :mark="markUnscale"
            :scale="scale"
            :signType="signType"
            :selectedImgUrl="selectedImgUrl"
        >
        </SinatureLabel>

        <WriteLabel
            v-else-if="['TEXT','TEXT_NUMERIC','CONFIRMATION_REQUEST_REMARK', 'NUMERIC_VALUE'].includes(mark.type)"
            :mark="markUnscale"
            :scale="scale"
            :isHybridCloudContract="isHybridCloudContract"
            :signType="signType"
            :txt-disable="true"
        >
        </WriteLabel>

        <DateLabel
            v-else-if="mark.type=='DATE'"
            :mark="markUnscale"
            :scale="scale"
        >
        </DateLabel>

        <BizDateLabel
            v-else-if="mark.type=='BIZ_DATE'"
            :mark="markUnscale"
            :scale="scale"
            :isHybridCloudContract="isHybridCloudContract"
            :signType="signType"
        >
        </BizDateLabel>
        <DateTimeLabel
            v-else-if="mark.type === 'DATE_TIME'"
            :mark="markUnscale"
            :scale="scale"
            :disabled="true"
        >
        </DateTimeLabel>
        <BoxLabel
            v-else-if="['SINGLE_BOX','MULTIPLE_BOX'].includes(mark.type)"
            :mark="markUnscale"
            :scale="scale"
            :disabled="true"
        >
        </BoxLabel>
        <DropDownLabel
            v-else-if="['COMBO_BOX',].includes(mark.type)"
            :mark="markUnscale"
            :scale="scale"
            :disabled="true"
        >
        </DropDownLabel>
        <ImageLabel
            v-else-if="['PICTURE'].includes(mark.type)"
            :mark="markUnscale"
            :scale="scale"
            :disabled="true"
            :selectedImgUrl="selectedImgUrl"
        >
        </ImageLabel>
        <el-tooltip
            effect="dark"
            content="将重叠的顶层置底"
            placement="right"
        >
            <span class="zIndexIcon" v-if="['PICTURE','SEAL'].includes(mark.type)" @click="changeZIndex($event)">
                <i class="el-icon-ssq-zhiyudiceng"></i>
            </span>
        </el-tooltip>

    </div>
</template>
<script>
import { coordinateReversal } from 'src/common/utils/fn';
import WriteLabel from './WriteLabel.vue';
import DateLabel from './DateLabel.vue';
import SealLabel from './SealLabel.vue';
import SinatureLabel from './SinatureLabel.vue';
import BizDateLabel from './BizDateLabel.vue';
import BoxLabel from './BoxLabel.vue';
import ConfirmSealLabel from './ConfirmSealLabel.vue';
import DropDownLabel from './DropDownLabel.vue';
import ImageLabel from './ImageLabel.vue';
import DateTimeLabel from './DateTimeLabel.vue';

export default {
    components: {
        WriteLabel,
        DateLabel,
        SealLabel,
        SinatureLabel,
        BizDateLabel,
        BoxLabel,
        ConfirmSealLabel,
        DropDownLabel,
        ImageLabel,
        DateTimeLabel,
    },
    props: [
        // eslint-disable-next-line vue/require-prop-types
        'signPlaceBySigner', 'docList', 'page', 'initialMark', 'receiver', 'isHybridCloudContract', 'markList', 'docIndex', 'pageIndex', 'markIndex', 'scale',
    ],
    data() {
        return {
            contractId: this.$route.query.contractId,
            signType: 'shareView',
            mark: [],
            SPACE_HEIGHT: 20,
            markUnscale: {}, // 标签相对 原始大小pdf 的数据，为了pdf高清渲染，mark自己这一层做缩放
        };
    },
    computed: {
        selectedImgUrl() {
            return `${this.mark.value}?access_token=${this.$cookie.get('access_token')}`;
        },
    },
    watch: {
        initialMark: {
            handler(value) {
                const numberCoordinate = coordinateReversal(value, this.page.width, this.page.height);
                this.mark = {
                    ...value,
                    ...numberCoordinate,
                    x: value.x < 1 ? numberCoordinate.x : value.x * this.scale, // 兼容老的模板发送的合同，为绝对定位的情况
                    y: value.y < 1 ? numberCoordinate.y : value.y * this.scale,
                    width: value.width <= 1 ? numberCoordinate.width : value.width * this.scale,
                    height: value.height <= 1 ? numberCoordinate.height : value.height * this.scale,
                    style: {
                        ...value.style,
                        fontSize: (value.style && value.style.fontSize || 18) * this.scale,
                    },
                };
                this.markUnscale = {
                    ...value,
                    ...coordinateReversal(value, this.page.width / this.scale, this.page.height / this.scale),
                };
            },
            immediate: true,
            deep: true,
        },
    },
    methods: {
        changeZIndex(e) {
            const labelContainer = e.target.closest('.label-container');
            labelContainer.style.zIndex = 0;
        },
    },
};
</script>
<style lang="scss">
	.contract-page-content .label-container{
		position: absolute;
        z-index: 10;
        .zIndexIcon{
            position: absolute;
            top: 100%;
            right: 0;
            width: 40px;
            height: 40px;
            background: #E7F3FB;
            border-radius: 2px;
            margin-top: 4px;
            cursor: pointer;
            i{
                font-size: 24px;
                color: #127FD2;
                line-height: 40px;
            }
        }
        .label-delte{
            position: absolute;
            cursor: pointer;

            i{
                color: #515151;
                font-size: 20px;
            }
        }
		.label{
			// position: absolute;

			.confirmed{
				position: absolute;
				left: 0;
				top: -24px;
				width: 100%;
                min-width: 130px;
				height: 24px;
				line-height: 24px;
				background: #1274CC;
				color: #fff;
				text-align: center;
				clear: both;
                display: flex;

				.item{
					position: relative;
                    flex: 1;
					display: inline-block;
                    &:last-child:first-child{
                        width: 100%;
                        &:after{
                            display: none;
                        }
                    }
					&:last-child:after{
						content: '';
						position: absolute;
						left: 0;
						top: 6px;
						display: block;
						width: 1px;
						height: 13px;
						background: #fff;
					}
				}

				i{
					font-size: 13px;
					vertical-align: middle;
				}

				span{
					font-size: 11px;
					vertical-align: middle;
				}
			}

			.label-header-detail{
				position: absolute;
				left: 0;
				top: -24px;
				padding: 0 5px;
				min-width: 100%;
				min-height: 24px;
				border: 1px solid #959595;
				line-height: 24px;
				box-sizing: border-box;
				background: #fff7b6;
				color: #333;
				font-size: 12px;
				cursor: pointer;

				.detail-content{
					display: inline-block;
					width: calc(100% - 28px);
                    white-space: normal;
				}

				.applicant-seal{
					display: inline-block;
				}
                .applicant-name {
                    padding-left: 10px;
                }

				.cancel{
					font-size: 12px;
					color: #1687dc;
					cursor: pointer;
				}
			}
		}
	}
</style>
