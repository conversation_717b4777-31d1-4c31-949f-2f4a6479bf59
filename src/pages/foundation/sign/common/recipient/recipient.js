/**
 * 筛选出后端需要的签约方数组数据
 * @param  {[Array]} recipients [要过滤的数组]
 * @return {[Array]}            [过滤后的数组]
 */
export const filterRecipient = (recipients) => {
    return recipients.map((item) => {
        return {
            receiverId: item.receiverId,
            userAccount: item.userAccount, 	// 用户账户
            userType: item.userType, 		// PERSON(1), ENTERPRISE(2)
            userName: item.userName, 		// 用户名称
            userId: item.userId,
            enterpriseName: item.enterpriseName, // 企业名称 todo,后台不需要了
            enterpriseId: item.enterpriseId, 	// 企业id，string, todo ，先写死，需要查询...
            noticeContent: item.noticeContent, // 用户联系方式
            routeOrder: item.routeOrder, 	// 参与的顺序，默认为上一个参与者顺序+1, 抄送人暂时都被定义为最后一位
            requireIdentityAssurance: item.requireIdentityAssurance, //	是否需要实名认证
            receiverType: item.receiverType, 	// 参与人类型 签署人(2) 抄送人(3)
            notification: item.notification,
            privateLetter: item.privateLetter, // 私信
            forceHandWrite: item.forceHandWrite, // 是否手绘签名
            faceVerify: item.faceVerify, 	// 刷脸签署
            messageVerifyCode: item.messageVerifyCode, // 身份验证
            thirdPartyPlatformId: item.thirdPartyPlatformId, //	第三方平台
            privateLetterFileList: item.privateLetterFileList, // 私信上传文件列表
            privateLetterFileVOList: item.privateLetterFileVOList, // 私信上传文件列表
            photoHref: item.photoHref,
            attachmentList: item.attachmentList,
            attachmentRequired: item.attachmentRequired,
            idNumberForVerify: item.idNumberForVerify,
        };
    });
};

/**
 * 合并签约方数组
 * @param  {[Array]} recipients    [原数组]
 * @param  {[Array]} newRecipients [新数组]
 * @return {[Array]}               [合并后的数组]
 * 目的使用新的数组覆盖合并旧数组，并对一些不适用于前端展示的数据格式化
 */
export const mergeRecipient = (recipients, newRecipients) => {
    return newRecipients.map((item, index) => {
        const recipient = recipients[index];
        return {
            ...recipient,
            ...item,
            errors: recipient.errors || [], // 错误是特例，前端覆盖后端
        };
    });
};
