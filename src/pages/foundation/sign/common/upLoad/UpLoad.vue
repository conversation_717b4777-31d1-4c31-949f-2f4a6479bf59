<!-- 签署——上传文件组件 -->
<template>
    <div class="upload-items">
        <Draggable
            :list="docList"
            @end="resetOrder"
            :disabled="!editable"
            :options="isUploading ? { disabled : true } : { disabled : false }"
        >

            <div class="upload-item back-grey fl"
                v-for="(doc, index) in docList"
                :key="index"
                @mouseenter="doc.zoomMaskShow = true"
                @mouseleave="doc.zoomMaskShow = false"
            >

                <div class="top" v-if="doc.uploadStatus == 1">
                    <i class="loading_icon el-icon-loading"></i>
                </div>
                <div class="top" v-if="doc.uploadStatus == 2">
                    <img :src="doc.imgSrc" :alt="$t('uploadFile.thumbnails')">
                    <div class="mask"
                        v-show="doc.zoomMaskShow"
                        @click="preview(doc)"
                    >
                        <i class="zoomIn_icon el-icon-view"></i>
                    </div>
                </div>
                <div class="bottom" :class="{ ['lang_' + $t('lang')]: true, 'diseditable': !editable }">
                    <p class="title">{{ doc.fileName }}</p>
                    <p class="info" v-if="doc.uploadStatus == 1">
                        <span class="status">{{ $t('uploadFile.isUploading') }}...</span>
                    </p>
                    <div class="info" v-if="doc.uploadStatus == 2">
                        <span v-show="!doc.zoomMaskShow" class="pages">{{ $t('uploadFile.totalPages', { page: doc.pageSize }) }}</span>
                        <p v-show="doc.zoomMaskShow">
                            <i class="edit-icon icon-bold drag_icon el-icon-ssq-yidong">
                                <span class="icon_toast">{{ $t('uploadFile.move') }}</span>
                            </i>
                            <i class="edit-icon icon-bold delete_icon el-icon-delete2" @click="deleteDoc(index)">
                                <span class="icon_toast">{{ $t('uploadFile.delete') }}</span>
                            </i>
                            <el-upload
                                class="edit-icon"
                                :action="reUploadUrl"
                                :headers="uploadHeaders"
                                :before-upload="beforeReUpload.bind(null, index, 'replace')"
                                :on-success="onReUploadSuccess"
                                :on-error="onReUploadError.bind(null, index, doc.fileName)"
                                :http-request="handleUploadRequest.bind(null, index, 'replace')"
                                :show-file-list="false"
                            >
                                <i class="again_upload_icon el-icon-ssq-tihuan icon-bold" @click="clickReplace">
                                    <span class="icon_toast">{{ $t('uploadFile.replace') }}</span>
                                </i>
                            </el-upload>
                        </p>
                    </div>
                </div>
            </div>
        </Draggable>

        <!-- uploadBtn -->
        <el-upload
            class=""
            :disabled="!editable"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :before-upload="beforeUpload.bind(null, -1, 'upload')"
            :on-success="onUploadSuccess"
            :on-error="onUploadError"
            :http-request="handleUploadRequest.bind(null, -1, 'upload')"
            :show-file-list="false"
        >
            <div class="upload-item upload-btn" v-if="docList.length <= 49" @click="clickUploadFile">
                <i class="upload_document_icon el-icon-ssq-shangchuanbendiwenjian"></i>
                <p>{{ $t('uploadFile.uploadFile') }}</p>
            </div>
        </el-upload>
    </div>
</template>

<script>
import { checkDocumentUploadLimit, checkFileNameLength } from 'src/common/utils/fileLimit';
import previewPdfPopup from 'components/previewPdfPopup/previewPdfPopup.js';
import Draggable from 'vuedraggable';
import { joinPathnameAndQueryObj } from 'utils/getQueryString.js';
import { prepareUploadRequest } from 'utils/hybrid/hybridBusiness.js';
import {  mapGetters } from 'vuex';

export default {
    components: {
        Draggable,
    },
    props: {
        files: {
            default() {
                return [];
            },
            type: Array,
        },
        maxNameLength: {
            default: null,
            type: Number,
        },
        editable: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            contractId: this.$route.query.contractId || '',
            isUploading: 0,
            uploadUrl: `${signPath}/contracts/${this.$route.query.contractId}/documents`,
            uploadData: {
                order: 0,
            },
            reUploadUrl: `${signPath}/contracts/${this.$route.query.contractId}/documents/replace`,
            reUploadData: {
                order: 0,
                documentId: '',
            },
            uploadHeaders: { Authorization: `bearer ${this.$cookie.get('access_token')}` },
            docList: [],
            previewDocId: '',
            previewIndex: 0,
        };
    },
    computed: {
        ...mapGetters(['checkFeat', 'isPerson', 'getIsForeignVersion', 'getUserType']),
        // 混合云host
        hybridServer() {
            return this.$store.state.commonHeaderInfo.hybridServer;
        },
        hybridAccessToken() {
            return this.$store.state.commonHeaderInfo.hybridAccessToken;
        },
    },
    watch: {
        /* 监控以同步 */
        docList: {
            handler(newValue) {
                this.$emit('update:files', newValue);
            },
            deep: true,
        },
    },
    methods: {
        sensorsFn(eventName, params = {}) {
            if (this.getUserType === 'Person') {
                this.$sensors.track({
                    eventName,
                    eventProperty: {
                        page_name: '添加文件和签约方',
                        first_category: '上传文档',
                        ...params,
                    },
                });
            }
        },
        clickUploadFile() {
            this.sensorsFn('Ent_ContractSendDetail_BtnClick', {
                icon_name: '上传本地文件',
            });
        },
        // 过滤错误
        matchErrMsg(message) {
            message = message || '';
            const matchedAry = message.match(/\{(.+?)\}/g);
            return message ? JSON.parse(matchedAry[0]) : {
                message: this.$t('uploadFile.matchErr'),
            };
        },
        // 排序重置
        sortOrder() {
            this.docList.sort((a, b) => a.order > b.order);
        },
        // 合并一个新doc数据到docList
        // 兼容混1、混3逻辑，混3逻辑与公有云保持一致，主要差异在预览图地址从后端获取
        handleMergeDocToList(res) {
            const mergedDoc = this.$hybrid.signPrepareMergeDoc(res, this.contractId);
            Vue.set(this.docList, mergedDoc.order, mergedDoc.docData);
        },
        // order洗牌，并提交给后端
        resetOrder() {
            this.docList.map((item, index) => {
                return item.order = index;
            });

            const orders = this.docList.map((doc, index) => {
                return {
                    documentId: doc.documentId,
                    order: index,
                };
            });

            this.$http({
                url: `${signPath}/contracts/${this.contractId}/documents/orders`,
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                data: JSON.stringify(orders),
            });
        },
        clickReplace() {
            this.sensorsFn('Ent_ContractSendDetail_BtnClick', {
                icon_name: '替换',
            });
        },
        // 删除文件
        async deleteDoc(index) {
            this.sensorsFn('Ent_ContractSendDetail_BtnClick', {
                icon_name: '删除',
            });
            const passRes = await this.$hybrid.offlineTip();
            if (!passRes) {
                return;
            }
            if (this.isUploading) {
                this.$MessageToast(this.$t('uploadFile.inUploadingDeleteErr'));
                return;
            }
            // this.$http.delete(`${signPath}/contracts/${this.contractId}/documents/${this.docList[index].documentId}`)
            this.$http({
                method: 'delete',
                url: `${signPath}/contracts/${this.contractId}/documents/${this.docList[index].documentId}`,
            })
                .then((res) => {
                    this.docList.splice(index, 1);
                    this.resetOrder();
                    this.getContractLifeEnd(res, 'delete');
                });
        },
        // 上传文件
        async beforeUpload(index, type, file) {
            const passRes = await this.$hybrid.offlineTip();
            const hybridTarget = type === 'upload' ? '/document/upload' : '/document/replace';

            if (!passRes) {
                return Promise.reject();
            }
            file = checkFileNameLength(file, this.maxNameLength, false);
            const size = this.checkFeat.bigFileSwitch ? 50 : 10;
            const isPassLimit = checkDocumentUploadLimit(file, size);
            const order = this.docList.length;
            if (isPassLimit) {
                const newDoc = {
                    order,
                    uploadStatus: 1,	// '0':初始化 '1':上传中 '2':上传成功
                    fileName: file.name,    // 文件名称
                };
                this.isUploading++;
                this.docList.push(newDoc);
            } else {
                return Promise.reject();
            }
            this.uploadData = {
                order,
                contractId: this.contractId,
            };
            // 混合云
            if (this.hybridServer) {
                return new Promise((resolve, reject) => {
                    this.$hybrid.makeHeader({ url: this.uploadUrl, hybridTarget, method: 'POST', requestData: this.uploadData, isFormType: 1 })
                        .then(res => {
                            const resHeaders = res.data;
                            this.uploadHeaders = {
                                ...this.uploadHeaders,
                                ...resHeaders,
                            };
                            resolve();
                        })
                        .catch(() => {
                            this.isUploading--;
                            this.docList.splice(this.docList.length - 1, 1);
                            reject();
                        });
                });
            }
        },
        onUploadSuccess(res) {
            this.isUploading--;
            this.handleMergeDocToList(res);
            if (this.docList.length === 1) {
                this.$emit('setFilename', res.fileName);
                this.getContractLifeEnd(res);
            }

            this.$emit('hadInitUploadFile');
        },
        onUploadError(err) {
            this.isUploading--;
            this.handleErrorTip(err);
            this.docList.splice(this.docList.length - 1, 1);
        },
        handleErrorTip(err) {
            const errMsg = err.code === 'ECONNABORTED' ? this.$t('uploadFile.timeOutErr') : err.response.data.message;
            // SAAS-24929：文件已经包含数字证书，个人场景不再支持发已加签合同
            if (err.response.data && err.response.data.code === '190007' && this.isPerson)  {
                this.$confirm(this.$t('uploadFile.hasCATip'), this.$t('uploadFile.tip'), {
                    confirmButtonText: this.$t('uploadFile.understand'),
                    showCancelButton: false,
                    type: 'warning',
                }).then(() => {}).catch(() => {});
            } else {
                this.$MessageToast.error(errMsg);
            }
        },

        // 替换文件
        async beforeReUpload(order, type, file) {
            const passRes = await this.$hybrid.offlineTip();
            const hybridTarget = type === 'upload' ? '/document/upload' : '/document/replace';

            if (!passRes) {
                return Promise.reject();
            }
            file = checkFileNameLength(file, this.maxNameLength, false);
            const size = this.checkFeat.bigFileSwitch ? 50 : 10;
            const isPassLimit = checkDocumentUploadLimit(file, size);
            if (isPassLimit) {
                this.isUploading++;
                this.docList[order].fileName = file.name;
                this.docList[order].uploadStatus = 1;
            }
            this.reUploadData = {
                order,
                contractId: this.contractId,
                documentId: this.docList[order].documentId,
            };
            // 混合云
            if (this.hybridServer) {
                return new Promise((resolve, reject) => {
                    this.$hybrid.makeHeader({ url: this.reUploadUrl, hybridTarget, method: 'POST', requestData: this.reUploadData, isFormType: 1 })
                        .then(res => {
                            const resHeaders = res.data;
                            this.uploadHeaders = {
                                ...this.uploadHeaders,
                                ...resHeaders,
                            };
                            this.$nextTick()
                                .then(() => {
                                    if (isPassLimit) {
                                        resolve();
                                    } else {
                                        reject();
                                    }
                                });
                        })
                        .catch(() => {
                            this.isUploading--;
                            this.docList[order].fileName = file.fileName;
                            this.docList[order].uploadStatus = 2;
                            reject();
                        });
                });
            } else {
                return isPassLimit ? Promise.resolve() : Promise.reject();
            }
        },
        onReUploadSuccess(res) {
            this.isUploading--;
            this.handleMergeDocToList(res);
            if (this.docList.length === 1) {
                this.$emit('setFilename', res.fileName);
                this.getContractLifeEnd(res);
            }
        },
        onReUploadError(index, orgName, err) {
            this.isUploading--;
            this.handleErrorTip(err);
            this.docList[index].fileName = orgName;
            this.docList[index].uploadStatus = 2;
        },

        // 自定义上传接口，入口区分混3
        handleUploadRequest(i, type, opts) {
            const url = type === 'upload' ? this.uploadUrl : this.reUploadUrl;
            const headers = this.uploadHeaders;
            const data = type === 'upload' ? this.uploadData : this.reUploadData;
            const hybridTarget = type === 'upload' ? '/document/upload' : '/document/replace';

            opts.file = checkFileNameLength(opts.file, this.maxNameLength, true);

            if (this.$hybrid.isGamma()) {
                prepareUploadRequest({
                    hybridServer: this.hybridServer,
                    hybridTarget,
                    data,
                    headers,
                    opts,
                }).then(res => {
                    opts.onSuccess(res.data);
                })
                    .catch(err => {
                        opts.onError(err);
                    });
            } else {
                this.normalUploadRequest(url, data, opts, headers);
            }
        },
        // 除混三外的原有上传逻辑
        normalUploadRequest(url, data, opts, headers) {
            // 特殊处理，后端需要
            url = joinPathnameAndQueryObj(url, data);

            // body
            const formData = new FormData();
            for (const k in opts.data) {
                const val = opts.data[k];
                formData.append(k, val);
            }
            formData.append('file', opts.file);

            this.$http.post(url, formData, { headers, noToast: 1 })
                .then(res => {
                    opts.onSuccess(res.data);
                })
                .catch(err => {
                    opts.onError(err);
                });
        },

        // 预览文件
        async preview(doc) {
            // this.previewDocId = documentId;
            // this.previewIndex = previewIndex;
            const passRes = await this.$hybrid.offlineTip();
            if (!passRes) {
                return;
            }
            // pdf文档预览需要根据page对象计算宽高
            if (!doc.page) {
                const page = [];
                let i = 0;
                while (i < doc.pageSize) {
                    i++;
                    page.push({
                        width: 0,
                        height: 0,
                    });
                }
                doc.page = page;
            }

            const fileStreamUrl = `${signPath}/contracts/${doc.contractId}/documents/download?documentIds=${doc.documentId}`;
            const pdfurl = await this.$hybrid.getPdfPreviewUrl({ url: fileStreamUrl, hybridServer: this.hybridServer, hybridTarget: '/contract/part/document/download', params: { contractId: doc.contractId, documentIds: doc.documentId }, hybridAccessToken: this.hybridAccessToken });
            const previewDoc = {
                name: doc.name,
                fileName: doc.fileName,
                page: doc.page,
                hasLoaded: false, // 不使用缓存
                pdfurl,
            };
            previewPdfPopup({ previewDoc });
        },

        // initUploadUrl
        initUploadUrl() {
            this.uploadUrl = `${this.$store.state.commonHeaderInfo.hybridServer}${signPath}/contracts/${this.$route.query.contractId}/documents`;
            this.reUploadUrl = `${this.$store.state.commonHeaderInfo.hybridServer}${signPath}/contracts/${this.$route.query.contractId}/documents/replace`;
        },

        // 自动识别合同到期日
        getContractLifeEnd(file, type) {
            const checkFeat = this.$store.getters.checkFeat;
            if (type === 'delete') {
                this.$emit('setContractLifeEnd', '');
            } else if (checkFeat.contractExpirationReminder) {
                this.$loading();
                this.$http.get(`${signPath}/contracts/get-contract-end-date/${file.contractId}/${file.documentId}`).then(({ data }) => {
                    if (data.code === '140001') {
                        this.$emit('setContractLifeEnd', data.result.contractEndDate);
                    }
                }).finally(() => {
                    this.$loading().close();
                });
            }
        },
    },
    created() {
        // 获取所有文件并展示
        this.$http.get(`${signPath}/contracts/${this.contractId}/documents`)
            .then(res => {
                (res.data || []).forEach((item, index) => {
                    this.$set(this.docList, index, item);
                    this.handleMergeDocToList(item);
                });
                (res.data && res.data.length) && this.$emit('hadInitUploadFile');
                this.initUploadUrl();
            })
            .catch(err => {
                this.$MessageToast.error(err?.response?.data?.message || '');
            })
            .finally(() => {
                this.$emit('loaded');
            });
    },
};
</script>

<style lang="scss">
	$item-width: 190px;
	.upload-items {
		font-size: 12px;
		margin: 20px 0 6px;
		* {
			box-sizing: border-box;
		}
		.upload-item {
			display: inline-block;
			width: $item-width;
			height: 225px;
			margin-right: 10px;
			margin-bottom: 20px;
			border: 1px solid #ddd;
			border-radius: $border-radius;
			text-align: center;
			vertical-align: top;
			&.back-grey{
				background-color: #f4f4f4;
			}
			i.upload_document_icon {
				margin-top: 58px;
				margin-bottom: 14px;
				color: #999;
				font-size: 50px;
			}
			i.loading_icon {
				color: #aaa;
				margin-top: 70px;
				font-size: 45px;
			}
			.top {
				position: relative;
				height: 170px;
				border-bottom: 1px solid $border-color;
				img {
					width: 152px;
					height: 152px;
					margin-top: 17px;
					border-left: 1px solid $border-color;
					border-top: 1px solid $border-color;
					border-right: 1px solid $border-color;
				}
				.mask {
					position: absolute;
					top: 0;
					left: -1px;
					width: $item-width;
					height: 100%;
					line-height: 170px;
					background-color: rgba(0,0,0,.7);
					cursor: pointer;
				}
				.zoomIn_icon {
					font-size: 24px;
					color: #fff;
				}
			}
			.bottom {
				padding-left: 10px;
				padding-right: 10px;
				text-align: left;
				.title {
					width: 100%;
					line-height: 30px;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
					color: #000;
				}
				.info {
					color: #999;
				}
				.edit-icon {
					display: inline-block;
					position: relative;
					padding-right: 10px;
					font-size: 15px;
					cursor: pointer;
					&:hover span {
						display: block;
					}
				}
				.icon-bold {
					font-weight: 300;
				}
				.icon_toast {
					display: none;
					position: absolute;
					top: 21px;
					left: -15px;
					width: 46px;
					height: 23px;
					line-height: 23px;
					background-color: rgb(51, 51, 51);
					text-align: center;
					color: #fff;
					font-size: 12px;
					font-weight: bold;
					border-radius: 2px;
					&:before {
						content: '';
						position: absolute;
						top: -3px;
						left: 41%;
						width: 6px;
						height: 6px;
						background-color: rgb(51, 51, 51);
						transform: rotate(45deg);
					}
				}
				&.lang_ru {
					.icon_toast {
						width: 95px;
					}
				}
			}
		}
		.upload-btn {
			border-style: dashed;
			font-size: 14px;
			color: #999;
			&:hover {
				i {
					color: #2298f1;
				}
				border-style: solid;
				color: #2298f1;
			}
		}
	}
</style>
