<template>
    <div class="sign-local-header">
        <ul class="clear">
            <li class="back" @click="toBack" v-if="(!noRight) && (!backConfig || backConfig.visible)"></li>
            <li class="title">
                <span class="contract-name">{{ title }}&nbsp;&nbsp;</span>
                <slot name="subTitle"></slot>
            </li>
            <div class="sign-header-right" v-if="!noRight">
                <slot name="otherBtn"></slot>
                <li class="video" v-if="showVideoBtn">
                    <a
                        class="operate-video"
                        href="javascript:void(0)"
                        @click="showVideo"
                    >
                        <i class="el-icon-ssq-shipinyaofang"></i>
                        <span>{{ $t('prepare.actionDemo') }}</span>
                    </a>
                </li>
                <template v-for="(type,index) in signerDragLabelTypeList">
                    <li v-if="isShowDragBtn(type)" class="next drag-sign" @click="dragLabel(type)" :key="index">{{ dragText(type) }}</li>
                </template>
                <!-- 下一步 -->
                <li v-if="isNextShow" class="next" @click="toNext">{{ rText || $t('prepare.next') }}</li>
                <slot name="prevBtn"></slot>
            </div>
        </ul>
    </div>
</template>

<script>
import { debounce } from 'utils/decorateTool.js';
export default {
    components: {
    },
    props: {
        title: {
            type: String,
            default: '',
        },
        lText: {
            type: String,
            default: '',
        },
        rText: {
            type: String,
            default: '',
        },
        rShow: {
            type: Boolean,
            default: true,
        },
        showVideoBtn: {
            type: Boolean,
            default: false,
        },
        backConfig: {
            type: Object,
            default: () => {},
        },
        noRight: {
            type: Boolean,
            default: false,
        },
        signerDragLabelTypeList: {
            default: () => [],
            type: Array,
        },
        signatureLabels: {
            default: () => [],
            type: Array,
        },
        dateLabels: {
            default: () => [],
            type: Array,
        },
        receiverId: {
            type: String,
            default: '',
        },
        isEnt: {
            default: false,
            type: Boolean,
        },
        sensorsTrackContractInfo: {
            default: () => {},
            type: Object,
        },
        signType: {
            type: String,
            default: '',
        },
    },
    computed: {
        // 新版本签署人指定签署位置：各自的数量不超过10个
        isShowDragBtn() {
            return (type) => {
                if (type === 'DATE') {
                    // 必须在继续拖章或者继续拖签名 且日期标签个数少于10个才显示拖日期按钮
                    return (this.isSignatureContinueDrag || this.isSealContinueDrag) && this.currentReceiverLabelCount(type) < 20;
                }
                return this.currentReceiverLabelCount(type) < 20;
            };
        },
        dragText() {
            return (type) => {
                if (type === 'SEAL') {
                    if (this.isSealContinueDrag) {
                        return this.$t('signPC.signPlaceBySigner.continueDragSeal');
                    }
                    return this.$t('signPC.signPlaceBySigner.dragSeal');
                }
                if (type === 'SIGNATURE') {
                    if (this.isSignatureContinueDrag) {
                        return this.$t('signPC.signPlaceBySigner.continueDragSignature');
                    }
                    return this.$t('signPC.signPlaceBySigner.dragSignature');
                }
                if (type === 'DATE') {
                    return '拖日期';
                }
            };
        },
        // 合同中的个人签名
        signatureLabelList() {
            return this.signatureLabels.filter(signature => signature.type === 'SIGNATURE');
        },
        // 合同中的印章
        sealLabelList() {
            return this.signatureLabels.filter(signature => signature.type === 'SEAL');
        },
        // 是否继续拖签名
        isSignatureContinueDrag() {
            return !!this.signatureLabelList.length;
        },
        // 是否继续拖章
        isSealContinueDrag() {
            return !!this.sealLabelList.length;
        },
        isNextShow() {
            return this.rShow === undefined || this.rShow;
        },
    },
    methods: {
        // 拖章，拖签名，拖日期
        dragLabel: debounce(function(type) {
            if (type === 'SEAL') {
                // 首次拖章
                if (!this.isSealContinueDrag) {
                    this.$emit('open-sign-place-dialog', { step: 2 });
                }
                return this.$emit('drag-label', { type: 'seal' });
            }
            if (type === 'SIGNATURE') {
                // 首次拖签名
                if (!this.isSignatureContinueDrag) {
                    this.$emit('open-sign-place-dialog', { step: 2 });
                }
                return  this.$emit('drag-label', { type: 'signature' });
            }
            if (type === 'DATE') {
                this.$emit('drag-label', { type: 'date' });
            }
        }, 400),
        currentReceiverLabelCount(type) {
            if (type === 'DATE') {
                return  this.dateLabels.filter(label => [type].includes(label.type) && label.receiverId === this.receiverId).length;
            }
            return this.signatureLabels.filter(label => [type].includes(label.type) && label.receiverId === this.receiverId).length;
        },
        toBack() {
            this.$sensors.track({
                eventName: this.signType === 'approval' ? 'Ent_ContractApproval_BtnClick' : 'Ent_ContractSign_BtnClick',
                eventProperty: {
                    page_name: this.signType === 'approval' ? '合同审批页' : '合同签署页',
                    first_category: '顶部导航栏',
                    icon_name: '返回',
                    ...this.sensorsTrackContractInfo,
                },
            });
            this.$emit('to-back');
        },
        toNext() {
            if (this.rText === this.$t('signPC.commonSign')) {
                this.$sensors.track({
                    eventName: 'Ent_ContractSign_BtnClick',
                    eventProperty: {
                        page_name: '合同签署页',
                        first_category: '顶部导航栏',
                        icon_name: '确认签署',
                        ...this.sensorsTrackContractInfo,
                    },
                });
            } else if (this.rText === this.$t('sign.approve')) {
                this.$sensors.track({
                    eventName: 'Ent_ContractApproval_BtnClick',
                    eventProperty: {
                        page_name: '合同审批页',
                        first_category: '顶部导航栏',
                        icon_name: '同意',
                        ...this.sensorsTrackContractInfo,
                    },
                });
            }
            this.$emit('to-next');
        },
        showVideo() {
            this.$emit('showVideo');
        },
    },
};
</script>

<style lang="scss">
.sign-local-header {
    z-index: 999;
    width: 100%;
    color: #fff;
    background-color: #00263C;
    .back, .title {
        float: left;
        [dir="rtl"] & {
            float: right;
        }
    }
    .next, .video {
        // float: right;
        display: inline-block;
    }
    li {
        height: 50px;
        font-size: 14px;
    }
    .back {
        position: relative;
        width: 55px;
        cursor: pointer;
        border-right: 1px solid #2B4B5F;
        &:after {
            content: "";
            position: absolute;
            top: 18px;
            left: 24px;
            border-left: 2px solid #fff;
            border-top: 2px solid #fff;
            padding: 5px;
            display: inline-block;
            transform: rotate(-45deg);
            -webkit-transform: rotate(-45deg);
            [dir="rtl"] & {
                left: auto;
                right: 24px;
                transform: rotate(135deg);
                -webkit-transform: rotate(135deg);
            }
        }
        &:hover {
            background-color: #0072CF;
        }
    }
    .title {
        width: 61%;
        height: 40px;
        line-height: 40px;
        padding-left: 30px;
        margin: 5px 0;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        .contract-name {
            float: left;
            [dir="rtl"] & {
                float: right;
            }
        }
        .tags-container {
            height: 40px;
            display: table-cell;
            vertical-align: middle;
            padding-top: 2px;
            .tag-item {
                vertical-align: text-top;
                float: left;
                margin-bottom: 3px;
            }
        }
    }
    .video{
        margin-top: 7px;
        height: 33px;
        line-height: 33px;
        margin-right: 15px;
        a{
            .el-icon-ssq-shipinyaofang{
                font-size: 20px;
                position: relative;
                top: 2px;
            }
            color: #44aeff;
        }
        &:hover a{
            color: #fff;
        }
    }
    .next {
        min-width: 85px;
        padding: 0 5px;
        width: auto;
        height: 30px;
        line-height: 30px;
        text-align: center;
        font-size: 14px;
        font-weight: bold;
        background-color: #127fd2;
        margin-top: 10px;
        margin-right: 22px;
        cursor: pointer;
        border-radius: 2px;
        [dir="rtl"] & {
            margin-right: 0;
            margin-left: 22px;
        }
        &:hover {
            background-color: #1687dc;
        }
    }
    .drag-sign{
        margin-right: 15px;
    }
    .sign-header-right {
        position: absolute;
        right: 0;
        height: 100%;
        [dir="rtl"] & {
            left: 0;
            right: auto;
        }
    }
}
</style>
