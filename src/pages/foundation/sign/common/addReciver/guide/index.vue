<template>
    <el-dialog
        :visible.sync="dialogVisible"
        :title="$t('addReceiverGuide.guideTitle')"
        :before-close="handleClose"
        class="add-receiver-guide-dialog"
    >
        <div class="guide-content">
            <p class="guide-title">{{ $t('addReceiverGuide.receiverType') }}</p>
            <div class="sign-type">
                <section
                    v-for="item in signerList"
                    :key="item.title"
                    class="sign-type__module"
                >
                    <p class="sign-type__title">
                        {{ item.title }}
                        <span class="sign-type__title-tip">{{ item.titleTip }}</span>
                    </p>
                    <div class="sign-type__content">
                        <div
                            v-for="val in item.signTypeList"
                            :key="val.id"
                            class="sign-type__item"
                        >
                            <el-radio-group
                                @change="handleRadioChange"
                                v-model="signType"
                                :class="{'last-child': val.id === '6'}"
                                :disabled="!listShowFun(val.value, val.userType)"
                            >
                                <el-radio :label="val.id" class="radio-text">
                                    <i class="radio-text__icon"
                                        :class="val.iconClass"
                                        :style="{backgroundColor: val.iconColor}"
                                    ></i>
                                    <div class="radio-text__content">
                                        <p class="radio-text__content-name">{{ val.text }}</p>
                                        <p v-if="val.desc" class="radio-text__content-desc">{{ val.desc }}
                                            <el-tooltip v-if="val.tooltip" effect="dark" :content="val.tooltip" placement="top">
                                                <i class="el-icon-ssq-bangzhu common-tip-icon"></i>
                                            </el-tooltip>
                                        </p>
                                    </div>
                                    <span v-if="!listShowFun(val.value, val.userType)" class="vip-tip">{{ $t('addReceiverGuide.vipOnly') }}</span>
                                </el-radio>
                            </el-radio-group>
                        </div>
                    </div>
                </section>
            </div>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-checkbox class="next-time-remind" v-model="notRemindNext">
                {{ $t('addReceiverGuide.notRemind') }}
            </el-checkbox>
            <el-button type="text" @click="handleClose">{{ $t('sign.understand') }}</el-button>
            <el-button type="primary" @click="handleNextSteps">{{ $t('sign.submit') }}</el-button>
        </span>
    </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
    name: 'AddReceiverGuideDialog',
    props: {
        dialogVisible: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            signerList: [
                {
                    title: this.$t('addReceiverGuide.asEntSign'),
                    signTypeList: [
                        {
                            text: this.$t('addReceiverGuide.stamp'),
                            desc: this.$t('addReceiverGuide.sealSub'),
                            id: '1',
                            value: 'SEAL',
                            userType: 'ENTERPRISE',
                            iconClass: 'el-icon-ssq-gaizhang2',
                            iconColor: '#127fd2',
                            config: {
                                receiverType: 'SIGNER',
                                'signerConfig.signType': 'SEAL',
                            },
                        },
                        {
                            text: this.$t('addReceiverGuide.entSign'),
                            desc: this.$t('addReceiverGuide.signatureSub'),
                            id: '2',
                            value: 'entSign',
                            userType: 'ENTERPRISE',
                            iconClass: 'el-icon-ssq-qiyeqianzi',
                            iconColor: '#F2A93E',
                            config: {
                                receiverType: 'SIGNER',
                                'signerConfig.signType': 'ENTERPRISE_SIGNATURE',
                            },
                        },
                        {
                            text: this.$t('addReceiverGuide.stampSign'),
                            desc: this.$t('addReceiverGuide.stampSub'),
                            id: '3',
                            value: 'SEAL_AND_SIGNATURE',
                            iconClass: 'el-icon-ssq-gaizhangjiaqianzi',
                            iconColor: '#FF5500',
                            userType: 'ENTERPRISE',
                            config: {
                                receiverType: 'SIGNER',
                                'signerConfig.signType': 'SEAL_AND_SIGNATURE',
                            },
                        },
                        {
                            text: this.$t('addReceiverGuide.requestSeal'),
                            desc: this.$t('addReceiverGuide.confirmSealSub'),
                            id: '4',
                            value: 'requestSeal',
                            iconClass: 'el-icon-ssq-yewuheduizhang',
                            iconColor: '#7A65FE',
                            userType: 'ENTERPRISE',
                            config: {
                                receiverType: 'SIGNER',
                                'signerConfig.signType': 'CONFIRMATION_REQUEST_SEAL',
                            },
                            // desc: this.$t('addReceiverGuide.confirmSealDesc'),
                        },
                    ],
                },
                {
                    title: this.$t('addReceiverGuide.asPersonSign'),
                    signTypeList: [
                        {
                            text: this.$t('addReceiverGuide.asPersonSignTip'),
                            id: '5',
                            value: 'SIGNATURE',
                            userType: 'PERSON',
                            iconClass: 'el-icon-ssq-gerenqianzi',
                            iconColor: '#6CCA55',
                            config: {
                                receiverType: 'SIGNER',
                                'signerConfig.signType': 'SIGNATURE',
                            },
                            desc: this.$t('addReceiverGuide.asPersonSignDesc'),
                        },
                        {
                            text: this.$t('addReceiverGuide.scanSign'),
                            id: '6',
                            value: 'scanSign',
                            userType: 'PERSON',
                            iconClass: 'el-icon-ssq-saomaqianzi',
                            iconColor: '#2ACBD6',
                            config: {
                                receiverType: 'SIGNER',
                                'signerConfig.signType': 'SCAN_CODE_SIGNATURE',
                            },
                            desc: this.$t('addReceiverGuide.scanSignDesc'),
                        },
                    ],
                },
            ],
            signType: null,
            curSignItem: null,
            notRemindNext: false,
        };
    },
    computed: {
        ...mapGetters(['checkFeat']),
    },
    methods: {
        handleClose() {
            this.$emit('closeDialog', this.notRemindNext);
            this.signType = null;
        },
        handleRadioChange(val) {
            const typeIndex = Number(val) > 4 ? 1 : 0;
            this.curSignItem = this.signerList[typeIndex].signTypeList.find(a => a.id === val);
        },
        handleNextSteps() {
            if (!this.curSignItem || !this.curSignItem.id) {
                return this.$MessageToast.info(this.$t('addReceiverGuide.selectSignTypeTip'));
            }
            this.$emit('selectSignType', this.curSignItem, this.notRemindNext);
            this.signType = null;
        },
        // 是否显示各项
        listShowFun(val, userType) {
            const checkFeat = this.checkFeat;
            const listPermission = {
                // 使用方式
                'SIGNATURE': userType === 'PERSON',
                entSign: userType === 'ENTERPRISE' && checkFeat.enterpriseSignature && false, // 普通发起不支持企业签字
                'SEAL': userType === 'ENTERPRISE',
                'SEAL_AND_SIGNATURE': userType === 'ENTERPRISE',
                requestSeal: userType === 'ENTERPRISE' && checkFeat.requestSeal && false, // 普通发起不支持询证函
                scanSign: userType === 'PERSON' && checkFeat.scanSign && false, // 普通发起不支持扫码签字
                cc: true,
            };
            return listPermission[val];
        },
    },
};
</script>

<style lang="scss">
$--color-primary: #127FD2;
/// 主要文字颜色 color|1|Font Color|2
$--color-text-primary: #333333 !default;
/// 常规文字颜色 color|1|Font Color|2
$--color-text-regular: #666666 !default;
/// 次要文字颜色 color|1|Font Color|2
$--color-text-secondary: #999999 !default;
$--color-primary-light-9: #F6F9FC !default;
.en-page .add-receiver-guide-dialog .el-dialog{
     width: 1015px!important;
}
.add-receiver-guide-dialog{
    .el-dialog {
        width: 1015px;
    }
    .el-dialog__body{
        padding: 20px 30px 0;
    }
    .el-dialog__footer{
        padding: 20px 20px 24px 20px;
        .next-time-remind {
            padding-right: 20px;
        }
    }
    .guide-content{
        word-break: keep-all;
        word-wrap: break-word;
        white-space: normal;
        .guide-title{
            font-size: 14px;
            color: $--color-text-primary;
        }
        .sign-type{
            .sign-type__module{
                .sign-type__title{
                    margin: 15px 0;
                    font-size: 14px;
                    color: $--color-text-primary;
                    &-tip {
                        float: right;
                        font-size: 12px;
                        color: $--color-text-secondary;
                    }
                }
                .sign-type__content{
                    display: flex;
                    flex-wrap: wrap;
                    .sign-type__item{
                        margin-bottom: 15px;
                        width: calc(50% - 10px);
                        min-height: 100px;
                        background: $--color-primary-light-9;
                        box-sizing: border-box;
                        border-radius: 4px;
                        border: 1px solid $--color-primary-light-9;
                        &:nth-child(odd) {
                            margin-right: 20px;
                        }
                        .el-radio-group{
                            padding: 20px;
                            width: 100%;
                            box-sizing: border-box;
                            .radio-text {
                                margin-right: 15px;
                                width: 100%;
                                .el-radio__input{
                                    float: right;
                                    height: 16px;
                                    width: 16px;
                                }
                                .el-radio__label {
                                    display: flex;
                                }
                                &__icon {
                                    //margin-top: 15px;
                                    margin-right: 20px;
                                    width: 40px;
                                    height: 40px;
                                    font-size: 18px;
                                    color: #fff;
                                    line-height: 40px;
                                    text-align: center;
                                    background: red;
                                    border-radius: 20px;
                                    &.el-icon-ssq-gaizhangjiaqianzi {
                                        font-size: 16px;
                                    }
                                }
                                &__content {
                                    width: 340px;
                                    &-name {
                                        line-height: 22px;
                                    }
                                    &-desc {
                                        padding-top: 5px;
                                        font-size: 12px;
                                        color: $--color-text-secondary;
                                        line-height: 20px;
                                        white-space: normal;
                                        word-break: break-all;
                                    }
                                }
                                .vip-tip {
                                    position: absolute;
                                    top: -23px;
                                    right: -29px;
                                    transform: scale(0.8);
                                    padding: 5px;
                                    font-size: 12px;
                                    color: #fff;
                                    border-radius: 2px;
                                    background: #FF5500;
                                }
                            }
                            .el-radio__label{
                                vertical-align: middle;
                            }
                        }
                        &:hover {
                            border-color: $--color-primary;
                            .el-radio__input .el-radio__inner {
                                border-color: $--color-primary;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
