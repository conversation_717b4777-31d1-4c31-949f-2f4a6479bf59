<template>
    <div class="account">
        <div>
            <p class="titile" ref="show_text">{{ a1||'default' }}</p>
        </div>
        <div
            v-if="a1 && !s"
            :class="{'active': c===1}"
            class="show_text"
            @click="clickText"
        >{{ a1 }}</div>
        <div
            v-else
            class="addr_text"
            :style="{'width': `${width || 13}px`}"
        >
            <input
                v-model="a1"
                @blur="onEnd"
                @input="onChange"
                type="text"
                style="border:none;outline:none;-webkit-appearance:none;width:100%;"
                autofocus
            >
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            a1: '',
            c: 0,
            s: false,
            width: 13,
        };
    },
    methods: {
        clickText() {
            ++this.c;
            console.log(this.$refs.show_text, this.$refs.show_text.offsetWidth);
            this.width = this.$refs.show_text.offsetWidth;
            if (this.c >= 2) {
                this.$nextTick(() => {
                    this.s = true;
                    this.c = 0;
                });
            }
        },
        onEnd() {
            console.error('onEnd');
            this.c = 0;
            this.width = this.$refs.show_text.offsetWidth;
            this.$nextTick(() => {
                this.s = false;
            });
        },
        onChange() {
            console.log('onChange', this.$refs.show_text.offsetWidth);
            this.width = this.$refs.show_text.offsetWidth + 10;
        },
    },
};
</script>
<style lang="scss" scoped>
.account {
    width: 280px;
    height: auto;
    float: left;
    background: #ffffff;
    .titile {
        background: #dddddd;
        display: inline-block;
    }
}
.active {
    background: #486388;
    min-width: 13px;
}
.show_text {
    display: inline-block;
    color:red;
}
.addr_text {
    float:left;
    input {
        width: 100%;
    }
}
</style>
