export const defaultRecipient = {
    routeOrder: 1,

    userType: 'PERSON',
    userName: '',
    userId: '',
    enterpriseName: '',
    enterpriseId: '',
    userAccount: '',
    roleName: '',

    photoHref: '',

    requireIdentityAssurance: true,
    receiverType: 'SIGNER',

    handWriteNotAllowed: false,
    forceHandWrite: false,
    notification: '',
    privateLetter: '',
    attachmentRequired: false,

    faceFirst: false,
    faceVerify: false,
    messageVerifyCode: '',
    thirdPartyPlatformId: '',

    status_faceFirst: false,
    status_faceVerify: false,
    status_handWriteNotAllowed: false,
    status_forceHandWrite: false,
    status_notification: false,
    status_privateLetter: false,
    status_mustReadBeforeSign: false,
    status_handWritingRecognition: false,
    status_newAttachmentRequired: false,

    // 错误信息
    errors: [],
    // 附件
    attachmentList: [],
    idNumberForVerify: '', // 身份证
    status_idNumberForVerify: false,
    requireEnterIdentityAssurance: false, // 企业经办人是否需要实名
    contractPayer: false, // 付费方
    mustReadBeforeSign: false, // 阅读完毕再签署
    handWritingRecognition: false, // 笔迹识别
    ifProxyClaimer: false, // 前台代收
    sendNoticeLanguage: '',
};
