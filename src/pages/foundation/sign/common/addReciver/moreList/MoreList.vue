<template>
    <div class="addrecipient-more-list sign-prepare-more-list">
        <div class="head">
            <span class="title fl" v-html="title">
            </span>
            <div class="fr" v-if="canOpera">
                <span class="retract" @click="retract">{{ isConShow ? $t('addReceiver.collapse') : $t('addReceiver.expand') }}</span>
                <span class="close" @click="close" v-if="canClose">{{ $t('addReceiver.delete') }}</span>
            </div>
        </div>
        <slot name="moreList-content" v-if="isConShow"></slot>
    </div>
</template>
<script>
export default {
    props: {
        'title': {
            type: String,
            default: '',
        },
        'iClass': {
            type: String,
            default: '',
        },
        'startShow': {
            type: Boolean,
            default: true,
        },
        'canClose': {
            type: Boolean,
            default: true,
        },
        'canOpera': {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            isConShow: this.startShow,
        };
    },
    methods: {
        retract() {
            this.isConShow = !this.isConShow;
        },
        close() {
            this.$emit('clickClose');
        },
    },
};
</script>
<style lang="scss">

	.addrecipient-more-list {
        &.sign-prepare-more-list{
            background-color: #f8f8f8;
            border-top: 1px solid #ebebeb;
            margin-left: 0;
            // 头
            .head {
                height: 42px;
                line-height: 42px;
                font-size: 12px;
            }
            .title {
                color: #333;
                margin-left: 0;
                line-height: 42px;
                i {
                    font-size: 14px;
                    margin-right: 4px;
                }
                .description{
                    color: #999;
                }
            }
            .retract, .close {
                color: $base-color;
                cursor: pointer;
            }
            .retract {
                margin-right: 10px;
            }
            .close {
                margin-right: 15px;
            }
        }

	}
</style>
