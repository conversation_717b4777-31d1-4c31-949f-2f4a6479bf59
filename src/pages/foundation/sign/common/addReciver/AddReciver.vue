<!-- 签署——添加签约方组件 -->
<template>
    <div class="add-recipient-cpn sign-prepare-cpn">
        <div class="edit">
            <ul>
                <li class="left" :class="{'diseditable': !editable}">
                    <el-checkbox
                        name="type"
                        :label="$t('addReceiver.orderSignLabel')"
                        v-model="signOrdered"
                        @change="resetOrder('click')"
                    >
                    </el-checkbox>
                </li>
                <li class="right">
                    <span class="address-list"
                        :class="{'diseditable': !editable}"
                        @click="clickAdressBook"
                    >
                        <i class="iconfont el-icon-ssq-dizhibao"></i>&nbsp;{{ $t('addReceiver.contactAddress') }}
                    </span>
                    <span class="order-view"
                        @click="clickSignOrderBtn"
                    >
                        <i class="iconfont el-icon-ssq-xitong-zuzhijiagou"></i>&nbsp;{{ $t('addReceiver.signOrder') }}
                    </span>
                </li>
            </ul>
        </div>
        <div class="content" :class="{'diseditable': !editable}">
            <ul>
                <Draggable
                    :list="recipients"
                    :options="isDisDrag"
                    @start="selectDisabled = true"
                    @end="onDragEnd"
                >
                    <li
                        v-for="(recipient, index) in recipients"
                        :key="index"
                        class="recipient-li"
                        :class="[
                            recipient.userType,
                            { 'order-sign': signOrdered }
                        ]"
                    >
                        <div class="card-top clear">
                            <p class="card-top-title">{{ recipient.userType==='ENTERPRISE' ? $t('addReceiver.enterprise') : $t('addReceiver.individual') }}</p>
                            <!-- 更多 select -->
                            <el-dropdown class="recipient-more fr"
                                :class="{['lang_' + $t('lang')]: true}"
                                trigger="click"
                                @command="clickMoreSelect"
                                @visible-change="handleVisibleChange($event,index)"
                                ref="messageDrop"
                            >
                                <el-button type="primary">
                                    {{ $t('addReceiver.more') }}
                                    <i class="el-icon-ssq-xialacaidan el-icon--right"></i>
                                    <i v-if="checkShowDropDownNew(recipient) && sendType!=='localSend'" class="el-icon-ssq-biaoqiannew"></i>
                                </el-button>
                                <el-dropdown-menu slot="dropdown" class="sign-el-dropdown-menu">
                                    <template v-if="!getIsForeignVersion">
                                        <el-dropdown-item
                                            v-if="isShowFaceVerify(recipient)"
                                            command="faceFirst"
                                            :index="index"
                                        >
                                            {{ $t('addReceiver.faceFirst') }}
                                        </el-dropdown-item>
                                        <el-dropdown-item
                                            v-if="isShowFaceVerify(recipient)"
                                            command="faceVerify"
                                            :index="index"
                                        >
                                            {{ $t('addReceiver.mustFace') }}
                                        </el-dropdown-item>
                                        <el-dropdown-item
                                            v-if="recipient.requireIdentityAssurance && recipient.userType == 'PERSON' && recipient.receiverType !== 'CC_USER'"
                                            command="handWriteNotAllowed"
                                            :index="index"
                                        >
                                            {{ $t('addReceiver.handWriteNotAllowed') }}
                                        </el-dropdown-item>
                                        <el-dropdown-item
                                            v-if="recipient.userType == 'PERSON' && recipient.receiverType !== 'CC_USER'"
                                            command="forceHandWrite"
                                            :index="index"
                                        >
                                            {{ $t('addReceiver.mustHandWrite') }}
                                        </el-dropdown-item>
                                        <el-tooltip
                                            :manual="true"
                                            :value="checkTrialInfoStatus('handWritingRecognition',trialFeatureData,1) && isTrialTipShowIndex === index && recipient.userType === 'PERSON'"
                                            effect="light"
                                            placement="left"
                                            popper-class="dropdown-tool-tip"
                                            v-if="sendType!=='localSend'"
                                        >
                                            <div slot="content" @click="handleTrialClick(trialFeatureData,'handWritingRecognition')" class="content"><i class="el-icon-ssq-jingshitanhaox"></i>{{ checkTrialInfoStatus('handWritingRecognition',trialFeatureData,3) }}</div>
                                            <el-dropdown-item
                                                v-if="recipient.userType == 'PERSON' &&recipient.receiverType === 'SIGNER'"
                                                command="handWritingRecognition"
                                                :index="index"
                                            >
                                                {{ $t('addReceiver.handWriting') }}
                                                <i v-if="checkTrialInfoStatus('handWritingRecognition',trialFeatureData,2)" class="el-icon-ssq-biaoqiannew">
                                                </i>
                                            </el-dropdown-item>
                                        </el-tooltip>
                                    </template>
                                    <el-dropdown-item
                                        command="privateLetter"
                                        :index="index"
                                    >
                                        {{ $t('addReceiver.addInstructions') }}
                                    </el-dropdown-item>
                                    <el-tooltip
                                        :manual="true"
                                        :value="checkTrialInfoStatus('attachmentRequired',trialFeatureData,1)&& isTrialTipShowIndex === index"
                                        effect="light"
                                        placement="left"
                                        popper-class="dropdown-tool-tip"
                                        v-if="sendType!=='localSend'"
                                    >
                                        <div slot="content" @click="handleTrialClick(trialFeatureData,'attachmentRequired')" class="content"><i class="el-icon-ssq-jingshitanhaox"></i>{{ checkTrialInfoStatus('attachmentRequired',trialFeatureData,3) }}</div>
                                        <el-dropdown-item
                                            v-if="recipient.receiverType == 'SIGNER'"
                                            command="attachmentRequired"
                                            :index="index"
                                        >
                                            {{ $t('addReceiver.attachTips') }}
                                            <el-tooltip class="item"
                                                effect="dark"
                                                :content="$t('addReceiver.instructionsContent')"
                                                placement="top"
                                            ><i class="el-icon-ssq-wenhao tips"></i>
                                            </el-tooltip>
                                            <i v-if="checkTrialInfoStatus('attachmentRequired',trialFeatureData,2)" class="el-icon-ssq-biaoqiannew">
                                            </i>
                                        </el-dropdown-item>
                                    </el-tooltip>
                                    <el-dropdown-item
                                        v-if="recipient.receiverType == 'SIGNER' && !hybridUser && $store.state.commonHeaderInfo.userType === 'Enterprise'"
                                        command="newAttachmentRequired"
                                        :index="index"
                                    >
                                        {{ $t('addReceiver.addContractingInfo') }}
                                        <el-tooltip class="item"
                                            effect="dark"
                                            :content="$t('addReceiver.contractingInfoContent')"
                                            placement="top"
                                        ><i class="el-icon-ssq-wenhao tips"></i>
                                        </el-tooltip>
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        v-if="recipient.receiverType == 'SIGNER' && checkFeat.singerPay"
                                        :disabled="!isAableContractPayer()"
                                        command="contractPayer"
                                        :index="index"
                                    >{{ $t('addReceiver.payer') }}</el-dropdown-item>
                                    <el-dropdown-item
                                        v-if="recipient.receiverType == 'SIGNER' && needReadBeforeSign"
                                        command="mustReadBeforeSign"
                                        :index="index"
                                    >{{ $t('addReceiver.afterReading') }}</el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                            <!-- 签署／抄送 select -->
                            <!-- 使用前台代收功能，不允许抄送 -->
                            <el-select :placeholder="$t('addReceiver.sign')"
                                class="recipient-signtype fr"
                                popper-class="sign-el-select"
                                v-model="recipient.participationType"
                                @change="onChangeSignType($event, index)"
                            >
                                <el-option
                                    v-for="item in (recipient.userType == 'PERSON' ? participationTypeOption : participationTypeOptionEnt)"
                                    :key="item.label + index"
                                    :label="item.label"
                                    :value="item.value"
                                    :disabled="isDisableSignTypeSelect(recipient) && item.value==='CC_USER'"
                                >
                                </el-option>
                            </el-select>
                            <!-- 个人账号 -->
                            <template v-if="recipient.userType == 'PERSON'">
                                <!-- 需要实名 select -->
                                <el-select :placeholder="$t('addReceiver.needAuth')"
                                    v-if="!getIsForeignVersion"
                                    class="recipient-needverified fr"
                                    :class="{ ['lang_' + $t('lang')]: true }"
                                    popper-class="sign-el-select"
                                    v-model="recipient.requireIdentityAssurance"
                                    @change="onChangeRequireIdentityAssurance($event, index)"
                                >
                                    <el-option
                                        v-for="item in realNameOption"
                                        :key="item.label + index"
                                        :label="item.label"
                                        :value="item.value"
                                        :disabled="!item.value && recipient.status_faceVerify || recipient.status_faceFirst"
                                    >
                                    </el-option>
                                </el-select>
                            </template>
                            <template v-else-if="getIsForeignVersion && isPersonSender && recipient.userType == 'ENTERPRISE'">
                                <!-- 不需要实名 select -->
                                <el-select :placeholder="$t('addReceiver.notNeedAuth')"
                                    :disabled="true"
                                    class="recipient-needverified recipient-needverified-ent fr"
                                    :class="{ ['lang_' + $t('lang')]: true }"
                                    popper-class="sign-el-select"
                                    v-model="recipient.requireIdentityAssurance"
                                >
                                    <el-option
                                        v-for="item in jaRealNameOptionEnt"
                                        :key="item.label + index"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                            </template>
                            <!-- 企业账号 -->
                            <template v-else>
                                <!-- 需要实名 select -->
                                <el-select :placeholder="$t('addReceiver.realName')"
                                    :disabled="selectDisabled || recipient.receiverType == 'CC_USER' || recipient.participationType === 'SEAL_AND_SIGNATURE'"
                                    class="recipient-needverified recipient-needverified-ent fr"
                                    :class="{ ['lang_' + $t('lang')]: true }"
                                    popper-class="sign-el-select"
                                    v-if="!getIsForeignVersion"
                                    @change="onChangeRequireEnterIdentityAssurance($event,index)"
                                    v-model="recipient.requireEnterIdentityAssurance"
                                >
                                    <el-option
                                        v-for="item in realNameOptionEnt"
                                        :key="item.label + index"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                            </template>

                        </div>
                        <div class="card-bottom clear">
                            <div class="card-container">
                                <div class="card-container-left">
                                    <!-- 顺序 input -->
                                    <div v-show="signOrdered" class="recipient-order fl">
                                        <input type="text"
                                            v-model.number="recipient.routeOrder"
                                            @change="changeOrderNo"
                                        >
                                    </div>

                                    <!-- 拖拽手柄 -->
                                    <div class="recipient-handle el-icon-ssq-yidongbiaoqian fl"
                                        v-show="signOrdered"
                                    >
                                    </div>

                                    <!-- 头像 -->
                                    <div class="recipient-photo fl">
                                        <img v-if="recipient.photoHref"
                                            :src="`${recipient.photoHref}?access_token=${$cookie.get('access_token')}`"
                                        >
                                        <template v-else>
                                            <i v-if="recipient.userType == 'PERSON'" class="el-icon-ssq-user-filling"></i>
                                            <i v-else class="el-icon-ssq-company"></i>
                                        </template>
                                    </div>
                                </div>

                                <div class="card-container-right">

                                    <!-- 个人账号 -->
                                    <template v-if="recipient.userType == 'PERSON'">
                                        <div class="input-wrapper fl">
                                            <!-- 姓名 input -->
                                            <div class="sign-principal-body input-wrapper-title">
                                                <span class="must" v-if="recipient.personNameNeeded">*</span>
                                                {{ $t('addReceiver.signSubjectPerson') }}
                                            </div>
                                            <el-autocomplete
                                                v-model="recipient.userName"
                                                :placeholder="$t(recipient.personNameNeeded ? 'addReceiver.requiredNameTips' : 'addReceiver.nameTips')"
                                                class="recipient-username"
                                                :popper-class="`ent-name-popper sign-el-autocomplete-popper sign-el-autocomplete-popper--type-three ${recipient.userName ? '' : 'receiver-autocomplete-popper'}`"
                                                :props="{
                                                    label: 'text'
                                                }"
                                                :fetch-suggestions="searchUser.bind(null, index)"
                                                @select="selectUserName($event, index)"
                                                @focus.native.capture="onFocusUsername(index)"
                                                @blur.native.capture="onBlurUsername(index)"
                                            >
                                            </el-autocomplete>
                                            <div class="back-error"
                                                v-html="joinErr(recipient.errors, 'userName')"
                                            >
                                            </div>
                                        </div>
                                        <!-- 填写身份号 -->
                                        <div class="input-wrapper fl" v-if="recipient.requireIdentityAssurance && !getIsForeignVersion">
                                            <div class="input-wrapper-title">
                                                <span class="must" v-if="recipient.personIdNumberNeeded">*</span>
                                                {{ $t('addReceiver.fillIDNumber') }}
                                            </div>
                                            <el-input class="recipient-notification"
                                                type="text"
                                                :placeholder="`${ $t('addReceiver.idNumberTips')} `"
                                                v-model.trim="recipient.idNumberForVerify"
                                                @blur="onBlurIdNumber(index)"
                                            ></el-input>
                                            <div class="back-error"
                                                v-show="recipient.errors && recipient.errors.some( item => item.fieldName == 'idNumberForVerify' )"
                                                v-html="joinErr(recipient.errors, 'idNumberForVerify')"
                                            >
                                            </div>
                                        </div>
                                    </template>

                                    <!-- 企业账号 -->
                                    <template v-else>
                                        <div class="input-wrapper fl">
                                            <!-- 企业名称 input -->
                                            <div class="sign-principal-body input-wrapper-title"><span class="must">*</span>{{ $t('addReceiver.signSubjectEnt') }}</div>
                                            <el-autocomplete
                                                v-model="recipient.enterpriseName"
                                                :placeholder="$t('addReceiver.entNameTips')"
                                                class="recipient-companyname inline-input fcl-input"
                                                :popper-class="`ent-name-popper sign-el-autocomplete-popper sign-el-autocomplete-popper--type-three ${recipient.enterpriseName.trim() ? '' : 'receiver-autocomplete-popper'}`"
                                                :fetch-suggestions="onFetchSuggestions.bind(null, index)"
                                                @select="selectEnpName"
                                                @focus.native.capture="onFocusEntName(index)"
                                                @blur.native.capture="onBlurEnpName"
                                            >
                                            </el-autocomplete>
                                            <!-- 内容提示 -->
                                            <div v-if="!joinErr(recipient.errors, 'enterpriseName')" class="recipient-tips clear">
                                                {{ $t('addReceiver.sameTip') }}
                                            </div>
                                            <div class="back-error"
                                                v-html="joinErr(recipient.errors, 'enterpriseName')"
                                            >
                                            </div>
                                        </div>
                                        <!-- 经办人 姓名 input -->
                                        <div class="input-wrapper fl" v-if="!getIsForeignVersion">
                                            <div class="agent input-wrapper-title">{{ $t('addReceiver.operator') }}</div>
                                            <el-input :placeholder="$t('addReceiver.entOperatorNameTips')"
                                                class="recipient-username"
                                                v-model="recipient.userName"
                                                @focus="onFocusUsername(index)"
                                                @blur="onBlurUsername(index)"
                                            >
                                            </el-input>
                                            <div class="back-error"
                                                v-html="joinErr(recipient.errors, 'userName')"
                                            >
                                            </div>
                                        </div>
                                    </template>

                                    <!-- 账号 input -->
                                    <div class="input-wrapper fl">
                                        <div class="sign-principal-account input-wrapper-title">
                                            <span class="must">*</span>{{ $tc(`addReceiver.receiver${getIsForeignVersion ? 'Ja' : ''}`, 1) }}<span class="tips" v-if="recipient.userType === 'ENTERPRISE'&& recipient.receiverType==='SIGNER'">{{ $tc('addReceiver.receiver', 2) }}</span>
                                        </div>
                                        <template>
                                            <el-autocomplete
                                                v-model.trim="recipient.userAccount"
                                                :disabled="isSelectProxy(recipient)"
                                                :placeholder="isSelectProxy(recipient) ? '' : $t(`addReceiver.accountPlaceholder${getIsForeignVersion ? 'Ja' : ''}`)"
                                                class="recipient-useraccout"
                                                :props="{
                                                    label: 'text'
                                                }"
                                                popper-class="receiver-autocomplete-popper sign-el-autocomplete-popper sign-el-autocomplete-popper--type-one"
                                                type="textarea"
                                                :fetch-suggestions="searchAccounts.bind(null, index)"
                                                @select="selectUserAccount($event, index)"
                                                @focus.native.capture="onFocusUseraccout(index)"
                                                @blur.native.capture="onBlurUseraccout(index)"
                                            >
                                            </el-autocomplete>
                                            <div
                                                v-if="isSelectProxy(recipient)"
                                                class="recipient-useraccout-proxy"
                                            >
                                                <span>{{ $t('addReceiver.proxy') }}</span>
                                                <i
                                                    class="el-icon-ssq-delete"
                                                    @click="handleSwitchReceptionCollection(index, false)"
                                                >
                                                </i>
                                            </div>
                                            <div
                                                v-if="receptionCollection &&recipient.userType === 'ENTERPRISE' && recipient.receiverType==='SIGNER'"
                                                class="recipient-useraccout-suffix"
                                                @click="handleSwitchReceptionCollection(index, true)"
                                            >
                                                <el-tooltip class="item" effect="dark" placement="top">
                                                    <div slot="content">
                                                        {{ $t('addReceiver.accountReceptionCollectionTip1') }}<br />{{ $t('addReceiver.accountReceptionCollectionTip2') }}
                                                    </div>
                                                    <span>代</span>
                                                </el-tooltip>
                                            </div>
                                            <div class="back-error"
                                                v-html="joinErr(recipient.errors, 'userAccount')"
                                            >
                                            </div>
                                            <div v-if="recipient.foreigners === 1 && !(recipient.errors || []).some((item) => item.fieldName === 'userAccount')" class="recipient-tips clear">
                                                {{ $t('addReceiver.aboradTip') }}
                                            </div>
                                        </template>
                                    </div>

                                    <!-- 业务角色 -->
                                    <div class="input-wrapper fl">
                                        <div class="sign-principal-body input-wrapper-title">
                                            {{ $t('addReceiver.busRole') }}
                                            <el-tooltip class="item" effect="dark" placement="top">
                                                <div slot="content">{{ $t('addReceiver.busRoleTip') }}</div>
                                                <i class="el-icon-ssq-bangzhu cursor-point"></i>
                                            </el-tooltip>
                                        </div>
                                        <el-input
                                            :placeholder="$t('addReceiver.busRolePlaceholder')"
                                            class="recipient-notification"
                                            v-model.trim="recipient.roleName"
                                            :maxlength="30"
                                        >
                                        </el-input>
                                    </div>
                                    <!-- 通知手机号 -->
                                    <div class="input-wrapper fl" v-show="recipient.status_notification">
                                        <div class="input-wrapper-title">{{ $t('addReceiver.fillNoticeCall') }}</div>
                                        <el-input class="recipient-notification"
                                            type="text"
                                            placeholder="选填"
                                            v-model.trim="recipient.notification"
                                        >
                                        </el-input>
                                        <div class="back-error"
                                            v-show="recipient.errors && recipient.errors.some( item => item.fieldName == 'notification' )"
                                            v-html="joinErr(recipient.errors, 'notification')"
                                        >
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <!-- 删除按钮 -->
                            <div class="recipient-delete">
                                <i class="delete-btn el-icon-ssq-delete" @click="clickDeleteRecipientBtn(index)"></i>
                            </div>
                            <!-- 更多列表 -->
                            <div class="morelist-container">
                                <MoreList
                                    :key="`notifyForeign${index}`"
                                    v-if="getIsForeignVersion && isPersonSender"
                                    class="manual-sign morelist"
                                    :title="$t('addReceiver.setNoticelang')"
                                    iClass="el-icon-ssq-edit"
                                    :canOpera="false"
                                >
                                    <div
                                        slot="moreList-content"
                                        class="moreList-content"
                                    >
                                        <el-radio v-model="recipient.sendNoticeLanguage" label="EN">{{ $t('addReceiver.English') }}</el-radio>
                                        <el-radio v-if="getIsUae" v-model="recipient.sendNoticeLanguage" label="AR">{{ $t('addReceiver.Arabic') }}</el-radio>
                                        <el-radio v-model="recipient.sendNoticeLanguage" label="ZH">{{ $t('addReceiver.Chinese') }}</el-radio>
                                        <el-radio v-model="recipient.sendNoticeLanguage" label="JA">{{ $t('addReceiver.Japanese') }}</el-radio>
                                    </div>
                                </MoreList>
                                <!-- 优先刷脸，备用验证码签署 -->
                                <MoreList
                                    :key="`faceFirst${index}`"
                                    v-show="recipient.status_faceFirst"
                                    class="face-sign morelist"
                                    @clickClose="clickClose(index, 'faceFirst')"
                                    :title="$t('addReceiver.faceFirst')"
                                    iClass="el-icon-ssq-shenji"
                                >
                                    <div slot="moreList-content" class="moreList-content">
                                        {{ $t('addReceiver.faceFirstTips') }}
                                    </div>
                                </MoreList>
                                <!-- 刷脸签署 -->
                                <MoreList
                                    :key="`faceVerify${index}`"
                                    v-show="recipient.status_faceVerify"
                                    class="face-sign morelist"
                                    @clickClose="clickClose(index, 'faceVerify')"
                                    :title="$t('addReceiver.faceSign')"
                                    iClass="el-icon-ssq-shenji"
                                >
                                    <div slot="moreList-content" class="moreList-content">
                                        <i class="el-icon-ssq-tishi1"></i><span>{{ $t('addReceiver.faceSignTips') }}</span>
                                    </div>
                                </MoreList>
                                <!-- 不允许手写签名 -->
                                <MoreList
                                    :key="`handWriteNotAllowed${index}`"
                                    v-show="recipient.status_handWriteNotAllowed"
                                    class="manual-sign morelist"
                                    @clickClose="clickClose(index, 'handWriteNotAllowed')"
                                    :title="$t('addReceiver.handWriteNotAllowed')"
                                    iClass="el-icon-ssq-edit"
                                >
                                    <div slot="moreList-content" class="moreList-content">
                                        {{ $t('addReceiver.handWriteNotAllowedTips') }}
                                    </div>
                                </MoreList>
                                <!-- 必须手写签名 -->
                                <MoreList
                                    :key="`forceHandWrite${index}`"
                                    v-show="recipient.status_forceHandWrite && recipient.userType === 'PERSON'"
                                    class="manual-sign morelist"
                                    @clickClose="clickClose(index, 'forceHandWrite')"
                                    :title="$t('addReceiver.mustHandWrite')"
                                    iClass="el-icon-ssq-edit"
                                >
                                    <div
                                        slot="moreList-content"
                                        class="moreList-content"
                                    >
                                        <i class="el-icon-ssq-tishi1"></i>
                                        <span
                                            v-if="recipient.status_handWritingRecognition"
                                        >
                                            {{ $t('addReceiver.handWritingTip') }}
                                        </span>
                                        <span
                                            v-else
                                        >
                                            {{ $t('addReceiver.handWriteTips') }}
                                        </span>
                                    </div>
                                </MoreList>
                                <!-- 添加私信 -->
                                <MoreList
                                    :key="`privateLetter${index}`"
                                    class="morelist"
                                    :title="`${$tc('addReceiver.instructions', 1)}<span class='description'>${$tc('addReceiver.instructions', 2)}</span>`"
                                    iClass="el-icon-ssq-duanxin"
                                    v-show="recipient.status_privateLetter"
                                    @clickClose="clickClose(index, 'privateLetter')"
                                >
                                    <div slot="moreList-content" class="moreList-content textarea-con">
                                        <PrivateMessage
                                            :maxContentLength="255"
                                            :privateLetter="recipient.privateLetter"
                                            :privateLetterFileList="recipient.privateLetterFileVOList || []"
                                            @change="updatePrivateMessage(...arguments, recipient)"
                                        />
                                    </div>
                                </MoreList>
                                <!-- 查看文件前验证身份 -->
                                <MoreList
                                    :key="`messageVerifyCode${index}`"
                                    v-show="recipient.status_messageVerifyCode"
                                    class="check-identity morelist"
                                    @clickClose="clickClose(index, 'messageVerifyCode')"
                                    :title="$t('addReceiver.verifyBefore')"
                                    iClass="el-icon-ssq-shenji"
                                >
                                    <div slot="moreList-content" class="moreList-content">
                                        {{ $t('addReceiver.verify') }}: <input type="text"
                                            v-model.trim="recipient.messageVerifyCode"
                                            :placeholder="$t('addReceiver.verifyTips')"
                                        >
                                        <i class="el-icon-ssq-warm-filling"></i>
                                        <span>{{ $t('addReceiver.verifyTips2') }}</span>
                                    </div>
                                </MoreList>
                                <!-- 发送给第三方平台 -->
                                <MoreList
                                    :key="`thirdPartyPlatformId${index}`"
                                    v-show="recipient.status_thirdPartyPlatformId"
                                    class="third-party morelist"
                                    @clickClose="clickClose(index, 'thirdPartyPlatformId')"
                                    :title="$t('addReceiver.sendToThirdPlatform')"
                                    iClass="el-icon-ssq-shenji"
                                >
                                    <div slot="moreList-content" class="moreList-content">
                                        {{ $t('addReceiver.platFormName') }}: <input type="text"
                                            v-model.trim="recipient.thirdPartyPlatformId"
                                            :placeholder="$t('addReceiver.fillThirdPlatFormName')"
                                        >
                                    </div>
                                </MoreList>
                                <!--附件-->
                                <MoreList
                                    :key="`annex${index}`"
                                    v-show="recipient.attachmentList.length"
                                    class="annex-list morelist"
                                    @clickClose="clickClose(index, 'attachmentRequired')"
                                    :title="$t('addReceiver.attachTips')"
                                    iClass="el-icon-ssq-fujian"
                                >
                                    <div slot="moreList-content" class="moreList-content">
                                        <template v-for="(attachment, attachmentIndex) in recipient.attachmentList">
                                            <div :key="`annexItem${attachmentIndex}`" class="annex-item clear">
                                                <div class="input-wrapper fl">
                                                    <label class="input-wrapper-title">{{ $t('addReceiver.attach') }}{{ attachmentIndex + 1 }}<span class="must">*</span></label>
                                                    <el-input v-model="attachment.name"
                                                        class="input-name"
                                                        @change="handleAttachmentName($event, attachmentIndex, recipient.attachmentList)"
                                                        :placeholder="$t('addReceiver.exampleID')"
                                                        :maxlength="10"
                                                    ></el-input>
                                                </div>
                                                <div class="input-wrapper fl">
                                                    <label class="input-wrapper-title">{{ $t('addReceiver.attachInfo') }}</label>
                                                    <el-input v-model="attachment.comment"
                                                        class="input-desc"
                                                        :placeholder="$t('addReceiver.attachInfoTips')"
                                                        :maxlength="30"
                                                    >
                                                    </el-input>
                                                </div>
                                                <i class="el-icon-ssq-guanbi"
                                                    v-if="recipient.attachmentList.length !== 1"
                                                    @click="removeAnnexItem(index, attachmentIndex)"
                                                ></i>
                                            </div>
                                        </template>
                                        <span v-if="recipient.attachmentList.length < 50"
                                            class="add-btn"
                                            @click="addAnnexItem(index)"
                                        >
                                            <i class="el-icon-ssq-jia"></i>
                                            <a href="javascript:void(0)">{{ $t('addReceiver.addAttachRequire') }}</a>
                                        </span>
                                    </div>
                                </MoreList>
                                <MoreList
                                    :startShow="false"
                                    :key="`annex${index}`"
                                    v-if="recipient.status_newAttachmentRequired && recipient.archiveId"
                                    class="annex-list morelist"
                                    @clickClose="clickClose(index, 'newAttachmentRequired')"
                                    :title="$t('addReceiver.contractingParty')"
                                    iClass="el-icon-ssq-fujian"
                                >
                                    <div slot="moreList-content" class="moreList-content">
                                        <BoxContent :archiveId="recipient.archiveId"></BoxContent>
                                    </div>
                                </MoreList>
                                <MoreList
                                    :key="`contractPayer${index}`"
                                    v-show="recipient.contractPayer"
                                    class="face-sign morelist "
                                    @clickClose="clickClose(index, 'contractPayer')"
                                    :title="$t('addReceiver.payer')"
                                >
                                    <div slot="moreList-content" class="moreList-content">{{ $t('addReceiver.signerPay') }}</div>
                                </MoreList>
                                <!-- 阅读完毕再签署 -->
                                <MoreList
                                    :key="`mustReadBeforeSign${index}`"
                                    v-show="recipient.status_mustReadBeforeSign"
                                    class="manual-sign morelist"
                                    @clickClose="clickClose(index, 'mustReadBeforeSign')"
                                    :title="$t('addReceiver.afterReadingTitle')"
                                    iClass="el-icon-ssq-edit"
                                >
                                    <div slot="moreList-content" class="moreList-content">
                                        <span>{{ $t('addReceiver.afterReading') }}</span>
                                    </div>
                                </MoreList>
                                <!-- 手写笔迹识别 -->
                                <MoreList
                                    :key="`handWritingRecognition${index}`"
                                    v-show="recipient.status_handWritingRecognition && checkFeat.handwriting"
                                    class="manual-sign morelist"
                                    @clickClose="clickClose(index, 'handWritingRecognition')"
                                    :title="$t('addReceiver.handWriting')"
                                    iClass="el-icon-ssq-edit"
                                >
                                    <div slot="moreList-content" class="moreList-content">
                                        <span>
                                            {{ $t('addReceiver.handWritingTips') }}
                                        </span>
                                    </div>
                                </MoreList>
                                <!-- 盖章并签字 -->
                                <MoreList
                                    :key="`signType${index}`"
                                    v-show="recipient.signType ==='SEAL_AND_SIGNATURE'"
                                    :canClose="false"
                                    class="manual-sign morelist"
                                    :title="$t('addReceiver.SsTitle')"
                                    iClass="el-icon-ssq-edit"
                                >
                                    <div slot="moreList-content" class="moreList-content">
                                        <i class="el-icon-ssq-tishi1"></i>
                                        <span>
                                            {{ $t('addReceiver.SsTip') }}
                                        </span>
                                    </div>
                                </MoreList>
                            </div>
                        </div>
                    </li>
                </Draggable>
            </ul>

            <div class="add-btns clear">
                <div class="add-btn" @click="clickAddEnterpriseRecipientBtn()">
                    <i class="el-icon-ssq-jia" style="font-weight: bold;"></i>&nbsp;{{ $t('addReceiver.addSignEnt') }}
                </div>
                <div class="add-btn" @click="clickAddRecipientBtn('PERSON')">
                    <i class="el-icon-ssq-jia" style="font-weight: bold;"></i>&nbsp;{{ $t('addReceiver.addSignPerson') }}
                </div>
            </div>

        </div>

        <DialogAddressBook
            v-if="addressBookDialogVisible"
            :visible.sync="addressBookDialogVisible"
            :addressBookSelectType="addressBookSelectType"
            @save="clickSaveChoosed"
            @showDataBoxInvite="showDataBoxInviteDialog = true"
            @close="addressBookDialogVisible = false"
        >
        </DialogAddressBook>

        <!-- 对话框：查看签署位置 -->
        <SignOrderDialog class="SignOrderDialog"
            :recipients="signOrderData"
            :ordered="signOrdered"
            ref="signOrderDialog"
            :unlockScroll="true"
        ></SignOrderDialog>
        <ChoseBox v-if="choseBoxDialog.visible" :type="choseBoxDialog.type" @close="choseBoxDialog.visible=false" @confirm="choseBoxConfirm"></ChoseBox>
        <DataBoxInvite
            v-if="$store.state.commonHeaderInfo.userType === 'Enterprise'"
            :visible.sync="showDataBoxInviteDialog"
        ></DataBoxInvite>
        <AddReceiverGuideDialog
            :dialogVisible.sync="addEntReceiverGuideVisible"
            @closeDialog="closeGuideDialog"
            @selectSignType="selectSignType"
        ></AddReceiverGuideDialog>
    </div>
</template>

<script>
import resRules from 'src/common/utils/regs.js';
import MoreList from './moreList/MoreList.vue';
import Draggable from 'vuedraggable';

import DialogAddressBook from 'src/components/sendingPrepare/dialogAddressBook/index.vue';
import SignOrderDialog from 'src/components/signOrder/index.vue';
import DataBoxInvite from 'src/components/dataBoxInvite/index.vue';
import { filterRecipient, mergeRecipient } from '../recipient/recipient.js';
import PrivateMessage from 'src/pages/foundation/sign/field/approvalApp/PrivateMessage.vue';
import { mapState, mapGetters } from 'vuex';
import { signerInfoAssociateMixin } from 'src/mixins/signerInfoAssociate.js';

import { defaultRecipient } from './static.js';
import BoxContent from '@/components/boxContent/';
import ChoseBox from '@/components/choseBox/';
import { advancedFeatureMixin } from 'src/mixins/advancedFeature.js';
import AddReceiverGuideDialog from 'foundation_pages/sign/common/addReciver/guide';
import { postTemplateSendGuideRemindInfo, getTemplateSendGuideRemindInfo } from '@/api/sign';

export default {
    components: {
        AddReceiverGuideDialog,
        MoreList,
        Draggable,
        DialogAddressBook,
        SignOrderDialog,
        PrivateMessage,
        BoxContent,
        ChoseBox,
        DataBoxInvite,
    },
    mixins: [signerInfoAssociateMixin, advancedFeatureMixin],
    // eslint-disable-next-line vue/require-prop-types
    props: ['initialSignOrdered', 'getDefaultConfig', 'editable', 'sendType'],
    data() {
        return {
            hasAccount: false,
            // 基本信息 from ajax
            contract_id: this.$route.query.contractId,

            // 拖拽
            isDraged: false,
            // 发件人信息
            sender: null,
            // 卡片
            recipients: [],
            // 阻止删除卡片时触发风险提醒
            selectDisabled: false,
            // 是否顺序签署
            signOrdered: this.initialSignOrdered,

            // selector视图
            participationTypeOption: [{
                value: 'SIGNATURE',
                label: this.$t('addReceiver.signature'),
            }, {
                value: 'CC_USER',
                label: this.$t('addReceiver.cc'),
            }],
            participationTypeOptionEnt: [{
                value: 'SEAL',
                label: this.$t('addReceiver.stamp'),
            }, {
                value: 'SEAL_AND_SIGNATURE',
                label: this.$t('addReceiver.Ss'),
            }, {
                value: 'CC_USER',
                label: this.$t('addReceiver.cc'),
            }],
            realNameOption: [{
                value: true,
                label: this.$t('addReceiver.needAuth'),
            }, {
                value: false,
                label: this.$t('addReceiver.notNeedAuth'),
            }],
            realNameOptionEnt: [{
                value: true,
                label: `${this.$t('addReceiver.operatorNeedAuth')}`,
            }, {
                value: false,
                label: `${this.$t('addReceiver.operatorNotNeedAuth')}`,
            }],
            jaRealNameOptionEnt: [{
                value: false,
                label: `${this.$t('addReceiver.notNeedAuth')}`, // 日本版本企业默认不需要实名，且不能切换
            }],

            // 计时状态
            isCounting: false,
            isDelete: 0,
            timer: 0,

            selectedIndex: 0,

            // 查询账号信息
            accountInfo: {},
            enpInfo: {},
            enpList: [],
            canEnpSelectChange: false,

            // 地址簿
            addressBookDialogVisible: false,
            addressBookSelectType: '',

            // 查看签署人顺序
            signOrderData: [],

            // 防恶意输入
            // imgVaDialogVisible: false,
            imgVaKey: 0,
            imageVerifyCode: '',
            // 图片验证码key
            imageKey: '',

            // 提取
            // pickDialogVisible: false,
            // pickedInfo: [],
            // isPicking: false,
            needReadBeforeSign: false,

            entDefaultRecipient: {},
            personDefaultRecipient: {},
            choseBoxDialog: { // 输入/选择档案柜弹窗
                visible: false,
                type: 0,
            },
            isTrialTipShowIndex: -1, // 试用tooltip是否显示对应数据的index

            addEntReceiverGuideVisible: false, // 是否展示添加企业引导弹窗
            guideWhenAddEntConfig: false, // 用户是否已选择下次不再提醒
            isLimitFaceConfig: false,
        };
    },
    computed: {
        ...mapState({
            hybridServer: state => state.commonHeaderInfo.hybridServer,
            currentEntId: state => state.commonHeaderInfo.currentEntId,
        }),
        ...mapState('sendingPrepare', ['receptionCollection']),
        ...mapGetters(['checkFeat', 'getIsForeignVersion', 'checkAdvancedFeatureData', 'getUserType', 'getIsUae']),
        isDisDrag() {
            return {
                // CFD-278 如果将拖拽区域定为 li 会有问题，所以将拖拽区域定为 .recipient-handle 图标
                handle: '.recipient-handle',
                disabled: !this.signOrdered,
            };
        },
        isPersonSender() {
            return this.currentEntId === '0';
        },
        recipientsLength() {
            return this.recipients.length;
        },
        uploadUrl() {
            return `${this.url}${this.uploadType[this.type || 0]}`;
        },
        hybridUser() {
            return !!this.hybridServer && this.$hybrid.isAlpha();
        },
        // 是否选择了前台代收
        isSelectProxy() {
            return recipient => this.receptionCollection && recipient.userType === 'ENTERPRISE' && recipient.ifProxyClaimer && !(recipient.proxyClaimerAccounts || []).length;
        },
        // 高级功能试用信息
        trialFeatureData() {
            return {
                attachmentRequired: this.checkAdvancedFeatureData('34'), // 添加合同附属资料
                handWritingRecognition: this.checkAdvancedFeatureData('79'), // 必须手写并开启笔迹识别
            };
        },
    },
    watch: {
        initialSignOrdered(v) {
            this.signOrdered = v;
        },
        noticeLanguage(val) {
            this.$emit('updateLanguage', val);
        },
    },
    methods: {
        closeGuideDialog(val) {
            this.addEntReceiverGuideVisible = false;
            if (val) {
                this.showRemindNextTime = false;
                this.guideWhenAddEntConfig = true;
                postTemplateSendGuideRemindInfo('true');
            }
        },
        // 弹窗选择签署方参与合同的方式回调
        selectSignType(item, nextTimeNotRemind) {
            this.addEntReceiverGuideVisible = false;
            if (nextTimeNotRemind) {
                this.guideWhenAddEntConfig = true;
                postTemplateSendGuideRemindInfo('true');
            }
            // 第一步 新增一个签约主体
            this.clickAddRecipientBtn(item.userType, item.value);
        },
        sensorsFn(eventName, params = {}) {
            if (this.getUserType === 'Person') {
                this.$sensors.track({
                    eventName,
                    eventProperty: {
                        page_name: '添加文件和签约方',
                        first_category: '添加签约方',
                        ...params,
                    },
                });
            }
        },
        // 签约方'更多'是否展示new标志
        checkShowDropDownNew(recipient) {
            if (!recipient) {
                return false;
            }
            if (recipient.userType === 'ENTERPRISE') {
                return  this.checkTrialInfoStatus('attachmentRequired', this.trialFeatureData, 2);
            } else if (recipient.userType === 'PERSON')  {
                return Object.keys(this.trialFeatureData).some(key => this.checkTrialInfoStatus(key, this.trialFeatureData, 2));
            }
            return false;
        },
        // dropdown状态控制试用tooltip是否显示
        handleVisibleChange(e, index) {
            setTimeout(() => {
                this.$nextTick(() => {
                    this.isTrialTipShowIndex =  e ? index : -1;
                });
            }, 500);
        },
        resetReciver(recivers) {
            const arr = [];
            recivers.forEach((el, index) => {
                el.userAccount = el.account;
                el.requireEnterIdentityAssurance = el.requireIdentityAssurance;
                arr.push(Object.assign({
                    routeOrder: index + 1,
                    userType: '',
                    userName: '',
                    userId: '',
                    enterpriseName: '',
                    enterpriseId: '',
                    userAccount: '',
                    photoHref: '',
                    requireIdentityAssurance: false,
                    receiverType: '',
                    handWriteNotAllowed: false,
                    forceHandWrite: false,
                    notification: '',
                    privateLetter: '',
                    attachmentRequired: false,
                    faceFirst: false,
                    faceVerify: false,
                    messageVerifyCode: '',
                    thirdPartyPlatformId: '',
                    status_faceFirst: false,
                    status_faceVerify: false,
                    status_handWriteNotAllowed: false,
                    status_forceHandWrite: false,
                    status_notification: false,
                    status_privateLetter: false,
                    status_mustReadBeforeSign: false,
                    status_handWritingRecognition: false,
                    status_newAttachmentRequired: false,
                    errors: [],
                    attachmentList: [],
                    idNumberForVerify: '',
                    status_idNumberForVerify: false,
                    requireEnterIdentityAssurance: false,
                    contractPayer: false,
                    mustReadBeforeSign: false,
                    handWritingRecognition: false,
                    ifProxyClaimer: false,
                }, el));
            });
            this.recipients = Object.assign([], arr);
        },
        getReadBeforeSignConfig() {
            this.$http.get('/ents/configs/read-before-sign-config')
                .then(res => {
                    this.needReadBeforeSign = res.data.value;
                });
        },

        clearTimer() {
            clearTimeout(this.timer);
        },

        joinErr(ary, key) {
            let res = '';
            ary && ary.forEach(item => {
                if (item.fieldName === key) {
                    res += item.errorInfo + '<br/>';
                }
            });
            return res;
        },

        spliceErr(ary, key) {
            ary = ary || [];
            if (!ary.length) {
                return;
            }
            const i = ary.findIndex(item => item.fieldName === key);
            i > -1 && ary.splice(i, 1);
        },

        pushErr(ary, key, errMsg) {
            Array.isArray(ary.errors) &&
                !ary.errors.some(item => item.fieldName === key) &&
                ary.errors.push({
                    fieldName: key,
                    errorInfo: errMsg,
                });
        },

        clearError(i) {
            this.recipients[i].errors = [];
        },

        addRecipient(RecipientsObj, configSignType) {
            // if (!this.beforeAddReceiver()) {
            //     return;
            // }
            // 如果有类型指定，指定对应的签署方式
            const signType = configSignType || (RecipientsObj.userType === 'PERSON' ? 'SIGNATURE' : 'SEAL');
            // 日本环境下签署方默认不需要实名
            let jaAuthConfig = {};
            if (this.getIsForeignVersion && this.isPersonSender) {
                jaAuthConfig = { requireIdentityAssurance: false, sendNoticeLanguage: 'EN' };
            }
            const newRecipient = {
                ...JSON.parse(JSON.stringify(defaultRecipient)),
                ...RecipientsObj.userType === 'PERSON' ? this.personDefaultRecipient : this.entDefaultRecipient,
                ...RecipientsObj,
                ...jaAuthConfig,
                signType,
                participationType: signType,
            };
            this.recipients.push(newRecipient);
        },

        // 确保在每次 recipients Dom 更新完毕后再触发 onChangeRequireIdentityAssurance
        toggleSelectDisabled() {
            this.$nextTick().then(() => {
                this.selectDisabled = false;
            });
        },

        /**
         * 顺序签署
         */
        // 重置签署顺序 顺序签署打勾
        resetOrder(type) {
            if (type === 'click') {
                this.sensorsFn('Ent_ContractSendDetail_BtnClick', {
                    icon_name: this.signOrdered ? '顺序签署' : '关闭顺序签署',
                });
            }
            this.recipients.forEach((item, index) => {
                item.routeOrder = ++index;
            });
        },
        // 改变顺序输入框
        changeOrderNo() {
            this.recipients.sort((a, b) => a.routeOrder > b.routeOrder);
        },
        // 拖拽卡片调换签署顺序
        onDragEnd() {
            this.resetOrder();
            this.toggleSelectDisabled();
        },

        /**
         * 查看签署顺序
         */
        clickSignOrderBtn() {
            this.sensorsFn('Ent_ContractSendDetail_BtnClick', {
                icon_name: '签署顺序',
            });
            this.sensorsFn('Ent_ContractSendDetailWindow_PopUp', {
                window_name: '查看签署顺序',
            });
            const sender = [{
                receiverType: 'SENDER',
                userName: this.sender.senderName,
                photoHref: this.sender.photoHref,
            }];
            this.signOrderData = [...this.recipients, ...sender];
            this.$refs.signOrderDialog.handleOpen();
        },

        /**
         * 通过联系人地址簿添加签约方
         */
        // 点击地址簿
        clickAdressBook() {
            this.sensorsFn('Ent_ContractSendDetail_BtnClick', {
                icon_name: '联系人地址簿',
            });
            this.sensorsFn('Ent_ContractSendDetailWindow_PopUp', {
                window_name: '联系人地址簿',
            });
            this.addressBookSelectType = 'checkBox';
            this.addressBookDialogVisible = true;
        },
        // 点击地址簿保存
        clickSaveChoosed(param) {
            this.sensorsFn('Ent_ContractSendDetailWindow_BtnClick', {
                window_name: '联系人地址簿',
                icon_name: '保存',
            });
            this.addressBookDialogVisible = false;
            param.choosedMembers.forEach(item => {
                let isPerson;
                if (param.choosedType !== 'myContacts') {
                    isPerson = false;
                } else {
                    isPerson = !item.entName;
                }
                // const userAccount = item.account || item.contactAccount;
                const index = this.recipients.length;
                const lastRouteOrder =
                    index
                        ? parseInt(this.recipients[index - 1].routeOrder) + 1
                        : 1;

                this.addRecipient({
                    userType: isPerson ? 'PERSON' : 'ENTERPRISE',
                    routeOrder: lastRouteOrder,
                });

                const recipient = this.recipients[index];
                // 内部联系人
                if (param.choosedType === 'innerMember') {
                    const info = {
                        userId: item.userId,
                        userAccount: item.account,
                        userName: item.empName,
                        enterpriseId: item.entId || '',
                        enterpriseName: item.entName || '',
                        photoHref: '',
                    };
                    this.$set(this.recipients, index, {
                        ...recipient,
                        ...info,
                    });
                } else if (param.choosedType === 'outerContact') {
                    const info = {
                        userAccount: item.contact,
                        userName: item.userName,
                        enterpriseName: item.entName,
                    };
                    this.$set(this.recipients, index, {
                        ...recipient,
                        ...info,
                    });
                } else {
                    const info = {
                        userId: item.userId || '',
                        userAccount: item.contactAccount,
                        userName: item.contactName,
                        enterpriseName: item.entName,
                    };
                    this.$set(this.recipients, index, {
                        ...recipient,
                        ...info,
                    });
                }
            });
        },

        /**
         * 点击添加按钮添加签约方
         */
        // 增加企业
        clickAddEnterpriseRecipientBtn() {
            this.sensorsFn('Ent_ContractSendDetail_BtnClick', {
                icon_name: '添加签约企业',
            });
            if (!this.guideWhenAddEntConfig && sessionStorage.getItem('hasRemindWhenAddEnt') !==
                '1') {
                this.addEntReceiverGuideVisible = true;
                sessionStorage.setItem('hasRemindWhenAddEnt', '1');
                return;
            }
            this.clickAddRecipientBtn('ENTERPRISE');
        },
        // 增加签署人：userType： ENTERPRISE， PERSON
        clickAddRecipientBtn(userType, configSignType) {
            this.sensorsFn('Ent_ContractSendDetail_BtnClick', {
                icon_name: '添加签约个人',
            });
            this.addRecipient({
                userType,
                routeOrder: this.recipients.length + 1,
            }, configSignType);
        },
        // 身份证失去焦点
        onBlurIdNumber(i) {
            const recipient = this.recipients[i];
            const { idNumberForVerify } = recipient;
            if (recipient.personIdNumberNeeded && !idNumberForVerify) {
                this.pushErr(recipient, 'idNumberForVerify', this.$t('addReceiver.noIDNumberErr'));
                return;
            }
            if (idNumberForVerify && !resRules.IDCardReg.test(idNumberForVerify) && !idNumberForVerify.includes('*')) {
                return this.pushErr(recipient, 'idNumberForVerify', this.$t('addReceiver.idNumberForVerifyErr'));
            } else {
                return this.spliceErr(recipient.errors, 'idNumberForVerify');
            }
        },

        /**
         * 姓名操作
         */
        // 姓名获取焦点
        onFocusUsername(i) {
            const recipient = this.recipients[i];
            this.spliceErr(recipient.errors, 'userName');
        },
        onBlurUsername(i) {
            const recipient = this.recipients[i];
            recipient.userName = recipient.userName.trim();
            const userName = recipient.userName;
            if (recipient.personNameNeeded && !userName) {
                this.pushErr(recipient, 'userName', this.$t('addReceiver.noUserNameErr'));
                return;
            }
            if (userName && !resRules.userName.test(userName)) {
                this.pushErr(recipient, 'userName', this.$t('addReceiver.userNameFormatErr'));
            }
        },
        onChangeRequireEnterIdentityAssurance(v) {
            this.sensorsFn('Ent_ContractSendDetail_BtnClick', {
                first_category: '签约企业',
                second_category: '经办人需要实名',
                icon_name: v ? '经办人需要实名' : '经办人不需要实名',
            });
        },
        /**
         * 实名操作
         */
        onChangeRequireIdentityAssurance(v, i) {
            this.sensorsFn('Ent_ContractSendDetail_BtnClick', {
                first_category: '签约个人',
                second_category: '需要实名',
                icon_name: v ? '需要实名' : '不需要实名',
            });
            if (v === false && this.selectDisabled === false) {
                const recipient = this.recipients[i];
                this.$messageBox({
                    iClass: 'sign-add-tip-msgbox',
                    title: this.$t('addReceiver.riskCues'),
                    message: this.$t('addReceiver.riskCuesMsg'),
                    confirmBtnText: this.$t('addReceiver.confirmBtnText'),
                    cancelBtnText: this.$t('addReceiver.cancelBtnText'),
                })
                    .then(() => {
                        recipient.requireIdentityAssurance = true;
                    })
                    .catch(() => {
                        recipient.requireIdentityAssurance = false;
                        recipient.status_idNumberForVerify = false;
                        recipient.idNumberForVerify = '';
                        recipient.handWriteNotAllowed = false;
                        recipient.status_handWriteNotAllowed = false;
                    });
            }
        },
        // <!-- 签字，盖章，签字并盖章抄送 select -->
        onChangeSignType(event, index) {
            // 如果是抄送，企业经办人切换为不需要实名
            const cur = this.recipients[index];
            this.sensorsFn('Ent_ContractSendDetail_BtnClick', {
                first_category: cur.userType === 'PERSON' ? '签约个人' : '签约企业',
                second_category: cur.userType === 'PERSON' ? '签字' : '盖章',
                icon_name: ({ CC_USER: '抄送', SEAL_AND_SIGNATURE: '盖章并签字', SIGNATURE: '签字', SEAL: '盖章' })[event],
            });
            if (event === 'CC_USER') {
                cur.requireEnterIdentityAssurance = false;
                cur.contractPayer = false;
                cur.faceFirst = false;
                cur.status_faceFirst = false;
                cur.faceVerify = false;
                cur.status_faceVerify = false;
                cur.handWriteNotAllowed = false;
                cur.status_handWriteNotAllowed = false;
                cur.forceHandWrite = false;
                cur.status_forceHandWrite = false;
                cur.attachmentList = [];
                cur.attachmentRequired = false;
                cur.mustReadBeforeSign = false;
                cur.status_mustReadBeforeSign = false;
                cur.handWritingRecognition = false;
                cur.status_handWritingRecognition = false;
                cur.newAttachmentRequire = false;
                cur.status_newAttachmentRequired = false;
                cur.receiverType = 'CC_USER';
                cur.signType = null;
            } else if (event === 'SEAL_AND_SIGNATURE') {
                cur.requireEnterIdentityAssurance = true;
                cur.receiverType = 'SIGNER';
                cur.signType = event;
            } else {
                cur.receiverType = 'SIGNER';
                cur.signType = event;
            }
        },
        checkIsMutex(command, selectedRecipient, mutexKeyNames) {
            // 互斥条件
            const mutex1 = (command === mutexKeyNames[0].key && selectedRecipient[mutexKeyNames[1].key]);
            const mutex2 = (command === mutexKeyNames[1].key && selectedRecipient[mutexKeyNames[0].key]);
            if (mutex1 || mutex2) {
                const msg = mutex1 ? mutexKeyNames[1].name : mutexKeyNames[0].name;
                this.$MessageToast.error(this.$t('addReceiver.mutexError', { msg: msg }));
                return true;
            }
            return false;
        },

        /**
         * 更多操作
         */
        // 点击更多select
        clickMoreSelect(command, vm) {
            const ObjectMap = {
                faceFirst: '优先刷脸，备用验证码签署',
                faceVerify: '必须刷脸签署',
                handWriteNotAllowed: '不允许手写签名',
                forceHandWrite: '必须手写签名',
                handWritingRecognition: '开启笔迹识别',
                privateLetter: '添加签约须知',
                attachmentRequired: '添加合同附属资料',
                newAttachmentRequired: '提交签约主体资料',
                contractPayer: '付费方',
                mustReadBeforeSign: '阅读完毕再签署',
            };
            this.sensorsFn('Ent_ContractSendDetail_BtnClick', {
                first_category: this.recipients[vm.$attrs.index].userType === 'PERSON' ? '签约个人' : '签约企业',
                second_category: '更多',
                icon_name: ObjectMap[command],
            });
            if (Object.keys(this.trialFeatureData).includes(command)) {
                // 高级功能未开启，提示
                if (!this.checkTrialInfo(this.trialFeatureData[command])) {
                    return;
                }
            }
            const selectedIndex = this.selectedIndex = vm.$attrs.index;
            const selectedRecipient = this.recipients[selectedIndex];
            // 必须手写和不允许手写互斥 saas-5490
            if (this.checkIsMutex(command, selectedRecipient, [{
                key: 'handWriteNotAllowed',
                name: this.$t('addReceiver.handWriteNotAllowed'),
            }, {
                key: 'forceHandWrite',
                name: this.$t('addReceiver.forceHandWrite'),
            }])) {
                return;
            }
            if (this.checkIsMutex(command, selectedRecipient, [{
                key: 'handWriteNotAllowed',
                name: this.$t('addReceiver.handWriteNotAllowed'),
            }, {
                key: 'handWritingRecognition',
                name: this.$t('addReceiver.handWritingRecognition'),
            }])) {
                return;
            }
            // saas-5890
            if (this.checkIsMutex(command, selectedRecipient, [{
                key: 'faceFirst',
                name: this.$t('addReceiver.faceFirst'),
            }, {
                key: 'faceVerify',
                name: this.$t('addReceiver.faceVerify'),
            }])) {
                return;
            }
            if (this.checkIsMutex(command, selectedRecipient, [{
                key: 'attachmentRequired',
                name: this.$t('addReceiver.attachmentRequired'),
            }, {
                key: 'newAttachmentRequired',
                name: this.$t('addReceiver.newAttachmentRequired'),
            }])) {
                return;
            }
            if (['faceFirst', 'faceVerify'].indexOf(command) > -1 && this.isLimitFaceConfig) {
                this.$MessageToast.error(this.$t('addReceiver.limitFaceConfigTip'));
                return;
            }
            selectedRecipient[`status_${command}`] = true;
            if (['faceFirst', 'faceVerify', 'handWriteNotAllowed', 'forceHandWrite', 'contractPayer', 'mustReadBeforeSign', 'handWritingRecognition'].indexOf(command) > -1) {
                selectedRecipient[command] = true;
            }
            // 开启手写笔迹识别，默认开启必须手写签名
            if (command === 'handWritingRecognition') {
                selectedRecipient.status_forceHandWrite = true;
                selectedRecipient.forceHandWrite = true;
            }
            if (command === 'attachmentRequired') {
                selectedRecipient.attachmentRequired = true;
                selectedRecipient.attachmentList = [{ name: '', comment: '' }];
            }
            if (command === 'newAttachmentRequired') {
                this.choseBoxDialog = {
                    visible: true,
                    type: selectedRecipient.userType === 'PERSON' ? 0 : 1,
                };
            }
        },
        clickClose(index, command) {
            this.recipients[index][`status_${command}`] = false;
            if (['faceFirst', 'faceVerify', 'handWriteNotAllowed', 'forceHandWrite', 'contractPayer', 'handWritingRecognition'].indexOf(command) > -1) {
                this.recipients[index][command] = false;
            } else if (command === 'attachmentRequired') {
                this.recipients[index][command] = false;
                this.recipients[index].attachmentList = [];
            } else if (command === 'privateLetter') {
                this.recipients[index][command] = '';
                this.recipients[index].privateLetterFileVOList = [];
                this.recipients[index].privateLetterFileList = [];
            } else if (command === 'newAttachmentRequired') {
                this.recipients[index][command] = false;
                this.recipients[index].archiveId = '';
            } else {
                this.recipients[index][command] = '';
            }
            // 手写笔迹识别依赖必须手写签名配置
            if (command === 'forceHandWrite') {
                this.recipients[index].status_handWritingRecognition = false;
                this.recipients[index].handWritingRecognition = false;
            }
        },

        /**
         * 卡片信息提交后台保存
         */
        saveRecipients() {
            const data = filterRecipient(this.recipients);
            return this.postRecipients(data)
                .then(res => {
                    this.recipients = mergeRecipient(this.recipients, res.data);
                });
        },

        /**
         * 删除卡片
         */
        clickDeleteRecipientBtn(index) {
            const receiverId = this.recipients[index].receiverId;
            new Promise((resolve) => {
                this.selectDisabled = true;

                if (!receiverId) {
                    resolve();
                } else {
                    this.deleteRecipient(receiverId, { noToast: 1 })
                        .then(() => {
                            resolve();
                        });
                }
            })
                .then(() => {
                    this.recipients.splice(index, 1);

                    this.toggleSelectDisabled();
                    // this.resetOrder(); // 不能重置，因为有两个接受者顺序一致的情况
                });
        },
        /**
         * ajax
         */
        getRecipients() {
            return this.$http.get(`${signPath}/contracts/${this.contract_id}/receivers`);
        },
        getSender() {
            return this.$http.get(`${signPath}/contracts/${this.contract_id}/sender`);
        },

        deleteRecipient(receiverId, opts) {
            return this.$http.delete(`${signPath}/contracts/${this.contract_id}/receivers/${receiverId}`, opts);
        },
        postRecipients(recipients) {
            return this.$http.post(`${signPath}/contracts/${this.contract_id}/receivers`, recipients);
        },
        updatePrivateMessage(data, recipient) {
            if (data.privateLetter === '' || data.privateLetter) {
                recipient.privateLetter = data.privateLetter;
            }
            if (data.privateLetterFileList) {
                recipient.privateLetterFileVOList = data.privateLetterFileList;
                recipient.privateLetterFileList = data.privateLetterFileList.map(i => i.fileId);
            }
        },
        // 添加附件
        addAnnexItem(recipientIndex) {
            if (this.recipients[recipientIndex].attachmentList.length >= 50) {
                return this.$MessageToast.error(this.$t('addReceiver.attachLengthErr'));
            }
            this.recipients[recipientIndex].attachmentList.push({
                name: '',
                comment: '',
            });
        },
        // 删除附件
        removeAnnexItem(recipientIndex, annexIndex) {
            this.recipients[recipientIndex].attachmentList.splice(annexIndex, 1);
        },
        // 是否展示 必须刷脸签署
        isShowFaceVerify(recipient) {
            const isPersonCan = recipient.userType === 'PERSON' && recipient.requireIdentityAssurance;
            const isEntCan = recipient.userType !== 'PERSON' && recipient.requireEnterIdentityAssurance; // 企业经办人刷脸 前提在 配置企业经办人需要实名

            if (recipient.receiverType === 'SIGNER' &&
                this.checkFeat.faceVerifyForSign &&
                (isPersonCan || isEntCan)
            ) {
                return true;
            }
            return false;
        },
        // 处理附件不能重名
        // eslint-disable-next-line no-unused-vars
        handleAttachmentName($event, attachmentIndex, attachmentList) {
            if ($event) {
                attachmentList = attachmentList.map((l, i) => {
                    if ($event && l.name === $event && i !== attachmentIndex) {
                        l.name = '';
                        this.$MessageToast.error(this.$t('addReceiver.attachmentError'));
                    }
                    return l;
                });
            }
        },
        isAableContractPayer() {
            return !this.recipients.filter(i => i.contractPayer).length;
        },
        // 账号前台代收切换
        handleSwitchReceptionCollection(i, val) {
            const recipient = this.recipients[i];
            const temObj = {
                ifProxyClaimer: val,
            };
            if (!val) {
                this.spliceErr(recipient.errors, 'proxyClaimerAccounts');
            } else {
                this.spliceErr(recipient.errors, 'userAccount');
                // 前台代收不能抄送
                if (recipient.receiverType === 'CC_USER') {
                    Object.assign(temObj, {
                        receiverType: 'SIGNER',
                        participationType: 'SEAL',
                        signType: 'SEAL',
                    });
                }
                recipient.userAccount = '';
                recipient.proxyClaimerAccounts = [];
            }
            this.$set(this.recipients, i, {
                ...recipient,
                ...temObj,
            });
        },
        // 选中档案柜后显示
        choseBoxConfirm(archiveId) {
            const selectedRecipient = this.recipients[this.selectedIndex];
            selectedRecipient.newAttachmentRequired = true;
            selectedRecipient.archiveId = archiveId;
            this.choseBoxDialog.visible = false;
        },
        getIsLimitFaceConfig() {
            this.$http.get('/template-api/v2/draft/sender/config')
                .then(({ data }) => {
                    this.isLimitFaceConfig = data?.result?.faceFeatureLimit;
                });
        },
    },
    created() {
        if (this.getIsForeignVersion) {
            this.participationTypeOptionEnt.splice(1, 1);
        }
        this.getDefaultConfig.then(({ entDefaultRecipient, personDefaultRecipient }) => {
            this.entDefaultRecipient = entDefaultRecipient;
            this.personDefaultRecipient = personDefaultRecipient;
        });

        this.getReadBeforeSignConfig();
        getTemplateSendGuideRemindInfo().then(res => {
            this.guideWhenAddEntConfig = res?.data?.value === 'true';
        });

        // 获取接受人列表
        this.getRecipients()
            .then(res => {
                if (res.data.length > 0) {
                    this.recipients = res.data.map(item => {
                        return {
                            ...item,
                            // userAccount: item.type==='ENTERPRISE'?
                            errors: item.errors || [],
                            status_handWriteNotAllowed: !!item.handWriteNotAllowed,
                            status_forceHandWrite: !!item.forceHandWrite,
                            status_notification: !!item.notification,
                            // status_privateLetter: item.privateLetter ? true : false,
                            status_privateLetter: !!((
                                item.privateLetter                                    ||
                                (item.privateLetterFileList && item.privateLetterFileList.length > 0)
                            )),
                            status_faceFirst: !!item.faceFirst,
                            status_faceVerify: !!item.faceVerify,
                            attachmentList: item.attachmentList || [],
                            status_idNumberForVerify: !!item.idNumberForVerify,
                            status_mustReadBeforeSign: !!item.mustReadBeforeSign,
                            status_handWritingRecognition: !!item.handWritingRecognition,
                            status_newAttachmentRequired: !!item.newAttachmentRequired,
                            ifProxyClaimer_end: item.ifProxyClaimer, // 后端数据，只为初始化展示使用
                            userAccount: (item.proxyClaimerAccounts || []).join(';'),
                            participationType: item.receiverType === 'CC_USER' ? item.receiverType : item.signType, // 参与方式
                            userName: item.inputUserName,
                        };
                    });
                }
            })
            .finally(() => {
                this.$emit('loaded');
            });

        // 获取发件人信息（用于签署顺序展示）
        this.getSender()
            .then(res => {
                this.sender = res.data;
            });
        this.getIsLimitFaceConfig();
    },
};
</script>

<style lang="scss">
    .receiver-autocomplete-popper {
        .el-autocomplete-suggestion__list {
            li:first-of-type {
                font-size: 12px;
                color: #ccc;
                pointer-events: none;
                cursor: not-allowed;
            }
        }
    }
    .ent-name-popper .el-autocomplete-suggestion__list {
        li:first-of-type {
            font-size: 12px;
            color: #127FD2;
            pointer-events: unset;
            cursor: pointer;
        }
    }
    .add-recipient-cpn.sign-prepare-cpn {
        position: relative;
        font-size: 12px;
        color: #666;

        // 提取签约方
        // .pick {
        //     position: absolute;
        //     top: -41px;
        //     left: 184px;
        //     padding-top: 0;
        //     padding-bottom: 0;
        //     width: 128px;
        //     height: 28px;
        //     line-height: 28px;
        //     color: #fff;
        //     font-size: 12px;
        //     text-align: center;
        //     border-radius: 2px;
        //     cursor: pointer;
        //     border: none;
        //     &.lang_ru {
        //         width: 290px;
        //         left: 420px;
        //     }
        // }
        // .isPicking {
        //     background-color: #ededed;
        //     color: #999;

        //     &:hover {
        //         background-color: #ededed;
        //         color: #999;
        //     }
        // }
        // .buttonHide {
        //     display: none;
        // }

        // 编辑区
        .edit {
            height: 16px;
            font-size: 14px;
            padding: 19px 24px 7px 0;
        }
        .left {
            float: left;
            .el-checkbox__input {
                cursor: pointer;
                .el-checkbox__inner { // 打勾大小
                    width: 16px;
                    height: 16px;
                    border-radius: 2px;
                }
            }
            .el-checkbox__label {
                padding-left: 12px;
                font-size: 12px;
                &:hover {
                    color: #1687dc;
                }
            }
        }
        .right {
            float: right;
            color: #666;
            span {
                font-size: 12px;
                cursor: pointer;
                i {
                    position: relative;
                    top: 1px;
                    font-size: 16px;
                    color: #999;
                }
                &:hover {
                    color: #1687dc;
                    i {
                        color: #1687dc;
                    }
                }
            }
            .order-view {
                border-left: 1px solid #666;
                padding-left: 15px;
                margin-left: 12px;
            }
        }

        // recipient

        .add-btns {
            .add-btn {
                float: left;
                box-sizing: border-box;
                width: 494px;
                height: 43px;
                line-height: 43px;
                text-align: center;
                font-size: 14px;
                color: #127fd2;
                background-color: #fafafa;
                border: 1px dashed #ddd;
                cursor: pointer;
                margin-top: 12px;
                &:first-child {
                    margin-right: 12px;
                }
                &:hover {
                    background-color: #f1f6fa;
                }
            }
        }

        .recipient-li {
            * {
                box-sizing: border-box;
            }
            box-sizing: border-box;
            position: relative;
            width: 100%;
            // padding: 0 24px 24px;
            // padding-bottom: 0;
            background-color: #f8f8f8;
            margin-top: 12px;
            .card-top{
                padding: 0 24px;
                background:  #F5F5F5;
                &-title {
                    display: inline-block;
                    color: #666;
                    line-height: 30px;
                }

                input{
                    border:0;
                    background: transparent;
                    // padding:0;
                    // padding-right: 20px;
                    text-align: right;
                    &:hover{
                        box-shadow: none;
                    }
                }
                .el-input.is-disabled{
                    .el-input__icon, .el-input__inner {
                        color: #c2d2de;
                    }
                }
            }
            .card-bottom{
                padding:0 24px;
                position: relative;
                 .input-wrapper{
                    position: relative;
                    margin-left: 12px;
                    padding-top:24px;
                    // padding-bottom: 18px;
                    .input-wrapper-title{
                        top: 4px;
                        left: 0px;
                        font-size: 12px;
                        position:absolute;
                        .must{
                            vertical-align: middle;
                            padding: 0 3px;
                            font-size: 12px;
                            color: #FF6432;
                        }
                    }
                }
            }
            &:hover {
                background-color: #f1f6fa;
                .card-top{
                    background:  #EBF3F9;;
                }
                .recipient-delete {
                    .delete-btn {
                        display: block;
                        color: #D2CED5;
                    }
                }
                .morelist-container {
                    .morelist {
                        background-color: #f1f6fa;
                    }
                }
            }

            // .sign-principal-account {
            //     position: absolute;
            //     top: 10px;
            //     left: 78px;
            //     font-size: 12px;
            //     color: #999;
            // }
            // .sign-principal-body {
            //     position: absolute;
            //     top: 10px;
            //     left: 269px;
            //     font-size: 12px;
            //     color: #999;
            // }
            // .agent {
            //     position: absolute;
            //     top: 10px;
            //     left: 592px;
            //     font-size: 12px;
            //     color: #999;
            // }

            .card-container {
                padding-bottom: 36px;
                display: flex;
                padding-top: 16px;
                .card-container-right{
                    flex:1;
                    .input-wrapper-title{
                        color: #666666;
                        &.sign-principal-account .tips {
                            color: #999999;
                        }
                    }
                }
            }

            // element
            .el-input__inner {
                height: 30px !important;
                font-size: 12px;
                // padding: 10px;
                border-color: #ddd;
                border-radius: 2px;
            }
            .el-select {
                border-radius: 2px;
            }

            .recipient-order {
                input {
                    width: 30px;
                    height: 30px;
                    line-height: 30px;
                    text-align: center;
                    font-size: 12px;
                    color: #666;
                    background-color: #fff;
                    border: 1px solid #ddd;
                }
            }
            .recipient-handle {
                font-size: 18px;
                padding: 6px;
                cursor: ns-resize;
            }
            .recipient-photo {
                width: 30px;
                height: 30px;
                i {
                    width: 30px;
                    height: 30px;
                    line-height: 30px;
                    font-size: 30px;
                    color: #E9E9E9;
                    text-align: center;
                    background-color: #CCCCCC;
                    border-radius: 2px;
                }
                img {
                    width: 100%;
                    height: 100%;
                    border-radius: 2px;
                }
            }
            .recipient-photo,.recipient-order,.recipient-handle{
                margin-top: 24px;
            }
            // .recipient-order, .recipient-handle, .recipient-useraccout, .recipient-username, .recipient-needverified, .recipient-companyname, .recipient-signtype, .recipient-more {
            //     margin-top: 5px;
            // }
            .recipient-useraccout {
                width: 280px;
                height: 30px;
                // margin-left: 12px;
                position: relative;
                input {
                    padding-right: 25px;
                }
            }
            .recipient-useraccout-suffix {
                position: absolute;
                right: 4px;
                top: 30px;
                font-size: 14px;
                color: #FFFFFF;
                background: #68ADF1;
                width: 18px;
                height: 18px;
                line-height: 18px;
                text-align: center;
                transform: scale(0.7);
                border-radius: 2px;
            }
            .recipient-useraccout-proxy {
                position: absolute;
                left: 0px;
                top: 20px;
                padding: 10px 140px 0 10px;
                span {
                    background: #DAF0FF;
                    border: 1px solid #92C4EB;
                    border-radius: 2px;
                    border-radius: 2px;
                    display: inline-block;
                    padding: 0 4px;
                }
                .el-icon-ssq-delete {
                    padding-left: 7px;
                    color: #ccc;
                    cursor: pointer;
                    &:hover {
                        color: #999;
                    }
                }
            }
            .recipient-username {
                width: 240px;
                height: 30px;
                // margin-left: 10px;
            }
            .recipient-notification{
                width:144px;
            }
            .recipient-needverified {
                width: 100px;
                i {
                    color: #777;
                }
                input {
                    padding-left: 5px;
                }
                &.lang_ru {
                    input {
                        padding-right: 25px;
                        /*text-overflow: ellipsis;*/
                    }
                }
            }
            .recipient-needverified-ent{
                width:140px;
            }
            .recipient-companyname {
                width: 240px;
                // margin-left: 10px;
            }
            .recipient-signtype {
                width: 106px;
                margin-left: 10px;
                i {
                    color: #777;
                }
            }
            .recipient-more {
                margin-left: 20px;
                button {
                    width: 66px;
                    height: 30px;
                    font-size: 12px;
                    background:transparent;
                    color: #666;
                    padding: 9px 0;
                    border:0;
                    .el-icon--right {
                        margin-left: 10px;
                    }
                    .el-icon-ssq-biaoqiannew{
                        font-size: 12px !important;
                        margin-left: 3px !important;
                    }
                }
                // &.lang_ru {
                //     button {
                //         .el-icon--right {
                //             margin-left: 28px;
                //         }
                //     }
                // }
            }
            .recipient-delete {
                position: absolute;
                top: 15px;
                right: 24px;
                width: 35px;
                height: 70px;
                line-height: 74px;
                &:hover {
                    .delete-btn {
                        display: block;
                        color: #999;
                    }
                }
                .delete-btn {
                    display: none;
                    margin-top: 30px;
                    margin-left: 8px;
                    font-size: 13px;
                    font-weight: bold;
                    color: #bbb;
                    cursor: pointer;
                }
            }
            .back-error {
                position: absolute;
                top: 54px;
                left: 0;
                line-height: 16px;
                color: #f76b26;
                word-break: keep-all;
                // div {
                //     position: absolute;
                //     height: 33px;
                //     line-height: 16px;
                //     overflow: hidden;
                //     text-overflow: ellipsis;
                //     display: -webkit-box;
                //     -webkit-line-clamp: 2;
                //     -webkit-box-orient: vertical;
                // }
                // .useraccount-err {
                //     left: 0;
                //     width: 186px;
                // }
                // .empname-err {
                //     left: 191px;
                //     width: 213px;
                // }
                // .username-err {
                //     left: 191px;
                //     width: 213px;
                // }
            }
            .recipient-tips {
                position: absolute;
                top: 54px;
                left: 0;
                width: 100%;
                color:#999;

                // &-name {
                //     position: absolute;
                //     top: 0;
                //     left: 190px;
                // }
            }

        }

        // // 顺序打勾时
        // .recipient-li.order-sign {
        //     padding-left: 12px;
        //     .sign-principal-account {
        //         left: 126px;
        //     }
        //     .sign-principal-body {
        //         left: 318px;
        //     }
        //     .agent {
        //         left: 641px;
        //     }
        //     .back-error {
        //         left: 125px;
        //     }
        // }

        // recipient-li企业／个人
        .recipient-li.ENTERPRISE {
            .recipient-username {
                width: 144px;
            }
            .username-err {
                left: 414px;
                width: 110px;
            }
        }

        .morelist {
        }

        // 添加私信
        .privateLetter-footer {
            color: #999;
            padding: 8px 0;
        }

        // 刷脸签署
        .face-sign .moreList-content, .manual-sign .moreList-content, .notification .moreList-content {
            padding-bottom: 10px;
        }
        .manual-sign {
            .moreList-content {
                position: relative;
                img {
                    position: absolute;
                    top: -14px;
                    left: 187px;
                }
            }
        }
         /*.check-identity, .third-party .moreList-content {*/
              /*padding-bottom: 30px;*/
          /*}*/
        // moreList
        .moreList-content {
            // margin-left: 36px;
            color:#666;
            i{
                color: #999;
                font-size: 14px;
                margin-right: 2px;
                vertical-align: middle;
            }
            span{
                vertical-align: middle;
            }
            margin-right: 25px;
        }
        .moreList-content {
            textarea {
                font-size: 12px;
                border-radius: 1px;
                border-color: #ddd;
            }
            input {
                width: 434px;
                height: 26px;
                color: #666;
                padding-left: 15px;
                border: 1px solid #ddd;
                border-radius: 1px;
                margin-left: 10px;
            }
            // input.input-notification {
            //     width: 216px;
            //     height: 30px;
            //     padding: 10px;
            //     border-radius: 2px;
            //     margin-left: 0;
            // }
            .notification-err {
                color: #f76b26;
            }
            // i {
            //     color: #337ccf;
            //     font-size: 14px;
            // }
        }

        // 联系人地址簿
        // .addressBookDialog {
        //     overflow: hidden;
        //     .el-dialog {
        //         width: 900px;
        //     }
        //     .el-dialog__header {
        //         padding: 25px 30px;
        //         border-bottom: 1px solid #eee;
        //         .el-dialog__title {
        //             font-weight: normal;
        //         }
        //     }
        //     .el-dialog__body {
        //         padding: 0;
        //     }
        //     .el-dialog__footer {
        //         padding: 12px 33px 15px;
        //         border-top: 1px solid #eee;
        //     }

        //     .addressBookDialog-btn {
        //         button {
        //             width: 100px;
        //             height: 34px;
        //             padding: 0;
        //             background-color: #127fd2;
        //             font-weight: normal;
        //         }
        //     }

        // }

        // 查看签署顺序
        .SignOrderDialog {
            * {
                box-sizing: border-box;
            }

            .el-dialog__header {
                height: 65px;
                border-bottom: 1px solid #eee;
                border-radius: 2px 2px 0 0;
                background: #fff;
            }

            .el-dialog {
                background: #f6f9fc;
            }
        }

        // 防止恶意输入对话框
        .imgVaDialog {
            .el-dialog {
                width: 280px;
            }
            .el-dialog__header {
                padding: 25px 30px;
                border-bottom: 1px solid #eee;
                .el-dialog__title {
                    font-weight: normal;
                }
            }
            .el-dialog__body {
                padding: 27px 30px;
                background-color: #f6f9fc;
                .el-input {
                    width: 158px;
                    input {
                        height: 26px;
                    }
                }
                .picture-verify {
                    width: 55px;
                    height: 26px;
                    margin-top: -5px;
                }
            }
            .el-dialog__footer {
                padding: 12px 33px 15px;
                background-color: #f6f9fc;
            }
            .imgVaDialog-btn {
                button {
                    width: 100px;
                    height: 34px;
                    padding: 0;
                    background-color: #127fd2;
                    font-weight: normal;
                }
            }
        }

        // 对话框：提取签约方
        // .pickDialog {
        //     .el-dialog {
        //         width: 600px;
        //         .el-dialog__body {
        //             padding: 0 33px;
        //             margin: 33px 0;
        //             line-height: 30px;
        //             background-color: #fff;
        //             max-height: 300px;
        //             overflow: auto;
        //             .account {
        //                 width: 160px;
        //                 padding-left: 20px;
        //             }
        //         }
        //     }
        //     ul {
        //         margin-left: 19px;
        //         li {
        //             font-size: 14px;
        //             color: #333;
        //             margin-bottom: 10px;
        //             .el-checkbox {
        //                 margin-right: 10px;
        //             }
        //         }
        //     }
        //     &--tips {
        //         background: #f2f2f2;
        //         line-height: 44px;
        //         margin-top: 20px;
        //         padding-left: 20px;
        //     }
        //     .pick-add-btn {
        //         float: right;
        //         color: #1787dd;
        //         cursor: pointer;
        //     }
        // }
        .annex-list {
            .moreList-content {
                // margin-left: 36px;
                .input-wrapper:first-child{
                    margin-left:0;
                }
                .input-wrapper-title{
                    color: #333;
                }
                .el-input {
                    width: auto;
                }
                .input-name input {
                    width: 175px;
                    margin-left: 0;
                }
                .input-desc input {
                    width: 370px;
                    margin-left: 0;
                }
                // label {
                //     margin-left: 12px;
                //     color: #333;
                // }
                .annex-item {
                    padding-bottom: 10px;
                    .el-icon-ssq-guanbi {
                        font-size: 12px;
                        color: #ccc;
                        cursor: pointer;
                        padding-top: 24px;
                        line-height: 30px;
                        padding-left: 40px;
                        &:hover {
                            color: #666;
                        }
                    }
                }
            }
            .title {
                color: #999;
            }
            .add-btn {
                margin-bottom: 20px;
                display: inline-block;
                cursor: pointer;
                i,a{
                    vertical-align: middle;
                    color: #127FD2;
                }
            }
        }

        .img-wrap {
            padding-bottom: 4px;
            .add-img {
                margin-bottom: 10px;
                .add {
                    color: #2298f1;
                    display: inline-block;
                    padding: 5px 0;
                    cursor: pointer;
                    b {
                        font-weight: bold;
                    }
                }
                .tip {
                    color: #999;
                }
            }
            .file-list {
                width: 305px;
                // max-height: 100px;
                // overflow: auto;
                .li-item {
                    width: 295px;
                    margin-bottom: 10px;
                    .close {
                        color: #999;
                        float: right;
                        margin-top: 2px;
                        cursor: pointer;
                    }
                }
            }
        }
        // 账号前台代收相关样式
        .receiverListUseAccountReceptionCollection__common,
        .receiverListUseAccountReceptionCollection__Collection {
            cursor: pointer;
        }
        .receiverListUseAccountReceptionCollection__line {
            padding-left: 5px;
            padding-right: 5px;
        }
    }

    .sign-add-tip-msgbox {
        .el-dialog {
            width: 500px;
        }
    }

</style>
