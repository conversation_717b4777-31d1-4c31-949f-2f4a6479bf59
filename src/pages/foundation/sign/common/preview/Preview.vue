<!-- 合同文件预览 -->
<!-- todo：关闭按钮可抽象，三角形可抽象 -->
<template>
    <transition name="fade">
        <div v-show="isShow" :key="randomKey" class="prepare-preview">
            <div class="head">
                <span class="close thick" @click="clickClose"></span>
            </div>
            <div class="content">
                <div class="title">{{ previewDoc.name || previewDoc.fileName }}</div>
                <div class="img-list" v-for="(item, index) in imgList" :key="index">
                    <img v-lazy="{
                             src: item.imgSrc, split: 3, index,
                             total: imgList.length
                         }"
                        v-adaptSize="{data: previewDoc.page && previewDoc.page[index]}"
                        width="940"
                    >
                    <div class="page"></div>
                    <div class="num">{{ index+1 }}</div>
                </div>
            </div>
        </div>
    </transition>
</template>
<script>
import { scrollToYSmooth } from 'src/common/utils/dom.js';

export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['previewDoc', 'contractId'],
    data() {
        return {
            isShow: false,
            randomKey: Math.random(),
            imgList: [],
        };
    },
    methods: {
        open(pageIndex) {
            this.imgList = [];
            this.isShow = true;
            this.randomKey = Math.random();
            if (!this.previewDoc.pageSize) {
                this.previewDoc.pageSize = 1;
            }
            for (let i = 0; i < this.previewDoc.pageSize; i++) {
                let redirectSrc = '';
                if (this.previewDoc.imgSrcStatic) {
                    redirectSrc = this.previewDoc.page[i].highQualityPreviewUrl;
                } else if (
                    this.previewDoc.fileType === 'CONTRACT_FILE' ||
                    this.previewDoc.fileType === 'ATTACHMENT_FILE' ||
                    // /doc/detail 附件
                    this.previewDoc.fileType === 'RECEIVER_ATTACHMENT'
                ) {
                    // 判断合同文件或者是模板添加的附件
                    redirectSrc = this.$hybrid.getContractImg(this.previewDoc.page[i].highQualityPreviewUrl);
                } else {
                    // 签署页面 附件预览
                    redirectSrc = `${this.previewDoc.previewUrlPrefix}&access_token=${this.$cookie.get('access_token')}&page=${i + 1}`;
                }
                this.imgList.push({
                    imgSrc: redirectSrc,
                });
            }
            if (pageIndex) { // 如果设置了点击到对应页面
                this.$nextTick(() => {
                    const dom = document.querySelector('.prepare-preview');
                    const theImgY = document.querySelectorAll('.img-list img')[pageIndex].y;
                    const titleY = document.querySelectorAll('.content .title')[0].offsetTop;
                    scrollToYSmooth(dom, theImgY - titleY); // 滚动高度对应页面等y坐标减去title的偏移量
                });
            }
        },
        clickClose() {
            this.isShow = false;
            this.imgList = [];
        },
    },
};
</script>
<style lang="scss">
.prepare-preview {
    z-index: 1001;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.6);
    overflow: scroll;

    .head {
        z-index: 999;
        position: fixed;
        top: 0;
        width: 100%;
        height: 50px;
        background-color: #000;

        .close {
            z-index: 999;

            position: absolute;
            top: 15px;
            right: 30px;
            display: inline-block;
            width: 20px;
            height: 20px;
            overflow: hidden;
            cursor: pointer;
        }

        .thick::before,
        .thick::after {
            height: 4px;
            margin-top: -2px;
        }

        .thick::before,
        .thick::after {
            content: '';
            position: absolute;
            height: 2px;
            width: 100%;
            top: 50%;
            left: 0;
            margin-top: -1px;
            background: #fff;

        }

        .thick::before {
            transform: rotate(45deg);
        }

        .thick::after {
            transform: rotate(-45deg);
        }

    }

    .content {
        width: 1000px;
        background-color: #EAEBED;
        border-radius: 4px;
        margin: 0 auto;
        margin-top: 50px;

        .title {
            height: 60px;
            line-height: 60px;
            font-size: 24px;
            text-align: center;
        }

        .img-list {
            position: relative;
            padding: 0 30px 18px;
            text-align: center;

            .page {
                position: absolute;
                right: 30px;
                bottom: 21px;

                // background-color: #8D8E8F;

                width: 0;
                height: 0;
                border-color: #8D8E8F transparent;
                border-width: 0px 0px 42px 42px;
                border-style: solid;
            }

            .num {
                position: absolute;
                bottom: 29px;
                right: 18px;
                font-size: 9px;
                color: #fff;
                padding-right: 20px;
            }
        }
    }

    @media (min-width: 320px) and (max-width: 768px) {
        .content {
            width: 100%;
            .img-list img {
                width: 100%;
            }
        }
    }
}
</style>
