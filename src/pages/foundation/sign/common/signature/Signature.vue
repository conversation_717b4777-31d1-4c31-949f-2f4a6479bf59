<template>
    <g class="Signature-cop" :type="type" :transform="translateMatrix(1,0,0,1, x, y)">

        <!-- 头部 -->
        <g
            v-if="type == 'SEAL' || type == 'SIGNATURE'"
            style="cursor: default;"
        >
            <rect
                x="0"
                y="-26"
                :width="width"
                height="24"
                fill="#fff7b6"
                stroke="#959595"
                stroke-width="1"
            />
            <text
                x="6"
                y="-10"
                fill="#333"
                font-size="10"
                text-anchor="start"
            >
                {{ owner || '' }}
            </text>
        </g>

        <!-- 矩形背景 -->
        <rect
            fill-opacity="0.75"
            :fill="fill"
            :stroke="isFocus?'#127fd2':'transparent'"
            stroke-width="1"
            :width="width"
            :height="height"
            rx="2"
            ry="2"
            :style="isDraging?'pointer-events: none;':''"
            @click="handleClickRect"
            @mousedown="handleMousedown"
            @contextmenu.prevent="openContextmenu"
        >
        </rect>

        <!-- 日期 -->
        <text
            v-if="type=='DATE'"
            x="34"
            y="24"
            fill="#333"
            style="pointer-events: none;"
            font-size="18"
        >
            签署日期
        </text>

        <!-- 印章 -->
        <template
            v-else-if="type=='SEAL'"
        >
            <circle cx="140"
                cy="106"
                r="84"
                fill="#fff"
                opacity=".3"
                style="pointer-events: none;"
            />
            <svg class="icon" aria-hidden="true">
                <use style="pointer-events: none;"
                    x="60"
                    y="54"
                    width="160"
                    height="100"
                    :xlink:href="`#${style.type}`"
                >
                </use>
            </svg>
        </template>

        <!-- 签名 -->
        <template
            v-else-if="type=='SIGNATURE'"
        >
            <svg class="icon" aria-hidden="true">
                <use style="pointer-events: none;"
                    x="0"
                    :y="-6"
                    width="134"
                    height="86"
                    :xlink:href="`#${style.type}`"
                >
                </use>
            </svg>
        </template>

        <!-- 删除按钮 -->
        <g
            class="del-btn"
            @click="handleClickdelBtn"
        >
            <circle :cx="width" cy="0" r="11" fill="#333" opacity=".75" />
            <use style="pointer-events: none;"
                :x="width - 5"
                :y="-5"
                width="10"
                height="10"
                fill="#fff"
                xlink:href="#el-icon-ssq-guanbi"
            >
            </use>
        </g>

    </g>
</template>
<script>
export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['owner', 'isFocus', 'isDraging', 'type', 'x', 'y', 'width', 'height', 'fill'],
    data() {
        return {
        };
    },
    computed: {
        style() {
            let style = {};
            switch (this.type) {
                case 'SEAL':
                    style =  {
                        type: 'el-icon-ssq-gaizhang1',
                        // rectWidth: 320,
                        // rectHeight: 280,
                        iconWidth: 200,
                        iconHeight: 130,
                    };
                    break;
                case 'SIGNATURE':
                    style = {
                        type: 'el-icon-ssq-qianzi',
                        // rectWidth: 70,
                        // rectHeight: 46,
                        iconWidth: 100,
                        iconHeight: 70,
                    };
                    break;
                case 'DATE':
                    style = {
                        // rectWidth: 43,
                        // rectHeight: 15,
                        // iconWidth: 42,
                        // iconHeight: 14,
                    };
                    break;
            }
            return style;
        },
    },
    methods: {
        translateMatrix(a, b, c, d, e, f) {
            return `matrix(${a},${b},${c},${d},${e},${f})`;
        },
        handleClickdelBtn() {
            this.$emit('delete-signature');
        },
        handleClickRect() {
            // 暂时不启动点击拖拽
            // this.$emit('click-signature', e);
        },
        handleMousedown(e) {
            if (e.button === 0) {
                this.$emit('click-signature', e);
            }
        },
        openContextmenu(e) {
            this.$emit('openContextmenu', e);
        },
    },
};
</script>
<style lang="scss">
	.Signature-cop {
		cursor: move;
		.del-btn {
			cursor: pointer;
		}
	}
</style>
