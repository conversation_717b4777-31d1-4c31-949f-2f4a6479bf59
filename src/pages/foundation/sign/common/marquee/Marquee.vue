<template>
    <g :type="type">
        <rect :opacity="isEditing"
            :x="x"
            :y="y"
            :width="style.rectWidth"
            :height="style.rectHeight"
            fill="transparent"
            :stroke="true?'transparent':'#127fd2'"
            stroke-width="2"
            rx="2"
            ry="2"
            style="pointer-events: none;"
        />
        <circle :opacity="isEditing"
            r="3.5"
            :cx="x"
            :cy="y"
            fill="#fff"
            stroke="#127fd2"
            stroke-width="2"
            style="cursor: nwse-resize;"
        />
        <circle :opacity="isEditing"
            r="3.5"
            :cx="x+style.rectWidth"
            :cy="y"
            fill="#fff"
            stroke="#127fd2"
            stroke-width="2"
            style="cursor: nesw-resize;"
        />
        <circle :opacity="isEditing"
            r="3.5"
            :cx="x+style.rectWidth"
            :cy="y+style.rectHeight"
            fill="#fff"
            stroke="#127fd2"
            stroke-width="2"
            style="cursor: nwse-resize;"
            @mousedown="start"
            @mousemove="move"
            @mouseup="end"
        />
        <circle :opacity="isEditing"
            r="3.5"
            :cx="x"
            :cy="y+style.rectHeight"
            fill="#fff"
            stroke="#127fd2"
            stroke-width="2"
            style="cursor: nesw-resize;"
        />
    </g>
</template>
<script>
export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['marquee', 'isDraging', 'isEditing', 'type', 'x', 'y'],
    data() {
        return {
            isChanging: 0,
            startX: 0,
            startY: 0,
        };
    },
    computed: {
        style() {
            let style = {};
            switch (this.type) {
                case 'el-icon-ssq-gongzhang':
                    style =  {
                        type: 'el-icon-ssq-gaizhang',
                        rectWidth: 320,
                        rectHeight: 280,
                        iconWidth: 320,
                        iconHeight: 280,
                    };
                    break;
                case 'el-icon-ssq-bi':
                    style = {
                        type: 'el-icon-ssq-qianming',
                        rectWidth: 70,
                        rectHeight: 46,
                        iconWidth: 68,
                        iconHeight: 44,
                    };
                    break;
                case 'el-icon-ssq-riqi':
                    style = {
                        rectWidth: 43,
                        rectHeight: 15,
                        iconWidth: 42,
                        iconHeight: 14,
                    };
                    break;
                default:
                    style = {
                        rectWidth: 0,
                        rectHeight: 0,
                        iconWidth: 0,
                        iconHeight: 0,
                    };
            }
            return style;
        },
    },
    methods: {
        start(e) {
            this.isChanging = 1;
            this.startX = e.x;
            this.startY = e.y;
        },
        move(e) {
            if (this.isChanging) {
                const disX = e.x - this.startX;
                const disY = e.y - this.startY;
                this.$emit('change-size', disX, disY);
            }
        },
        end() {
            this.isChanging = 0;
        },
    },
};
</script>
<style lang="scss">

</style>
