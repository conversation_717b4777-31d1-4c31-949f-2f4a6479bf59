<!-- 签署——合同信息组件 -->
<template>
    <div class="contractInfo-comp" :class="{'diseditable': !editable}">
        <el-form class="clear" :class="{['lang_' + $t('lang')]: true}">
            <el-form-item class="contract-name-item">
                <div class="label">
                    <span class="must">*</span>{{ $t('contractInfo.contractName') }}
                    <el-tooltip
                        class="item"
                        effect="dark"
                        :content="$t('contractInfo.contractNameTooltip')"
                        placement="top"
                    >
                        <i class="el-icon-ssq-bangzhu tips"></i>
                    </el-tooltip>
                </div>

                <el-input
                    v-model.trim="contractTitle"
                    :maxlength="100"
                    @change="onTitleChange"
                >
                    <ElIDelete slot="icon"></ElIDelete>
                </el-input>
                <div class="contract-name-err err etc-sigle"
                    v-show="contractTitleErr"
                >
                    {{ contractTitleErr }}
                </div>
            </el-form-item>
            <!-- 合同类型 -->
            <el-form-item class="contracts-types"
                v-if="$store.state.commonHeaderInfo.userType == 'Enterprise' && contractsTypes.length && checkFeat.contractType"
            >
                <div class="label">
                    {{ $t('contractInfo.contractType') }}
                </div>
                <el-select v-model="contractTypeId" :placeholder="$t('contractInfo.toSelect')" popper-class="sign-el-select">
                    <el-option
                        v-for="item in contractsTypes"
                        :key="item.contractTypeId"
                        :label="item.contractTypeName"
                        :value="item.contractTypeId"
                    >
                    </el-option>
                </el-select>
                <div class="err">
                    <template v-if="contractTypeNotExist">{{ $t('contractInfo.contractTypeErr') }}</template>
                </div>
            </el-form-item>
            <el-form-item>
                <div class="label">
                    {{ $t('contractInfo.signDeadLine') }}
                    <el-tooltip
                        class="item"
                        effect="dark"
                        :content="$t('contractInfo.signDeadLineTooltip')"
                        placement="top"
                    >
                        <i class="el-icon-ssq-bangzhu tips"></i>
                    </el-tooltip>
                </div>
                <el-date-picker
                    :editable="false"
                    v-model="expiredDate"
                    type="date"
                    format="yyyy-MM-dd"
                    :placeholder="$t('contractInfo.selectDate')"
                    :picker-options="datePickerOpts"
                >
                </el-date-picker>
                <div class="err"></div>
            </el-form-item>
            <!-- 合同到期日 不再根据乐高城配置-->
            <!-- <el-form-item v-module-id.prep_main_dueDate> -->
            <el-form-item v-if="$store.state.commonHeaderInfo.userType == 'Enterprise'">
                <div class="label">
                    {{ $t('contractInfo.contractExpireDate') }}
                    <el-tooltip
                        class="item"
                        effect="dark"
                        :content="$t('contractInfo.expireDateTooltip')"
                        placement="top"
                    >
                        <i class="el-icon-ssq-bangzhu tips"></i>
                    </el-tooltip>
                </div>
                <el-date-picker
                    :editable="false"
                    v-model="contractLifeEnd"
                    type="date"
                    format="yyyy-MM-dd"
                    :placeholder="$t('contractInfo.notNecessary')"
                    :picker-options="datePickerOpts"
                >
                </el-date-picker>
                <div class="err"></div>
                <div class="date-tips" v-show="dateTipsVisible" @click.prevent.stop="dateTipsVisible = false">{{ $t('contractInfo.dateTips') }}</div>
            </el-form-item>
            <el-form-item v-if="$store.state.commonHeaderInfo.userType == 'Enterprise'">
                <div class="label">{{ $t('contractInfo.internalNumber') }}</div>
                <el-input
                    v-model.trim="customContractId"
                    :maxlength="100"
                    :placeholder="$t('contractInfo.notNecessary')"
                >
                    <ElIDelete slot="icon"></ElIDelete>
                </el-input>
                <div class="err"></div>
            </el-form-item>
            <el-form-item v-for="(item, index) in describeFields" :key="index">
                <div class="label">
                    <span class="must" v-if="item.necessary">*</span>
                    {{ item.fieldName }}
                </div>
                <el-input
                    v-if="item.bizFieldType === 'TEXT'"
                    v-model.trim="item.fieldValue"
                    :maxlength="100"
                    :placeholder="item.necessary ? $t('contractInfo.necessary') : $t('contractInfo.notNecessary')"
                >
                    <ElIDelete slot="icon"></ElIDelete>
                </el-input>
                <el-select v-if="item.bizFieldType === 'SINGLE_BOX'" v-model="item.fieldValue" popper-class="sign-el-select">
                    <el-option
                        v-for="(option, i) in item.buttons"
                        :key="i"
                        :label="option"
                        :value="option"
                    >
                    </el-option>
                </el-select>
                <el-date-picker
                    v-if="item.bizFieldType === 'BIZ_DATE'"
                    v-model="item.fieldValue"
                    type="date"
                    format="yyyy-MM-dd"
                    :placeholder="item.necessary ? $t('contractInfo.necessary') : $t('contractInfo.notNecessary')"
                    :picker-options="datePickerOpts"
                >
                </el-date-picker>
                <div class="err"></div>
            </el-form-item>
        </el-form>
    </div>
</template>
<script>
import { stringLangTransformMixin } from 'src/mixins/stringLangTransform';
import ElIDelete from 'components/el_i_delete/ElIDelete.vue';
import { mapGetters } from 'vuex';
export default {
    components: {
        ElIDelete,
    },
    mixins: [stringLangTransformMixin],
    props: {
        value: {
            type: String,
            default: '',
        },
        editable: {
            type: Boolean,
            default: true,
        },
        getDefaultConfig: Promise,
    },
    data() {
        return {
            contractTitle: '',
            contractTitleErr: '',
            // 签约截止时间
            expiredDate: Date.now() + 30 * 24 * 60 * 60 * 1000,
            datePickerOpts: {
                disabledDate(time) {
                    return time.getTime() < Date.now() - 8.64e7;
                },
            },
            // 合同到期日
            contractLifeEnd: '',
            /*  folderId		string
					folderName		string
					remark 			string
				*/
            contractsTypes: [],
            contractTypeId: '',
            dateTipsVisible: false,
            describeFields: [], // 描述字段
            customContractId: '',
        };
    },
    computed: {
        ...mapGetters(['checkFeat']),
        contractTypeNotExist() {
            const contractTypeErr = !this.contractsTypes.map(d => d.contractTypeId).includes(this.contractTypeId);
            this.$emit('update:contractTypeErr', contractTypeErr);
            return contractTypeErr;
        },
    },
    watch: {
        value(v) {
            this.contractTitleErr = v;
        },
    },
    methods: {
        checkDescribeFields() {
            const flag = this.describeFields.findIndex(el => {
                return el.necessary && !el.fieldValue;
            });
            return flag < 0;
        },
        onTitleChange() {
            if (/【|\[|】|\]]/.test(this.contractTitle)) {
                this.contractTitleErr = this.$t('contractInfo.contractTitleErr');
            } else if (this.contractTitle.length > 100) {
                this.contractTitleErr = this.$t('contractInfo.contractTitleLengthErr');
            } else {
                this.contractTitleErr = '';
            }
            this.$emit('input', this.contractTitleErr);
        },
        resetData(config) {
            config.contractTitle && (this.contractTitle = config.contractTitle);
            config.contractTypeId && (this.contractTypeId = config.contractTypeId);
        },
    },
    created() {
        const getInfo = this.$http.get(`${signPath}/contracts/${this.$route.query.contractId}`);
        const getTypes = this.$http.get(`${signPath}/contract-type-new/`);
        Promise.all([getInfo, getTypes, this.getDefaultConfig])
            .then(res => {
                res[2].expiredDate && (this.expiredDate = res[2].expiredDate);
                res[2].contractLifeEnd && (this.contractLifeEnd = res[2].contractLifeEnd);

                const expiredDate = res[0].data.signDeadline;
                const contractLifeEnd = res[0].data.contractLifeEnd;
                const contractTypeId = res[0].data.contractTypeId;
                const signOrdered = res[0].data.signOrdered;
                this.contractTitle = res[0].data.contractTitle;
                this.describeFields = res[0].data.describeFields || [];
                this.customContractId = res[0].data.customContractId;
                this.contractsTypes = this.handleRoleNameTransform(res[1].data, ['未分类'], [this.$t('CSBusiness.unSort')]);
                if (expiredDate) {
                    this.expiredDate = expiredDate;
                }
                if (contractLifeEnd) {
                    if (Date.now() < contractLifeEnd) {
                        this.contractLifeEnd = contractLifeEnd;
                    }
                }
                if (this.contractsTypes.length) {
                    this.contractTypeId = contractTypeId || res[1].data[0].contractTypeId;
                }
                this.$emit('sign-ordered', signOrdered);
            })
            .finally(() => {
                this.$emit('loaded');
            });
    },
};
</script>
<style lang="scss">
	.contractInfo-comp {
		box-sizing: border-box;
		padding-top: 17px;
        padding-bottom: 9px;
        padding-left: 28px;
		margin-bottom: 30px;
		// background-color: #f4f4f4; // 跟ui商量下，这个颜色不好看
		background-color: #f8f8f8;
		// border: 1px solid $border-color;
		.el-form-item {
			float: left;
			position: relative;
			.err {
				height: 14px;
				line-height: 18px;
				font-size: 12px;
				color: #f76b26;
				margin-top: -5px;
			}
            .label {
                line-height: 18px;
                font-size: 12px;
                color: #666;
                .must{
                    vertical-align: middle;
                    padding: 0 3px;
                    font-size: 12px;
                    color: #FF6432;
                }
            }
		}

		.contract-name-item {
			.el-input__inner {
				padding-right: 28px;
			}
		}

		.tips {
			margin-left: 2px;
			font-size: 12px;
			color: #666;
			cursor: pointer;
		}

		i {
			color: #ccc;
		}
		label {
			font-size: 12px;
			color: #000;
		}
		.el-input {
			// width: 317px;
            width: 300px;
			i {
				margin-top: 1px;
			}
		}
		.el-input .el-input__inner {
			height: 28px;
			font-size: 12px;
			padding-top: 4px;
			border: 1px solid #ddd;
			border-radius: 2px;
		}
		.el-date-editor--datetime {
			.el-input__inner {
				padding-top: 5px;
			}
		}
		.el-form-item {
			// width: 33.3%;
            margin-right: 20px;
			margin-bottom: 2px;
		}
		.el-form-item__content {
			// margin-left: 107px;
		}

		.contracts-types {
			.el-form-item__content {
				position: relative;
				// margin-bottom: 10px;

				.err{
					position: absolute;
					left: 0;
					top: 36px;
				}

				i {
					color: #666;
				}
			}
		}
		.date-tips {
			position: absolute;
			font-size: 12px;
			background: rgba(51, 51, 51, 0.85);
			padding: 0 24px;
			color: #fff;
			border-radius: 4px;
			height: 28px;
			line-height: 28px;
			&:before {
				display:block;
				content:'';
				border-width: 8px 8px 8px 8px;
				border-style: solid;
				border-color: transparent transparent rgba(51, 51, 51, 0.85) transparent;
				/* 定位 */
				position:absolute;
				left:40%;
				top:-16px;
			}
		}
		.lang_ru {
			.el-form-item__label {
				width: 150px !important;
			}
			.el-form-item__content {
				margin-left: 150px !important;
			}
		}
	}
</style>
