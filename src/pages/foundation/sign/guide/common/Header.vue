<template>
    <div class="sign-guide-header">
        <div class="content">
            <h1 v-if="isPC" class="ssq-logo inline-block baseline-middle">
                <router-link tag="div" to="/account-center/home" class="ilBlock">
                    <img class="default-logo cursor-point" :src="GLOBAL.WHITE_LOGO" width="86" height="37" alt="">
                </router-link>
            </h1>
            <span v-if="isPC" class="line inline-block baseline-middle">line</span>
            <p class="inline-block baseline-middle certification-type">
                <!-- 签署引导 -->{{ $t('entAuth.signIntro') }}
            </p>
            <div v-if="isPC" class="right-content">
                <a class="cursor-point common-font-color help" target="_blank" href="http://bestsign.udesk.cn/hc"><!-- 帮助 -->{{ $t('entAuth.help') }}</a>
                <span> | </span>
                <span class="hotline"><!-- 服务热线 -->{{ $t('entAuth.hotline') }}：400-993-6665</span>
            </div>
        </div>
    </div>
</template>
<script>
import { isPC } from 'src/common/utils/device.js';
export default {
    data() {
        return {
            userInfo: {
                currentEntId: '',
                hasManager: false,
                enterprises: [],
                platformUser: {
                    fullName: '',
                },
                userType: '',
            },
            isPC: isPC(),
        };
    },
    computed: {
        commonHeaderInfo: function() {
            return this.$store.state.commonHeaderInfo;
        },
    },
    watch: {
        commonHeaderInfo: {
            handler(val) {
                this.userInfo = val;
            },
            deep: true,
        },
    },
    methods: {
        toLogout() {
            this.$router.push('/login');
        },
    },
    created: function() {
        this.userInfo = this.commonHeaderInfo;
    },
};
</script>
<style lang="scss">
	$uc-header-height: 63px;

	.sign-guide-header {
		height: $uc-header-height;
		background-color: #002b45;
		font-size: 12px;
		.inline-block {
			display: inline-block;
		}
		.cursor-point {
			cursor: pointer;
		}
		.baseline-middle {
			vertical-align: middle;
		}
		.content {
			position: relative;
			width: 1000px;
			margin: 0 auto;
		}
		.ssq-logo {
			width: 92px;
			text-align: right;
			color: #fff;
			i {
				font-size: 30px;
			}
		}
		.line {
			height: 30px;
			margin-left: 20px;
			margin-right: 17px;
			border-right: 1px solid #4d6b7d;
			text-indent: -1000px;
		}
		.certification-type {
			font-size: 16px;
			font-weight: bold;
			color: #fff;
			line-height: $uc-header-height;
		}

		.right-content {
			position: absolute;
			right: 0;
			top: 20px;
			// height: 100%;
			.user-name {
				padding-left: 18px;
				color: #fff;
			}
			span:nth-child(2),span:last-child {
				color: #fff;
			}
			span:nth-child(2n) {
				padding-left: 7px;
				padding-right: 7px;
			}
			span:nth-child(4),span:nth-child(6) {
				color: #4d6b7d;
			}
		}

		/*
			手机屏幕适配
		*/

		@media (min-width: 320px) and (max-width: 768px) {

			.content {
				width: auto;
				text-align: center;
			}

		}
	}
</style>
