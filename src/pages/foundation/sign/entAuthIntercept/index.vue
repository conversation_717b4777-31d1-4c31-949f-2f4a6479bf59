<template>
    <div class="auth-intercept-container">
        <div class="intercept-container-icon">
            <img :src="GLOBAL.LOGO" height="60" alt="logo">
        </div>
        <div v-if="!askSignVis">
            <div class="intercept-container-content">
                <p class="container-content-title">{{ letterSender }} {{ $t('authIntercept.title') }}</p>
                <p class="container-content-name" v-if="inputEnterpriseName">{{ inputEnterpriseName }}</p>
                <p class="container-content-tip1"> {{ $t('authIntercept.authTip') }}</p>
                <p class="container-content-tip2">{{ $t('authIntercept.viewAndSign') }}</p>
                <p class="container-content-tip3">{{ $t('authIntercept.tips2') }}</p>
            </div>
            <div class="intercept-container-btn-area">
                <el-button class="intercept-container-btn1" type="primary" @click.stop.prevent="askSignVis = true">{{ $t('authIntercept.requestOtherAnth') }}</el-button>
                <el-button class="intercept-container-btn2" type="primary" @click="goToAuth">{{ $t('authIntercept.goAuth') }}</el-button>
            </div>
        </div>
        <AskSign
            v-if="askSignVis"
            :visable="askSignVis"
            :entName="inputEnterpriseName"
            :contractId="contractId"
            @close="askSignVis = false"
        ></AskSign>
    </div>
</template>

<script>
import AskSign from 'src/components/askOtherAuth/index.vue';
import { createEnterpriseMinxin } from 'src/mixins/index.js';
export default {
    name: 'EntAuthIntercept',
    components: {
        AskSign,
    },
    mixins: [createEnterpriseMinxin],
    data() {
        return {
            letterSender: '',
            contractId: '',
            askSignVis: false,
            inputEnterpriseName: '',
        };
    },
    methods: {
        // 初始化页面数据
        initPageData() {
            const params = JSON.parse(sessionStorage.getItem('entAuthIntercept'));
            this.letterSender = params.letterSender;
            this.contractId = params.contractId;
            this.inputEnterpriseName = params.inputEnterpriseName;
        },
        goToAuth() {
            // 引导企业去实名。如果企业都已实名，新建一个企业，去实名。如果存在未存在未实名身份A, 切换到未实名身份A，去实名
            this.switchIdentityAndAuth().then(() => {
                this.$router.push('/ent/sign/guide');
            });
        },
    },
    created() {
        this.initPageData();
    },
};
</script>

<style lang="scss">
.auth-intercept-container{
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #333;
    font-weight: 400;
    font-size: 14px;
    .intercept-container-icon {
        margin: 70px 0;
    }
    .container-content-name {
        color: #999;
    }
    .container-content-tip1 {
        margin-top: 20px;
    }
    .container-content-tip3 {
        color: #999;
        margin: 20px 0;
    }
    .intercept-container-btn {
        margin-top: 40px;
    }
}
</style>
