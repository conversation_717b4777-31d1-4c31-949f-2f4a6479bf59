<!--
页面：api 合同预览页面, 通过单点登录进入
链接demo：https://ent.bestsign.info/sso/login?**
需求wiki: http://wiki.bestsign.tech/pages/viewpage.action?pageId=11575034
-->
<template>
    <section class="temp-field-Body-cpn temp-preview" v-loading="isLoading">
        <header>{{ templateName }}</header>
        <!-- 选择缩放下拉框 -->
        <div class="scale">
            <el-select class="scale-opt"
                v-model="zoom"
                popper-class="sign-el-select scale-opts-el-select"
                size="small"
                placeholder="50%"
            >
                <el-option
                    v-for="item in zoomOpts"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                >
                </el-option>
            </el-select>
        </div>
        <section class="temp-preview__content">
            <div class="temp-preview__left" @scroll="handleMainScroll">
                <div class="temp-preview__scroll__main">
                    <img
                        alt=""
                        v-for="(item, index) in contractList"
                        :key="item"
                        :src="singleImgSrc(item)"
                        :index="`${index + 1}`"
                        :id="`main_img_${index + 1}`"
                        :style="{
                            'width': `${width}px`,
                            'height': `${height}px`
                        }"
                        :class="{'initinal': zoom === '适合宽度'}"
                    >
                </div>
            </div>
            <div class="temp-preview__right">
                <p class="title">文档</p>
                <p class="subtitle">页数：{{ length }} 页</p>
                <ul class="temp-preview__scroll__mini">
                    <li
                        v-for="(item, index) in contractList"
                        :key="index"
                        :index="`${index + 1}`"
                        @click="handleMiniClick(index + 1)"
                        :class="{'current': currentIndex === index+1}"
                    >
                        <img
                            :src="singleImgSrc(item)"
                            :index="`${index + 1}`"
                            :id="`mini_img_${index + 1}`"
                            alt=""
                        >
                    </li>
                </ul>
            </div>
        </section>
        <RegisterFooter></RegisterFooter>
    </section>
</template>
<script>
import RegisterFooter from 'components/register_footer/RegisterFooter.vue';
import { throttle } from 'src/common/utils/fn.js';
export default {
    components: {
        RegisterFooter,
    },
    data() {
        return {
            // zoom select
            zoom: '适合宽度',
            zoomOpts: [{ value: 0.25, label: '25%' }, { value: 0.5, label: '50%' }, { value: 0.75, label: '75%' }, { value: 1, label: '100%' }, { value: 1.25, label: '125%' }, { value: 1.5, label: '150%' }, { value: 2, label: '200%' }, { value: 4, label: '400%' }, { value: '适合宽度' }],
            templateName: '',
            contractList: [],
            currentIndex: 1,
            length: 0,
            isLoading: true,
            initWidth: 0,
            initHeight: 0,
            width: 'auto',
            height: 'auto',
            hybirdAjaxHeader: {},
        };
    },
    watch: {
        zoom(newValue) {
            if (!this.initWidth) {
                const eImg = document.getElementById('main_img_1');
                this.initWidth = eImg.offsetWidth;
                this.initHeight = eImg.offsetHeight;
            }
            let scale;
            if (newValue === '适合宽度') {
                scale = 1;
            } else {
                scale = parseFloat(newValue);
            }
            this.width = scale * this.initWidth;
            this.height = scale * this.initHeight;
        },
    },
    methods: {
        singleImgSrc(item) {
            // 判断混1的base64和混3、公有云的图片url
            return item.includes('data:image/jpg;base64') ? item : this.$hybrid.getContractImg(item);
        },
        handleMainScroll: throttle(function() {
            const scrollTop = document.querySelector('.temp-preview__left').scrollTop;
            const eleMain = document.querySelector('.temp-preview__scroll__main');
            const everyImgHeight = eleMain.offsetHeight / this.length;
            this.currentIndex = Math.ceil(scrollTop / everyImgHeight) + 1;
            if (scrollTop < everyImgHeight) {
                this.currentIndex = 1;
            }
            if (this.currentIndex > this.length) {
                this.currentIndex = this.length;
            }
        }, 10),
        handleMiniClick(index) {
            this.currentIndex = index;
            const maxImg = document.getElementById(`main_img_${this.currentIndex}`);
            if (maxImg) {
                maxImg.scrollIntoView();
            }
        },
    },
    created() {
        const { templatePreviewId, templateId } = this.$route.query;
        this.$http(`/template-api/templates/${templateId}?type=send`).then(({ data }) => {
            this.templateName = data.templateName;
        });

        // 获取合同图片 list
        this.$hybrid.tempPreviewGetContractImgList(templateId, templatePreviewId)
            .then(res => {
                this.contractList = res.list;
                this.length = res.pageSize;
            }).finally(() => {
                this.isLoading = false;
            });
    },
};
</script>
<style lang="scss">
.temp-preview {
    header {
        background-color: #00263c;
        height: 50px;
        line-height: 50px;
        width: 100%;
        padding: 0 50px;
        color: #ffffff;
        z-index: 100;
        position: fixed;
        top: 0;
    }
    &__content {
        height: 100%;
        width: 100%;
        position: relative;
        background-color: #f6f6f6;
        img {
            display: block;
            height: auto;
        }
    }
    &__left {
        height: 100%;
        overflow: auto;
        margin-right: 211px;
    }
    &__right {
        position: absolute;
        width: 210px;
        height: 100%;
        top: 0;
        right: 0;
        border-left: 1px solid #ddd;
        height: 100%;
        overflow: auto;
        .title {
            height: 38px;
            line-height: 38px;
            font-size: 14px;
            font-weight: 700;
            padding-left: 20px;
            padding-right: 15px;
            border-bottom: 1px solid #ddd;
        }
        .subtitle {
            margin: 10px 40px;
            height: 18px;
            color: #999;
            font-size: 12px;
        }
    }
    &__scroll {
        &__main {
            position: relative;
            img {
                margin: 20px auto;
                position: relative;
                &.initinal {
                    max-width: 100%;
                }
            }
        }
        &__mini {
            li {
                list-style: none;
                margin: 10px 40px;
                border: 1px solid #c5c5c5;
                position: relative;
                overflow: auto;
                &.current {
                        border: 1px solid #0072cf;
                }
                &::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 15px;
                    width: 0;
                    height: 0;
                    border-top: 18px solid transparent;
                    border-left: 18px solid #999;
                    border-right: 18px solid transparent;
                    border-bottom: 18px solid #999;
                    margin-left: -15px;
                }
                &::before {
                    content: attr(index);
                    position: absolute;
                    bottom: 2px;
                    left: 6px;
                    z-index: 1;
                    color: #ffffff;
                    font-weight: lighter;
                    font-size: 14px;
                }
                img {
                    width: 130px;
                    height: 184px;
                    max-width: 100%;
                }
            }
        }
    }
    .register-footer {
        position: fixed;
        bottom: 0;
    }
}
// 移动端适配
@media (max-width: 768px) {
    .register-footer, .temp-preview__right, .scale {
        display: none !important;
    }
    .temp-preview__left {
        margin-right: 0;
    }
}
</style>
