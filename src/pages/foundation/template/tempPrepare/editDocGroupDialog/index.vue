<template>
    <el-dialog
        class="edit-doc-group-dialog"
        :title="`${isEdit ? '编辑':'新增'}模版组合`"
        :visible.sync="visible"
        size="tiny"
        @close="handleClose"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
    >
        <el-form ref="doc-group-form" :model="docGroupInfo" :rules="rules" label-position="right" label-width="110px">
            <el-form-item label="模版组合名称" prop="docGroupName">
                <el-input class="input-part" v-model="docGroupInfo.docGroupName"></el-input>
            </el-form-item>
            <el-form-item label="模版组合编号" v-if="docGroupInfo.docGroupId">
                <el-input class="input-part" readonly disabled v-model="docGroupInfo.docGroupId"></el-input>
            </el-form-item>
            <el-form-item label="模版文档组合">
                <div class="doc-select input-part">
                    <div class="selected-field" :class="{'none': !docGroupInfo.documents.length}">
                        <template v-if="!docGroupInfo.documents.length">
                            请选择模版组合文档
                        </template>
                        <div
                            v-else
                            class="tag"
                            :key="index"
                            v-for="(item, index) in docGroupInfo.documents"
                        >
                            {{ item.documentName }} <i @click="removeFile(item)">&times;</i>
                        </div>
                    </div>
                    <div class="search-checkbox">
                        <el-input class="search-input" placeholder="请输入搜索内容" v-model="searchContent" clearable />
                        <div class="file-list">
                            <div v-if="isLoading" class="no-file">加载中...</div>
                            <template v-else-if="filteredFileList.length">
                                <el-checkbox
                                    v-for="item in filteredFileList"
                                    :key="item.documentId"
                                    v-model="item.checked"
                                    @input="clickFileHandler(item)"
                                    class="file-item"
                                    :label="item.documentId"
                                >
                                    {{ item.fileName }}
                                </el-checkbox>
                            </template>
                            <div v-else-if="searchContent && !filteredFileList.length" class="no-file">没有找到匹配的文档</div>
                            <div v-else class="no-file">尚未上传模版文档</div>
                        </div>
                    </div>
                </div>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="handleClose">取消</el-button>
            <el-button type="primary" @click="handleConfirm">保存</el-button>
        </div>
    </el-dialog>
</template>

<script>
export default {
    name: 'EditDocGroupDialog',
    props: {
        show: {
            type: Boolean,
            default: false,
        },
        isEdit: {
            type: Boolean,
            default: false,
        },
        editingDocGroup: {
            type: Object,
            default: () => {},
        },
        docList: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        const { docGroupName = '', docGroupId = '', documents = [] } = this.editingDocGroup;
        return {
            visible: this.show,
            isLoading: false,
            searchContent: '',
            docGroupInfo: {
                docGroupName,
                docGroupId,
                documents: [...documents],
            },
            rules: {
                docGroupName: [
                    { required: true, message: '请输入模版组合名称', trigger: 'blur' },
                ],
            },
        };
    },
    computed: {
        filteredFileList() {
            return this.docList.map(doc => {
                doc.checked = this.docGroupInfo.documents.some(a => a.documentId === doc.documentId);
                return doc;
            }).filter(a => a.fileName.includes(this.searchContent));
        },
    },
    watch: {
        show(v) {
            this.visible = v;
        },
    },
    methods: {
        removeFile(item) {
            item.checked = false;
            const idx = this.docGroupInfo.documents.findIndex(a => a.documentId === item.documentId);
            this.docGroupInfo.documents.splice(idx, 1);
        },
        clickFileHandler(item) {
            if (item.checked) {
                this.docGroupInfo.documents.push({
                    documentId: item.documentId,
                    documentName: item.fileName,
                });
            } else {
                this.removeFile(item);
            }
        },
        handleClose() {
            this.visible = false;
            this.$emit('close');
        },
        handleConfirm() {
            this.$refs['doc-group-form'].validate(valid => {
                if (!valid) {
                    return false;
                } // 未校验通过

                const { docGroupName, docGroupId, documents = [] } = this.docGroupInfo;
                const paramData = {
                    docGroupName,
                    documentIds: documents.map(doc => doc.documentId),
                    templateId: this.$route.query.templateId,
                    docGroupId,
                };
                const url = docGroupId ? '/template-api/templates/document-group/update' : '/template-api/templates/document-group/add';
                this.$http.post(url, paramData).then((res) => {
                    this.handleClose();
                    this.$emit('confirm', res.data);
                });
            });
        },
    },
};
</script>

<style lang="scss">
.edit-doc-group-dialog .el-dialog {
    width: 650px;
    .el-dialog__header {
        padding: 20px;
        border-bottom: 1px solid #eee;
    }
    .el-dialog__body {
        padding: 30px 50px 0;
        .el-form {
            .input-part {
                display: inline-block;
                width: calc(100% - 40px);
            }
            .doc-select {
                .selected-field {
                    font-size: 12px;
                    padding-right: 10px;
                    background: #fff;
                    color: #d9d9d9;
                    max-height: 110px;
                    overflow: auto;
                    line-height: 30px;
                    border: 1px solid rgba(0, 0, 0, 0.15);
                    margin-bottom: 4px;
                    border-radius: 4px;
                    &.none {
                        text-indent: 12px;
                        cursor: default;
                    }
                    .tag {
                        cursor: default;
                        margin: 3px 0 3px 10px;
                        height: 24px;
                        line-height: 24px;
                        height: 24px;
                        display: inline-block;
                        vertical-align: middle;
                        background-color: #f4f9fc;
                        padding: 0 10px 0 8px;
                        border-radius: 4px;
                        color: rgb(0, 0, 0);
                        i {
                            cursor: pointer;
                            font-style: normal;
                            color: #9b9b9b;
                            display: inline-block;
                            width: 8px;
                            text-align: center;
                            &:hover {
                                font-weight: bold;
                                color: #666;
                            }
                            // float: right;
                        }
                    }
                }

                .file-list {
                    height: 210px;
                    overflow: auto;
                    border: 1px solid rgba(0, 0, 0, 0.15);
                    z-index: 1;
                    position: relative;
                    top: -1px;
                    background: #fff;
                    .file-item {
                        display: block;
                        height: 32px;
                        line-height: 32px;
                        padding: 0 12px;
                        margin: 0;
                        .el-checkbox__label {
                            padding-left: 10px;
                            font-size: 12px;
                        }
                        &:hover {
                            background-color: #f8f8f8;
                            color: #127fd2;
                        }
                    }
                }
                .no-file {
                    padding: 10px;
                    color: #b7b7b7;
                }
            }
        }
    }
    .dialog-footer {
        .el-button--primary {
            background: #127FD2;
        }
    }
}
</style>
