<template>
    <div class="doc-group">
        <el-table
            :data="docGroupData"
            :empty-text="`您已上传了${docList.length}个文档，且未有任何模板组合`"
            ref="docGroupTable"
            style="width: 100%"
            class="doc-group-table"
        >
            <el-table-column
                label="模版组合名称"
                prop="docGroupName"
            ></el-table-column>
            <el-table-column
                label="模版组合编号"
                prop="docGroupId"
            ></el-table-column>
            <el-table-column
                label="模版文档组成"
            >
                <template slot-scope="scope">
                    <p class="doc-preview"
                        v-for="file in scope.row.documents"
                        :key="file.documentId"
                        @click="previewDoc(file.documentId)"
                    >
                        {{ file.documentName }}
                    </p>
                </template>
            </el-table-column>
            <el-table-column
                label="操作"
                width="120"
            >
                <template slot-scope="scope">
                    <el-button @click="handleEdit(scope.$index)" type="text" size="small">编辑</el-button>
                    <el-button @click="handledelete(scope.$index)" type="text" size="small">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-button class="add-btn" @click="addDocGroupHandler" type="primary">新增模版组合</el-button>
        <EditDocGroupDialog
            v-if="showAddDialog"
            :show="showAddDialog"
            :docList="docList"
            :editingDocGroup="docGroupData[editIndex] || {}"
            @confirm="handleEditConfirm"
            @close="showAddDialog=false;"
        >
        </EditDocGroupDialog>
    </div>
</template>

<script>
import EditDocGroupDialog from '../editDocGroupDialog';

export default {
    name: 'DocGroupManage',
    components: {
        EditDocGroupDialog,
    },
    props: {
        docList: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            templateId: this.$route.query.templateId || '',
            showAddDialog: false,
            editIndex: -1,
            docGroupData: [],
        };
    },
    watch: {
        docList() {
            this.getDocGroupList(); // 文件数据更新时，重新获取组合列表数据
        },
    },
    methods: {
        addDocGroupHandler() {
            this.editIndex = -1;
            this.showAddDialog = true;
        },
        handledelete(index) {
            this.$http.delete(`/template-api/templates/document-group?documentGroupId=${this.docGroupData[index].docGroupId}`)
                .then(() => {
                    this.docGroupData.splice(index, 1);
                    this.$MessageToast.success('删除模版组合成功');
                });
        },
        handleEditConfirm(data) {
            if (this.editIndex === -1) {
                this.docGroupData.push({ ...data });
            } else {
                this.$set(this.docGroupData, this.editIndex, data);
            }
        },
        handleEdit(index) {
            this.showAddDialog = true;
            this.editIndex = index;
        },
        // 预览文档
        previewDoc(documentId) {
            const doc = this.docList.filter(d => d.documentId === documentId)[0];
            this.$emit('previewDoc', doc);
        },
        getDocGroupList() {
            this.$http.get(`/template-api/templates/document-group?templateId=${this.templateId}`)
                .then(res => {
                    this.docGroupData = res.data || [];
                });
        },
    },
    created() {
        this.getDocGroupList();
    },
};
</script>

<style lang="scss">
.doc-group {
    .add-btn {
        margin-top: 15px;
        background: #127FD2;
        border-radius: 2px;
        border-radius: 2px;
    }
    .el-table {
        &__header th {
            background-color: #fbfbfb;
            .cell {
                font-size: 14px;
                font-weight: normal;
                color: #666666;
                background-color: #fbfbfb;
            }
        }
        &__row td {
            padding: 10px 0;
            .cell {
                color: #333333;
                .el-button {
                    font-size: 14px;
                    color: #127FD2;
                }
                .doc-preview {
                    cursor: pointer;
                    color: #127FD2;
                }
            }
        }
        &__empty-block {
            height: 110px;
        }
    }
}
</style>
