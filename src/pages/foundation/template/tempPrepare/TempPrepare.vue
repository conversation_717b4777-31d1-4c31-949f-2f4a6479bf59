<!-- 模板——上传文件页 -->
<template>
    <div
        class="tmp-prepare-page footer-follow-page"
        v-show="isVersionJudged"
        v-loading.body.fullscreen="isLoading"
    >
        <SignHeader
            class="header"
            :title="templateStatus == 'edit' ? '设置模板' : '使用模板'"
            @to-back="toBack"
            @to-next="toNext"
            :showVideoBtn="true"
            @showVideo="videoVisible = !videoVisible"
            :backConfig="ssoTmpEdit.tmpEdit_1_back"
        >
        </SignHeader>

        <div class="prepare-container footer-follow-page-container">
            <div class="prepare-upload">
                <h1 class="upload-title">
                    <span class="order-num">第一步：</span>上传文件
                    <template v-if="templateStatus === 'edit'">
                        <el-button class="right-config" type="text" @click="onClick">高级配置<span style="color:red">（限免）</span></el-button>
                        <template v-if="isDocGroupLinkBtnVisible">
                            <el-button class="right-config gap-line" type="text" v-if="allowComposeDocument">|</el-button>
                            <el-button ref="toDocGroup" class="right-config" type="text" @click="toGroupManagePos" v-if="allowComposeDocument">模版组合管理</el-button>
                        </template>
                    </template>
                </h1>
                <AdvancedConfigDialog
                    :showAttachment="canUploadAccessoryBtn && !isDynamicTemplate"
                    :noticeSpecificUser.sync="noticeSpecificUser"
                    :notAllowNotify.sync="notAllowNotify"
                    :notifyInEnglish="notifyInEnglish"
                    :canModifyWhenUsed="canModifyWhenUsed"
                    :canUseAttachment="canUseAttachment"
                    :allowComposeDocument="allowComposeDocument"
                    :showAllowComposeDocument="checkFeat.templateCombination && !isDynamicTemplate && !(!!commonHeaderInfo.hybridServer && this.$hybrid.isAlpha())"
                    @updateSwitch="handleUpateSwitch"
                    @close="advancedDialogVisible=false"
                    :isShow="advancedDialogVisible"
                >
                </AdvancedConfigDialog>
            </div>
            <UpLoad
                ref="upload"
                :hybridServer="hybridServer"
                :templateStatus="templateStatus"
                :maxNameLength="100"
                :canUseAttachment="canUseAttachment"
                :selectedDocIds="selectedDocIds"
                @setFilename="setFilename"
                @preview="preview"
                @loaded="onLoaded('uploadReady')"
            >
            </UpLoad>

            <!-- 文件信息 -->
            <ContractInfo
                ref="ContractInfo"
                :isDynamicTemplate="isDynamicTemplate"
                v-model="templateTitleErr"
                :templateStatus="templateStatus"
                :templateName_useTem="templateName"
                :expireDays_useTem="expireDays"
                :contractTypeId_useTem="contractTypeId"
                @loaded="onLoaded('contractInfoReady')"
            >
            </ContractInfo>

            <template v-if="allowComposeDocument && templateStatus === 'edit'">
                <h1 class="group-title">
                    模版组合管理
                    <span class="right-config back-top-btn" v-show="isDocGroupLinkBtnVisible" @click="backToTop">返回上传文件</span>
                </h1>
                <DocGroupManage ref="docGroup"
                    :docList="docList"
                    @previewDoc="preview"
                ></DocGroupManage>
            </template>

            <!-- 添加签约方 -->
            <h1 class="addreciver-title"><span class="order-num">第二步：</span>添加签约方</h1>
            <div class="sender-name"><span class="sender-icon"><i class="el-icon-ssq-fajianxiang"></i></span>
                <!-- 代发或者代签的模板  -->
                <template v-if="existProxySendAuth">
                    请添加签约方
                    <el-tooltip effect="dark" placement="top">
                        <div slot="content">该模板已获得代发授权，发出的每份合同以已授权的签约方企业名义展示。<br />若同份合同出现多个已授权的签约方企业，则不作为代发合同。</div>
                        <span class="sender-proxy-icon"><i class="el-icon-ssq-bangzhu cursor-point"></i></span></el-tooltip>
                </template>
                <template v-else-if="templateStatus === 'use' && entOptionsInfos.length > 1">
                    当前发件方：
                    <el-select class="ent-select-input" popper-class="ent-select-dropdown" v-model="proxySenderEntId">
                        <el-option
                            v-for="(ent, index) in entOptionsInfos"
                            :key="index"
                            :value="ent.entId"
                            :label="ent.entName"
                        >
                        </el-option>
                    </el-select>
                </template>
                <template v-else>
                    当前发件方：<span class="sender-ent">{{ currentEnt }}</span>
                </template>
            </div>
            <AddReciver
                class="AddReciver"
                ref="addReciver"
                :isDynamicTemplate="isDynamicTemplate"
                :templateStatus="templateStatus"
                :noticeSpecificUser="noticeSpecificUser"
                :notAllowNotify="notAllowNotify"
                @loaded="onLoaded('addReciverReady')"
                @updateTemInfo="handleUpdateTemInfo"
            >
            </AddReciver>

            <!-- 关联合同 -->
            <LinkContracts v-if="templateStatus !== 'edit'" v-show="isSingleSigners" :linkTempId="templateId" ref="linkContract"></LinkContracts>
        </div>

        <VideoDialog
            title="操作演示"
            :visible.sync="videoVisible"
            videoPath="https://download.bestsign.cn/video/%E6%A8%A1%E7%89%88%E6%93%8D%E4%BD%9C%E6%BC%94%E7%A4%BA.mp4"
        >
        </VideoDialog>

        <Preview ref="preview" :previewDoc="previewDoc" :contractId="$route.query.templateId" :canUseAttachment="canUseAttachment"></Preview>
        <RegisterFooter class="footer footer-follow" v-if="ftVisible"></RegisterFooter>
    </div>
</template>

<script>
    /**
     * 文档地址：http://*************/delta/delta-aggregation-template/blob/dev/doc/API-DESIGN.md
     */
import VideoDialog from 'enterprise_pages/template/common/VideoDialog.vue';
import SignHeader from '../common/signHeader/SignHeader.vue';
import UpLoad from '../common/upLoad/UpLoad.vue';
import ContractInfo from '../common/contractInfo/ContractInfo.vue';
import AddReciver from '../common/addReciver/AddReciver.vue';
import Preview from '../common/preview/Preview.vue';
import RegisterFooter from 'components/register_footer/RegisterFooter.vue';
import LinkContracts from 'components/linkContracts/LinkContracts.vue';
import resRules from 'src/common/utils/regs.js';
import { checkEntNameFormat } from 'src/common/utils/reg';
import { getCurrentUser } from 'src/common/utils/getCurrentUser.js';

import { mapGetters, mapActions, mapState, mapMutations } from 'vuex';
import moment from 'dayjs';
import Bus from 'components/bus/bus.js';
import { resendTemplateMinxin } from 'src/mixins/resendTemplate.js';
import AdvancedConfigDialog from './advancedConfigDialog/AdvancedConfigDialog.vue';
import DocGroupManage from './docGroupList';
import { businessLocalField } from 'utils/hybrid/hybridBusiness.js';

export default {
    components: {
        DocGroupManage,
        SignHeader,
        UpLoad,
        Preview,
        ContractInfo,
        AddReciver,
        RegisterFooter,
        VideoDialog,
        LinkContracts,
        AdvancedConfigDialog,
    },
    mixins: [resendTemplateMinxin],
    // eslint-disable-next-line vue/require-prop-types
    props: ['templateStatus'],
    data() {
        return {
            canModifyWhenUsed: true,
            canUploadAccessoryBtn: false, // 是否可上传附件的按钮，通过pms接口返回
            isLoading: true,
            uploadReady: false,
            contractInfoReady: false,
            addReciverReady: false,
            templateName: '',
            templateTitleErr: '',
            expireDays: '',
            contractTypeId: '',
            videoVisible: false,
            ssoTmpEdit: {}, // 单点登录配置
            canUseAttachment: false, // 是否支持附加上传，使用模板时，通过后端接口返回判断
            isInit: true, // 首次进入页面，没点是否可上传的那个按钮
            returnUrl: ['null', ''].includes(this.$route.query.returnUrl) ? null : this.$route.query.returnUrl,
            isSingleSigners: false, // 手动添加单个签署人
            currentEnt: '',
            proxySenderEntId: '', // 选择代发企业
            entOptionsInfos: [], // 代发合同可选企业
            advancedDialogVisible: this.templateStatus !== 'use',
            noticeSpecificUser: true,
            notifyInEnglish: false,
            notAllowNotify: false,
            previewDoc: {}, // 预览文档
            // isStandardVersion: false,
            getDefaultConfig: this.$http('/ents/configs/industry-package-configs/contract').then(res => {
                const result = {};
                if (res && Array.isArray(res.data)) {
                    const entDefaultRecipient = {};
                    const personDefaultRecipient = {};

                    res.data.forEach(item => {
                        switch (item.name) {
                            // 文件设置
                            case 'PACKAGE_CONTRACT_EXPIRE_DAYS':
                                item.value && (result.expireDays = item.value);
                                break;
                            case 'PACKAGE_CONTRACT_LIFE_END_DAYS':
                                if (item.value) {
                                    result.contractLifeEnd = moment(Date.now() + item.value * 24 * 60 * 60 * 1000).format('YYYY-MM-DD');
                                } else {
                                    result.contractLifeEnd = '';
                                }
                                break;

                            // 签约企业设置
                            case 'PACKAGE_CONTRACT_ENT_FACE':
                                if (item.value === '1') {
                                    entDefaultRecipient.faceVerify = true;
                                    entDefaultRecipient.status_faceVerify = true;
                                } else if (item.value === '2') {
                                    entDefaultRecipient.faceFirst = true;
                                    entDefaultRecipient.status_faceFirst = true;
                                }
                                break;

                            // 签约个人设置
                            case 'PACKAGE_CONTRACT_PERSON_NAME_NEEDED':
                                if (item.value === 'true') {
                                    personDefaultRecipient.personNameNeeded = true;
                                }
                                break;
                            case 'PACKAGE_CONTRACT_PERSON_ID_NUMBER_NEEDED':
                                if (item.value === 'true') {
                                    personDefaultRecipient.personIdNumberNeeded = true;
                                }
                                break;
                            case 'PACKAGE_CONTRACT_PERSON_FACE':
                                if (item.value === '1') {
                                    personDefaultRecipient.faceVerify = true;
                                    personDefaultRecipient.status_faceVerify = true;
                                } else if (item.value === '2') {
                                    personDefaultRecipient.faceFirst = true;
                                    personDefaultRecipient.status_faceFirst = true;
                                }
                                break;
                            case 'PACKAGE_CONTRACT_PERSON_HAND_WRITE':
                                if (item.value === '1') {
                                    personDefaultRecipient.forceHandWrite = true;
                                    personDefaultRecipient.status_forceHandWrite = true;
                                } else if (item.value === '2') {
                                    personDefaultRecipient.handWriteNotAllowed = true;
                                    personDefaultRecipient.status_handWriteNotAllowed = true;
                                }
                                break;
                        }
                    });

                    result.entDefaultRecipient = entDefaultRecipient;
                    result.personDefaultRecipient = personDefaultRecipient;
                }
                return result;
            }),
            selectedDocIds: [], // 使用时合同模版包功能选中的合同id
            allowComposeDocument: false, // 是否支持合同模版组合
            isResend: !!this.$route.query.contractId || (!!this.$route.query.back && this.templateStatus === 'use'), // 重新发起合同 或从预览页返回
            existProxySendAuth: false, // 是否代发授权模板
            isVersionJudged: false,
        };
    },
    provide() {
        return {
            getDefaultConfig: this.getDefaultConfig,
        };
    },
    computed: {
        ...mapState({
            commonHeaderInfo: state => state.commonHeaderInfo,
            localFieldSupportType: state => state.localFieldSupportType,
        }),
        ...mapState('sendingPrepare', ['receptionCollection']), // 是否开通前台代收
        ...mapGetters(['getSsoConfig', 'getAuthStatus', 'checkFeat']),
        templateId() {
            return this.$route.query.templateId;
        },
        operateMarkId() {
            return this.$route.query.operateMarkId;
        },
        customTemplateId() {
            return this.$route.query.customTemplateId;
        },
        // 混合云host
        hybridServer() {
            return this.$store.state.commonHeaderInfo.hybridServer;
        },
        ftVisible() {
            return !this.ssoTmpEdit.tmpEdit_ft || this.ssoTmpEdit.tmpEdit_ft.visible;
        },
        refReceiverList() {
            return this.$refs.addReciver && this.$refs.addReciver.$refs.receiverList;
        },
        isNewGroup() { // 是否为新集团
            return +this.commonHeaderInfo.groupVersion === 2;
        },
        isDocGroupLinkBtnVisible() {
            return this.allowComposeDocument && this.$refs.upload && this.docList.length > 9;
        },
        isDynamicTemplate() {
            return this.$route.query.dynamicMark === 'DYNAMIC' || this.$route.query.dynamicMark === 'DYNAMIC_YOZO';
        },
        isOldDynamicTemp() {
            return this.$route.query.dynamicMark === 'DYNAMIC';
        },
        docList() {
            return this.$refs.upload.docList || [];
        },
        jumpToNewTemplateEdit() { // 新模版开通时，直接跳转到新模版页面
            return this.templateStatus === 'edit' &&
                this.commonHeaderInfo.openNewContractTemplate && !this.isDynamicTemplate;
        },
    },
    watch: {
        isLoading(v) {
            if (!v) {
                document.body.style.overflow = 'visible'; // fix page can't scroll problem
            }
        },
    },
    methods: {
        ...mapActions('template', ['getMultipleDynamicSignerConfig']),
        ...mapMutations('template', ['setCanModifyWhenUsed']),
        ...mapActions('sendingPrepare', ['getReceptionCollectionConfig']),
        backToTop() {
            window.scrollTo(0, 0); // 返回上传文档位置
            this.$refs.toDocGroup.$el.blur(); // 避免全局focusout事件滚动的影响
        },
        toGroupManagePos() {
            window.scrollTo(0, document.getElementsByClassName('tmp-contractInfo-comp')[0].offsetTop); // 返回上传文档位置
            this.$refs.toDocGroup.$el.blur(); // 避免全局focusout事件滚动的影响
        },
        async preview(document) {
            const passRes = await this.$hybrid.offlineTip();
            if (!passRes) {
                return;
            }

            this.previewDoc = document;
            this.$refs.preview.open();
        },
        pushErr(ary, key, errMsg) {
            Array.isArray(ary.errors) &&
                !ary.errors.some(item => item.fieldName === key) &&
                ary.errors.push({
                    fieldName: key,
                    errorInfo: errMsg,
                });
        },
        // 编辑模版：保存
        saveTemplateWhenEdit() {
            const { recipients, signOrdered } = this.refReceiverList;
            const templateName = this.$refs.ContractInfo.templateName;
            const { contractTypeId, expireDays } = this.$refs.ContractInfo;

            // 新版动态模版（DYNAMIC_YOZO）用普通模版接口
            return this.$http.post(`${tempPath}/${this.isOldDynamicTemp ? 'dynamic-template' : 'templates'}/${this.templateId}/web-edit-next`, {
                canUseAttachment: this.canUseAttachment,
                templateName,
                expireDays,
                contractTypeId,
                signOrdered,
                customTemplateId: this.customTemplateId,
                canModifyWhenUsed: this.canModifyWhenUsed,
                noticeSpecificUser: this.noticeSpecificUser,
                notAllowNotify: this.notAllowNotify,
                allowComposeDocument: this.allowComposeDocument,
                notifyInEnglish: this.notifyInEnglish,

                roles: recipients.map(item => {
                    item.needDesensitization = null;
                    const proxyClaimerAccounts = (item.userType === 'ENTERPRISE'
                        ? (item.userAccount ? item.userAccount.split(';') : [])
                        :                            [item.userAccount]).filter(v => !!v);
                    /*
                        注意事项：
                        1. 账号 -> 接收手机/邮箱
                        2. 与接口数据交互：签署时 账号传proxyClaimerAccounts，抄送时 账号赋值userAccount
                        3. 页面中账号 input v-model="userAccount"，初始化数据需要做下转换
                        */
                    const userAccount = item.receiverType === 'CC_USER' ? item.userAccount : null;
                    // ifProxyClaimer 当不填账号 或者 填写多个账号的时候 都为true
                    const ifProxyClaimer = proxyClaimerAccounts.length >= 2 ? true : item.ifProxyClaimer;
                    let extendData = {};
                    if (item.roleType === 'PLACEHOLDER') {
                        // 可变签署人 assignFullInfo为false，避免userName传前端写死的默认值 给后端
                        extendData = {
                            userId: '',
                            userName: '',
                            enterpriseName: '',
                            enterpriseId: '',
                        };
                    }
                    return {
                        ...item,
                        proxyClaimerAccounts,
                        userAccount,
                        ifProxyClaimer,
                        ...extendData,
                    };
                }),
            });
        },
        toBack() {
            if (this.templateStatus === 'edit') {
                this.saveTemplateWhenEdit();
            }

            const backUrl = this.templateStatus === 'use' && this.allowComposeDocument ? `/template/doc-select?templateId=${this.templateId}` : '/template/list';
            this.$router.push(backUrl);
        },
        checkNumber(docList) {
            if (this.selectedDocIds.length > 30 || (this.selectedDocIds.length === 0 && docList.length > 30)) {
                return true;
            }
        },
        toNext() {
            const { isUploading, docList } = this.$refs.upload;
            const { recipients, signOrdered } = this.refReceiverList;
            const templateName = this.$refs.ContractInfo.templateName;
            const { contractTypeId, expireDays } = this.$refs.ContractInfo;

            if (isUploading) {
                this.$MessageToast.error('文件还未上传完成，请在完成后继续操作');
                return;
            }

            if (!docList.length) {
                this.$MessageToast.error('未上传文件，请上传后继续操作');
                return;
            }

            if (this.templateStatus === 'use' && this.checkNumber(docList)) {
                this.$MessageToast.error('合同文档总数不能超过30');
                return;
            }

            if (!templateName) {
                this.$MessageToast.error('未填写模板名称，请填写后继续');
                return;
            }

            if (this.templateTitleErr) {
                this.$MessageToast.error(this.templateTitleErr);
                return;
            }

            if (!expireDays) {
                this.$MessageToast.error('请填写签约有效期后继续');
                return;
            }
            // if(this.isDynamicTemplate && (!recognize.before || !recognize.after)) {
            //     this.$MessageToast.error('请填写业务字段识别符');
            //     return;
            // }

            if (!/^[0-9]*[1-9][0-9]*$/.test(expireDays)) {
                this.$MessageToast.error('请填写有效的签约有效期后继续');
                return;
            }

            if (!recipients.length) {
                this.$MessageToast.error('至少添加一个签约方');
                return;
            }
            // 检查receiver是否超出限制
            // if ((this.isStandardVersion || this.commonHeaderInfo.currentEntId === '0')) {
            // 	if (recipients.length > STANDVERSION_LIMIT) {
            // 		return this.$MessageToast.error(this.$t('addReceiver.signerLimit', { limit: STANDVERSION_LIMIT }));
            // 	}
            // }

            if (this.refReceiverList.placeholderUser.length === 0) {
                this.$MessageToast.error('必须添加一个可变签约主体');
                return;
            }

            let isLackAttachmentName = false;
            // 校验多方签署时身份信息是否填写
            if (!this.checkRoleName(recipients)) {
                return;
            }
            // 签约方格式详细校验
            recipients.forEach((item, index) => {
                const recipient = item;

                // 添加了附件要求但是没填写附件名称
                if (!isLackAttachmentName && recipient.attachmentRequired) {
                    isLackAttachmentName = item.attachmentList.some(item => !item.name);
                }

                if (recipient.roleType === 'PLACEHOLDER') {
                    // console.log(this.templateStatus);
                    // console.log(this.refReceiverList.importedNum);
                    if (this.templateStatus === 'edit' && this.isDynamicTemplate && !recipient.roleName) {
                        // 清空业务角色报错数据
                        this.pushErr(recipient, 'roleName', '业务角色不能为空');
                    } else if (this.isDynamicTemplate && this.templateStatus === 'edit' && !resRules.roleNameStr.test(recipient.roleName)) {
                        this.pushErr(recipient, 'roleName', '请输入中文、英文或者数字');
                    } else if (this.templateStatus === 'edit' || (this.templateStatus === 'use' && this.refReceiverList.importedNum)) {
                        // 清空可变签约主体的报错数据
                        item.errors = [];
                        return;
                    } else {
                        if (recipient.idNumber && !resRules.IDCardReg.test(recipient.idNumber)) {
                            this.pushErr(recipient, 'idNumber', '请输入正确的身份证号');
                        }
                    }
                }
                // 固定签署人，不加入改合同时，不做账号校验
                if (recipient.roleType !== 'PLACEHOLDER' && recipient.notUsed) {
                    // 把之前notUsed为false时校验的错误清空
                    this.$set(recipients, index, {
                        ...item,
                        errors: [],
                    });
                    return;
                }

                // 动态模版需要校验业务角色名称
                if (this.isDynamicTemplate && this.templateStatus === 'edit' && !recipient.roleName) {
                    this.pushErr(recipient, 'roleName', '业务角色不能为空');
                }

                if (this.isDynamicTemplate && !resRules.roleNameStr.test(recipient.roleName)) {
                    this.pushErr(recipient, 'roleName', '请输入中文、英文或者数字');
                }

                if (this.templateStatus === 'edit' && !recipient.assignFullInfo) {
                    return;
                }

                // 使用模板，签约方为企业，开通了前台代收功能，使用了前台代收功能 - 不需要填写任何账号
                if (
                    this.templateStatus === 'use' &&
                    item.userType === 'ENTERPRISE' &&
                    this.receptionCollection &&
                    item.ifProxyClaimer
                ) {
                    // do nothing
                } else {
                    if (!recipient.userAccount) {
                        this.pushErr(recipient, 'userAccount', '账号不能为空');
                    }
                    if (
                        recipient.userAccount.split(';').some(t => t && !resRules.userAccount.test(t))
                    ) {
                        this.pushErr(recipient, 'userAccount', '请输入正确的手机号或邮箱');
                    }
                }

                if (recipient.personIdNumberNeeded && !recipient.idNumber) {
                    this.pushErr(recipient, 'idNumber', '身份证号不能为空');
                }

                if (recipient.idNumber && !resRules.IDCardReg.test(recipient.idNumber)) {
                    this.pushErr(recipient, 'idNumber', '请输入正确的身份证号');
                }

                if (recipient.personNameNeeded && !recipient.userName) {
                    this.pushErr(recipient, 'userName', '姓名不能为空');
                }

                if (recipient.userName && !resRules.longUserName.test(recipient.userName)) {
                    this.pushErr(recipient, 'userName', '请输入正确的姓名');
                }

                if (item.userType === 'ENTERPRISE') {
                    // 使用模板是必填，需要校验是否为空
                    const errorMsg = checkEntNameFormat(recipient.enterpriseName, this.templateStatus === 'use');
                    if (errorMsg) {
                        this.pushErr(recipient, 'enterpriseName', errorMsg);
                    }
                }
            });

            if (recipients.some(item =>
                item.errors && item.errors.length,
            )) {
                this.$MessageToast.error('签约方有误');
                return;
            }

            // 清空error
            recipients.forEach((item, index) => {
                this.$set(recipients, index, {
                    ...item,
                    errors: [],
                });
            });

            // 判断至少存在一个签署人
            if (!recipients.some(person => {
                return person.receiverType !== 'CC_USER';
            })) {
                this.$MessageToast.error('请至少添加一个签署人');
                return;
            }
            if (isLackAttachmentName) {
                return this.$MessageToast.error('请填写附件名称');
            }
            if (recipients && recipients.filter(i => !i.notUsed && i.contractPayer).length > 1) {
                return this.$MessageToast.error('付费方只能指定一个签署方');
            }

            if (this.templateStatus === 'edit') {
                this.isLoading = true;
                this.saveTemplateWhenEdit()
                    .then(res => {
                        if (res.data.hasError) {
                            this.$MessageToast({
                                type: 'error',
                                message: '账号信息错误、字段内容空缺或Excel格式错误，无法继续',
                                duration: 5000,
                            });
                            this.refReceiverList.recipients = res.data.errorRoles; // todo...
                        } else if (this.$route.params.templateStatus === 'edit' && this.isDynamicTemplate) {
                            this.$router.push(`/template/${this.isOldDynamicTemp ? 'dynamicOld' : 'dynamic'}/field?templateId=${this.templateId}${this.returnUrl ? '&returnUrl=' + encodeURIComponent(this.returnUrl) : ''}`);
                        } else {
                            this.$router.push(`/template/${this.$route.params.templateStatus}/field?templateId=${this.templateId}${this.returnUrl ? '&returnUrl=' + encodeURIComponent(this.returnUrl) : ''}`);
                        }
                    })
                    .catch(err => {
                        const errMsg = err.response.data.message;
                        this.$MessageToast.error(errMsg);
                        if (('' + err.response.data.code) === '170007') {
                            this.templateTitleErr = errMsg;
                        }
                    })
                    .finally(() => {
                        this.isLoading = false;
                    });
            } else {
                const allData = {
                    contractName: templateName,
                    expireDays,
                    contractTypeId,
                    signOrdered,
                    roles: recipients.map(item => {
                        const proxyClaimerAccounts = (item.userType === 'ENTERPRISE'
                            ? (item.userAccount ? item.userAccount.split(';') : [])
                            :                                [item.userAccount]).filter(v => !!v);
                        const cacheItem = Object.assign({}, item);
                        delete cacheItem.singleToggle;
                        delete cacheItem.singleList;
                        delete cacheItem.ifProxyClaimer_end;
                        return {
                            ...cacheItem,
                            needDesensitization: null,
                            userAccount: null,
                            proxyClaimerAccounts,
                            ifProxyClaimer: proxyClaimerAccounts.length >= 2 ? true : cacheItem.ifProxyClaimer,
                        };
                    }),
                };

                let contractDescriptionsInputs;
                // 使用的是单个输入可变签约主体
                if (!this.refReceiverList.importedNum) {
                    const { error, msg } = this.refReceiverList.checkContractDescriptionsInputsFinished();
                    // 如果有字段未填，阻止添加签约方
                    if (error) {
                        this.$MessageToast.error(msg);
                        return;
                    }
                    const contractDescriptionsInputsRef = this.refReceiverList.$refs.contractDescriptionsInputs;
                    contractDescriptionsInputs = (contractDescriptionsInputsRef &&
                        contractDescriptionsInputsRef.cacheFields &&
                        contractDescriptionsInputsRef.cacheFields.inputFields) ? contractDescriptionsInputsRef.cacheFields.inputFields : [];
                    contractDescriptionsInputs.map(item => {
                        function flatArr(arr) {
                            return arr.reduce((acc, val) => acc.concat(val), []);
                        }
                        if (item.type === 'DYNAMIC_TABLE') {
                            // 动态表格无数据的情况
                            const temArr = item.value;
                            if (
                                temArr &&
                                temArr !== 'ExcelImport' &&
                                Array.isArray(JSON.parse(temArr)) &&
                                flatArr(JSON.parse(temArr)).every(item => item === '')
                            ) {
                                item.value = '';
                            }
                        }
                        return {
                            fieldName: item.fieldName,
                            value: item.value,
                            custom: item.custom,
                        };
                    });
                    Object.assign(allData, {
                        inputFields: contractDescriptionsInputs,
                    });
                } else {
                    const { hasMustFillPictureField, hasBatchUploadPic } = this.refReceiverList;
                    // 批量，如果存在必填图片业务字段，需要校验用户有没有上传成功过图片
                    if (hasMustFillPictureField && !hasBatchUploadPic) {
                        return this.$MessageToast.error('存在必填图片业务字段，请先上传图片');
                    }
                    // 批量导入，需要清空可变签约主体的账号
                    allData.roles.map(role => {
                        if (role.roleType === 'PLACEHOLDER') {
                            role.userAccount = '';
                        }
                    });
                }
                this.isLoading = true;

                // 保存选择的代发企业账号
                if (!this.existProxySendAuth && this.templateStatus === 'use' && this.entOptionsInfos.length > 1) {
                    const params = {
                        entId: this.proxySenderEntId,
                        enterpriseName: this.entOptionsInfos.filter(a => a.entId === this.proxySenderEntId)[0].entName,
                    };
                    this.$http.post(`/template-api/multiple-dynamic-signer/${this.templateId}/save-sender`, params)
                        .then(() => {
                            this.savePostAllInfo(allData);
                        });
                } else {
                    this.savePostAllInfo(allData);
                }
            }
        },
        postAllInfo(data) {
            // 动态模板
            if (this.isDynamicTemplate) {
                if (!this.refReceiverList.importedNum) {
                    data.correlationParam = this.$refs.linkContract.tempSubmitLinks;
                }
                const temp = !this.refReceiverList.importedNum ? 'hand-input' : 'cache-use-template';
                let dynamicSaveUrl = `${tempPath}/dynamic-template/${this.templateId}/web/${temp}/save`;
                dynamicSaveUrl = this.handleRequestLink(dynamicSaveUrl);
                return this.$http.post(dynamicSaveUrl, data);
            }

            let url = `${tempPath}/multiple-dynamic-signer/${this.templateId}/web/cache-use-template-info${this.operateMarkId ? '?operateMarkId=' + this.operateMarkId : ''}`;

            if (!this.refReceiverList.importedNum) {
                data.correlationParam = this.$refs.linkContract.tempSubmitLinks;

                // 混合云3.0本地存储
                if (this.$hybrid.isGammaLocalField(this.localFieldSupportType)) {
                    const fields = data.inputFields.map(item => ({
                        name: item.fieldName,
                        value: item.value,
                        saveLocation: item.saveLocation,
                    }));
                    delete data.inputFields;
                    return businessLocalField({
                        url: '/hybrid/field',
                        requestData: {
                            target: '/templates/hand-input',
                            params: JSON.stringify({
                                templateId: this.templateId,
                                ...data,
                            }),
                            fields,
                        },
                    });
                }
                url = `${tempPath}/multiple-dynamic-signer/${this.templateId}/web/hand-input-v2/save`;
            }
            url = this.handleRequestLink(url);
            return this.$http.post(url, data);
        },
        // 使用时下一步，提交信息
        savePostAllInfo(data) {
            this.postAllInfo(data)
                .then(({ data }) => {
                    const { code, message, result } = data;
                    if (code && code !== '140001' && !this.isDynamicTemplate) {
                        return this.$MessageToast({
                            type: 'error',
                            message,
                            duration: 5000,
                        });
                    }
                    if (result && result.hasError) {
                        this.$MessageToast({
                            type: 'error',
                            message: '账号信息错误、字段内容空缺或Excel格式错误，无法发送',
                            duration: 5000,
                        });
                        this.refReceiverList.recipients = result && result.errorRoles; // todo...
                    } else {
                        if (!this.existProxySendAuth) {
                            if (this.entOptionsInfos.length > 1) { // 如果是代发合同，弹出提示
                                this.isLoading = false;
                                const entName = this.entOptionsInfos.filter(a => a.entId === this.proxySenderEntId)[0].entName;
                                return this.$msgbox({
                                    title: '提示',
                                    message: `当前合同发送主体为：${entName}`,
                                    showCancelButton: true,
                                    confirmButtonText: '确定',
                                    cancelButtonText: '取消',
                                    customClass: 'message-box-confirm-custom',
                                }).then(() => {
                                    this.goNextPage();
                                });
                            } else {
                                return this.goNextPage();
                            }
                        }
                        // 如果是代发模板，需要查询签约方信息，弹窗提示用户代发签约方
                        return this.queryProxyInfo();
                    }
                })
                .catch(err => {
                    try {
                        const errMsg = err.response.data.message;
                        this.$MessageToast.error(errMsg);
                        if (('' + err.response.data.code) === '170007') {
                            this.templateTitleErr = errMsg;
                        }
                    } catch (err) {
                        console.log(err);
                    }
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
        queryProxyInfo() {
            this.$http.post(`/template-api/multiple-dynamic-signer/${this.templateId}/using-info`).then(({ data: { result } }) => {
                const multipleDynamicSignerResponses = result.multipleDynamicSignerResponses || [];
                // 代理的名义发件方列表
                const nominalSenderList = multipleDynamicSignerResponses.reduce((total, item) => {
                    const enterpriseBasicInfoDTO = (item.nominalSenderDTO || {}).enterpriseBasicInfoDTO;
                    if (enterpriseBasicInfoDTO && enterpriseBasicInfoDTO.enterpriseId) {
                        total.push(enterpriseBasicInfoDTO.enterpriseName);
                    }
                    return total;
                }, []);
                const nominalSenderLen = nominalSenderList.length;
                const normalSenderLen = multipleDynamicSignerResponses.length - nominalSenderLen;
                // nominalSenderList去重
                const nominalStr = Array.from(new Set(nominalSenderList)).join('、');

                const temp = [];
                if (normalSenderLen && nominalSenderLen) {
                    temp.push(`当前有${nominalSenderLen}份为代理发送合同，名义发件方为：${nominalStr}`);
                    temp.push(`有${normalSenderLen}份发件方为：${this.currentEnt}`);
                } else {
                    temp.push(normalSenderLen ? `当前合同发件方为：${this.currentEnt}` : `当前为代理发送合同，名义发件方为：${nominalStr}`);
                }

                const h = this.$createElement;
                const vNode = temp.reduce((total, item) => {
                    total.push(h('div', null, item));
                    return total;
                }, []);
                this.$msgbox({
                    title: '提示',
                    message: h('div', null, vNode),
                    showCancelButton: true,
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    customClass: 'message-box-confirm-custom',
                }).then(() => {
                    this.goNextPage();
                }).catch(() => {

                });
            });
        },
        // 使用模板时跳转到下一页
        goNextPage() {
            let url = `/template/use/field?templateId=${this.templateId}${this.returnUrl ? '&returnUrl=' + encodeURIComponent(this.returnUrl) : ''}${this.operateMarkId ? '&operateMarkId=' + this.operateMarkId : ''}${this.$route.query.contractId ? '&contractId=' + this.$route.query.contractId : ''}`;
            if (this.isDynamicTemplate) {
                url = `/template/view?templateId=${this.templateId}`;
            }
            this.$router.push(url);
        },
        setFilename(data) {
            this.$refs.ContractInfo.templateName = data;
        },

        onLoaded(component) {
            this[component] = true;
            if (this.uploadReady && this.contractInfoReady) {
                this.isLoading = false;
            }
        },
        handleUpdateTemInfo(data) {
            this.templateName = data.templateName;
            this.expireDays = data.expireDays;
            this.contractTypeId = data.contractTypeId;
        },
        handleErrorConfirm() {
            this.$router.push('/template/list');
        },
        // ajax
        getOrder() {
            return this.$http.get(`${tempPath}/templates/${this.$route.query.templateId}`);
        },
        getAuth() {
            return this.$http.get(`${tempPath}/templates/attachment/auth`);
        },
        getProxyEntInfo() {
            return this.$http.get('/ents/proxy/contract/subjects');
        },
        // 批量签署个数判断，两个或两个以上批量签署时，必须添加身份信息
        checkRoleName(recipients) {
            const holder = recipients.filter(item => {
                return item.roleType === 'PLACEHOLDER';
            });
            if (this.templateStatus === 'edit' && holder.length > 1 && holder.some(item => !item.roleName)) {
                this.$MessageToast.error('请添加可变签署人的"业务角色"');
                return false;
            } else {
                return true;
            }
        },
        // 监听 ReceiverList emit 的签署方添加方式的改变
        watchSignerType() {
            Bus.$on('toggleSignersType', (v) => {
                this.isSingleSigners = v;
            });
        },
        // 高级配置选项更新时调用
        handleUpateSwitch(propName, value) {
            this[propName] = value;
        },
        // 重新发起，获取之前选中的文档id
        getSelectedDocIds() {
            this.$http.get(`${tempPath}/re-initiate-contract/documentIds?templateId=${this.templateId}&contractId=${this.$route.query.contractId}`).then(res => {
                this.selectedDocIds = res.data.result;
            });
        },
        // 获取已选发件方信息
        getSelectedProxyEntInfo() {
            return this.$http.get(`/template-api/multiple-dynamic-signer/${this.templateId}/get-sender`);
        },
        onClick(event) {
            if (event && event.stopPropagation) {
                event.stopPropagation();
            } else {
                window.event.cancelBubble = true;
            }
            this.advancedDialogVisible = true;
        },
    },
    beforeMount() {
        this.ssoTmpEdit = this.getSsoConfig.tmpEdit || {};
        this.watchSignerType();
    },
    beforeRouteLeave(to, from, next) {
        next();
    },
    created() {
        if (this.jumpToNewTemplateEdit) {
            location.href = `/sign-flow/template/${this.$route.query.templateId}/config/upload${location.search}`;
            return;
        }
        // 判断版本后才显示界面
        this.isVersionJudged = true;
        // 获取该企业是否可以配置上传附件的配置
        this.getAuth().then(res => {
            this.canUploadAccessoryBtn = res.data.result || false;
        });
        // 使用模版时获取集团授权的代发企业列表
        if (this.templateStatus === 'use' && this.isNewGroup) {
            this.getProxyEntInfo().then(res => {
                this.entOptionsInfos = res.data;

                if (this.entOptionsInfos.length > 1) {
                    this.getSelectedProxyEntInfo()
                        .then(res => {
                            this.proxySenderEntId = (res.data && res.data.entId) || this.commonHeaderInfo.currentEntId;
                        });
                }
            });
        }

        this.getOrder().then(res => {
            const { canUseAttachment, noticeSpecificUser, canModifyWhenUsed, existProxySendAuth, notAllowNotify, notifyInEnglish } = res.data;
            // canUseAttachment可能为null
            this.canUseAttachment = !!canUseAttachment && this.canUploadAccessoryBtn;
            // 固定签署人接收短信或者开关提醒
            this.noticeSpecificUser = noticeSpecificUser;
            // 是否发送英文通知
            this.notifyInEnglish = notifyInEnglish;
            // 是否禁止发送通知
            this.notAllowNotify = !!notAllowNotify;
            // 模版组合是否开启
            this.allowComposeDocument = res.data.allowComposeDocument;
            // 重新发起，获取之前选中的文档id
            if (this.isResend || (this.allowComposeDocument && this.templateStatus === 'use')) {
                this.getSelectedDocIds();
            }
            // DYNAMIC: 动态模版，STATIC：普通模版，创建动态模版时路由带参数
            this.canModifyWhenUsed = canModifyWhenUsed;
            // 只有使用模版的时候才将接口数据更新到store，设置模版默认store中的CanModifyWhenUsed为true。每次进入此路有 都需要初始化，fix 多份模版，一份配置开启，部分配置关闭，切换编辑和使用页面disable错乱。
            this.setCanModifyWhenUsed(this.templateStatus === 'use' ? canModifyWhenUsed : true);
            this.existProxySendAuth = existProxySendAuth;
        });
        this.getMultipleDynamicSignerConfig();
        this.getReceptionCollectionConfig();
        this.currentEnt = getCurrentUser(this.commonHeaderInfo, this.getAuthStatus);
    },
};
</script>

<style lang="scss">

    .tmp-prepare-page {
        .header {
            position: fixed;
            top: 0;
        }
        .prepare-container {
            width: 1000px;
            background-color: #fff;
            margin: 0 auto;
            padding-top: 50px;
        }
        .order-num {
            font-size: 18px;
            color: #127fd2;
        }
        //确认发起方
        .sender-title{
            height: 50px;
            line-height: 50px;
            border-bottom: 1px solid $border-color;
            font-size: 18px;
            font-weight: bold;
        }
        .sender-name{
            height: 40px;
            line-height: 40px;
            font-size: 13px;
            color: #151515;
            padding: 0 40px 0 20px;
            background: #F9F9F9;
            position: relative;
            .sender-icon {
                display: inline-block;
                height: 24px;
                line-height: 24px;
                width: 24px;
                margin-right: 8px;
                border-radius: 12px;
                font-size: 14px;
                color: #fff;
                background-color: #ccc;
                text-align: center;
            }
            .sender-ent {
                color: #333;
            }
            .sender-proxy-icon{
                position: absolute;
                right: 20px;
                color: #999
            }
            .ent-select-input .el-input__inner {
                height: 34px;
            }
        }
        .prepare-upload {
            position: relative;
        }
        // 上传文件样式
        .upload-title {
            height: 50px;
            line-height: 50px;
            border-bottom: 1px solid $border-color;
            font-size: 18px;
            font-weight: bold;
        }
        .right-config {
            padding: 0;
            line-height: 50px;
            float: right;
            color: #127fd2;
            &.back-top-btn {
                font-size: 14px;
                font-weight: normal;
                cursor: pointer;
            }
        }
        .gap-line {
            padding-right: 10px;
            padding-left: 4px;
            color: #dddddd;
        }
        .el-switch.is-checked .el-switch__core {
            .el-switch__button {
                background-color: #2298f1;
                left: 3px;
            }
        }
        .group-title {
            height: 50px;
            line-height: 50px;
            font-size: 18px;
            font-weight: bold;
        }
        .addreciver-title {
            margin-top: 30px;
            width: 1000px;
            height: 50px;
            line-height: 50px;
            border-bottom: 1px solid #ddd;
            font-size: 18px;
            font-weight: bold;
        }

        .footer {
            background-color: #F4F4F4;
        }

        :-moz-placeholder { /* Mozilla Firefox 4 to 18 */
            color: #ccc; opacity:1;
        }

        ::-moz-placeholder { /* Mozilla Firefox 19+ */
            color: #ccc;opacity:1;
        }

        input:-ms-input-placeholder{
            color: #ccc;opacity:1;
        }

        input::-webkit-input-placeholder{
            color: #ccc;opacity:1;
        }
    }
    .dynamic-template-confirm {
        width: 460px;
        .el-message-box__message p {
            line-height: 18px;
        }
    }

    // footer跟随
    // @include footerFollow;

</style>
