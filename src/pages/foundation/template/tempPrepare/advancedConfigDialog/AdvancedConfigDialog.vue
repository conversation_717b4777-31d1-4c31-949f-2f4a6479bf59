<template>
    <!-- 模板编辑高级配置弹窗 -->
    <div class="prepare-advanced-popper" v-show="isShow">
        <ul class="prepare-advanced_list">
            <li class="prepare-advanced_item">
                <div class="prepare-advanced_content">
                    <p class="prepare-advanced_title">是否不给以下签约方发送签约相关的短信/邮件通知（关闭状态下默认发送）</p>
                    <p class="prepare-advanced_explain">
                        <el-radio v-model="noticeChooseType" label="1" :disabled="!noticeSwitch">不给固定签约方发，可变签约方不受影响</el-radio>
                        <br>
                        <el-radio v-model="noticeChooseType" label="2" :disabled="!noticeSwitch">不给所有签约方发</el-radio>
                    </p>
                </div>
                <div class="prepare-advanced_switch">
                    <el-switch
                        v-model="noticeSwitch"
                        on-color="#aadaff"
                        off-color="#989898"
                        on-text=""
                        off-text=""
                        :width="31"
                        @change="handleNoticeChange"
                    >
                    </el-switch>
                </div>
            </li>
            <li class="prepare-advanced_item" v-for="(item, index) in configList.filter(item => item.isShow)" :key="index">
                <div class="prepare-advanced_content">
                    <p class="prepare-advanced_title">{{ item.title }}</p>
                    <p class="prepare-advanced_explain" v-html="item.explain"></p>
                </div>
                <div class="prepare-advanced_switch">
                    <el-switch
                        v-model="item.model"
                        on-color="#aadaff"
                        off-color="#989898"
                        on-text=""
                        off-text=""
                        :width="31"
                        @change="$emit('updateSwitch', item.props, item.model)"
                    >
                    </el-switch>
                </div>
            </li>
        </ul>
    </div>
</template>

<script>
export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['showAttachment', 'isShow', 'noticeSpecificUser', 'canModifyWhenUsed', 'canUseAttachment', 'allowComposeDocument', 'showAllowComposeDocument', 'notAllowNotify', 'notifyInEnglish'],
    data() {
        return {
            configList: [
                {
                    title: '英文通知',
                    explain: '给所有签约方发送英文通知',
                    props: 'notifyInEnglish',
                    isShow: true,
                },
                {
                    title: '允许发件人修改合同配置',
                    explain: '使用模板发送合同前可修改：合同类型、签约有效期、是否必须实名、<br>是否必须刷脸签署、是否必须手写签名、签署人数量',
                    props: 'canModifyWhenUsed',
                    isShow: true,
                },
                {
                    title: '允许发件方添加合同附件',
                    explain: '使用模板发送合同前可上传文件做为合同发送',
                    props: 'canUseAttachment',
                    isShow: this.showAttachment,
                },
                {
                    title: '模板组合',
                    explain: '在使用时，您需要先挑选模板中的文件后再发送',
                    props: 'allowComposeDocument',
                    isShow: this.showAllowComposeDocument,
                },
            ],
            noticeSwitch: false,
            noticeChooseType: '0',
        };
    },
    watch: {
        showAttachment: {
            handler(val) {
                this.updateIsShow('canUseAttachment', val);
            },
            immediate: true,
        },
        canModifyWhenUsed: {
            handler(val) {
                this.initData('canModifyWhenUsed', val);
            },
            immediate: true,
        },
        canUseAttachment: {
            handler(val) {
                this.initData('canUseAttachment', val);
            },
            immediate: true,
        },
        allowComposeDocument: {
            handler(val) {
                this.initData('allowComposeDocument', val);
            },
            immediate: true,
        },
        showAllowComposeDocument: {
            handler(val) {
                this.updateIsShow('allowComposeDocument', val);
            },
            immediate: true,
        },
        noticeSpecificUser: {
            handler() {
                this.initNotice();
            },
            immediate: true,
        },
        notAllowNotify: {
            handler() {
                this.initNotice();
            },
            immediate: true,
        },
        notifyInEnglish: {
            handler(val) {
                this.initData('notifyInEnglish', val);
            },
            immediate: true,
        },
        noticeChooseType(val) {
            if (val === '1') {
                this.$emit('update:notAllowNotify', false);
                this.$emit('update:noticeSpecificUser', false);
            } else {
                this.$emit('update:notAllowNotify', true);
                this.$emit('update:noticeSpecificUser', false);
            }
        },
    },
    methods: {
        initNotice() {
            this.noticeSwitch = !(!this.notAllowNotify && this.noticeSpecificUser);
            if (this.noticeSwitch) {
                this.noticeChooseType = this.notAllowNotify ? '2' : '1';
            } else {
                if (this.noticeChooseType === '0') {
                    this.noticeChooseType = '1';
                }
            }
        },
        handleNoticeChange(val) {
            if (val) {
                // 打开, 二选一 等待用户自己选择
                if (this.noticeChooseType === '0') {
                    this.noticeChooseType = '1';
                }
                this.$emit('update:notAllowNotify', false);
                this.$emit('update:noticeSpecificUser', false);
            } else {
                // 关闭, 默认给所有签署方发送通知
                this.$emit('update:notAllowNotify', false);
                this.$emit('update:noticeSpecificUser', true);
            }
        },
        handleClose() {
            this.$emit('close');
        },
        initData(props, val) {
            const item = this.configList.find(item => item.props === props);
            this.$set(item, 'model', val);
        },
        updateIsShow(props, val) {
            const item = this.configList.find(item => item.props === props);
            this.$set(item, 'isShow', val);
        },
        onBodyClick(event) {
            if (event.target.closest('.prepare-advanced-popper')) {
                return;
            }
            this.handleClose();
        },
    },
    mounted() {
        document.body.addEventListener('click', this.onBodyClick);
    },
    beforeDestroy() {
        document.body.removeEventListener('click', this.onBodyClick);
    },
};
</script>

<style lang="scss">
    .prepare-advanced-popper {
        padding: 5px 30px 5px 30px;
        background: #FFFFFF;
        box-shadow: 0 0 6px 0 rgba(0,0,0,0.10);
        background-color: #fff;
        top: 48px;
        position: absolute;
        right: -120px;
        z-index: 10;
        border-radius: 8px;
        &::before {
            display: block;
            position: absolute;
            content: ' ';
            width: 12px;
            height: 12px;
            border-left: 1px solid #f1f1f1;
            border-top: 1px solid #f1f1f1;
            transform: rotate(45deg);
            top: -7px;
            right: 140px;
            background-color: #fff;
        }
        .prepare-advanced_list {
            .prepare-advanced_item {
                position: relative;
                border-bottom: 1px dashed #eee;
                padding: 10px 0;
                &:last-child {
                    border: 0 none;
                }
                .prepare-advanced_switch {
                    position: absolute;
                    top: 50%;
                    right: 0;
                    width: 32px;
                    margin-top: -7px;
                    .el-switch {
                        position: relative;
                        height: 13px;
                        line-height: 13px;
                        vertical-align: middle;
                        &__label, &__core {
                            height: 12px;
                        }
                        &__button {
                            top: -50%;
                            left: -3px;
                            box-shadow: -1px 2px 3px 1px #ccc;
                        }
                    }
                    .el-switch,.el-switch__label,.el-switch__label * {
                        font-size: 12px;
                        display: inline-block;
                    }
                    .el-switch.is-checked .el-switch__core {
                        .el-switch__button {
                            background-color: #2298f1;
                            left: 3px;
                        }
                    }
                }
                .prepare-advanced_content {
                    padding-right: 32px;
                    .prepare-advanced_title {
                        font-size: 14px;
                        color: #333;
                    }
                    .prepare-advanced_explain {
                        margin-top: 2px;
                        font-size: 12px;
                        color: #999;
                        .el-radio__label {
                            color: #999;
                        }
                    }
                }
            }
        }
    }
</style>
