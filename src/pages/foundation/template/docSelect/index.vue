<!-- 模板——上传文件页 -->
<template>
    <div
        class="doc-select-page footer-follow-page"
        v-loading.body.fullscreen="isLoading"
    >
        <SignHeader
            class="header"
            title="选择需要发送的合同组合"
            rText="保存继续"
            @to-back="toBack"
            @to-next="toNext"
            :backConfig="ssoTmpEdit.tmpEdit_1_back"
        >
        </SignHeader>
        <div class="doc-select-container footer-follow-page-container">
            <el-radio-group class="group-type-select" v-model="groupType">
                <div class="select-module single-part">
                    <h1><el-radio label="single">自由选择模板组合</el-radio></h1>
                    <el-checkbox-group @change="switchGroupType('single')" class="file-select" v-model="selectFileIds">
                        <div class="file-item" v-for="doc in docList.filter(doc => doc)" :key="doc.documentId">
                            <el-checkbox :label="doc.documentId" :disabled="groupType !== 'single'">
                                {{ doc.fileName }}
                            </el-checkbox>
                            <span @click="preview(doc.documentId)" class="name">{{ doc.fileName }}</span>
                        </div>
                    </el-checkbox-group>
                </div>
                <div class="select-module group-part">
                    <h1><el-radio label="group">选择已配置的模板组合</el-radio></h1>
                    <el-radio-group class="group-select" v-model="selectGroupId" @change="switchGroupType('group')">
                        <div class="group-item"
                            v-for="group in filterEmptyDocGroup"
                            :key="group.docGroupId"
                        >
                            <el-radio :label="group.docGroupId" :disabled="groupType !== 'group'"></el-radio>
                            <span class="name">{{ group.docGroupName }}</span>
                            <span class="id">{{ group.docGroupId }}</span>
                            <div class="documents">
                                <p class="cur-pointer"
                                    v-for="file in group.documents"
                                    :key="file.documentId"
                                    @click="preview(file.documentId)"
                                >
                                    {{ file.documentName }}
                                </p>
                            </div>
                        </div>
                        <div class="no-group" v-if="!filterEmptyDocGroup.length">暂无可用模版组合</div>
                    </el-radio-group>
                </div>
            </el-radio-group>
            <div class="submit-btn">
                <el-button type="primary" @click="toNext">保存继续</el-button>
            </div>
        </div>
        <Preview ref="preview" :previewDoc="previewDoc" :contractId="$route.query.contractId"></Preview>
        <RegisterFooter class="footer footer-follow" v-if="ftVisible"></RegisterFooter>
    </div>
</template>

<script>
import SignHeader from '../common/signHeader/SignHeader.vue';
import Preview from '../common/preview/Preview.vue';
import RegisterFooter from 'components/register_footer/RegisterFooter.vue';
import { mapGetters } from 'vuex';

export default {
    components: {
        SignHeader,
        Preview,
        RegisterFooter,
    },
    data() {
        return {
            isLoading: true,
            groupType: 'single',
            docList: [],
            docGroupData: [],
            selectFileIds: [], // 选中的文件id集合
            selectGroupId: '',
            previewDoc: {},
            ssoTmpEdit: {}, // 单点登录配置
            templateId: this.$route.query.templateId,
        };
    },
    computed: {
        ...mapGetters(['getSsoConfig', 'getAuthStatus']),
        ftVisible() {
            return !this.ssoTmpEdit.tmpEdit_ft || this.ssoTmpEdit.tmpEdit_ft.visible;
        },
        filterEmptyDocGroup() {
            return this.docGroupData.filter(a => a.documents.length > 0);
        },
    },
    methods: {
        toBack() {
            this.$router.push('/template/list');
        },
        toNext() {
            // 未选文档或模版组合时提示
            if ((this.groupType === 'single' && !this.selectFileIds.length) || (!this.selectGroupId && this.groupType === 'group')) {
                return this.$MessageToast.info('未选择模版文件或模版组合，请选择后继续');
            }
            // 保存选中的合同组合
            this.$http.post(`/template-api/multiple-dynamic-signer/${this.templateId}/web/cache-documentIds`, {
                documentIds: this.groupType === 'single' ? [...this.selectFileIds] : [],
                docGroupIds: this.groupType === 'single' ? [] : [this.selectGroupId],
            }).then(() => {
                this.$router.push(`/template/use/prepare?templateId=${this.templateId}`);
            });
        },
        switchGroupType(type) {
            this.groupType = type;
        },
        // 合并一个新doc数据到docList
        // 兼容混1、混3逻辑，混3逻辑与公有云保持一致，主要差异在预览图地址从后端获取
        handleMergeDocToList(res) {
            const mergedDoc = this.$hybrid.tempPrepareMergeDoc(res, this.templateId);
            Vue.set(this.docList, mergedDoc.order, mergedDoc.docData);
        },
        async preview(docId) {
            const passRes = await this.$hybrid.offlineTip();
            if (!passRes) {
                return;
            }

            this.previewDoc = this.docList.find(a => a.documentId === docId);
            this.$refs.preview.open();
        },
        // 获取所有文件并展示
        getDocuments() {
            return this.$http.get(`${tempPath}/templates/${this.templateId}/documents?isGenerateDocumentImages=1`);
        },
        getGroupData() {
            return this.$http.get(`/template-api/templates/document-group?templateId=${this.templateId}`);
        },
    },
    created() {
        Promise.all([
            this.getDocuments(),
            this.getGroupData(),
        ]).then(res => {
            const docData = res[0].data || [];
            this.docGroupData = res[1].data || [];
            docData.forEach(item => {
                this.handleMergeDocToList(item);
            });
        }).catch(() => {})
            .finally(() => {
                this.isLoading = false;
            });
    },
    beforeMount() {
        this.ssoTmpEdit = this.getSsoConfig.tmpEdit || {};
    },
};
</script>

<style lang="scss">
.doc-select-page {
    .header {
        position: fixed;
        top: 0;
    }
    .doc-select-container {
        width: 1000px;
        background-color: #fff;
        margin: 0 auto;
        padding-top: 50px;
        .group-type-select {
            width: 100%;
            padding-top: 10px;
            .select-module {
                h1 {
                    height: 50px;
                    color: #333;
                    line-height: 50px;
                    font-weight: 700;
                    border-bottom: 1px solid #dddddd;
                    .el-radio__label {
                        padding-left: 10px;
                    }
                }
                span {
                    font-size: 14px;
                }
                .file-select {
                    padding: 20px 0 10px 20px;
                    .file-item {
                        display: inline-block;
                        height: 30px;
                        width: 472px;
                        margin-left: 0;
                        .el-checkbox .el-checkbox__label {
                            display: none;
                        }
                        .el-checkbox .is-disabled .el-checkbox__inner {
                            background-color: #eef1f6;
                            border-color: #d1dbe5;
                        }
                        .name {
                            display: inline-block;
                            padding-left: 10px;;
                            cursor: pointer;
                            color: #127fd2;
                            max-width: 450px;
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            vertical-align: middle;
                        }
                        &:nth-child(2n) {
                            margin-left: 15px;
                        }
                    }
                }
                .group-select {
                    font-size: 14px;
                    width: 100%;
                    .group-item {
                        padding: 15px 20px;
                        border-bottom: 1px solid #eee;
                        .el-radio {
                            padding-right: 10px;
                            vertical-align: middle;
                            .el-radio__label {
                                display: none;
                            }
                        }
                        .name {
                            display: inline-block;
                            color: #333;
                            width: 260px;
                            max-width: 250px;
                            padding-right: 10px;
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            vertical-align: middle;
                        }
                        .id {
                            display: inline-block;
                            width: 180px;
                            color: #666;
                            padding-right: 10px;
                            vertical-align: middle;
                        }
                        .documents {
                            display: inline-block;
                            padding-left: 10px;
                            width: 260px;
                            color: #127fd2;
                            line-height: 24px;
                            vertical-align: middle;
                        }
                    }
                    .no-group {
                        padding: 25px;
                        color: #666;
                        text-align: center;
                        border-bottom: 1px solid #eee;
                    }
                }
            }
        }
        .submit-btn {
            margin-top: 30px;
            text-align: center;
            .el-button--primary {
                height: 30px;
                padding: 5px 20px;
                background: #127FD2;
                border-color: #127fd2;
            }
        }
    }
    .footer {
        background-color: #F4F4F4;
    }
}
</style>
