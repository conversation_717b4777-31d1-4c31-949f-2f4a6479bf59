<!-- 指定位置页重构 -->
<template>
    <div class="temp-field-page" v-loading.fullscreen.lock="loading">
        <!-- 页头 -->
        <SignHeader class="header"
            :title="contractTitle"
            :rText="isEdit ? '保存' : '发送'"
            :rShow="nextVisible"
            @to-back="toBack"
            @to-next="toNext"
            :backConfig="ssoTmpEdit.tmpEdit_1_back"
        >
            <!-- 提交审批按钮，只在单点登录并开启功能点的模板预览页展示 -->
            <li v-if="approvalBtnVisible" slot="prevBtn" class="next" @click="handleSubmitApproval">
                提交审批
            </li>
        </SignHeader>

        <!-- 主体 -->
        <TempFieldBody
            ref="FieldBody"
            :isEdit="isEdit"
            :canUseAttachment="canUseAttachment"
            :hybridServerConnected="hybridContractServerConnected"
            :templateStatus="this.$route.params.templateStatus"
            :templateMatchId="templateMatchId"
            @init-receivers-data="onInitReceiversData"
        >
        </TempFieldBody>

        <!-- 页脚 -->
        <RegisterFooter class="footer" v-if="ftVisible"></RegisterFooter>

        <!-- 新手指引 -->
        <Guide v-if="isEdit" :guideUserType="guideUserType"></Guide>

        <!-- 审批流dialog -->
        <el-dialog class="ssq-dialog dialog-approval"
            title="申请发送合同"
            :visible.sync="dialogApproval"
            @open="ApprovalAppKey = Math.random()"
        >
            <ApprovalApp
                :key="ApprovalAppKey"
                :definesData="definesData"
                @apply-approval="onApplyApproval"
            >
            </ApprovalApp>
        </el-dialog>

        <!-- 计费dialog -->
        <el-dialog class="ssq-dialog dialog-charge"
            v-if="dialogCharge"
            :visible.sync="dialogCharge"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
        >
            <SignCharge
                :contractPayerAccount="contractPayerAccount"
                @SignCharge-confirm="onChargeConfirm"
                @SignCharge-cacel-dialog="dialogCharge = false"
            >
            </SignCharge>
        </el-dialog>

        <!-- 发送提示dialog -->
        <el-dialog class="ssq-dialog dialog-send-tips dialog-now-use"
            :visible.sync="dialogSendTips"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
        >
            <div class="icon-bg">
                <i class="el-icon-ssq-tongguo"></i>
                <p class="p-top">提交成功</p>
                <p class="p-bottom">{{ sendedDialogTip }}</p>
            </div>
            <div class="btns clear">
                <div class="ssq-btn-confirm" @click="$router.push('/template/record')" v-if="ssoDgViewRecordVisible">查看记录</div>
                <div class="ssq-btn-cancel" v-if="ssoDgSentDoenVisble" :class="!ssoDgViewRecordVisible && 'singleBtn'" @click="`${$router.push(doneBtnRedirectUrl)}`">完成</div>
            </div>
        </el-dialog>

        <!-- 缺少签署位置dialog -->
        <FieldTip
            :tipList="tipList"
            :show.sync="dialogTip"
            @confirm="saveTemplate"
            confirmButtonText="继续保存"
        ></FieldTip>
    </div>
</template>

<script>
import SignHeader from '../../sign/common/signHeader/SignHeader.vue'; // 复用普通发起的header
import RegisterFooter from 'src/components/register_footer/RegisterFooter.vue';
import TempFieldBody from './temp_field_body/TempFieldBody.vue';
import ApprovalApp from 'foundation_pages/sign/field/approvalApp/ApprovalApp.vue';
import SignCharge from 'foundation_pages/sign/field/signCharge/SignCharge.vue';
import Guide from './temp_field_guide/TempFeildGuide.vue';
import { goReturnUrl } from 'src/common/utils/returnUrl.js';
import FieldTip from 'foundation_pages/sign/field/fieldTip/FieldTip.vue';
import { fieldLanBreak } from 'utils/hybrid/hybridBusiness.js';

import { mapState, mapGetters } from 'vuex';

export default {
    components: {
        SignHeader,
        RegisterFooter,
        TempFieldBody,
        ApprovalApp,
        SignCharge,
        Guide,
        FieldTip,
    },
    data() {
        return {
            // 合同信息
            contractTitle: '',
            isEdit: this.$route.params.templateStatus === 'edit',
            returnUrl: ['null', ''].includes(this.$route.query.returnUrl) ? null : this.$route.query.returnUrl,
            // 模板匹配生成的ID
            canUploadAccessory: false,    // 是否可上传附件的按钮，通过pms接口返回
            templateMatchId: this.$route.query.templateMatchId,

            templateId: this.$route.query.templateId,
            // 若为混合云合同，是否能连接服务器，默认true
            hybridContractServerConnected: true,
            // 页面信息
            loading: false,
            signOrdered: false,

            // guide
            guideUserType: 'PERSON',

            // 保存模板弹窗 4.10.2版本去掉
            // dialogNowUse: false,

            // 审批流
            dialogApproval: false,
            ApprovalAppKey: 0,
            definesData: [],
            commitFlow: [],
            defInfoId: '',

            // 计费
            dialogCharge: false,

            // 发送成功提示
            dialogSendTips: false,

            canUseAttachment: false,
            dialogTip: false,
            tipList: [],
            contractPayerAccount: {},
        };
    },
    computed: {
        ...mapState({
            headerInfo: state => {
                return state.commonHeaderInfo;
            },
            hybridServer: state => state.commonHeaderInfo.hybridServer,
        }),
        ...mapGetters(['getSsoConfig']),
        ...mapState('template', ['multipleDynamicSigner']),
        ftVisible() {
            return !this.ssoTmpEdit.tmpEdit_ft || this.ssoTmpEdit.tmpEdit_ft.visible;
        },
        // 单点登录模板编辑页配置
        ssoTmpEdit() {
            return this.getSsoConfig.tmpEdit || {};
        },
        // 单点登录模板预览页配置
        ssoTplPrvw() {
            return this.getSsoConfig.tplPrvw || {};
        },
        // 提交审批按钮是否显示
        approvalBtnVisible() {
            // 判断在模板预览页并且单点登录配置了显示
            return (this.$route.path.includes('/use/field') && (this.ssoTplPrvw.tplPrvw_1_apv && this.ssoTplPrvw.tplPrvw_1_apv.visible));
        },
        // 下一步按钮是否显示
        nextVisible() {
            // 判断在模板指定位置页，默认显示（单点登录控制隐藏）
            return (this.$route.path.includes('/edit/field') || (this.$route.path.includes('/use/field') && (!this.ssoTplPrvw.tplPrvw_1_send || this.ssoTplPrvw.tplPrvw_1_send.visible)));
        },
        // 提交成功弹窗，查看记录按钮是否显示
        ssoDgViewRecordVisible() {
            return !this.ssoTplPrvw.tplPrvw_dg_btn_vwRcds || this.ssoTplPrvw.tplPrvw_dg_btn_vwRcds.visible;
        },
        // 提交成功弹窗，完成按钮是否显示
        ssoDgSentDoenVisble() {
            return !this.ssoTplPrvw.tplPrvw_dg_btn_done || this.ssoTplPrvw.tplPrvw_dg_btn_done.visible;
        },
        // 提交成功弹窗，完成按钮跳转链接
        doneBtnRedirectUrl() {
            return (this.ssoTplPrvw.tplPrvw_dg_btn_done && this.ssoTplPrvw.tplPrvw_dg_btn_done.url) ? this.ssoTplPrvw.tplPrvw_dg_btn_done.url : '/doc/list';
        },
        // 提交成功弹窗，提示文案
        sendedDialogTip() {
            if (this.ssoTplPrvw.tplPrvw_dg_txt_doneTip && this.ssoTplPrvw.tplPrvw_dg_txt_doneTip.text) {
                return this.ssoTplPrvw.tplPrvw_dg_txt_doneTip.text;
            } else {
                if (this.ssoDgViewRecordVisible) {
                    return '实际发送结果请查看模板发送记录';
                }
                return '';
            }
        },
    },
    methods: {
        toBack() {
            if (this.templateMatchId) {
                this.$router.push(`/template/match-result?resultId=${this.templateMatchId}`);
            } else {
                this.$router.push(`/template/${this.$route.params.templateStatus}/prepare?back=true&templateId=${this.templateId}${this.$route.query.contractId ? '&contractId=' + this.$route.query.contractId : ''}`);
            }
        },
        toNext() {
            if (this.isEdit) {
                const receivers = this.$refs.FieldBody.receivers;
                const marks 	  = this.$refs.FieldBody.marks;
                const allDone = receivers.every(receiver => {
                    return marks.some(mark => {
                        return (mark.roleId === receiver.roleId) && (mark.type === 'SEAL' || mark.type === 'SIGNATURE');
                    });
                });
                const someTextNoName = marks.some(item => {
                    return item.type === 'TEXT' && !item.name;
                });

                if (!allDone) {
                    this.$MessageToast.error('请为每个签署方指定签署位置');
                    return;
                }

                if (someTextNoName) {
                    this.$MessageToast.error('请为每个文本标签填写名称');
                    return;
                }
                this.handleTip();
            } else {
                // 模板匹配过来不触发审批流
                if (!this.templateMatchId) {
                    // 审批流
                    this.getDefines()
                        .then(res => {
                            this.definesData = res.data;
                            // 触发审批流程
                            if (this.definesData.length) {
                                this.dialogApproval = true;
                            } else {
                                // 不触发审批流程，弹出计费弹窗
                                this.dialogCharge = true;
                            }
                        })
                        .catch(() => {});
                } else {
                    this.dialogCharge = true;
                }
            }
        },
        // 对应类型已盖章或签字
        isMarked(marks, roleId, types) {
            return  marks.some(mark => {
                return (mark.roleId === roleId) && types.includes(mark.type);
            });
        },
        saveTemplate() {
            this.putSaveTemplate()
                .then(() => {
                    // 保存成功直接去模板列表页
                    // this.dialogNowUse = true;
                    this.$MessageToast.success('保存成功').then(() => {
                        /* 如果客户定义了跳转地址，则首先跳转 */
                        if (this.returnUrl) {
                            goReturnUrl(this.returnUrl);
                            return;
                        }
                        if (this.ssoTmpEdit.tmpEdit_1_save && this.ssoTmpEdit.tmpEdit_1_save.url) {
                            this.$router.push(this.ssoTmpEdit.tmpEdit_1_save.url);
                        } else {
                            this.$router.push('/template/list');
                        }
                    });
                });
        },
        handleTip() {
            const errorList = [];
            const receivers = this.$refs.FieldBody.receivers;
            const marks 	  = this.$refs.FieldBody.marks;
            const docList = this.$refs.FieldBody.docList;
            // 先判断下是否有签署人没有印章和签名
            receivers.forEach((receiver) => {
                const { roleId, showName, userType, signType } = receiver;
                const fileList = [];
                docList.forEach((doc, docIndex) => {
                    const { fileName, documentId } = doc;
                    const docMarks = marks.filter(mark => mark.documentId === documentId);
                    // 文档中是否已经包含该receiver的签名和印章
                    // const hasMarked = (docMarks || []).filter(mark => ['SEAL', 'SIGNATURE'].includes(mark.type))
                    //     .some(mark => roleId === mark.roleId);

                    let hasMarked = this.isMarked(docMarks || [], roleId, ['SEAL', 'SIGNATURE']);
                    let hasPartedMarked = false; // 完成了盖章或签字
                    if (signType === 'SEAL_AND_SIGNATURE') {
                        const hasMarkedSeal = this.isMarked(docMarks || [], roleId, ['SEAL']);
                        const hasMarkedSignature = this.isMarked(docMarks || [], roleId, ['SIGNATURE']);
                        hasMarked = hasMarkedSeal && hasMarkedSignature; // 拖了盖章和签字
                        hasPartedMarked = (hasMarkedSeal || hasMarkedSignature); // 拖了章
                    }
                    if (!hasMarked) {
                        fileList.push({
                            fileName,
                            docIndex,
                            hasPartedMarked,
                        });
                    }
                });
                if (fileList.length) {
                    errorList.push({
                        roleId,
                        userName: showName,
                        userType,
                        fileList,
                        signType,
                    });
                }
            });
            if (!errorList.length) {
                this.saveTemplate();
            } else {
                // 先判断是否某个签署人没有签名或者盖章
                const isNone = errorList.some(error => {
                    if ((error.fileList || []).length !== docList.length) {
                        return false;
                    }
                    // 如果是签字并盖章，要判断是否是签字和盖章都没有添加
                    return error.signType !== 'SEAL_AND_SIGNATURE' || error.fileList.every(file => !file.hasPartedMarked);
                });
                if (isNone) {
                    return this.$MessageToast.error('请为每个签署方指定签署位置');
                }
                const hasPartedMarked = errorList.some(error => {
                    if (error.signType === 'SEAL_AND_SIGNATURE') {
                        return error.fileList.filter(file => file.hasPartedMarked).length;
                    }
                    return false;
                });
                if (hasPartedMarked) {
                    return this.$MessageToast.error('指定了“盖章并签字”的签约方，必须同时指定盖章和签字');
                } else {
                    this.dialogTip = true;
                    this.tipList = errorList;
                }
            }
        },
        // SAAS-2351 模板提交审批
        // http://wiki.bestsign.tech/pages/viewpage.action?pageId=11572533
        handleSubmitApproval() {
            this.loading = true;
            // 有混合云需求
            this.$hybrid.makeRequest({
                method: 'post',
                url: `${tempPath}/templates/${this.templateId}/convert-to-contracts_for_approval`,
                hybridTarget: '/templates/convert-to-contracts-for-approval',
                data: {
                    sendPlatform: 'WEB',
                    templateId: this.templateId, // 混3 在 data 中增加 templateId
                },
            })
                .then(() => {
                    this.dialogSendTips = true;
                })
                .catch(() => {

                })
                .finally(() => {
                    this.loading = false;
                });
        },

        // 接收到应用审批流消息，弹出计费弹窗
        onApplyApproval(commitFlow, defInfoId) {
            this.commitFlow 	= commitFlow;
            this.defInfoId 		= defInfoId;
            this.dialogApproval = false;
            this.dialogCharge 	= true;
        },
        postTemplateData() {
            if (this.templateMatchId) {
                return this.sendMatchTemplate({
                    resultId: this.templateMatchId,
                }, { noToast: 1 });
            } else {
                return this.postSendTemplate({
                    defInfoId: this.defInfoId,
                    flowStepList: this.commitFlow,
                }, { noToast: 1 });
            }
        },

        // 计费弹窗——点击确认
        onChargeConfirm() {
            this.loading = true;
            return this.postTemplateData()
                .then(() => {
                    /* 如果客户定义了跳转地址，则首先跳转 */
                    if (this.returnUrl) {
                        this.$MessageToast.success('发送成功').then(() => {
                            goReturnUrl(this.returnUrl);
                        });
                        return;
                    }
                    this.dialogSendTips = true;
                })
                .catch(err => {
                    const msg = err.response.data.message;
                    this.$alert(msg, {
                        confirmButtonText: '确定',
                        customClass: 'field-error-alert',
                    }).then(() => {
                        this.$router.push('/template/list');
                    });
                })
                .finally(() => {
                    this.loading = false;
                    this.dialogCharge = false;
                });
        },

        onInitReceiversData(res) { // res就是receivers数据
            this.guideUserType = res[0].userType;
            res.some(r => {
                if (r.contractPayer) {
                    this.contractPayerAccount = {
                        account: r.userAccount || r.proxyClaimerAccounts.join('或') || '占位符', // 考虑到模版批量上传
                        enterpriseName: r.enterpriseName,
                    };
                }
                return r.contractPayer;
            });
        },
        // ajax
        getContractInfo() {
            return this.$http.get(`${tempPath}/templates/${this.templateId}?type=send`);
        },
        // 获取模板是否有上传附件的权限配置（pms）
        getAuth() {
            return this.$http.get(`${tempPath}/templates/attachment/auth`);
        },
        // 获取审批流列表：
        getDefines() {
            return this.$http.get(`${tempPath}/templates/${this.templateId}/send-defines`);
        },
        // 保存
        putSaveTemplate() {
            return this.$http.put(`${tempPath}/templates/${this.templateId}/save`);
        },
        // 发送模板匹配
        sendMatchTemplate(data, opts) {
            return this.$http({
                method: 'post',
                url: `${tempPath}/templates/match/batch-convert-to-contracts`,
                data: {
                    sendPlatform: 'WEB',
                    ...data,
                },
                opts,
            });
        },
        // 发送
        postSendTemplate(data, opts) {
            const { operateMarkId } = this.$route.query;
            return this.$hybrid.makeRequest({
                method: 'post',
                url: `${tempPath}/multiple-dynamic-signer/${this.templateId}/convert-to-contract${operateMarkId ? '?operateMarkId=' + operateMarkId : ''}`,
                hybridTarget: '/templates/convert-to-contracts',
                data: {
                    templateId: this.templateId, // 混3 在 data 中增加 templateId
                    sendPlatform: 'WEB',
                    ...data,
                },
                opts,
            });
        },
        // 是否为不在企业内网中的混合云用户
        notInLan() {
            return this.$store.getters.getHybridUserType === 'hybrid' && !this.$store.getters.getInLAN;
        },
    },
    async created() {
        this.loading = true;
        // 单点登录不经过列表进入编辑页面时，将模板从使用状态设置为编辑状态
        if (this.isEdit) {
            await this.$http.put(`/template-api/templates/${this.templateId}/start-edit`);
        }

        // 判断混合云网络状态
        fieldLanBreak(() => {
            this.loading = false; this.hybridContractServerConnected = false;
        });

        // 获取该企业是否可以配置上传附件的配置
        this.getAuth().then(res => {
            this.canUploadAccessory = res.data.result || false;
        });
        // 获取合同信息
        this.getContractInfo()
            .then(res => {
                this.contractTitle = res.data.templateName;
                this.canUseAttachment = res.data.canUseAttachment && this.canUploadAccessory;
            })
            .catch(() => {
                this.$router.push('/template/list');
            });
    },
};
</script>

<style lang="scss">
	.temp-field-page {
		user-select: none;
		position: relative;
		height: 100vh;
		font-size: 12px;
		color: #333;
		background-color: #f6f6f6;
		.header {
			z-index: 100;
			position: fixed;
			top: 0;
		}
		.footer {
			position: fixed;
			bottom: 0;
		}

		// 审批流弹窗
		.dialog-approval {
			.el-dialog--small {
				width: auto;
				min-width: 409px;
			}
		}

		// 计费弹窗
		.dialog-charge {
			.el-dialog--small {
				width: 400px;
			}
			.el-dialog__header {
				display: none;
			}
			.el-dialog__body {
				padding: 0;
			}
		}

		// 立即使用弹窗
		.dialog-now-use {
			* {
				box-sizing: border-box;
			}
			.el-dialog {
				width: 333px;
				.el-dialog__header {
					display: none;
				}
				.el-dialog__body {
					padding: 0;
					.icon-bg {
						width: 333px;
						height: 146px;
						text-align: center;
						padding-top: 30px;
						i {
							font-size: 66px;
							color: #3591de;
						}
						div {
							margin-top: 16px;
						}
					}
					.btns {
						padding: 13px 56px;
						background-color: #fff;
						border-top: 1px solid $border-color;
						div {
							float: left;
							width: 100px;
							height: 34px;
							line-height: 34px;
						}
						div:first-child {

						}
						div:last-child {
							margin-left: 20px;
						}
                        div.singleBtn{
                            margin: 0 auto;
                            float: none;
                        }
					}
				}
			}
		}

		// 发送成功提示弹窗
		.dialog-send-tips {
			.el-dialog .el-dialog__body .icon-bg i {
				font-size: 36px;
				color: #2baa3f;
			}
			p {
				font-size: 14px;
			}
			.p-top {
				margin-top: 10px;
			}
			.p-bottom {
				color: #999;
				margin-top: 10px;
			}
		}
	}
	.field-error-alert {
		p {
			white-space: pre-wrap;
			word-wrap: break-word;
		}
	}
</style>
