<template>
    <g class="Signature-cop"
        :class="[ { 'qrcode': mark.type=='QR_CODE' }, { 'cur-default': !isEdit } ]"
        :type="mark.type"
        :transform="`matrix(1,0,0,1, ${x}, ${y})`"
    >

        <!-- 头部 -->
        <g class="owner-title"
            v-if="mark.type == 'SEAL'
                || mark.type == 'SIGNATURE'"
        >
            <rect
                x="0"
                y="-26"
                :width="mark.width"
                height="24"
                fill="#fff7b6"
            />
            <text
                x="6"
                y="-9"
                fill="#333"
                font-size="12"
                text-anchor="start"
            >
                {{ owner || '' }}
            </text>
        </g>

        <!-- 矩形背景 -->
        <rect
            v-if="!(['SINGLE_BOX','MULTIPLE_BOX'].includes(mark.type) && isEdit)"
            class="mark"
            fill-opacity="0.75"
            :fill="fill"
            :stroke="reactStyle.stroke"
            :stroke-width="reactStyle.strokeWidth"
            :width="mark.width"
            :height="mark.height"
            :rx="reactStyle.rx"
            :ry="reactStyle.rx"
            @mousedown="onDragStart(mark.attachmentId, $event,'mark')"
            @click="clickReact('mark')"
            @contextmenu.prevent="openContextmenu"
        >
            <title v-if="mark.type == 'QR_CODE'">签署后扫码，即可查看签署详情、验证签名有效性及该合同是否被篡改</title>
        </rect>
        <rect v-else
            class="mark box-mark-react"
            stroke-dasharray="4"
            fill-opacity="0"
            fill="transparent"
            :stroke="focusing?'#127fd2':'#88CCFF'"
            :stroke-width="reactStyle.strokeWidth"
            :width="mark.width"
            :height="mark.height"
            :rx="reactStyle.rx"
            :ry="reactStyle.rx"
            @mousedown="onDragStart(mark.attachmentId ,$event, 'mark')"
            @click="clickReact('mark')"
            @contextmenu.prevent="openContextmenu"
        >
        </rect>

        <!-- 日期 -->
        <text
            v-if="mark.type=='DATE'"
            x="0"
            y="0"
            fill="#333"
            style="pointer-events: none;"
            :font-size="mark.fontSize"
        >
            <tspan x="1" :dy="textPaddingTop">签署日期</tspan>
        </text>

        <!-- 印章 -->
        <template
            v-else-if="mark.type=='SEAL'"
        >
            <circle cx="134"
                cy="102"
                r="81"
                fill="#fff"
                opacity=".3"
                style="pointer-events: none;"
            />
            <svg class="icon" aria-hidden="true">
                <use style="pointer-events: none;"
                    x="58"
                    y="52"
                    width="154"
                    height="96"
                    :xlink:href="`#${style.type}`"
                >
                </use>
            </svg>
        </template>

        <!-- 签名 -->
        <template
            v-else-if="mark.type=='SIGNATURE'"
        >
            <svg class="icon" aria-hidden="true">
                <use style="pointer-events: none;"
                    x="0"
                    :y="-6"
                    width="134"
                    height="83"
                    :xlink:href="`#${style.type}`"
                >
                </use>
            </svg>
        </template>

        <!-- 文本 -->
        <template
            v-else-if="mark.type=='TEXT' || mark.type=='TEXT_NUMERIC'"
        >
            <template v-if="mark.value">
                <!-- 发起人业务字段填写过长，后端返回mark.value为Array，前端需要做换行处理 -->
                <text style="pointer-events: none;"
                    v-for="(item, index) in mark.value"
                    :key="index"
                    fill="#333"
                    :x="getTextAlignStyle.x"
                    :y="textPaddingTop + index*mark.fontSize"
                    :text-anchor="getTextAlignStyle.alignment"
                    :font-size="mark.fontSize"
                    font-weight="bold"
                >
                    {{ item }}
                </text>
            </template>
            <template v-else>
                <text style="pointer-events: none;"
                    fill="#333"
                    :x="getTextAlignStyle.x"
                    :y="textPaddingTop"
                    :text-anchor="getTextAlignStyle.alignment"
                    :font-size="mark.fontSize"
                >
                    {{ mark.name }}
                </text>
            </template>

        </template>

        <!-- 业务字段日期类型 -->
        <template v-else-if="mark.type=='BIZ_DATE'">
            <template v-if="mark.value">
                <text style="pointer-events: none;"
                    fill="#333"
                    x="0"
                    y="0"
                    :font-size="mark.fontSize"
                    font-weight="bold"
                >
                    <tspan v-for="(item, index) in mark.value"
                        :key="index"
                        x="1"
                        :dy="textPaddingTop"
                    >
                        {{ item }}
                    </tspan>
                </text>

            </template>
            <template v-else>
                <text style="pointer-events: none;"
                    fill="#333"
                    x="0"
                    y="0"
                    :font-size="mark.fontSize"
                >
                    <tspan x="1" :dy="textPaddingTop">{{ mark.name }}</tspan>
                </text>
            </template>
        </template>

        <!-- 临时字段：图片类型 -->
        <template v-else-if="mark.type==='PICTURE'">
            <template v-if="mark.valueStr">
                <image v-lazy="`/template-api/multiple-dynamic-signer/web/download-picture?pictureId=${mark.valueStr}&templateId=${$route.query.templateId}&access_token=${$cookie.get('access_token')}`" :width="mark.width" :height="mark.height"></image>
            </template>
            <template v-else>
                <use width="16" height="16" x="4" y="5" :xlink:href="`#el-icon-ssq-tuchuzhanweifu`"></use>
                <text style="pointer-events: none;"
                    fill="#333"
                    x="22"
                    y="18"
                    :font-size="mark.fontSize"
                >
                    {{ mark.name }}
                </text>
            </template>
        </template>

        <!-- 二维码 -->
        <template
            v-else-if="mark.type=='QR_CODE'"
        >
            <image style="pointer-events: none;"
                :xlink:href="`${mark.imageHref}?access_token=${$cookie.get('access_token')}`"
                :width="mark.width"
                :height="mark.height"
            >
            </image>
        </template>
        <!-- 单选框 -->
        <template
            v-else-if="['SINGLE_BOX','MULTIPLE_BOX'].includes(mark.type)"
        >
            <template v-if="!isEdit">
                <g v-for="(item,index) in mark.buttons" :key="index" class="box-label-button">
                    <image :x="item.buttonX" :y="item.buttonY" :width="markButtonStyle.width" :height="markButtonStyle.height" :xlink:href="mark.type === 'SINGLE_BOX'?signRadio:signCheckbox" />
                    <image :x="item.buttonX"
                        :y="item.buttonY"
                        :width="markButtonStyle.width"
                        :height="markButtonStyle.height"
                        class="box-label-button-select"
                        v-if="mark.valueStr && mark.valueStr.split(',').includes(item.buttonValue)"
                        xlink:href="~img/label/select.png"
                    />
                </g>
            </template>
            <template v-else>
                <!-- 虚线框 -->

                <g class="box-mark-buttons" v-for="(item,index) in mark.buttons" :key="index" @click="clickReact('markButton', index)" @mousedown="onDragStart(mark.attachmentId,$event,'markButton',index)">
                    <rect class="option"
                        :x="item.buttonX-1"
                        :y="item.buttonY-1"
                        rx="2"
                        ry="2"
                        :stroke-width="2"
                        :stroke="(focusing && markButtonInd==index)? '#127fd2':'transparent'"
                        :fill="fill"
                        fill-opacity="0.75"
                        :width="markButtonStyle.width+2"
                        :height="markButtonStyle.height+2"
                        @contextmenu.prevent="openContextmenu"
                    >
                    </rect>
                    <image :x="item.buttonX"
                        :y="item.buttonY"
                        :xlink:href="mark.type === 'SINGLE_BOX'?radioImg:checkboxImg"
                        :width="markButtonStyle.width"
                        :height="markButtonStyle.height"
                        style="pointer-events: none;"
                    ></image>
                </g>
            </template>

        </template>

        <!-- 删除按钮 -->
        <!--
			show的情况：
				编辑模板且标签不为二维码；
				使用模板时新增的附件中的标签且标签不为二维码；
		-->
        <g class="del-btn"
            v-if="mark.type != 'QR_CODE' && (templateStatus == 'edit' || (templateStatus !== 'edit' && mark.attachmentId))"
            @click="clickdelBtn"
        >
            <circle :cx="mark.width" cy="0" r="11" fill="#333" opacity=".75" />
            <use style="pointer-events: none;"
                :x="mark.width - 5"
                :y="-5"
                width="10"
                height="10"
                fill="#fff"
                xlink:href="#el-icon-ssq-guanbi"
            >
            </use>
        </g>

        <!-- 增加选择框 -->
        <g class="add-btn"
            v-if="['MULTIPLE_BOX','SINGLE_BOX'].includes(mark.type)&& templateStatus == 'edit'"
            @click="addOption"
            :transform="addBtnStyle.transform"
        >
            <rect width="28"
                height="16"
                :x="addBtnStyle.x"
                :y="addBtnStyle.y"
                fill="transparent"
                fill-opcity="0"
            />
            <image width="28"
                height="16"
                :x="addBtnStyle.x"
                :y="addBtnStyle.y"
                :xlink:href="addImg"
                style="pointer-events: none;"
            ></image>
        </g>

    </g>
</template>
<script>
import { markIconInfo } from 'src/pages/foundation/sign/common/info/info.js';
import radioImg from 'img/label/radio.png';
import checkboxImg from 'img/label/checkbox.png';
import addImg from 'img/label/add.png';
import { markInfo } from 'src/pages/foundation/sign/common/info/info.js';
import signRadio from 'img/label/signRadio.png';
import signCheckbox from 'img/label/signCheckbox.png';
export default {
    props: {
        'isEdit': {
            type: Boolean,
        },
        'owner': {
            type: String,
            default: '',
        },
        'focusing': {
            type: Boolean,
        },
        'mark': {
            type: Object,
            default: () => {},
        },
        'x': {
            type: Number,
            default: 0,
        },
        'y': {
            type: Number,
            default: 0,
        },
        'fill': {
            type: String,
            default: '',
        },
        'contractId': {
            type: String,
            default: '',
        },
        'hasAttachment': {
            type: Boolean,
            default: false,
        },
        'markButtonInd': {
            type: Number,
            default: -1,
        },
        'zoom': {
            type: Number,
            default: 1,
        },
        'pageX': {
            type: Number,
            default: 0,
        },
        'pageY': {
            type: Number,
            default: 0,
        },
    },
    data() {
        // let isQRCode = this.mark.type == 'QR_CODE';
        return {
            templateStatus: this.$route.params.templateStatus,
            textSpace: 0,
            // textFontSize: 14,

            dragging: false,
            // 点击还是拖拽的判断
            startTime: '',
            endTime: '',
            isClick: false,
            curSignatureDocType: 'CONTRACT', // 当前标签属于的文档类型'CONTRACT'合同 'ATTACHMENT'附件
            radioDragging: false,
            radioImg,
            addImg,
            checkboxImg,
            signRadio,
            signCheckbox,
            move: { // 移动的单选框，复选框按钮

            },
            markButtonStyle: markInfo(this.mark.type).button || {},
            moveMetaData: {}, // 移动过程中的修改
        };
    },
    computed: {
        textPaddingTop() {
            return (this.mark.fontSize || 12) + 1;
        },
        getTextAlignStyle() {
            if (this.mark.alignment === 0 || !this.mark.alignment) {
                return {
                    x: 0,
                    alignment: 'inhert',
                };
            }
            // 居中 :x="mark.width/2" text-anchor="middle"
            if (this.mark.alignment === 1) {
                return {
                    x: this.mark.width / 2,
                    alignment: 'middle',
                };
            }
            // 居右 :x="mark.width" text-anchor="end"
            return {
                x: this.mark.width,
                alignment: 'end',
            };
        },
        style() {
            return markIconInfo(this.mark.type);
        },
        reactStyle() {
            return  {
                stroke: this.focusing ? '#127fd2' : 'transparent',
                strokeWidth: '2',
                rx: '2',
            };
        },
        addBtnStyle() {
            const mark = this.mark;
            // if (mark.buttonDirection == 'V' ){
            return {
                x: (mark.width - 28) / 2,
                y: mark.height + 2,
                transform: '',
            };
            // } else {
            //     return {
            //         x: mark.width - 4,
            //         y: (mark.height - 16)/2,
            //         transform: `rotate(90 ${mark.width+14-4} ${mark.height/2})`
            //     }
            // }
        },
        markAddWidth() { // 增加选择按钮时，标签需增加的宽度
            return this.markButtonStyle.initSplit + this.markButtonStyle.width;
        }, // 横向时标签宽度需增加
        markAddHeight() {
            return this.markButtonStyle.initSplit + this.markButtonStyle.height;
        },
    },
    methods: {
        clickReact(type, index) {
            if (!this.isClick) {
                return;
            }
            this.$emit('mark-click', index);
        },
        onDragStart(attachmentId, e, type, index = -1) {
            if (attachmentId) {
                this.curSignatureDocType = 'ATTACHMENT';
            }
            // 使用模板时，标签不是附件中的标签，阻止拖拽
            if (!this.isEdit && this.curSignatureDocType !== 'ATTACHMENT') {
                return;
            }
            this.startTime = new Date().getTime();
            this.dragging = true;
            if (type === 'markButton') { // 选项按钮选择当前，并准备移动
                this.$emit('mark-click', index);
                this.radioDragging = true;
                const item = this.mark.buttons[index];
                this.move = {
                    ex: e.x,
                    ey: e.y,
                    startX: item.buttonX,
                    startY: item.buttonY,
                };
            } else {
                // 二维码需要跳转到二维码合同信息页
                if (e.button === 0) {
                    this.$emit('mark-start', e);
                }
            }

            document.addEventListener('mousemove', this.onDragMove);
            document.addEventListener('mouseup', this.onDragEnd);
        },
        onDragMove(e) {
            if (!this.dragging || (!this.isEdit && this.curSignatureDocType !== 'ATTACHMENT')) {
                return;
            }
            if (this.radioDragging) { // 选项按钮移动
                const item = this.mark.buttons[this.markButtonInd];
                const markButtonStyle = this.markButtonStyle;

                const moveX = (e.x - this.move.ex) / this.zoom;
                const moveY = (e.y - this.move.ey) / this.zoom;
                // const buttonDirection = moveX / moveY  >= 1 ? 'W':'H';
                // if (buttonDirection === 'H') { // 横向移动
                // const moveX = (e.x - this.move.ex) / this.zoom;
                item.buttonX = this.move.startX + moveX;
                const maxX = this.pageX - this.mark.x - this.markAddWidth;
                item.buttonX = item.buttonX < markButtonStyle.initSplit ? markButtonStyle.initSplit : (item.buttonX > maxX ? maxX : item.buttonX);
                // } else { // 竖向移动
                // const moveY = (e.y-this.move.ey) / this.zoom;
                item.buttonY = this.move.startY + moveY;
                const maxY = this.pageY - this.mark.y - this.markAddHeight;
                item.buttonY = item.buttonY < markButtonStyle.initSplit ? markButtonStyle.initSplit : (item.buttonY > maxY ? maxY : item.buttonY);
                // }
                this.$set(this.mark.buttons, this.markButtonInd, item);
                this.moveMetaData = this.changeMarkSize(this.mark.buttons);
                // if (buttonDirection === 'H')
                this.mark.width = this.moveMetaData.width;
                // else
                this.mark.height = this.moveMetaData.height;
            } else { // 标签移动
                this.$emit('mark-move', e);
            }
        },
        onDragEnd(e) {
            if (!this.isEdit && this.curSignatureDocType !== 'ATTACHMENT') {
                return;
            }
            this.endTime = new Date().getTime();
            this.isClick = this.endTime - this.startTime < 200;

            document.removeEventListener('mousemove', this.onDragMove);
            document.removeEventListener('mouseup', this.onDragEnd);
            this.dragging = false;
            if (this.isClick) {
                this.radioDragging = false;
                return;
            }
            if (this.radioDragging) {
                this.radioDragging = false;
                this.markButtonChange(this.markButtonInd, this.moveMetaData); // 标签数据更新
            } else {
                this.$emit('mark-end', e);
            }
        },
        clickdelBtn(e) {
            const isDelButton = ['SINGLE_BOX', 'MULTIPLE_BOX'].includes(this.mark.type) && this.markButtonInd !== -1 && this.mark.buttons.length > 1 && this.focusing;
            if (isDelButton) { // 删除选项按钮
                const newButtons = this.mark.buttons;
                newButtons.splice(this.markButtonInd, 1);
                const newMarkButtonInd = newButtons.length > this.markButtonInd ? this.markButtonInd : this.markButtonInd - 1;
                this.markButtonChange(newMarkButtonInd, this.changeMarkSize(newButtons));
            } else {
                this.$emit('delete-signature', e);
            }
        },
        openContextmenu(e) {
            this.$emit('openContextmenu', e);
        },
        addOption() { // 增加选项按钮
            if (this.mark.buttons.length >= 20) {
                this.$MessageToast.info('选择器个数已达上限');
                return;
            }
            const markButtonStyle = this.markButtonStyle;
            // const hCanAdd = this.mark.buttonDirection == 'H' && (this.mark.width + this.markAddWidth < this.pageX - this.mark.x);
            // const vCanAdd = this.mark.buttonDirection == 'V' && (this.mark.height + this.markAddHeight  < this.pageY - this.mark.y);
            const vCanAdd = (this.mark.height + this.markAddHeight  < this.pageY - this.mark.y);
            if (vCanAdd) { // 竖向还可以加
                // const buttonX = hCanAdd ? this.mark.width - markButtonStyle.initSplit + markButtonStyle.split : markButtonStyle.initSplit;
                const buttonX = this.mark.width - this.markAddWidth;
                // const buttonY = hCanAdd ? markButtonStyle.initSplit : this.mark.height  - markButtonStyle.initSplit + markButtonStyle.split;
                const buttonY = this.mark.height  - markButtonStyle.initSplit + markButtonStyle.split;
                const newButtons =  Object.assign([], this.mark.buttons);
                newButtons.push({
                    buttonX,
                    buttonY,
                    buttonValue: this.formatButtonValue(),
                });
                this.markButtonChange(newButtons.length - 1, this.changeMarkSize(newButtons)); // 数据更新
            } else {
                this.$MessageToast.info('超出页面边界，无法添加');
            }
        },
        // 选项改变，标签大小跟着改变
        changeMarkSize(newButtons) {
            // const buttonDirection = this.mark.buttonDirection;
            const metaData = {
                buttons: newButtons,
            };
            const list = [].concat(newButtons);
            metaData.width = list.sort((a, b) => b.buttonX - a.buttonX)[0].buttonX + this.markAddWidth;
            // metaData.width = listH[0].buttonX + this.markAddWidth
            metaData.height = list.sort((a, b) => b.buttonY - a.buttonY)[0].buttonY + this.markAddHeight;

            // if (buttonDirection === 'H')
            // metaData.width = listH[0].buttonX + this.markAddWidth
            // else
            // metaData.height = listV[0].buttonY + this.markAddHeight;
            return metaData;
        },
        markButtonChange(markButtonInd, metaData) { // 选项数据更新
            this.$emit('mark-button-change', { metaData, markButtonInd });
        },
        // 选项按钮值不能相同
        formatButtonValue() {
            let sameNameLabel;
            let count = this.mark.buttons.length;
            do {
                count++;
                sameNameLabel = this.mark.buttons.filter(a => {
                    return a.buttonValue === `备选项${count}`;
                });
            } while (sameNameLabel.length > 0);

            return `备选项${count}`;
        },
    },
};
</script>
<style lang="scss">
	.Signature-cop {
		cursor: move;
		&.qrcode {
			cursor: pointer;
		}
		.owner-title {
			cursor: default;
		}
		.del-btn,.add-btn {
			cursor: pointer;
		}
    }
    .box-mark-buttons *,.add-btn *,.del-btn *{
         transition: x,y,width,height .5s;
    }
    .box-mark-react{
        transition: x,y .5s;
    }
</style>
