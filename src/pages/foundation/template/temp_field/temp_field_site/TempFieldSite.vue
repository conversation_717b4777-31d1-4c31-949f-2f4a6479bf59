<template>
    <div class="temp-field-site-cpn" v-if="receivers.length">

        <!-- 选择收件人菜单 -->
        <div class="site-bar-head">
            <span class="order-num">第一步：</span>
            <span class="head-title">选择签约方</span>
        </div>
        <div class="sigers">
            <ul>
                <el-tooltip class="item"
                    effect="dark"
                    v-for="(item, index) in receivers"
                    :key="index"
                    :content="item.showName"
                    popper-class="FieldSite-cpn-popper"
                    placement="right"
                >
                    <li @click="switchReceiver(index)">
                        <span class="signers-color" :style="computeBgc(index)"></span>
                        <span class="signers-name etc-sigle">{{ item.showName }}</span>
                        <i class="signers-active el-icon-ssq-qianyuewancheng" v-show="index == receiverIndex"></i>
                    </li>
                </el-tooltip>
            </ul>
        </div>

        <!-- 字段 -->
        <div class="site-bar-head">
            <span class="order-num">第二步：</span>
            <span class="head-title">拖动签署位置</span>
        </div>

        <div class="site-bar-body">
            <div class="site-bar-title">签署字段</div>
            <div class="site-bar-content">
                <ul>
                    <li v-for="item in signIcons"
                        :key="item.class"
                        v-show="item.show"
                        @mousedown="onDragStart($event, item.type, item.class, item.name)"
                    >
                        <i :class="item.class" :style="computeBgc(receiverIndex)"></i>
                        <span>{{ item.name }}</span>
                    </li>
                </ul>
            </div>
            <!-- 编辑模板的时候，展示签署字段外的其他字段 -->
            <!-- 使用模板的时候不展示 -->
            <template v-if="isEdit">
                <!-- 内容字段是否展示 -->
                <template>
                    <div class="site-bar-title">
                        临时字段
                        <i class="el-icon-ssq-wenhao" @click="dialogTempMetaDesc.show = true"></i>
                    </div>
                    <div class="site-bar-content">
                        <ul>
                            <template v-for="item in contentIcons">
                                <li
                                    :key="item.class"
                                    v-if="!item.notShow"
                                    @mousedown="onDragStart($event, item.type, item.class, item.name,item.receiverFill)"
                                >
                                    <i :class="item.class"></i>
                                    <span>{{ item.name }}</span>
                                </li>
                            </template>
                        </ul>
                    </div>
                </template>

                <!-- 业务字段是否显示 -->
                <template>
                    <div class="site-bar-title metaLabel">
                        业务字段
                        <i class="el-icon-ssq-wenhao" @click="dialogMetaDesc.show = true"></i>

                        <el-tooltip class="item"
                            effect="dark"
                            content="添加业务字段"
                            placement="top"
                        >
                            <i class="add-metaLabel el-icon-ssq-jia"
                                v-if="couldManageBizField && (bizFields.length < 500)"
                                @click="handleAddBizField"
                            ></i>
                        </el-tooltip>

                        <el-tooltip class="item"
                            effect="dark"
                            content="管理业务字段"
                            placement="top"
                            v-if="couldManageBizField"
                        >
                            <a href="/console/enterprise/contract/bizField" class="toManageLink" target="_blank">
                                <i class="el-icon-ssq-shezhi"></i>
                            </a>
                        </el-tooltip>
                    </div>
                    <div class="site-bar-content metaLabel-content biz-metaLabel-content"
                        v-show="bizFields.length"
                    >
                        <div class="site-bar-bizname-search">
                            <el-input placeholder="输入需查找的业务字段名称" icon="search" size="small" v-model="bizNameSearchValue" @change="handleChange"></el-input>
                        </div>
                        <ul class="metaLabel-content-ul">
                            <li
                                v-for="item in filterBizFields"
                                :key="item.fieldId"
                                @mousedown="onDragStart($event, item.labelDataType, fieldIconMap[item.labelDataType], item.name, item.receiverFill, item.fontSize, item.necessary, item.labelMetaId, item.buttons)"
                            >
                                <i :class="`${fieldIconMap[item.labelDataType]}`"></i>
                                <span
                                    v-if="item.name.length < 10"
                                    class="metaLabel-name"
                                >
                                    {{ item.name }}
                                </span>
                                <el-tooltip
                                    v-else
                                    class="item"
                                    effect="dark"
                                    :content="item.name"
                                    placement="bottom"
                                    popper-class="temp-field-site-cpn__metaLabel-name-popper"
                                >
                                    <span class="metaLabel-name">
                                        {{ item.name }}
                                    </span>
                                </el-tooltip>
                                <span class="metaLabel-operate-block" @mousedown.prevent.stop v-if="couldManageBizField">
                                    <i class="metaLabel-operate-icon el-icon-ssq-gengduo"></i>

                                    <ul class="metaLabel-hidden-operate">
                                        <li @mousedown.prevent.stop="handleEditBizField(item)">编辑</li>
                                    </ul>
                                </span>
                            </li>
                        </ul>
                    </div>
                </template>

                <!-- 内容字段是否展示 -->
                <template v-if="contentMetaVisible">
                    <div class="site-bar-title metaLabel">
                        自定义字段
                        <span class="metaUploadBtn" @click="dialogFieldUpdate.show = true">升级为业务字段</span>
                    </div>
                    <div class="site-bar-content metaLabel-content"
                        v-show="metaLabels.length"
                    >
                        <ul class="metaLabel-content-ul">
                            <li
                                v-for="(item, index) in metaLabels"
                                :key="index"
                                @mousedown="onDragStart($event, item.labelDataType, 'el-icon-ssq-wenben', item.name, item.receiverFill, item.fontSize, item.necessary)"
                            >
                                <i class="el-icon-ssq-wenben"></i>
                                <span class="metaLabel-name">{{ item.name }}</span>

                                <span class="metaLabel-operate-block" @mousedown.prevent.stop>
                                    <i class="metaLabel-operate-icon el-icon-ssq-gengduo"></i>

                                    <ul class="metaLabel-hidden-operate">
                                        <li @mousedown.prevent.stop="clickEditMeta(index)">编辑</li>
                                        <li @mousedown.prevent.stop="clickDeleteMeta(index)">删除</li>
                                    </ul>
                                </span>
                            </li>
                        </ul>
                    </div>
                </template>

                <div class="site-bar-title decorate-title">
                    合同装饰
                    <span>（选填）</span>
                </div>
                <div class="site-bar-content">
                    <ul>
                        <li v-for="item in decorationIcons"
                            :key="item.class"
                            v-show="item.show"
                            @click="onAddDecorator(item.type)"
                            @mousedown="onDragStart($event, item.type, item.class, item.name,item.receiverFill)"
                        >
                            <i :class="item.class" :style="computeBgc(receiverIndex)"></i>
                            <el-tooltip
                                :value="item.show && checkTrialInfoStatus(item.type,trialFeatureData,1)&& isShowTrialTip"
                                :manual="true"
                                effect="light"
                                placement="right"
                                popper-class="dropdown-tool-tip"
                            >
                                <div slot="content" @click="handleTrialClick(trialFeatureData,item.type)" class="content"><i class="el-icon-ssq-jingshitanhaox"></i>{{ checkTrialInfoStatus(item.type,trialFeatureData,3) }}</div>
                                <span>
                                    <span>{{ item.name }}</span>
                                    <i v-if="checkTrialInfoStatus(item.type,trialFeatureData,2)" class="el-icon-ssq-biaoqiannew"></i>
                                </span>

                            </el-tooltip>
                        </li>
                    </ul>
                </div>
            </template>
        </div>

        <!-- ================================== 华丽的分割线 ====================================== -->

        <!-- 添加 | 编辑自定义字段dialog -->
        <!-- <el-dialog class="ssq-dialog dialog-addMetaLabel"
                   :title="dialogAddMeta.type === 'add' ? '添加自定义字段' : '编辑自定义字段'"
                   :visible.sync="dialogAddMeta.show">
            <div class="dialog-addMetaLabel-content">
                <div class="title">名称</div>
                <el-input
                    placeholder="请输入名称字段"
                    v-model="name"
                    :maxlength="10"></el-input>
                <div class="dialog-addMetaLabel-btns">
                    <div class="ssq-btn-confirm"
                         v-if="dialogAddMeta.type === 'add'"
                         @click="clickConfirmAdd">确定</div>
                    <div class="ssq-btn-confirm"
                         v-else
                         @click="clickConfirmRename">确定</div>
                    <div class="ssq-btn-cancel"
                         @click="dialogAddMeta.show = false">取消</div>
                </div>
            </div>
        </el-dialog>-->
        <el-dialog class="ssq-dialog dialog-addMetaLabel"
            :title="dialogAddMeta.type === 'add'
                ? '添加自定义字段'
                : '编辑自定义字段'"
            :visible.sync="dialogAddMeta.show"
        >
            <CustomFieldEditor
                :ImmediateSave="false"
                :editorMetaData="editorMetaData"
                @confirm="onEditMetaConfirm"
                @cancel="dialogAddMeta.show = false"
            >
            </CustomFieldEditor>
        </el-dialog>

        <div class="box-sizing-dialog">
            <!-- 删除自定义字段二次确认 -->
            <el-dialog class="el-dialog-bg dialog-confirm-deleteLabel"
                title="删除"
                :visible.sync="dialogDelMeta.show"
                size="tiny"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
            >
                <p>已经放置到合同中的字段不会一起删除</p>
                <p><strong>确定删除该自定义字段吗？</strong></p>
                <div slot="footer" class="dialog-footer">
                    <el-button type="primary" @click="clickConfirmDelete(dialogDelMeta.index)">确 定</el-button>
                    <el-button @click="dialogDelMeta.show = false">取 消</el-button>
                </div>
            </el-dialog>

            <!-- 没有业务管理权限 -->
            <!--<el-dialog class="el-dialog-bg dialog-no-promission"
                       :title="dialogNoPromission.title"
                       :visible.sync="dialogNoPromission.show"
                       size="tiny"
                       :close-on-click-modal="false"
                       :close-on-press-escape="false">
                <p>您没有“业务管理”的权限，请联系管理员给您增加“业务管理”权限</p>
                <div slot="footer" class="dialog-footer">
                    <el-button type="primary" @click="dialogNoPromission.show = false">知道了</el-button>
                </div>
            </el-dialog>-->

            <!-- 临时字段的概念描述 -->
            <el-dialog class="el-dialog-bg dialog-tempMeta-desc"
                title="什么是临时字段？"
                :visible.sync="dialogTempMetaDesc.show"
                size="tiny"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
            >
                <p>临时字段可用于设置模板变量，设置后只在该模板生效，无法在其他模板重复使用。</p>
                <p>发起模板后在临时字段内填入的合同内容暂时不支持搜索。</p>
                <div slot="footer" class="dialog-footer">
                    <el-button type="primary" @click="dialogTempMetaDesc.show = false">知道了</el-button>
                </div>
            </el-dialog>

            <!-- 业务字段的概念描述 -->
            <el-dialog class="el-dialog-bg dialog-meta-desc"
                title="什么是业务字段？"
                :visible.sync="dialogMetaDesc.show"
                size="tiny"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
            >
                <p>业务字段可用于设置模板变量，设置后企业成员均可在设置模板时重复使用。</p>
                <p>发起模板后在业务字段内填入的合同内容可搭配合同管理的“列表配置”功能支持查看和搜索。</p>
                <div slot="footer" class="dialog-footer">
                    <el-button type="primary" @click="dialogMetaDesc.show = false">知道了</el-button>
                </div>
            </el-dialog>

            <!-- 升级为业务字段 -->
            <FieldUpdateDialog
                :labels="metaLabels"
                :param="dialogFieldUpdate"
                @close="dialogFieldUpdate.show = false"
                @edit="handleGetLabels"
                @update="handleRefreshFields"
            >
            </FieldUpdateDialog>

            <!-- 创建、编辑业务字段 -->
            <EditBizFieldDialog
                v-if="dialogBizField.show"
                :curRow="dialogBizField.curField"
                @update="getValidBizFields"
                @close="handleCloseBizDialog"
            >
            </EditBizFieldDialog>
        </div>

    </div>
</template>
<script>
    // 复用sign colorInfo
import { rgbColorInfo } from 'utils/colorInfo.js';
import CustomFieldEditor from '../custom_field_editor/CustomFieldEditor.vue';
import FieldUpdateDialog from '../../common/fieldUpdateDialog/Temp-Field-Update-Dialog.vue';
import EditBizFieldDialog from 'enterprise_pages/console/contract/bizField/DialogCreate.vue';
import { mapGetters, mapState } from 'vuex';
import Bus from 'components/bus/bus.js';
import { advancedFeatureMixin } from 'src/mixins/advancedFeature.js';

// 将rgbColorInfo中第一个默认的蓝色保留给发件方和业务字段颜色使用
const colorInfo = rgbColorInfo.slice(1);

export default {
    components: {
        CustomFieldEditor,
        FieldUpdateDialog,
        EditBizFieldDialog,
    },
    mixins: [advancedFeatureMixin],
    props: {
        receivers: {
            type: Array,
            default: () => [],
        },
        isEdit: {
            type: Boolean,
            default: false,
        },
        hasAttachment: {
            type: Boolean,
            default: false,
        },
        marks: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            receiverIndex: 0,
            /* meta: {
                    'labelId': 'string', // 标签编号
                    'labelName': 'string', // 标签名称
                    'labelWidth': 'number', // 标签长度
                    'labelType': 'string' // 标签类型
                }, */
            metaLabels: [],

            bizFields: [],

            curBizField: {},

            // 内容字段是否显示
            contentMetaVisible: false,

            editorMetaData: {
                name: '',
                receiverFill: false,
                fontSize: '14.5',
                necessary: true,
            },

            dialogAddMeta: {
                show: false,
                type: 'add',
                index: 0,
            },
            dialogDelMeta: {
                show: false,
                index: null,
            },
            /* dialogNoPromission: {
                    show: false,
                    title: '添加业务字段，管理业务字段'
                }, */
            dialogTempMetaDesc: {
                show: false,
            },
            dialogMetaDesc: {
                show: false,
            },
            dialogAddBizField: {
                show: false,
                type: 'add',
            },
            dialogFieldUpdate: {
                show: false,
            },
            dialogBizField: {
                show: false,
                curField: null,
            },
            bizNameSearchValue: '', // 搜索业务字段的输入内容
            filterBizFields: [], // 搜索过滤后的业务字段数组
            fieldIconMap: {
                'BIZ_DATE': 'el-icon-ssq-riqi',
                'TEXT': 'el-icon-ssq-wenben',
                'SINGLE_BOX': 'el-icon-ssq-danxuananniu',
                'MULTIPLE_BOX': 'el-icon-ssq-fuxuankuang',
            },
            isShowTrialTip: false, // 是否显示高级功能试用tip
        };
    },
    computed: {
        ...mapGetters(['getUserPermissons', 'checkFeat', 'checkAdvancedFeatureData']),
        ...mapState(['commonHeaderInfo']),
        signIcons() {
            const { signType, userType } = this.receivers[this.receiverIndex];
            return [
                {
                    class: 'el-icon-ssq-gongzhang',
                    type: 'SEAL',
                    name: '盖章',
                    show: signType !== 'SIGNATURE',
                },
                {
                    class: 'el-icon-ssq-bi',
                    type: 'SIGNATURE',
                    name: (signType === 'SIGNATURE' && userType === 'PERSON') ? this.$t('field.signature') : '盖章人签字',
                    show: signType !== 'SEAL',
                },
                {
                    class: 'el-icon-ssq-riqi',
                    type: 'DATE',
                    name: '签署日期',
                    show: true,
                },
            ];
        },
        contentIcons() {
            return [
                {
                    class: 'el-icon-ssq-wenben',
                    type: 'TEXT',
                    name: '文本',
                    receiverFill: false,
                    // show: this.isPlaceHolder
                },
                {
                    class: 'el-icon-ssq-danxuananniu',
                    type: 'SINGLE_BOX',
                    name: '单选框',
                    receiverFill: false,
                    notShow: this.commonHeaderInfo.hybridServer && this.$hybrid.isAlpha(), // 混合云1.0不支持单选框，复选框
                },
                {
                    class: 'el-icon-ssq-fuxuankuang',
                    type: 'MULTIPLE_BOX',
                    name: '复选框',
                    receiverFill: false,
                    notShow: this.commonHeaderInfo.hybridServer && this.$hybrid.isAlpha(),
                },
            ];
        },
        decorationIcons() {
            const userType = this.receivers[this.receiverIndex].userType;
            return [
                {
                    class: 'el-icon-ssq-shuiyin', // 水印
                    type: 'WATERMARK',
                    name: '水印',
                    show: this.isEdit ? true : this.checkFeat.contractDecoration,
                    notDrag: true,
                }, {
                    class: 'el-icon-ssq-qifengzhang', // 骑缝章
                    type: 'DECORATE_RIDING_SEAL',
                    name: '骑缝章',
                    show: userType === 'ENTERPRISE' && (this.isEdit ? true : this.checkFeat.contractDecoration),
                    notDrag: true,
                }, {
                    class: 'el-icon-ssq-tuchuzhanweifu',
                    type: 'PICTURE',
                    name: '图片',
                    show: !(this.commonHeaderInfo.hybridServer && this.$hybrid.isAlpha()), // 混合云1.0 不展示 临时业务字段-图片类型 入口
                    notDrag: false,
                },
            ];
        },
        isPlaceHolder() {
            const receiver = this.receivers[this.receiverIndex] || {};
            return receiver.roleType === 'PLACEHOLDER';
        },
        // 是否有管理业务字段权限
        couldManageBizField() {
            // 集团垂直管控子企业不具有业务管理权限
            return this.getUserPermissons.sign_m && !(+this.commonHeaderInfo.groupVersion === 2 && this.commonHeaderInfo.hasGroupConsole === false);
        },
        // 高级功能试用信息
        trialFeatureData() {
            return {
                WATERMARK: this.checkAdvancedFeatureData('68'), // 水印
                DECORATE_RIDING_SEAL: this.checkAdvancedFeatureData('68'), // 骑缝章
            };
        },
    },
    methods: {

        handleChange() {
            if (!this.bizNameSearchValue.trim()) {
                this.filterBizFields = this.bizFields;
            }
            this.filterBizFields = this.bizFields.filter(item => {
                return item.bizName.toLowerCase().includes(this.bizNameSearchValue);
            });
        },
        // utils
        computeBgc(i) {
            return `background-color: rgba(${colorInfo[i % 8]}, 0.6);
                        border: 1px solid rgb(${colorInfo[i % 8]})`;
        },
        // 切换接受人
        switchReceiver(v) {
            this.receiverIndex = v;
            this.$emit('switch-receiver', {
                receiverIndex: v,
                colorIndex: v % 8,
            });
        },
        onDragStart(e, type, className, name, receiverFill, fontSize, necessary, labelMetaId, buttons = null) {
            const filterDecorationIcons = this.decorationIcons.filter(item => item.type === type);
            // 骑缝章、水印 去掉拖拽
            if (filterDecorationIcons.length && filterDecorationIcons[0].notDrag) {
                return false;
            }
            if (!['TEXT', 'DATE', 'BIZ_DATE', 'TEXT_NUMERIC', 'SINGLE_BOX', 'MULTIPLE_BOX'].includes(type)) {
                this.$emit('site-drag-start', e, type, className, name);
            } else {
                this.$emit('site-drag-start', e, type, className, name, receiverFill, fontSize, necessary, labelMetaId, buttons);
            }
        },
        onAddDecorator(type) {
            if (Object.keys(this.trialFeatureData).includes(type)) {
                // 高级功能未开启，提示
                if (!this.checkTrialInfo(this.trialFeatureData[type])) {
                    return;
                }
            }
            Bus.$emit('add-decorator', type);
        },

        // 添加自定义字段
        clickAddMeta() {
            this.editorMetaData = {
                name: '',
                receiverFill: false,
                fontSize: 14,
                necessary: true,
            };
            this.dialogAddMeta.show = true;
            this.dialogAddMeta.type = 'add';
        },
        // 编辑自定义字段弹窗
        clickEditMeta(index) {
            const {
                name = '',
                receiverFill = false,
                fontSize = '14.5',
                necessary = true,
            } = this.metaLabels[index];
            this.editorMetaData = {
                name,
                receiverFill,
                fontSize,
                necessary,
            };
            this.dialogAddMeta.show = true;
            this.dialogAddMeta.type = 'edit';
            this.dialogAddMeta.index = index;
        },
        // 确认编辑自定义字段
        onEditMetaConfirm(editData) {
            const data = {
                labelWidth: 80,
                labelType: 'TEXT',
                ...editData,
            };
            if (!editData.name) {
                this.$MessageToast.error('请填写名称！');
                return;
            }
            if (this.dialogAddMeta.type === 'add') {
                this.postMetaLabels(data)
                    .then(res => {
                        this.dialogAddMeta.show = false;
                        this.metaLabels.push(res.data);
                    });
            } else if (this.dialogAddMeta.type === 'edit') {
                const i = this.dialogAddMeta.index;
                const labelMeta = this.metaLabels[i];
                const labelMetaId = labelMeta.labelMetaId;
                this.putMetaLabels(labelMetaId, data)
                    .then(() => {
                        this.$MessageToast.success('保存成功');
                        this.dialogAddMeta.show = false;
                        this.$set(this.metaLabels, i, {
                            ...labelMeta,
                            ...editData,
                        });
                    });
            }
        },

        // 删除自定义弹窗
        clickDeleteMeta(index) {
            this.dialogDelMeta.show = true;
            this.dialogDelMeta.index = index;
        },
        // 确认删除自定义字段
        clickConfirmDelete(index) {
            this.deleteMetaLabel(this.metaLabels[index].labelMetaId)
                .then(() => {
                    this.metaLabels.splice(index, 1);
                    this.dialogDelMeta.show = false;
                });
        },

        // 获取自定义字段
        handleGetLabels() {
            // 升级业务字段后判断如果没有自定义字段了就隐藏模块
            this.getMetaLabels().then(res => {
                this.metaLabels = res.data;
            });
        },

        // 显示添加业务字段弹窗
        handleAddBizField() {
            this.dialogBizField.curField = {
                operateType: 'new',
            };
            this.dialogBizField.show = true;
        },
        // 显示编辑业务字段弹窗
        handleEditBizField(item) {
            // 编辑或删除之前首先查询是否曾经应用过模板  SAAS-5292 remove limit
            // this.$http.get(`/template-api/bizfield/is-applied/${item.fieldId}`).then(({ data }) => {
            // const hasApplied = data.result;
            this.dialogBizField.curField = Object.assign({}, item, {
                operateType: 'edit',
                // hasApplied,
            });
            this.dialogBizField.show = true;
            // });
        },
        // 刷新业务字段
        handleRefreshFields() {
            this.getValidBizFields()
                .then(res => {
                    const result = (res.data.result || []).filter(item => item.bizFieldType !== 'COMBO_BOX');
                    this.mappingFieldToMata(result);
                });
        },
        // 关闭业务字段弹窗
        handleCloseBizDialog(status) {
            this.dialogBizField.show = false;

            if (status === true) {
                this.handleRefreshFields();
            }
        },
        // 获取是否显示临时字段的判断值
        // 这里判断的条件是 4.10.2 版本前创建过模板的所有企业成员
        getFieldVisible() {
            return this.$http.get(`${tempPath}/templates/individual_contract/used_template`);
        },
        // 获取自定义标签
        getMetaLabels() {
            return this.$http.get(`${tempPath}/meta-labels/`);
        },
        // 创建自定义标签
        postMetaLabels(data) {
            return this.$http.post(`${tempPath}/meta-labels/`, data);
        },
        // 编辑自定义标签
        putMetaLabels(id, data) {
            return this.$http.put(`${tempPath}/meta-labels/${id}/`, data);
        },
        // 删除自定义标签
        deleteMetaLabel(id) {
            return this.$http.delete(`${tempPath}/meta-labels/${id}/`);
        },
        // 获取启用中的业务字段
        getValidBizFields() {
            return this.$http.get(`${tempPath}/bizfields/valid`);
        },
        // 映射业务字段值为自定义标签值，保持原来代码逻辑不动
        mappingFieldToMata(data) {
            if (!data.length) {
                return;
            }

            // 初始化 bizFields数据
            data.forEach(item => {
                item.labelMetaId = item.fieldId;
                item.labelDataType = item.bizFieldType;
                item.name = item.bizName;
                item.receiverFill = item.writeBy !== 'SENDER';
            });
            this.bizFields = [];
            this.bizFields = this.bizFields.concat(data);
            // 业务字段初始化或者更新之后，触发一下搜索事件，重新获取当前搜索的结果
            this.handleChange();
        },
    },
    created() {
        this.getFieldVisible().then(res => {
            this.contentMetaVisible = res.data;
        });

        this.handleGetLabels();

        this.handleRefreshFields();
    },
    mounted() {
        setTimeout(() => {
            this.isShowTrialTip = true;
        }, 500);
    },
};
</script>
<style lang="scss">
    $site-bgclr: #c9e7ff;
    .temp-field-site-cpn {
        .site-bar-head {
            height: 44px;
            line-height: 42px;
            font-size: 16px;
            color: #333;
            padding-left: 15px;
            border-top: 2px solid #127fd2;
            border-bottom: 1px solid #ddd;
            span {
                vertical-align: middle;
            }
            &:first-child {
                margin-top: 1px;
            }
            .order-num {
                font-size: 16px;
                color: #127fd2;
            }
        }

        // 接收人
        .sigers {
            height: 143px;
            background-color: #f6f6f6;
            padding-top: 3px;
            overflow-y: auto;
            li {
                height: 30px;
                line-height: 30px;
                cursor: pointer;
                .signers-color {
                    display: inline-block;
                    float: left;
                    width: 14px;
                    height: 14px;
                    border-radius: 50%;
                    margin: 7px 7px 0 18px;
                }
                .signers-name {
                    display: inline-block;
                    width: 120px;
                }
                .signers-active {
                    display: inline-block;
                    float: right;
                    font-weight: bold;
                    margin-top: 9px;
                    margin-right: 14px;
                }
                &.active {

                }
                &:hover {
                    background-color: #eee;
                }
            }
        }

        // 字段
        .site-bar-body {
            height: calc(100% - 238px);
            overflow: auto;
        }
        .site-bar-title {
            height: 38px;
            line-height: 38px;
            font-size: 14px;
            font-weight: bold;
            color: #333;
            padding-left: 22px;
            border-bottom: 1px solid $border-color;

            .el-icon-ssq-wenhao{
                margin-left: 5px;
                font-size: 12px;
                color: #666;
                cursor: pointer;
            }
            &.decorate-title span {
                float: right;
                margin-right: 10px;
                font-size: 12px;
                color: #999;
            }
        }
        .site-bar-content {
            background-color: #fafafa;
            padding-top: 10px;
            padding-bottom: 15px;
            border-bottom: 1px solid $border-color;
            li {
                display: block;
                height: 32px;
                line-height: 32px;
                white-space: nowrap;
                position: relative;
                &:hover {
                    background-color: #eee;
                    cursor: pointer;
                    .metaLabel-operate-block {
                        display: inline-block;
                    }
                }
                i {
                    width: 24px;
                    height: 24px;
                    line-height: 24px;
                    text-align: center;
                    font-size: 16px;
                    background-color: $site-bgclr;
                    border-radius: 3px;
                    margin-top: 4px;
                    margin-left: 22px;
                    margin-right: 10px;
                }
                .metaLabel-name {
                    display: inline-block;
                    width: 125px;
                    overflow: hidden;
                    vertical-align: top;
                    text-overflow: ellipsis;
                }

                .metaLabel-operate-block{
                    right: 0;
                    position: absolute;
                    display: none;
                    margin-right: 12px;

                    &:hover{
                        .metaLabel-hidden-operate{
                            display: block;
                        }

                        .metaLabel-operate-icon{
                            color: #1687dc;
                        }
                    }

                    .metaLabel-operate-icon{
                        margin-left: 0;
                        margin-right: 0;
                        background-color: transparent;
                    }

                    .metaLabel-hidden-operate{
                        display: none;
                        position: absolute;
                        left: -30px;
                        top: 29px;
                        background: #fff;
                        border: 1px solid #ddd;
                        text-align: center;
                        box-shadow: 0px 2px 3px 1px #ddd;;
                        z-index: 1;

                        li{
                            width: 60px;
                            height: 36px;
                            line-height: 36px;
                            color: #333;
                            text-align: center;
                            font-size: 12px;

                            &:hover{
                                background: #f6f6f6;
                            }
                        }
                    }
                }
            }
        }
        .metaLabel {
            position: relative;
            .add-metaLabel {
                position: absolute;
                top: 12px;
                right: 42px;
                font-size: 14px;
                font-weight: bold;
                cursor: pointer;

                &:hover{
                    color: #127fd2;
                }
            }

            .toManageLink{
                display: inline-block;
                position: absolute;
                top: 1px;
                right: 15px;

                .el-icon-ssq-shezhi{
                    color: #666;
                    font-size: 16px;

                    &:hover{
                        color: #127fd2;
                    }
                }
            }

            .metaUploadBtn{
                margin-left: 10px;
                cursor: pointer;
                font-size: 12px;
                font-weight: normal;
                color: #127fd2;
            }
        }
        .metaLabel-content {
            // height: calc(100% - 469px);
            // min-height: 150px;
            // overflow-y: auto;

            .metaLabel-content-ul{
                padding-bottom: 20px;
            }
        }

        .site-bar-bizname-search {
            margin: 0 10px 10px 10px;
        }

        .biz-metaLabel-content .metaLabel-content-ul{
            padding-bottom: 20px;
        }

        // dialog
        .dialog-addMetaLabel {
            .el-dialog {
                width: 408px;
            }
            .el-dialog__body {
                padding: 24px 33px;
            }
            .el-input {
                width: 340px;
                padding-top: 10px;
                :-moz-placeholder { /* Mozilla Firefox 4 to 18 */
                    color: #ccc; opacity:1;
                }

                ::-moz-placeholder { /* Mozilla Firefox 19+ */
                    color: #ccc;opacity:1;
                }

                input:-ms-input-placeholder{
                    color: #ccc;opacity:1;
                }

                input::-webkit-input-placeholder{
                    color: #ccc;opacity:1;
                }
            }
            .el-input__inner {
                height: 26px;
                line-height: 26px;
                border-radius: 2px;
            }
            .dialog-addMetaLabel-btns {
                // padding-top: 30px;
                // padding-bottom: 14px;
                // padding-left: 166px;
                padding: 30px 0 14px 166px;
                div {
                    display: inline-block;
                    height: 32px;
                    line-height: 32px;
                }
                div:first-child {
                    width: 98px;
                    border: 1px solid #127fd2;
                    margin-right: 4px;
                }
                div:last-child {
                    width: 64px;
                    border: 1px solid $border-color;
                }
            }
        }

        .box-sizing-dialog{
            .dialog-confirm-deleteLabel .el-dialog{
                width: 435px;
                .el-dialog__body p{
                    line-height: 25px;
                }
            }
            .dialog-no-promission .el-dialog{
                width: 360px;
            }

            .dialog-tempMeta-desc .el-dialog, .dialog-meta-desc .el-dialog{
                width: 655px;

                .el-dialog__body{
                    p{
                        color: #333;
                        line-height: 30px;
                    }
                }
            }
        }
    }
    .FieldSite-cpn-popper {
        &.is-dark {
            background: #333;
        }
    }
    .temp-field-site-cpn__metaLabel-name-popper {
        width: 125px;
        line-height: 1.5;
    }
     .dropdown-tool-tip{
        cursor: pointer;
        margin-left: 30px;
        .popper__arrow::after {
            border-right-color: #FDF4F4 !important;
        }
        .el-icon-ssq-jingshitanhaox{
            margin-lef: 5px;
        }
    }
    .dropdown-tool-tip.el-tooltip__popper.is-light {
        background: #FDF4F4;
        // border-color: transparent;
        color: #FD6666;
        font-size: 12px;
        border:0 !important ;
    }
    .el-icon-ssq-biaoqiannew{
        font-size: 15px !important;
        margin-left: 10px;
        color: #FF5500;
        background-color:#f6f6f6 !important;
    }
</style>
