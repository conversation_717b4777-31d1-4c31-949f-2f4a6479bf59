<template>
    <div class="temp-field-Body-cpn" v-loading.fullscreen.lock="hybridServerConnected ? loading : 0">
        <!-- 选择缩放下拉框 -->
        <div class="scale">
            <el-select class="scale-opt"
                v-model="zoom"
                popper-class="sign-el-select scale-opts-el-select"
                size="small"
                placeholder="50%"
            >
                <el-option
                    v-for="item in zoomOpts"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                >
                </el-option>
            </el-select>

            <div v-if="isEdit && inAnalyze" class="analyzeTip">
                <i class="el-icon-loading"></i>
                <span>正在智能解析签署坐标</span>
            </div>
        </div>

        <!-- 签约方和字段区域 -->
        <div class="site-content clear">

            <!-- 选择签约方 & 字段仓库 -->
            <!-- 展示这部分的两种情况：编辑模板；使用模板的时候有附件； -->
            <FieldSite class="FieldSite fl"
                v-if="isEdit || (hasAttachment && !isEdit)"
                :receivers="receivers"
                :isEdit="isEdit"
                :hasAttachment="hasAttachment"
                :marks="marks"
                @switch-receiver="onSwitchReceiver"
                @site-drag-start="onDragStart"
            >
            </FieldSite>

            <!-- 合同内容 -->
            <FieldDoc class="FieldDoc"
                ref="FieldDoc"
                :templateStatus="templateStatus"
                :isEdit="isEdit"
                :initialZoom="zoom"
                :contractId="contractId"
                :docList="newDocList"
                :marks="marks"
                :watermarkList="decorateInfo.watermarkList"
                :ridingSealList="decorateInfo.ridingSealList"
                :receivers="receivers"
                :receiverIndex="receiverIndex"
                :colorIndex="colorIndex"
                :iconDragStatus="dragStatus"
                :float="float"
                :focus="focus"
                :holderTriggerList="holderTriggerList"
                :hasAttachment="hasAttachment"
                @mark-focus="onMarkFocus"
                @mark-blur="onMarkBlur"
                @mark-update="onMarkUpdate"
                @mark-delete="onMarkDelete"
                @attmark-delete="onAttMarkDelete"
                @doc-scroll="onDocScroll"
                @save-meta="onMetaSave"
            >
            </FieldDoc>

            <!-- 缩略图区 -->
            <!-- 使用模板的时候，如果上传了附件，包含文档和附件两部分 -->
            <section class="FieldMiniDoc">
                <FieldMiniDoc
                    :initialZoom="zoom"
                    :docList="docList"
                    :receivers="receivers"
                    :marks="marks"
                    :scrollDocI="scrollDocI"
                    :scrollPageI="scrollPageI"
                    :ridingSealList="decorateInfo.ridingSealList"
                >
                </FieldMiniDoc>
                <!--<FieldMiniDoc
                    v-if="hasAttachment"
                    title="附件"
                    :docListLength="docList.length"
                    :initialZoom="zoom"
                    :docList="accDocList"
                    :receivers="receivers"
                    :marks="accMarks"
                    :scrollDocI="scrollDocI"
                    :scrollPageI="scrollPageI">
                </FieldMiniDoc>-->
            </section>

            <!-- 编辑区 -->
            <FieldEdit class="FieldEdit"
                :receivers="receivers"
                :focus="focus"
                @change-signer="onSignerChange"
                @save-meta="onMetaSave"
                @delete-mark="onMarkDelete"
                @change-mark-focus-button="onMarkFocus"
            >
            </FieldEdit>
        </div>

        <!-- 鼠标跟随icon -->
        <div id="flying-icon"
            class="flying-icon"
            v-show="float.show"
            :class="float.class"
        >
        </div>
    </div>
</template>

<script>
import FieldSite from '../temp_field_site/TempFieldSite.vue';
import FieldDoc from '../temp_field_doc/TempFieldDoc.vue';
import FieldMiniDoc from '../temp_field_minidoc/TempFieldMiniDoc.vue';
import FieldEdit from '../temp_field_edit/TempFieldEdit.vue';
// import { swapArray, uniqBy } from 'src/common/utils/ary.js';
import { SPACE } from '../common/config/config.js';
import { initDecorateInfo } from 'src/common/utils/decorate.js';
import { markCoordinateTransform, coordinateReversal } from 'src/common/utils/fn';
import { mapState } from 'vuex';
import { businessLocalField } from 'utils/hybrid/hybridBusiness.js';
export default {
    components: {
        FieldSite,
        FieldDoc,
        FieldMiniDoc,
        FieldEdit,
    },
    props: {
        templateStatus: {
            default: '',
            type: String,
        },
        isEdit: {
            type: Boolean,
            default: true,
        },
        canUseAttachment: {
            type: Boolean,
            default: false,
        },
        hybridServerConnected: {
            type: Boolean,
            default: true,
        },
        templateMatchId: {
            default: '',
            type: String,
        },
    },
    provide() {
        return {
            getMarkCurrentPage: this.getMarkCurrentPage,
        };
    },
    data() {
        return {
            // 模版Id
            contractId: this.$route.query.templateId,
            operateMarkId: this.$route.query.operateMarkId,
            // 页面loading
            loading: 1,

            // zoom select
            zoom: '适合宽度',
            zoomOpts: [{ value: 0.25, label: '25%' }, { value: 0.5, label: '50%' }, { value: 0.75, label: '75%' }, { value: 1, label: '100%' }, { value: 1.25, label: '125%' }, { value: 1.5, label: '150%' }, { value: 2, label: '200%' }, { value: 4, label: '400%' }, { value: '适合宽度' }],

            receiverIndex: 0,
            colorIndex: 0,

            // flyingIcon
            float: {
                show: false,
                type: '',
                class: '',
                // ========
                name: '',
                receiverFill: false,
                fontSize: 14,
                necessary: true,
            },
            dragStatus: 'end',

            // doc scroll
            scrollDocI: 0,
            scrollPageI: 0,

            // focus
            focus: {
                markIndex: -1,
                signerIndex: 0,
                mark: {},
                markButtonInd: -1,
            },

            // ajax
            receivers: [],
            docList: [],
            accDocList: [], // 附件相关
            newDocList: [],

            marks: [],
            accMarks: [],
            // 缓存后端返回的 ai 坐标数据
            holderTriggerData: [],
            // 格式化后前端使用的 ai 坐标数据
            holderTriggerList: [],

            inAnalyze: false,
            decorateInfo: {
                ridingSealList: [],
                watermarkList: [],
            },
        };
    },
    computed: {
        ...mapState({
            hybridServer: state => state.commonHeaderInfo.hybridServer,
            localFieldSupportType: state => state.localFieldSupportType,
        }),
        hasAttachment() {
            // 可以上传附件&已经上传附件
            // 模版中附件归属文档，不能指定标签，避免各种组件处理该问题，于是这边直接改为无附件
            return false;
            // return this.canUseAttachment && this.accDocList.length > 0
        },
    },
    watch: {
        marks: {
            handler(val) {
                if (val && val.length > 0) {
                    this.accMarks = val.filter(perMark => {
                        return (perMark.attachmentId);
                    });
                    // let len = this.docList.length;
                    // this.accMarks.forEach(val => {
                    // val.docI = val.docI - len;
                    // })
                    // console.log('-----accMarks----------::::::::::');
                    // console.dir(this.accMarks);
                }
            },
            deep: true,
            immediate: true,
        },
    },
    methods: {
        getMarkCurrentPage(docI, pageI) {
            const doc = this.newDocList[docI];
            return doc && doc.page && doc.page[pageI];
        },
        // ajax   获取文档信息，是否可以上传附件
        getOrder() {
            return this.$http.get(`${tempPath}/templates/${this.contractId}`);
        },
        followCursorWithIcon(e) {
            document.querySelector('#flying-icon').style.left = `${e.clientX + 1}px`;
            document.querySelector('#flying-icon').style.top = `${e.clientY + 1}px`;
        },
        onSwitchReceiver(res) {
            this.receiverIndex = res.receiverIndex;
            this.colorIndex = res.colorIndex;
            // todo... edit也需要变
        },

        onDragStart(e, type, className, name, receiverFill = false, fontSize = 14, necessary = true, labelMetaId = '0', buttons = null) {
            setTimeout(() => {
                this.dragStatus = 'started';
                this.followCursorWithIcon(e);
                this.float.show = true;
                this.float.type = type;
                this.float.class = className;
                this.float.name = name;
                this.float.refBizFieldId = labelMetaId;
                if (['TEXT', 'BIZ_DATE', 'TEXT_NUMERIC', 'PICTURE', 'SINGLE_BOX', 'MULTIPLE_BOX'].includes(type)) {
                    this.float.receiverFill = receiverFill;
                    this.float.fontSize = fontSize;
                    this.float.necessary = necessary;
                    this.float.buttons = buttons;
                }
            }, 150);
        },
        onDragMove(e) {
            if (this.dragStatus === 'started') {
                if (e.target.closest('.image')) {
                    this.float.show = false;
                } else {
                    this.followCursorWithIcon(e);
                    this.float.show = true;
                }
            }
        },
        onDragEnd() {
            this.dragStatus = 'end';
            this.float.show = false;
            this.float.class = '';
        },

        updataFocusManual(i) {
            this.focus.mark = this.marks[i];
        },
        onMarkFocus(res) {
            this.focus.markIndex = res.markIndex;
            this.focus.mark = this.marks[res.markIndex];
            this.focus.signerIndex = res.signerIndex;
            this.focus.markButtonInd = res.markButtonInd; // 单选框，复选框选中
        },
        onMarkBlur() {
            this.focus.markIndex = -1;
            this.focus.mark = {};
            this.focus.signerIndex = -1;
        },
        onMarkUpdate(marks) {
            this.marks = marks;
        },

        onDocScroll(res) {
            this.scrollDocI = res.docI;
            this.scrollPageI = res.pageI;
        },

        // 编辑， 同一签名 改变签署人
        onSignerChange(i) {
            // 改变对应mark的roleId即可
            const mark = this.focus.mark;
            const oriRoleId = mark.roleId;
            mark.roleId = this.receivers[i].roleId;
            this.saveMark([{
                labelId: mark.labelId,
                roleId: this.receivers[i].roleId,
            }])
                .then(() => {})
                .catch(() => {
                    mark.roleId = oriRoleId;
                });
        },
        // 右侧编辑修改横竖向时，导致标签出边界控制
        buildBlackHole(page, mark) {
            if (mark.x > (page.width - mark.width)) {
                mark.x = (page.width - mark.width);
            }
            if (mark.y > (page.height - mark.height)) {
                mark.y = (page.height - mark.height);
            }
        },
        hasDiffType(newItem) {
            return this.marks.some(mark => {
                return mark.name === newItem.name && mark.type !== newItem.type && newItem.labelId !== mark.labelId;
            });
        },
        // 编辑区保存标签修改
        onMetaSave(i, metaData) {
            const mark = this.marks[i];
            const page = this.newDocList[mark.docI].page[mark.pageI];

            // 不同类型的业务字段是不允许同名
            if (this.marks.length > 1 && this.hasDiffType({
                ...this.marks[i],
                ...metaData,
            })) {
                return this.$MessageToast.error('已存在不同类型的同名字段');
            }
            // 是否位复选框，单选框
            const isCheckBox = ['MULTIPLE_BOX', 'SINGLE_BOX'].includes(mark.type);
            if (isCheckBox) {
                if (Object.keys(metaData)[1] === 'name') { // 复选框，单选框不能同名
                    const sameNameMarks = this.marks.filter(a => (a.type === mark.type && a.name === metaData[Object.keys(metaData)[1]]));
                    const maxNum = mark.name === metaData[Object.keys(metaData)[1]] ? 1 : 0;
                    if (sameNameMarks.length > maxNum) {
                        this.$MessageToast.error('已存在同名字段，请修改名称'); // 复选框，单选框不能重名
                        return;
                    }
                }
            }
            metaData = { ...metaData, ...markCoordinateTransform({ ...mark, ...metaData }, page.width, page.height) };

            this.saveMark([{
                ...mark,
                labelId: mark.labelId,
                // x: markCoordinateTransform(mark, page.width, page.height).x,
                // y: markCoordinateTransform(mark, page.width, page.height).y,
                ...metaData,
            }])
                .then(res => {
                    // this.$MessageToast.success('保存成功');
                    const newData = res.data.filter(a => a.labelId === mark.labelId)[0];

                    // 浅拷贝一个对象将坐标先转为百分比
                    const cacheMark = Object.assign(mark, {
                        x: newData.x,
                        y: newData.y,
                        width: newData.width,
                        height: newData.height,
                    });
                    if (isCheckBox) {
                        cacheMark.buttons = newData.buttons;
                    }
                    // 将浅拷贝的对象（里面的参数都是百分比）转换为 px
                    const reversalData = coordinateReversal(cacheMark, page.width, page.height);

                    // 将 mark 的值更新为转换后的值
                    mark.x = reversalData.x;
                    mark.y = reversalData.y;
                    mark.width = reversalData.width;
                    mark.height = reversalData.height;
                    if (isCheckBox) {
                        mark.buttons = reversalData.buttons;
                    }
                    if (Object.keys(metaData)[1] === 'name' && res.data.length === 1) { // 只有当前标签需要更新时
                        // 不重置整个对象，防止input blur重复触发请求
                        this.marks[i].name = metaData.name;
                        this.marks[i].width = mark.width;
                        this.marks[i].height = mark.height;
                        this.marks[i].x = mark.x;
                        this.marks[i].y = mark.y;
                        if (isCheckBox) {
                            this.marks[i].buttons = mark.buttons;
                        }
                    } else {
                        this.updateMarks(res.data, i, mark); // 需要更新同名字段
                    }
                    this.updataFocusManual(this.focus.markIndex);
                })
                .catch(() => {});
        },
        updateMarks(data, i, currentMark) {
            const that = this;
            if (data.length > 1) { // 存在同名字段属性需要更新
                const fakeMarks = [...this.marks]; // 备份marks修改需要更新的数据，做统一更新
                data.forEach((updateItem) => {
                    const index = that.marks.findIndex((a) => a.labelId === updateItem.labelId);
                    const originMark = that.marks[index];
                    const page = that.newDocList[originMark.docI].page[originMark.pageI];
                    const cacheMark = Object.assign(originMark, {
                        x: updateItem.x,
                        y: updateItem.y,
                        width: updateItem.width,
                        height: updateItem.height,
                    });
                    if (updateItem.buttons) {
                        cacheMark.buttons = updateItem.buttons;
                    }
                    // 将浅拷贝的对象（里面的参数都是百分比）转换为 px
                    const reversalData = coordinateReversal(cacheMark, page.width, page.height);

                    const newUpdateItem = { ...originMark, ...updateItem, ...reversalData };
                    // // 将 mark 的值更新为转换后的值
                    // newUpdateItem.x = reversalData.x;
                    // newUpdateItem.y = reversalData.y;
                    // newUpdateItem.width = reversalData.width;
                    // newUpdateItem.height = reversalData.height;
                    fakeMarks[index] = { ...newUpdateItem };
                });
                this.marks = [...fakeMarks];
                this.$MessageToast.info(`已将${data.length - 1}个同名字段属性更新为当前字段属性值`); // 更新同名字段属性提示
            } else { // 修改时需要获取计算后的位置信息，需要延用当前mark里的docI属性等；
                const newUpdateItem = data[0];
                newUpdateItem.x = currentMark.x;
                newUpdateItem.y = currentMark.y;
                newUpdateItem.width = currentMark.width;
                newUpdateItem.height = currentMark.height;
                if (['MULTIPLE_BOX', 'SINGLE_BOX'].includes(currentMark.type)) {
                    newUpdateItem.buttons = currentMark.buttons;
                }

                this.$set(this.marks, i, {
                    ...currentMark,
                    ...newUpdateItem,
                });
            }
        },
        onMarkDelete(i, labelId) {
            this.onMarkBlur();
            this.deleteMarks(labelId)
                .then(() => {
                    this.$MessageToast.success('删除成功');
                    this.marks.splice(i, 1);
                })
                .catch(() => {});
        },
        // 删除附件中的标签
        onAttMarkDelete(i, labelId, attachmentId) {
            this.onMarkBlur();
            this.deleteAttachmentMarks(labelId, attachmentId)
                .then(() => {
                    this.$MessageToast.success('删除成功');
                    this.marks.splice(i, 1);
                })
                .catch(() => {});
        },
        saveMark(data) {
            return this.postMarks(data);
        },

        // ajax
        getRecivers() {
            const url = this.isEdit ? `${tempPath}/templates/${this.contractId}/roles?isOnlyNeedSigner=1&displayRuleName=true`
                : `${tempPath}/multiple-dynamic-signer/${this.contractId}/recipient${this.operateMarkId ? '?operateMarkId=' + this.operateMarkId : ''}`; // isOnlyNeedSigner不要抄送人，displayRuleName按规则格式化接收人名称
            return this.$http.get(url);
        },
        getDocs() {
            // 混合云3.0本地存储
            if (this.$hybrid.isGammaLocalField(this.localFieldSupportType) && !this.isEdit) {
                return businessLocalField({
                    url: '/hybrid/field',
                    requestData: {
                        target: '/templates/preview-metadata',
                        params: JSON.stringify({
                            templateId: this.contractId,
                        }),
                    },
                });
            }
            const previewDocUrl = this.templateMatchId
                ? `${tempPath}/templates/match/${this.templateMatchId}/previews`
                : `${tempPath}/templates/${this.contractId}/previews`;
            const url = this.isEdit
                ? `${tempPath}/templates/${this.contractId}/documents`
                : `${previewDocUrl}${this.operateMarkId ? '?operateMarkId=' + this.operateMarkId : ''}`;
            return this.$http.get(url); // showLabels 取标签信息
        },
        getAttachmentDoc() {
            return this.$http.get(`${tempPath}/templates/${this.contractId}/attachment`);
        },
        noticeBackForGenerateImages() {
            // return this.$http.get(`${tempPath}/templates/${this.contractId}/generate-document-images`);

            return this.$hybrid.makeRequestAdaptToPublic({
                method: 'get',
                url: `${tempPath}/templates/${this.contractId}/generate-document-images`,
            });
        },
        // ai 分析签名位置
        analyzePositions() {
            // 不在指定位置页则 return
            if (!this.isEdit) {
                return;
            }
            this.inAnalyze = true;
            this.$http.post(`${tempPath}/templates/${this.contractId}/ai-analysis-positions`)
                .then(res => {
                    this.holderTriggerData = res.data.documents;
                    this.holderTriggerList = this.initTriggersData(res.data.documents);
                })
                .catch(() => {
                    this.inAnalyze = false;
                });
        },

        postMarks(labelAry = [], opts = {}) {
            if (!labelAry[0] || !labelAry[0].labelId) {
                this.$MessageToast.warning('字段数据异常，请重新操作');
                return Promise.reject();
            }
            return this.$http.post(
                `${tempPath}/templates/${this.contractId}/labels/create-and-modify/`,
                labelAry[0],
                opts,
            );
        },
        deleteMarks(labelId) {
            return this.$http.delete(`${tempPath}/templates/${this.contractId}/labels/${labelId}`);
        },
        deleteAttachmentMarks(labelId, attachmentId) {
            return this.$http.post(`${tempPath}/templates/${this.contractId}/${attachmentId}/${labelId}/delete-lable`);
        },
        // 初始化收件人数据
        initReceiversData(data) {
            return data.map((item) => {
                // 后端做了判断，直接取值
                const labelName = item.userName;
                return {
                    ...item,
                    labelName,
                };
            });
        },
        // 初始化文档数据
        initDocData(resData) {
            let painList = []; // 扁平化文档和附件
            resData.forEach((doc) => {
                let pageSize = 0;
                doc.tempAttachments && doc.tempAttachments.map(item => { // 计算文档+附件页数
                    pageSize += item.pageSize;
                });
                doc.allPageSize = doc.pageSize + pageSize;
                doc.tempAttachments && doc.tempAttachments.map(item => { // 关联附件与文档关系
                    item.documentId = doc.documentId;
                });
                painList.push(doc);
                doc.tempAttachments && (painList = painList.concat(doc.tempAttachments));
            });
            const maxW = painList.reduce((p, n) => {
                return {
                    imageMaxWidth: Math.max(p.imageMaxWidth, n.imageMaxWidth),
                };
            }).imageMaxWidth; // ???需要后端传参数

            function sumH(page) { // 文档+附件的高度
                return page
                    .map(item => item.height)
                    .reduce((p, n) => p + n + SPACE, 0);
            }
            painList.forEach((doc, docI) => {
                // let pageH = doc.page[0].height; // 第一页高度
                if (+docI === 0) {
                    doc.y = 20;
                } else {
                    const preDoc = painList[docI - 1];
                    const preDocH = sumH(preDoc.page);
                    doc.y = preDoc.y + preDocH;
                }
                if (+docI === painList.length - 1) {
                    painList.totalH = doc.y + sumH(doc.page);
                }
                doc.page.forEach((page, pageI) => { // 计算当前文档+附件的位置
                    page.x = (maxW - page.width) / 2;
                    if (pageI === 0) {
                        page.y = 0;
                    } else {
                        const pPage = doc.page[pageI - 1];
                        page.y = pPage.y + pPage.height + SPACE;
                    }

                    page.imagePreviewUrl = this.$hybrid.getContractImg(page.imagePreviewUrl);
                    page.highQualityPreviewUrl = this.$hybrid.getContractImg(page.highQualityPreviewUrl);
                    page.marks = page.marks || [];

                    page.marks.forEach((mark) => {
                        mark.docI = docI;
                        mark.pageI = pageI;
                    });
                });
            });
            painList.viewBox = `0 0 ${maxW} ${painList.totalH}`;
            painList.maxWidth = maxW;
            return painList;
        },
        // 初始化标签数据，水印和骑缝章数据在marks
        initMarksData() {
            const labels = [];
            this.newDocList.forEach((doc, docI) => {
                doc.page.forEach((page, pageI) => {
                    page.marks.forEach((mark, markI) => {
                        // 接口返回mark的x,y值为：以图片左下角为坐标原点 标签左下角的坐标 占图片宽高的 百分比，
                        // 转换以图片右上角为原点，标签左上角为坐标点 的数值形式，
                        const numberCoordinate = coordinateReversal(mark, page.width, page.height);
                        let labelObj = {
                            labelId: mark.labelId,
                            roleId: mark.roleId,
                            // contractId: this.contractId,
                            documentId: doc.documentId,
                            attachmentId: doc.attachmentId,
                            docI,
                            pageI,
                            markI,
                            pageNumber: pageI + 1,
                            type: mark.type, // SIGNATURE(1), DATE(2), SEAL(3),
                            x: numberCoordinate.x,
                            y: numberCoordinate.y,
                            width: numberCoordinate.width,
                            height: numberCoordinate.height,
                            name: mark.name,
                            value: mark.value,
                            valueStr: mark.valueStr,
                            imageHref: mark.imageHref,
                            // 文本专属
                            receiverFill: mark.receiverFill,
                            fontSize: mark.fontSize,
                            necessary: mark.necessary,
                            refBizFieldId: mark.refBizFieldId,
                            alignment: mark.alignment || 0,

                        };
                        if (['MULTIPLE_BOX', 'SINGLE_BOX'].includes(mark.type)) {
                            labelObj = {
                                ...labelObj,
                                buttons: numberCoordinate.buttons,
                                // buttonDirection: mark.buttonDirection,
                            };
                        }
                        labels.push(labelObj);
                    });
                });
            });
            return labels;
        },
        // 初始化AI坐标数据
        initTriggersData(data) {
            const triggers = [];
            let sum = 0;
            // 格式化成想要的数据格式
            data.forEach((doc, docI) => {
                doc.pages.forEach((page, pageI) => {
                    page.positions.forEach((pos) => {
                        const posToPx = coordinateReversal(pos, this.newDocList[docI].page[pageI].width, this.newDocList[docI].page[pageI].height);
                        triggers.push({
                            ...posToPx,
                            documentId: doc.documentId,
                            attachmentId: doc.attachmentId,
                            docI: docI,
                            pageI: pageI,
                            triggerId: sum,
                        });

                        sum++;
                    });
                });
            });

            setTimeout(() => {
                this.inAnalyze = false;
            }, 1500);

            return triggers;
        },
        // 根据标签宽度对标签值进行分组
        computeMarkValue(fontSize, markValue, labelWidth) {
            let tempStr = '';
            let tempValue = 0;
            return markValue.split('').reduce((total, char, index) => {
                // 字母，数字，符号
                const actualSize = /^[\u4E00-\u9FA5]+$/.test(char) ? fontSize : fontSize / 2;
                if (tempValue + actualSize > labelWidth) {
                    total.push(tempStr);
                    tempStr = char;
                    tempValue = actualSize;
                } else {
                    tempStr += char;
                    tempValue += actualSize;
                }
                // 最后一个字符时，将剩余内容push进数组
                if (index === markValue.length - 1) {
                    total.push(tempStr);
                }
                return total;
            }, []);
        },
    },
    beforeMount() {
        document.addEventListener('mousemove', this.onDragMove);
        document.addEventListener('mouseup', this.onDragEnd);
    },
    beforeDestroy() {
        document.removeEventListener('mousemove', this.onDragMove);
        document.removeEventListener('mouseup', this.onDragEnd);
    },
    created() {
        // ai 分析签名位置，第一次分析的时间可能比较长
        // this.analyzePositions();

        // 获取所有文件和收件人信息并展示
        Promise.all([
            this.getRecivers(),
            this.getDocs(),
            // this.getAttachmentDoc(),
            this.noticeBackForGenerateImages(),
        ])
            .then(res => {
                const res0Data = res[0].data;
                // 初始化receivers数据
                this.receivers = this.initReceiversData(res0Data);
                // 同步给TempField父组件，目前是为了给userGuide组件提供userType用
                this.$emit('init-receivers-data', this.receivers);
                if (this.isEdit) {
                    // 编辑模板
                    this.newDocList = this.docList = this.initDocData(res[1].data);
                } else {
                    if (this.$hybrid.isGammaLocalField(this.localFieldSupportType)) {
                        const { localFields, result } = res[1].data;
                        result.forEach(doc => {
                            doc.page.forEach(page => {
                                const pageWidth = page.width;
                                page.marks.forEach(mark => {
                                    if (mark.saveLocation === 'LOCAL' && mark.valueStr) {
                                        const value = (localFields.find(local => local.fieldId === mark.valueStr) || {}).value || '';
                                        const markWidth = mark.width > 1 ? mark.width : mark.width * pageWidth;
                                        // 兼容原有的字段类型为Array
                                        mark.value = ['TEXT'].includes(mark.type) ? this.computeMarkValue(mark.fontSize || 14, value, markWidth) : [value];
                                        mark.valueStr = value;
                                    }
                                });
                            });
                        });
                        this.newDocList = this.docList = this.initDocData(result);
                    } else {
                        this.newDocList = this.docList = this.initDocData(res[1].data);
                    }
                }
                this.decorateInfo = initDecorateInfo(res[0].data, this.docList[0].page[0].height, true);
                // 初始化marks数据
                this.$nextTick()
                    .then(() => {
                        this.marks = this.initMarksData(this.newDocList);
                        this.holderTriggerList = this.initTriggersData(this.holderTriggerData);
                    });
            })
            .catch((err) => {
                console.log('err', err);
            })
            .finally(() => {
                this.loading = 0;
            });
    },
};
</script>

<style lang="scss">
    .temp-field-Body-cpn {
        position: absolute;
        top: 90px;
        bottom: 35px;
        width: 100%;
        overflow: hidden;
        .scale {
            z-index: 100;
            position: fixed;
            top: 50px;
            width: 100%;
            height: 40px;
            line-height: 40px;
            text-align: center;
            background-color: #f6f6f6;
            padding-right: 210px;
            border-bottom: 1px solid $border-color;
            .scale-opt {
                width: 100px;

                .el-input__icon { // el
                    color: #333;
                }
                input { // el
                    font-size: 12px;
                    background-color: #f6f6f6;
                    border: 0;
                    border-radius: 0;
                }
            }

            .analyzeTip{
                position: absolute;
                top: 0;
                left: 20px;
                font-size: 12px;
                color: #43adfd;

                .el-icon-loading{
                    margin-right: 5px;
                    font-size: 20px;
                    color: #0092ff;
                }

                i, span{
                    vertical-align: middle;
                }
            }
        }

        .site-content {
            height: 100%;
            .FieldSite {
                position: relative;
                width: 210px;
                height: 100%;
                border-right: 1px solid $border-color;
            }
            .FieldDoc {
                height: 100%;
                overflow: auto;
                margin-left: 211px;
                margin-right: 211px;
            }
            .FieldMiniDoc {
                position: absolute;
                top: 0;
                right: 0;
                width: 210px;
                height: 100%;
                border-left: 1px solid #ddd;
                overflow-y: auto;
                .temp-field-minidoc-cpn {
                    border-left: none;
                }
            }
            .FieldEdit {
                position: absolute;
                top: 1px;
                right: 0;
                width: 211px;
                height: 100%;
            }
        }

        .flying-icon {
            z-index: 9999;
            pointer-events: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 28px;
            height: 28px;
            line-height: 27px;
            text-align: center;
            font-size: 18px;
            background-color: #fff;
            border-radius: 4px;
            box-shadow:1px 1px 13px #ccc, -1px 1px 13px #ccc;
        }
    }

    .scale-opts-el-select {
        .el-select-dropdown__wrap {
            max-height: 285px;
        }
    }

</style>
