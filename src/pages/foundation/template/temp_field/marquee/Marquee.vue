<template>
    <g :type="marquee.type">
        <circle
            r="3.5"
            :cx="position.x + 1"
            :cy="position.y + 1"
            fill="#fff"
            stroke="#127fd2"
            stroke-width="2"
        />
        <circle class="marquee-circle"
            r="3.5"
            :cx="position.x + orgWidth + disX - 1"
            :cy="position.y + orgHeight + disY - 1"
            fill="#fff"
            stroke="#127fd2"
            stroke-width="2"
            style="cursor: nwse-resize;"
            @mousedown="start"
            @mouseup="end"
        />
        <circle
            r="3.5"
            :cx="position.x + 1"
            :cy="position.y + orgHeight + disY - 1"
            fill="#fff"
            stroke="#127fd2"
            stroke-width="2"
        />
    </g>
</template>
<script>
export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['mark', 'initialPosition', 'zoom'],
    inject: ['getMarkCurrentPage'],
    data() {
        return {
            isResizing: 0,
            startX: 0,
            startY: 0,
            disX: 0,
            disY: 0,

            orgWidth: this.mark.width,
            orgHeight: this.mark.height,
        };
    },
    computed: {
        position() {
            return this.initialPosition;
        },
        marquee() {
            return this.mark;
        },
    },
    watch: {
        mark() {
            this.updateMarquee();
        },
    },
    methods: {
        updateMarquee() {
            this.orgWidth = this.marquee.width;
            this.orgHeight = this.marquee.height;
            this.disX = 0;
            this.disY = 0;
        },
        start(e) {
            this.isResizing = 1;
            this.startX = e.x;
            this.startY = e.y;

            document.body.style.cursor = 'nwse-resize';
            document.addEventListener('mousemove', this.move);
            document.addEventListener('mouseup', this.end);
        },
        move(e) {
            if (!this.isResizing) {
                return;
            }
            this.disX = (e.x - this.startX) / this.zoom;
            this.disY = (e.y - this.startY) / this.zoom;

            const { width, height } = this.getMarkCurrentPage(this.mark.docI, this.mark.pageI);

            // 边界控制
            if (this.orgWidth + this.disX <= 35) {
                this.disX = 35 - this.orgWidth;
            }
            if (this.orgWidth + this.disX >= width) {
                return this.$MessageToast.error(`宽度已超出边界最大值${width}`);
            }
            if (this.orgHeight + this.disY >= height) {
                return this.$MessageToast.error(`高度已超出边界最大值${height}`);
            }
            if (this.orgHeight + this.disY <= 20) {
                this.disY = 20 - this.orgHeight;
            }
            this.marquee.width = this.orgWidth + this.disX <= 35
                ? 35
                : this.orgWidth + this.disX;
            this.marquee.height = this.orgHeight + this.disY <= 20
                ? 20
                : this.orgHeight + this.disY;
            this.$emit('move', this.marquee.width, this.marquee.height);
        },
        end() {
            this.isResizing = 0;
            this.updateMarquee();

            document.body.style.cursor = 'default';
            document.removeEventListener('mousemove', this.move);
            document.removeEventListener('mouseup', this.end);

            this.$emit('end', this.marquee.width, this.marquee.height);
        },
    },
};
</script>
<style lang="scss">

</style>
