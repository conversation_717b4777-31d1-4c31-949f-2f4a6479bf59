<template>
    <div v-if="docList"
        :style="{ 'margin-left': (isEdit || (!isEdit && hasAttachment)) ? '211px' : '0' }"
        @scroll="onDocScroll"
    >
        <div class="watermarks-container" :style="{'width': `${zoomWidth}px`}">
            <Watermark
                v-show="isEdit"
                @delete="handleDecoratorDelete(watermark, 'watermarkList', index)"
                v-for="(watermark, index) in watermarkList"
                :key="watermark.labelId"
                :watermark="watermark"
                :color="computeFillColor(watermark)"
                :readonly="true"
            >
            </Watermark>
        </div>
        <!-- 合同内容 -->
        <div class="Field-Doc-cpn documents-content">
            <svg
                id="svgView"
                style="display: block; margin: 0 auto;"
                :viewBox="docViewBox"
                :width="zoomWidth"
                :height="zoomHeight"
            >
                <defs>
                    <pattern id="grid"
                        x="0"
                        y="0"
                        width="300"
                        height="200"
                        patternUnits="userSpaceOnUse"
                    >
                        <text x="0" y="0" fill="#999" transform="translate(0,100) rotate(-30)" font-size="10">
                            <tspan x="0" :y="20*(index+1)" v-for="(watermark, index) in watermarkList" :key="watermark.labelId">{{ watermark.watermarkText }}</tspan>
                            <!-- <tspan x="0" y="40">我是一个中国人，旋转和缩放在不同。</tspan> -->
                        </text>
                    </pattern>
                    <!-- 骑缝章书页背景 -->
                    <linearGradient id="linear"
                        x1="0.2"
                        y1="0"
                        x2="0.4"
                        y2="0"
                        x3="0.6"
                        y3="0"
                        x4="0.8"
                        y4="0"
                        x5="1"
                        y5="0"
                        spreadMethod="repeat"
                    >
                        <stop offset="0%" stop-color="#d9d9d9" stop-opacity="0.5" />
                        <stop offset="100%" stop-color="#fff" stop-opacity="0.05" />
                    </linearGradient>
                </defs>
                <g class="outer">
                    <g class="document"
                        v-for="(doc, docIndex) in docList"
                        :key="doc.documentId?doc.documentId:doc.attachmentId"
                        :transform="`matrix(1, 0, 0, 1, 0, ${ doc.y })`"
                    >
                        <g class="page"
                            v-for="(page, pageIndex) in doc.page"
                            :key="page.imageId?page.imageId:page.imagePreviewUrl"
                            :transform="`matrix(1, 0, 0, 1, ${ page.x }, ${ page.y })`"
                        >

                            <!-- 合同图片 -->
                            <image
                                class="image"
                                v-lazy="page.highQualityPreviewUrl"
                                :width="page.width"
                                :height="page.height"
                                :doc-index="docIndex"
                                :page-index="pageIndex"
                            >
                            </image>
                            <rect x="0"
                                y="0"
                                :width="page.width"
                                :height="page.height"
                                fill="url(#grid)"
                                opacity="0.2"
                                style="pointer-events: none"
                            ></rect>
                            <!-- 1个文档只有1页，不展示骑缝章 -->
                            <g class="riding-seal-container"
                                :transform="`matrix(1, 0, 0, 1, ${ page.width - 41 }, 0)`"
                                v-if="doc.page.length > 1 && isShowRidingSeal"
                            >
                                <rect x="41" y="0" width="120" :height="page.height" fill="url(#linear)"></rect>
                                <rect x="0"
                                    y="0"
                                    width="160"
                                    :height="page.height"
                                    fill="#127fd2"
                                    fill-opacity="0.05"
                                    stroke-dasharray="10"
                                    stroke="#127fd2"
                                    stroke-width="1"
                                ></rect>
                                <RidingSealSvg
                                    v-for="(ridingseal, sealIndex) in ridingSealList"
                                    :key="ridingseal.labelId"
                                    :ridingseal="ridingseal"
                                    :isEdit="isEdit"
                                    :pageHeight="page.height"
                                    :zoom="zoom"
                                    :isActive="operateSealInfo.docI === docIndex && operateSealInfo.pageI === pageIndex && operateSealInfo.index === sealIndex"
                                    :color="computeFillColor(ridingseal)"
                                    @update-operate-page-info="handleSealInfoUpdate($event, sealIndex, docIndex, pageIndex)"
                                    @update-server="postRideSealTopToServer($event, sealIndex)"
                                    @delete="handleDecoratorDelete(ridingseal, 'ridingSealList', sealIndex)"
                                >
                                </RidingSealSvg>
                            </g>

                            <!-- 页脚 -->
                            <text
                                x="3"
                                :y="page.height + 11"
                                fill="#999"
                                font-size="9"
                            >
                                {{ doc.fileName }}
                            </text>
                            <text
                                :x="page.width"
                                :y="page.height + 11"
                                fill="#999"
                                font-size="9"
                                text-anchor="end"
                            >
                                第{{ pageIndex+1 }}页，共{{ doc.pageSize }}页
                            </text>
                        </g>
                    </g>

                    <!-- 标签 -->
                    <Signature
                        v-for="(mark, markIndex) in marks"
                        :zoom="zoom"
                        :key="mark.labelId"
                        :isEdit="isEdit"
                        :style="{ 'pointer-events': iconDragStatus == 'started' ? 'none' : 'auto' }"
                        :mark="mark"
                        :x="docList[mark.docI].page[mark.pageI].x + mark.x"
                        :y="docList[mark.docI].y + docList[mark.docI].page[mark.pageI].y + mark.y"
                        :pageX="docList[mark.docI].page[mark.pageI].width"
                        :pageY="docList[mark.docI].page[mark.pageI].height"
                        :owner="computeOwner(mark)"
                        :fill="computeFillColor(mark)"
                        :focusing="focus.markIndex == markIndex"
                        :markButtonInd="focus.markButtonInd"
                        :contractId="contractId"
                        :hasAttachment="hasAttachment"
                        @mark-click="onMarkClick($event, mark, markIndex)"
                        @mark-start="onMarkStart($event, mark, markIndex)"
                        @mark-move="onMarkMove($event, mark, markIndex)"
                        @mark-button-change="onMarkButtonChange($event, mark, markIndex)"
                        @mark-end="onMarkEnd($event, mark, markIndex)"
                        @delete-signature="onMarkDelete($event, markIndex)"
                        @mark-add-option="onMarkButtonAdd($event, mark,markIndex)"
                    >
                    </Signature>
                    <!-- 选中框 -->
                    <Marquee
                        v-if="focus.markIndex > -1
                            && (focus.mark.type == 'TEXT' || focus.mark.type == 'BIZ_DATE' || focus.mark.type == 'TEXT_NUMERIC' || focus.mark.type === 'PICTURE')
                            && isEdit"
                        class="test-1"
                        :mark="focus.mark"
                        :initialPosition="getPosInSVG(focus.mark)"
                        :zoom="zoom"
                        @move="onMarqueeMove"
                        @end="onMarqueeEnd"
                    ></Marquee>

                    <!-- AI placeholder -->
                    <!-- transform 的 y 实际上只要取 trigger.y（ai返回的y坐标） 就行了，"-curTriggerHeight + trigger.height 是做一个兼容，优化用户体验" -->
                    <g
                        v-for="trigger in holderTriggerList"
                        class="trigger"
                        :doc-index="trigger.docI"
                        :page-index="trigger.pageI"
                        :style="{
                            'pointer-events': iconDragStatus == 'started' ? 'none' : 'auto',
                            'display': activePlaceHolder.includes(trigger.triggerId) ? 'block' : 'none',
                        }"
                        :key="trigger.triggerId"
                        :transform="translateMatrix(1,0,0,1, docList[trigger.docI].page[trigger.pageI].x + trigger.x, docList[trigger.docI].y + docList[trigger.docI].page[trigger.pageI].y + trigger.y - curTriggerHeight + trigger.height)"
                    >
                        <rect
                            class="trigger trigger-rect"
                            :doc-index="trigger.docI"
                            :page-index="trigger.pageI"
                            :width="trigger.width"
                            :height="curTriggerHeight"
                            stroke-width="1"
                            :fill="intoPlaceHolder === trigger.triggerId ? '#00ffff' : 'transparent'"
                            fill-opacity="0.3"
                            stroke="#127fd2"
                            stroke-dasharray="5"
                            stroke-dashoffset="0"
                        >
                        </rect>
                    </g>
                </g>
            </svg>
        </div>
    </div>
</template>

<script>
import Signature from '../signature/Signature.vue';
import Marquee from '../marquee/Marquee.vue';
import Watermark from 'components/watermarkEdit/WatermarkEdit.vue';
import RidingSealSvg from 'components/ridingSeal/RidingSealSvg.vue';
import { throttle, markCoordinateTransform, coordinateReversal } from 'src/common/utils/fn.js';
import { markInfo, textMarkInfo } from 'src/pages/foundation/sign/common/info/info.js';
import { colorInfo } from 'utils/colorInfo.js';
import { SPACE } from '../common/config/config.js';
import { RIDESEAL_SHADOW_WIDTH, DEFAULT_RS_HEIGHT, calcRidingSeal, factoryWatermarkText } from 'src/common/utils/decorate.js';
import { COLOR_TYPES } from '../../../sign/common/info/info.js';
import Bus from 'components/bus/bus.js';

export default {
    components: {
        Watermark,
        Signature,
        Marquee,
        RidingSealSvg,
    },
    // eslint-disable-next-line vue/require-prop-types
    props: ['hasAttachment', 'templateStatus', 'isEdit', 'contractId', 'initialZoom', 'docList', 'receivers', 'receiverIndex', 'marks', 'iconDragStatus', 'float', 'focus', 'holderTriggerList', 'ridingSealList', 'watermarkList'],
    /**
     * focus包括聚焦标签的markIndex, signerIndex和mark信息
     * 其中mark依赖focusMark方法对markIndex的更新而更新
     * 原则上不直接对mark修改
     * focus用于：
     * 1. 标签聚焦时的边框展示
     * 2. 标签聚焦时的edit区域展示和操作
     * 3. 标签聚焦时的选中框的展示和操作
     */
    data() {
        return {
            colorInfo: colorInfo.slice(1),
            docWidth: 0,
            markStash: {},
            creation: {
                docI: 0,
                pageI: 0,
                status: 'pedding',
            },
            move: {
                docI: 0,
                pageI: 0,
                status: 'pedding',
            },
            activePlaceHolder: [],
            intoPlaceHolder: null,
            // AI 区域的高度根据拖出来业务字段的高度展示
            curTriggerHeight: 0,
            // 触发 AI 功能的标签类型，因为目前没有一个通用参数判断是业务字段，所以业务字段增加类型时这里也需要对应增加
            AIAttachFields: ['TEXT', 'BIZ_DATE', 'TEXT_NUMERIC'],
            // 标记当前操作的骑缝章的页面索引，从而在拖拽时只更新当前页面的位置信息，在拖拽结束时才进行数据的整体更新
            operateSealInfo: {
                docI: -1,
                pageI: -1,
                index: -1,
            },
        };
    },
    computed: {
        isShowRidingSeal() {
            return this.ridingSealList.length > 0 && !this.docList.every(doc => doc.page.length === 1);
        },
        docMaxWidth() { // 存在骑缝章时，拓宽界面最大宽度用以放置骑缝章div， RIDESEAL_SHADOW_WIDTH
            return this.isShowRidingSeal ? this.docList.maxWidth + RIDESEAL_SHADOW_WIDTH : this.docList.maxWidth;
        },
        docViewBox() { // 存在骑缝章时，拓宽界面最大宽度用以放置骑缝章div
            const viewBox = this.docList.viewBox;
            return this.docList.length ? `0 0 ${this.docMaxWidth} ${viewBox.split(' ')[3]}` : viewBox;
        },
        zoom() {
            if (this.initialZoom === '适合宽度') {
                return ((this.docWidth - 100) / this.docMaxWidth);
            } else {
                return this.initialZoom;
            }
        },
        zoomWidth() {
            return this.docList.length ? (this.docMaxWidth * this.zoom + 0) : 0;
        },
        zoomHeight() {
            return this.docList.length ? this.docViewBox.split(' ')[3] * this.zoom : 0;
        },
    },
    watch: {
        hasAttachment: {
            handler() {
                this.initDocWidth();
            },
            immediate: true,
        },
    },
    methods: {
        deleteMarks(labelId) {
            return this.$http.delete(`${tempPath}/templates/${this.contractId}/labels/${labelId}`);
        },
        addWatermark() {
            const { roleId, showName } = this.receivers[this.receiverIndex] || {};
            if (this.watermarkList.find(item => item.roleId === roleId)) {
                return;
            }
            this.$http.post(`${tempPath}/templates/labels/add-watermark`, {
                roleId: roleId,
                templateId: this.contractId,
            }).then((res) => {
                this.watermarkList.push({
                    ...res.data,
                    roleName: showName, // 增加roleName
                    watermarkText: factoryWatermarkText(this.receivers[this.receiverIndex]),
                });
            });
        },
        // 点击装饰器
        addRideSeal() {
            if (this.docList.every(doc => doc.page.length === 1)) {
                this.$MessageToast.error('单页文档无法添加骑缝章');
                return;
            }
            const { roleId, showName } = this.receivers[this.receiverIndex] || {};
            if (this.ridingSealList.find(item => item.roleId === roleId)) {
                return;
            }
            const pageInfo = this.docList[0].page[0];
            const y = calcRidingSeal(this.ridingSealList, pageInfo.height);
            this.$http.post(`${tempPath}/templates/labels/save-decorate-ride-seal`, {
                labelId: '',
                height: DEFAULT_RS_HEIGHT / pageInfo.height,
                width: DEFAULT_RS_HEIGHT / pageInfo.width,
                roleId: roleId,
                templateId: this.contractId,
                y: 1 - (y + DEFAULT_RS_HEIGHT) / pageInfo.height,
            }).then((res) => {
                this.ridingSealList.push({
                    ...res.data,
                    roleName: showName, // 增加roleName
                    y,
                });
            });
        },
        // 骑缝章数据提交到服务端, yData计算后的百分比位置，yPos像素高度
        postRideSealTopToServer({ y, startY }, i) {
            const { docI, pageI } = this.operateSealInfo;
            const pageHeight = this.docList[docI].page[pageI].height;
            const updateTarget = this.ridingSealList[i];
            this.$http.post(`${tempPath}/templates/labels/save-decorate-ride-seal`, {
                labelId: updateTarget.labelId,
                height: updateTarget.height,
                width: updateTarget.width,
                roleId: updateTarget.roleId,
                templateId: this.contractId,
                y: 1 - (y + DEFAULT_RS_HEIGHT) / pageHeight, // 骑缝章左下角据据页面左下角的距离
            }).then(() => {
                this.$set(this.ridingSealList, i, {
                    ...updateTarget,
                    y,
                });
                this.blurOperateSeal();
            }).catch(() => {
                this.$set(this.ridingSealList, i, {
                    ...updateTarget,
                    y: startY,
                });
            });
        },
        blurOperateSeal() {
            this.operateSealInfo = {
                docI: -1,
                pageI: -1,
                index: -1,
            };
        },
        // 更新操作的骑缝章页码、index信息
        handleSealInfoUpdate(e, index = -1, docI = -1, pageI = -1) {
            this.operateSealInfo = {
                docI,
                pageI,
                index,
            };
        },
        handleDecoratorDelete(decorator, target, index) {
            this.blurOperateSeal();
            const { labelId } = decorator;
            this.deleteMarks(labelId).then(() => {
                this[target].splice(index, 1);
            });
        },
        initDocWidth() {
            const dom = document.querySelector('.documents-content');
            if (dom) {
                this.docWidth = dom
                    .getBoundingClientRect()
                    .width;
            }
        },
        translateMatrix(a, b, c, d, e, f) {
            return `matrix(${a},${b},${c},${d},${e},${f})`;
        },

        findReceiverIdx(id) {
            return this.receivers.findIndex(item => item.roleId === id);
        },
        computeOwner(mark) {
            const receiver = this.receivers[this.findReceiverIdx(mark.roleId)] || {};
            let ownerName = receiver.showName || '';
            if (mark.type === 'QR_CODE') {
                ownerName = '签名查验';
            }
            return ownerName;
        },
        // 获取鼠标相对于目标元素左/上边界的距离
        getMarkPostion(e) {
            return {
                x: e.x - e.target.getBoundingClientRect().left,
                y: e.y - e.target.getBoundingClientRect().top,
            };
        },
        computeIndex(y) {
            let docI = 0;
            let pageI = 0;
            const initialDoc = { x: 0, y: 20, page: [{ x: 0, y: 0 }] };
            if (y <= 0) {
                y = SPACE + 1;
            }

            function reducePage(o) {
                o.page.reduce((p, n, i) => {
                    if (y > o.y + p.y && y < o.y + n.y) {
                        pageI = i - 1;
                    } else if (y >= o.y + n.y) {
                        pageI = i;
                    }
                    return n;
                }, { x: 0, y: 0 });
            }

            this.docList.reduce((p, n, i) => {
                if (y > p.y && y < n.y) {
                    docI = i - 1;
                    reducePage(p);
                } else if (y >= n.y) {
                    docI = i;
                    if (+i === this.docList.length - 1) {
                        reducePage(n);
                    }
                }
                return n;
            }, initialDoc);
            return {
                docI, pageI,
            };
        },
        getPosInSVG(mark) {
            const doc = this.docList[mark.docI];
            const page = doc.page[mark.pageI];
            return {
                x: (this.docList.maxWidth - page.width) / 2 + mark.x,
                y: doc.y + page.y + mark.y,
            };
        },
        // 新建标签，鼠标移动、拖拽时的边界控制
        buildPrison(page, mark, pX, pY) {
            const tooRight = pX >= page.width - mark.width && pX <= page.width;
            const tooDown = pY >= page.height - mark.height && pY < page.height + 10;

            if (tooRight || tooDown) {
                // 右 标签部分超出右边界时，把 x 定在最右边
                if (tooRight) {
                    mark.x = page.width - mark.width;
                }
                // 下 标签部分超出下边界时，把 y 定在最下边
                if (tooDown) {
                    mark.y = page.height - mark.height;
                }
            } else {
                mark.x = pX;
                mark.y = pY;
            }
        },

        // 已创建的标签，拖拽时的边界控制
        buildBlackHole(page, mark, pX, pY) {
            if (pX < 0) {
                mark.x = 0;
            } else if (pX > (page.width - mark.width)) {
                mark.x = (page.width - mark.width);
            }
            if (pY < 0) {
                mark.y = 0;
            } else if (pY > (page.height - mark.height)) {
                mark.y = (page.height - mark.height);
            }
            return {
                markX: mark.x,
                markY: mark.y,
            };
        },

        // 进入引力区域
        inAttraction(target, page, mark, x, y) {
            let isContain = false;
            let isCenter = false;
            const padding = 50;

            // 不在同一份文档或者同一页
            if ((target.documentId !== page.documentId) || (target.pageI !== mark.pageI)) {
                return {
                    contain: isContain,
                    center: isCenter,
                };
            }

            // 判断进入触发区域
            if (
                // 右下
                (x > (target.x - padding) && y > (target.y - mark.height + target.height - padding)) &&
                // 左上
                (x < (target.x + target.width + padding) && y < (target.y + target.height + padding))
            ) {
                // 进入 AI 元素实际区域
                if (
                    // 右下
                    (x > target.x && y > (target.y - mark.height + target.height)) &&
                    // 左上
                    (x < (target.x + target.width) && y < (target.y + target.height))
                ) {
                    isCenter = true;
                }

                isContain = true;
            } else {
                isContain = false;
            }

            return {
                contain: isContain,
                center: isCenter,
            };
        },

        // 引力场
        attractionField(page, mark, x, y) {
            // 目前只检查业务字段
            if (!(this.AIAttachFields.includes(mark.type))) {
                return false;
            }

            // 筛选当前页面的字段
            const curPageTriggers = this.holderTriggerList.filter(item => {
                return ((item.pageI === mark.pageI) && (item.documentId === page.documentId));
            });

            // 判断处于字段吸引中
            const inField = curPageTriggers.filter(trigger => {
                return this.inAttraction(trigger, page, mark, x, y).contain;
            });
            // 处于 AI 字段实际区域中
            const fieldCenter = curPageTriggers.filter(trigger => {
                return this.inAttraction(trigger, page, mark, x, y).center;
            });

            // AI 区域的高度赋值
            this.curTriggerHeight = mark.height;

            // 如果被多个字段吸引，激活最后一个字段
            // this.activePlaceHolder = inField.length ? inField[inField.length - 1].triggerId : null;
            this.activePlaceHolder = [];
            inField.forEach(item => {
                this.activePlaceHolder.push(item.triggerId);
            });

            // 在多个字段区域内，放置到最后一个字段
            this.intoPlaceHolder = fieldCenter.length ? fieldCenter[fieldCenter.length - 1].triggerId : null;
        },

        // 吸引力参数修正，将坐标修正为引力区域
        attracionAmend(mark) {
            // 目前只修正业务字段
            if (
                this.intoPlaceHolder === null || !this.AIAttachFields.includes(mark.type)
            ) {
                this.activePlaceHolder = [];
                return mark;
            }

            const targetTrigger = this.holderTriggerList.find(trigger => {
                return trigger.triggerId === this.intoPlaceHolder;
            });

            mark.x = targetTrigger.x;
            mark.width = targetTrigger.width;

            mark.y = targetTrigger.y - mark.height + targetTrigger.height;
            // mark.height = targetTrigger.height;

            // 重置引力区域
            this.activePlaceHolder = [];
            this.intoPlaceHolder = null;

            return mark;
        },

        clickDoc(e) {
            if (
                // e.target.getAttribute('class') != 'mark' &&
                e.target.getAttribute('class') !== 'marquee-circle' &&
                !e.target.closest('.Signature-cop') &&
                !e.target.closest('.temp-field-edit-cpn') &&
                !e.target.closest('.option') &&
                !e.target.closest('.option-delete') &&
                !e.target.closest('.Signature-cop')
            ) {
                this.blurMark();
            }
        },

        // 新建标签
        onMousemove: throttle(function(e) {
            if (this.iconDragStatus !== 'started') {
                return;
            }
            const $clt = e.target.closest('.image') || e.target.closest('.trigger');

            if ($clt) {
                const docI = $clt.getAttribute('doc-index');
                const pageI = $clt.getAttribute('page-index');
                const x = this.getMarkPostion(e).x / this.zoom;
                const y = this.getMarkPostion(e).y / this.zoom;

                this.creation.docI = docI;
                this.creation.pageI = pageI;

                if (this.creation.status === 'ready') {
                    const page = this.docList[docI].page[pageI];
                    const mark = this.marks[this.marks.length - 1];
                    mark.docI = docI;
                    mark.pageI = +pageI;
                    mark.x = x;
                    mark.y = y;
                    this.buildPrison(page, mark, x, y);
                    this.attractionField(page, mark, x, y);
                } else {
                    const markI = this.marks.length;

                    // 判断如果是文本标签的话，取自适应宽高
                    // markInfo 中返回的是常量值
                    const markSize = ['TEXT', 'DATE', 'BIZ_DATE', 'TEXT_NUMERIC'].indexOf(this.float.type) > -1 ? textMarkInfo(this.float) : markInfo(this.float.type);

                    let createDom = {
                        roleId: this.receivers[this.receiverIndex].roleId,
                        type: this.float.type,
                        name: this.float.name,
                        docI,
                        pageI,
                        markI,
                        x,
                        y,
                        // create时宽高取默认，其他操作取当前mark宽高
                        width: markSize.width,
                        height: markSize.height,
                        // 文本专属，取meta上的值
                        receiverFill: this.float.receiverFill,
                        fontSize: this.float.fontSize,
                        necessary: this.float.necessary,
                        refBizFieldId: this.float.refBizFieldId,
                        alignment: 0,
                    };
                    if (['MULTIPLE_BOX', 'SINGLE_BOX'].includes(this.float.type)) {
                        let buttons = [{ buttonX: 10, buttonY: 10, buttonValue: '备选项1' }, { buttonX: 10, buttonY: 41, buttonValue: '备选项2' }];
                        let height = markSize.height;
                        // 如果是从业务字段拖过来的
                        if (this.float.buttons) {
                            buttons = this.float.buttons.map((name, index) => {
                                return { buttonX: 10, buttonY: 10 + 31 * index, buttonValue: name };
                            });
                            const markSize = this.changeMarkSize(this.float.type, buttons);
                            height = markSize.height;
                        }
                        createDom = {
                            ...createDom,
                            buttons,
                            height,
                            // buttonDirection: 'V', // 默认竖向
                        };
                    }
                    this.createMark(createDom);
                    this.creation.status = 'ready';
                }
            } else {
                if (this.creation.status === 'ready') {
                    this.deleteMark(this.marks.length - 1);
                    this.creation.status = 'pedding';
                }
            }
        }, 24),
        // 选项改变，标签大小跟着改变
        changeMarkSize(markType, newButtons) {
            const markButtonStyle = markInfo(markType).button || {};
            const markAddWidth = markButtonStyle.initSplit + markButtonStyle.width;
            const markAddHeight = markButtonStyle.initSplit + markButtonStyle.height;

            const metaData = {
                buttons: newButtons,
            };
            const list = [].concat(newButtons);
            metaData.width = list.sort((a, b) => b.buttonX - a.buttonX)[0].buttonX + markAddWidth;

            metaData.height = list.sort((a, b) => b.buttonY - a.buttonY)[0].buttonY + markAddHeight;

            return metaData;
        },
        // hasDiffType 新建时判断是否存在同名不同类型字段
        hasDiffType(i) {
            const newItem = this.marks[i];
            const oldMarks = this.marks.slice(0, i);
            const index = oldMarks.findIndex((mark) => {
                return mark.name === newItem.name && mark.type !== newItem.type;
            });
            return index !== -1;
        },
        // mouseup 事件，从左边拖拽、点击增加标签时都会进入这里
        onMouseup() {
            if (this.creation.status !== 'ready') {
                return;
            }
            this.creation.status = 'done';

            // 新建的标签位置在最后一位
            const i = this.marks.length - 1;
            // 判断是否存在同名不同类型字段
            if (this.marks.length > 1 && this.hasDiffType(i)) {
                this.deleteMark(i);
                this.$MessageToast.error('已存在不同类型的同名字段');
                return;
            }

            const mark = this.attracionAmend(this.marks[i]);
            const orcDoc = this.docList[mark.docI];
            const signatureDocTypeIsAttachment = !!this.docList[mark.docI].attachmentId; // 是否在附件上添加标签
            // 使用模板时，如果拖动签署字段到文档，则删除标签&弹框提示
            // 根据下边的代码this.docList[mark.docI]中的新增属性可以判断当前为文档还是附件
            if (!this.isEdit && !signatureDocTypeIsAttachment) {
                this.deleteMark(this.marks.length - 1);
                this.$MessageToast.error('您只能设置附件中的签署位置');
                return;
            }
            let postData = {};
            let attachmentId = '';
            const pageNumber = mark.pageI > orcDoc.pageSize ? orcDoc.pageSize : parseFloat(mark.pageI) + 1;
            if (!signatureDocTypeIsAttachment) {
                postData = {
                    labelId: '', // 新建标签，labelId为空
                    contractId: this.contractId,
                    documentId: this.docList[mark.docI].documentId,
                    pageNumber,
                };
            } else if (signatureDocTypeIsAttachment) {
                attachmentId = this.docList[mark.docI].attachmentId;
                postData = {
                    labelId: '', // 新建标签，labelId为空
                    contractId: this.contractId,
                    attachmentId: this.docList[mark.docI].attachmentId,
                    pageNumber,
                };
            }

            // 标签坐标转换为以页面左下角为原点基于页面的百分比,标签宽高转换为与页面宽高的百分比
            const page = this.docList[mark.docI].page[mark.pageI];
            const percentCoordinate = markCoordinateTransform(mark, page.width, page.height);
            if (!signatureDocTypeIsAttachment) {
                this.saveMark({
                    ...postData,
                    ...mark,
                    ...percentCoordinate,
                    name: this.formatNewMarkName(mark),
                })
                    .then(res => {
                        this.updateMarks(res.data, i, page);
                        // update focus
                        this.focusMark({
                            markIndex: i,
                            signerIndex: this.receiverIndex,
                            markButtonInd: 0,
                        });
                    })
                    .catch(() => {
                        this.deleteMark(i);
                    });
            } else if (signatureDocTypeIsAttachment) {
                this.saveAttachmentMark({
                    ...postData,
                    ...mark,
                    ...percentCoordinate,
                }, attachmentId)
                    .then(res => {
                        this.updateMarks([res.data.result], i, page);
                        // update focus
                        this.focusMark({
                            markIndex: i,
                            signerIndex: this.receiverIndex,
                        });
                    })
                    .catch(() => {
                        this.deleteMark(i);
                    });
            }
        },

        // 标签点击
        onMarkClick(markButtonInd, mark, index) {
            // 聚焦
            if (mark.type !== 'QR_CODE') {
                this.focusMark({
                    markIndex: index,
                    signerIndex: this.findReceiverIdx(mark.roleId),
                    markButtonInd,
                });
            }
        },

        // 标签拖拽移动
        onMarkStart(e, mark, index) {
            const $clt = e.target.closest('.mark');
            if (!$clt) {
                return;
            }
            this.markStash = mark;
            this.move.eX = e.x;
            this.move.eY = e.y;
            this.move.markX = mark.x;
            this.move.markY = mark.y;
            this.move.pageI = mark.pageI;
            this.move.docI = mark.docI;
            this.move.pageNumber = mark.pageNumber;
            this.focusMark({
                markIndex: index,
                signerIndex: this.findReceiverIdx(mark.roleId),
            });
        },
        onMarkMove(e, mark, index) {
            mark.x = this.move.markX + (e.x - this.move.eX) / this.zoom;
            mark.y = this.move.markY + (e.y - this.move.eY) / this.zoom;
            this.focusMark({
                markIndex: index,
                signerIndex: this.findReceiverIdx(mark.roleId),
            });
        },
        onMarkButtonChange(data, mark, index) {
            this.focusMark({
                markIndex: index,
                signerIndex: this.findReceiverIdx(mark.roleId),
                markButtonInd: data.markButtonInd,
            });
            this.$emit('save-meta', index, data.metaData, 'buttons');
        },
        // 标签拖拽结束
        onMarkEnd(e, mark, index) {
            // 修复弱网情况下，用户创建标签接口未返回，又快速拖动标签导致标签重复创建的问题
            if (!mark.labelId) {
                return;
            }
            const oriDoc = this.docList[mark.docI];
            const oriPage = oriDoc.page[mark.pageI];
            const y = oriDoc.y + oriPage.y + mark.y; // mark相对于整个svg的y坐标
            const { docI, pageI } = this.computeIndex(y);
            const doc = this.docList[docI];
            const page = doc.page[pageI];

            const signatureDocTypeIsAttachment = !!this.docList[mark.docI].attachmentId; // 是否在附件上添加标签
            mark.docI = docI;
            mark.pageI = pageI;

            // x, y是mark相对于页面的坐标
            mark.x = mark.x + (page.width - oriPage.width) / 2;
            mark.y = y - (doc.y + page.y);
            this.buildBlackHole(page, mark, mark.x, mark.y);

            let postMark = {
                labelId: mark.labelId,
                roleId: mark.roleId,
                fontSize: mark.fontSize,
                templateId: this.contractId,
                pageNumber: mark.pageI > oriDoc.pageSize ? oriDoc.pageSize : parseFloat(mark.pageI) + 1, // fix CFD-1329,限制最大页码
                type: mark.type, // SIGNATURE(1), DATE(2), SEAL(3),
                name: mark.name,
                x: mark.x,
                y: mark.y,
                width: mark.width,
                height: mark.height,
                refBizFieldId: mark.refBizFieldId,
                alignment: mark.alignment || 0,
            };
            if (['MULTIPLE_BOX', 'SINGLE_BOX'].includes(mark.type)) {
                postMark = {
                    ...postMark,
                    buttons: mark.buttons,
                };
            }
            // 标签坐标转换为以页面左下角为原点基于页面的百分比,标签宽高转换为与页面宽高的百分比
            const percentCoordinate = markCoordinateTransform(postMark, page.width, page.height);
            postMark = { ...postMark, ...percentCoordinate };

            if (!signatureDocTypeIsAttachment) {
                postMark.documentId = this.docList[mark.docI].documentId;
                this.saveMark(postMark)
                    .then(res => {
                        this.updateMarks(res.data, index, page, true);
                    })
                    .catch(() => {
                        this.$set(this.marks, index, {
                            ...this.marks[index],
                            x: this.move.markX,
                            y: this.move.markY,
                        });
                    });
            } else if (signatureDocTypeIsAttachment) {
                postMark.attachmentId = this.docList[mark.docI].attachmentId;
                if (!postMark.attachmentId) {
                    this.$MessageToast.error('您只能设置附件中的签署位置');
                    mark.x = this.move.markX;
                    mark.y = this.move.markY;
                    mark.pageI = this.move.pageI;
                    mark.docI = this.move.docI;
                    mark.pageNumber = this.move.pageNumber;
                    return;
                }
                this.saveAttachmentMark(postMark, postMark.attachmentId)
                    .then(res => {
                        this.updateMarks([res.data.result], index, page);
                    })
                    .catch(() => {
                        this.$set(this.marks, index, {
                            ...this.marks[index],
                            x: this.move.markX,
                            y: this.move.markY,
                        });
                    });
            }
        },

        // 删除标签
        onMarkDelete(e, i) {
            // let mark = this.marks.splice(i, 1)[0];
            // let labelId = mark.labelId;
            // this.blurMark();
            // this.deleteMarks(labelId)
            // .then( res => {
            // this.$MessageToast.success('删除成功');
            // this.marks.splice(i, 1);
            // } )
            // .catch( () => {} );
            const labelId = this.marks[i].labelId;
            const attachmentId = this.marks[i].attachmentId;
            if (attachmentId) {
                this.$emit('attmark-delete', i, labelId, attachmentId);
            } else {
                this.$emit('mark-delete', i, labelId);
            }
        },
        // 添加标签选项按钮
        onMarkButtonAdd(e, mark, i) {
            mark.buttons.push(e);
            this.$set(this.marks, i, {
                ...this.marks[i],
            });
        },
        // 选中框
        onMarqueeMove(newWidth, newHeight) {
            const i = this.focus.markIndex;
            const mark = this.marks[i];
            mark.width = newWidth;
            mark.height = newHeight;
        },
        onMarqueeEnd() {
            const mark = this.focus.mark;
            if (!mark.labelId) {
                return;
            }
            const page = this.docList[mark.docI].page[mark.pageI];
            this.buildBlackHole(page, mark, mark.x, mark.y);
            // 将标签坐标转换成百分比形式传给后端
            const percentCoordinate = markCoordinateTransform(mark, page.width, page.height);
            const data = {
                labelId: mark.labelId,
                ...percentCoordinate,
            };
            const i = this.focus.markIndex;
            this.saveMark(data)
                .then(res => {
                    // 图片类型的大小需要同步
                    this.updateMark(i, page, {
                        ...this.marks[i],
                        ...res.data,
                    });
                    this.$emit('mark-update', this.marks);
                    // update focus
                    this.focusMark({
                        markIndex: i,
                        signerIndex: this.receiverIndex,
                    });
                });
        },
        createMark(data) {
            this.marks.push(data);
        },
        deleteMark(i) {
            this.marks.splice(i, 1);
        },
        saveMark(data) {
            return this.postMarks(data);
        },
        saveAttachmentMark(data, attachmentId) {
            return this.postAttachmentMarks(data, attachmentId);
        },
        // 更新标签
        updateMarks(data, i, page, isMove) {
            if (isMove) { // 如果只是拖拽位置，只更新当前项
                const index = data.findIndex((a) => a.labelId === this.marks[i].labelId);
                this.updateMark(i, page, data[index]);
            } else if (data.length > 1) { // 存在同名字段需要更新，else不存在的话只需要更新自己
                data.forEach((updateItem) => {
                    const index = this.marks.findIndex((a) => a.labelId === updateItem.labelId);
                    // 新增字段默认push在marks末尾并且已经初始化部分属性
                    const newUpdateItem = index === -1 ? { ...this.marks[this.marks.length - 1], ...updateItem } : { ...this.marks[index], ...updateItem };
                    if (index === -1) { // 如果是新增的标签
                        this.updateMark(i, page, newUpdateItem);
                    } else {
                        this.updateMark(index, this.docList[newUpdateItem.docI].page[newUpdateItem.pageI], newUpdateItem);
                    }
                });
                this.$MessageToast.info(`已将${data.length - 1}个同名字段属性更新为当前字段属性值`); // 更新同名字段属性提示
            } else {
                this.updateMark(i, page, { // 布局和计算需要延用当前mark属性
                    ...this.marks[this.marks.length - 1],
                    ...data[0],
                });
            }
            this.$emit('mark-update', this.marks);
        },
        updateMark(i, page, data) {
            const numberCoordinate = coordinateReversal(data, page.width, page.height);
            this.$set(this.marks, i, {
                ...this.marks[i],
                ...data,
                ...numberCoordinate,
            });
        },
        focusMark(data) {
            const { markIndex, signerIndex, markButtonInd = -1 } = data;
            if (this.marks[markIndex].type === 'QR_CODE') {
                return;
            }
            this.$emit('mark-focus', { markIndex, signerIndex, markButtonInd });
        },
        blurMark() {
            this.$emit('mark-blur');
        },

        onDocScroll: throttle(function(e) {
            const scrollTop = e.target.scrollTop / this.zoom;
            const { pageI, docI } = this.computeIndex(scrollTop);
            this.$emit('doc-scroll', {
                pageI, docI,
            });
        }, 24),

        // ajax
        postMarks(labelObj, opts) {
            return this.$http.post(
                `${tempPath}/templates/${this.contractId}/labels/create-and-modify`,
                labelObj,
                opts,
            );
        },
        postAttachmentMarks(label, attachmentId, opts) {
            return this.$http.post(
                `${tempPath}/templates/${this.contractId}/${attachmentId}/save-lable`,
                label,
                opts,
            );
        },
        computeFillColor(mark) {
            // 签署方或者印章、签名、日期类型时，根据身份选择
            if (mark.receiverFill || COLOR_TYPES.includes(mark.type)) {
                return this.colorInfo[this.findReceiverIdx(mark.roleId) % 8] || 'transparent';
            } else {
                // 发件方使用固定颜色
                return colorInfo[0];
            }
        },
        // 临时字段-处理单选框，复选框name，
        formatNewMarkName(mark) {
            if (['SINGLE_BOX', 'MULTIPLE_BOX'].includes(mark.type) && !this.float.buttons) {
                const sameTypeMarks = this.marks.filter(a => a.type === mark.type && a.labelId); // 当前已存在同类型标签，无labelId为临时数据
                let count = sameTypeMarks.length;
                const nameStr = markInfo(mark.type).name;
                let sameNameLabel;
                do {
                    count++;
                    sameNameLabel = sameTypeMarks.filter(a => {
                        return a.name === `${nameStr}${count}`;
                    });
                } while (sameNameLabel.length > 0);

                return `${nameStr}${count}`;
            }
            return mark.name;
        },
        // deleteMarks(labelId) {
        // return this.$http.delete(`${tempPath}/templates/${this.contractId}/labels/${labelId}`);
        // }
        // ajax   获取文档信息，是否可以上传附件
    },
    created() {
        Bus.$on('add-decorator', (type) => {
            if (type === 'WATERMARK') {
                return this.addWatermark();
            }
            if (type === 'DECORATE_RIDING_SEAL') {
                return this.addRideSeal();
            }
        });
    },
    beforeDestroy() {
        document.removeEventListener('mousemove', this.onMousemove);
        document.removeEventListener('mouseup', this.onMouseup);
        document.removeEventListener('click', this.clickDoc);
        Bus.$off('add-decorator'); // 移除事件避免多次监听
    },
    mounted() {
        this.initDocWidth();
        document.addEventListener('mousemove', this.onMousemove);
        document.addEventListener('mouseup', this.onMouseup);
        document.addEventListener('click', this.clickDoc);
    },
};
</script>

<style lang="scss">
    .Field-Doc-cpn{
        .trigger-rect{
            transition: border 1s linear;
            animation-name: ring;
            animation-duration: 60s;
            animation-timing-function: linear;
            animation-iteration-count: infinite;
        }

        @keyframes ring {
            from {
                stroke-dashoffset: 0;
            }
            to {
                stroke-dashoffset: 2000;
            }
        }
    }
</style>
