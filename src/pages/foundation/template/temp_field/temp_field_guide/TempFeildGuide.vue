<!-- 模板指定位置——新手指引组件 -->
<template>
    <!-- 用 class 控制当前显示的步骤 -->
    <!-- user-guide是公用css（参见common.scss），用于控制步骤 -->
    <div class="temp-field-guide-cpn user-guide"
        :class="guide.stepQueue[0]"
        v-if="guide.show"
    >
        <div class="guide-ent-step1 guide-step">
            <img src="~img/guide-step-1.png"
                width="480"
                height="136"
            >
            <span class="guide-trigger"
                @click="clickNextGuide"
            >
            </span>
        </div>
        <div class="guide-ent-step2 guide-step"
            :class="{ 'guide-step2-ent': guideUserType !== 'PERSON' }"
        >
            <img src="~img/guide-step-2.png"
                width="560"
                height="200"
            />
            <span class="guide-text"
                v-if="guideUserType === 'PERSON'"
            >
                <i class="el-icon-ssq-bi"></i>
                签名
            </span>
            <span class="guide-text" v-else>
                <i class="el-icon-ssq-gongzhang"></i>
                盖章
            </span>
            <span class="guide-trigger"
                @click="clickNextGuide"
            >
            </span>
        </div>
    </div>
</template>
<script>
export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['guideUserType'],
    data() {
        return {
            // 新手引导
            guide: {
                show: false,
                stepVisible: '',
                stepQueue: ['active-step-2'],
            },
        };
    },
    methods: {
        // 用户引导
        initGuide() {
            this.$http.get('/users/configs/USER_GUIDE')
                .then(res => {
                    if (!res.data.value || res.data.value.indexOf('signpage') === -1) {
                        this.guide.show = true;
                        this.guide.stepQueue = ['active-step-2'];
                    }
                })
                .catch(() => {});
        },
        // 下一步引导
        clickNextGuide() {
            // 如果已经在最后一步，就关闭引导
            if (this.guide.stepQueue.length === 1) {
                this.guide.show = false;
                this.guide.stepQueue.splice(0, 1, '');
                this.$http.post('/users/configs/USER_GUIDE', {
                    value: 'signpage',
                    name: 'USER_GUIDE',
                })
                    .catch(() => {});
                return;
            }
            this.guide.stepQueue.shift();
        },

        // ajax
        getGuideInfo() {
            return this.$http.get('/users/configs/USER_GUIDE');
        },
    },
    created() {
        // 用户引导
        this.getGuideInfo()
            .then(res => {
                if (!res.data.value || res.data.value.indexOf('signpage') === -1) {
                    this.guide.show = true;
                    this.guide.stepQueue = ['active-step-2'];
                }
            })
            .catch(() => {});
    },
};
</script>
<style lang="scss">
	.temp-field-guide-cpn {
		position: fixed;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;

		.guide-ent-step1 {
			left: 11px;
			top: 54px;

			.guide-trigger {
				right: 37px;
				bottom: 9px;
			}
		}
		.guide-ent-step2 {
			left: 10px;
			top: 372px;

			.guide-text {
				left: 13px;
				top: 9px;
				font-size: 12px;

				i {
					width: 24px;
					height: 24px;
					line-height: 24px;
					font-size: 16px;
					text-align: center;
					border-radius: 3px;
					margin-top: 4px;
					margin-right: 10px;
					background: rgb(201, 231, 255);
					color: #333;
				}
			}

			.guide-trigger {
				right: 0;
				top: 6px;
			}

			&.guide-step2-ent {
				top: 372px;
			}
		}
	}
</style>
