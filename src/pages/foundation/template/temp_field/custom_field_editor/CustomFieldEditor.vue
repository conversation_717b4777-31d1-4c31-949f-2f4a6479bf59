<!-- eslint-disable vue/prop-name-casing -->
<template>
    <div class="cus-field-editor-cpn">
        <el-form ref="form" :model="form">

            <el-form-item label="名称" class="form-name">
                <el-input placeholder="必填"
                    v-model="form.name"
                    :minlength="1"
                    :maxlength="30"
                    :disabled="markType === 'DATE'"
                    @blur="onChange('name')"
                >
                </el-input>
            </el-form-item>
            <template v-if="markType != 'DATE'">
                <!-- <div v-if="['SINGLE_BOX','MULTIPLE_BOX'].includes(markType)" class="cus-field-editor-cpn--date-content-writer-con">
                    <p class="cus-field-editor-cpn--date-content-writer-con-title">内容填写人</p>
                    <p class="reciver-write"><i class="el-icon-ssq-tishi1"></i>该内容仅合同签署人可以填写</p>
                </div> -->
                <el-form-item label="内容填写人">
                    <p v-if="markType === 'PICTURE'" class="image-tips"><i class="el-icon-ssq-tishi1" style="padding-right: 4px;"></i>该内容仅发件人可以填写</p>
                    <el-radio-group
                        v-else
                        v-model="form.receiverFill"
                        @change="onChange('receiverFill')"
                    >
                        <el-radio :label="false">
                            发件人
                            <el-tooltip
                                class="item"
                                effect="dark"
                                content="选择“发件人”，则此合同内容在合同发出前由发件人填写"
                                placement="top"
                            >
                                <i class="el-icon-ssq-wenhao tips"></i>
                            </el-tooltip>
                        </el-radio>
                        <el-radio :label="true">
                            签署人
                            <el-tooltip
                                class="item"
                                effect="dark"
                                content="选择“签署人”，则此合同内容在签署人签署时填写"
                                placement="top"
                            >
                                <i class="el-icon-ssq-wenhao tips"></i>
                            </el-tooltip>
                        </el-radio>
                    </el-radio-group>
                </el-form-item>

                <!-- 签署人多个变量时添加身份选择 -->
                <el-form-item v-if="form.receiverFill && receivers.length > 1 && markType !== 'PICTURE'" class="cus-field-editor-identity">
                    <el-radio-group v-model="form.roleId" @change="onChange('roleId')">
                        <div v-for="item in receivers"
                            :key="item.roleId"
                        >
                            <el-radio :label="item.roleId">
                                <span>{{ item.showName }}</span>
                            </el-radio>
                        </div>
                    </el-radio-group>
                </el-form-item>
                <!-- 临时字段-图片类型 设置宽高 -->
                <el-form-item v-if="markType === 'PICTURE'" label="显示尺寸">
                    <div>
                        <span class="image-tips-1">宽度</span><el-input v-model="form.width" type="Number" placeholder="请输入宽度" class="image-tips-input" @blur="onChange('width')" /><span class="image-tips-1">px</span>
                    </div>
                    <div>
                        <span class="image-tips-1">高度</span><el-input v-model="form.height" type="Number" placeholder="请输入高度" class="image-tips-input" @blur="onChange('height')" /><span class="image-tips-1">px</span>
                    </div>
                </el-form-item>
            </template>
            <div v-else class="cus-field-editor-cpn--date-content-writer-con">
                <p class="cus-field-editor-cpn--date-content-writer-con-title">内容填写人</p>
                <p>系统自动填写</p>
            </div>
            <template v-if="['SINGLE_BOX','MULTIPLE_BOX'].includes(markType)">
                <!-- <el-form-item label="方向设置">
                        <el-radio-group class="buttons-direction" v-model="form.buttonDirection" @change="onChange('buttonDirection')">
                            <div><el-radio label="V">垂直向下增加</el-radio></div>
                            <div><el-radio label="H">水平向右增加</el-radio></div>
                         </el-radio-group>
                </el-form-item> -->
                <el-form-item label="备选项">
                    <div class="buttons">
                        <div v-for="(item,index) in form.buttons" :key="index">
                            <span class="option-checkbox"></span>
                            <el-input :maxlength="30"
                                @focus="changeButton(index,item)"
                                @blur="onChange('buttons',index)"
                                :class="index== markButtonInd? 'selected':''"
                                type="text"
                                v-model.trim="item.buttonValue"
                            >
                                <ElIDelete class="option-delete" v-if="index== markButtonInd" slot="icon" :selfClick="true" @click="clearButtons($event,item)"></ElIDelete>
                            </el-input>
                        </div>
                    </div>
                </el-form-item>
            </template>
            <el-form-item label="字号" v-else-if="markType !== 'PICTURE'">
                <el-select popper-class="cus-field-editor-select"
                    v-model="form.fontSize"
                    placeholder="请选择字号"
                    :disabled="fontSizeSelectDisable"
                    @change="onChange('fontSize')"
                >
                    <el-option
                        v-for="(item, index) in fontSizeRange"
                        :key="index"
                        :label="item.label"
                        :value="convertPtToPx(item.pt, dpi)"
                    ></el-option>
                </el-select>
            </el-form-item>
            <!-- 设置文字对齐方式 -->
            <el-form-item label="文字对齐方式" v-if="!hybridServer && ['TEXT'].indexOf(markType) > -1">
                <el-radio-group v-model="form.alignment" @change="onChange('alignment')" class="alignment-group">
                    <!-- private Integer alignment;0（居左）,1（居中）,2（居右） -->
                    <el-radio v-for="(item, i) in alignmentArr" :key="i" :label="item.label" :class="{'active': form.alignment==item.label}">
                        <i :class="item.class"></i>
                    </el-radio>
                </el-radio-group>
            </el-form-item>

            <el-form-item v-if="markType !== 'DATE'" label="填写要求">
                <el-checkbox-group
                    v-model="form.necessary"
                    @change="onChange('necessary')"
                >
                    <el-checkbox label="必填，不填不能发送或签署" name="type"></el-checkbox>
                </el-checkbox-group>
            </el-form-item>

        </el-form>

        <div class="btns clear"
            v-if="!ImmediateSave"
        >
            <div class="ssq-btn-cancel"
                @click="clickCancelBtn"
            >取消</div>
            <div class="ssq-btn-confirm"
                @click="clickConfirmBtn"
            >确定</div>
        </div>

    </div>
</template>
<script>
import { mapState } from 'vuex';
import { convertPtToPx, fontSizeRange } from 'utils/fontSize.js';
import ElIDelete from 'components/el_i_delete/ElIDelete.vue';
import { find } from 'src/common/utils/dom.js';
import resRules from 'utils/regs.js';
export default {
    components: {
        ElIDelete,
    },
    props: {
        // eslint-disable-next-line vue/prop-name-casing
        ImmediateSave: {
            type: Boolean,
            default: false,
        },
        editorMetaData: {
            type: Object,
            default: () => {
                return {
                    name: '',
                    receiverFill: '',
                    fontSize: 14,
                    necessary: true,
                    fieldType: '',
                    roleId: '', // 角色Id
                    // buttonDirection: 'V',
                    type: '',
                    buttons: [],
                    width: 0,
                    height: 0,
                    labelId: null,
                    docI: 0,
                    pageI: 0,
                    alignment: 0,
                };
            },
        },
        receivers: {
            type: Array,
            default: () => [],
        },
        markButtonInd: {
            type: Number,
            default: -1,
        },
    },
    inject: ['getMarkCurrentPage'],
    data() {
        return {
            form: JSON.parse(JSON.stringify(this.editorMetaData)),
            cacheOldData: JSON.parse(JSON.stringify(this.editorMetaData)),
            dpi: 96, // 目前写死
            convertPtToPx: convertPtToPx,
            fontSizeRange: fontSizeRange,
            input: '',
            alignmentArr: [{
                label: 0,
                class: 'el-icon-ssq-juzuoduiqi',
            }, {
                label: 1,
                class: 'el-icon-ssq-juzhongduiqi',
            }, {
                label: 2,
                class: 'el-icon-ssq-juyouduiqi',
            }],
        };
    },
    computed: {
        ...mapState({
            hybridServer: state => state.commonHeaderInfo.hybridServer,
        }),
        markType() {
            return this.editorMetaData.type;
        },
        fontSizeSelectDisable() {
            // 混合云用户禁止修改签署日期的字号
            return !!this.hybridServer && this.markType === 'DATE';
        },
    },
    watch: {
        editorMetaData: {
            handler(v) {
                this.cacheOldData = JSON.parse(JSON.stringify(v)); // 缓存form数据
                this.form = JSON.parse(JSON.stringify(v)); // 缓存form数据
            },
            deep: true,
        },
    },
    methods: {
        changeButton(ind) {
            if (('' + ind) !== ('' + this.markButtonInd)) {
                this.$emit('change-mark-button-ind', ind);
            }
        },
        onChange(k, index) {
            if (!this.ImmediateSave) {
                return;
            }
            if (k === 'name' && this.form[k].trim().length === 0) {
                this.$MessageToast.error('请输入名称');
                return;
            }
            if (k === 'buttons') {
                const now = this.form[k][index].buttonValue;
                const old = this.cacheOldData[k][index].buttonValue;
                const sameItems = this.form[k].filter(item => item.buttonValue === now);
                if (!now.trim()) {
                    this.$MessageToast.error('请输入备选项');
                    return;
                } else if (old === now) {
                    return;
                } else if (sameItems.length > 1) {
                    this.$MessageToast.error('备选项不能相同');
                    return;
                }
                // 备选项名字需要做格式校验，不能包括特殊字符, CFD-6264，SAAS-12570
                if (resRules.fieldValueReg.test(now)) {
                    this.$MessageToast.error('备选项名字不能包含特殊字符\\/#()');
                    return;
                }
            }

            if (this.cacheOldData[k] === this.form[k]) { // 利用缓存数据，在名称未发生变化时，直接返回，不触发数据更新
                return;
            }

            const data = {};
            data[k] = this.form[k];
            if (k !== 'fontSize' && this.form.fontSize) { // 避免后端设置字体默认值
                data.fontSize = this.form.fontSize;
            }
            if (k === 'buttons') { // 修改备选项
                data[k][index].buttonValue = this.form[k][index].buttonValue;
            }

            // 图片类型，修改宽高 大小限制提示
            if (this.markType === 'PICTURE' && (k === 'width' || k === 'height')) {
                const { width, height } = this.getMarkCurrentPage(this.editorMetaData.docI, this.editorMetaData.pageI);
                let errMsg = null;
                if (k === 'width') {
                    if (this.form[k] < 28) {
                        errMsg = '宽度请输入大于等于28的值';
                    }
                    if (this.form[k] >= width) {
                        errMsg =  `宽度请输入小于${width}的值`;
                    }
                } else {
                    if (this.form[k] < 20) {
                        errMsg = '高度请输入大于等于20的值';
                    }
                    if (this.form[k] >= height) {
                        errMsg = `高度请输入小于${height}的值`;
                    }
                }
                if (errMsg) {
                    this.$set(this.form, k, this.cacheOldData[k]);
                    return this.$MessageToast.error(errMsg);
                }
            }
            this.$emit('change-value', data, k);
        },

        clickConfirmBtn() {
            this.$emit('confirm', this.form);
        },

        clickCancelBtn() {
            this.$emit('cancel');
        },
        clearButtons(e, item) {
            item.buttonValue = '';
            find(e.$parent.$el, 'INPUT').focus();
        },
    },
};
</script>
<style lang="scss">
	.cus-field-editor-cpn {
		label {
			width: 100%;
			height: 20px;
			font-size: 14px;
			color: #333;
			text-align: left;
			padding-top: 0;
			padding-bottom: 0;
		}
		.form-name .el-form-item__label::after {
			content: "*";
            color: #f86b26;
            margin-left: 4px;
		}
		.el-form-item__content .el-input {
			padding-top: 0;
			.el-input__inner {
				height: 28px;
				line-height: 28px;
				font-size: 12px;
			}
			.el-input__icon {
				color: #333;
			}
		}
		.el-radio {
			width: 72px;
			.el-radio__label {
				padding-left: 2px;
			}
		}
		.btns {
			.ssq-btn-confirm, .ssq-btn-cancel {
				float: right;
				line-height: 34px;
			}
			.ssq-btn-confirm {
				margin-right: 8px;
			}
		}
		.tips {
			margin-left: 1px;
			font-size: 12px;
			color: #666;
			cursor: pointer;
		}
		.cus-field-editor-identity {
			margin-top: -16px;
            .el-radio-group {
                .el-radio {
					width: 100%;
					overflow: hidden;
					height: auto;
					margin-bottom: 3px;
                    .el-radio__input {
                        width: 20px;
                        float: left;
                    }
                    .el-radio__label {
                        display: block;
                        margin-left: 20px;
						white-space: normal;
						line-height: 16px;
                    }
                }
            }
        }
        .alignment-group.el-radio-group {
            background: #F8F8F8;
            border: 1px solid #DDDDDD;
            border-radius: 2px;
            border-radius: 2px;
            width: 100%;
            .el-radio {
                width: 32%;
                margin: 0;
                text-align: center;
                padding: 4px 0;
                i[class^="el-icon-ssq-"] {
                    font-size: 20px;
                }
                &.active {
                    background: #FFFFFF;
                    i[class^="el-icon-ssq-"] {
                        color: #127FD2;
                    }
                }
                &:nth-child(2) {
                    width: 34%;
                    border-left: 1px solid #DDDDDD;
                    border-right: 1px solid #DDDDDD;
                }
            }
            .el-radio .el-radio__input .el-radio__inner {
                display: none;
            }
        }
	}
        .buttons{
            .option-checkbox{
                display: inline-block;
                background: #FFFFFF;
                border: 1px solid #CCCCCC;
                border-radius: 2px;
                width: 14px;
                height: 14px;
                vertical-align: middle;
            }
            .el-input{
                background: #FFFFFF;
                width: 136px;
                height: 30px;
                margin-left: 8px;
                border: 1px solid #CCCCCC;
                box-sizing: border-box;
                line-height: 100%;
                input,input:hover,input:focus{
                    padding-left: 6px!important;
                    box-shadow:none;
                    border:none;
                    height: 30px;
                    line-height: 30px;
                }
                &.selected{
                    border: 1px solid #127fd2;
                    box-shadow: 0 0 2px #47acfc;
                }
            }
        }
        .buttons-direction{
            line-height: 22px;
            .el-radio .el-radio__label{
                padding-left: 10px;
            }
        }

	.cus-field-editor-select {
		li {
			height: 28px;
			line-height: 28px;
			padding: 0 10px;
		}
	}
	.cus-field-editor-role {
		margin-top: -16px;
	}
    .cus-field-editor-cpn--date-content-writer-con {
        margin-bottom: 22px;
        color: #333;
        .reciver-write{
            color: #999;
            font-size: 12px;
            i{
                padding-right: 3px;
            }
        }
    }
    .cus-field-editor-cpn--date-content-writer-con-title {
        margin-bottom: 8px;
        font-size: 14px;

    }
    .image-tips {
        font-size: 12px;
        color: #999999;
        &-1 {
            color: #333333;
            display: inline-block;
            font-size: 12px;
        }
        &-input {
            width: 125px;margin: 0 9px;
        }
    }
</style>
