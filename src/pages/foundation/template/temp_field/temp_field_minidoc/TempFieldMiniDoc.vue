<template>
    <div class="temp-field-minidoc-cpn">

        <!-- 缩略图区 -->
        <div class="title">
            {{ title }}
            <span v-if="false">编辑</span>
        </div>

        <div class="doc-edit-view">
            <ul>
                <li class="doc"
                    :class="doc.attachmentId ? 'no-bottom': ''"
                    :data-docId="doc.documentId"
                    v-for="(doc, docIndex) in docList"
                    :key="doc.attachmentId?doc.attachmentId:doc.documentId"
                >

                    <template v-if="doc.attachmentId">
                        <div class="doc-title doc-title-attachment" :style="doc.documentId != docList[0].documentId ? 'display: none' : ''"> <i class="el-icon-ssq-fujian1"></i> 附件：{{ doc.fileName }}</div>
                    </template>
                    <template v-else>
                        <div class="doc-title"
                            :class="{ 'close': docIndex !== 0 }"
                            @click="clickDocTitle"
                        >
                            {{ doc.fileName }}
                        </div>
                        <div class="doc-totalPage"
                            :style="docIndex != 0 ? 'display: none' : ''"
                        >
                            页数：{{ doc.allPageSize }}页
                        </div>
                        <div class="doc-totalPage doc-totalPage-attachment" :style="docIndex != 0 ? 'display: none' : ''" v-if="doc.allPageSize - doc.pageSize > 0"> <i class="el-icon-ssq-fujian1"></i>附件：<span>{{ doc.allPageSize - doc.pageSize }}</span>页</div>
                    </template>

                    <div class="doc-pages"
                        :style="docIndex != 0 && doc.documentId != docList[0].documentId? 'display: none':''"
                    >
                        <ul>
                            <li v-for="(page, pageIndex) in doc.page"
                                :key="page.imageId"
                                :class="{ 'current':
                                    docI == docIndex &&
                                    pageI == pageIndex }"
                            >
                                <div class="doc-perpage"
                                    @click="clickMiniImage(docIndex, pageIndex)"
                                >
                                    <img
                                        v-if="pageIndex < 3"
                                        :src="page.imagePreviewUrl"
                                    >
                                    <img
                                        v-else
                                        v-lazy.defer="page.imagePreviewUrl"
                                    >
                                    <div class="page-foot">
                                        <span>{{ pageIndex+1 }}</span>
                                        <i class="iconfont el-icon-ssq-xuanzhuan"
                                            style="margin-left: 22px; display: none;"
                                        ></i>
                                        <i class="iconfont el-icon-ssq-lajitong"
                                            style="margin-left: 6px; display: none;"
                                        ></i>
                                    </div>
                                    <div class="indicators">
                                        <div class="indicators-tag"
                                            v-for="mark in filterReceivers(marks.filter( (item, i) => item.docI == (docIndex + docListLength) && item.pageI == pageIndex && item.type != 'QR_CODE' ))"
                                            :key="mark.labelId"
                                            :style="`background-color: ${colorInfo[findReceiverIdx(mark.roleId) % 8]}`"
                                        >
                                            <div class="sanjiao"
                                                :style="`border-left: 6px solid ${colorInfo[findReceiverIdx(mark.roleId) % 8]}`"
                                            >
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</template>
<script>
import { uniqBy } from 'src/common/utils/ary.js';
import { scrollToYSmooth } from 'src/common/utils/dom.js';
import { colorInfo } from 'utils/colorInfo.js';
import { RIDESEAL_SHADOW_WIDTH } from 'utils/decorate';

export default {
    props: {
        'initialZoom': {
            type: String,
            default: '',
        },
        'docList': {
            type: Array,
            default: () => [],
        },
        'receivers': {
            type: Array,
            default: () => [],
        },
        'marks': {
            type: Array,
            default: () => [],
        },
        'scrollDocI': {
            type: Number,
            default: 0,
        },
        'scrollPageI': {
            type: Number,
            default: 0,
        },
        'title': {
            type: String,
            default: '文档',
        },
        'docListLength': {
            type: Number,
            default: 0,
        },
        ridingSealList: {
            type: Array,
            default: () => ([]),
        },
    },
    data() {
        return {
            colorInfo: colorInfo.slice(1), // 第一个颜色保留给发件方使用
            // scroll
            docI: 0,
            pageI: 0,
            docWidth: 0,
        };
    },
    computed: {
        isShowRidingSeal() {
            return this.ridingSealList.length > 0 && !this.docList.every(doc => doc.page.length === 1);
        },
        docMaxWidth() { // 存在骑缝章时，拓宽界面最大宽度用以放置骑缝章div， RIDESEAL_SHADOW_WIDTH
            return this.isShowRidingSeal ? this.docList.maxWidth + RIDESEAL_SHADOW_WIDTH : this.docList.maxWidth;
        },
        zoom() {
            if (this.initialZoom === '适合宽度') {
                return ((this.docWidth - 100) / this.docMaxWidth);
            } else {
                return this.initialZoom;
            }
        },
    },
    watch: {
        scrollDocI(v) {
            this.docI = v;
        },
        scrollPageI(v) {
            this.pageI = v;
        },
    },
    methods: {
        findReceiverIdx(id) {
            return this.receivers.findIndex(item => ('' + item.roleId) === ('' + id));
        },
        filterReceivers(marks) {
            return uniqBy(marks || [], 'roleId').res;
        },

        clickMiniImage(docIndex, pageIndex) {
            const $doc = document.querySelector('.FieldDoc');
            const docHeight = this.docList[docIndex].y;
            const pageHeight = this.docList[docIndex].page[pageIndex].y;
            const targetY = (docHeight + pageHeight) * (this.zoom * 1.001); // zoom 存在误差，需要乘以一个放大系数
            scrollToYSmooth($doc, targetY, 400, 'ease-out');
        },
        clickDocTitle(e) { // 需要触发下图片请求
            const doc = e.target.parentNode;
            const miniImgDisplay = doc.lastChild.style.display;
            const miniContainer = document.querySelector('.FieldMiniDoc');
            if (miniImgDisplay === 'none') {
                this.toggleDocShow(doc, 'block');
            } else if (!miniImgDisplay || miniImgDisplay === 'block') {
                this.toggleDocShow(doc, 'none');
            }
            miniContainer.scrollTop++;
            miniContainer.scrollTop--;
        },
        toggleDocShow(doc, display) { // 文档显示隐藏切换
            const documentId = doc.attributes['data-docId'].value;
            const nextList = document.querySelectorAll(`.doc.no-bottom[data-docId="${documentId}"]`);
            nextList.forEach(item => {
                item.style.display = display;
                for (let i = 0; i < item.children.length; i++) {
                    item.children[i].style.display = display;
                }
            });
            for (let i = 1; i < doc.children.length; i++) {
                doc.children[i].style.display = display;
            }
        },
    },
    mounted() {
        this.docWidth = document.querySelector('.documents-content')
            .getBoundingClientRect()
            .width;
    },
};
</script>
<style lang="scss">
	.temp-field-minidoc-cpn {
		border-left: 1px solid $border-color;
		overflow-y: auto;
		height: 100%;

		// 缩略图区
		.title {
			height: 38px;
			line-height: 38px;
			font-size: 14px;
			font-weight: bold;
			padding-left: 20px;
			padding-right: 15px;
			span {
				float: right;
				font-size: 12px;
				color: #3B86D4;
				&:hover {
					cursor: pointer;
				}
			}
		}

		.doc-edit-view {
			border-bottom: 1px solid $border-color;
			.doc {
                border-top: 1px solid $border-color;
                .text-blue{
                    color: #108EE8;
                }
                &.no-bottom{
                    border-top:0;
                    .doc-pages{
                        padding-top:0;
                        li .page-foot{
                            border-left: 18px solid #D8D8D8;
                            border-bottom: 18px solid #D8D8D8;
                        }
                    }
                }
			}
			.doc-title {
				position: relative;
				height: 18px;
				line-height: 18px;
				color: #333;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
				cursor: pointer;
				padding: 6px 40px 6px 39px;
				// border-top: 1px solid $border-color;
				&:hover {
					color: #3B86D4;
				}
                &.doc-title-attachment{
                    color: #99999A;
                    &:after {
                        display: none;
                    }
                    &:hover {
                        color: #99999A;
                    }
                }
				&:after {
					position: absolute;
					top: 9px;
					right: 24px;
					content: "";
					border-left: 1px solid #333;
					border-top: 1px solid #333;
					padding: 3px;
					display: inline-block;
					transform: rotate(225deg);
				}
			}
			.doc-totalPage {
				height: 18px;
				line-height: 18px;
				color: #333333;
				padding-left: 39px;
                &.doc-totalPage-attachment{
                    color: #999999;
                    .el-icon-ssq-fujian1{
                        padding-right: 2px;
                    }
                }
			}
			.doc-pages {
				padding-top: 13px;
				padding-left: 39px;
				li {
					width: 130px;
					margin-bottom: 14px;
					border: 1px solid #C5C5C5;
					&.current {
						border: 1px solid #0072CF;
					}
					.doc-perpage {
                        position: relative;
                        width: 130px;
                        height: 184px;
                        cursor: pointer;
                        img {
                            position: absolute;
                            max-width: 100%;
                            max-height: 100%;
                            top: 0;
                            right: 0;
                            bottom: 0;
                            left: 0;
                            margin: auto;
                            display: block;
                        }
					}
					.page-foot {
                        position: absolute;
                        bottom: 0;
                        left: 15px;
                        width: 0;
                        height: 0;
                        border-top: 18px solid transparent;
                        border-left: 18px solid #999;
                        border-right: 18px solid transparent;
                        border-bottom: 18px solid #999;
                        margin-left: -15px;
                        span {
                            position: absolute;
                            bottom: -14px;
                            left: -17px;
                            width: 15px;
                            height: 15px;
                            text-align: center;
                            color: #fff;
                            font-size: 11px;
                        }
						i {
							font-size: 10px;
							color: #666;
							cursor: pointer;
						}
					}
					.indicators {
						position: absolute;
						top: 0;
						left: -8px;
						width: 50px;
						height: 100%;
						margin-top: 5px;
						overflow: hidden;
						pointer-events: none;
						.indicators-tag {
							position: relative;
							width: 20px;
							height: 12px;
							background-color: transparent;
							margin-bottom: 3px;
							.sanjiao {
								position: absolute;
								top: 0px;
								left: 35px;
								width: 0;
								height: 0;
								border-top: 6px solid transparent;
								border-left: 6px solid transparent;
								border-right: 6px solid transparent;
								border-bottom: 6px solid transparent;
								margin-left: -15px;
							}
						}
					}
				}
			}
		}
	}
</style>
