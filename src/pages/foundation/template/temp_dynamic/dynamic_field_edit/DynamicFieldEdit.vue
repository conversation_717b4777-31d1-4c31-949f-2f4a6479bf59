<template>
    <transition name="slider">
        <div class="temp-field-edit-cpn dynamic-template"
            :class="focus.mark.type || ''"
            v-if="focus.markIndex > -1"
        >

            <div class="type"><i class="type-icon el-icon-ssq-wenben"></i>{{ editTypeName }}</div>
            <!-- 更改签约方下拉框 -->
            <template v-if="floatTypes.includes(focus.mark.type)">
                <div class="receiver">
                    <div class="title">接收方</div>
                    <span class="signers-opt-circle"
                        :style="computeStyle(focus.signerIndex)"
                    >
                    </span>
                    <el-select class="signers-opt"
                        size="small"
                        v-model="focus.signerIndex"
                        @change="changeSigner"
                        @visible-change="onSignerChangeVisible"
                        popper-class="sign-el-select FieldEdit-signers-selct"
                    >
                        <el-option
                            v-for="(item, index) in receivers"
                            :disabled="focus.mark.type == 'SEAL' && item.userType == 'PERSON' || focus.mark.type == 'SIGNATURE' && item.userType == 'ENTERPRISE'"
                            :key="item.receiverId"
                            :label="item.showName"
                            :value="index"
                        >
                            <span class="signers-name"
                                :style="computeStyle(index)"
                            >
                            </span>
                            <span class="signers-cirle">
                                {{ item.showName }}
                            </span>
                        </el-option>
                    </el-select>
                </div>
            </template>
            <template v-else>
                <CustomFieldEditor class="receiver"
                    :termTypeList="termTypeList"
                    :receivers="receivers"
                    :editorMetaData="editorMetaData"
                    @change-value="onMarkValueChange"
                >
                </CustomFieldEditor>
                <p class="business_field_info" v-if="focus.mark.type !== 'TERM'">
                    <b>请注意：</b>
                    <template v-if="focus.mark.type !== 'DYNAMIC_TABLE'">
                        <br>1）如果模板中多个位置添加了同一个业务字段，填写人只需填写一次，并保存为同一个值；
                        <br>2）属性设置会同步更新当前模版内所有同名业务字段
                    </template>
                    <template v-else>
                        <br>1）动态表格不允许重名；
                        <br>2）当前编辑器内动态表格为占位符，使用模版时按真实行列数展示
                    </template>
                </p>
            </template>
        </div>
    </transition>
</template>
<script>
import { rgbColorInfo } from 'utils/colorInfo.js';
import { markInfo, FLOAT_TYPES } from 'src/pages/foundation/sign/common/info/info.js';
import CustomFieldEditor from '../custom_field_editor/CustomFieldEditor.vue';
import { mapGetters } from 'vuex';
export default {
    // 这里有个和文档不符的地方：作为props的edit.signerIndex，通过v-model改变值：
    // 首先没有警告，其次不仅子组件的edit.signerIndex发生了改变，父组件的edit.signerIndex也发生了改变，即双向绑定发生在了父子组件之间
    // 虽然文档说明：“所有的 prop 都使得其父子 prop 之间形成了一个单向下行绑定：父级 prop 的更新会向下流动到子组件中，但是反过来则不行。这样会防止从子组件意外改变父级组件的状态，从而导致你的应用的数据流向难以理解。”
    // 但是：https://cn.vuejs.org/v2/guide/components-props.html#%E5%8D%95%E5%90%91%E6%95%B0%E6%8D%AE%E6%B5%81
    components: {
        CustomFieldEditor,
    },
    // eslint-disable-next-line vue/require-prop-types
    props: ['receivers', 'focus', 'termTypeList'],
    data() {
        return {
            floatTypes: FLOAT_TYPES,
            rgbColorInfo: rgbColorInfo.slice(1), // 第一个默认颜色保留给发件方使用
            allowChangeSigner: false,
            editorMetaData: {},
        };
    },
    computed: {
        ...mapGetters(['getUserPermissons']),
        // 编辑区
        editTypeName() {
            return this.focus.mark ? markInfo(this.focus.mark.type).name : '';
        },
    },
    watch: {
        'focus.mark': function(v) {
            if (v) {
                this.editorMetaData = {
                    type: v.type,
                    name: v.name,
                    receiverFill: v.receiverFill,
                    fontSize: v.fontSize,
                    necessary: v.necessary,
                    fieldType: v.fieldType,
                    refBizFieldId: v.refBizFieldId,
                    roleId: v.roleId,
                    row: v.row,
                    column: v.column,
                    wordNum: v.wordNum,
                    termTypeId: v.termTypeId,
                };
            }
        },
    },
    methods: {
        computeStyle(i) {
            return `background-color: rgba(${this.rgbColorInfo[i % 8]}, 0.6);
                        border: 1px solid rgb(${this.rgbColorInfo[i % 8]})`;
        },
        changeSigner(i) {
            if (i < 0) { // element ui bug fix
                return;
            }
            if (!this.allowChangeSigner) { // element ui bug fix
                return;
            }
            this.$emit('change-signer', i);
        },
        onSignerChangeVisible(isShow) { // element ui bug fix
            this.allowChangeSigner = isShow;
        },
        // 文本
        onMarkValueChange(res) {
            const i = this.focus.markIndex;
            const mark = this.focus.mark;
            const metaData = {
                labelId: mark.labelId,
                ...res,
            };

            if (!mark.labelId) {
                return;
            }

            this.$emit('save-meta', i, metaData);
        },
    },
};
</script>
<style lang="scss">
$site-bgclr: #c9e7ff;
// 编辑区
// add class dynamic-template，和普通模板区分，避免样式互相影响
.temp-field-edit-cpn.dynamic-template {
    position: relative;
    background-color: #fff;
    border-left: 1px solid $border-color;
    overflow-y: auto;

    .disableMask{
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.5);
        z-index: 5;
        cursor: not-allowed;
    }
    .type {
        height: 40px;
        line-height: 40px;
        padding-left: 22px;
        font-size: 14px;
        color: #333;
        font-weight: bold;
        border-bottom: 1px solid $border-color;
        .type-icon {
            font-size: 16px;
            margin-right: 6px;
        }
    }
    .receiver {
        position: relative;
        padding: 10px 14px;
        border-bottom: 1px solid $border-color;
        .title {
            font-size: 12px;
            color: #333;
            padding-bottom: 10px;
            strong {
                color: #fc4343;
            }
        }
        .el-input__inner {
            padding-left: 36px;
            border-radius: 1px;
            height: 26px;
            font-size: 12px;
        }
        .el-input__icon {
            color: #333;
        }
        .signers-opt-circle {
            z-index: 99;
            position: absolute;
            top: 42px;
            left: 34px;
            width: 14px;
            height: 14px;
            background-color: $site-bgclr;
            // border: 1px solid #82C9FC;
            border-radius: 50%;
        }
    }

    &.TEXT , &.BIZ_DATE , &.TEXT_NUMERIC, &.DYNAMIC_TABLE, &.TERM {
        .receiver {
            .el-input__inner {
                padding-left: 10px;
            }
        }
    }
    // 字段使用提示
    .business_field_info {
        padding: 10px;
        font-size: 12px;
    }
}
.FieldEdit-signers-selct {
    .signers-name {
        display: inline-block;
        position: relative;
        top: 0;
        width: 16px;
        height: 16px;
        border-radius: 50%;
    }
    .signers-cirle {
        display: inline-block;
        position: relative;
        top: -4px;
        margin-left: 8px;
    }
}
// 动画
.slider-enter-active{
    transition: all .3s ease;
}
.slider-leave-active {
    transition: all .3s ease;
}
.slider-enter, .slider-leave-to /* .fade-leave-active in below version 2.1.8 */ {
    transform: translateX(210px);
}
</style>
