<template>
    <div class="temp-dynamic-field" v-loading.fullscreen.lock="loading">
        <!-- 签约方和字段区域 -->
        <div class="site-content clear">

            <!-- 选择签约方 & 字段仓库 -->
            <FieldSite class="FieldSite fl"
                :receivers="receivers"
                :termTypeList="termTypeList"
                @switch-receiver="onSwitchReceiver"
                @field-searched="reinitDragStatEvent"
                @site-drag-start="onDragStart"
            >
            </FieldSite>

            <!-- 编辑器 -->
            <DynamicDoc
                ref="dynamicDoc"
                :loading="loading"
                :templateId="templateId"
                :docList="newDocList"
                :docPageIndex="currentDocPageIndex"
                :currentDocIndex="currentDocIndex"
                :marks="marks"
                :termTypeList="termTypeList"
                :receivers="receivers"
                :receiverIndex="receiverIndex"
                :colorIndex="colorIndex"
                :iconDragStatus="dragStatus"
                :float="float"
                :focus="focus"
                @update-doc-index="handleDocSwitch"
                @mark-focus="onMarkFocus"
                @mark-blur="onMarkBlur"
                @mark-update="onMarkUpdate"
                @field-drag-end="onDragEnd"
                @hide-float="() => { this.float.show = false }"
            ></DynamicDoc>

            <!-- 缩略图区 -->
            <section class="FieldMiniDoc">
                <DynamicFieldMiniDoc
                    :docList="docList"
                    :currentDocIndex="currentDocIndex"
                    @page-switch="handlePageSwitch"
                    @doc-switch="handleDocSwitch"
                >
                </DynamicFieldMiniDoc>
            </section>

            <!-- 编辑区 -->
            <DynamicFieldEdit class="dynamic-field-edit"
                :receivers="receivers"
                :focus="focus"
                :termTypeList="termTypeList"
                @change-signer="onSignerChange"
                @save-meta="onMetaSave"
            >
            </DynamicFieldEdit>
        </div>

        <!-- 鼠标跟随icon -->
        <div id="flying-icon"
            class="flying-icon"
            v-show="float.show"
            :class="float.class"
        >
        </div>
    </div>
</template>

<script>
import FieldSite from '../temp_dynamic_site/TempDynamicSite.vue';
import DynamicFieldMiniDoc from '../dynamic_field_minidoc/DynamicFieldMiniDoc.vue';
import DynamicFieldEdit from '../dynamic_field_edit/DynamicFieldEdit.vue';
import DynamicDoc from '../temp_dynamic_doc/TempDynamicDoc.vue';
import { markInfo, FLOAT_TYPES } from 'src/pages/foundation/sign/common/info/info.js';

export default {
    components: {
        FieldSite,
        DynamicFieldMiniDoc,
        DynamicFieldEdit,
        DynamicDoc,
    },
    data() {
        return {
            // 模版Id
            templateId: this.$route.query.templateId,
            operateMarkId: this.$route.query.operateMarkId,
            // 页面loading
            loading: 1,

            receiverIndex: 0,
            colorIndex: 0,

            // flyingIcon
            float: {
                show: false,
                type: '',
                class: '',
                // ========
                name: '',
                receiverFill: false,
                fontSize: 14,
                necessary: true,
            },
            dragStatus: 'end',

            // focus
            focus: {
                markIndex: -1,
                signerIndex: 0,
                mark: {},
            },

            // ajax
            receivers: [],
            docList: [],
            accDocList: [], // 附件相关
            newDocList: [],
            termTypeList: [], // 条款类型列表

            marks: [],
            accMarks: [],

            inAnalyze: false,
            currentDocPageIndex: 1,
            currentDocIndex: 0,
        };
    },
    computed: {
    },
    methods: {
        // ajax   获取文档信息，是否可以上传附件
        getOrder() {
            return this.$http.get(`${tempPath}/templates/${this.templateId}`);
        },
        followCursorWithIcon(e) {
            document.querySelector('#flying-icon').style.left = `${e.clientX + 1}px`;
            document.querySelector('#flying-icon').style.top = `${e.clientY + 1}px`;
        },
        onSwitchReceiver(res) {
            this.receiverIndex = res.receiverIndex;
            this.colorIndex = res.colorIndex;
        },
        handlePageSwitch(pageIndex) {
            this.currentDocPageIndex = pageIndex;
        },
        handleDocSwitch(index) {
            this.currentDocIndex = index;
        },
        reinitDragStatEvent() {
            this.$refs.dynamicDoc.initFieldDragEvent();
        },
        /*
         * @desc 添加字段起始事件，监听字段点击与字段拖动
         * @param {Event, String, String, String, Boolean}
         * isDragEvent 标记是点击事件还是拖拽事件
         */
        onDragStart(e, type, className, name, isDragEvent, receiverFill = false, fontSize = 14, necessary = true, labelMetaId = '0') {
            setTimeout(() => {
                if (!isDragEvent) {
                    this.dragStatus = 'started';
                    this.followCursorWithIcon(e);
                    this.float.show = true;
                } else {
                    this.onDragEnd();
                }
                this.float.type = type;
                this.float.class = className;
                this.float.name = name;
                this.float.refBizFieldId = labelMetaId;
                if (!FLOAT_TYPES.includes(type)) {
                    this.float.receiverFill = receiverFill;
                    this.float.fontSize = fontSize;
                    this.float.necessary = necessary;
                }
            }, 200);
        },
        onDragMove(e) {
            if (this.dragStatus === 'started') {
                // IFRAME 为编辑器外部边框范围，类名float-item标记浮动标签
                if (e.target.tagName === 'IFRAME' || e.target.closest('.float-item')) { // 编辑器内部移动时，隐藏图标
                    this.float.show = false;
                } else {
                    this.followCursorWithIcon(e);
                    this.float.show = true;
                }
            }
        },
        onDragEnd() {
            this.dragStatus = 'end';
            this.float.show = false;
            this.float.class = '';
        },

        updataFocusManual(i) {
            this.focus.mark = this.marks[i];
        },
        onMarkFocus(res) {
            this.focus.markIndex = res.markIndex;
            this.focus.mark = this.marks[res.markIndex];
            this.focus.signerIndex = res.signerIndex;
        },
        onMarkBlur() {
            this.focus.markIndex = -1;
            this.focus.mark = {};
            this.focus.signerIndex = -1;
        },
        onMarkUpdate(marks) {
            this.marks = marks;
        },

        // 编辑， 同一签名 改变签署人
        onSignerChange(i) {
            // 改变对应mark的roleId即可
            const mark = this.focus.mark;
            const oriRoleId = mark.roleId;
            mark.roleId = this.receivers[i].roleId;
            this.saveMark({
                labelId: mark.labelId,
                roleId: this.receivers[i].roleId,
            })
                .then(() => {
                    this.$set(this.marks, this.focus.markIndex, {
                        ...this.marks[this.focus.markIndex],
                        roleId: this.receivers[i].roleId,
                    });
                    this.$refs.dynamicDoc.updateFieldPropInEditor(this.focus.markIndex, null, mark.roleId);
                })
                .catch(() => {
                    mark.roleId = oriRoleId;
                });
        },
        // 编辑区保存标签修改
        onMetaSave(i, metaData) {
            const mark = this.marks[i];
            if (Object.keys(metaData)[1] === 'name' && (mark.type === 'DYNAMIC_TABLE' || mark.type === 'TERM')) {
                const sameNameMarks = this.marks.filter(a => (a.type === mark.type && a.name === metaData[Object.keys(metaData)[1]]));
                const maxNum = mark.name === metaData[Object.keys(metaData)[1]] ? 1 : 0;
                if (sameNameMarks.length > maxNum) {
                    this.$MessageToast.error('已存在同名字段，请修改名称'); // 表格和条款不能重名
                    return;
                }
            }
            this.saveMark({
                labelId: mark.labelId,
                ...metaData,
            })
                .then(res => {
                    const { fontSize, receiverFill, roleId, necessary, wordNum, name } = res.data[0];
                    this.marks.forEach((a, index) => { // 更新同名字段
                        if (a.name === name || a.labelId === mark.labelId) { // marks中name为修改前的数据，需要用labelId来做修正
                            this.$set(this.marks, index, {
                                ...this.marks[index],
                                fontSize,
                                receiverFill,
                                roleId,
                                necessary,
                                wordNum: wordNum || 5,
                                [Object.keys(metaData)[1]]: metaData[Object.keys(metaData)[1]],
                            });
                        }
                    });
                    this.$refs.dynamicDoc.updateFieldPropInEditor(i, Object.keys(metaData)[1]);
                    this.updataFocusManual(this.focus.markIndex);
                })
                .catch(() => {});
        },
        saveMark(data) {
            return this.postMarks(data);
        },

        // ajax
        getRecivers() {
            return this.$http.get(`${tempPath}/templates/${this.templateId}/roles?isOnlyNeedSigner=1&displayRuleName=true`); // isOnlyNeedSigner不要抄送人，displayRuleName按规则格式化接收人名称
        },

        getDocs() {
            return this.$http.get(`${tempPath}/dynamic-template/${this.templateId}/documents`); // showLabels 取标签信息
        },

        getTermTypeList() {
            return this.$http.get(`${tempPath}/templates/terms/search?termType=`);
        },

        postMarks(labelObj = {}, opts = {}) {
            return this.$http.post(
                `${tempPath}/dynamic-template/${this.templateId}/labels/create-and-modify/`,
                labelObj,
                opts,
            );
        },
        // 初始化收件人数据
        initReceiversData(data) {
            return data.map((item) => {
                // 后端做了判断，直接取值
                const labelName = item.userName;
                return {
                    ...item,
                    labelName,
                };
            });
        },
        // 初始化标签数据
        initMarksData() {
            const labels = [];
            this.newDocList.forEach((doc, docI) => {
                doc.labels.forEach((mark) => {
                    const {
                        labelId, roleId, type, x, y, name, value, width, height, wordNum,
                        imageHref, receiverFill, fontSize, necessary, refBizFieldId, column, row, termTypeId,
                    } = mark;
                    labels.push({
                        labelId,
                        roleId,
                        documentId: doc.documentId,
                        docI,
                        type, // SIGNATURE(1), DATE(2), SEAL(3),
                        x,
                        y,
                        name,
                        value,
                        width: width > 1 ? width : markInfo(type).width,
                        height: height > 1 ? height : markInfo(type).height,
                        imageHref,
                        // 文本专属
                        receiverFill,
                        fontSize,
                        necessary,
                        refBizFieldId,
                        // 动态表格行列
                        column,
                        row,
                        // 签署人字段内容字数
                        wordNum,
                        // 条款类别id
                        termTypeId,
                    });
                });
            });
            return labels;
        },
    },
    beforeMount() {
        document.addEventListener('mousemove', this.onDragMove);
        document.addEventListener('mouseup', this.onDragEnd);
    },
    beforeDestroy() {
        document.removeEventListener('mousemove', this.onDragMove);
        document.removeEventListener('mouseup', this.onDragEnd);
    },
    created() {
        // 获取条款类型数据
        this.getTermTypeList().then(res => {
            this.termTypeList = res.data || [];
        });
        // 获取所有文件和收件人信息并展示
        Promise.all([
            this.getRecivers(),
            this.getDocs(),
        ]).then(res => {
            const res0Data = res[0].data;
            const res1Data = res[1].data;

            // 初始化receivers数据
            this.receivers = this.initReceiversData(res0Data);
            // 同步给TempField父组件，目前是为了给userGuide组件提供userType用
            this.$emit('init-receivers-data', this.receivers);
            this.newDocList = this.docList = res1Data;
            this.marks = this.initMarksData(this.newDocList);
            // 初始化marks数据
            this.$http.get(`${tempPath}${this.docList[0].htmlUrl}`)
                .then((res) => {
                    this.$nextTick().then(() => {
                        this.loading = 0;
                        this.$refs.dynamicDoc.setDataAndInitField(res.data);
                    });
                }).catch(() => {
                    this.loading = 0;
                });
        })
            .catch(() => {
                this.loading = 0;
                this.$refs.dynamicDoc.initCKEditor();
            });
    },
};
</script>

<style lang="scss">
.temp-dynamic-field {
    position: absolute;
    top: 50px;
    bottom: 35px;
    width: 100%;
    overflow: hidden;
    .scale {
        z-index: 100;
        position: fixed;
        top: 50px;
        width: 100%;
        height: 40px;
        line-height: 40px;
        text-align: center;
        background-color: #f6f6f6;
        padding-right: 210px;
        border-bottom: 1px solid $border-color;
        .scale-opt {
            width: 100px;

            .el-input__icon { // el
                color: #333;
            }
            input { // el
                font-size: 12px;
                background-color: #f6f6f6;
                border: 0;
                border-radius: 0;
            }
        }

        .analyzeTip{
            position: absolute;
            top: 0;
            left: 20px;
            font-size: 12px;
            color: #43adfd;

            .el-icon-loading{
                margin-right: 5px;
                font-size: 20px;
                color: #0092ff;
            }

            i, span{
                vertical-align: middle;
            }
        }
    }

    .site-content {
        height: 100%;
        .FieldSite {
            position: relative;
            width: 210px;
            height: 100%;
            border-right: 1px solid $border-color;
        }
        .FieldMiniDoc {
            position: absolute;
            top: 0;
            right: 0;
            width: 210px;
            height: 100%;
            border-left: 1px solid #ddd;
            overflow-y: auto;
            .temp-field-minidoc-cpn {
                border-left: none;
            }
        }
        .dynamic-field-edit {
            position: absolute;
            top: 1px;
            right: 0;
            width: 211px;
            height: 100%;
        }
    }

    .flying-icon {
        z-index: 9999;
        pointer-events: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 28px;
        height: 28px;
        line-height: 27px;
        text-align: center;
        font-size: 18px;
        background-color: #fff;
        border-radius: 4px;
        box-shadow:1px 1px 13px #ccc, -1px 1px 13px #ccc;
    }
}

.scale-opts-el-select {
    .el-select-dropdown__wrap {
        max-height: 285px;
    }
}
</style>
