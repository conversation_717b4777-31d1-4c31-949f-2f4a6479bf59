<template>
    <div class="dynamic-field-doc" ref="FieldDoc">
        <textarea v-show="!loading" name="editor" id="editor" v-model="editorContent"></textarea>
        <!-- 60 为编辑器内容顶部距父级边界的距离，标记浮动标签是否在编辑器内部-->
        <FloatFieldItem
            v-for="(mark, index) in docMarks"
            :key="index"
            v-show="(mark.y - editorScrollTop > editorOffsetTop && currentDocId === mark.documentId && isMarkSetPosFinished) || !mark.labelId"
            :mark="mark"
            :mark-index="index"
            :focusing="focus.markIndex == index"
            :editorScrollTop="editorScrollTop"
            :receivers="receivers"
            :editor="staticEditor"
            :showFloatBusinessField="index === docMarks.length - 1 && creation.status === 'ready'"
            @mark-click="onMarkClick($event, mark, index)"
            @mark-start="onMarkStart($event, mark, index)"
            @mark-move="onMarkMove($event, mark, index)"
            @mark-end="onMarkEnd($event, mark, index)"
            @mark-delete="onFloatMarkDelete($event, index)"
            @stop-float="onStopFloat"
        >
        </FloatFieldItem>

        <!-- 动态模版高亮提示弹窗 -->
        <el-dialog
            title="提醒"
            v-if="!loading"
            :visible.sync="noticeDlgVisible"
            custom-class="dynamic-notice-dialog"
            size="tiny"
            top="35%"
            :show-close="false"
        >
            <div>
                系统为模板添加的高亮颜色用于识别模板中的业务字段，仅为查阅方便，不会体现在最终发送的合同中。
            </div>
            <span slot="footer" class="dialog-footer">
                <el-checkbox v-model="neverTipAgain">不再提醒</el-checkbox>
                <el-button type="primary" @click="onUseImmediate">立即使用</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { getStyle, getElemOffset, scrollToYSmooth } from 'src/common/utils/dom.js';
import { throttle } from 'src/common/utils/fn.js';

import { markInfo, A4_PAGE_WIDTH, A4_PAGE_HEIGHT, newMarkSizeInfo, FLOAT_TYPES } from 'src/pages/foundation/sign/common/info/info.js';
import { rgbColorInfo } from 'utils/colorInfo.js';
import toolbar from './toolbarConfig';
import FloatFieldItem from '../dynamic_float_field/FloatFieldItem.vue';
import { ORIGIN_TEMPLATE_PIC_WIDTH } from '../../../sign/common/info/info';

const COMMON_BODY_MARGIN = 8;
const HEAD_HEIGHT = 50; // 头部高度
const FOOT_HEIGHT = 35; // 头部加底部高度
const EDITOR_OFFSET_TOP = 41 + 20; // 编辑器头部 工具条41px，间隔20px
// const EDITOR_PADDING_TOP = 50; // 编辑器头部padding
// const EDITOR_PADDING_BOTTOM = 150; // 编辑器padding-bottom
// const EDITOR_PADDING_LR = 145; // 编辑器padding 左右边距
const DEFAULT_WORDNUM = 5; // 默认字数长度

export default {
    components: {
        FloatFieldItem,
    },
    // eslint-disable-next-line vue/require-prop-types
    props: ['templateStatus', 'templateId', 'docList', 'receivers', 'receiverIndex', 'marks', 'loading', 'iconDragStatus', 'float', 'focus', 'docPageIndex', 'currentDocIndex', 'termTypeList'],
    data() {
        return {
            editorOffsetTop: EDITOR_OFFSET_TOP,
            noticeDlgVisible: false, // 高亮提示弹窗可见性
            neverTipAgain: false, // 不再提醒
            isMarkSetPosFinished: false, // 标签位置设置是否完成
            rgbColorInfo: rgbColorInfo.slice(1),
            editor: null,
            staticEditor: CKEDITOR,
            editorContent: '',
            creation: {
                docI: 0,
                status: 'pedding',
            },
            docMarks: [],

            iframeOffsetLeft: 0,
            iframeOffsetTop: 0,
            iframeClientHeight: 0,
            iframeClientWidth: 0,
            editorScrollTop: 0,
            // contentMarginTop: 0,
        };
    },
    computed: {
        ...mapGetters([
            'getUserId',
        ]),
        currentDocId() {
            return this.docList[this.currentDocIndex].documentId;
        },
    },
    watch: {
        marks: { // 绑定数据
            handler: function(v) {
                this.docMarks = v;
            },
            deep: true,
        },
        docPageIndex(v) { // 页码切换，滚动条滚动
            const editorDocument = CKEDITOR.instances.editor.document;
            const maxScrollHeight = editorDocument.$.body.clientHeight - this.iframeClientHeight;
            const SCROLL_PER_PAGE_HEIGHT = Math.floor(maxScrollHeight / this.docList[this.currentDocIndex].pageSize);

            let scrollHeight = SCROLL_PER_PAGE_HEIGHT * (v - 1);
            scrollHeight === 0 && scrollHeight++; // 1 is offset to avoid change doc index
            this.docList[this.currentDocIndex].pageSize === v && scrollHeight--;

            scrollToYSmooth(editorDocument.$.scrollingElement, scrollHeight, 400, 'ease-out');
            this.editorScrollTop = scrollHeight;
        },
        currentDocIndex(v) { // 滚动条滚动时切换index，文档内容切换并保存
            this.editor.removeAllListeners();
            this.editor.destroy();
            this.editor = null;
            document.getElementById('cke_editor').remove(); // 手动移除编辑器，后续重新初始化
            this.$http.get(`${tempPath}${this.docList[v].htmlUrl}`)
                .then((res) => {
                    this.setDataAndInitField(res.data); // 编辑器及字段重新初始化
                });
        },
    },
    mounted() {
        document.addEventListener('click', this.onClickDoc);
        this.noticeDlgVisible = localStorage.getItem(`dynamicHighlightColorTip_${this.getUserId}`) !== '1';
    },
    beforeDestroy() {
        document.removeEventListener('click', this.onClickDoc);
        this.editor.removeAllListeners();
        this.editor.destroy();
        this.editor = null;
    },
    methods: {
        // 高亮弹窗确认
        onUseImmediate() {
            this.noticeDlgVisible = false;
            if (this.neverTipAgain) {
                localStorage.setItem(`dynamicHighlightColorTip_${this.getUserId}`, '1'); // 标记"不再提醒"
            }
        },
        initCKEditor() {
            const self = this;
            // 页面默认样式，去匹配线下word样式
            // CKEditor 配置项 编辑器内容padding, 字段默认样式，text-indent 去除导致布局乱掉的附加缩进。动态表格默认样式，
            CKEDITOR.addCss(`.cke_editable { font-size: 14px;}
                    .cke_editable .field-item { background-color: rgba(${rgbColorInfo[0]}, 0.75); text-align: left;
                     display: inline-block; border: 2px solid transparent; text-indent: 0;line-height: 1.5;text-decoration: inherit;}
                    .cke_editable .focus-field { border-color: #20a0ff;} .cke_editable .focus-field + .field-table { border: 2px solid #20a0ff}
                    .cke_editable .field-table .field-table-td {min-height: 30px; min-width: 60px; border-color: #333;}
                    .cke_editable > div { display: inline-block;}`,
            // to fix SAAS-7813、CFD-5157: add display inline to fix ie select and insert problem // todo add inline cause layout outcontrol
            // ie hasLayout：true 引起的虚线边框和resize，https://ckeditor.com/old/forums/CKEditor/contenteditable-and-Internet-Explorer-problem-with-hasLayout
            // 可以通过display:inline避免hasLayout值为true
            );

            this.editor = CKEDITOR.replace('editor', {
                toolbar,
                extraAllowedContent: 'h3{clear};h2{line-height};h2 h3{margin-left,margin-top}',
                extraPlugins: 'print,font,colorbutton,justify,imagepaste',
                height: window.innerHeight - HEAD_HEIGHT - FOOT_HEIGHT - EDITOR_OFFSET_TOP,
                removeDialogTabs: 'image:advanced;link:advanced',
                // 移除编辑器底部状态栏显示的元素路径和调整编辑器大小的按钮
                removePlugins: 'elementspath,resize,magicline',
                bodyId: 'tempEditor',
                disableUndo: true,
            });

            // CKEditor 初始化完成加载数据
            this.editor.on('instanceReady', self.onEditorInstanceReady);
            this.editor.on('change', self.onEditorChange); // 监听内容变化，做字段位置计算、字段删除逻辑处理
            this.editor.on('key', self.blurMark); // 编辑器内部输入文本时，移除mark焦点
            this.editor.on('paste', (e) => { // 监听的drag包括左菜单业务字段的dragstart，添加保证dragstart的数据处理完成
                setTimeout(() => {
                    self.onDragEnd(e);
                }, 0);
            });
        },

        // 编辑器实例初始化完成时，添加事件
        onEditorInstanceReady(e) {
            const self = this;
            const firstDivChildren = CKEDITOR.instances.editor.document.findOne('#tempEditor > div');
            // default margin top is negative value which may cause contract pdf top text be cut. remove it to fix CFD-2990
            // firstDivChildren && firstDivChildren.setStyle('margin-top', '0');
            const tableElements = CKEDITOR.instances.editor.document.find('table');
            tableElements.$.forEach(ckTable => {
                ckTable.classList.add('show-borders');
            });

            // 初始化时记录边距数据
            this.iframeOffsetLeft = document.getElementsByTagName('IFRAME')[0].offsetLeft;
            this.iframeOffsetTop = document.getElementsByTagName('IFRAME')[0].offsetTop;
            this.iframeClientHeight = document.getElementsByTagName('IFRAME')[0].clientHeight;
            this.iframeClientWidth = document.getElementsByTagName('IFRAME')[0].clientWidth;
            if (firstDivChildren) {
                // this.contentMarginTop = firstDivChildren.$.offsetTop;
                if (firstDivChildren.$.clientWidth + firstDivChildren.$.offsetLeft > ORIGIN_TEMPLATE_PIC_WIDTH + 1) { // 后端转换出来的样式和界面不兼容，需要处理宽度, 后端页面宽度794， + 1做兼容
                    firstDivChildren.setStyle('width', `${firstDivChildren.$.clientWidth - firstDivChildren.$.offsetLeft}px`);
                }
            }

            setTimeout(() => { // setTimeout to resolve firefox position calc uncorrect problem, todo improve
                self.initMark(); // 初始化计算编辑器内字段位置和点击事件
            }, 180);

            CKEDITOR.instances.editor.document.$.onscroll = self.onEditorScroll; // 监听编辑器滚动条滚动，用来触发文档切换
            CKEDITOR.instances.editor.document.$.body.onmouseout = self.onMouseout; // 移出编辑器修改插入状态
            CKEDITOR.instances.editor.document.$.body.onclick = self.onClickDoc; // 点击触发blur，隐藏字段编辑栏
            CKEDITOR.instances.editor.document.$.body.onmousemove = self.onMousemove;
            CKEDITOR.instances.editor.document.$.body.onmouseup = self.onMouseup;

            self.initFieldDragEvent(); // 初始化左侧边栏字段拖拽插入事件监听
        },

        // ====================  编辑器事件监听 ====================
        onEditorScroll() {
            const self = this;
            if (self.creation.status == 'ready') { // 滚动时去除插入状态
                self.docMarks.splice(self.docMarks.length - 1, 1);
                self.creation.status = 'pedding';
                self.$emit('field-drag-end');
            }

            const scrollHeight = CKEDITOR.instances.editor.document.$.body.clientHeight - self.iframeClientHeight;
            const scrollEle = CKEDITOR.instances.editor.document.$.scrollingElement || CKEDITOR.instances.editor.document.$.documentElement; // ckedtior api：获取滚动元素
            self.editorScrollTop = scrollEle.scrollTop;
            if (scrollEle.scrollTop <= 0 && self.currentDocIndex !== 0) {
                this.saveDocument();
                self.$emit('update-doc-index', self.currentDocIndex - 1);
            }
            if (scrollEle.scrollTop >= scrollHeight && self.currentDocIndex !== self.docList.length - 1) {
                this.saveDocument();
                self.$emit('update-doc-index', self.currentDocIndex + 1);
            }
        },
        // 编辑器内容变化：键盘事件、插入事件，重新计算浮动字段位置
        onEditorChange(e, isFloatDelete) {
            const self = this;
            setTimeout(function() { // 延时保证新插入元素可以获取到
                const copyMarks = self.docMarks.concat();
                const deletedMarksId = [];
                copyMarks.forEach((mark, i) => {
                    if (self.currentDocId !== mark.documentId) {
                        return;
                    }
                    // 浮动标签需要重新计算位置
                    if (FLOAT_TYPES.includes(mark.type) && self.editor.document.getById(mark.labelId)) {
                        const markEl = self.editor.document.getById(mark.labelId);
                        const x = getElemOffset(markEl.$).offsetLeft + self.iframeOffsetLeft + COMMON_BODY_MARGIN;
                        const y = getElemOffset(markEl.$).offsetTop + self.iframeOffsetTop - newMarkSizeInfo(mark.type).height + markEl.$.clientHeight + COMMON_BODY_MARGIN;
                        if (y < EDITOR_OFFSET_TOP) {
                            deletedMarksId.push(mark.labelId); // 记录删除元素
                            self.deleteMarks(mark.labelId); // 调接口删除标签数据
                            self.$MessageToast.error('请为标签预留足够的布局空间');
                            markEl.remove();
                        }

                        self.$set(self.docMarks, i, {
                            ...self.docMarks[i],
                            x,
                            y,
                        });
                        // 编辑器内容变化时，需要判断是否被删除
                    } else if (!isFloatDelete && !self.editor.document.getById(mark.labelId)) {
                        deletedMarksId.push(mark.labelId); // 记录删除元素
                        self.deleteMarks(mark.labelId); // 调接口删除标签数据 todo 调用批量删除接口
                    }
                });
                if (deletedMarksId.length > 0) {
                    self.docMarks = self.docMarks.filter(a => !deletedMarksId.includes(a.labelId));
                    self.saveDocument();
                    self.$emit('mark-update', self.docMarks);
                }
            }, 0);
        },
        onMouseout(e) { // 插入签章时存在浮动图标闪烁的问题，原因：移动过程中会触发mouseout事件，导致图标重新生成
            if (this.creation.status === 'ready' && (!e.target.closest || !e.target.closest('.float-item'))) {
                this.docMarks.splice(this.docMarks.length - 1, 1);
                this.creation.status = 'pedding';
            }
        },
        onMousemove: throttle(function(e) {
            const self = this;
            if (self.iconDragStatus !== 'started') {
                return;
            }
            CKEDITOR.instances.editor.focus();

            self.$emit('hide-float'); // 隐藏浮动的字段小图标

            const x = e.pageX + self.iframeOffsetLeft + 8; // margin; 8 is gap seperate cursor and float item
            const y = e.pageY + self.iframeOffsetTop - newMarkSizeInfo(self.float.type).height; // 浮动标签定位标准为左下角
            if (self.creation.status === 'ready') {
                const mark = self.docMarks[self.docMarks.length - 1];
                mark.x = x;
                mark.y = y;
            } else {
                self.docMarks.push({
                    roleId: self.receivers[self.receiverIndex].roleId,
                    type: self.float.type,
                    name: self.float.name,
                    docI: self.currentDocIndex + '',
                    documentId: self.currentDocId,
                    x,
                    y,
                    width: FLOAT_TYPES.includes(self.float.type) ? newMarkSizeInfo(self.float.type).width / A4_PAGE_WIDTH : 0,
                    height: FLOAT_TYPES.includes(self.float.type) ? newMarkSizeInfo(self.float.type).height / A4_PAGE_HEIGHT : 0,
                    // 文本专属，取meta上的值
                    receiverFill: self.float.receiverFill,
                    fontSize: self.float.fontSize,
                    necessary: self.float.necessary,
                    refBizFieldId: self.float.refBizFieldId,
                    column: 3,
                    row: 2,
                    wordNum: DEFAULT_WORDNUM,
                    termTypeId: self.float.type === 'TERM' ? self.termTypeList[0].termTypeId : '',
                });
                self.creation.status = 'ready';
            }
        }, 0),
        // mouseup 事件，从左边拖拽、点击增加标签时都会进入这里
        onMouseup(e) {
            if (this.creation.status != 'ready') {
                return;
            }
            // 停止浮动
            this.creation.status = 'done';
            this.$emit('field-drag-end');

            if (e.target.className.includes('field-item')) { // 如果点击的是字段，不会触发插入操作
                this.docMarks.splice(this.docMarks.length - 1, 1);
                return;
            }
            // 新建的标签位置在最后一位
            const i = this.docMarks.length - 1;
            const mark = this.docMarks[i];

            if (FLOAT_TYPES.includes(mark.type) && mark.y < EDITOR_OFFSET_TOP) { // 距离上边界间距不足以摆放签章时，进行提示
                this.$MessageToast.error('请为标签预留足够的布局空间');
                this.docMarks.splice(i, 1);
                this.$emit('mark-update', this.docMarks);
                return;
            }

            const postData = {
                labelId: '', // 新建标签，labelId为空
                templateId: this.templateId,
                documentId: this.currentDocId,
            };

            this.saveMark({
                ...postData,
                ...mark,
                x: 0, // 后端需要校验，不能直接传值
                y: 0, // 后端需要校验，不能直接传值
                name: this.formatNewMarkName(mark), // 动态表格名称需要做拼接
            }).then(res => {
                this.updateMarks(i, res);
            }).catch((err) => {
                this.docMarks.splice(i, 1);
            });
        },
        onDragEnd(evt) {
            // 不能直接用this.float, float内数据未更新，是旧数据
            const field = evt.data.dataTransfer.getData('field');

            if (!field) {
                return;
            }

            if (typeof field === 'string') { // 如果是拖拽签章的话
                this.onMarkMoveEnd(field); // 这里拖拽签章时实际传值是mark索引
                return;
            }

            this.removeFocusClass();
            const i = this.docMarks.length;
            const mark = {
                labelId: '', // 新建标签，labelId为空
                roleId: this.receivers[this.receiverIndex].roleId,
                type: field.type,
                name: field.name,
                docI: this.currentDocIndex,
                // x,
                // y,
                // create时宽高取默认，其他操作取当前mark宽高
                // 文本专属，取meta上的值
                receiverFill: field.receiverFill,
                fontSize: field.fontSize,
                necessary: field.necessary,
                refBizFieldId: field.refBizFieldId,
                templateId: this.templateId,
                documentId: this.currentDocId,
                wordNum: DEFAULT_WORDNUM,
                column: 3,
                row: 2,
                termTypeId: field.type === 'TERM' ? this.termTypeList[0].termTypeId : '',
                width: FLOAT_TYPES.includes(field.type) ? newMarkSizeInfo(field.type).width / A4_PAGE_WIDTH : 0,
                height: FLOAT_TYPES.includes(field.type) ? newMarkSizeInfo(field.type).height / A4_PAGE_HEIGHT : 0,
            };

            this.saveMark({
                ...mark,
                name: this.formatNewMarkName(mark), // 动态表格名称需要做拼接
                x: 0, // 后端需要校验，不能直接传值
                y: 0, // 后端需要校验，不能直接传值
            }).then(res => {
                const newMark = res.data[0];
                this.docMarks.push(newMark);
                this.updateMarks(i, res);
            });
        },
        // 标签点击
        onMarkClick(e, mark, index) {
            FLOAT_TYPES.includes(mark.type) && this.removeFocusClass();

            this.focusMark({
                markIndex: index,
                signerIndex: this.findReceiverIdx(mark.roleId),
            });
        },
        // 标签拖拽结束
        onMarkMoveEnd(i) {
            const mark = this.docMarks[i];
            this.editor.document.getById(mark.labelId) && this.editor.document.getById(mark.labelId).remove();

            this.insertMarkToEditor(mark); // 先插入字段元素，位置会在change时统一计算
        },
        onFloatMarkDelete(e, i) {
            const labelId = this.docMarks[i].labelId;
            this.blurMark();
            this.deleteMarks(labelId)
                .then(() => {
                    this.$MessageToast.success('删除成功');
                    this.docMarks.splice(i, 1);
                    this.$emit('mark-update', this.docMarks);
                    this.saveDocument(); // 修改内容时保存文档内容
                })
                .catch(() => {});
            this.editor.document.getById(labelId) && this.editor.document.getById(labelId).remove();
            this.onEditorChange(null, true);
        },
        onClickDoc(e) {
            // 点击浮动标签时不用失焦，temp-field-edit-cpn：属性编辑侧边栏
            const classStr = e.target.getAttribute('class');
            if (classStr === 'next' || classStr === 'back') { // 点击保存按钮时
                this.saveDocument();
            } else if (
                classStr != 'sign-content' && classStr != 'sign-content focusing' &&
                classStr != 'sign-icon' && classStr != 'focusing' && // 字段
                classStr != 'field-table-td' && // 动态表格
                classStr != 'field-item focus-field' && classStr != 'field-item' &&
                (e.target.closest && !e.target.closest('.temp-field-edit-cpn')) // 属性编辑栏，ie不兼容closest
            ) {
                this.blurMark();
            }
        },
        // 暂停字段插入
        onStopFloat() {
            this.creation.status = 'done';
            this.$emit('field-drag-end');
        },

        // ==================== 数据、Dom处理 ====================
        setDataAndInitField(data) {
            this.editorContent = data;
            this.editorScrollTop = 0; // 切换文档后要重置编辑器滚动条位置
            this.isMarkSetPosFinished = false;
            this.$emit('mark-blur'); // 文档切换时，移除聚焦隐藏属性编辑栏
            this.initCKEditor();
        },
        initFieldDragEvent() {
            const self = this;
            // 获取可拖拽对象，并添加拖拽事件监听
            const eventTarget = CKEDITOR.document.find('.drag-item').toArray();

            eventTarget.forEach(a => {
                a.on('dragstart', function(evt) {
                    CKEDITOR.plugins.clipboard.initDragDataTransfer(evt);
                    const dataTransfer = evt.data.dataTransfer;

                    dataTransfer.setData('field', self.float);
                    // You need to set some normal data types to backup values for two reasons:
                    // * In some browsers this is necessary to enable drag and drop into text in the editor.
                    // * The content may be dropped in another place than the editor
                    dataTransfer.setData('text/html', '<span></span>');
                });
            });
        },
        // 文档解析后，初始化文档内的字段
        initMark() {
            CKEDITOR.instances.editor.document.$.body.style.minHeight = `${A4_PAGE_HEIGHT - 200}px`; // 最小为单文档高度
            CKEDITOR.instances.editor.document.$.body.style.position = 'relative';
            CKEDITOR.instances.editor.focus();
            this.docMarks.forEach((mark, i) => {
                if (mark.documentId === this.currentDocId) {
                    const markElem = CKEDITOR.instances.editor.document.getById(mark.labelId);
                    if (markElem) {
                        if (mark.type === 'DYNAMIC_TABLE') {
                            markElem.setStyles({
                                display: 'block',
                            });
                            markElem.findOne('.field-table') && markElem.findOne('.field-table').setStyle('width', '100%'); // 让动态表格铺满行
                            this.addTableFocusListener(markElem, mark);
                        } else if (FLOAT_TYPES.includes(mark.type)) {
                            const x = getElemOffset(markElem.$).offsetLeft + this.iframeOffsetLeft + COMMON_BODY_MARGIN;
                            // 浮动标签定位标准为左下角
                            const y = getElemOffset(markElem.$).offsetTop + this.iframeOffsetTop - newMarkSizeInfo(mark.type).height + markElem.$.clientHeight + COMMON_BODY_MARGIN;
                            markElem.setStyles({
                                'background-color': 'transparent',
                                width: `${newMarkSizeInfo(mark.type).width}px`,
                                border: 'none',
                            });
                            this.$set(this.docMarks, i, {
                                ...this.docMarks[i],
                                x,
                                y,
                            });
                        } else {
                            markElem.setStyles({
                                'background-color': this.computeFillColor(mark),
                                fontSize: mark.receiverFill ? `${mark.fontSize}px` : 'unset',
                                width: mark.receiverFill ? `${(mark.wordNum || DEFAULT_WORDNUM) * mark.fontSize}px` : 'auto',
                                'text-decoration': 'inherit',
                            });

                            this.addFieldFocusListener(markElem, mark);
                        }
                    }
                }
            });
            this.$nextTick().then(() => {
                this.isMarkSetPosFinished = true;
            });
        },
        // 更新marks数据
        updateMarks(i, res) {
            const currentMark = res.data[res.data.length - 1]; // 新加的默认在最后一位
            // this.removeFocusClass();

            // 更新marks数据
            this.$set(this.docMarks, i, {
                ...this.docMarks[i],
                ...currentMark,
            });

            this.$nextTick().then(() => {
                currentMark.type === 'DYNAMIC_TABLE' ? this.insertTableToEditor(this.docMarks[i], i) : this.insertMarkToEditor(this.docMarks[i]); // 先插入字段元素，位置会在change时统一计算
            });

            if (res.data.length > 1) { // 存在同名字段：更新当前项同名字段数据
                res.data.forEach((updateItem, index) => {
                    if (index === res.data.length - 1) {
                        return;
                    }
                    const oldIndex = this.docMarks.findIndex(a => a.labelId === updateItem.labelId);
                    if (oldIndex >= 0) {
                        this.$set(this.docMarks, oldIndex, {
                            ...this.docMarks[oldIndex],
                            ...updateItem,
                        });
                    }
                });

                // 更新模版中的同名字段标签属性
                this.updateSameNameLabels(currentMark);
            }
            this.$emit('mark-update', this.docMarks);

            // update focus
            this.focusMark({
                markIndex: i,
                signerIndex: this.receiverIndex,
            });
        },
        updateSameNameLabels(currentMark) {
            const oldLabels = Array.from(CKEDITOR.instances.editor.document.$.body.getElementsByClassName('field-item'));
            oldLabels.forEach(label => {
                const fieldName = label.innerText.includes('@') ? label.innerText.split('@')[0].substring(2) : label.innerText.substring(2, label.innerText.length - 2);
                if (fieldName === currentMark.name) { // 匹配同名字段
                    label.style.backgroundColor = this.computeFillColor(currentMark); // background会导致id和readonly属性丢失
                    if (currentMark.receiverFill) {
                        label.style.fontSize = `${currentMark.fontSize}px`;
                        label.style.width = `${currentMark.fontSize * currentMark.wordNum}px`;
                    } else {
                        label.style.width = 'auto';
                    }
                }
            });
        },
        /*
         * @description 修改字段属性时更新编辑器内部dom元素
         * @param i: Number 字段索引，propName: String 修改的属性名， subData: 签署字段对应为new roleId, 动态表格对应表格名称
         */
        updateFieldPropInEditor(i, propName, subData) {
            new Promise((resolve, reject) => {
                const editMark = this.docMarks[i];
                const { labelId, receiverFill, name, fontSize, wordNum, type, row = 2, column = 3 } = editMark;

                const currentEditEle = this.editor.document.getById(editMark.labelId);

                if (!propName) { // 如果修改的是签署字段，更新背景色和代号
                    editMark.roleId = subData; // roleID hasn't update, need to update manual
                    // let nameSub = receiverFill ? `@${this.computeOwner(editMark, 'roleName')}` : '';
                    currentEditEle.setStyle('background-color', 'transparent');
                    currentEditEle.setText(`{#${markInfo(type).name}#}`);
                    resolve();
                    return;
                }

                if (type === 'DYNAMIC_TABLE') {
                    const tableContainer = this.editor.document.getById(`${editMark.labelId}`);
                    const innerDom = this.splicingTableContent(row, column);
                    tableContainer.setHtml(`
                            <div class="field-item focus-field" style="width: fit-content;border-bottom: none;">{#${name}#}</div>
                            <table class="field-table" border="1" cellspacing="0" cellpadding="0" style="width: 100%;background: rgba(${rgbColorInfo[0]}, 0.75)">
                            ${innerDom}
                            </table>`);
                    this.addTableFocusListener(tableContainer, editMark); // 给新插入的元素添加弹窗事件
                    resolve();
                    return;
                }

                this.docMarks.forEach((updateMark) => {
                    // 名称、类型相同即更新将DOM元素属性更新
                    if (updateMark.name === name && updateMark.type === type) {
                        const targetElem = this.editor.document.getById(updateMark.labelId);
                        // let nameSub = receiverFill ? `@${this.computeOwner(editMark, 'roleName')}` : '';
                        const fakeWordNum = +(wordNum || DEFAULT_WORDNUM);
                        switch (propName) {
                            case 'name':
                                targetElem.setStyles({
                                    'background-color': this.computeFillColor(editMark), // 当前修改名称，receiver属性会覆盖相同名称字段的属性
                                    'font-size': receiverFill ? `${fontSize}px` : 'unset',
                                });
                                if (updateMark.labelId === labelId) {
                                    targetElem.setText(`{#${name}#}`);
                                    if ((type == 'TEXT' || type == 'BIZ_DATE' || type == 'TEXT_NUMERIC') && receiverFill) {
                                        targetElem.setStyle('width', `${fakeWordNum * fontSize}px`);
                                    }
                                }
                                break;
                            case 'wordNum':
                                // targetElem.setStyle('width', `${markInfo(type).width}px`);
                                if (type == 'TEXT' || type == 'BIZ_DATE' || type == 'TEXT_NUMERIC') { // 非签署字段需要重新根据字数计算宽度
                                    targetElem.setStyle('width', `${fakeWordNum * fontSize}px`);
                                }
                                break;
                            case 'fontSize':
                                targetElem.setStyles({
                                    'font-size': `${fontSize}px`,
                                    width: `${fakeWordNum * fontSize}px`,
                                });
                                break;
                            case 'roleId':
                            case 'receiverFill':
                                targetElem.setStyle('background-color', this.computeFillColor(editMark));
                                targetElem.setText(`{#${name}#}`);
                                if (!receiverFill) {
                                    targetElem.removeStyle('font-size');
                                    targetElem.removeStyle('width');
                                } else {
                                    targetElem.setStyles({
                                        'font-size': `${fontSize}px`,
                                        width: `${fakeWordNum * fontSize}px`,
                                    });
                                }
                                break;
                            case 'termTypeId': // 条款类别变化
                                break;
                            default:
                                break;
                        }
                    }
                });
                this.onEditorChange(null, false);
                resolve();
            }).then(() => {
                this.saveDocument();
            });
        },
        // 插入动态表格
        insertTableToEditor(tableMark, i) {
            const { labelId, row = 2, column = 3, name = '动态表格' } = tableMark;

            const tableInnerHtml = this.splicingTableContent(row, column);
            const tableElemStr = `<div id="${labelId}-container" hidefocus="true">&nbsp;
                        <div contenteditable="false" autofocus id="${labelId}" class="field-table-module" hidefocus="true" style="display: block">
                            <div class="field-item focus-field" style="width: fit-content;border-bottom: none;">{#${name}#}</div>
                            <table class="field-table" border="1" cellspacing="0" cellpadding="0" style="width: 100%;background: rgba(${rgbColorInfo[0]},0.75)">
                            ${tableInnerHtml}
                            </table></div>&nbsp;
                        </div>`;

            const elem = CKEDITOR.dom.element.createFromHtml(tableElemStr);
            this.addTableFocusListener(elem.findOne('.field-table-module'), tableMark); // 给新插入的元素添加弹窗事件
            CKEDITOR.instances.editor.insertElement(elem);
            this.saveDocument();

            // update focus
            this.focusMark({
                markIndex: i,
                signerIndex: this.receiverIndex,
            });
        },
        // 向编辑器内部插入字段，浮动位置在editor的change事件里统一计算
        insertMarkToEditor(field) {
            const self = this;
            // add settimeout to avoid blurmark influence of onclickdoc
            setTimeout(() => {
                // let fieldText = field.name + (field.receiverFill && false ? `@${self.computeOwner(field, 'roleName')}` : '');
                // 编辑器插入字段，两边加空格：解决拖拽至边界不能成功插入ele问题，float-item：字段统一标记，focus-field：标记当前聚焦字段
                const fontSizeStr = field.receiverFill ? `font-size: ${field.fontSize}px;` : ''; // 签署人填写字段字号限制编辑器内部修改
                const widthStyle = field.receiverFill ? `width: ${field.wordNum * field.fontSize}px` : '';
                let eleStr = `<span id="${field.labelId}-container" style="text-decoration: inherit;">&nbsp;<span contenteditable="false"
                            id="${field.labelId}"
                            class="field-item focus-field"
                             style="background-color: ${this.computeFillColor(field)};${fontSizeStr};${widthStyle}">{#${field.name}#}</span>&nbsp;</span>`;

                if (FLOAT_TYPES.includes(field.type)) { // 浮动字段占位符
                    eleStr = `<span id="${field.labelId}-container">&nbsp;<span contenteditable="false"
                            id="${field.labelId}"
                            class="field-item focus-field"
                             style="font-size: 14px;background-color: transparent; width: ${newMarkSizeInfo(field.type).width}px;border: none;">{#${markInfo(field.type).name}#}</span>&nbsp;</span>`;
                }
                const elem = CKEDITOR.dom.element.createFromHtml(eleStr);
                self.addFieldFocusListener(elem.findOne('.field-item'), field); // 给新插入的元素添加弹窗事件,只对mark区域添加事件监听
                CKEDITOR.instances.editor.insertElement(elem);
                this.saveDocument();
            }, 10);
        },
        addTableFocusListener(el, mark) {
            const { labelId } = mark;
            const self = this;
            el.on('click', function(e) {
                self.removeFocusClass();
                CKEDITOR.instances.editor.document.getById(labelId).findOne('.field-item').addClass('focus-field');
                const i = self.docMarks.findIndex(a => a.labelId === labelId);
                self.onMarkClick(e.data.$, mark, i);
            });
        },
        // 新插入节点添加事件
        addFieldFocusListener(fieldEl, field) {
            const self = this;
            fieldEl.on('click', function(e) {
                self.removeFocusClass();
                e.data.$.target.classList.add('focus-field');
                const i = self.docMarks.findIndex(a => a.labelId === field.labelId);
                if (field.receiverFill) { // 如果是签署人填写，需要获取文档上下文的字体大小
                    const currentFontsize = parseInt(getStyle(fieldEl.$, 'font-size'));
                    self.$set(self.docMarks, i, {
                        ...self.docMarks[i],
                        fontSize: currentFontsize > 36 ? 36 : currentFontsize < 12 ? 12 : currentFontsize, // 字号最小12px，最大仅支持36px
                    });
                }
                self.onMarkClick(e.data.$, field, i);
            });
        },
        removeFocusClass() {
            const focusItem = Array.from(CKEDITOR.instances.editor.document.$.body.getElementsByClassName('focus-field'))[0];

            // 移除当前聚焦的类名标记
            if (focusItem) { // 移除字段聚焦标记类，会在新插入标签内插入标记类
                focusItem.classList.remove('focus-field');
            }
        },
        // 拼接动态表格行列html, 表格占位符默认两行三列
        splicingTableContent(row, column) {
            let tableInnerHtml = '';
            for (let i = 0; i < 2; i++) {
                tableInnerHtml += '<tr>';
                for (let j = 0; j < 3; j++) {
                    tableInnerHtml += '<td class="field-table-td" style="">&nbsp;</td>';
                }
                tableInnerHtml += '</tr>';
            }
            return tableInnerHtml;
        },
        // 处理marks
        blurMark(e) {
            if (e && e.data.keyCode === 32) { // 如果输入的是空格
                const elem = CKEDITOR.dom.element.createFromHtml('<span>&nbsp;</span>');
                CKEDITOR.instances.editor.insertElement(elem);
            }
            this.removeFocusClass();
            this.$emit('mark-blur');
        },
        focusMark(data) {
            const { markIndex, signerIndex } = data;
            this.$emit('mark-focus', { markIndex, signerIndex });
        },

        // ===================== api请求 =====================
        saveMark(data) {
            return this.$http.post(
                `${tempPath}/dynamic-template/${this.templateId}/labels/create-and-modify`,
                data,
            );
        },
        deleteMarks(labelId) {
            return this.$http.delete(`${tempPath}/dynamic-template/${this.templateId}/labels/${labelId}`);
        },
        saveDocument() {
            let bodyContent = CKEDITOR.instances.editor.getData();
            bodyContent = bodyContent.replace(/focus-field/g, ''); // 聚焦的类名不需要传到后台
            let htmlStr = `<html lang="en"><body>${bodyContent}</body></html>`;

            // 移除样式，重新拼接，避免重复添加
            if (!bodyContent.includes('* {page-break-inside: avoid;page-break-after: avoid;page-break-before: avoid;}')) {
                htmlStr = `<html lang="en"><head><meta charset="UTF-8"><style>* {page-break-inside: avoid;page-break-after: avoid;page-break-before: avoid;}.show-borders td{border: 1px solid #333}</style></head><body>${bodyContent}</body></html>`;
            }

            return this.$http.put(
                `${tempPath}/dynamic-template/${this.templateId}/document/${this.currentDocId}/save`, {
                    documentId: this.currentDocId,
                    html: htmlStr,
                },
            );
        },

        // ===================== 属性计算 ====================
        formatNewMarkName(mark) {
            if (mark.type !== 'DYNAMIC_TABLE' && mark.type !== 'TERM') {
                return mark.name;
            }
            const sameTypeMarks = this.docMarks.filter(a => a.type === mark.type && a.labelId); // 当前已存在的动态表格，无labelId的为临时数据
            let count = sameTypeMarks.length + 1;
            const nameStr = markInfo(mark.type).name;
            let sameNameTable = sameTypeMarks.filter(a => {
                return a.name === `${nameStr}${count}`;
            });
            while (sameNameTable.length > 0) {
                count++;
                sameNameTable = sameTypeMarks.filter(a => {
                    return a.name === `${nameStr}${count}`;
                });
            }

            return `${nameStr}${count}`;
        },
        findReceiverIdx(id) {
            return this.receivers.findIndex(item => item.roleId == id);
        },
        computeOwner(mark, key) {
            const receiver = this.receivers[this.findReceiverIdx(mark.roleId)] || {};
            return receiver[key] || '';
        },
        computeFillColor(mark) {
            // 签署方或者印章、签名、日期类型时，根据身份选择
            if (mark.receiverFill || mark.type === 'SEAL' || mark.type === 'SIGNATURE' || mark.type === 'DATE') {
                return `rgba(${this.rgbColorInfo[this.findReceiverIdx(mark.roleId) % 8]}, 0.75)` || 'transparent';
            } else {
                // 发件方使用固定颜色
                return `rgba(${rgbColorInfo[0]}, 0.75)`;
            }
        },
    },
};
</script>

<style lang="scss">
.dynamic-field-doc {
    height: 100%;
    min-height: 600px;
    // remove to avoid float position problem
    /*overflow: auto;*/
    margin: 0px 249px 0;
    border-top: 1px solid $border-color;
    background-color: #f6f6f6;
    #cke_editor {
        border: none;
        margin-top: 10px;
        .cke_top {
            border: none;
            /*padding-left: 147px;*/
            background: #f6f6f6;
            display: flex;
            justify-content: center;
        }
        .cke_inner {
            background: #f6f6f6;
        }
        .cke_contents {
            width: 845px;
            margin: 10px auto 0;
            /*margin: 20px 220px 0;*/
            box-shadow: 0 -2px 8px 0 rgba(177,177,177, 0.50);
        }
    }
}
.dynamic-notice-dialog {
    width: 358px;
    height: 191px;
    box-shadow: 0 0 8px 0 rgba(142,142,142,0.50);
    border-radius: 8px;
    .el-dialog__header {
        padding: 40px 33px 0;
        .el-dialog__title {
            font-size: 13px;
            font-weight: 600;
            color: #333;
        }
    }
    .el-dialog__body {
        font-size: 12px;
        padding: 10px 33px 20px;
    }
    .el-dialog__footer {
        padding-right: 33px;
        .el-button {
            height: 30px;
            line-height: 30px;
            padding: 0 10px;
            margin-left: 20px;
        }
    }
}

</style>
