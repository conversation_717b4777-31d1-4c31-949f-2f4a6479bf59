<!-- 指定位置页重构 -->
<template>
    <div class="temp-field-page" v-loading.fullscreen.lock="loading">
        <!-- 页头 -->
        <SignHeader class="header"
            :title="contractTitle"
            rText="保存"
            :rShow="true"
            @to-back="toBack"
            @to-next="toNextBefore"
        >
        </SignHeader>

        <!-- 主体 -->
        <TempDynamicBody
            v-if="!loading"
            ref="fieldDynamicBody"
            @init-receivers-data="onInitReceiversData"
        >
        </TempDynamicBody>

        <!-- 页脚 -->
        <RegisterFooter class="footer" v-if="ftVisible"></RegisterFooter>

        <!-- 新手指引 -->
        <Guide :guideUserType="guideUserType"></Guide>
    </div>
</template>

<script>
import SignHeader from '../../sign/common/signHeader/SignHeader.vue'; // 复用普通发起的header
import RegisterFooter from 'src/components/register_footer/RegisterFooter.vue';
import TempDynamicBody from '../temp_dynamic/temp_dynamic_body/TempDynamicBody.vue';
import Guide from '../temp_field/temp_field_guide/TempFeildGuide.vue';
import { goReturnUrl } from 'src/common/utils/returnUrl.js';

import { mapGetters } from 'vuex';
import store from '@/store/store';

export default {
    components: {
        SignHeader,
        RegisterFooter,
        TempDynamicBody,
        Guide,
    },
    data() {
        return {
            // 合同信息
            contractTitle: '',
            returnUrl: ['null', ''].includes(this.$route.query.returnUrl) ? null : this.$route.query.returnUrl,

            templateId: this.$route.query.templateId,
            // 页面信息
            loading: false,

            // guide
            guideUserType: 'PERSON',
        };
    },
    computed: {
        ...mapGetters(['getSsoConfig', 'checkFeat']),
        ftVisible() {
            return !this.ssoTmpEdit.tmpEdit_ft || this.ssoTmpEdit.tmpEdit_ft.visible;
        },
        // 单点登录模板编辑页配置
        ssoTmpEdit() {
            return this.getSsoConfig.tmpEdit || {};
        },
    },
    methods: {
        toBack() {
            this.$router.push(`/template/edit/prepare?templateId=${this.templateId}&dynamicMark=DYNAMIC`);
        },
        toNextBefore() {
            const h = this.$createElement;
            const style = {
                style: 'line-height: 26px; color: #666666;margin-bottom: 5px;',
            };
            const vNode = [
                h('p', style, this.$t('template.dynamicTemplateUpdate.newVersionDesc')),
                h('p', style, this.$t('template.dynamicTemplateUpdate.updateTip')),
                h('p', style, this.$t('template.dynamicTemplateUpdate.connectUs')),
            ];
            this.$msgbox({
                title: this.$t('template.dynamicTemplateUpdate.title'),
                customClass: 'dynamic-update-tip',
                message: h('div', null, vNode),
                showCancelButton: true,
                confirmBtnText: '确认',
                cancelBtnText: '取消',
            }).then(() => {
                this.toNext();
            }).catch(() => {
            });
        },
        toNext() {
            const receivers = this.$refs.fieldDynamicBody.receivers;
            const marks = this.$refs.fieldDynamicBody.marks;
            const allDone = receivers.every(receiver => {
                return marks.some(mark => {
                    return (mark.roleId === receiver.roleId) && (mark.type === 'SEAL' || mark.type === 'SIGNATURE');
                });
            });
            const someTextNoName = marks.some(item => {
                return item.type === 'TEXT' && !item.name;
            });

            if (!allDone) {
                this.$MessageToast.error('请为每个签署方指定签署位置');
                return;
            }

            if (someTextNoName) {
                this.$MessageToast.error('请为每个文本标签填写名称');
                return;
            }
            this.loading = true;
            this.putSaveTemplate()
                .then(() => {
                    // 保存成功直接去模板列表页
                    this.$MessageToast.success('保存成功').then(() => {
                        /* 如果客户定义了跳转地址，则首先跳转 */
                        if (this.returnUrl) {
                            goReturnUrl(this.returnUrl);
                            return;
                        }
                        if (this.ssoTmpEdit.tmpEdit_1_save && this.ssoTmpEdit.tmpEdit_1_save.url) {
                            this.$router.push(this.ssoTmpEdit.tmpEdit_1_save.url);
                        } else {
                            if (store.state.commonHeaderInfo.openNewContractTemplate && store.getters.checkFeat.dynamicTemplate) {
                                this.$router.push('/template/list?isDynamic=1');
                            } else {
                                this.$router.push('/template/list');
                            }
                        }
                    });
                }).finally(() => this.loading = false);
        },
        onInitReceiversData(res) { // res就是receivers数据
            this.guideUserType = res[0].userType;
        },
        // ajax
        getContractInfo() {
            return this.$http.get(`${tempPath}/templates/${this.templateId}?type=send`);
        },
        // 保存
        putSaveTemplate() {
            return this.$http.put(`${tempPath}/dynamic-template/${this.templateId}/save`);
        },
        loadJS(src, callback, isCache = false) {
            const script = document.createElement('script');
            script.type = 'text/JavaScript';
            if (isCache) {
                script.src = src + '?t=' + new Date().getTime();
            } else {
                script.src = src;
            }
            if (script.addEventListener) {
                script.addEventListener('load', callback, false);
            }
            document.head.appendChild(script);
        },
    },
    created() {
        // 判断混合云环境，动态模版不支持混合云
        if (this.$store.getters.getHybridUserType === 'hybrid') {
            this.$router.push('/account-center/home');
        }
        // 获取合同信息
        this.getContractInfo()
            .then((resp) => {
                this.contractTitle = resp.data.templateName;
            }).catch(() => {
                this.$router.push('/template/list');
            });

        this.loading = true;
        this.loadJS('/public_static/ckeditor/ckeditor.js', () => {
            this.loading = false;
        });
    },
};
</script>

<style lang="scss">
.temp-field-page {
    user-select: none;
    position: relative;
    height: 100vh;
    font-size: 12px;
    color: #333;
    background-color: #f6f6f6;
    .header {
        z-index: 100;
        position: fixed;
        top: 0;
    }
    .footer {
        position: fixed;
        bottom: 0;
    }
}
.field-error-alert {
    p {
        white-space: pre-wrap;
        word-wrap: break-word;
    }
}
.dynamic-update-tip {
    width: 500px;
}
</style>
