<template>
    <div class="cus-field-editor-cpn">
        <el-form ref="form" :model="form" @submit.native.prevent>
            <el-form-item label="名称" class="form-name">
                <el-input placeholder="必填"
                    v-model="form.name"
                    :minlength="1"
                    :maxlength="30"
                    @blur="onChange('name')"
                    @submit.native.prevent
                >
                </el-input>
            </el-form-item>

            <!-- DYNAMIC_TABLE、TERM固定为发件人填写 -->
            <el-form-item label="内容填写人" v-if="editorMetaData.type !== 'DYNAMIC_TABLE' && editorMetaData.type !== 'TERM'">
                <el-radio-group
                    v-model="form.receiverFill"
                    @change="onChange('receiverFill')"
                >
                    <el-radio :label="false">
                        发件人
                        <el-tooltip
                            class="item"
                            effect="dark"
                            content="选择“发件人”，则此合同内容在合同发出前由发件人填写"
                            placement="top"
                        >
                            <i class="el-icon-ssq-wenhao tips"></i>
                        </el-tooltip>
                    </el-radio>
                    <el-radio :label="true">
                        签署人
                        <el-tooltip
                            class="item"
                            effect="dark"
                            content="选择“签署人”，则此合同内容在签署人签署时填写"
                            placement="top"
                        >
                            <i class="el-icon-ssq-wenhao tips"></i>
                        </el-tooltip>
                    </el-radio>
                </el-radio-group>
            </el-form-item>

            <!-- 签署人多个变量时添加身份选择 -->
            <el-form-item v-if="form.receiverFill && receivers.length > 1" class="cus-field-editor-identity">
                <el-radio-group v-model="form.roleId" @change="onChange('roleId')">
                    <div v-for="item in receivers"
                        :key="item.roleId"
                    >
                        <el-radio :label="item.roleId">
                            <span>{{ item.showName }}</span>
                        </el-radio>
                    </div>
                </el-radio-group>
            </el-form-item>

            <!-- 合同条款类型选择 -->
            <el-form-item label="请选择条款类别" v-if="editorMetaData.type === 'TERM'" class="term-select-group">
                <el-radio-group v-model="form.termTypeId" @change="onChange('termTypeId')">
                    <div v-for="term in termTypeList"
                        :key="term.termTypeId"
                    >
                        <el-radio :label="term.termTypeId">
                            <span>{{ term.termTypeName }}</span>
                        </el-radio>
                    </div>
                </el-radio-group>
            </el-form-item>

            <!--动态表格行列设置-->
            <el-form-item label="动态表格设置" v-if="editorMetaData.type === 'DYNAMIC_TABLE'">
                <el-row :gutter="20" class="table-config-row">
                    <el-col :span="12">
                        <label>行数</label>
                        <el-input v-model="form.row"
                            :minlength="1"
                            :maxlength="4"
                            @blur="onChange('row')"
                        >
                        </el-input>
                    </el-col>
                    <el-col :span="12">
                        <label>列数</label>
                        <el-input v-model="form.column"
                            :minlength="1"
                            :maxlength="4"
                            @blur="onChange('column')"
                        >
                        </el-input>
                    </el-col>
                </el-row>
            </el-form-item>

            <!-- 签署人字段设定字号、字数 -->
            <template v-if="editorMetaData.receiverFill">
                <el-form-item label="字号">
                    <el-select popper-class="cus-field-editor-select"
                        v-model="form.fontSize"
                        placeholder="请选择字号"
                        @change="onChange('fontSize')"
                    >
                        <el-option
                            v-for="(item, index) in fontSizeRange"
                            :key="index"
                            :label="item.label"
                            :value="convertPtToPx(item.pt, dpi)"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item class="form-word-num">
                    <div class="lable">
                        内容字数
                        <el-tooltip
                            class="item"
                            effect="dark"
                            content="用于计算字段内容的预留宽度，不限制字段内容字数，默认为5，超出界面部分会被截断"
                            placement="top"
                        >
                            <i class="el-icon-ssq-bangzhu cursor-point"></i>
                        </el-tooltip>
                    </div>
                    <el-input placeholder="内容字数"
                        v-model="form.wordNum"
                        @blur="onChange('wordNum')"
                    >
                    </el-input>
                </el-form-item>
            </template>

            <el-form-item label="填写要求" v-if="editorMetaData.type !== 'DYNAMIC_TABLE' && editorMetaData.type !== 'TERM'">
                <el-checkbox-group
                    v-model="form.necessary"
                    @change="onChange('necessary')"
                >
                    <el-checkbox label="必填，不填不能发送或签署" name="type"></el-checkbox>
                </el-checkbox-group>
            </el-form-item>
        </el-form>
    </div>
</template>
<script>
import { convertPtToPx, fontSizeRange } from 'utils/fontSize.js';
import { DYNAMIC_TABLE_LIMIT } from 'utils/commonVar.js';
export default {
    props: {
        editorMetaData: {
            type: Object,
            default: () => ({
                type: '',
                name: '',
                receiverFill: false,
                fontSize: 14,
                necessary: true,
                fieldType: '',
                roleId: '', // 角色Id
                row: 2,
                column: 3,
                wordNum: 5,
                termTypeId: '',
            }),
        },
        receivers: {
            type: Array,
            default: () => [],
        },
        termTypeList: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            form: this.editorMetaData,
            cacheOldData: {},
            dpi: 96, // 目前写死
            convertPtToPx: convertPtToPx,
            fontSizeRange: fontSizeRange,
        };
    },
    watch: {
        editorMetaData(v) {
            this.cacheOldData = JSON.parse(JSON.stringify(v)); // 缓存form数据
            this.form = v;
            if (v.type === 'TERM' && !v.termTypeId) {
                this.form.termTypeId = (this.termTypeList && this.termTypeList[0].termTypeId) || '';
            }
        },
    },
    methods: {
        onChange(k) {
            if (k === 'name' && this.form[k].trim().length === 0) {
                this.$MessageToast.error('请输入名称');
                return;
            }

            if (k === 'row' || k === 'column') { // 行列数限制校验
                const maxLen = DYNAMIC_TABLE_LIMIT[k]; // 拓展最大列数是16
                if (!this.form[k] || this.form[k] < 1 || this.form[k] > maxLen || isNaN(parseInt(this.form[k]))) {
                    this.$MessageToast.error(`请填写正确的动态表格${k === 'column' ? '列' : '行'}数，范围1～${maxLen}！`);
                    return;
                }
                this.form[k] = parseInt(this.form[k]);
            }

            if (k === 'wordNum' && this.form[k] < 1) {
                this.$MessageToast.error('请填写字段内容字数！');
                return;
            }

            if (this.cacheOldData[k] === this.form[k]) { // 利用缓存数据，在名称未发生变化时，直接返回，不触发数据更新
                return;
            }

            const data = {};
            data[k] = this.form[k];
            this.$emit('change-value', data);
        },

        clickConfirmBtn() {
            console.log(this.form);
            this.$emit('confirm', this.form);
        },

        clickCancelBtn() {
            this.$emit('cancel');
        },
    },
};
</script>
<style lang="scss">
.cus-field-editor-cpn {
    label {
        width: 100%;
        height: 20px;
        font-size: 14px;
        color: #333;
        text-align: left;
        padding-top: 0;
        padding-bottom: 0;
    }
    .form-name .el-form-item__label::after {
        content: "*";
        color: #f86b26;
        margin-left: 4px;
    }
    .el-form-item__content .table-config-row {
        .el-col {
            padding-top: 10px;
            line-height: 26px;
            label {
                font-size: 12px;
            }
        }
    }
    .el-form-item__content .el-input {
        padding-top: 0;
        .el-input__inner {
            height: 28px;
            line-height: 28px;
            font-size: 12px;
        }
        .el-input__icon {
            color: #333;
        }
    }
    .el-radio {
        width: 72px;
        .el-radio__label {
            padding-left: 2px;
        }
    }
    .btns {
        .ssq-btn-confirm, .ssq-btn-cancel {
            float: right;
            line-height: 34px;
        }
        .ssq-btn-confirm {
            margin-right: 8px;
        }
    }
    .tips {
        margin-left: 1px;
        font-size: 12px;
        color: #666;
        cursor: pointer;
    }
    .cus-field-editor-identity  {
        margin-top: -16px;
        .el-radio-group {
            .el-radio {
                width: 100%;
                overflow: hidden;
                height: auto;
                margin-bottom: 3px;
                .el-radio__input {
                    width: 20px;
                    float: left;
                }
                .el-radio__label {
                    display: block;
                    margin-left: 20px;
                    white-space: normal;
                    line-height: 16px;
                }
            }
        }
    }
    .term-select-group {
        .el-radio-group {
            padding-top: 3px;
            .el-radio {
                padding: 4px 0;
                .el-radio__label {
                    margin-left: 4px;
                }
            }

        }
    }
}

.cus-field-editor-select {
    li {
        height: 28px;
        line-height: 28px;
        padding: 0 10px;
    }
}
.cus-field-editor-role {
    margin-top: -16px;
}
</style>
