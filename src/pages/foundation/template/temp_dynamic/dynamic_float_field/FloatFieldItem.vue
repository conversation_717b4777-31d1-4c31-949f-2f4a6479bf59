<template>
    <div class="float-item"
        :style="{
            top: mark.y - editorScrollTop + 'px',
            left: mark.x + 'px',
        }"
    >
        <!-- 签名 -->
        <div class="float-item_sign" v-if="mark.type === 'SIGNATURE'">
            <p class="sign-title"
                :style="{
                    width: `${realWidth}px`,
                }"
            >{{ computeOwner(mark) }}</p>
            <div
                @click="clickMark"
                draggable="true"
                @dragstart="onStart"
                class="sign-content"
                :class="{'focusing': focusing}"
                :style="{
                    backgroundColor: computeFillColor(mark),
                    width: `${realWidth}px`,
                    height: `${realHeight}px`,
                }"
            >
                <svg class="sign-icon" aria-hidden="true">
                    <use style="pointer-events: none;"
                        x="0"
                        y="-7"
                        width="72"
                        height="62"
                        :xlink:href="markIcon(mark.type)"
                    >
                    </use>
                </svg>
            </div>
            <i class="close-icon el-icon-ssq-guanbi" @click="handleFieldDelete"></i>
        </div>

        <!-- 签章 -->
        <div class="float-item_sign seal" v-else-if="mark.type === 'SEAL'">
            <p class="sign-title"
                :style="{
                    width: `${realWidth}px`,
                }"
            >{{ computeOwner(mark) }}</p>
            <div
                @click="clickMark"
                draggable="true"
                @dragstart="onStart"
                class="sign-content"
                :class="{'focusing': focusing}"
                :style="{
                    backgroundColor: computeFillColor(mark),
                    width: `${realWidth}px`,
                    height: `${realHeight}px`,
                }"
            >
                <svg class="sign-icon" aria-hidden="true">
                    <use style="pointer-events: none;"
                        x="6"
                        y="32"
                        width="115"
                        height="72"
                        :xlink:href="markIcon(mark.type)"
                    >
                    </use>
                </svg>
            </div>
            <i class="close-icon el-icon-ssq-guanbi" @click="handleFieldDelete"></i>
        </div>

        <!-- 签署日期 -->
        <div class="float-item_date"
            v-else-if="mark.type === 'DATE'"
        >
            <i class="close-icon el-icon-ssq-guanbi" @click="handleFieldDelete"></i>
            <span
                @click="clickMark"
                draggable="true"
                @dragstart="onStart"
                :class="{'focusing': focusing}"
                :style="{
                    backgroundColor: computeFillColor(mark),
                    width: `${realWidth}px`,
                    height: `${realHeight}px`,
                }"
            >
                签署日期</span>
        </div>

        <!-- 浮动文本 -->
        <span class="float-item_field"
            v-else-if="showFloatBusinessField"
            :style="{
                backgroundColor: computeFillColor(mark)
            }"
        >
            {{ mark.name }}
        </span>
    </div>
</template>

<script>
import { markIconInfo, newMarkSizeInfo } from 'src/pages/foundation/sign/common/info/info.js';
import { rgbColorInfo } from 'utils/colorInfo.js';
export default {
    name: 'FloatFieldItem',
    props: {
        mark: {
            type: Object,
            required: true,
        },
        editorScrollTop: {
            type: Number,
            default: 0,
        },
        receivers: {
            type: Array,
            default: () => [],
        },
        showFloatBusinessField: {
            type: Boolean,
            default: false,
        },
        focusing: {
            type: Boolean,
            default: false,
        },
        markIndex: {
            type: Number,
            required: true,
        },
        editor: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            rgbColorInfo: rgbColorInfo.slice(1),
            dragging: false,
            isClick: false,
        };
    },
    computed: {
        realWidth() {
            return newMarkSizeInfo(this.mark.type).width;
        },
        realHeight() {
            return newMarkSizeInfo(this.mark.type).height;
        },
    },
    methods: {
        onStart(evt) {
            if (navigator.userAgent.indexOf('MSIE') > 0) { // close IE, 避免IE浮动标签内部拖动的问题，SAAS-7858
                return;
            }
            this.$emit('stop-float'); // 拖拽时移除当前插入状态，开始新的拖拽事件
            this.editor.plugins.clipboard.initDragDataTransfer(evt, undefined, true);
            const dataTransfer = evt.dataTransfer;

            dataTransfer.setData('field', this.markIndex);
            dataTransfer.setData('text/html', '<span></span>');
        },
        clickMark() {
            this.$emit('mark-click');
        },
        findReceiverIdx(id) {
            return this.receivers.findIndex(item => ('' + item.roleId) === ('' + id));
        },
        markIcon(type) {
            return `#${markIconInfo(type).type}`;
        },
        computeOwner(mark) {
            const receiver = this.receivers[this.findReceiverIdx(mark.roleId)] || {};
            return receiver.showName || '';
        },
        computeFillColor(mark) {
            // 签署方或者印章、签名、日期类型时，根据身份选择
            if (mark.receiverFill || mark.type === 'SEAL' || mark.type === 'SIGNATURE' || mark.type === 'DATE') {
                return `rgba(${this.rgbColorInfo[this.findReceiverIdx(mark.roleId) % 8]}, 0.75)` || 'transparent';
            } else {
                // 发件方使用固定颜色
                return `rgba(${rgbColorInfo[0]}, 0.75)`;
            }
        },
        handleFieldDelete(e) {
            this.$emit('mark-delete', e);
        },
    },
};
</script>

<style lang="scss">
    .float-item {
        position: absolute;
        display: inline-block;
        &_sign {
            // 浮动字段的宽高要根据实际值做计算
            width: 110px;
            height: 74px;
            margin-top: -18px;
            .sign-title {
                padding: 0 6px;
                margin-bottom: 2px;
                height: 18px;
                line-height: 18px;
                box-sizing: border-box;
                background-color: rgb(255, 247, 182);
            }
            .sign-content {
                border-radius: 4px;
                padding-left: 18px;
                box-sizing: border-box;
                border: 2px solid;
                border-color: transparent;
                .sign-icon {
                    width: 60px;
                    height: 48px;
                }
                &:hover {
                    cursor: move;
                }
            }
            &.seal {
                // 浮动字段的宽高要根据实际值做计算
                height: 172px;
                width: 176px;
                .sign-content {
                    padding-left: 38px;
                    padding-top: 15px;
                }
                .sign-icon {
                    width: 121px;
                    height: 121px;
                    background: rgba(255, 255, 255, 0.3);
                    border-radius: 60px;
                }
            }
        }
        &_date {
            // 浮动字段的宽高要根据实际值做计算
            height: 30px;
            width: 112px;
            span {
                font-size: 14px;
                height: 20px;
                line-height: 20px;
                display: inline-block;
                text-align: center;
                border: 2px solid;
                border-color: transparent;
                box-sizing: border-box;
                &:hover {
                    cursor: move;
                }
            }
        }
        &_field {
            height: 24px;
            line-height: 24px;
            padding: 4px 12px;
        }
        .focusing {
            border-color: #127fd2;
        }
        .close-icon {
            color: white;
            font-size: 12px;
            background-color: rgba(51, 51, 51, .75);
            padding: 5px;
            border-radius: 12px;
            position: absolute;
            top: -8px;
            right: 0px;
            cursor: pointer;
        }
    }
</style>
