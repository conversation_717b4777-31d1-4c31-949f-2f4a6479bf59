<template>
    <div class="preview">
        <div v-show="loading" class="preview-error" v-loading="loading" element-loading-text=" "></div>
        <div v-if="error" class="preview-error">文件预览失败，您可以尝试使用PDF格式的文件上传。</div>
        <template v-else>
            <div class="preview-thumbnail">
                <Thumbnail
                    :docList="tempData.formatDocList"
                    :currentDocIndex="currentDocIndex"
                    :currentPageIndex="currentPageIndex"
                    @emit-doc="handleDoc"
                    :receivers="receivers"
                ></Thumbnail>
            </div>
            <div class="preview-pdf pdfViewer" id="pdfContainer" @scroll="handleScroll"></div>
        </template>
    </div>
</template>

<script>
import 'pdfview/build/pdf.js';
import workerSrc from 'pdfview/build/pdf.worker.js';
import 'pdfview/web/pdf_viewer';
import 'pdfview/web/pdf_viewer.css';
import { throttle } from 'src/common/utils/fn.js';
import Thumbnail from '../thumbnail/Thumbnail.vue';
// 高清渲染需要的参数
const CSS_UNITS = 96.0 / 72.0;
// const PDF_WIDTH = document.body.clientWidth > 1400 ? 1366 - 320 : 1200 - 320; // PDF预览区宽度
const PDF_WIDTH = 1000; // PDF预览区宽度
PDFJS.workerSrc = workerSrc;
PDFJS.disableWorker = true;
PDFJS.cMapUrl = pdfCmaps;
PDFJS.cMapPacked = true;
import { rgbColorInfo } from 'utils/colorInfo.js';
import Label from '../label/Label.vue';
// 预览图宽高
const THUMBNAIL_WIDTH = 130;
const THUMBNAIL_HEIGHT = 184;
/* eslint-disable */
    const LabelExtend = Vue.extend(Label);
    export default {
        props: ['receivers', 'tempData', 'formatDocList'],
        data() {
            return {
                error: false,
                currentDocIndex: 0,
                currentPageIndex: 0,
                loading: false,
            };
        },
        components: {
            Thumbnail,
            LabelExtend,
        },
        computed: {
            // pdf每一页的放缩
            scale() {
                return PDF_WIDTH / this.tempData.maxWidth;
            },
        },
        methods: {
            findReceiverColor(roleId) {
                const index = this.receivers.findIndex(item => item.roleId === roleId);
                // colorInfo的第一个颜色保留给发件方使用
                return rgbColorInfo.slice(1)[index % 8];
            },
            findReceiverName(mark) {
                let receiver = this.receivers.find(item => item.roleId === mark.roleId);
				return (receiver || {}).showName || '';
            },
            handleDoc({ docIndex, pageIndex }) {
                this.currentDocIndex = docIndex;
                this.currentPageIndex = pageIndex;
                // 滚动到文档的那一页
                let top = 0;
                const list = this.tempData.formatDocList;
                for(let i = 0 ; i <= docIndex; i++) {
                    const doc = list[i];
                    const page = i === docIndex ? doc.page.slice(0, pageIndex): doc.page;
                    top = page.reduce((total, item) => {
                        total += (this.scale * item.height + 30)
                        return total;
                    }, top);
                }
                document.getElementById('pdfContainer').scrollTop = top;
            },
            // 导出文档的缩略图
            generateImage(pdfCanvas, width, height, targetWidth, targetHeight) {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                let canvasWidth, canvasHeight;
                // 对图片进行压缩
                // 图片比较宽，以高度为scale进行放缩
                if (targetWidth / width > targetHeight / height) {
                    canvasWidth = width * (targetHeight / height);
                    canvasHeight = targetHeight;
                } else {
                    // 图片较高，以宽度为scale进行放缩
                    canvasWidth = targetWidth;
                    canvasHeight = height * (targetWidth / width);
                }
                canvas.width = canvasWidth * 4;
                canvas.height = canvasHeight * 4;
                ctx.drawImage(pdfCanvas, 0, 0, canvas.width, canvas.height);
                return canvas.toDataURL();
            },
            // 加载每一个文档对象
            async loadPDFDocument(doc, documentId) {
                const pdfUrl = `${tempPath}${doc.pdfUrl}?access_token=${this.$cookie.get('access_token')}`;
                try {
                    const pdfDocument = await PDFJS.getDocument(pdfUrl);
                    const container = document.createElement('div');
                    container.setAttribute('data-document-id', documentId);
                    const wrapper = document.getElementById('pdfContainer');
                    wrapper.appendChild(container);
                    let pageNum = 1;
                    while (pageNum <= pdfDocument.numPages) {
                        await this.renderPDFPage(pdfDocument, pageNum, container, doc);
                        pageNum++;
                    }
                } catch (error) {
                    this.error = true;
                }
            },
            // 渲染每一页
            async renderPDFPage(pdfDocument, pageNum, container, doc) {
                // TODO 本页面渲染时添加loading提示
                const pdfPage = await pdfDocument.getPage(pageNum);
                const pdfPageView = new PDFJS.PDFPageView({
                    container: container,
                    id: pageNum,
                    scale: this.scale / CSS_UNITS,
                    defaultViewport: pdfPage.getViewport(this.scale / CSS_UNITS),
                });
                pdfPageView.setPdfPage(pdfPage);
                await pdfPageView.draw();
                const { width, height } = pdfPageView.pdfPage.getViewport(1.0);
                doc.page[pageNum - 1].width = width;
                doc.page[pageNum - 1].height = height;
                // 生成缩略图
                this.generateList(pdfPageView, doc);
                // 渲染完本页面之后，还需要插入页面内的标签
                this.renderLabel(pdfPageView.div, doc.page[pageNum - 1]);
                // 插入页码
                this.addPageNumber(pdfPageView.div, doc.fileName, pageNum, doc.page.length);
            },
            // 添加左下角文件名和页码
            addPageNumber(container, fileName, pageNum, pageSize) {
                const div = document.createElement('div');
                div.className = 'pagenum';
                div.innerText = `${fileName}（第${pageNum}页，共${pageSize}页）`;
                container.appendChild(div);
            },
            // 生成缩略图列表
            generateList(pdfPageView, doc) {
                // Pdf文档的宽高
                const { width, height } = pdfPageView.pdfPage.getViewport(1.0);
                // 生成缩略图，参数为pdf文档宽高，要生成的缩略图的宽高
                const thumbnail = this.generateImage(pdfPageView.canvas, width, height, THUMBNAIL_WIDTH, THUMBNAIL_HEIGHT);
                if (!doc.thumbnailList) {
                    this.$set(doc, 'thumbnailList', []);
                }
                doc.thumbnailList.push(thumbnail);
            },
            async loadPDF(docList) {
                let index = 0;
                while (index < docList.length) {
                    let doc = docList[index];
                    await this.loadPDFDocument(doc, doc.attachmentId || doc.documentId);
                    index++;
                }
            },
            renderLabel(element, page) {
                if (!page.marks || !page.marks.length) return;
                // 渲染页面的所有标签信息
                page.marks.forEach((mark, index) => {
                    const scale = PDF_WIDTH / this.tempData.maxWidth;
                    const color = this.findReceiverColor(mark.roleId);
                    const receiverName = this.findReceiverName(mark);
                    const data = { page, mark, scale, color, receiverName };
                    const instance = new LabelExtend({ data }).$mount();
                    element.appendChild(instance.$el);
                });
            },
            handleScroll: throttle(function (e) {
                const { formatDocList, maxWidth } = this.tempData;
                const scale = PDF_WIDTH / maxWidth;
                // 视图内的可视范围
                const scrollTop = e.target.scrollTop;
                const scrollBottom = scrollTop + (document.body.clientHeight - 85);
                // 寻找视图可视范围内，内容占比最大的那一页
                let startTop = 0;
                const visible = formatDocList.reduce((total, doc, docIndex) => {
                    doc.page.forEach((page, pageIndex) => {
                        const pageHeight = page.height * scale;
                        let endTop = startTop + pageHeight;
                        let percent = 0;
                        // 在可视范围内的元素
                        if (startTop <= scrollTop && endTop < scrollBottom) {
                            percent = (endTop - scrollTop) / pageHeight;
                        } else if (startTop >= scrollTop && endTop <= scrollBottom) {
                            percent = (scrollBottom - startTop) / pageHeight;
                        } else if (startTop < scrollBottom && endTop >= scrollBottom) {
                            percent = (scrollBottom - startTop) / pageHeight;
                        }
                        startTop += pageHeight + 10;
                        if (!percent) return;
                        total.push({
                            docIndex: docIndex,
                            pageIndex: pageIndex,
                            percent
                        });
                    });
                    return total;
                }, []);
                // 上面找到的是最小的那个页码，但是如果页面中一半的内容，已经是下一页的内容，则需要进行下一页的渲染
                const find = visible.reduce((total, item) => {
                    return item.percent >= total.percent ? item : total;
                });
                this.currentPageIndex = find.pageIndex;
                this.currentDocIndex = find.docIndex;
            }, 24)
        },
        mounted() {
            this.loadPDF(this.tempData.formatDocList).then(() => {
                // fix CFD-2990: 遨游/safari 界面出现锯齿，部分文字显示不全，滚动后正确显示。未定位到原因，通过滚动规避
                document.getElementById('pdfContainer').scrollTop = 1;
            });
        }
    };
</script>

<style lang="scss">
.preview {
    width: 100%;
    height: 100%;
    .preview-pdf {
        user-select: text;
        height: 100%;
        overflow: auto;
        background-color: #f0f1f5;
        transition: all 0 ease-out 0;
    }
    .preview-thumbnail {
        float: right;
        width: 210px;
        height: 100%;
    }
    .preview-error {
        user-select: text;
        width: 100%;
        height: 100%;
        text-align: center;
        margin: 0 auto;
        background-color: #fff;
        font-size: 14px;
        padding-top: 250px;
        box-sizing: border-box;
        color: #ccc;
    }
    .pdfViewer.singlePageView .page {
        margin-top: 10px;
        border: none;
    }
    .pdfViewer .page {
        direction: ltr;
        width: 100%;
        margin: 0 auto;
        position: relative;
        overflow: visible;
        border: 0 none;
        background-clip: content-box;
        background-color: #fff;
        margin-bottom: 30px;
        .pagenum {
            margin-top:3px;
            text-align:center;
        }
    }
}
</style>
