<template>
    <div class="thumbnail">
        <!-- 缩略图区 -->
        <div class="title">文档</div>
        <div class="doc-edit-view">
            <ul>
                <li class="doc"
                    v-for="(doc, docIndex) in docList"
                    :key="doc.attachmentId ? doc.documentId:doc.attachmentId"
                    :class="doc.attachmentId ? 'no-bottom': ''"
                >
                    <div v-if="!doc.attachmentId" class="doc-title" @click="clickDocTitle(doc.documentId)">
                        {{ doc.fileName }}
                    </div>
                    <div v-show="activeId.includes(doc.documentId)">
                        <div class="doc-title doc-title-attachment" v-if="doc.attachmentId"> <i class="el-icon-ssq-fujian1"></i> 附件：{{ doc.fileName }}</div>
                        <div class="doc-totalPage" v-if="!doc.attachmentId"> 页数：{{ doc.totalPageSize }}页 </div>
                        <div class="doc-totalPage doc-totalPage-attachment" v-if="doc.totalPageSize - doc.pageSize > 0"> <i class="el-icon-ssq-fujian1"></i>附件：<span>{{ doc.totalPageSize - doc.pageSize }}</span>页</div>
                        <ul class="doc-pages">
                            <li v-for="(item, pageIndex) in doc.thumbnailList"
                                :key="pageIndex"
                                :class="{ 'current':
                                    currentDocIndex === docIndex &&
                                    currentPageIndex === pageIndex }"
                            >
                                <div class="doc-perpage" @click="clickMiniImage(docIndex, pageIndex)">
                                    <img :src="item" />
                                    <div class="page-foot">
                                        <span>{{ pageIndex+1 }}</span>
                                    </div>
                                    <div class="indicators">
                                        <div class="indicators-tag"
                                            v-for="mark in filterReceivers((doc.page[pageIndex].marks || []).filter(item => item.type !== 'QR_CODE' ))"
                                            :key="mark.labelId"
                                            :style="`background-color: ${findReceiverColor(mark.roleId)}`"
                                        >
                                            <div class="sanjiao"
                                                :style="`border-left: 6px solid ${findReceiverColor(mark.roleId)}`"
                                            >
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</template>
<script>
import { uniqBy } from 'src/common/utils/ary.js';
import { colorInfo } from 'utils/colorInfo.js';

export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['receivers', 'docList', 'currentDocIndex', 'currentPageIndex'],
    data() {
        return {
            // 展开的文档Id
            activeId: [this.docList[0].documentId],
            // colorInfo: colorInfo.slice(1), // 第一个颜色保留给发件方使用
        };
    },
    methods: {
        findReceiverColor(roleId) {
            const index = this.receivers.findIndex(item => item.roleId === roleId);
            // colorInfo的第一个颜色保留给发件方使用
            return colorInfo.slice(1)[index % 8];
        },
        filterReceivers(marks) {
            return uniqBy(marks || [], 'roleId').res;
        },
        clickMiniImage(docIndex, pageIndex) {
            this.$emit('emit-doc', { docIndex, pageIndex });
        },
        clickDocTitle(documentId) {
            const findIndex = this.activeId.findIndex(item => documentId === item);
            if (findIndex === -1) {
                this.activeId.push(documentId);
            } else {
                this.activeId.splice(findIndex, 1);
            }
        },
    },
};
</script>
<style lang="scss">
    .thumbnail {
        border-left: 1px solid $border-color;
        overflow-y: scroll;
        height: 100%;
        // 缩略图区
        .title {
            height: 38px;
            line-height: 38px;
            font-size: 14px;
            font-weight: bold;
            padding-left: 20px;
            padding-right: 15px;
            span {
                float: right;
                font-size: 12px;
                color: #3B86D4;
                &:hover {
                    cursor: pointer;
                }
            }
        }

        .doc-edit-view {
            border-bottom: 1px solid $border-color;
            .doc {
                border-top: 1px solid $border-color;
                &.no-bottom{
                    border-top:0;
                    .doc-pages{
                        padding-top:0;
                        li .page-foot{
                            border-left: 18px solid #D8D8D8;
                            border-bottom: 18px solid #D8D8D8;
                        }
                    }
                }
            }
            .doc-title {
                position: relative;
                height: 18px;
                line-height: 18px;
                color: #333;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                cursor: pointer;
                padding: 6px 40px 6px 39px;
                &.doc-title-attachment{
                    color: #99999A;
                    &:after {
                        display: none;
                    }
                    &:hover {
                        color: #99999A;
                    }
                }
                &:hover {
                    color: #3B86D4;
                }
                &:after {
                    position: absolute;
                    top: 9px;
                    right: 24px;
                    content: "";
                    border-left: 1px solid #333;
                    border-top: 1px solid #333;
                    padding: 3px;
                    display: inline-block;
                    transform: rotate(225deg);
                }
            }
            .doc-totalPage {
                height: 18px;
                line-height: 18px;
                color: #999;
                padding-left: 39px;
            }
            .doc-pages {
                padding-top: 13px;
                padding-left: 39px;
                li {
                    width: 130px;
                    margin-bottom: 14px;
                    border: 1px solid #C5C5C5;
                    &.current {
                        border: 1px solid #0072CF;
                    }
                    .doc-perpage {
                        position: relative;
                        width: 130px;
                        height: 184px;
                        cursor: pointer;
                        img {
                            position: absolute;
                            max-width: 100%;
                            max-height: 100%;
                            top: 0;
                            right: 0;
                            bottom: 0;
                            left: 0;
                            margin: auto;
                            display: block;
                        }
                    }
                    .page-foot {
                        position: absolute;
                        bottom: 0;
                        left: 15px;
                        width: 0;
                        height: 0;
                        border-top: 18px solid transparent;
                        border-left: 18px solid #999;
                        border-right: 18px solid transparent;
                        border-bottom: 18px solid #999;
                        margin-left: -15px;
                        span {
                            position: absolute;
                            bottom: -14px;
                            left: -17px;
                            width: 15px;
                            height: 15px;
                            text-align: center;
                            color: #fff;
                            font-size: 11px;
                        }
                        i {
                            font-size: 10px;
                            color: #666;
                            cursor: pointer;
                        }
                    }
                    .indicators {
                        position: absolute;
                        top: 0;
                        left: -8px;
                        width: 50px;
                        height: 100%;
                        margin-top: 5px;
                        overflow: hidden;
                        pointer-events: none;
                        .indicators-tag {
                            position: relative;
                            width: 20px;
                            height: 12px;
                            background-color: transparent;
                            margin-bottom: 3px;
                            .sanjiao {
                                position: absolute;
                                top: 0px;
                                left: 35px;
                                width: 0;
                                height: 0;
                                border-top: 6px solid transparent;
                                border-left: 6px solid transparent;
                                border-right: 6px solid transparent;
                                border-bottom: 6px solid transparent;
                                margin-left: -15px;
                            }
                        }
                    }
                }
            }
        }
    }
</style>
