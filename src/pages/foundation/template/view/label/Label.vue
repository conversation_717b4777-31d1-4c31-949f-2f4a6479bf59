<template>
    <div>
        <div class="template-label" :style="styleObject" v-if="['SEAL', 'SIGNATURE', 'DATE'].includes(mark.type)">
            <div class="template-label-name" v-if="['SEAL', 'SIGNATURE'].includes(mark.type)">{{ receiverName }}</div>
            <div class="template-label-wrapper" :style="`background-color: rgba(${color}, 0.8); width: ${defaultWidth}px; height: ${defaultHeight}px;`">
                <!-- 印章 -->
                <div class="template-label-seal" v-if="mark.type=== 'SEAL'">
                    <div class="template-label-seal-img">
                        <svg class="icon" aria-hidden="true">
                            <use :xlink:href="`#${iconType}`"></use>
                        </svg>
                    </div>
                </div>
                <!-- 签名 -->
                <div class="template-label-signature" v-else-if="mark.type === 'SIGNATURE'">
                    <svg class="icon" aria-hidden="true">
                        <use :xlink:href="`#${iconType}`"></use>
                    </svg>
                </div>
                <div v-else-if="mark.type === 'DATE'" class="template-label-date">签署日期</div>
            </div>
        </div>
        <div v-else :style="normalStyle" class="template-label-normal">
            <div v-if="['TEXT', 'BIZ_DATE', 'TEXT_NUMERIC'].includes(mark.type)">
                {{ mark.name }}
            </div>
        </div>
    </div>
</template>

<script>
import { markInfo, markIconInfo } from 'src/pages/foundation/sign/common/info/info.js';
const DPI_RATE = 96 / 72; // PDF转换时会有DPI变化

export default {
    data() {
        return {
            page: {},
            scale: 1,
            mark: {},
            color: '#fff',
            receiverName: '',
        };
    },
    computed: {
        defaultWidth() {
            return markInfo(this.mark.type).width;
        },
        defaultHeight() {
            return markInfo(this.mark.type).height;
        },
        iconType() {
            return markIconInfo(this.mark.type).type;
        },
        normalStyle() {
            const { height, x, y, fontSize } = this.mark;
            const left = x;
            const top = 1 - y - height;
            return {
                top: `${top * 100 + (fontSize / 80)}%`, // 手动修正坐标偏移问题
                left: `${left * 100}%`,
                width: `${this.markWidth}px`,
                height: `${this.markHeight}px`,
                lineHeight: `${this.markHeight}px`,
                position: 'absolute',
                backgroundColor: `rgba(${this.color}, 0.8)`,
                fontSize: `${fontSize * this.scale / DPI_RATE}px`,
            };
        },
        styleObject() {
            const { x, y, type, height } = this.mark;
            const left = x;
            // 新的坐标系根据文字定位，标签的左上角相对于pdf左下角的位置
            const top = 1 - y - height;
            const defaultWidth = markInfo(type).width;
            const transformHeight = ['SEAL', 'SIGNATURE'].includes(type) ? '-26' : 0;
            // scale为标签的实际宽度与默认宽度的比值，因为印章，签名和二维码都是固定的，不可拖动（这里主要是为了兼容老的模板，如果是动态模板，应该只需要根据scale来放缩就可以了）
            const scale = this.markWidth / defaultWidth;
            return {
                top: `${top * 100 - 0.4}%`, // 手动fix 坐标
                left: `${left * 100}%`,
                transform: `scale(${scale}) translate(0px, ${transformHeight}px)`,
                width: defaultWidth,
            };
        },
        // 页面的高度
        pageHeight() {
            return this.page.height * this.scale;
        },
        // 页面的宽度
        pageWidth() {
            return this.page.width * this.scale;
        },
        // 标签的高度
        markHeight() {
            return this.pageHeight * this.mark.height;
        },
        // 标签的宽度
        markWidth() {
            return this.pageWidth * this.mark.width;
        },
    },
};
</script>

<style lang="scss">
    .template-label {
        position: absolute;
        border-radius: 2px;
        transform-origin: left top;
        .template-label-name {
            width: 100%;
            border-radius: 2px;
            background-color: rgba(#fff7b6, 0.7);
            margin-bottom: 2px;
            font-size: 12px;
            padding-left: 5px;
            box-sizing: border-box;
            white-space: nowrap;
            line-height: 24px;
        }
        .icon {
            width: 100%;
            height: 100%;
        }
        .template-label-wrapper {
            position: relative;
            border-radius: 2px;
        }
        .template-label-signature {
            position: relative;
            top: -6px;
            left: 28px;
            width: 96px;
            height: 83px;
        }
        .template-label-seal {
            position: absolute;
            width: 162px;
            height: 162px;
            left: 53px;
            top: 21px;
            background-color: rgba(#fff, 0.3);
            border-radius: 50%;
            &-img {
                text-align: center;
                left: 50%;
                top: 50%;
                position: absolute;
                width: 154px;
                height: 96px;
                transform: translate(-50%, -50%);
            }
        }
        .template-label-date {
            font-size: 24px;
            text-align: center;
        }
    }
</style>
