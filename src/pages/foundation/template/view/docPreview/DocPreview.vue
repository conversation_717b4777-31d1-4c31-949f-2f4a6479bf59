<!-- 指定位置页重构 -->
<template>
    <div class="FieldNew-page">
        <div class="FieldBody-wrapper" v-loading.fullscreen.lock="hybridContractServerConnected ? loading : 0">
            <!-- 签约方和字段区域 -->
            <div class="site-content clear">
                <div class="FieldDoc-wrapper">
                    <div class="site-doc-title" v-if="docList.length">{{ docList[currentDocIndex].fileName }}</div>
                    <!-- 合同内容 -->
                    <FieldDoc v-if="docList.length"
                        :contractId="contractId"
                        :docList="docList"
                        :receivers="receivers"
                        :receiverIndex="receiverIndex"
                        :dragStatus="dragStatus"
                        :floatingType="floatingType"
                        :float="float"
                        :edit="edit"
                        :watermarkList="decorateInfo.watermarkList"
                        :ridingSealList="decorateInfo.ridingSealList"
                        @mark-focus="onMarkFocus"
                        @mark-blur="onMarkBlur"
                        @updateFloatIcon="updateFloatIcon"
                        :currentDocIndex="currentDocIndex"
                        :currentPageIndex="currentPageIndex"
                        @updateCurrentDocIndex="handleUpdateDocIndex"
                        @updateCurrentPageIndex="handleUpdatePageIndex"
                        @to-next="toNext"
                        :templateStatus="templateStatus"
                    >
                    </FieldDoc>
                </div>
                <!-- 缩略图区 -->
                <FieldMiniDoc
                    class="FieldMiniDoc"
                    v-if="docList.length"
                    :docList="docList"
                    :currentDocIndex="currentDocIndex"
                    :currentPageIndex="currentPageIndex"
                    @updateCurrentDocIndex="handleUpdateDocIndex"
                    @updateCurrentPageIndex="handleUpdatePageIndex"
                >
                </FieldMiniDoc>
            </div>
        </div>

    </div>
</template>

<script>
import FieldDoc from 'src/pages/foundation/sign/field/fieldDoc/FieldDoc.vue';
import FieldMiniDoc from 'src/pages/foundation/sign/field/fieldMiniDoc/FieldMiniDoc.vue';
import { initDecorateInfo } from 'src/common/utils/decorate.js';
import { cloneDeep } from 'lodash';
import dayjs from 'dayjs';

export default {
    components: {
        FieldDoc,
        FieldMiniDoc,
    },
    props: {
        templateStatus: {
            default: 'edit',
            type: String,
        },
    },
    data() {
        return {
            contractId: this.$route.query.templateId,
            templateId: this.$route.query.templateId,
            tempPath: '/template-api',
            hybridContractServerConnected: true,
            signOrdered: false,

            loading: true,
            receiverIndex: 0,
            isFloatIconShow: false,
            floatingType: '',
            floatingClass: '',
            dragStatus: 'end',
            float: {
                fontSize: 14,
            },
            // edit
            edit: {
                mark: null,
            },
            // ajax
            receivers: [],
            docList: [],
            decorateInfo: [],
            currentDocIndex: 0, // 当前第几份文档
            currentPageIndex: 1, // 第几份文档的第几页
        };
    },
    computed: {
        // 混合云host
        hybridServer() {
            return this.$store.state.commonHeaderInfo.hybridServer;
        },
        hybridAccessToken() {
            return this.$store.state.commonHeaderInfo.hybridAccessToken;
        },
    },
    methods: {
        toNext() {
            const errorList = [];
            // 先判断下是否有签署人没有印章和签名
            this.receivers.forEach((receiver) => {
                const { receiverId, userName, userType, signType } = receiver;
                const fileList = [];
                this.docList.forEach((doc, docIndex) => {
                    const { fileName, marks } = doc;
                    // 文档中是否已经包含该receiver的签名和印章
                    let hasMarked = this.isMarked(marks, receiverId, ['SEAL', 'SIGNATURE']);
                    let hasPartedMarked = false; // 完成了盖章或签字
                    if (signType === 'SEAL_AND_SIGNATURE') {
                        const hasMarkedSeal = this.isMarked(marks, receiverId, ['SEAL']);
                        const hasMarkedSignature = this.isMarked(marks, receiverId, ['SIGNATURE']);
                        hasMarked = hasMarkedSeal && hasMarkedSignature; // 拖了盖章和签字
                        hasPartedMarked = (hasMarkedSeal || hasMarkedSignature); // 拖了章
                    }
                    if (!hasMarked) {
                        fileList.push({
                            fileName,
                            docIndex,
                            hasPartedMarked,
                        });
                    }
                });
                if (fileList.length) {
                    errorList.push({
                        receiverId,
                        userName,
                        fileList,
                        userType,
                        signType,
                    });
                }
            });
            if (!errorList.length) {
                this.getDefines(); // 审批流
            } else {
                const isNone = errorList.some(error => {
                    if ((error.fileList || []).length !== this.docList.length) {
                        return false;
                    }
                    // 如果是签字并盖章，要判断是否是签字和盖章都没有添加
                    return error.signType !== 'SEAL_AND_SIGNATURE' || error.fileList.every(file => !file.hasPartedMarked);
                });
                if (isNone) {
                    return this.$MessageToast.error(this.$t('field.signaturePositionErr'));
                }
                const hasPartedMarked = errorList.some(error => {
                    if (error.signType === 'SEAL_AND_SIGNATURE') {
                        return error.fileList.filter(file => file.hasPartedMarked).length;
                    }
                    return false;
                });
                if (hasPartedMarked) {
                    return this.$MessageToast.error(this.$t('field.partedMarkedError'));
                } else {
                    this.dialogTip = true;
                    this.tipList = errorList;
                }
            }
        },
        // 每次切换文档时，滚动到顶部，当前页设置为第一页
        handleUpdateDocIndex(currentDocIndex) {
            this.currentDocIndex = currentDocIndex;
            this.currentPageIndex = 1;
        },
        handleUpdatePageIndex(currentPageIndex) {
            this.currentPageIndex = currentPageIndex;
        },
        followCursorWithIcon(e) {
            document.querySelector('#flying-icon').style.left = `${e.clientX + 1}px`;
            document.querySelector('#flying-icon').style.top = `${e.clientY + 1}px`;
        },
        onSwitchReceiver(res) {
            this.receiverIndex = res.receiverIndex;
        },
        onDragStart(e, type, className) {
            setTimeout(() => {
                this.dragStatus = 'started';
                this.followCursorWithIcon(e);
                this.isFloatIconShow = true;
                this.floatingType = type;
                this.floatingClass = className;
                if (type === 'DATE') {
                    this.float.fontSize = 14;
                    this.float.type = type;
                }
            }, 150);
        },
        onDragMove(event) {
            this.updateFloatIcon(this.isFloatIconShow, event);
        },
        updateFloatIcon(isFloatIconShow, event) {
            if (this.dragStatus !== 'started') {
                return;
            }
            this.isFloatIconShow = isFloatIconShow;
            if (isFloatIconShow) {
                this.followCursorWithIcon(event);
            }
        },
        onDragEnd() {
            this.dragStatus = 'end';
            this.isFloatIconShow = false;
            this.floatingClass = '';
        },
        onMarkFocus(res) {
            this.edit.mark = res.mark;
        },
        onMarkBlur() {
            this.edit.mark = null;
        },
        getRecivers() {
            return this.$http.get(`${this.tempPath}/templates/${this.templateId}/roles?isOnlyNeedSigner=1&displayRuleName=true`); // isOnlyNeedSigner不要抄送人，displayRuleName按规则格式化接收人名称
        },
        getDocInfo() {
            return this.$http.get(`${this.tempPath}/dynamic-template/${this.templateId}/previews`);
        },
        handleResponseData(docData, inialData) {
            const initialDocData = cloneDeep(docData);
            const handledData = initialDocData.map((doc) => {
                doc.page = new Array(doc.pageSize).fill({});
                const newArr = [];
                doc.page.map((page, index) => {
                    newArr.push(Object.assign({}, page, {
                        width: inialData.maxWidth,
                        height: inialData.maxHeight,
                        imagePreviewUrl: `/template-api/templates/${this.templateId}/documents/${doc.documentId}/view/${index + 1}?_=${dayjs(new Date()).valueOf()}`,
                        highQualityPreviewUrl: `/template-api/templates/${this.templateId}/documents/${doc.documentId}/view/${index + 1}?_=${dayjs(new Date()).valueOf()}`,
                        pageNumber: index + 1,
                        documentId: doc.documentId,
                        marks: doc.labels,
                    }));
                });
                doc.page =  newArr;
                return doc;
            });
            return handledData;
        },
        // 初始化 marks[签名、标签字段] , marks包含 上上签查验二维码【文档右下角固定显示】
        initDocData(resData) {
            resData.forEach((doc) => {
                // 文档缩略图默认为空
                doc.thumbnail = '';
                // 兼容混合云文件预览
                doc.pdfurl = `${this.tempPath}${doc.pdfUrl}?access_token=${this.$cookie.get('access_token')}`;
                // doc.pdfurl = this.$hybrid.getPdfPreviewUrl({ url: doc.fileStreamUrl, hybridServer: this.hybridServer, hybridTarget: '/contract/part/document/download', params: { contractId: this.contractId, documentIds: doc.documentId }, hybridAccessToken: this.hybridAccessToken });
                doc.marks = (doc.page || []).reduce((total, page, pageIndex) => {
                    // 对每一页的mark坐标进行转换
                    const markList = (page.marks || []).map(mark => {
                        return {
                            ...mark,
                            pageNumber: mark.pageNumber || pageIndex + 1,
                            documentId: doc.documentId,
                        };
                    });
                    return total.concat(markList);
                }, []);
            });
            return resData;
        },
        isMarked(marks, receiverId, types) {
            return (marks || []).filter(mark => types.includes(mark.type))
                .some(mark => receiverId === mark.receiverId);
        },
    },
    beforeMount() {
        document.addEventListener('mousemove', this.onDragMove);
        document.addEventListener('mouseup', this.onDragEnd);
    },
    beforeDestroy() {
        document.removeEventListener('mousemove', this.onDragMove);
        document.removeEventListener('mouseup', this.onDragEnd);
    },
    async created() {
        this.loading = true;
        Promise.all([
            this.getDocInfo(),
            this.getRecivers(),
        ]).then(([{ data: docData }, { data: receiverData }]) => {
            this.loading = false;
            const handledData = this.handleResponseData(docData.documents, docData);
            this.docList = this.initDocData(handledData);
            this.receivers = receiverData || [];
            this.decorateInfo = initDecorateInfo(receiverData, this.docList[0].page[0].height);
        });
    },
};
</script>

<style lang="scss">
    .FieldNew-page {
        user-select: none;
        position: relative;
        height: 100vh;
        font-size: 12px;
        color: #333;
        background-color: #f6f6f6;

        // 审批流弹窗
        .dialog-approval {
            .el-dialog--small {
                width: auto;
                min-width: 409px;
            }
        }
        // 计费弹窗
        .dialog-charge {
            .el-dialog--small {
                width: 400px;
            }
            .el-dialog__header {
                display: none;
            }
            .el-dialog__body {
                padding: 0;
            }
        }
    }
    .field-error-alert {
        p {
            white-space: pre-wrap;
            word-wrap: break-word;
        }
    }
    .dialog-footer {
        &.lang_ru {
            button {
                width: 148px !important;
                & + button {
                    margin-left: 5px !important;
                }
            }
        }
    }

    // 原有的field-body
    .FieldBody-wrapper {
        position: absolute;
        top: 0px;
        bottom: 35px;
        width: 100%;
        overflow: hidden;

        .site-content {
            height: 100%;
            .FieldSite {
                position: relative;
                width: 210px;
                height: 100%;
                border-right: 1px solid $border-color;
                overflow-y: auto;
            }
            .FieldDoc-wrapper {
                height: 100%;
                overflow: auto;
                margin-left: 0px;
                margin-right: 211px;
                .site-doc-title {
                    width: calc(100% - 211px);
                    position: fixed;
                    z-index: 100;
                    top: 50px;
                    text-align: center;
                    border-bottom: 1px solid $border-color;
                    height: 40px;
                    line-height: 40px;
                    font-size: 14px;
                    background-color: #f6f6f6;
                }

            }
            .FieldMiniDoc {
                position: absolute;
                top: 0;
                right: 0;
                width: 210px;
                height: 100%;
            }
            .FieldEdit {
                position: absolute;
                top: 0px;
                right: 0;
                width: 211px;
                height: 100%;
                z-index: 101;
            }
        }

        .flying-icon {
            z-index: 9999;
            pointer-events: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 28px;
            height: 28px;
            line-height: 27px;
            text-align: center;
            font-size: 18px;
            background-color: #fff;
            border-radius: 4px;
            box-shadow:1px 1px 13px #ccc, -1px 1px 13px #ccc;
        }
    }
    .sign-later-message-box {
        .el-message-box__status {
            color: #999;
        }
    }
</style>
