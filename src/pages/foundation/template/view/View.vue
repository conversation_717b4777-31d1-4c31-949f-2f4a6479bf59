<!-- 动态模板预览 -->
<template>
    <div class="temp-field-page" v-loading.fullscreen.lock="loading">
        <!-- 页头 -->
        <SignHeader
            class="header"
            :title="tempData.templateName"
            rText="发送"
            :rShow="nextVisible"
            @to-back="toBack"
            @to-next="toNext"
            :backConfig="ssoTmpEdit.tmpEdit_1_back"
        >
            <!-- 提交审批按钮，只在单点登录并开启功能点的模板预览页展示 -->
            <li v-if="approvalBtnVisible" slot="prevBtn" class="next" @click="handleSubmitApproval">
                提交审批
            </li>
        </SignHeader>
        <div class="content">
            <!-- 原来的预览页面-->
            <!-- <Preview :receivers="receivers" :tempData="tempData" v-if="tempData.documents"></Preview> -->

            <!-- 图片版本的预览页面-->
            <!-- <DocPreview
                :canDrag="false"
                :canSendlabelEdit="false"
                :templateId="templateId"
                :iconDragStatus="false"
                :float="float"
                @change-doc="changeDoc"
                :decorateCanChange="false"
            ></DocPreview>
             <MiniDoc
                :documents="docList"
                @change-page="changePage"
                @change-doc="changeDoc"
            ></MiniDoc> -->

            <!-- pdf格式的预览页面-->
            <DocPreview :templateStatus="templateStatus"></DocPreview>

        </div>

        <!-- 页脚 -->
        <RegisterFooter class="footer" v-if="ftVisible"></RegisterFooter>

        <!-- 审批流dialog -->
        <el-dialog class="ssq-dialog dialog-approval"
            title="申请发送合同"
            :visible.sync="dialogApproval"
            @open="ApprovalAppKey = Math.random()"
        >
            <ApprovalApp
                :key="ApprovalAppKey"
                :definesData="definesData"
                @apply-approval="onApplyApproval"
            >
            </ApprovalApp>
        </el-dialog>

        <!-- 计费dialog -->
        <el-dialog class="ssq-dialog dialog-charge"
            v-if="dialogCharge"
            :visible.sync="dialogCharge"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
        >
            <SignCharge
                @SignCharge-confirm="onChargeConfirm"
                @SignCharge-cacel-dialog="dialogCharge = false"
            >
            </SignCharge>
        </el-dialog>

        <!-- 发送提示dialog -->
        <el-dialog class="ssq-dialog dialog-send-tips dialog-now-use"
            :visible.sync="dialogSendTips"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
        >
            <div class="icon-bg">
                <i class="el-icon-ssq-tongguo"></i>
                <p class="p-top">提交成功</p>
                <p class="p-bottom">{{ sendedDialogTip }}</p>
            </div>
            <div class="btns clear">
                <div class="ssq-btn-confirm" @click="$router.push('/template/record')" v-if="ssoDgViewRecordVisible">查看记录</div>
                <div class="ssq-btn-cancel" v-if="ssoDgSentDoenVisble" :class="!ssoDgViewRecordVisible && 'singleBtn'" @click="`${$router.push(doneBtnRedirectUrl)}`">完成</div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import SignHeader from '../../sign/common/signHeader/SignHeader.vue'; // 复用普通发起的header
import RegisterFooter from 'src/components/register_footer/RegisterFooter.vue';
import ApprovalApp from 'foundation_pages/sign/field/approvalApp/ApprovalApp.vue';
import SignCharge from 'foundation_pages/sign/field/signCharge/SignCharge.vue';
// import Preview from './preview/Preview.vue';
import { mapGetters, mapState, mapMutations } from 'vuex';
import { fieldLanBreak } from 'utils/hybrid/hybridBusiness.js';
// import DocPreview from '../tempYzDynamic/temp_dynamic_decoration/DocPreview.vue';
// import MiniDoc from '../tempYzDynamic/temp_dynamic_decoration/MiniDoc.vue';
import { cloneDeep } from 'lodash';
import dayjs from 'dayjs';
import { scrollToYSmooth } from 'src/common/utils/dom.js';
// import { initWatermark, initRidingSeal } from 'src/common/utils/decorateTool.js';
import DocPreview from './docPreview/DocPreview.vue';
export default {
    components: {
        SignHeader,
        RegisterFooter,
        ApprovalApp,
        SignCharge,
        // Preview,
        DocPreview,
        // MiniDoc,
    },
    data() {
        return {
            // 合同信息
            contractTitle: '',
            templateMatchId: this.$route.query.templateMatchId,
            // templateId: this.$route.query.templateId,
            // 若为混合云合同，是否能连接服务器，默认true
            hybridContractServerConnected: true,
            // 页面信息
            loading: false,
            // 审批流
            dialogApproval: false,
            ApprovalAppKey: 0,
            definesData: [],
            commitFlow: [],
            defInfoId: '',
            // 计费
            dialogCharge: false,
            // 发送成功提示
            dialogSendTips: false,
            receivers: [],
            tempData: {},
            float: {
                show: false,
            },
            templateId: this.$route.query.templateId,
            tempPath: '/template-api',
            templateStatus: this.$route.params.templateStatus || '',
        };
    },
    computed: {
        ...mapState('template', ['docList', 'currentDocIndex', 'zoom']),
        ...mapGetters(['getSsoConfig']),
        ftVisible() {
            return !this.ssoTmpEdit.tmpEdit_ft || this.ssoTmpEdit.tmpEdit_ft.visible;
        },
        // 单点登录模板编辑页配置
        ssoTmpEdit() {
            return this.getSsoConfig.tmpEdit || {};
        },
        // 单点登录模板预览页配置
        ssoTplPrvw() {
            return this.getSsoConfig.tplPrvw || {};
        },
        // 提交审批按钮是否显示
        approvalBtnVisible() {
            // 判断在模板预览页并且单点登录配置了显示
            return this.ssoTplPrvw.tplPrvw_1_apv && this.ssoTplPrvw.tplPrvw_1_apv.visible;
        },
        // 下一步按钮是否显示
        nextVisible() {
            // 判断在模板指定位置页，默认显示（单点登录控制隐藏）
            return !this.ssoTplPrvw.tplPrvw_1_send || this.ssoTplPrvw.tplPrvw_1_send.visible;
        },
        // 提交成功弹窗，查看记录按钮是否显示
        ssoDgViewRecordVisible() {
            return !this.ssoTplPrvw.tplPrvw_dg_btn_vwRcds || this.ssoTplPrvw.tplPrvw_dg_btn_vwRcds.visible;
        },
        // 提交成功弹窗，完成按钮是否显示
        ssoDgSentDoenVisble() {
            return !this.ssoTplPrvw.tplPrvw_dg_btn_done || this.ssoTplPrvw.tplPrvw_dg_btn_done.visible;
        },
        // 提交成功弹窗，完成按钮跳转链接
        doneBtnRedirectUrl() {
            return (this.ssoTplPrvw.tplPrvw_dg_btn_done && this.ssoTplPrvw.tplPrvw_dg_btn_done.url) ? this.ssoTplPrvw.tplPrvw_dg_btn_done.url : '/doc/list';
        },
        // 提交成功弹窗，提示文案
        sendedDialogTip() {
            if (this.ssoTplPrvw.tplPrvw_dg_txt_doneTip && this.ssoTplPrvw.tplPrvw_dg_txt_doneTip.text) {
                return this.ssoTplPrvw.tplPrvw_dg_txt_doneTip.text;
            } else {
                if (this.ssoDgViewRecordVisible) {
                    return '实际发送结果请查看模板发送记录';
                }
                return '';
            }
        },
    },
    methods: {
        ...mapMutations('template', ['setDocList', 'setCurrentDocIndex', 'setCurrentPageIndex', 'setReceivers', 'setWatermarkList', 'setRidingSealList']),
        toBack() {
            if (this.templateMatchId) {
                this.$router.push(`/template/match-result?resultId=${this.templateMatchId}`);
            } else {
                this.$router.push(`/template/use/prepare?templateId=${this.templateId}&dynamicMark=${this.tempData.templateCategory}`);
            }
        },
        toNext() {
            // 模板匹配过来不触发审批流
            if (this.templateMatchId) {
                return this.dialogCharge = true;
            }
            // 审批流
            this.getDefines()
                .then(res => {
                    this.definesData = res.data;
                    // 触发审批流程
                    if (this.definesData.length) {
                        this.dialogApproval = true;
                    } else { // 不触发审批流程，弹出计费弹窗
                        this.dialogCharge = true;
                    }
                })
                .catch(() => {});
        },
        // SAAS-2351 模板提交审批
        // http://wiki.bestsign.tech/pages/viewpage.action?pageId=11572533
        handleSubmitApproval() {
            this.loading = true;
            // 有混合云需求
            this.$hybrid.makeRequest({
                method: 'post',
                url: `${tempPath}/templates/${this.templateId}/convert-to-contracts_for_approval`,
                hybridTarget: '/templates/convert-to-contracts-for-approval',
                data: {
                    sendPlatform: 'WEB',
                    template: this.templateId, // 混3 在 data 中增加 templateId
                },
            })
                .then(() => {
                    this.dialogSendTips = true;
                })
                .catch(() => {})
                .finally(() => {
                    this.loading = false;
                });
        },
        // 接收到应用审批流消息，弹出计费弹窗
        onApplyApproval(commitFlow, defInfoId) {
            this.commitFlow = commitFlow;
            this.defInfoId = defInfoId;
            this.dialogApproval = false;
            this.dialogCharge = true;
        },
        postTemplateData() {
            if (this.templateMatchId) {
                return this.postSendTemplate({
                    resultId: this.templateMatchId,
                }, { noToast: 1 });
            } else {
                return this.postSendTemplate({
                    defInfoId: this.defInfoId,
                    flowStepList: this.commitFlow,
                }, { noToast: 1 });
            }
        },

        // 计费弹窗——点击确认
        onChargeConfirm() {
            this.loading = true;
            return this.postTemplateData()
                .then(() => {
                    this.dialogSendTips = true;
                })
                .catch(err => {
                    const msg = err.response.data.message;
                    this.$alert(msg, {
                        confirmButtonText: '确定',
                        customClass: 'field-error-alert',
                    }).then(() => {
                        this.$router.push('/template/list');
                    });
                })
                .finally(() => {
                    this.loading = false;
                    this.dialogCharge = false;
                });
        },
        // 查询模板信息接口
        getTemplateInfo() {
            return this.$http.get(`${tempPath}/dynamic-template/${this.templateId}/previews`);
        },
        // 获取审批流列表：
        getDefines() {
            return this.$http.get(`${tempPath}/templates/${this.templateId}/send-defines`);
        },
        // 发送
        postSendTemplate(data, opts) {
            const { operateMarkId } = this.$route.query;
            let url;
            if (this.templateMatchId) {
                url = `${tempPath}/templates/match/batch-convert-to-contracts`;
            } else {
                url = `${tempPath}/multiple-dynamic-signer/${this.templateId}/convert-to-contract${operateMarkId ? '?operateMarkId=' + operateMarkId : ''}`;
            }
            return this.$hybrid.makeRequestAdaptToPublic({
                method: 'post',
                url: url,
                data: {
                    sendPlatform: 'WEB',
                    ...data,
                },
                opts,
            });
        },
        // 是否为不在企业内网中的混合云用户
        notInLan() {
            return this.$store.getters.getHybridUserType === 'hybrid' && !this.$store.getters.getInLAN;
        },
        // 附件和文档扁平化
        formatData(documents) {
            return documents.reduce((total, doc) => {
                const attachmentList = doc.attachments || [];
                // labels的页码可能会被其他标签的插入影响变大，导致遍历时数据处理报错，这里取最大值
                const maxPageSize = Math.max(...((doc.labels || []).map(a => a.pageNumber)), doc.pageSize);
                // 文档的pageSize 取与附件的累加
                const totalPageSize = (attachmentList || []).reduce((totalPage, attachment) => {
                    // 根据总页数创建page数组
                    attachment.page = [];
                    for (let num = 0; num < attachment.pageNum; num++) {
                        attachment.page.push({});
                    }
                    attachment.totalPageSize = attachment.pageNum;
                    return totalPage += attachment.pageNum;
                }, maxPageSize);
                // 根据总页数创建page数组
                doc.page = [];
                for (let num = 0; num < maxPageSize; num++) {
                    doc.page.push({ marks: [] });
                }
                // labels转换
                doc.labels.forEach(item => {
                    doc.page[item.pageNumber - 1].marks.push(item);
                });
                total.push({
                    documentId: doc.documentId, // 或者attachmentId
                    fileName: doc.fileName,
                    pageSize: maxPageSize,
                    pdfUrl: doc.pdfUrl,
                    totalPageSize,
                    page: doc.page,
                });
                return total.concat([...attachmentList]);
            }, []);
        },
        handleResponseData(docData, inialData) {
            const initialDocData = cloneDeep(docData);
            const handledData = initialDocData.map((doc) => {
                doc.documentPages = new Array(doc.pageSize).fill({});
                const newArr = [];
                doc.documentPages.map((page, index) => {
                    newArr.push(Object.assign({}, page, {
                        width: inialData.maxWidth,
                        height: inialData.maxHeight,
                        imagePreviewUrl: `/template-api/templates/${this.templateId}/documents/${doc.documentId}/view/${index + 1}?_=${dayjs(new Date()).valueOf()}`,
                        highQualityPreviewUrl: `/template-api/templates/${this.templateId}/documents/${doc.documentId}/view/${index + 1}?_=${dayjs(new Date()).valueOf()}`,
                        pageNumber: index + 1,
                        documentName: doc.fileName,
                    }));
                });
                doc.documentPages =  newArr;
                return doc;
            });
            return handledData;
        },
        getDocInfo() {
            return this.$http.put(`${this.tempPath}/dynamic-template/${this.templateId}/decoration-preview`);
        },
        initDoc(resData) {
            resData.forEach((doc) => {
                doc.documentPages.forEach((page) => {
                    page.documentName = doc.documentName;
                    page.imagePreviewUrl = this.$hybrid.getContractImg(page.imagePreviewUrl);
                    page.highQualityPreviewUrl = this.$hybrid.getContractImg(page.highQualityPreviewUrl);
                });
                doc.image = doc.documentPages[0].imagePreviewUrl; // 缩略图只需 第一页
            });
            return resData;
        },
        // 右侧修改页数
        changePage(index) {
            const $scrollEl = document.querySelector('.point-position-doc-list');
            const docPages = document.querySelectorAll('.point-position-doc-pages')[this.currentDocIndex].children;
            const initY = document.querySelector('.point-position-doc-wrapper').offsetTop;
            const y = docPages[index].offsetTop * this.zoom + initY;
            scrollToYSmooth($scrollEl, y, 400, 'ease-out');
            setTimeout(() => {
                this.setCurrentPageIndex(index);
            }, 400);
        },
        // 切换文档
        changeDoc(docIndex) {
            this.setCurrentDocIndex(docIndex);
            this.$nextTick(() => {
                setTimeout(() => {
                    document.querySelector('.point-position-doc-list').scrollTo(0, 0);
                }, 400);
            });
        },
        // 初始化收件人数据
        initReceiversData(data) {
            return data.map((item) => {
                // 后端做了判断，直接取值
                const labelName = item.roleName;
                return {
                    ...item,
                    labelName,
                };
            });
        },
    },

    async created() {
        this.loading = true;

        // 判断混合云网络状态
        fieldLanBreak(() => {
            this.hybridContractServerConnected = false;
        });

        // 获取模板信息
        this.getTemplateInfo()
            .then(({ data }) => {
                // 对返回的数据进行格式化
                data.formatDocList = this.formatData(data.documents);
                this.tempData = data;
            })
            .catch(err => {
                console.log('err', err);
                this.$router.push('/template/list');
            }).finally(() => {
                this.loading = false;
            });
        // this.getRecivers().then(res => {
        //     this.receivers = res.data;
        // });
    },
};
</script>

<style lang="scss">
    .temp-field-page {
        user-select: none;
        position: relative;
        height: 100vh;
        font-size: 12px;
        color: #333;
        background-color: #f6f6f6;
        .content {
            height: calc(100% - 85px);
            width: 100%;
            margin: 50px auto;
            overflow: hidden;
        }
        .header {
            z-index: 100;
            position: fixed;
            top: 0;
        }
        .footer {
            position: fixed;
            bottom: 0;
        }

        // 审批流弹窗
        .dialog-approval {
            .el-dialog--small {
                width: auto;
                min-width: 409px;
            }
        }

        // 计费弹窗
        .dialog-charge {
            .el-dialog--small {
                width: 400px;
            }
            .el-dialog__header {
                display: none;
            }
            .el-dialog__body {
                padding: 0;
            }
        }

        // 立即使用弹窗
        .dialog-now-use {
            * {
                box-sizing: border-box;
            }
            .el-dialog {
                width: 333px;
                .el-dialog__header {
                    display: none;
                }
                .el-dialog__body {
                    padding: 0;
                    .icon-bg {
                        width: 333px;
                        height: 146px;
                        text-align: center;
                        padding-top: 30px;
                        i {
                            font-size: 66px;
                            color: #3591de;
                        }
                        div {
                            margin-top: 16px;
                        }
                    }
                    .btns {
                        padding: 13px 56px;
                        background-color: #fff;
                        border-top: 1px solid $border-color;
                        div {
                            float: left;
                            width: 100px;
                            height: 34px;
                            line-height: 34px;
                        }
                        div:first-child {

                        }
                        div:last-child {
                            margin-left: 20px;
                        }
                        div.singleBtn{
                            margin: 0 auto;
                            float: none;
                        }
                    }
                }
            }
        }

        // 发送成功提示弹窗
        .dialog-send-tips {
            .el-dialog .el-dialog__body .icon-bg i {
                font-size: 36px;
                color: #2baa3f;
            }
            p {
                font-size: 14px;
            }
            .p-top {
                margin-top: 10px;
            }
            .p-bottom {
                color: #999;
                margin-top: 10px;
            }
        }
    }
    .field-error-alert {
        p {
            white-space: pre-wrap;
            word-wrap: break-word;
        }
    }
</style>
