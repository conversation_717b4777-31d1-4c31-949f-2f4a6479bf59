<!-- 模板——合同信息组件 -->
<template>
    <div class="tmp-contractInfo-comp">
        <el-form class="clear">
            <el-form-item class="contract-name-item">
                <div class="lable">
                    模板名称
                    <el-tooltip
                        class="item"
                        effect="dark"
                        content="模板名称请不要包含特殊字符，且长度不超过100字"
                        placement="top"
                    >
                        <i class="el-icon-ssq-bangzhu cursor-point"></i>
                    </el-tooltip>
                </div>

                <el-input
                    v-model.trim="templateName"
                    :disabled="templateStatus === 'use'"
                    :maxlength="100"
                    @change="onTitleChange"
                >
                    <ElIDelete slot="icon" v-if="templateStatus === 'edit'"></ElIDelete>
                </el-input>
                <div class="contract-name-err err etc-sigle"
                    v-show="templateTitleErr"
                >
                    {{ templateTitleErr }}
                </div>
            </el-form-item>
            <el-form-item class="contracts-types"
                v-if="$store.state.commonHeaderInfo.userType == 'Enterprise' && contractsTypes.length && checkFeat.contractType"
            >
                <div class="lable">合同类型</div>
                <el-select v-model="contractTypeId" placeholder="请选择" popper-class="sign-el-select" :disabled="!canModifyWhenUsed">
                    <el-option
                        v-for="item in contractsTypes"
                        :key="item.contractTypeId"
                        :label="item.contractTypeName"
                        :value="item.contractTypeId"
                    >
                    </el-option>
                </el-select>
                <div class="err"></div>
            </el-form-item>
            <el-form-item>
                <div class="lable">
                    签约有效期(天)
                    <el-tooltip
                        class="item"
                        effect="dark"
                        content="如果合同在此日期前未完成签署，则无法继续签署"
                        placement="top"
                    >
                        <i class="el-icon-ssq-bangzhu cursor-point"></i>
                    </el-tooltip>
                </div>
                <el-input class="expire-days"
                    v-model="expireDays"
                    :disabled="!canModifyWhenUsed"
                    placeholder=""
                    :maxlength="9"
                >
                    <ElIDelete slot="icon" v-if="canModifyWhenUsed"></ElIDelete>
                </el-input>
                <div class="err"></div>
            </el-form-item>
        </el-form>
    </div>
</template>
<script>
import ElIDelete from 'components/el_i_delete/ElIDelete.vue';
import { resendTemplateMinxin } from 'src/mixins/resendTemplate.js';
import { mapGetters, mapState } from 'vuex';
export default {
    components: {
        ElIDelete,
    },
    mixins: [resendTemplateMinxin],
    inject: ['getDefaultConfig'],
    // eslint-disable-next-line vue/require-prop-types, vue/prop-name-casing
    props: ['value', 'templateStatus', 'templateName_useTem', 'expireDays_useTem', 'contractTypeId_useTem'],
    data() {
        return {
            templateName: '',
            templateTitleErr: '',
            expireDays: 30,
            contractsTypes: [],
            sendUserType: this.$store.state.commonHeaderInfo.userType,
            contractTypeId: '',
        };
    },
    computed: {
        ...mapState('template', ['canModifyWhenUsed']),
        ...mapGetters(['checkFeat']),
    },
    watch: {
        value(v) {
            this.templateTitleErr = v;
        },
    },
    methods: {
        onTitleChange() {
            if (/【|\[|】|\]]/.test(this.templateName)) {
                this.templateTitleErr = '模板名称不能包含方括号';
            } else if (this.templateName.length > 100) {
                this.templateTitleErr = '模板名称长度请不要超过100字';
            } else {
                this.templateTitleErr = '';
            }
            this.$emit('input', this.templateTitleErr);
        },
        /* 增加合同类型修正的逻辑：当id无法匹配时，增加尝试使用name进行匹配，再返回id */
        /* contractTypeName字段不一定会存在接口的返回数据中 */
        fixContractId(contractTypeId, contractsTypes, contractTypeName = null) {
            let __contractTypeId = null;
            const includes = contractsTypes.some(typeObj => {
                if (typeObj.contractTypeName === contractTypeName) {
                    __contractTypeId = typeObj.contractTypeId;
                }
                return typeObj.contractTypeId === contractTypeId;
            });
            return includes ? contractTypeId : (__contractTypeId || '10000'); // 1000为未分类
        },
        initTemInfo(infoDate) {
            let expireDays = infoDate.expireDays;
            const contractTypeName = infoDate.contractTypeName;
            let templateName = infoDate.templateName;
            let contractTypeId = infoDate.contractTypeId;
            if (this.templateStatus === 'use') {
                if (this.templateName_useTem) {
                    templateName = this.templateName_useTem;
                }
                if (this.expireDays_useTem) {
                    expireDays = this.expireDays_useTem;
                }
                if (this.contractTypeId_useTem) {
                    contractTypeId = this.contractTypeId_useTem;
                }
            }

            this.templateName = templateName;
            if (expireDays) {
                this.expireDays = expireDays;
            }
            if (contractTypeId) {
                this.contractTypeId = this.fixContractId(contractTypeId, this.contractsTypes, contractTypeName);
            } else {
                if (this.contractsTypes.length) {
                    this.contractTypeId = this.contractsTypes[0].contractTypeId;
                }
            }
        },
    },
    created() {
        let getInfoUrl = `${tempPath}/templates/${this.$route.query.templateId}`;
        getInfoUrl = this.handleRequestLink(getInfoUrl);
        const getInfo = this.$http.get(getInfoUrl);
        const getTypes = this.$http.get(`${signPath}/contract-type-new/`);
        // todo 获取通配符 api
        Promise.all([getInfo, getTypes, this.getDefaultConfig])
            .then(res => {
                const { expireDays } = res[2];
                expireDays && (this.expireDays = expireDays);

                const infoDate = res[0].data;
                this.contractsTypes = res[1].data;
                this.initTemInfo(infoDate);
            })
            .catch(() => {})
            .finally(() => {
                this.$emit('loaded');
            });
    },
};
</script>
<style lang="scss">
	.tmp-contractInfo-comp {
		box-sizing: border-box;
		padding-top: 17px;
        padding-bottom: 9px;
        padding-left: 28px;
		// background-color: #f4f4f4; // 跟ui商量下，这个颜色不好看
		background-color: #f8f8f8;
		// border: 1px solid $border-color;
		.el-form-item {
			float: left;
			.err {
				height: 14px;
				line-height: 18px;
				font-size: 12px;
				color: #f76b26;
				margin-top: -5px;
			}
            .lable{
                line-height: 18px;
                font-size: 12px;
                color: #666;
            }
            .el-input__inner {
                border-color: #ddd;
                border-radius: 2px;
            }
		}

		.contract-name-item {
			.el-input__inner {
				padding-right: 28px;
			}

		}
		label {
			font-size: 12px;
			color: #000;
		}
		.expire-days {
			margin-right: 2px;
		}
		.el-input {
			width: 300px;
			i {
				margin-top: 1px;
			}
		}
		.el-input__inner {
			height: 28px;
			font-size: 12px;
			padding-top: 4px;
			border: 1px solid #ccc;
			border-radius: 2px;
		}
		.el-date-editor--datetime {
			.el-input__inner {
				padding-top: 5px;
			}
		}
		.el-form-item {
			// width: 50%;
            margin-right: 20px;
			margin-bottom: 2px;
		}
		.el-form-item__content {
			font-size: 12px;
			// margin-left: 107px;
            line-height: 28px;
		}

		.contracts-types {
			.el-form-item__content {
				position: relative;
				// margin-bottom: 10px;

				.err{
					position: absolute;
                    left: 0;
                    top: 36px;
				}

				i {
					color: #666;
				}
			}
		}
	}
</style>
