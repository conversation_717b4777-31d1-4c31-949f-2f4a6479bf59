<!-- 签署——上传文件组件 -->
<template>
    <div class="temp-upload-items clear">
        <!-- uploaded -->
        <Draggable
            :class="{ 'clear' : templateStatus == 'use' && !canUseAttachment }"
            :list="docList"
            @end="resetOrder"
            :options="isUploading || templateStatus == 'use' ? {disabled:true} : {disabled:false}"
        >
            <template v-for="(doc, index) in docList.filter(doc => doc)">
                <!-- 选择了模版组合文档，只展示选中的 -->
                <div class="fl doc-item" v-if="filterSelect(doc.documentId)" :key="index" :class="{'clear': isNextLine(doc.documentId)}">
                    <div class="upload-item back-grey "
                        @mouseenter="doc.zoomMaskShow = true"
                        @mouseleave="doc.zoomMaskShow = false"
                    >
                        <div class="top" v-if="doc.uploadStatus == 1">
                            <i class="loading_icon el-icon-loading"></i>
                        </div>
                        <div class="top" v-if="doc.uploadStatus == 2">
                            <img :src="doc.imgSrc" alt="缩略图">
                            <div class="mask"
                                v-show="doc.zoomMaskShow"
                                @click="preview(doc)"
                            >
                                <i class="zoomIn_icon el-icon-view"></i>
                            </div>
                        </div>
                        <div class="bottom">
                            <p class="title">{{ doc.fileName }}</p>
                            <!-- 添加附件触发区域 -->
                            <el-upload
                                class="fl"
                                v-if="doc.uploadStatus == 2 && templateStatus == 'use' && canUseAttachment"
                                :action="accUploadUrl"
                                :headers="uploadHeaders"
                                :before-upload="beforeUpload.bind(null, index, 'attachment')"
                                :on-success="attOnUploadSuccess.bind(null, index)"
                                :on-error="onUploadError.bind(null,index, 'attachment')"
                                :http-request="handleUploadRequest.bind(null, -1, 'upload')"
                                :show-file-list="false"
                            >
                                <span class="attachment-text text-blue">添加附件</span>
                            </el-upload>
                            <el-tooltip
                                v-if="doc.uploadStatus == 2 && templateStatus == 'use' && canUseAttachment"
                                effect="dark"
                                placement="right"
                            >
                                <div slot="content">
                                    1.支持格式：PDF、doc、jpg、png、xls等<br />
                                    2.最多可上传10份附件，每份大小不超过10M<br />
                                    3.附件将和此文档并为一个PDF，总大小不超过16M<br />
                                </div>
                                <i class="el-icon-ssq-wenhao"></i>
                            </el-tooltip>

                            <p class="info" v-if="doc.uploadStatus==1">
                                <span class="status">正在上传...</span>
                            </p>
                            <div class="info" v-if="doc.uploadStatus == 2 && templateStatus == 'edit'">
                                <span v-show="!doc.zoomMaskShow" class="pages">{{ doc.pageSize }}页</span>
                                <p v-show="doc.zoomMaskShow">
                                    <i class="edit-icon icon-bold drag_icon el-icon-ssq-yidong">
                                        <span class="icon_toast">移动</span>
                                    </i>
                                    <i class="edit-icon icon-bold delete_icon el-icon-delete2" @click="deleteDoc(index)">
                                        <span class="icon_toast">删除</span>
                                    </i>
                                    <el-upload
                                        v-if="!isDynamicTemplate"
                                        class="edit-icon"
                                        :action="reUploadUrl"
                                        :headers="uploadHeaders"
                                        :before-upload="beforeReUpload.bind(null, index, doc)"
                                        :on-success="onReUploadSuccess"
                                        :on-error="onReUploadError.bind(null, index, doc.fileName)"
                                        :http-request="handleUploadRequest.bind(null, index, 'replace')"
                                        :show-file-list="false"
                                    >
                                        <i class="again_upload_icon el-icon-ssq-tihuan icon-bold">
                                            <span class="icon_toast">替换</span>
                                        </i>
                                    </el-upload>
                                </p>
                            </div>
                            <!-- 老逻辑，改版后已经不存在该场景 -->
                            <div class="info" v-if="doc.attachmentId && templateStatus == 'use'">
                                <span v-show="!doc.zoomMaskShow" class="pages">{{ doc.pageSize }}页</span>
                                <p v-show="doc.zoomMaskShow">
                                    <i class="edit-icon icon-bold drag_icon el-icon-ssq-yidong">
                                        <span class="icon_toast">移动</span>
                                    </i>
                                    <i class="edit-icon icon-bold delete_icon el-icon-delete2" @click="accDeleteDoc(index)">
                                        <span class="icon_toast">删除</span>
                                    </i>
                                    <el-upload
                                        class="edit-icon"
                                        :action="getReUploadAccUrl(doc.attachmentId)"
                                        :headers="uploadHeaders"
                                        :before-upload="beforeReUpload.bind(null, index, doc)"
                                        :on-success="onReUploadSuccess"
                                        :on-error="onReUploadError.bind(null, index, doc.fileName)"
                                        :http-request="handleUploadRequest.bind(null, index,'replace')"
                                        :show-file-list="false"
                                    >
                                        <i class="again_upload_icon el-icon-ssq-tihuan icon-bold">
                                            <span class="icon_toast">替换</span>
                                        </i>
                                    </el-upload>
                                </p>
                            </div>
                            <!--  -->
                        </div>
                    </div>
                    <div class="attachment" v-if="doc.tempAttachments && doc.tempAttachments.length > 0">
                        <div class="attachment-num">本合同下已添加 <span class="text-blue">{{ doc.tempAttachments.length }}</span> 份附件</div>
                        <ul>
                            <li v-for="(item,ind) in doc.tempAttachments" :key="ind" @click="preview(item)">
                                <div v-if="ind < 3 || (ind >= 3 && doc.attmore)">
                                    <i class="el-icon-ssq-fujian1"></i><span>{{ item.fileName }}{{ item.uploadStatus === 1 ? '上传中...' : '' }} </span><i class="el-icon-ssq-delete fr" @click.stop="accDeleteDoc(ind, doc)"></i>
                                </div>
                            </li>
                        </ul>
                        <div class="show-more" v-if="doc.tempAttachments.length > 3 && !doc.attmore" @click="doc.attmore=true">查看更多</div>
                    </div>
                </div>
            </template>
        </Draggable>

        <!-- uploadBtn 最多上传20个文件 -->
        <el-upload
            v-if="templateStatus == 'edit'"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :before-upload="beforeUpload.bind(null,0,'doc')"
            :on-success="onUploadSuccess"
            :on-error="onUploadError.bind(null,0, 'doc')"
            :http-request="handleUploadRequest.bind(null, -1, 'upload')"
            :show-file-list="false"
        >
            <div class="upload-item upload-btn" v-if="docList.length <= 49">
                <i class="upload_document_icon el-icon-ssq-shangchuanbendiwenjian"></i>
                <p>上传本地文件</p>
                <p v-if="isDynamicTemplate" class="upload-tip">仅支持上传.doc 或.docx格式</p>
            </div>
        </el-upload>
    </div>
</template>

<script>
import Bus from 'components/bus/bus.js';
import { checkDocumentUploadLimit, checkFileNameLength } from 'src/common/utils/fileLimit';
import Draggable from 'vuedraggable';
import { joinPathnameAndQueryObj } from 'utils/getQueryString.js';
import { resendTemplateMinxin } from 'src/mixins/resendTemplate.js';
import { prepareUploadRequest } from 'utils/hybrid/hybridBusiness.js';
import {  mapGetters } from 'vuex';
export default {
    components: {
        Draggable,
    },
    mixins: [resendTemplateMinxin],
    // eslint-disable-next-line vue/require-prop-types
    props: ['templateStatus', 'hybridServer', 'canUseAttachment', 'maxNameLength', 'selectedDocIds'],
    data() {
        return {
            contractId: this.$route.query.templateId || '',
            isUploading: 0,
            // 上传的url分为编辑时上传的接口以及使用是上传的附件的接口
            uploadUrl: this.templateStatus === 'edit' ? `${tempPath}/templates/${this.$route.query.templateId}/documents/` : `${tempPath}/templates/${this.$route.query.templateId}/attachment/`,
            uploadData: {
                order: 0,
            },
            reUploadUrl: this.templateStatus === 'edit' ? `${tempPath}/templates/${this.$route.query.templateId}/documents/replace` : `${tempPath}/templates/${this.$route.query.templateId}/`,   // 附件的替换，混合云需要请求混合云的接口
            reUploadData: {
                order: 0,
                documentId: '',
            },
            uploadHeaders: { Authorization: `bearer ${this.$cookie.get('access_token')}` },
            docList: [],
            accDocList: [],
            accUploadUrl: `${tempPath}/templates/${this.$route.query.templateId}/attachment`,
            docListLength: 0,     // 初始化 文档的长度，不包括附件的文档
            indexAccDoc: 0,        // 新增附件时需要用的索引 index
            isDynamicTemplate: !['STATIC', '', undefined, null].includes(this.$route.query.dynamicMark),
            hybridTargetMap: {
                'edit-upload': '/templates/documents/upload',
                'edit-replace': '/templates/documents/replace',
                'use-upload': '/templates/attachments/upload',
                'use-replace': '/templates/attachments/replace', // 已弃用
            },
        };
    },
    computed: {
        ...mapGetters(['checkFeat']),
        selectedDocList() {
            // 如果存在已选择的合同组合
            return this.selectedDocIds && this.selectedDocIds.length > 0
                ? this.docList.filter(doc => this.selectedDocIds.includes(doc.documentId)) : this.docList.filter(doc => doc);
        },
    },
    watch: {
        docList(value) {
            Bus.$emit('doc-List-Update', value);
        },
        canUseAttachment: {
            handler() {},
            deep: true,
            immediate: true,
        },
    },
    methods: {
        // 过滤选中的组合
        filterSelect(documentId) {
            return this.selectedDocIds.length === 0 || this.selectedDocIds.includes(documentId);
        },
        // 文档5个换行
        isNextLine(documentId) {
            let ind = 0;
            this.selectedDocList.forEach((doc, index) => {
                if (doc.documentId === documentId) {
                    ind = index;
                }
            });
            return (ind + 1) % 5 === 1;
        },
        // 获取附件替换的url
        getReUploadAccUrl(attachmentId) {
            const url = `${tempPath}/templates/${this.$route.query.templateId}/${attachmentId}/replace`;
            return url;
        },
        // 排序重置
        sortOrder() {
            this.docList.sort((a, b) =>  a.order > b.order);
        },
        // 合并一个新doc数据到docList
        // 兼容混1、混3逻辑，混3逻辑与公有云保持一致，主要差异在预览图地址从后端获取
        handleMergeDocToList(res) {
            const mergedDoc = this.$hybrid.tempPrepareMergeDoc(res, this.contractId);
            Vue.set(this.docList, mergedDoc.order, mergedDoc.docData);
        },
        accMergeDocToList(res, index) {
            const offsetDoclist = this.docListLength - 1;     // 将附件的文件列表合并到文件的列表  这个是相对于整体的一个偏移量 后面附件的order一次加上这个值，（附件的计数是从1开始，文档的是0开始）
            const order = index + offsetDoclist + 1;
            this.indexAccDoc = index + 1;    // 当前的附件index

            const mergedDoc = this.$hybrid.tempAttachmentMergeDocToList(res, this.contractId);
            Vue.set(this.docList, order, mergedDoc.docData);
        },
        // order洗牌，并提交给后端
        resetOrder() {
            const docListByOrder = [];
            this.docList.map((item) => {
                docListByOrder.push(item);
            });
            // 区分出附件和文本
            docListByOrder.map((item, index) => {
                return item.order = index;
            });

            const orders = docListByOrder.map((doc, index) => {
                return {
                    documentId: doc.documentId,
                    order: index,
                };
            });
            if (this.templateStatus !== 'use') {
                const url = this.isDynamicTemplate ? `${tempPath}/dynamic-template/${this.contractId}/document/order`
                    : `${tempPath}/templates/${this.contractId}/documents/orders`;
                this.$http({
                    url,
                    method: this.isDynamicTemplate ? 'PUT' : 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    data: JSON.stringify(orders),
                })
                    .then(() => {
                    });
            }
        },
        // 删除文件
        async deleteDoc(index) {
            const passRes = await this.$hybrid.offlineTip();
            if (!passRes) {
                return;
            }
            if (this.isUploading) {
                this.$MessageToast('请在上传完毕后删除');
                return;
            }
            if (this.docList[index].page && this.docList[index].page.some(item => item.marks && item.marks.length)) {
                this.$confirm('删除文件的同时会删除其字段，是否确认删除？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                }).then(() => {
                    this.submitDelDoc(index);
                });
            } else {
                this.submitDelDoc(index);
            }
        },
        // 删除模板附件
        async accDeleteDoc(index, doc) {
            const passRes = await this.$hybrid.offlineTip();
            if (!passRes) {
                return;
            }
            if (this.isUploading) {
                this.$MessageToast('请在上传完毕后删除');
                return;
            }
            this.submitAccDelDoc(index, doc);
        },
        submitDelDoc(index) {
            const url = this.isDynamicTemplate ? `${tempPath}/dynamic-template/${this.contractId}/document/${this.docList[index].documentId}`
                : `${tempPath}/templates/${this.contractId}/documents/${this.docList[index].documentId}`;
            this.$http({
                method: 'delete',
                url,
            })
                .then(() => {
                    this.docList.splice(index, 1);
                    this.resetOrder();
                });
        },
        submitAccDelDoc(index, doc) {
            const attachmentId = doc.tempAttachments[index].attachmentId;
            // 附件的删除全都走公有云
            this.$http.delete(`${tempPath}/templates/${this.contractId}/attachment/${attachmentId}`)   // `${tempPath}/templates/${this.contractId}/documents/${this.docList[index].documentId}`
                .then(() => {
                    doc.tempAttachments.splice(index, 1);
                });
        },
        // 上传文件
        // type: doc 文档 attachment 附件
        async beforeUpload(index, type, file) {
            const passRes = await this.$hybrid.offlineTip();
            if (!passRes) {
                return Promise.reject();
            }
            file = checkFileNameLength(file, this.maxNameLength, false);
            const size = this.checkFeat.bigFileSwitch && type === 'doc'  ? 50 : 10;
            const isPassLimit = checkDocumentUploadLimit(file, size);
            const hybridTarget = type === 'attachment' ? '/templates/attachments/upload' : '/templates/documents/upload';
            if (this.templateStatus === 'edit' && this.isDynamicTemplate && !['doc', 'docx'].includes(file.name.split('.')[1])) { // 动态模版仅支持word上传
                this.$MessageToast.error('动态模版仅支持上传.doc 或.docx格式');
                return Promise.reject();
            }
            // del
            if (type === 'attachment') {
                // 总附件不得超过10个
                const attachmentLength = this.docList.reduce(function(total, item) {
                    const lnt = (item.tempAttachments && item.tempAttachments.length) || 0;
                    return total + lnt;
                }, 0);

                if (!this.docList[index].tempAttachments) {
                    this.docList[index].tempAttachments = [];
                }

                if (attachmentLength >= 10)  {
                    this.$MessageToast.error('附件量已达上限');
                    return Promise.reject();
                }
            }
            const order = type === 'attachment' ? this.docList[index].tempAttachments.length : this.docList.length;
            if (isPassLimit) {
                const newDoc = {
                    order: order,
                    uploadStatus: 1,	// '0':初始化 '1':上传中 '2':上传成功
                    fileName: file.name,    // 文件名称
                };
                this.isUploading++;

                if (type === 'attachment')  {
                    this.docList[index].tempAttachments.push(newDoc);
                } else {
                    this.docList.push(newDoc);
                }
            } else {
                return Promise.reject();
            }

            this.uploadData = {
                order,
                templateId: this.contractId,
            };
            if (type === 'attachment') {
                this.uploadData.documentId = this.docList[index].documentId;
            }

            // 混合云
            if (this.hybridServer) {
                return new Promise((resolve, reject) => {
                    this.$hybrid.makeHeader({ url: this.uploadUrl, hybridTarget, method: 'POST', requestData: this.uploadData, isFormType: 1 })
                        .then(res => {
                            const resHeaders = res.data;
                            this.uploadHeaders = {
                                ...this.uploadHeaders,
                                ...resHeaders,
                            };
                            resolve();
                        })
                        .catch(() => {
                            reject();
                        });
                });
            }
        },
        attOnUploadSuccess(index, res) {
            this.isUploading--;
            const mergedDoc = this.$hybrid.tempAttachmentMergeDocToList(res, this.contractId);
            Vue.set(this.docList[index].tempAttachments, this.docList[index].tempAttachments.length - 1, mergedDoc.docData);
        },
        onUploadSuccess(res) {
            this.isUploading--;
            this.handleMergeDocToList(res || {});
            if (this.docList.length === 1) {
                this.$emit('setFilename', res.fileName);
            }
        },
        onUploadError(index, type, err) {
            const errMsg = err.code === 'ECONNABORTED' ? '请求超时' : err.response.data.message;
            this.isUploading--;
            if (err.code !== 'U-900') { // 避免和http拦截提示重复
                this.$MessageToast.error(errMsg);
            }
            if (type === 'attachment')  {
                this.docList[index].tempAttachments.splice(this.docList[index].tempAttachments.length - 1);
            } else {
                this.docList.splice(this.docList.length - 1, 1);
            }
        },

        // 替换文件
        async beforeReUpload(order, doc, file) {
            const passRes = await this.$hybrid.offlineTip();
            this.reUploadUrl = doc.attachmentId ? `${this.reUploadUrl}${doc.attachmentId}/replace/` : this.reUploadUrl;    // 附件的替换和文档的替换
            if (!passRes) {
                return Promise.reject();
            }
            file = checkFileNameLength(file, this.maxNameLength, false);
            const hybridTarget = doc.attachmentId ? '/templates/attachments/replace' : '/templates/documents/replace';
            const size = this.checkFeat.bigFileSwitch && hybridTarget.includes('documents')  ? 50 : 10;
            const isPassLimit = checkDocumentUploadLimit(file, size);
            if (isPassLimit) {
                this.docList[order].fileName = file.name;
                this.docList[order].uploadStatus = 1;
                this.isUploading++;
            } else {
                return Promise.reject();
            }

            this.reUploadData = doc.attachmentId ? {
                order,
                attachmentId: doc.attachmentId,
                templateId: doc.templateId,
            }
                : {
                    order,
                    documentId: this.docList[order].documentId,
                    templateId: doc.templateId,
                };

            // 混合云
            if (this.hybridServer) {
                return new Promise((resolve, reject) => {
                    this.$hybrid.makeHeader({ url: this.reUploadUrl, method: 'POST', hybridTarget, requestData: this.reUploadData, isFormType: 1 })
                        .then(res => {
                            const resHeaders = res.data;
                            this.uploadHeaders = {
                                ...this.uploadHeaders,
                                ...resHeaders,
                            };
                            resolve();
                        })
                        .catch(() => {
                            reject();
                        });
                });
            }
        },
        // todo... bug 后端返回需要和忠祥一致
        onReUploadSuccess(res) {
            this.isUploading--;
            if (res.attachmentId) {
                this.accMergeDocToList(res, res.order - 1);
            } else {
                this.handleMergeDocToList(res);
            }
            if (this.docList.length === 1) {
                this.$emit('setFilename', res.fileName);
            }
        },
        onReUploadError(index, orgName, err) {
            const errMsg = err.code === 'ECONNABORTED' ? '请求超时' : err.response.data.message;
            this.isUploading--;

            if (err.code !== 'U-900') { // 避免和http拦截提示重复
                this.$MessageToast.error(errMsg);
            }
            this.docList[index].fileName = orgName;
            this.docList[index].uploadStatus = 2;
            // this.docList.splice(index, 1);
        },
        handleUploadRequest(i, type, opts) {
            const url = type === 'upload' ? this.uploadUrl : this.reUploadUrl;
            const headers = this.uploadHeaders;
            const data = type === 'upload' ? this.uploadData : this.reUploadData;
            const hybridTarget = this.hybridTargetMap[this.templateStatus + '-' + type];

            opts.file = checkFileNameLength(opts.file, this.maxNameLength, true);

            if (this.$hybrid.isGamma()) {
                prepareUploadRequest({
                    hybridServer: this.hybridServer,
                    hybridTarget,
                    data,
                    headers,
                    opts,
                }).then(res => {
                    opts.onSuccess(res.data);
                }).catch(err => {
                    opts.onError(err);
                });
            } else {
                this.normalUploadRequest(url, data, opts, headers);
            }
        },
        normalUploadRequest(url, data, opts, headers) {
            // 特殊处理，后端需要
            url = joinPathnameAndQueryObj(url, data);

            // body
            const formData = new FormData();
            for (const k in opts.data) {
                const val = opts.data[k];
                formData.append(k, val);
            }
            if (this.templateStatus === 'use') {
                formData.append('templateId', this.contractId);
            }
            formData.append('file', opts.file);
            this.$http.post(url, formData, { headers, noToast: 1 })
                .then(res => {
                    if (this.templateStatus === 'use' && this.canUseAttachment) {
                        opts.onSuccess(res.data.result);
                    } else {
                        opts.onSuccess(res.data);
                    }
                })
                .catch(err => {
                    opts.onError(err);
                });
        },
        // 附件的上传

        // 预览文件
        async preview(doc) {
            if (doc.uploadStatus === 1) { // 文件正在上传
                return;
            }
            this.$emit('preview', doc);
        },

        // initUploadUrl
        initUploadUrl() {
            if (this.isDynamicTemplate) {
                this.uploadUrl = `${this.$store.state.commonHeaderInfo.hybridServer}${tempPath}/dynamic-template/${this.$route.query.dynamicMark === 'DYNAMIC' ? '' : 'v2/'}${this.contractId}/document`;
                this.reUploadUrl = `${this.$store.state.commonHeaderInfo.hybridServer}${tempPath}/dynamic-template/${this.contractId}/documents/replace`;
            } else if (this.templateStatus === 'edit') {
                this.uploadUrl = `${this.$store.state.commonHeaderInfo.hybridServer}${tempPath}/templates/${this.contractId}/documents`;
                this.reUploadUrl = `${this.$store.state.commonHeaderInfo.hybridServer}${tempPath}/templates/${this.contractId}/documents/replace`;
            } else {
                this.uploadUrl = `${this.$store.state.commonHeaderInfo.hybridServer}${tempPath}/templates/${this.contractId}/attachment/`;
                this.reUploadUrl = `${this.$store.state.commonHeaderInfo.hybridServer}${tempPath}/templates/${this.contractId}/`;
            }
        },
        getDocument() {
            let url = this.isDynamicTemplate ? `${tempPath}/dynamic-template/${this.contractId}/documents?isGenerateDocumentImages=1`
                : `${tempPath}/templates/${this.contractId}/documents?isGenerateDocumentImages=1`;
            url = this.handleRequestLink(url);
            return this.$http.get(url);
        },
        getAttachment() {
            return this.$http.get(`${tempPath}/templates/${this.contractId}/attachment`);
        },
    },
    created() {
        // 获取所有文件并展示
        this.getDocument()
            .then(res => {
                if (res.data) {
                    this.docListLength = res.data.length;
                }
                const list = (res.data || []).sort((a, b) => a.order - b.order); // fix CFD-12823 后端数据order有重复的（无法复现操作），根据order升序，然后order重复赋值。避免相同order展示不出来
                list.forEach((item, index) => {
                    item.attmore = false; // 默认附件超出3个显示更多
                    this.handleMergeDocToList({
                        ...item,
                        order: index,
                    });
                });
                this.initUploadUrl();
            })
            .catch(() => {})
            .finally(() => {
                this.$emit('loaded');
            });
    },
};
</script>

<style lang="scss">
	$item-width: 190px;
	.temp-upload-items {
		font-size: 12px;
		margin: 20px 0 6px;
		* {
			box-sizing: border-box;
		}
        .doc-item{
            display: inline-block;
            margin-right: 10px; // margin-right 会导致调换顺序的时候样式错乱
            margin-bottom: 20px;
            width: $item-width;
            .text-blue{
                color: #108EE8;
            }
            .attachment{
                .attachment-num{
                    padding: 4px 0;
                }
                li{
                    vertical-align: middle;
                    line-height: 20px;
                    cursor: pointer;
                    color: #333333;
                    &:hover{
                        background: #F4F9FC;
                        span{
                            color: #108EE8;
                        }
                        .el-icon-ssq-delete{
                            display: block;
                        }
                    }
                    span{
                        padding-left: 4px;

                        text-overflow: ellipsis;
                        display: inline-block;
                        width: 150px;
                        white-space: nowrap;
                        overflow: hidden;
                        vertical-align: middle;
                        line-height: 20px;

                    }
                    .el-icon-ssq-fujian1{
                        font-size: 8px;
                        vertical-align: middle;
                        line-height: 20px;
                        padding-left: 8px;
                    }
                    .el-icon-ssq-delete{
                        display: none;
                         cursor: pointer;
                        vertical-align: middle;
                        line-height: 20px;
                        color: #595959;
                        padding-right: 8px;
                    }

                }
                .show-more{
                    border-top: 1px solid #EEEEEE;
                    border-bottom: 1px solid #EEEEEE;
                    cursor: pointer;
                    text-align: center;
                    line-height: 22px;
                    &:hover{
                        color: #108EE8;
                    }
                }
            }
        }
		.upload-item {
			display: inline-block;
			width: $item-width;
			height: 225px;
			border: 1px solid #ddd;
			border-radius: $border-radius;
			text-align: center;
			vertical-align: top;
			&.back-grey{
				background-color: #f4f4f4;
			}
			i.upload_document_icon {
				margin-top: 58px;
				margin-bottom: 14px;
				color: #999;
				font-size: 50px;

			}
			i.loading_icon {
				color: #aaa;
				margin-top: 70px;
				font-size: 45px;
			}
			.top {
				position: relative;
				height: 170px;
				border-bottom: 1px solid $border-color;
				img {
					width: 152px;
					height: 152px;
					margin-top: 17px;
					border-left: 1px solid $border-color;
					border-top: 1px solid $border-color;
					border-right: 1px solid $border-color;
				}
				.mask {
					position: absolute;
					top: 0;
					left: -1px;
					width: $item-width;
					height: 100%;
					line-height: 170px;
					background-color: rgba(0,0,0,.7);
					cursor: pointer;
				}
				.zoomIn_icon {
					font-size: 24px;
					color: #fff;
				}
			}
			.bottom {
				padding-left: 10px;
				padding-right: 10px;
				text-align: left;
				.title {
					width: 100%;
					line-height: 30px;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
					color: #000;
				}
                .attachment-text{
                    font-size: 12px;
                    font-weight: bold;
                    padding-right: 4px;
                }
                .el-tooltip{
                    vertical-align: middle;
                    color:#666666;
                    font-size: 10px;
                }

				.info {
					color: #999;
				}
				.edit-icon {
					display: inline-block;
					position: relative;
					padding-right: 10px;
					font-size: 15px;
					cursor: pointer;
					&:hover span {
						display: block;
					}
				}
				.icon-bold {
					font-weight: 300;
				}
				.icon_toast {
					display: none;
					position: absolute;
					top: 21px;
					left: -15px;
					width: 46px;
					height: 23px;
					line-height: 23px;
					background-color: rgb(51, 51, 51);
					text-align: center;
					color: #fff;
					font-size: 12px;
					font-weight: bold;
					border-radius: 2px;
					&:before {
						content: '';
						position: absolute;
						top: -3px;
						left: 41%;
						width: 6px;
						height: 6px;
						background-color: rgb(51, 51, 51);
						transform: rotate(45deg);
					}
				}
			}
		}
		.upload-btn {
			border-style: dashed;
			font-size: 14px;
			color: #999;
            .upload-tip {
                font-size: 12px;
            }
			&:hover {
				i {
					color: #2298f1;
				}
				border-style: solid;
				color: #2298f1;
			}
		}
	}
</style>
