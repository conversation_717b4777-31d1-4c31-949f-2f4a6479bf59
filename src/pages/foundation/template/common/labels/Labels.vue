<template>
    <g :type="mark.type" :transform="translateMatrix(1,0,0,1, mark.x, mark.y)" style="cursor: pointer">
        <!-- <title>签署</title> -->

        <!-- 印章 -->
        <template v-if="mark.type == 'SEAL'">
            <!-- 由xxx盖xxx章 -->
            <template v-if="mark.status == 'INACTIVE'">
                <g>
                    <rect x="0"
                        y="-25"
                        :width="mark.width"
                        height="23"
                        fill="#fff7b6"
                        stroke="#959595"
                        stroke-width="1"
                    />
                    <text x="6" y="-9" fill="#333" font-size="12">由{{ mark.receiverName || '' }}盖{{ mark.contractName || '' }}章</text>
                    <text :x="mark.width-30" y="-9" fill="#1687dc" font-size="12" @click="handleClickCancel">取消</text>
                </g>
            </template>
            <template v-if="mark.status == 'CONFIRMED' && mark.parentReceiverId=='0'">
                <g v-show="isHeadShow" @click="handleClickSwitch" @mouseover="hasOverHead=1" @mouseleave="onMouseLeave('hasOverHead')">
                    <rect x="0"
                        y="-23"
                        :width="switchTabRectWidth"
                        height="23"
                        fill="#1274CC"
                        stroke="transparent"
                        stroke-width="0"
                    />
                    <svg class="icon switch" aria-hidden="true" :x="switchTabIconX" y="-18">
                        <use width="13" height="13" :xlink:href="`#el-icon-ssq-qiehuan`"></use>
                    </svg>
                    <text :x="switchTabTextX" y="-7" fill="#FFF" font-size="11">切换</text>
                </g>
            </template>
            <rect
                :fill="isLabelImageShow ? '#F7F8F9' : 'transperant'"
                fill-opacity="0.75"
                stroke="#127fd2"
                :stroke-dasharray="isLabelImageShow ? 3 : 0"
                :width="mark.width"
                :height="mark.height"
                rx="2"
                ry="2"
            >
            </rect>
            <image :width="mark.width"
                :height="mark.height"
                :xlink:href="isLabelImageShow ? selectedImgUrl : ''"
                @click="handleClickLabel"
                @mouseover="hasOverLabel=1"
                @mouseleave="onMouseLeave('hasOverLabel')"
            ></image>
            <svg v-if="!isLabelImageShow" class="icon" aria-hidden="true">
                <!-- <use :width="mark.width" :height="mark.height" :xlink:href="`#${style.type}`" style="pointer-events: none;"></use> -->
                <use :x="mark.width/2-style.iconWidth/2"
                    :y="mark.height/2-style.iconHeight/2"
                    :width="style.iconWidth"
                    :height="style.iconHeight"
                    :xlink:href="`#${style.type}`"
                    style="pointer-events: none;"
                ></use>
            </svg>
        </template>

        <!-- 签名 -->
        <template v-if="mark.type == 'SIGNATURE'">
            <!-- 转签 -->
            <template v-if="mark.status == 'ACTIVE' && receiver.userType == 'ENTERPRISE'">
                <g v-show="isHeadShow" @mouseover="hasOverHead=1" @mouseleave="onMouseLeave('hasOverHead')">
                    <rect x="0"
                        y="-25"
                        :width="mark.parentReceiverId=='0'?163:mark.width"
                        height="23"
                        fill="#fff7b6"
                        stroke="#959595"
                        stroke-width="1"
                    />
                    <text x="3" y="-9" fill="#333" font-size="12">点击此处签名</text>
                    <text v-if="mark.parentReceiverId=='0'" x="75" y="-9" fill="#333" font-size="12">或</text>
                    <text v-if="mark.parentReceiverId=='0'"
                        x="88"
                        y="-9"
                        fill="#1687dc"
                        font-size="12"
                        @click="handleClickTransfer"
                    >转给其他人签</text>
                </g>
            </template>
            <!-- 由xxx签名... -->
            <template v-if="mark.status == 'INACTIVE'">
                <g>
                    <rect x="0"
                        y="-25"
                        :width="mark.width"
                        height="23"
                        fill="#fff7b6"
                        stroke="#959595"
                        stroke-width="1"
                    />
                    <text x="4" y="-9" fill="#333" font-size="12">由{{ mark.receiverName || '' }}签名</text>
                    <text :x="mark.width-30" y="-9" fill="#1687dc" font-size="12" @click="handleClickCancel">取消</text>
                </g>
            </template>
            <!-- 切换 & 手写 tab -->
            <template v-if="mark.status == 'CONFIRMED'">
                <g v-show="isHeadShow" @mouseover="hasOverHead=1" @mouseleave="onMouseLeave('hasOverHead')">
                    <g @click="handleClickSwitch">
                        <rect x="0"
                            y="-23"
                            :width="switchTabRectWidth"
                            height="23"
                            fill="#1274CC"
                            stroke="transparent"
                            stroke-width="0"
                        />
                        <svg class="icon switch" aria-hidden="true" :x="switchTabIconX" y="-18">
                            <use width="13" height="13" :xlink:href="`#el-icon-ssq-qiehuan`"></use>
                        </svg>
                        <text :x="switchTabTextX" y="-7" fill="#FFF" font-size="11">切换</text>
                    </g>
                    <line :x1="mark.width/2-1"
                        y1="-18"
                        :x2="mark.width/2-1"
                        y2="-5"
                        stroke="#fff"
                        stroke-width="1"
                    />
                    <g @click="handleClickHandWrite">
                        <rect :x="mark.width/2-1"
                            y="-23"
                            :width="mark.width/2+1"
                            height="23"
                            fill="#1274CC"
                            stroke="transparent"
                            stroke-width="0"
                        />
                        <svg class="icon switch" aria-hidden="true" :x="mark.width/4*3-23" y="-18">
                            <use width="13" height="13" :xlink:href="`#el-icon-ssq-edit`"></use>
                        </svg>
                        <text :x="mark.width/4*3-5" y="-7" fill="#FFF" font-size="11">手写</text>
                    </g>
                </g>
            </template>
            <!-- 背景  -->
            <rect
                :fill="isLabelImageShow ? '#F7F8F9' : 'transperant'"
                fill-opacity="0.75"
                stroke="#127fd2"
                :stroke-dasharray="isLabelImageShow ? 3 : 0"
                :width="mark.width"
                :height="mark.height"
                rx="2"
                ry="2"
            >
            </rect>
            <!-- 签名图片 -->
            <image :width="mark.width"
                :height="mark.height"
                :xlink:href="isLabelImageShow ? selectedImgUrl : ''"
                @click="handleClickLabel"
                @mouseover="hasOverLabel=1"
                @mouseleave="onMouseLeave('hasOverLabel')"
            ></image>
            <!-- icon -->
            <svg v-if="!isLabelImageShow" class="icon" aria-hidden="true">
                <!-- <use :width="mark.width" :height="mark.height" :xlink:href="`#${style.type}`" style="pointer-events: none;"></use> -->
                <use :x="mark.width/2-style.iconWidth/2"
                    :y="mark.height/2-style.iconHeight/2"
                    :width="style.iconWidth"
                    :height="style.iconHeight"
                    :xlink:href="`#${style.type}`"
                    style="pointer-events: none;"
                ></use>
            </svg>
        </template>

        <!-- 日期 -->
        <template v-if="mark.type=='DATE'">
            <!-- <rect fill-opacity="0.75" stroke="#127fd2" :width="mark.width" :height="mark.height" rx="2" ry="2" @click="handleClickLabel"></rect> -->
            <text v-if="mark.type=='DATE'"
                x="10"
                y="20"
                fill="#333"
                style="pointer-events: none;"
                font-family="SimSun"
                font-weight="bold"
                font-size="18"
            >{{ mark.value || '签署日期' }}</text>
        </template>

    </g>
</template>
<script>
export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['mark', 'receiver'],
    data() {
        return {
            // isHeadShow: 1,
            hasOverLabel: 0,
            hasOverHead: 0,
        };
    },
    computed: {
        isLabelImageShow() {
            return this.mark.status === 'CONFIRMED' || this.mark.parentReceiverId > 0;
        },
        selectedImgUrl() { // todo: delete /zx/ done
            return `${this.mark.value}?access_token=${this.$cookie.get('access_token')}`;
        },
        style() {
            let style = {};
            switch (this.mark.type) {
                case 'SEAL':
                    style =  {
                        type: 'el-icon-ssq-gaizhang',
                        iconWidth: 200,
                        iconHeight: 130,
                    };
                    break;
                case 'SIGNATURE':
                    style = {
                        type: 'el-icon-ssq-qianming',
                        iconWidth: 100,
                        iconHeight: 70,
                    };
                    break;
            }
            return style;
        },
        switchTabRectWidth() {
            let width;
            if (this.mark.type === 'SEAL') {
                width = this.mark.width;
            } else if (this.mark.type === 'SIGNATURE') {
                width =  this.mark.width / 2;
            }
            return width;
        },
        switchTabIconX() {
            let width;
            if (this.mark.type === 'SEAL') {
                width = this.mark.width / 2 - 20;
            } else if (this.mark.type === 'SIGNATURE') {
                width = this.mark.width / 4 - 20;
            }
            return width;
        },
        switchTabTextX() {
            let width;
            if (this.mark.type === 'SEAL') {
                width =  this.mark.width / 2 - 3;
            } else if (this.mark.type === 'SIGNATURE') {
                width =  this.mark.width / 4 - 3;
            }
            return width;
        },
        isHeadShow() {
            return this.hasOverLabel || this.hasOverHead;
        },
    },
    methods: {
        translateMatrix(a, b, c, d, e, f) {
            return `matrix(${a},${b},${c},${d},${e},${f})`;
        },

        onLabelHover() {
            this.isHeadShow = 1;
        },
        onMouseLeave(key) {
            setTimeout(() => {
                this[key] = 0;
            }, 100);
        },

        handleClickTransfer() {
            this.$emit('click-transfer');
        },
        handleClickCancel() {
            this.$emit('click-cancel');
        },
        handleClickLabel() {
            this.$emit('click-label');
        },
        handleClickSwitch() {
            this.$emit('click-switch');
        },
        handleClickHandWrite() {
            this.$emit('click-handWrite');
        },
    },
};
</script>
<style lang="scss">
	.switch {
		color: #fff;
	}
</style>
