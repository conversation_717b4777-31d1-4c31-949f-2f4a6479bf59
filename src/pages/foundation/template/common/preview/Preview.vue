<!-- 合同文件预览 -->
<!-- todo：关闭按钮可抽象，三角形可抽象 -->
<template>
    <transition name="fade">
        <div v-show="isShow" :key="randomKey" class="prepare-preview">
            <div class="head">
                <span class="close thick" @click="isShow=false"></span>
            </div>
            <div class="content">
                <div class="title">{{ previewDoc.fileName }}</div>
                <div class="img-list" v-for="(item, index) in imgList" :key="index">
                    <img v-lazy="{ src: item.imgSrc, split: 3, index, total: imgList.length }" width="940" alt="">
                    <div class="page"></div>
                    <div class="num">{{ index+1 }}</div>
                </div>
            </div>
        </div>
    </transition>
</template>
<script>
import dayjs from 'dayjs';
export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['previewDoc', 'contractId', 'canUseAttachment'],
    data() {
        return {
            isShow: false,
            randomKey: Math.random(),
        };
    },
    computed: {
        imgList() {
            const list = [];
            const pages = this.previewDoc.page || [];
            if (!this.previewDoc.page) { // 动态模版接口不返回page信息
                for (let i = 0; i < this.previewDoc.pageSize; i++) {
                    list.push({
                        imgSrc: this.$hybrid.getContractImg(`/template-api/templates/${this.contractId}/documents/${this.previewDoc.documentId}/view/${i + 1}?_=${dayjs(new Date()).valueOf()}`),
                    });
                }
            } else {
                pages.forEach((item) => {
                    list.push({ imgSrc: this.$hybrid.getContractImg(item.highQualityPreviewUrl) });
                });
            }
            return list;
        },
    },
    methods: {
        open() {
            this.isShow = true;
            this.randomKey = Math.random();
        },
    },
};
</script>
<style lang="scss">
	.prepare-preview {
		z-index: 1001;
		position: fixed;
		top: 0; right: 0; bottom: 0; left: 0;
		background-color: rgba(0,0,0,0.6);
		overflow: scroll;
		.head {
			z-index: 999;
			position: fixed;
			top: 0;
			width: 100%;
			height: 50px;
			background-color: #000;
			.close {
				z-index: 999;
				position: absolute;
				top: 15px;
				right: 30px;
				display: inline-block;
				width: 20px;
				height: 20px;
				overflow: hidden;
				cursor: pointer;
			}
			.thick::before, .thick::after {
				height: 4px;
				margin-top: -2px;
			}
			.thick::before, .thick::after {
				content: '';
				position: absolute;
				height: 2px;
				width: 100%;
				top: 50%;
				left: 0;
				margin-top: -1px;
				background: #fff;

			}
			.thick::before {
				transform: rotate(45deg);
			}
			.thick::after {
				transform: rotate(-45deg);
			}

		}
		.content {
			width: 1000px;
			background-color: #EAEBED;
			border-radius: 4px;
			margin: 0 auto;
			margin-top: 50px;
			.title {
				height: 60px;
				line-height: 60px;
				font-size: 24px;
				text-align: center;
			}
			.img-list {
				position: relative;
				padding: 0 30px 18px;
				text-align: center;
				.page {
					position: absolute;
					right: 30px;
					bottom: 21px;

					// background-color: #8D8E8F;

					width: 0;
					height: 0;
					border-color: #8D8E8F transparent;
					border-width: 0px 0px 42px 42px;
					border-style: solid;
				}
				.num {
					position: absolute;
					bottom: 29px;
					right: 18px;
					font-size: 9px;
					color: #fff;
					padding-right: 20px;
				}
			}
		}
	}
</style>
