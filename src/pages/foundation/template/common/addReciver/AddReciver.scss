.temp-add-recipient {
    position: relative;
    font-size: 12px;
    color: #666;
    .red {
        color: #FF6432;
    }

    // 编辑区
    .edit {
        height: 16px;
        font-size: 14px;
        padding: 19px 24px 7px 20px;
    }
    .left {
        float: left;
        .el-checkbox__input {
            cursor: pointer;
            .el-checkbox__inner { // 打勾大小
                width: 16px;
                height: 16px;
                border-color: $border-color;
                border-radius: 2px;
            }
        }
        .el-checkbox__label {
            padding-left: 12px;
            font-size: 12px;
            &:hover {
                color: #1687dc;
            }
        }
    }
    .right {
        float: right;
        color: #666;
        span {
            font-size: 12px;
            cursor: pointer;
            i {
                position: relative;
                top: 1px;
                font-size: 16px;
                color: #999;
            }
            &:hover {
                color: #1687dc;
                i {
                    color: #1687dc;
                }
            }
        }
        .address-list {
            border-right: 1px solid #666;
            padding-right: 12px;
        }
        .order-view {
            padding-left: 15px;
        }
    }

    .recipient-li {
        * {
            box-sizing: border-box;
        }
        box-sizing: border-box;
        position: relative;
        width: 100%;
        background-color: #f9f9f9;
        margin-top: 12px;

        &:hover {
            background-color:#F3F7FA;
            .receiverList-li-top {
                background-color: #EBF3F9;
            }
            .recipient-delete {
                .delete-btn {
                    display: block;
                    color: #D2CED5;
                }
            }
            .morelist-container {
                .morelist {
                    background-color: #f1f6fa;
                }
            }
        }


        .morelist-container{
            padding: 0 25px;
            .annex-list {
                .moreList-content {
                    .el-input {
                        width: auto;
                    }
                    .input-name input {
                        width: 175px;
                    }
                    .input-desc input {
                        width: 370px;
                    }
                    label {
                        color: #333;
                    }
                }
                .add-btn {
                    color: #127fd2;
                    margin-bottom: 20px;
                    display: inline-block;
                    cursor: pointer;
                }
            }
            // 更多 附件
            .morelist-annex-list-item {
                display: inline-block;
                margin-right: 10px;
                padding-bottom: 11px;
                vertical-align: top;
                font-size: 12px;
            }
            .annex-item {
                .el-icon-ssq-delete {
                    position: relative;
                    left: 40px;
                    top: 27px;
                    color: #ccc;
                    cursor: pointer;
                    &:hover {
                        color: #999;
                    }
                }
            }
            .morelist-annex-list-item-top,
            .morelist-annex-list-item-bottom {
                display: inline-block;
            }
            .morelist-annex-list-item-top {
                height: 19px;
                color: #999;
            }
            .morelist-annex-list-item-name {
                width: 175px;
            }
            .morelist-annex-list-item-desc {
                width: 370px;
            }
        }
        //
        .receiverList-li-bottom-item {
            .el-input__inner::placeholder{
                color:#999;
            }
            .is-disabled .el-input__inner,
            .is-disabled .el-input__inner::placeholder{
                color: #C2D2DE;
            }
        }
        .receiverList-li-bottom {
            .el-input__inner {
                border-color: #ddd;
            }
        }
        .receiverList-li-bottom__icons {
            position: absolute;
            top: 19px;
            left: 20px;
        }
        .receiverList-li-bottom-item {
            display: inline-block;
            margin-right: 10px;
            margin-top: 17px;
            vertical-align: top;
            font-size: 12px;
        }
        .receiverList-li-bottom-item-top,
        .receiverList-li-bottom-item-bottom {
            display: inline-block;
        }
        .receiverList-li-bottom-item-top {
            height: 19px;
            color: #666666;
            .grey {
                color: #999999;
            }
        }
        .receiverList-li-bottom-item-bottom__error {
            position: absolute;
            color: #f76b26;
        }
        .receiverList-li-bottom-item-bottom__tips {
            position: absolute;
            color: #999;
        }
        .receiverList-li-bottom-item__account {
            width: 280px;
            position: relative;
        }
        .recipient-useraccout {
            width: 280px;
            height: 30px;
            input {
                padding-right: 25px;
            }
        }
        .recipient-useraccout-suffix {
            position: absolute;
            right: 4px;
            top: 25px;
            font-size: 14px;
            color: #FFFFFF;
            background: #68ADF1;
            width: 18px;
            height: 18px;
            line-height: 18px;
            text-align: center;
            transform: scale(0.7);
            border-radius: 2px;
        }
        .recipient-useraccout-proxy {
            position: absolute;
            left: 0px;
            top: 15px;
            padding: 10px 140px 0 10px;
            span {
                background: #DAF0FF;
                border: 1px solid #92C4EB;
                border-radius: 2px;
                border-radius: 2px;
                display: inline-block;
                padding: 0 4px;
            }
            .el-icon-ssq-delete {
                padding-left: 7px;
                color: #ccc;
                cursor: pointer;
                &:hover {
                    color: #999;
                }
            }
        }
        .receiverList-li-bottom-item__roleName {
            width: 144px;
        }
        .recipient-single-code {
            width: 144px;
        }
        .receiverList-li-bottom-item__person-username {
            width: 144px;
        }
        .recipient-username {
            width: 144px;
        }
        .receiverList-li-bottom-item__ent-companyname {
            width: 240px;
        }
        .recipient-companyname {
            width: 240px;
        }
        .receiverList-li-bottom-item__ent-username {
            width: 144px;
        }
        .recipient-username {
            width: 144px;
        }
        .receiverList-li-bottom-item__notification {
            width: 144px;
        }
        .recipient-notification {
            width: 144px;
        }
        .receiverList-li-bottom-item__idNumber {
            width: 144px;
        }
        .recipient-idNumber {
            width: 144px;
        }
        // 固定签约主体 账号
        .sign-principal-account {
            .switchDetail {
                .el-checkbox__inner {
                    margin-top: -2px;
                }
                .el-checkbox__label {
                    padding-left: 2px;
                }
                color: #999;
            }
        }

        &.recipient-li-holder {
            .addrecipient-more-list {
                background: #f4f5f8;
            }
        }
        &.recipient-li_disabled {
            .receiverList-li-top, input, textarea, button, .recipient-li, .sign-principal-account, .sign-principal-body, .agent, .recipient-single-code-name, .addrecipient-more-list {
                background: #F7F7F7;
                color: #CCCCCC;
            }
            &:hover, .morelist:hover, .addrecipient-more-list:hover, .moreList-content:hover {
                background: #F7F7F7;
            }
        }
        // 使用模板 当前固定签署方是否参与改合同的签署
        .console-link {
            position: absolute;
            top: 42%;
            top: calc(50% - 10px);
            right: 37px;
            .switch-btn {
                margin-right: 5px;

                .chooseBtn {
                    display: none;
                }

                .choose-label {
                    width: 31px;
                    height: 12px;
                    display: inline-block;
                    border-radius: 20px;
                    position: relative;
                    background-color: #989898;
                }

                .choose-label:before {
                    cursor: pointer;
                    box-shadow: #ccc -1px 2px 3px 1px;
                    content: '';
                    position: absolute;
                    left: 0;
                    top: -2px;
                    width: 16px;
                    height: 16px;
                    display: inline-block;
                    border-radius: 20px;
                    background-color: #fff;
                    z-index: 20;
                    -webkit-transition: all 0.5s;
                    transition: all 0.5s;
                }

                .chooseBtn:checked+label.choose-label:before {
                    left: 16px;
                    background-color: #2298f1;
                }

                .chooseBtn:checked+label.choose-label {
                    background-color: #aadaff;
                }
            }
        }

        .card-container {
            position: relative;
            padding: 0 80px 36px 60px;
            font-size: 0;
            &.padding-right-24 {
                padding-right: 24px;
            }
        }
        // element
        .el-input__inner {
            height: 30px!important;
            font-size: 12px;
            // padding: 10px;
            border-radius: 2px;
        }
        .el-select {
            border-radius: 2px;
        }
        // 顺序签署 数字
        .recipient-order {
            margin-right: 5px;
            input {
                width: 30px;
                height: 30px;
                line-height: 30px;
                text-align: center;
                font-size: 12px;
                color: #666;
                background-color: #fff;
                border: 1px solid #ddd;
            }
        }
        // 顺序签署 拖拽
        .recipient-handle {
            margin-right: 5px;
            padding-top: 6px;
            cursor: ns-resize;
            font-size: 18px;
        }
        .recipient-photo {
            width: 30px;
            height: 30px;
            i {
                width: 30px;
                height: 30px;
                line-height: 30px;
                font-size: 30px;
                color: #E9E9E9;
                text-align: center;
                background-color: #CCCCCC;
                border-radius: 2px;
            }
            img {
                border-radius: 2px;
            }
        }

        .recipient-userType {
            width: 100px;
            margin-left: 10px;
            i {
                color: #777;
            }
        }

        .mult-import-tips {
            padding-top: 25px;
            color: #999;
            i {
                font-size: 14px;
            }
        }
        // 删除按钮
        .recipient-delete {
            position: absolute;
            top: 40px;
            right: 20px;
            width: 43px;
            height: 70px;
            line-height: 74px;
            &:hover {
                .delete-btn {
                    display: block;
                    color: #999;
                }
            }
            .delete-btn {
                display: none;
                margin-top: 30px;
                margin-left: 8px;
                font-size: 13px;
                font-weight: bold;
                color: #bbb;
                cursor: pointer;
            }
        }
    }
// 签约方卡片上半部分
.receiverList-li-top {
    position: relative;
    padding-top: 7px;
    padding-left: 20px;
    padding-right: 20px;
    height: 32px;
    background: #F5F5F5;
}
.receiverList-li-top-title {
    display: inline-block;
    color: #666;
}
.receiverList-li-top-selects {
    position: absolute;
    top: 0;
    right: 29px;
    .el-input__inner {
        background: transparent;
        border: none;
        text-align: right;
        color: inherit;
    }
    .el-input.is-disabled .el-input__inner {
        background: transparent;
    }
    div.el-select:hover .el-input__inner {
        box-shadow: none;
    }
    .el-button {
        &:hover {
            color: #127fd2;
        }
        &.is-disabled {
            color: #C2D2DE;
        }
    }
    .el-select .el-input {
        &:hover {
            .el-icon-caret-top,
            .el-input__inner {
                color: #127fd2;
            }
        }
        &.is-disabled {
            .el-icon-caret-top,
            .el-input__inner {
                color: #C2D2DE;
            }
        }
        // Safari input disabled样式变浅
        input:disabled{
            opacity: 1;
            -webkit-text-fill-color: #C2D2DE;
        }
    }
}

// 账号前台代收相关样式
.receiverListUseAccountReceptionCollection__common,
.receiverListUseAccountReceptionCollection__Collection {
    cursor: pointer;
}
.receiverListUseAccountReceptionCollection__line {
    padding-left: 5px;
    padding-right: 5px;
}

// 是否需要实名
.recipient-needverified {
    width: 100px;
    i {
        color: #777;
    }
}
.recipient-needverified__ent {
    width: 140px;
    i {
        color: #777;
    }
}
// 更多，下拉框设置
.recipient-more {
    margin-left: 20px;
    button {
        width: 66px;
        height: 32px;
        font-size: 12px;
        background-color: transparent;
        color: #666;
        padding: 0;
        border: none;
        .el-icon--right {
            margin-left: 10px;
            color: #888;
        }
    }
}
// 签署/抄送 设置
.recipient-signtype {
    width: 106px;
    margin-left: 10px;
    i {
        color: #777;
    }
}

    // 顺序打勾时
    .recipient-li.order-sign {
        .card-container {
            padding-left: 117px;
        }
    }

    // 添加私信
    .privateLetter-footer {
        color: #999;
        padding: 8px 0 16px;
    }

    // 刷脸签署
    .face-sign .moreList-content, .manual-sign .moreList-content, .notification .moreList-content {
        padding-bottom: 25px;
    }
    // moreList
    .moreList-content {
        margin-right: 15px;
    }
    .moreList-content {
        textarea {
            font-size: 12px;
            border-radius: 1px;
            border-color: #ddd;
        }
        input {
            width: 434px;
            height: 26px;
            color: #666;
            border: 1px solid #ddd;
            border-radius: 1px;
        }
        input.input-notification {
            width: 216px;
            height: 30px;
            padding: 10px;
            border-radius: 2px;
            margin-left: 0;
        }
        .notification-err {
            color: #f76b26;
        }
        i {
            font-size: 13px;
        }
    }

    // // 联系人地址簿
    // .addressBookDialog {
    //     .el-dialog {
    //         width: 900px;
    //     }
    //     .el-dialog__header {
    //         padding: 25px 30px;
    //         border-bottom: 1px solid #eee;
    //         .el-dialog__title {
    //             font-weight: normal;
    //         }
    //     }
    //     .el-dialog__body {
    //         padding: 0;
    //     }
    //     .el-dialog__footer {
    //         padding: 12px 33px 15px;
    //         border-top: 1px solid #eee;
    //     }

    //     .addressBookDialog-btn {
    //         button {
    //             width: 100px;
    //             height: 34px;
    //             padding: 0;
    //             background-color: #127fd2;
    //             font-weight: normal;
    //         }
    //     }

    // }

    // 查看签署顺序
    .SignOrderDialog {
        * {
            box-sizing: border-box;
        }

        .el-dialog__header{
            height: 65px;
            border-bottom: 1px solid #eee;
            border-radius: 2px 2px 0 0;
            background: #fff;
        }

        .el-dialog{
            background: #f6f9fc;
        }
    }

    // 防止恶意输入对话框
    .imgVaDialog {
        .el-dialog {
            width: 280px;
        }
        .el-dialog__header {
            padding: 25px 30px;
            border-bottom: 1px solid #eee;
            .el-dialog__title {
                font-weight: normal;
            }
        }
        .el-dialog__body {
            padding: 27px 30px;
            background-color: #f6f9fc;
            .el-input {
                width: 158px;
                input {
                    height: 26px;
                }
            }
            .picture-verify {
                width: 55px;
                height: 26px;
                margin-top: -1px;
            }
        }
        .el-dialog__footer {
            padding: 12px 33px 15px;
            background-color: #f6f9fc;
        }
        .imgVaDialog-btn {
            button {
                width: 100px;
                height: 34px;
                padding: 0;
                background-color: #127fd2;
                font-weight: normal;
            }
        }
    }

    // excel报错弹窗
    .excelErrorDialog {
        width: 100%;
        overflow: scroll;

        .el-dialog{
            width: auto;
            max-width: 100%;
        }
        .el-dialog__body {
            background-color: #fff;
            max-height: 500px;
            overflow: auto;
        }
        .excel-error-line {
            line-height: 24px;
            color: #333;
        }
        .el-table {
            margin-top: 12px;
        }
        .el-dialog--small {
            width: auto;
        }
        .el-dialog__footer {
            padding: 0 33px 25px;
            button {
                background-color: #127fd2;
            }
        }
    }

    // 对话框：提取签约方
    // .pickDialog {
    //     .el-dialog {
    //         width: 410px;
    //     }
    //     ul {
    //         list-style: disc;
    //         margin-left: 19px;
    //         li {
    //             font-size: 14px;
    //             color: #333;
    //             margin-bottom: 10px;
    //         }
    //     }
    //     .pick-add-btn {
    //         float: right;
    //         color: #1787dd;
    //         cursor: pointer;
    //     }
    // }

    .card-tab{
        border-bottom: 2px solid #ddd;
        display: flex;
        .card-tab-item{
            flex:1;
            height: 42px;
            line-height: 42px;
            color: #666;
            font-size: 14px;
            text-align: center;
            background: #fbfbfb;
            cursor: pointer;

            .nums{
                color: #999;
                font-size: 12px;
            }

            &.active{
                background: #f1f6fa;
                color: #1280d2;
            }
            &.is-disabled{
                // background: #f1f6fa;
                color: #C2D2DE;
                cursor: not-allowed;
            }
        }
    }

    // 使用模板 签署方批量导入
    .use-add-btns{
        // border-top: 1px dashed #ddd;
        padding: 15px 0 11px;
        text-align: center;
        .edit-icon{
            display: inline-block;
            vertical-align: middle;
            .ssq-btn-confirm{
                width: 165px;
                height: 30px;
                line-height: 30px;
                border-radius: 4px;
            }
        }
        .use-add-download{
            margin-top: 10px;
        }
    }
    .excel-import {
        padding-top: 10px;
        .el-icon-ssq-tongguo{
            color: #2baa3f;
            font-size: 16px;
        }
        .excel-msg {
            margin-left: 4px;
            color: #999;
            font-size: 14px;
        }
        .excel-import-clear {
            color: #127fd2;
            margin-left: 5px;
            cursor: pointer;
        }
    }
    .sign-excel-more {
        color: #127fd2;
        font-size: 14px;
        height: 40px;
        line-height: 40px;
        margin-left: 100px;
        border-left: 1px solid #ccc;
        padding-left: 75px;
        cursor: pointer;
    }
    .sign-excel-more-special {
        padding-left: 46px;
    }
    // 编辑模板，添加固定和可变签约主体按钮部分
.add-subject-btns {
    margin-top: 20px;
    height: 184px;
    font-size: 0;
    * {
        box-sizing: border-box;
    }
}
.add-subject-btns-fixed__default-none,
.add-subject-btns-variable__default-none {
    display: none;
}
.add-subject-btns-fixed,
.add-subject-btns-variable {
    display: inline-block;
    vertical-align: top;
    font-size: 14px;
    cursor: pointer;
    &:hover {
        .add-subject-btns-fixed__default-none,
        .add-subject-btns-variable__default-none {
            display: block;
        }
        .add-subject-btns-fixed__main,
        .add-subject-btns-variable__main {
            border-bottom-style: solid;
            border-bottom-color: #eee;
        }
    }
}
.add-subject-btns-fixed__main,
.add-subject-btns-variable__main {
        width: 494px;
        height: 44px;
        background-color: #fafafa;
        border: 1px dashed #ddd;
        line-height: 44px;
        text-align: center;
        color: #127fd2;
        i {
            transition: transform 0.5s;
        }
        &:hover {
            background-color: #f1f6fa;
            i {
                transform: rotate(180deg);
            }
        }
    }
    .add-subject-btns-fixed__main {
        margin-right: 12px;
    }
    .add-subject-btns-fixed__personal,
    .add-subject-btns-fixed__enterprise,
    .add-subject-btns-variable__personal,
    .add-subject-btns-variable__enterprise {
        width: 494px;
        height: 70px;
        background-color: #fff;
        border: 1px solid #eee;
        border-top: none;
        text-align: center;
        &:hover {
            background-color: #fafafa;
            &, .add-subject-btns-variable__description-one {
                color: #1787DC;
            }
        }
        .el-icon-ssq-jia {
            color: #1787DC;
        }
    }
    .add-subject-btns-fixed__personal,
    .add-subject-btns-fixed__enterprise {
        line-height: 70px;
     }
     .add-subject-btns-variable__personal,
     .add-subject-btns-variable__enterprise {
        padding-top: 16px;
     }
    .add-subject-btns-fixed__enterprise,
    .add-subject-btns-variable__enterprise  {
        border-bottom-left-radius: 4px;
        border-bottom-right-radius: 4px;
    }
    .add-subject-btns-variable__description-con {
        display: inline-block;
        vertical-align: middle;text-align: left;
    }
    .add-subject-btns-variable__description-one {
        display: block;
        color: #333;
    }
    .add-subject-btns-variable__description-two {
        font-size: 12px;
        color: #ccc;
    }

    .add-subject-btns-variable__disable {
        cursor: not-allowed;
        .add-subject-btns-variable__main {
            color: #C2D2DE;
        }
        .add-subject-btns-variable__disable-info {
            display: none;
            padding-top: 25px;
            padding-left: 60px;
            width: 494px;
            height: 100px;
            border: 1px solid #ddd;
            border-bottom-left-radius: 4px;
            border-bottom-right-radius: 4px;
            border-top: none;
            color: #999;
        }
        &:hover {
            .add-subject-btns-variable__disable-info {
                display: block;
            }
        }
    }
}

.receiver-autocomplete-popper {
    .el-autocomplete-suggestion__list {
        li:first-of-type {
            font-size: 12px;
            color: #ccc;
            pointer-events: none;
            cursor: not-allowed;
        }
    }
}
.excel-error-toast {
    .el-message-box__content {
        overflow: auto;
        max-height: 120px;
    }
    p {
        white-space: pre;
        overflow-x: auto;
    }
}

.temp-add-tip-msgbox {
    .el-dialog {
        width: 500px;
    }
}

.temp-receiverList-autoSave-notify{
    width: 160px;
    padding: 5px;
    right: 0;

    .el-icon-ssq-tongguo{
        top: 2px;
        line-height: 40px;
        text-align: center;
        font-size: 20px;
        color: #2baa3f;
        vertical-align: middle;
    }

    .el-notification__group{
        margin-left: 35px;

        .el-notification__content{
            color: #666;
        }
        .el-icon-close{
            display: none;
        }
    }
}



.excelErrorDialog.ssq-dialog {
    div.cell-red {
        // color: red !important;
        background: #ff7875;
    }
    p.el-popover--tips {
        color: #000000 !important;
    }
    .el-table td, .el-table th {
        .cell {
            padding: 0;
            text-align: center;
            span {
                display: block;
                height: 40px;
                width: 100%;
                div {
                    height: 40px;
                    line-height: 40px;
                    text-align: center;
                }
            }
            div {
                text-align: center;
            }
        }
    }
}
