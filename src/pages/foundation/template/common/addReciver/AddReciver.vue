<!-- 模板——添加收件人组件 -->
<template>
    <div class="temp-add-recipient">
        <div class="edit">
            <ul>
                <li class="left">
                    <el-checkbox name="type"
                        label="顺序签署"
                        v-if="templateStatus == 'edit'"
                        v-model="signOrdered"
                        @change="clickSwitchOrder"
                    >
                    </el-checkbox>
                </li>
                <li class="right">
                    <span class="address-list"
                        v-if="templateStatus == 'edit'"
                        @click="clickAdressBook"
                    >
                        <i class="iconfont el-icon-ssq-dizhibao"></i>&nbsp;联系人地址簿
                    </span>
                    <span class="order-view"
                        @click="clickSignOrderBtn"
                    >
                        <i class="iconfont el-icon-ssq-xitong-zuzhijiagou"></i>&nbsp;签署顺序
                    </span>
                </li>
            </ul>
        </div>

        <!-- 签约人列表 -->
        <ReceiverList
            :templateStatus="templateStatus"
            :ordered="signOrdered"
            :isDynamicTemplate="isDynamicTemplate"
            :noticeSpecificUser="noticeSpecificUser"
            :notAllowNotify="notAllowNotify"
            @loaded="handleLoaded"
            @updateTemInfo="handleUpdateTemInfo"
            @showDataBoxInvite="showDataBoxInviteDialog = true"
            ref="receiverList"
        ></ReceiverList>

        <DialogAddressBook
            v-if="addressBookDialogVisible"
            :visible.sync="addressBookDialogVisible"
            :addressBookSelectType="addressBookSelectType"
            @save="clickSaveChoosed"
            @showDataBoxInvite="showDataBoxInviteDialog = true"
            @close="addressBookDialogVisible = false"
        >
        </DialogAddressBook>

        <!-- 对话框：查看签署位置 -->
        <SignOrderDialog class="SignOrderDialog"
            :recipients="signOrderData"
            :ordered="signOrdered"
            ref="signOrderDialog"
            :unlockScroll="true"
        ></SignOrderDialog>

        <!-- 对话框：excel报错精确提示 -->
        <el-dialog class="excelErrorDialog ssq-dialog"
            size="big"
            title="导入失败"
            :visible.sync="excelErrorDialogVisible"
            @close="excelErrorKey=Math.random()"
            :lock-scroll="false"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
        >
            <div class="excel-error-line"
                v-for="(item, index) in excelErrorMissingData"
                :key="index"
            >
                缺失：{{ item }}；
            </div>
            <div class="excel-error-line"
                v-for="(item, index) in excelErrorLineData"
                :key="index"
            >
                第{{ item.lineNumber }}行：{{ item.errMsg }}
            </div>
            <el-table
                v-show="excelErrorData.length"
                :data="excelErrorData"
                border
                stripe
                style="width: 100%"
                empty-text=" "
            >
                <el-table-column
                    v-for="(value, key) in excelHeaderData.line"
                    :key="key"
                    :prop="value"
                    :label="key"
                    width="50"
                >
                </el-table-column>
                <!-- 签署人字段，存在多身份，需要合并单元格，表头占两行 -->
                <template v-if="excelHeaderData.singers.length > 1">
                    <el-table-column
                        v-for="singer in excelHeaderData.singers"
                        :key="singer.signerName"
                        :label="singer.signerName"
                        width="120"
                    >
                        <el-table-column
                            v-for="field in singer.fields"
                            :key="field"
                            :prop="`${singer.signerName}-${field}`"
                            :label="field"
                            width="120"
                        >
                            <template slot-scope="scope">
                                <el-popover trigger="hover" placement="top" v-if="scope.row[`${singer.signerName}-${field}-error`]">
                                    <p class="el-popover--tips">{{ scope.row[`${singer.signerName}-${field}-error`] }}</p>
                                    <div slot="reference" class="cell-red">
                                        {{ scope.row[`${singer.signerName}-${field}`] }}
                                    </div>
                                </el-popover>
                                <div v-else>{{ scope.row[`${singer.signerName}-${field}`] }}</div>
                            </template>
                        </el-table-column>
                    </el-table-column>
                </template>
                <!-- 签署人字段，不存在多身份 -->
                <template v-else>
                    <el-table-column
                        v-for="field in excelHeaderData.singers[0] && excelHeaderData.singers[0].fields"
                        :key="field"
                        :prop="`${excelHeaderData.singers[0].signerName}-${field}`"
                        :label="field"
                        width="120"
                    >
                        <template slot-scope="scope">
                            <el-popover trigger="hover" placement="top" v-if="scope.row[`${excelHeaderData.singers[0].signerName}-${field}-error`]">
                                <p>{{ scope.row[`${excelHeaderData.singers[0].signerName}-${field}-error`] }}</p>
                                <div slot="reference" class="cell-red">
                                    {{ scope.row[`${excelHeaderData.singers[0].signerName}-${field}`] }}
                                </div>
                            </el-popover>
                            <div v-else>{{ scope.row[`${excelHeaderData.singers[0].signerName}-${field}`] }}</div>
                        </template>
                    </el-table-column>
                </template>
                <!-- 业务字段 -->
                <el-table-column
                    v-for="field in excelHeaderData.inputFields"
                    :key="field"
                    :prop="field"
                    :label="field"
                    width="120"
                >
                    <template slot-scope="scope">
                        <el-popover trigger="hover" placement="top" v-if="scope.row[`${field}-error`]">
                            <p>{{ scope.row[`${field}-error`] }}</p>
                            <div slot="reference" class="cell-red">
                                {{ scope.row[field] }}
                            </div>
                        </el-popover>
                        <div v-else>{{ scope.row[field] }}</div>
                    </template>
                </el-table-column>
            </el-table>
            <div slot="footer" class="dialog-footer register excelErrorDialog-btn">
                <el-button type="primary" @click="excelErrorDialogVisible = false">知道了</el-button>
            </div>
        </el-dialog>
        <DataBoxInvite
            v-if="$store.state.commonHeaderInfo.userType === 'Enterprise'"
            :visible.sync="showDataBoxInviteDialog"
        ></DataBoxInvite>
    </div>
</template>

<script>
import Bus from 'components/bus/bus.js';
import ReceiverList from './receiverList/ReceiverList.vue';

import DataBoxInvite from 'src/components/dataBoxInvite/index.vue';
import DialogAddressBook from 'src/components/sendingPrepare/dialogAddressBook/index.vue';
import SignOrderDialog from 'src/components/signOrder/index.vue';
import { resendTemplateMinxin } from 'src/mixins/resendTemplate.js';

import { mapState, mapGetters, mapActions } from 'vuex';

export default {
    components: {
        ReceiverList,
        DialogAddressBook,
        SignOrderDialog,
        DataBoxInvite,
    },
    mixins: [resendTemplateMinxin],
    // eslint-disable-next-line vue/require-prop-types
    props: ['templateStatus', 'isDynamicTemplate', 'noticeSpecificUser', 'notAllowNotify'],
    data() {
        return {
            // 基本信息 from ajax
            contract_id: this.$route.query.templateId || '',

            // 是否顺序签署
            signOrdered: false,

            // 地址簿
            addressBookDialogVisible: false,
            addressBookSelectType: '',

            // 查看签署人顺序
            signOrderData: [],

            // 防恶意输入
            // imgVaDialogVisible: false,
            imgVaKey: 0,
            imageVerifyCode: '',
            // 图片验证码key
            imageKey: '',

            // Excel精确报错
            excelErrorDialogVisible: false,
            excelErrorKey: 0,
            excelErrorData: [
                {
                    fields: [],
                },
            ],
            excelErrorMissingData: [],
            excelErrorLineData: [],
            // 上传的文档
            docList: [],
            // 提取
            // pickDialogVisible: false,
            // pickedInfo: [],
            // isPicking: false,
            excelLineMap: {},
            excelHeaderData: {
                singers: [],
                inputFields: [],
            },
            excelRowData: [],
            showDataBoxInviteDialog: false,
            // isStandardVersion: false,
        };
    },
    computed: {
        ...mapState({
            hybridServer: state => state.commonHeaderInfo.hybridServer,
        }),
        ...mapGetters(['getSsoConfig', 'checkFeat', 'getIsLoginFromDeveloper']),
        ssoOrderSignVal() {
            return this.ssoTmpEdit.tmpEdit_ordSign && this.ssoTmpEdit.tmpEdit_ordSign.defaultValue;
        },
    },
    methods: {
        ...mapActions('template', ['getIsLimitFaceConfig']),
        // 过滤错误
        matchErrMsg(message) {
            const matchedAry = message.match(/\{(.+?)\}/g);
            return JSON.parse(matchedAry[0]);
        },

        /**
         * 顺序签署
         */
        // 重置签署顺序
        resetOrder() {
            this.$refs.receiverList.recipients.forEach((item, index) => {
                item.routeOrder = ++index;
            });
        },
        // 顺序签署打勾
        clickSwitchOrder() {
            this.resetOrder();
        },

        /**
         * 查看签署顺序
         */
        clickSignOrderBtn() {
            const sender = [{
                receiverType: 'SENDER',
                userName: this.$refs.receiverList.sender.senderName,
                photoHref: this.$refs.receiverList.sender.photoHref,
            }];
            this.signOrderData = [...this.$refs.receiverList.recipients, ...sender];
            this.$refs.signOrderDialog.handleOpen();
        },

        /**
         * 通过联系人地址簿添加签约方
         */
        // 点击地址簿
        clickAdressBook() {
            this.addressBookSelectType = 'checkBox';
            this.addressBookDialogVisible = true;
        },
        // 点击地址簿保存
        clickSaveChoosed(param) {
            this.addressBookDialogVisible = false;
            param.choosedMembers.forEach(item => {
                let isPerson;
                if (param.choosedType !== 'outerContract') {
                    isPerson = false;
                } else {
                    isPerson = !item.entName;
                }
                // const userAccount = item.account || item.contactAccount;
                const index = this.$refs.receiverList.recipients.length;
                const lastRouteOrder =
                    index
                        ? parseInt(this.$refs.receiverList.recipients[index - 1].routeOrder) + 1
                        : 1;

                this.$refs.receiverList.addRecipient({
                    userType: isPerson ? 'PERSON' : 'ENTERPRISE',
                    routeOrder: lastRouteOrder,
                });

                const recipient = this.$refs.receiverList.recipients[index];
                console.log(param);
                // 内部联系人
                if (param.choosedType === 'innerMember') {
                    const info = {
                        userId: item.userId,
                        userAccount: item.account,
                        userName: item.empName,
                        enterpriseId: item.entId || '',
                        enterpriseName: this.$store.getters.getCurrentEntInfo.entName || '',
                        photoHref: '',
                    };
                    this.$set(this.$refs.receiverList.recipients, index, {
                        ...recipient,
                        ...info,
                    });
                } else if (param.choosedType === 'outerContact') {
                    const info = {
                        userAccount: item.contact,
                        userName: item.userName,
                        enterpriseName: item.entName,
                    };
                    this.$set(this.$refs.receiverList.recipients, index, {
                        ...recipient,
                        ...info,
                    });
                } else {
                    const info = {
                        userId: item.userId || '',
                        userAccount: item.contactAccount,
                        userName: item.contactName,
                        enterpriseName: item.entName,
                    };
                    this.$set(this.$refs.receiverList.recipients, index, {
                        ...recipient,
                        ...info,
                    });
                }
            });
        },

        // 加载完毕
        handleLoaded() {
            this.$emit('loaded');
        },
        handleUpdateTemInfo(data) {
            this.$emit('updateTemInfo', data);
        },
        // ajax
        getOrder() {
            let url = `${tempPath}/templates/${this.contract_id}`;
            url = this.handleRequestLink(url);
            return this.$http.get(url);
        },
        putOrder(signOrdered) {
            return this.$http.put(`${tempPath}/templates/${this.contract_id}`, { signOrdered });
        },
    },
    created() {
        this.ssoTmpEdit = this.getSsoConfig.tmpEdit || {};

        // 获取文档信息
        this.getOrder()
            .then(res => {
                this.signOrdered = res.data.signOrdered;
            })
            .catch(() => {})
            .finally(() => {
                // signOrdered为null表示为新建模板，从乐高城获取到是否需要顺序签署的默认值
                if (this.templateStatus === 'edit' && this.signOrdered === null) {
                    this.signOrdered = this.getIsLoginFromDeveloper ? this.ssoOrderSignVal : this.checkFeat.orderSign;
                }
                this.signOrdered = this.signOrdered ? this.signOrdered : false;
            });
        Bus.$on('doc-List-Update', (docList) => {
            this.docList = docList;
        });
        this.getIsLimitFaceConfig();
    },
};
</script>

<style lang="scss">
    @import './AddReciver.scss';
</style>
