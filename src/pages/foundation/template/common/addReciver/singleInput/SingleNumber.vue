<!--  excel 单个签约方输入控件-数字输入框 -->
<template>
    <div class="signle-signer-form-item">
        <div class="form-item-title">
            <span>{{ cacheField.fieldName }}</span>
        </div>
        <div class="form-item-input">
            <el-input size="small" v-model.trim="cacheField.value" :placeholder="placeholderInfo" @focus="handleFocus" @blur="handleBlur"></el-input>
        </div>
        <span class="errMsg" v-if="cacheField.errorMessage">{{ cacheField.errorMessage }}</span>
    </div>
</template>

<script>
import resRules from 'src/common/utils/regs.js';

export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['field', 'index'],
    data() {
        return {
            cacheField: {},
            placeholderInfo: '',
        };
    },
    watch: {
        field: {
            handler(v) {
                this.cacheField = Object.assign({}, v);
            },
            immediate: true,
            deep: true,
        },
        index: {
            handler() {
                const notNull = this.cacheField.notNull ? '必填' : '选填';
                this.placeholderInfo = notNull;
            },
            immediate: true,
            deep: true,
        },
    },
    methods: {
        // 获取焦点时清空错误
        handleFocus() {
            this.cacheField.errorMessage = '';
        },
        // 失去焦点
        handleBlur() {
            const value = this.cacheField.value;
            if (this.cacheField.value && (this.cacheField.value === this.field.value)) {
                return;
            }
            const isNumber = resRules.numberLabelCode.test(value);
            if (value && !isNumber) {
                this.cacheField.errorMessage = '格式不正确, 请输入正确的数字';
            } else {
                this.cacheField.errorMessage = '';
            }

            if (this.cacheField.notNull && !this.cacheField.value) {
                this.cacheField.errorMessage = `${this.cacheField.fieldName}不能为空`;
            }

            this.$parent.handleUpdate(this.cacheField);
        },
    },
};
</script>
