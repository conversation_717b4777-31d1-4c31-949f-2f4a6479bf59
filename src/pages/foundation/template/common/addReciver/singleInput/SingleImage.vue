<template>
    <div class="field-image-container">
        <p class="title">{{ cacheField.fieldName }} <span v-if="cacheField.notNull">*</span></p>
        <div class="content">
            <p v-if="cacheField.value"><i class="el-icon-ssq-tongguo" style="color: #00AA64"></i>{{ fileName }}上传成功</p>
            <p v-else-if="hasUploadExceedSize" style="color: #FF5500"><i class="el-icon-ssq-tishi1"></i>图片过大！</p>
            <!-- 长宽比为{{field.extendedAttribute && field.extendedAttribute.width}}*{{field.extendedAttribute && field.extendedAttribute.height}}px -->
            <p v-else><i class="el-icon-ssq-tishi1" style="color: #979797;"></i>请上传图片且大小不超过5M</p>
            <h6 v-if="cacheField.errorMessage">{{ cacheField.errorMessage }}</h6>
            <el-upload
                :action="uploadUrl"
                accept="image/png,imgage/jpg,image/jpeg"
                :headers="uploadHeaders"
                :before-upload="onBeforeUpload"
                :on-success="onSuccessUpload"
                :on-error="onErrorUpload"
                :show-file-list="false"
            ><div class="upload" v-loading="loading">{{ cacheField.value || hasUploadExceedSize ? '重新上传' : '上传图片' }}</div>
            </el-upload>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        field: { // 表格相关数据
            type: Object,
            default: function() {
                return {};
            },
        },
    },
    data() {
        return {
            loading: false,
            cacheField: {},
            hasUploadExceedSize: false,
            uploadUrl: `${tempPath}/multiple-dynamic-signer/web/upload-picture`,
            uploadHeaders: {
                Authorization: `bearer ${this.$cookie.get('access_token')}`,
            },
            fileName: '图片',
        };
    },
    watch: {
        field: {
            handler(v) {
                this.cacheField = Object.assign({}, v);
            },
            immediate: true,
            deep: true,
        },
    },
    methods: {
        onBeforeUpload(file) {
            this.fileName = '';
            if (!(['image/png', 'image/jpg', 'image/jpeg'].indexOf(file.type) > -1)) {
                this.$MessageToast.error('仅支持图片png、jpg、jpeg格式，请重新上传');
                return false;
            }
            if (file.size / 1024 / 1024 <= 5) {
                this.fileName = file.name;
                this.loading = true;
                return true;
            }
            this.$MessageToast.info('图片大小不能超过5M');
            this.hasUploadExceedSize = true;
            return false;
        },
        onSuccessUpload(response) {
            this.loading = false;
            this.cacheField.value = response;
            this.cacheField.errorMessage = '';
            this.$parent.handleUpdate(this.cacheField);
        },
        onErrorUpload() {
            this.loading = false;
        },
    },
};
</script>

<style lang="scss">
.field-image-container {
    .title {
        font-size: 12px;
        color: #999999;
        padding: 8px 0;
        background: #ffffff;
        margin: 0 -20px;
        span {
            color: red;
            display: inline-block;
            vertical-align: middle;
        }
    }
    .content {
        text-align: center;
        padding: 35px 0;
        position: relative;
        p {
            color: #333333;
            font-size: 12px;
            margin-bottom: 10px;
            i {
                margin-right: 2px;
            }
        }
        .upload {
            background: #127FD2;
            width: 88px;
            height: 28px;
            line-height: 28px;
            color: #ffffff;
            border-radius: 1px;
        }
        h6 {
            color: #FF6432;
            position: absolute;
            bottom: 15px;
            width: 100%;
            text-align: center;
        }
    }
}
</style>
