<!--  excel 单个签约方输入控件-日期输入框 -->
<template>
    <div class="signle-signer-form-item">
        <div class="form-item-title">
            <span
                v-if="cacheField.notNull"
                class="red"
            >
                *
            </span>
            <span>{{ cacheField.fieldName }}</span>
        </div>
        <div class="form-item-input">
            <el-date-picker
                v-model="cacheField.value"
                align="right"
                :placeholder="placeholderInfo"
                :editable="false"
                @change="handleDateChange"
                :picker-options="field.fieldName === '合同到期日' ? pickerAfterToday : emptyOption"
            >
            </el-date-picker>
        </div>

        <span class="errMsg" v-if="cacheField.errorMessage">{{ cacheField.errorMessage }}</span>
    </div>
</template>

<script>
export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['field', 'index'],
    data() {
        return {
            cacheField: {},
            pickerAfterToday: {
                disabledDate(time) {
                    return time.getTime() < Date.now() - 8.64e7;
                },
            },
            emptyOption: {},
            placeholderInfo: '',
        };
    },
    watch: {
        field: {
            handler(v) {
                this.cacheField = Object.assign({}, v);
                this.cacheField.value = v.value ? Date.parse(v.value) : '';
            },
            immediate: true,
            deep: true,
        },
        index: {
            handler() {
                const notNull = this.cacheField.notNull ? '必填' : '选填';
                this.placeholderInfo = notNull;
            },
            immediate: true,
            deep: true,
        },
    },
    methods: {
        // 日期范围 input 值改变，返回文本框值
        handleDateChange(val) {
            this.cacheField.value = val;
            this.cacheField.errorMessage = '';
            this.$parent.handleUpdate(this.cacheField);
        },
    },
};
</script>
