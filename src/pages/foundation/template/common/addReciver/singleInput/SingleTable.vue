<!-- eslint-disable vue/no-template-shadow -->

<template>
    <div class="field-table-container">
        <div>
            <div class="form-item-title">
                <span>{{ cacheField.fieldName }}</span>
                <div class="field-table-row-container">
                    <div class="field-table-row">
                        <label>行数：</label>
                        <input type="number" v-model.number="row" />
                    </div>
                    <div class="field-table-row">
                        <label>列数：</label>
                        <input type="number" v-model.number="col" />
                    </div>
                </div>
            </div>
            <div class="field-table-content">
                <div v-if="row > 3 || col > 3">
                    <p v-if="uploadedSuccessfully">
                        <i class="el-icon-ssq-tongguo"></i>
                        <template v-if="file && file.name">
                            {{ file.name }}
                        </template>
                        上传成功！
                    </p>
                    <p v-else><i class="el-icon-ssq-tishi1"></i>提示：行列数超过3，则需要手动上传Excel表格，总列数不能超过{{ tableLimit.column }}列</p>
                    <el-upload
                        :action="uploadUrl"
                        :headers="uploadHeaders"
                        :show-file-list="false"
                        :on-success="handleSuccess"
                        :on-error="handleError"
                        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                    >
                        <el-button type="primary" size="small" class="btn-type-one">{{ uploadedSuccessfully ? '重新上传表格' : '上传表格' }}</el-button>
                    </el-upload>
                </div>
                <!-- 表格的style样式给后端转换成pdf时使用，不要删掉 -->
                <!-- <table class="field-table" ref="dynamicTable" style="border-collapse: collapse;border-spacing: 0;" v-else>
                    <tr v-for="(item, index) in row" :key="index">
                        <td v-for="(item, index) in col" :key="index" @click="handleClick" style="width:auto;min-width:75px;padding:3px;height:30px;font-size:13px;border:1px solid #333;word-break:break-word;"></td>
                    </tr>
                </table> -->
                <table
                    v-else
                    class="field-table-content__table"
                >
                    <tr
                        v-for="(row, rowKey) in temArr"
                        :key="rowKey"
                    >
                        <td
                            class="field-table-content__table-td"
                            v-for="(item, index) in row"
                            :key="index"
                            @click="handleUpdateInputShowArr(rowKey, index, true)"
                        >
                            <el-input
                                v-if="inputShowArr[rowKey][index]"
                                v-focus="inputShowArr[rowKey][index]"
                                class="field-table-content__input"
                                v-model="temArr[rowKey][index]"
                                size="5"
                                @blur="handleUpdateInputShowArr(rowKey, index, false)"
                            >
                            </el-input>
                            <template v-else>
                                <div class="field-table-content__div">
                                    {{ temArr[rowKey][index] }}
                                </div>
                            </template>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</template>

<script>
import { DYNAMIC_TABLE_LIMIT } from 'utils/commonVar.js';
export default {
    props: {
        field: { // 表格相关数据
            type: Object,
            default: function() {
                return {};
            },
        },
    },
    data() {
        return {
            cacheField: {},
            row: this.field.extendedAttribute.row,
            col: this.field.extendedAttribute.column,
            uploadHeaders: { Authorization: `bearer ${this.$cookie.get('access_token')}` },
            file: null,
            uploadedSuccessfully: false,
            temArr: [], // 表格数组数据
            inputShowArr: [], // 对应表格数据，控制是否展示input
            tableLimit: DYNAMIC_TABLE_LIMIT,
        };
    },
    computed: {
        uploadUrl() {
            const templateId = this.$route.query.templateId;
            return `${tempPath}/dynamic-template/${templateId}/label/${this.cacheField.labelId}/web/import-dynamic-table`;
        },
    },
    watch: {
        field: {
            handler(v) {
                this.cacheField = Object.assign({}, v);
                // 字符串数据转换初始化
                if (this.cacheField.value) {
                    if (this.cacheField.value !== 'ExcelImport') {
                        try {
                            this.temArr = JSON.parse(this.cacheField.value);
                            // 后端行列数据不准，不是最新的
                            this.row = this.temArr.length;
                            this.col = this.temArr[0].length;
                        } catch (error) {
                            this.temArr = [];
                        }
                    } else {
                        // 通过批量上传成功过的
                        this.uploadedSuccessfully = true;
                        this.temArr = 'ExcelImport';
                        this.row = this.col = 4;
                    }
                }
            },
            immediate: true,
            deep: true,
        },
        row: {
            handler(newValue, oldValue) {
                this.handleColRowData(newValue);
                // 行数控制不能超过1000行
                if (newValue > DYNAMIC_TABLE_LIMIT.row) {
                    this.row = oldValue;
                }
            },
            immediate: true,
        },
        col: {
            handler(newValue, oldValue) {
                this.handleColRowData(newValue);
                // 总列数不能超过16列
                if (newValue > DYNAMIC_TABLE_LIMIT.column) {
                    this.col = oldValue;
                }
            },
            immediate: true,
        },
        temArr: {
            handler(val) {
                // 接口需要的value是String格式
                if (Array.isArray(val)) {
                    this.cacheField.value = JSON.stringify(val);
                } else {
                    this.cacheField.value = val;
                }
                // 这个字段传给后端后端不会保存;
                // this.cacheField.extendedAttribute.row = this.row;
                // this.cacheField.extendedAttribute.column = this.col;
                this.$parent.handleUpdate(this.cacheField);
            },
            immediate: true,
            deep: true,
        },
    },
    methods: {
        // 处理行列数的公共逻辑
        handleColRowData(newValue) {
            if (newValue === '') {
                return;
            }
            if (newValue < 4) {
                // 初始化表格数组
                this.initArrayData(this.row, this.col);
            }
            // 清空用户手动输入的内容的值
            if (newValue > 3) {
                if (this.uploadedSuccessfully) {
                    // 已经上传过表格
                    return;
                }
                if (this.cacheField.value) {
                    // 清空用户手动输入的内容的值
                    this.cacheField.value = '';
                    this.$parent.handleUpdate(this.cacheField);
                }
            }
        },
        // 初始化数组
        initArrayData(row, col) {
            this.temArr = this.createNewArray(row, col, '', this.temArr);
            this.inputShowArr = this.createNewArray(row, col, false);
        },
        // 创建二维数组
        createNewArray(row, col, defaultValue, arr) {
            if (!Array.isArray(arr)) {
                arr = null;
            }
            const temArr = new Array(row);
            for (let i = 0; i < row; i++) {
                temArr[i] = new Array(col);
                for (let j = 0; j < col; j++) {
                    if (arr && arr[i] && arr[i][j]) {
                        // 变动行列值的时候，如果之前有值，赋之前的值
                        temArr[i][j] = arr[i][j];
                    } else {
                        temArr[i][j] = defaultValue;
                    }
                }
            }
            return temArr;
        },
        handleUpdateInputShowArr(rowKey, index, val) {
            // 直接通过索引改，vue监测不到变化
            this.$set(this.inputShowArr[rowKey], index, val);
        },
        handleSuccess(response, file) {
            this.file = file;
            this.uploadedSuccessfully = true;
            this.temArr = 'ExcelImport';
        },
        handleError(err, file) {
            this.uploadedSuccessfully = false;
            this.file = null;
            this.$MessageToast.error(`${file.name}上传失败`);
        },
    },
};
</script>

<style lang="scss">
    .field-table-container {
        margin: 0 -20px;
        background-color: #fff;
        .form-item-title {
            padding-top: 17px;
            width: 100%;
        }
        .field-table-row-container {
            float:right;
            .field-table-row {
                display: inline-block;
                line-height: 20px;
                input {
                    padding: 0 3px;
                    border: 1px solid #ddd;
                    height: 20px;
                    width: 36px;
                    color: #333;
                    font-size: 12px;
                    line-height: 18px;
                    box-sizing: border-box;
                    border-radius: 2px;
                    box-shadow: none;
                    &[type=number] {
                        -moz-appearance: textfield;
                    }
                    &[type=number]::-webkit-inner-spin-button,
                    &[type=number]::-webkit-outer-spin-button {
                        -webkit-appearance: none;
                        margin: 0;
                    }
                }
            }
        }
        .field-table-content {
            background-color:#f9f9f9;
            padding: 20px;
            margin-top: 3px;
            text-align: center;
            color: #333;
            font-size: 12px;
            i {
                font-size: 14px;
                margin-right: 4px;
            }
            .el-icon-ssq-tishi1 {
                color: #FF5500;
            }
            .el-icon-ssq-tongguo {
                color: #00AA64;
            }
            .el-upload {
                margin-top: 10px;
            }
        }
        .field-table-content__table {
            margin-top: 3px;
            border-collapse: collapse;
            border-spacing: 0;
            background-color: #fff;
            text-align: left;

            table-layout:fixed;
        }
        .field-table-content__table-td {
            position: relative;
            border: 1px solid #ddd;
            padding: 0;
            height: 30px;
            box-sizing: border-box;
            font-size: 13px;
        }
        .field-table-content__div {
            width: auto;
            min-width: 75px;

            word-break:break-all;
            word-wrap:break-word;
        }
        .field-table-content__input {
            .el-input__inner {
                border: 1px;
                border-color: none;
                font-size: 13px;
                &:hover, &:focus {
                    border: none;
                    box-shadow: none;
                }
            }
            &,
            .el-input__inner {
                box-sizing: border-box;
                width: 100%;
                height: 100%;
                height: calc(100% - 2px);
                padding: 0;
                margin: 0;
            }
        }
    }
</style>
