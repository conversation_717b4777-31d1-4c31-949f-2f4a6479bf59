<template>
    <div class="clause-category-wrap">
        <div class="clause-category-name">
            <span>{{ field.fieldName }}</span>
            <el-button v-if="selectedClause.length" type="text" size="small" class="fr" @click="changeClause">修改</el-button>
        </div>
        <div class="select-clause-wrap" v-if="!selectedClause.length">
            <!-- <p><i class="el-icon-ssq-tishi1"></i>提示：这是一个提示</p> -->
            <el-button type="primary" size="small" class="btn-type-one" @click="getClauseList">选择条款</el-button>
        </div>
        <div v-else class="clause-wrap">
            <div
                class="clause-detail"
                v-for="(clause, index1) in formModel"
                :key="index1"
            >
                <p class="clause-render-text" v-html="clause.renderText()"></p>
                <div class="signle-signer-form-item" v-for="(item, index2) in clause.fields" :key="index2">
                    <div class="form-item-title">
                        <span class="red">*</span>
                        <span>{{ item.name }}</span>
                    </div>
                    <div class="form-item-input">
                        <el-input
                            :maxlength="50"
                            placeholder="请输入"
                            size="small"
                            v-model="item.value"
                            @focus="doFocus(clause, item)"
                            @blur="doBlur(clause, item)"
                        />
                        <span class="red" v-if="!item.value && item.error">不能为空</span>
                        <span class="red" v-else-if="item.value && item.error">非法输入</span>
                    </div>
                </div>
            </div>
        </div>
        <SequenceCheckboxDialog
            :list="clauseList"
            :checked="selectedClause"
            :visible.sync="clauseDialogVisible"
            @setData="selectedClauseChange"
        />

    </div>
</template>

<script>
import Bus from 'components/bus/bus.js';
import { htmlentities } from 'src/common/utils/dom.js';
import SequenceCheckboxDialog from './SequenceCheckboxDialog.vue';

const regexp = /{#(.*?)#}/g;
export default {
    components: {
        SequenceCheckboxDialog,
    },
    // eslint-disable-next-line vue/require-prop-types
    props: ['field', 'index', 'signerFields', 'isEnt', 'type'],
    data() {
        return {
            dialog: true,
            clauseDialogVisible: false,
            clauseList: [],
            selectedClause: [],
            formModel: [],
            errorMessage: '',
        };
    },
    computed: {
        dynamicMark() {
            return this.$route.query.dynamicMark;
        },
    },
    methods: {
        updateValue() {
            this.checkInputValue();
            this.$parent.handleUpdate({
                ...this.field,
                errorMessage: this.errorMessage,
                value: this.formateSubmitValue(),
            });
        },
        formateSubmitValue() {
            const valueStrs = [];
            const subValue = this.formModel.map(clause => {
                // 记住用户填写的值（初始化的时候从 value 里拎出来）
                let str = '';
                const meta = {
                    clause: clause.defaultText,
                    value: clause.fields.map(field => field.value),
                };
                if (this.dynamicMark === 'DYNAMIC_YOZO') {
                    valueStrs.push(clause.renderText(true));
                    str = `<meta content='${JSON.stringify(meta)}'>`;
                } else {
                    str = `<meta content='${JSON.stringify(meta)}'><p>${clause.renderText()}</p>`;
                }
                return str;
            }).join('');
            if (this.dynamicMark === 'DYNAMIC_YOZO') {
                return JSON.stringify({
                    metaStr: subValue,
                    value: valueStrs,
                });
            }
            return subValue;
        },
        // showError=true 在点击下一步时被触发了 Bus.$emit('clause-error-check');
        // 触发来源：src/pages/foundation/template/common/addReciver/receiverList/ReceiverList.vue
        checkInputValue(showError) {
            this.errorMessage = null;
            return this.formModel.some(clause => {
                return clause.fields.some(item => {
                    let error = false;
                    if (!item.value) {
                        this.errorMessage = `${this.field.fieldName}-${item.name}不能为空`;
                        error = true;
                    } else {
                        // 用户输入了 {##}
                        if (item.value.match(regexp)) {
                            this.errorMessage = `请修改${this.field.fieldName}-${item.name}的输入内容`;
                            error = true;
                        }
                    }
                    if (showError) {
                        item.error = error;
                    }
                    return error;
                });
            });
        },
        doFocus(clause, item) {
            clause.focusIndex = item.index;
            item.error = false;
        },
        doBlur(clause, item) {
            clause.focusIndex = -1;
            item.error = !item.value || !!item.value.match(regexp);
            this.updateValue();
        },
        changeClause() {
            if (this.field.value) {
                this.getClauseList();
            } else {
                this.clauseDialogVisible = true;
            }
        },
        // 第二个参数是记住的默认值
        selectedClauseChange(data, values) {
            const that = this;
            this.selectedClause = data;
            // 数据结构
            this.formModel = data.map((defaultText, clauseIndex) => {
                return {
                    defaultText,
                    focusIndex: -1,
                    renderText(isSubValue) {
                        let count = -1;
                        return defaultText.replace(regexp, field => {
                            count++;
                            if (that.dynamicMark === 'DYNAMIC_YOZO' && isSubValue) {
                                return this.fields[count].value || field;
                            }
                            const style = 'color: #000;text-decoration: underline;';
                            if (count === this.focusIndex) {
                                // return `<span style="${style}background-color: #c9e7ff;">${field}</span>`;
                                return `<span style="${style}background-color: #c9e7ff;">${htmlentities.encode(this.fields[count].value || field)}</span>`;
                            }
                            // return `<span style="${style}">${field}</span>`;
                            return `<span style="${style}">${htmlentities.encode(this.fields[count].value || field)}</span>`;
                        });
                    },
                    fields: (defaultText.match(regexp) || []).map((field, index) => {
                        const defaultValue = values ? values[clauseIndex][index] : '';
                        return {
                            index,
                            name: field.slice(2, -2),
                            value: defaultValue,
                            error: false,
                        };
                    }),
                };
            });
            this.updateValue();
        },
        getClauseList() {
            this.$http.get(`/template-api/templates/terms/${this.field.extendedAttribute.termTypeId}`)
                .then(res => {
                    this.clauseList = res.data.templateTermContents;
                    this.clauseDialogVisible = true;
                });
        },
    },
    mounted() {
        Bus.$on('clause-error-check', () => {
            this.checkInputValue(true);
        });

        // 使用模板时下一步后退回来时条款的默认值
        if (this.field.value) {
            try {
                // 新版动态模版应后端要求，做JSON处理，这里解析
                const defaultInfo = (this.dynamicMark === 'DYNAMIC_YOZO' ? JSON.parse(this.field.value).metaStr || ''
                    : this.field.value).match(/<meta content=.*?>/gi).map(v => v.slice(15, -2));
                const clauses = defaultInfo.map(v => JSON.parse(v).clause);
                const values = defaultInfo.map(v => JSON.parse(v).value);
                this.selectedClauseChange(clauses, values);
            } catch (e) {
                console.error(`${this.field.value} 解析存在报错，不影响到流程`);
            }
        }
    },
};
</script>

<style lang="scss" scoped>
    // 条款内容跟着变：word-break: break-all;
    .clause-category-wrap {
        display: block;
        box-sizing: border-box;
        margin: 0 -20px;
        margin-top: 7px;
        background-color: #fff;
        &:first-child {
            margin-top: 17px;
        }
        .clause-category-name {
            display: block;
            margin-bottom: 3px;
            padding-top: 17px;
            color: #999;
            line-height: 20px;
        }
        .select-clause-wrap {
            text-align: center;
            padding: 20px 0;
            background: #f9f9f9;
            i {
                font-size: 14px;
                color: #ff5500;
                margin-right: 4px;
            }
            .btn-type-one {
                margin-top: 10px;
            }
        }
    }
    .clause-wrap {
        padding: 20px 20px 0;
        background: #f9f9f9;
        .clause-detail {
            border-top: 1px solid #eee;
            padding: 21px 0 17px;
            color: #666;
            &:first-child {
                border: none;
                padding-top: 0;
            }
            .clause-render-text {
                line-height: 25px;
                margin-bottom: 10px;
            }
            .signle-signer-form-item {
                vertical-align: middle;
                .form-item-title {
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    max-width: 280px;
                    display: inline-block;
                }
            }
            .red {
                color: #ff6432;
            }
        }
    }
</style>
