<template>
    <div class="box-sizing-dialog sequence-checkbox-dialog">
        <el-dialog
            title="选择合同条款"
            custom-class="sequence-checkbox-dialog"
            :visible.sync="visible"
            :before-close="handleClose"
        >
            <div class="content-tip">
                <i class="el-icon-ssq-tishi1" />
                {{ sequenceList.length ? '请按顺序选择适用的条款' : '暂无条款' }}
            </div>
            <ul class="sequence-checkbox-list">
                <li
                    :data-label="item.label"
                    class="sequence-checkbox-item"
                    :class="{'checked': item.label}"
                    v-for="(item, index) in sequenceList"
                    :key="index"
                    @click="toggleItem(markedList[index].id)"
                    v-html="$options.filters.highlightClauseText(item.value)"
                ></li>
            </ul>
            <span slot="footer" class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="ok">确定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
export default {
    filters: {
        highlightClauseText(value) {
            return value.replace(/{#(.*?)#}/g, field => {
                const style = 'color: #000;text-decoration: underline;';
                return `<span style="${style}">${field}</span>`;
            });
        },
    },
    props: {
        list: {
            type: Array,
            required: true,
        },
        checked: {
            type: Array,
            required: true,
        },
        visible: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            checkedIds: [],
            markedList: [], // 给一个标记，使重复的字符串也有顺序
            lastCheckedIds: [], // 修改时记住上一次的值
        };
    },
    computed: {
        sequenceList() {
            const sequenceList = this.list.map(v => ({
                label: '',
                value: v,
            }));
            this.checkedIds.forEach((id, index) => {
                sequenceList[id].label = index + 1;
            });
            return sequenceList;
        },
    },
    watch: {
        list: {
            handler(newValue) {
                this.markedList = newValue.map((value, id) => ({
                    value,
                    id,
                }));
                this.lastCheckedIds = [];
                this.checkedIds = [];
            },
            immediate: true,
        },
        visible: {
            handler(newValue) {
                if (newValue) {
                    this.checkedIds = this.lastCheckedIds.slice();
                }
            },
        },
    },
    methods: {
        toggleItem(id) {
            const idPos = this.checkedIds.indexOf(id);
            if (idPos > -1) {
                this.checkedIds.splice(idPos, 1);
            } else {
                this.checkedIds.push(id);
            }
        },
        ok() {
            this.lastCheckedIds = this.checkedIds.slice();
            // this.$emit('setData', this.list.filter((v, index) => this.checkedIds.indexOf(index) > -1));
            this.$emit('setData', this.checkedIds.map(v => this.list[v]));
            this.$emit('update:visible', false);
        },
        handleClose() {
            this.$emit('update:visible', false);
        },
    },
};
</script>

<style lang="scss">
    .sequence-checkbox-dialog {
        width: 700px;
        .sequence-checkbox-list {
            max-height: 466px;
            margin: -12px -12px 0;
            padding: 20px 12px 0;
            font-size: 12px;
            overflow: auto;
            .sequence-checkbox-item {
                position: relative;
                margin-bottom: 18px;
                border: 1px solid #ddd;
                border-radius: 2px;
                padding: 11px 14px;
                line-height: 25px;
                color: #666;
                cursor: pointer;
                &.checked, &:hover {
                    border-color: #127fd2;
                    background-color: #f6f9fc;
                }
                &::before {
                    position: absolute;
                    top: -11px;
                    left: -12px;
                    width: 22px;
                    height: 22px;
                    line-height: 22px;
                    box-sizing: border-box;
                    border: 1px solid #ccc;
                    border-radius: 100%;
                    text-align: center;
                    background: #fff;
                    content: attr(data-label);
                }
                &.checked {
                    &::before {
                        background-color: #127fd2;
                        border-color: #127fd2;
                        color: #fff;
                    }
                }
                &:hover {
                    &::before {
                        border-color: #127fd2;
                    }
                }
            }
        }
    }
</style>
