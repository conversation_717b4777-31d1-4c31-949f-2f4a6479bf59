<!-- excel 单个签约方输入控件，可能有文本、日期、单选、多选，在这里做组件判断 -->
<template>
    <!-- <div class="single-signer-form-item-container"> -->
    <component
        :is="componentInputName"
        :field="field"
        :index="index"
        :signerFields="signerFields"
        :isEnt="isEnt"
        :type="type"
    ></component>
    <!-- </div> -->
</template>

<script>
import TextInput from './SingleText.vue';
import DateInput from './SingleDate.vue';
import NumberInput from './SingleNumber.vue';
import TableInput from './SingleTable.vue';
import DynamicTable from './SingleDynamicTable.vue';
import SingleClause from './SingleClause/SingleClause.vue';
import SingleImage from './SingleImage.vue';
import SelectInput from './SelectInput.vue';

export default {
    components: {
        TextInput,
        DateInput,
        NumberInput,
        TableInput,
        DynamicTable,
        SingleClause,
        SingleImage,
        SelectInput,
    },
    // eslint-disable-next-line vue/require-prop-types
    props: ['field', 'index', 'expanded', 'type', 'signerFields', 'isEnt'],
    data() {
        return {

        };
    },
    computed: {
        componentInputName() {
            const { type } = this.field;
            switch (type) {
                case 'BIZ_DATE':
                    return 'DateInput';
                case 'TEXT_NUMERIC':
                    return 'NumberInput';
                case 'DYNAMIC_TABLE':
                    return this.$route.query.dynamicMark === 'DYNAMIC' ? 'TableInput' : 'DynamicTable';
                case 'TERM':
                    return 'SingleClause';
                case 'PICTURE':
                    return 'SingleImage';
                case 'MULTIPLE_BOX':
                case 'SINGLE_BOX':
                    return 'SelectInput';
                default:
                    return 'TextInput';
            }
        },
    },
    // type： publicFields 合同信息 / signers 签约方信息
    methods: {
        handleUpdate(field) {
            this.$emit('update', field, this.type);
        },
    },
};
</script>
