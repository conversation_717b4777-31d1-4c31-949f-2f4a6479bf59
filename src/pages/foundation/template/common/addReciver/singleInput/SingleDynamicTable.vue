
<template>
    <div class="field-table-container">
        <div>
            <div class="form-item-title">
                <span>{{ field.fieldName }}</span>
            </div>
            <div class="field-table-content">
                <div>
                    <p v-if="uploadedSuccessfully">
                        <i class="el-icon-ssq-tongguo"></i>
                        <template v-if="file && file.name">
                            {{ file.name }}
                        </template>
                        上传成功！
                    </p>
                    <p v-else><i class="el-icon-ssq-tishi1"></i>提示：Excel表格总行数不能超过{{ rowLimit }}，总列数不能超过{{ columnLimit }}</p>
                    <el-upload
                        :action="uploadUrl"
                        :headers="uploadHeaders"
                        :before-upload="beforeUpload"
                        :show-file-list="false"
                        :on-success="handleSuccess"
                        :on-error="handleError"
                    >
                        <el-button type="primary" size="small" class="btn-type-one">{{ uploadedSuccessfully ? '重新上传表格' : '上传表格' }}</el-button>
                    </el-upload>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { checkExcelUploadLimit } from 'src/common/utils/fileLimit';
export default {
    props: {
        field: { // 表格相关数据
            type: Object,
            default: function() {
                return {};
            },
        },
    },
    data() {
        return {
            cacheField: {},
            uploadHeaders: { Authorization: `bearer ${this.$cookie.get('access_token')}` },
            file: null,
            uploadedSuccessfully: false,
            rowLimit: 500,
            columnLimit: 16,
        };
    },
    computed: {
        uploadUrl() {
            const templateId = this.$route.query.templateId;
            return `${tempPath}/dynamic-template/${templateId}/label/${this.field.labelId}/web/import-dynamic-table`;
        },
    },
    watch: {
        field: {
            handler(v) {
                this.cacheField = Object.assign({}, v);
                // 字符串数据转换初始化
                if (this.cacheField.value) {
                    // 通过批量上传成功过的
                    this.uploadedSuccessfully = true;
                }
            },
            immediate: true,
            deep: true,
        },
    },
    methods: {
        handleSuccess(response, file) {
            this.file = file;
            this.uploadedSuccessfully = true;
            if (this.cacheField.value !== 'ExcelImport') {
                this.cacheField.value = 'ExcelImport';
                this.$parent.handleUpdate(this.cacheField);
            }
        },
        handleError(err, file) {
            this.uploadedSuccessfully = false;
            this.file = null;
            this.$MessageToast.error(`${file.name}上传失败`);
        },
        async beforeUpload(file) {
            const isPassLimit = checkExcelUploadLimit(file);

            return isPassLimit;
        },
    },
};
</script>

<style lang="scss">
    .field-table-container {
        margin: 0 -20px;
        background-color: #fff;
        .form-item-title {
            padding-top: 17px;
            width: 100%;
        }
        .field-table-content {
            background-color:#f9f9f9;
            padding: 20px;
            margin-top: 3px;
            text-align: center;
            color: #333;
            font-size: 12px;
            i {
                font-size: 14px;
                margin-right: 4px;
            }
            .el-icon-ssq-tishi1 {
                color: #FF5500;
            }
            .el-icon-ssq-tongguo {
                color: #00AA64;
            }
            .el-upload {
                margin-top: 10px;
            }
        }
    }
</style>
