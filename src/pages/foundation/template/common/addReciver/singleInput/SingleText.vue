<!--  excel 单个签约方输入控件-文本输入框 -->
<template>
    <div class="signle-signer-form-item">
        <div class="form-item-title">
            <span
                v-if="cacheField.notNull"
                class="red"
            >
                *
            </span>
            <span>{{ cacheField.fieldName }}</span>
        </div>
        <div class="form-item-input">
            <!-- 单个输入 企业名称/姓名可以联想出来 -->
            <el-autocomplete
                v-if="isShowAutocomplete"
                :placeholder="`${placeholderInfo}`"
                class="recipient-companyname inline-input fcl-input fl"
                popper-class="sign-el-autocomplete-popper sign-el-autocomplete-popper--type-two"
                v-model="cacheField.value"
                :fetch-suggestions="searchEnpName.bind(null, 1)"
                @select="selectEnpName"
                @blur.native.capture="handleBlur()"
                style="margin: 0"
            >
            </el-autocomplete>

            <el-input
                v-else
                size="small"
                :placeholder="placeholderInfo"
                v-model="cacheField.value"
                @focus="handleFocus"
                @blur="handleBlur"
            ></el-input>
        </div>
        <span class="errMsg" v-if="cacheField.errorMessage">{{ cacheField.errorMessage }}</span>
        <span
            class="signle-signer-form-item-tips"
            v-if="cacheField.fieldName === '企业名称' && type === 'signers'"
            :class="{'signle-signer-form-item-tips_err': cacheField.errorMessage}"
        >提示：签约方的企业名称完全一致才能签署</span>
    </div>
</template>

<script>
import resRules from 'src/common/utils/regs.js';

export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['field', 'index', 'signerFields', 'isEnt', 'type'],
    data() {
        return {
            cacheField: {},
            placeholderInfo: '',
        };
    },
    computed: {
        isShowAutocomplete() {
            if (this.cacheField.custom) {
                return false;
            }
            if ((this.isEnt && this.cacheField.fieldName === '企业名称') || (!this.isEnt && this.cacheField.fieldName === '姓名')) {
                return true;
            }
            return false;
        },
    },
    watch: {
        field: {
            handler(v) {
                this.cacheField = Object.assign({}, v);
            },
            immediate: true,
            deep: true,
        },
        index: {
            handler() {
                let notNull = this.cacheField.notNull ? '必填' : '选填';
                if ((this.cacheField.fieldName === '身份证号' || this.cacheField.fieldName === '企业名称') && this.type === 'signers') {
                    notNull += '，用于签约身份核对';
                }
                this.placeholderInfo = notNull;
            },
            immediate: true,
            deep: true,
        },
    },
    methods: {
        // 获取焦点时清空错误
        handleFocus() {
            this.cacheField.errorMessage = '';
        },
        // 失去焦点
        handleBlur() {
            this.cacheField.value = this.cacheField.value && this.cacheField.value.trim();
            if (this.cacheField.notNull && !this.cacheField.value) {
                this.cacheField.errorMessage = `${this.cacheField.fieldName}不能为空`;
            }
            if (this.cacheField.notNull && this.cacheField.value) {
                this.cacheField.errorMessage = null;
            }

            this.checkDefaultFields();

            // 如果值没修改就不向父组件更新
            if (this.cacheField.value && (this.cacheField.value === this.field.value)) {
                return;
            }

            this.$parent.handleUpdate(this.cacheField);
        },
        // 表单校验，检查默认字段
        checkDefaultFields() {
            if (!this.cacheField.value) {
                return true;
            }

            // 针对系统字段的格式判断，注意：业务字段的“账号”不做校验
            if (!this.cacheField.custom && this.cacheField.fieldName === '账号' && !resRules.userAccount.test(this.cacheField.value)) {
                this.cacheField.errorMessage = '格式不正确，请输入正确的手机号或邮箱';
            } else if (this.cacheField.fieldName === '身份证(代传身份证号码)' && this.cacheField.value.length) {
                (!resRules.IDCardReg.test(this.cacheField.value)) && (this.cacheField.errorMessage = '格式不正确，请输入正确的身份证号');
            } else if (this.cacheField.fieldName === '身份证号' && this.type === 'signers' && this.cacheField.value.length && !this.cacheField.value.includes('*')) {
                (!resRules.IDCardReg.test(this.cacheField.value)) && (this.cacheField.errorMessage = '格式不正确，请输入正确的身份证号');
            }
        },
        selectEnpName(item) {
            this.cacheField.value = item.value;
            this.$nextTick(this.handleBlur);
        },
        searchEnpName(i, queryString, cb) {
            const { value } = (this.signerFields.filter(field => field.fieldName === '账号') || [{ value: '' }])[0];

            if (!value) {
                return false;
            }

            const isPerson = this.cacheField.fieldName === '姓名';
            const url = isPerson ? '/users/receiver' : '/ents/employees/receiver';
            return this.$http.get(`${url}?account=${value}`, {
                noToast: 1,
            }).then(res => {
                const data = res.data;
                if (!data) {
                    return;
                }
                let result;
                if (isPerson) {
                    result = [{
                        value: data.fullName,
                    }];
                } else {
                    result = data.enterprises.map(item => {
                        return {
                            value: item.entName,
                            label: `${item.entName}`,
                            data: item,
                        };
                    });
                }
                cb(result);
            });
        },
    },
};
</script>
