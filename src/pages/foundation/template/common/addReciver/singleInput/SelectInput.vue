<!--  excel 单个签约方输入控件-数字输入框 -->
<template>
    <div class="signle-signer-form-item">
        <div class="form-item-title">
            <span
                v-if="cacheField.notNull"
                class="red"
            >
                *
            </span>
            <span>{{ cacheField.fieldName }}</span>
        </div>
        <div class="form-item-input">
            <el-select v-model="cacheField.value" :multiple="cacheField.type === 'MULTIPLE_BOX'" placeholder="请选择" @change="handleChange">
                <el-option
                    v-for="item in cacheField.extendedAttribute && cacheField.extendedAttribute.optionalValues"
                    :key="item"
                    :label="item"
                    :value="item"
                >
                </el-option>
            </el-select>
        </div>
    </div>
</template>

<script>
export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['field'],
    data() {
        return {
            cacheField: {},
        };
    },
    watch: {
        field: {
            handler(v) {
                this.cacheField = Object.assign({}, v);
                if (this.cacheField.type === 'MULTIPLE_BOX') {
                    this.cacheField.value = this.cacheField.value ? this.cacheField.value.split(',') : [];
                } else {
                    this.cacheField.value = this.cacheField.value ? this.cacheField.value : '';
                }
            },
            immediate: true,
            deep: true,
        },
    },
    methods: {
        handleChange() {
            const newValue = this.cacheField.type === 'MULTIPLE_BOX' ? this.cacheField.value.toString() : this.cacheField.value;
            this.$parent.handleUpdate({
                ...this.cacheField,
                value: newValue,
            });
        },
    },
};
</script>
