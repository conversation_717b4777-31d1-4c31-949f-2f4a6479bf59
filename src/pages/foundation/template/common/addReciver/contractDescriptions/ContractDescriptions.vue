<!-- eslint-disable vue/no-template-shadow -->
<!-- 使用模板，单个添加的签约方，合同描述字段 -->
<template>
    <div class="tem-use-ContractDescriptions">
        <h1 class="tem-use-ContractDescriptions-title">合同基本信息</h1>
        <div class="tem-use-ContractDescriptions-form">
            <SingleInput
                v-for="(field, index) in publicFields"
                :key="'publicField_'+index+'_'+field.type"
                :field="field"
                :index="index"
                @update="handleUpdate"
                type="publicFields"
            ></SingleInput>
        </div>
    </div>
</template>

<script>
import { mapState } from 'vuex';
import SingleInput from '../singleInput/SingleInput.vue';

export default {
    components: {
        SingleInput,
    },
    // eslint-disable-next-line vue/require-prop-types
    props: ['index', 'inputFields'],
    data() {
        return {
            loadingTimer: null,
            domLoadFlag: false,
            // 异步加载业务字段
            bizFieldLimit: 20,
            asyncBizFields: [],
            // 所有字段
            cacheFields: {
                inputFields: [],
            },
        };
    },
    computed: {
        ...mapState({}),
        // 公用合同属性, 业务字段-
        publicFields() {
            return this.cacheFields.inputFields.map((field, i) => {
                field.fieldIndex = i;
                return field;
            });
        },
    },
    watch: {
        inputFields: {
            handler(v) {
                this.asyncLoadBizFields(JSON.parse(JSON.stringify(v)));
            },
            deep: true,
            immediate: true,
        },
    },
    methods: {
        // 业务字段数量上限500，超过阈值就异步加载
        asyncLoadBizFields(list) {
            const length = list.length;
            // 不是首次加载或者字段数量小于阈值就直接赋值
            if (this.domLoadFlag === true || length <= this.bizFieldLimit) {
                this.cacheFields.inputFields = list;
                this.domLoadFlag = true;
            } else {
                const maxLength = Math.ceil(length / this.bizFieldLimit);

                for (let i = 0; i < maxLength; i++) {
                    (() => {
                        this.$nextTick(() => {
                            this.loadingTimer = setTimeout(() => {
                                // 批量赋值，异步加载
                                this.cacheFields.inputFields = this.cacheFields.inputFields.concat(
                                    list.slice(
                                        i * this.bizFieldLimit,
                                        (i + 1) * this.bizFieldLimit,
                                    ),
                                );

                                // 最后一次循环是关闭 loading 提醒，清除定时器
                                if (i === maxLength - 1) {
                                    this.domLoadFlag = true;
                                    clearTimeout(this.loadingTimer);
                                }
                            }, i * 300);
                        });
                    })();
                }
            }
        },
        // 字段更新
        handleUpdate(field) {
            const { value, fieldIndex, errorMessage } = field;
            const temp = this.cacheFields.inputFields[fieldIndex];
            temp.value = value;
            temp.errorMessage = errorMessage;
            this.cacheFields.inputFields[fieldIndex] = temp;
        },
    },
};
</script>
<style lang="scss">
    .tem-use-ContractDescriptions-title {
        margin-top: 17px;
        line-height: 20px;
        margin-bottom: 4px;
        color: #999;
    }
    .tem-use-ContractDescriptions-form {
        padding: 17px 19px 0;
        margin-bottom: 23px;
        background: #F9F9F9;
        .signle-signer-form-item {
            position: relative;
            display: inline-block;
            margin-right: 10px;
            margin-bottom: 17px;
            width: 290px;
            vertical-align: top;
            font-size: 12px;
        }
        .form-item-title,
        .receiverList-li-bottom-item-bottom {
            display: inline-block;
        }
        .form-item-title {
            height: 20px;
            line-height: 20px;
            color: #999;
        }
        .form-item-input {
            &, .el-input__inner {
                width: 290px;
                height: 30px;
                border-color: #ddd;
                border-radius: 2px;
            }
            .el-date-editor.el-input {
                width: auto;
            }
        }
        .errMsg {
            position: absolute;
            left: 0;
            color: #FF6432;
            font-size: 12px;
            white-space: pre;
        }
    }
</style>
