<!-- eslint-disable vue/no-template-shadow -->
<!-- 单个添加的签约方 -->
<template>
    <div class="single-signer-container">
        <div class="single-signer-title">
            <p>
                <span @click="toggleSingleSigner"> {{ expanded ? '收起' : '展开' }}</span>
                <span @click="delSingleSigner">删除</span>
            </p>
        </div>
        <!-- 所有字段 -->
        <div class="single-signer-hiddenBlock" :class="{ 'point-view' : !expanded }">
            <!-- 公用合同属性，业务字段 -->
            <div class="single-signer-line" v-show="expanded">
                <div class="single-signer-line-title">
                    合同信息：
                </div>
                <div class="single-signer-form">
                    <SingleInput
                        v-for="(field, index) in publicFields"
                        :key="'publicField_'+index+'_'+field.type"
                        :field="field"
                        :index="index"
                        @update="handleUpdate"
                        type="publicFields"
                    ></SingleInput>
                    <div class="clear"></div>
                </div>
            </div>

            <!-- 签约方信息，存在多方签署 -->
            <div class="single-signer-line single-signer-line__singers">
                <div class="single-signer-line-title" v-show="expanded">
                    签约方基本信息：
                </div>
                <div class="single-signer-all">
                    <div class="single-signer-form"
                        v-for="(signers, order) in bizFields"
                        :key="order"
                    >
                        <p v-if="signers.signerName">{{ signers.signerName }}</p>
                        <div class="single-signer-form-content">
                            <SingleInput
                                v-for="(signerField, index) in signers.signerFields"
                                :key="'bizField_'+index+'_'+signerField.type"
                                :field="signerField"
                                :index="index"
                                @update="handleUpdate"
                                type="signers"
                                v-show="isShowExpandInput(signerField)"
                                :signerFields="signers.signerFields"
                                :isEnt="signers.type === 'ENTERPRISE'"
                            ></SingleInput>
                            <div class="clear"></div>
                        </div>
                    </div>
                    <!-- 加载提示 -->
                    <div class="loading" v-show="!domLoadFlag">
                        <i class="el-icon-loading"></i>
                        <span>数据加载中，请稍后......</span>
                    </div>
                </div>
            </div>

        </div>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import SingleInput from '../singleInput/SingleInput.vue';

export default {
    components: {
        SingleInput,
    },
    // eslint-disable-next-line vue/require-prop-types
    props: ['single', 'index'],
    data() {
        return {
            expanded: true,
            loadingTimer: null,
            domLoadFlag: false,
            // 异步加载业务字段
            bizFieldLimit: 20,
            asyncBizFields: [],
            // 所有字段
            cacheFields: {
                inputFields: [],
            },
            // 修改过的字段，用 map 去重，定时保存提交的数据，保存后会清空
            editedFields: new Map(),
        };
    },
    computed: {
        ...mapState({}),
        ...mapGetters([
            'getUserType',
        ]),
        // 公用合同属性, 业务字段-
        publicFields() {
            return this.cacheFields.inputFields.map((field, i) => {
                field.fieldIndex = i;
                return field;
            });
        },
        // 签约方信息属性
        bizFields() {
            return this.cacheFields.signers.map((item, i) => {
                item.signerFields.map((field, index) => {
                    field.fieldIndex = index;
                    field.roleIndex = i;
                    return field;
                });
                return item;
            });
        },
        // 折叠时展示的字段，判断企业、个人
        foldedViewFields() {
            let defaultFields = this.cacheFields.signers.filter(item => {
                return item.fieldName === '账号';
            });

            if (this.getUserType === 'Enterprise') {
                defaultFields = defaultFields.concat(this.cacheFields.signers.filter(item => {
                    return (item.fieldName === '企业名称' || item.fieldName === '经办人');
                }));
            }

            return defaultFields;
        },
    },
    watch: {
        // 区分折叠事件和字段的监听，避免折叠、展开影响字段值
        'single.expanded': {
            handler(v) {
                this.expanded = v;
            },
            immediate: true,
        },
        'single': {
            handler(v) {
                this.asyncLoadBizFields(JSON.parse(JSON.stringify(v)));
            },
            deep: true,
            immediate: true,
        },
    },
    methods: {
        // 业务字段数量上限500，超过阈值就异步加载
        asyncLoadBizFields(list) {
            const length = list.inputFields.length + list.signers.length;
            // 不是首次加载或者字段数量小于阈值就直接赋值
            if (this.domLoadFlag === true || length <= this.bizFieldLimit) {
                this.cacheFields = list;
                this.domLoadFlag = true;
            } else {
                const maxLength = Math.ceil(length / this.bizFieldLimit);
                this.cacheFields.signers = list.signers;

                for (let i = 0; i < maxLength; i++) {
                    (() => {
                        this.$nextTick(() => {
                            this.loadingTimer = setTimeout(() => {
                                // 批量赋值，异步加载
                                this.cacheFields.inputFields = this.cacheFields.inputFields.concat(list.inputFields.slice(i * this.bizFieldLimit, (i + 1) * this.bizFieldLimit));

                                // 最后一次循环是关闭 loading 提醒，清除定时器
                                if (i === (maxLength - 1)) {
                                    this.domLoadFlag = true;
                                    clearTimeout(this.loadingTimer);
                                }
                            }, i * 300);
                        });
                    })();
                }
            }
        },

        /* 单个签约方操作事件 */
        // 折叠展开当前签约方业务字段表单
        toggleSingleSigner() {
            this.cacheFields.expanded = !this.expanded;
            this.$emit('update', this.index, this.cacheFields);
        },
        // 删除当前签约方
        delSingleSigner() {
            this.$emit('delete', this.cacheFields.lineNumber);
        },
        // 字段更新
        handleUpdate(field, type) {
            let temp;
            const { value, fieldIndex, roleIndex, errorMessage } = field;
            if (type === 'publicFields') {
                temp = this.cacheFields.inputFields[fieldIndex];
                temp.value = value;
                temp.errorMessage = errorMessage;
                this.cacheFields.inputFields[fieldIndex] = temp;
            } else {
                temp = this.cacheFields.signers[roleIndex].signerFields[fieldIndex];
                temp.value = field.value;
                temp.errorMessage = errorMessage;
                this.cacheFields.signers[roleIndex].signerFields[fieldIndex] = temp;
            }
        },
        // 清空缓存的已修改字段
        clearCache() {
            this.editedFields.clear();
        },
        isShowExpandInput(item = []) {
            if (this.expanded) {
                return true;
            }
            return item.fieldName === '账号' || (
                this.getUserType === 'Enterprise' && (item.fieldName === '企业名称' || item.fieldName === '经办人')
            );
        },
    },
};

/**
 * 单个single的数据结构如下
 *
{
    "lineNumber":null,
    "signers":[
        {
            "roleId":"2215180990265951241",
            "routerOrder":1,
            "signerName":"",
            "type":"PERSION",
            "signerFields":[
                {
                    "fieldName":"账号",
                    "notNull":true,
                    "type":"TEXT",
                    "extendedAttribute":null,
                    "value": null
                },
                {
                    "fieldName":"姓名",
                    "notNull":false,
                    "type":"TEXT",
                    "extendedAttribute":null,
                    "value": null
                },
                {
                    "fieldName":"通知手机",
                    "notNull":false,
                    "type":"TEXT",
                    "extendedAttribute":null,
                    "value": null
                }
            ]
        }
    ],
    "inputFields":[
        {
            "fieldName":"合同标题",
            "notNull":false,
            "type":"TEXT",
            "extendedAttribute":null,
            "value": null
        },
        {
            "fieldName":"合同到期日",
            "notNull":false,
            "type":"BIZ_DATE",
            "extendedAttribute":null,
            "value": null
        },
        {
            "fieldName":"公司内部编号",
            "notNull":false,
            "type":"TEXT",
            "extendedAttribute":null,
            "value": null
        }
    ]
}
*/

</script>
<style lang="scss" scoped>
.single-signer-container{
            background: #ffffff;
            padding: 16px;
            margin: 16px 19px;
            margin-top: 0;
            &:last-child {
                margin-bottom: 0px;
            }
            .single-signer-title{
                position: relative;
                border-top: 1px dashed #e7e7e7;
                margin: 0 115px 0 25px;
                line-height: 30px;

                p{
                    position: absolute;
                    right: -90px;
                    top: -15px;

                    span{
                        display: inline-block;
                        width: 30px;
                        height: 30px;
                        margin-right: 6px;
                        font-size: 12px;
                        color: #1280d2;
                        cursor: pointer;
                    }
                }
            }
            .single-signer-hiddenBlock{
                // padding: 0 25px;

                .single-signer-line{
                    // margin: 0 30px 10px 50px;
                    // padding-bottom: 10px;
                    // border-bottom: 1px solid #e3e3e3;

                    &:last-child{
                        border-bottom: none;
                        padding-bottom: 0;
                    }

                    .single-signer-line-title{
                        font-size: 16px;
                        color: #000;
                        font-weight: 500;
                    }

                    .single-signer-form{
                        width: 100%;
                        font-weight: normal;
                        p {
                            height: 44px;
                            line-height: 44px;
                            color: #000;
                            background: #fafafa;
                            padding: 0 24px;
                            border-bottom: 1px solid #e9e9e9;
                            border-top: 1px solid #e9e9e9;
                            font-weight: 500;
                            font-size: 14px;
                         }

                        .single-signer-form-item-container{
                            float: left;
                        }

                        .signle-signer-form-item{
                            float: left;
                            margin-right: 70px;
                            position: relative;
                            padding-top: 36px;
                            // margin-top: 16px;
                            padding-bottom: 16px;
                            width: 240px;

                            .form-item-title{
                                position: absolute;
                                left: 0;
                                top: 0;
                                font-size: 14px;
                                color: #000;
                                height: 36px;
                                width: 100%;
                                span {
                                    position: absolute;
                                    bottom: 0;
                                    width: 100%;
                                    display: block;
                                }
                            }

                            .el-input{
                                width: 240px;
                                margin-top: 8px;
                            }

                            .errMsg{
                                position: absolute;
                                left: 0;
                                bottom: -2px;
                                color: #f76b26;
                                font-size: 12px;
                                white-space: pre;
                            }
                            .signle-signer-form-item-tips {
                                position: absolute;
                                left: 0;
                                bottom: -2px;
                                font-size: 12px;
                                &.signle-signer-form-item-tips_err {
                                    bottom: -10px;
                                }
                            }
                        }
                    }

                    .loading{
                        padding: 10px 0;
                        text-align: center;
                        color: #43adfd;
                        font-size: 12px;

                        .el-icon-loading{
                            margin-right: 3px;
                            color: #0092ff;
                            font-size: 20px;
                        }

                        .el-icon-loading,span{
                            vertical-align: middle;
                        }
                    }
                }

                // 签约方css 覆盖
                .single-signer-line.single-signer-line__singers {
                    .single-signer-all {
                        border: 1px solid #E9E9E9;
                        border-radius: 3px;
                    }
                    .single-signer-line-title{
                        margin: 18px 0;
                    }
                    .single-signer-form {
                        .single-signer-form-content {
                            padding: 0 24px;
                            .signle-signer-form-item {
                                margin-right: 53px;
                                margin-top: 16px;
                                padding-bottom: 16px;
                            }
                        }
                        &:first-child {
                            p {
                                border-top: none;
                            }
                        }
                    }
                }

                &.point-view{
                    padding-bottom: 0;
                    padding-top: 10px;

                    .single-signer-line{
                        padding-bottom: 0;
                    }
                }
            }
        }
</style>
