// http://jira.bestsign.tech/browse/SAAS-6567
<template>
    <div>
        <!-- 启用下载码免责声明 disclaimer -->
        <el-dialog
            title="功能使用提示"
            class="ssq-dialog disclaimer-dialog-wrap"
            :visible.sync="visible"
            :close-on-press-escape="false"
            :close-on-click-modal="false"
            :before-close="handleClose"
        >
            <template v-if="visible">
                <div class="disclaimer-content-wrap">
                    <div class="disclaimer-content">
                        <p>根据《电子签名法》相关规定，合法有效的电子合同，必须要保证签约主体能够随时调取查用，禁止签约方下载、查看的做法将违反了《电子签名法》的要求。</p>
                        <p>请确保签约人可以查看、下载已经签署的协议。</p>
                    </div>
                    <p class="agree-checkbox">
                        <el-checkbox v-model="checked">我已阅读并同意以上内容</el-checkbox>
                    </p>
                    <p class="confirm">
                        <el-button :disabled="!checked" type="primary" class="ssq-btn-confirm" @click="handleAgree">继续</el-button>
                    </p>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
export default {
    data() {
        return {
            visible: false,
            handleClose: null,
            handleAgree: null,
            checked: false,
        };
    },
    methods: {
        // 改写成 promise 形式，在调用方显得更清晰
        confirm() {
            return new Promise((resolve, reject) => {
                this.checked = false;
                this.visible = true;

                this.handleClose = () => {
                    this.visible = false;
                    reject(new Error('cancel set download code'));
                };

                this.handleAgree = () => {
                    this.visible = false;
                    this.visible = false;
                    resolve(true);
                };
            });
        },
    },
};
</script>

<style lang="scss">
    .disclaimer-dialog-wrap {
        .el-dialog {
            width: 450px;
            .el-dialog__body button {
                float: none;
            }
        }
        .disclaimer-content {
            font-size: 12px;
            line-height: 22px;
            // max-height: 200px;
            overflow: auto;
        }
        .agree-checkbox, .confirm {
            text-align: center;
            margin: 14px 0 12px;
        }
    }
    // .confirm {}
</style>
