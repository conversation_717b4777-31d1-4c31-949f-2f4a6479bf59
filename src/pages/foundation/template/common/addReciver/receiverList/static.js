const defaultRecipient = {
    routeOrder: 1,
    roleType: 'SPECIFIC_USER',
    userType: 'PERSON',
    userName: '',
    userId: '',
    enterpriseName: '',
    enterpriseId: '',
    userAccount: '',
    roleName: '',

    photoHref: '',

    requireIdentityAssurance: true,
    receiverType: 'SIGNER',

    handWriteNotAllowed: false,
    forceHandWrite: false,
    handWritingRecognition: false,
    notification: '',
    privateLetter: '',
    attachmentRequired: false,

    faceFirst: false,
    faceVerify: false,
    messageVerifyCode: '',
    thirdPartyPlatformId: '',
    contractPayer: false, // 签署付费方
    mustReadBeforeSign: false, // 阅读完毕再签署

    status_faceFirst: false,
    status_faceVerify: false,
    status_handWriteNotAllowed: false,
    status_forceHandWrite: false,
    status_handWritingRecognition: false,
    status_notification: false,
    status_privateLetter: false,
    status_mustReadBeforeSign: false,
    status_newAttachmentRequired: false,

    // 错误信息
    errors: [],

    // 展示详细信息
    assignFullInfo: true,
    // 附件
    attachmentList: [],
    requireEnterIdentityAssurance: false, // 企业经办人是否实名
    contractDownloadControl: false, // 下载码权限
    ifProxyClaimer: false, // 前台代收
    archiveId: '', // 关联档案柜
};
const defaultPlaceHolder = {
    routeOrder: 1,
    assignFullInfo: false,
    roleType: 'PLACEHOLDER', // 用户角色的类型 非固定角色(PLACEHOLDER) 具体用户(SPECIFIC_USER)
    userType: 'PERSON', // 账户类型
    userName: '待Excel导入', // 接收方姓名
    userId: '',
    enterpriseName: '', // 公司名
    enterpriseId: '', // 公司Id
    userAccount: '', // 账户名称
    notification: '',
    roleName: '',

    receiverType: 'SIGNER', // 签署 or 抄送

    requireIdentityAssurance: true, // 是否需要实名
    privateLetter: '',
    faceFirst: false,
    faceVerify: false,
    handWriteNotAllowed: false,
    forceHandWrite: false,
    handWritingRecognition: false,
    messageVerifyCode: '',
    thirdPartyPlatformId: '',
    contractPayer: false, // 签署付费方
    mustReadBeforeSign: false, // 阅读完毕再签署

    // 样式交互用数据
    status_privateLetter: false,
    status_faceFirst: false,
    status_faceVerify: false,
    status_handWriteNotAllowed: false,
    status_forceHandWrite: false,
    status_handWritingRecognition: false,
    status_messageVerifyCode: false,
    status_thirdPartyPlatformId: false,
    status_mustReadBeforeSign: false,
    status_newAttachmentRequired: false,

    attachmentList: [],
    hasAuthenticated: false, // 是否已实名认证过
    hasRegister: true,
    photoHref: '',
    passIdNumber: false, // 发件人代传身份信息
    requireEnterIdentityAssurance: false, // 经办人是否实名
    contractDownloadControl: false, // 下载码权限
    ifProxyClaimer: false, // 前台代收
    archiveId: '', // 关联档案柜
};
export {
    defaultRecipient,
    defaultPlaceHolder,
};
