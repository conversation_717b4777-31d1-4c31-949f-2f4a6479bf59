<!-- 模板-添加签约方-签约方单行组件 -->
<template>
    <div class="content">
        <div v-if="templateStatus == 'use'">
            <!-- 使用模板，批量导入用户-单个-批量切换tab -->
            <div class="card-tab">
                <div
                    class="card-tab-item card-tab-dozen"
                    :class="!singleToggle && 'active'"
                    @click="handleBatchInput"
                >
                    <span>签约方批量导入</span>
                    <span class="nums" v-if="importedNum">（{{ importedNum }}）</span>
                </div>
                <div
                    class="card-tab-item card-tab-single"
                    :class="{
                        'active': singleToggle,
                    }"
                    @click="handleSingeInput"
                    v-if="!operateMarkId"
                >
                    <span>签约方单个手动添加</span>
                </div>
                <div class="clear"></div>
            </div>
            <!-- 批量导入按钮 -->
            <div class="use-add-btns" v-show="!singleToggle">
                <template v-if="!operateMarkId">
                    <el-upload
                        class="edit-icon"
                        :action="uploadUrl"
                        :headers="uploadHeaders"
                        :data="{fileType: 'EXCEL'}"
                        :before-upload="beforeUpload"
                        :on-success="onUploadSuccess"
                        :on-error="onUploadError"
                        :http-request="uploadRequest"
                        :show-file-list="false"
                    >
                        <div class="ssq-btn-confirm">Excel 批量导入</div>
                    </el-upload>
                    <p class="use-add-download">
                        <span>可变签约主体需要批量导入，请先</span>
                        <a :href="excelUrl" download="Excel模板">下载Excel模板</a>
                        ，填写完成后，直接导入
                    </p>
                </template>
                <div class="excel-import" v-show="importedNum">
                    <i class="el-icon-ssq-tongguo"></i>
                    <span class="excel-msg">
                        成功导入{{ importedNum }}{{ placeholderUser.length > 1 ? '组' : '个' }}签约方 <span class="excel-import-clear" @click="handleClearExcel">清除</span>
                    </span>
                </div>
            </div>
            <div class="use-add-btns" v-show="hasPictureField && !singleToggle && !operateMarkId">
                <el-upload
                    class="edit-icon"
                    :action="picUploadUrl"
                    :headers="uploadHeaders"
                    :on-success="onPicUploadSuccess"
                    :on-error="onUploadError"
                    :http-request="uploadPicRequest"
                    :show-file-list="false"
                >
                    <div class="ssq-btn-confirm">上传图片</div>
                </el-upload>
                <p v-if="hasBatchUploadPic" class="excel-import">
                    <i class="el-icon-ssq-tongguo"></i><span class="excel-msg">成功上传</span>
                </p>
                <p class="use-add-download">
                    请上传.zip格式的压缩包文件，系统会按照图片的文件名将图片匹配至对应的合同
                </p>
            </div>
            <!-- 合同描述字段 -->
            <ContractDescriptions
                v-show="singleToggle"
                :input-fields="descriptionsInputFields"
                ref="contractDescriptionsInputs"
            >
            </ContractDescriptions>
        </div>
        <!-- 签约方list -->
        <ul>
            <Draggable
                :list="recipients"
                :options="isDisDrag"
                @start="selectDisabled = true"
                @end="onDragEnd"
            >
                <li
                    v-for="(recipient, index) in recipients"
                    :key="`${recipient.roleType}_${recipient.userType}_${recipient.routeOrder}_${index}`"
                    class="recipient-li"
                    :class="[
                        recipient.userType,
                        {
                            'order-sign': signOrdered,
                            'recipient-li-holder': recipient.roleType == 'PLACEHOLDER' && templateStatus == 'use',
                            'recipient-li_disabled': templateStatus === 'use' && canModifyWhenUsed && recipient.roleType !== 'PLACEHOLDER' && recipient.notUsed
                        },
                    ]"
                >
                    <!-- 签约方卡片上半部分 -->
                    <div class="receiverList-li-top">
                        <h1 class="receiverList-li-top-title">
                            <span>
                                签约{{ recipient.userType==='ENTERPRISE' ? '企业' : '个人' }}（{{ recipient.roleType === 'PLACEHOLDER' ? '可变签约主体' : '固定签约主体' }}）
                            </span>
                            <el-tooltip
                                class="item"
                                effect="dark"
                                placement="bottom"
                            >
                                <span slot="content">
                                    {{ recipient.roleType === 'PLACEHOLDER' ? '使用模板时才能确定的签约主体' : '创建模板时就已经确定的签约主体' }}
                                </span>
                                <i class="el-icon-ssq-bangzhu cursor-point"></i>
                            </el-tooltip>
                        </h1>
                        <div class="receiverList-li-top-selects">
                            <!-- 是否需要实名 -->
                            <!-- 个人 -->
                            <el-select
                                v-if="recipient.userType == 'PERSON'"
                                placeholder="需要实名"
                                class="recipient-needverified"
                                :disabled="!canModifyWhenUsed || (templateStatus == 'use' && recipient.notUsed)"
                                popper-class="sign-el-select"
                                v-model="recipient.requireIdentityAssurance"
                                @change="onChangeRequireIdentityAssurance($event, index)"
                            >
                                <el-option
                                    v-for="item in realNameOption"
                                    :key="item.label + index"
                                    :label="item.label"
                                    :value="item.value"
                                    :disabled="!item.value && (recipient.status_faceVerify || recipient.status_faceFirst)"
                                >
                                </el-option>
                            </el-select>
                            <!-- 企业 -->
                            <el-select
                                placeholder="经办人需实名"
                                v-else-if="recipient.userType == 'ENTERPRISE'"
                                class="recipient-needverified__ent"
                                popper-class="sign-el-select"
                                v-model="recipient.requireEnterIdentityAssurance"
                                :disabled="!canModifyWhenUsed || recipient.receiverType == 'CC_USER' || recipient.participationType === 'SEAL_AND_SIGNATURE'"
                            >
                                <el-option
                                    v-for="item in realNameOptionEnt"
                                    :key="item.label + index"
                                    :label="item.label"
                                    :value="item.value"
                                    :disabled="!item.value && (recipient.status_faceVerify || recipient.status_faceFirst)"
                                >
                                </el-option>
                            </el-select>
                            <!-- 签署／抄送 select -->
                            <el-select placeholder="签署"
                                class="recipient-signtype"
                                popper-class="sign-el-select"
                                v-model="recipient.participationType"
                                :disabled="templateStatus == 'use' || isDynamicTemplate"
                                @change="onChangeSignType($event, index)"
                            >
                                <el-option
                                    v-for="item in (recipient.userType == 'PERSON' ? participationTypeOption : participationTypeOptionEnt)"
                                    :key="item.label + index"
                                    :label="item.label"
                                    :value="item.value"
                                    :disabled="isDisableSignTypeSelect(recipient) && item.value==='CC_USER'"
                                >
                                </el-option>
                            </el-select>
                            <!-- 更多 select -->
                            <el-dropdown class="recipient-more"
                                trigger="click"
                                @command="moreSelectClick"
                                @visible-change="handleVisibleChange($event,index)"
                                ref="messageDrop"
                            >
                                <el-button :disabled="(templateStatus == 'use' && recipient.notUsed)">
                                    更多
                                    <i class="el-icon-ssq-xialacaidan el-icon--right"></i>
                                    <i v-if="checkShowDropDownNew(recipient)" class="el-icon-ssq-biaoqiannew"></i>
                                </el-button>
                                <el-dropdown-menu slot="dropdown" class="sign-el-dropdown-menu">
                                    <el-dropdown-item
                                        v-if="isShowFaceVerify(recipient)"
                                        :disabled="recipient.receiverType == 'CC_USER' || !canModifyWhenUsed"
                                        command="faceFirst"
                                        :index="index"
                                    >
                                        优先刷脸，备用验证码签署
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        v-if="isShowFaceVerify(recipient)"
                                        :disabled="recipient.receiverType == 'CC_USER' || !canModifyWhenUsed"
                                        command="faceVerify"
                                        :index="index"
                                    >
                                        必须刷脸签署
                                    </el-dropdown-item>
                                    <!-- 选择需实名才可以有该选项 -->
                                    <el-dropdown-item
                                        v-if="recipient.requireIdentityAssurance && recipient.userType == 'PERSON' && recipient.receiverType !== 'CC_USER'"
                                        :disabled="!canModifyWhenUsed"
                                        command="handWriteNotAllowed"
                                        :index="index"
                                    >
                                        不允许手写签名
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        v-if="recipient.userType == 'PERSON' && recipient.receiverType !== 'CC_USER'"
                                        command="forceHandWrite"
                                        :disabled="!canModifyWhenUsed"
                                        :index="index"
                                    >
                                        必须手写签名
                                    </el-dropdown-item>
                                    <!-- <el-dropdown-item
                                        v-if="recipient.roleType != 'PLACEHOLDER' && recipient.userType==='PERSON'"
                                        :disabled="templateStatus == 'edit' &&!recipient.assignFullInfo || notAllowNotify || !noticeSpecificUser"
                                        command="notification"
                                        :index="index">
                                        填写通知手机
                                    </el-dropdown-item> -->
                                    <el-dropdown-item
                                        command="privateLetter"
                                        :disabled="!canModifyWhenUsed"
                                        :index="index"
                                    >
                                        添加签约须知
                                    </el-dropdown-item>
                                    <el-tooltip
                                        :manual="true"
                                        :value="checkTrialInfoStatus('attachmentRequired',trialFeatureData,1)&& isTrialTipShowIndex === index"
                                        effect="light"
                                        placement="left"
                                        popper-class="dropdown-tool-tip"
                                    >
                                        <div slot="content" @click="handleTrialClick(trialFeatureData,'attachmentRequired')" class="content"><i class="el-icon-ssq-jingshitanhaox"></i>{{ checkTrialInfoStatus('attachmentRequired',trialFeatureData,3) }}</div>
                                        <el-dropdown-item
                                            :disabled="recipient.receiverType == 'CC_USER' || !canModifyWhenUsed"
                                            command="attachmentRequired"
                                            :index="index"
                                        >
                                            添加合同附属资料
                                            <el-tooltip class="item"
                                                effect="dark"
                                                content="提交的资料用于帮助您追踪合同履约状态，判断业务执行是否正常。设置后，该签署人必须按要求提交"
                                                placement="top"
                                            ><i class="el-icon-ssq-wenhao tips"></i>
                                            </el-tooltip>
                                            <i v-if="checkTrialInfoStatus('attachmentRequired',trialFeatureData,2)" class="el-icon-ssq-biaoqiannew">
                                            </i>
                                        </el-dropdown-item>
                                    </el-tooltip>
                                    <el-dropdown-item
                                        v-if="!isDynamicTemplate && !hybridUser"
                                        :disabled="recipient.receiverType == 'CC_USER' || !canModifyWhenUsed"
                                        command="newAttachmentRequired"
                                        :index="index"
                                    >
                                        提交签约主体资料
                                        <el-tooltip class="item"
                                            effect="dark"
                                            content="提交的资料用于帮助您查验签约方的主体资质，判断是否可以与其开始或继续开展业务。如果签约方已提交过的相同资料可以不再重复提交"
                                            placement="top"
                                        ><i class="el-icon-ssq-wenhao tips"></i>
                                        </el-tooltip>
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        command="contractDownloadControl"
                                        :disabled="recipient.receiverType == 'CC_USER'"
                                        v-if="checkFeat.contractDownload"
                                        :index="index"
                                    >
                                        启用下载码
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        v-if="$t('lang') === 'zh' && recipient.receiverType == 'SIGNER' && checkFeat.singerPay"
                                        :disabled="!isAableContractPayer()"
                                        command="contractPayer"
                                        :index="index"
                                    >付费方</el-dropdown-item>
                                    <el-dropdown-item
                                        v-if="needReadBeforeSign"
                                        command="mustReadBeforeSign"
                                        :index="index"
                                    >
                                        阅读完毕再签署
                                    </el-dropdown-item>
                                    <el-tooltip
                                        :manual="true"
                                        :value="checkTrialInfoStatus('handWritingRecognition',trialFeatureData,1) && isTrialTipShowIndex === index && recipient.userType === 'PERSON'"
                                        effect="light"
                                        placement="left"
                                        popper-class="dropdown-tool-tip"
                                    >
                                        <div slot="content" @click="handleTrialClick(trialFeatureData,'handWritingRecognition')" class="content"><i class="el-icon-ssq-jingshitanhaox"></i>{{ checkTrialInfoStatus('handWritingRecognition',trialFeatureData,3) }}</div>
                                        <el-dropdown-item
                                            v-if="recipient.userType === 'PERSON' && recipient.receiverType !== 'CC_USER' "
                                            command="handWritingRecognition"
                                            :disabled="!canModifyWhenUsed"
                                            :index="index"
                                        >
                                            开启手写笔迹识别
                                            <i v-if="checkTrialInfoStatus('handWritingRecognition',trialFeatureData,2)" class="el-icon-ssq-biaoqiannew">
                                            </i>
                                        </el-dropdown-item>
                                    </el-tooltip>
                                </el-dropdown-menu>
                            </el-dropdown>
                        </div>
                    </div>
                    <!-- 签约方卡片主体 -->
                    <div class="card-container clear receiverList-li-bottom" :class="!isShowCanJoinInContractBtn(recipient)&&'padding-right-24'">
                        <div class="receiverList-li-bottom__icons">
                            <!-- 顺序 input -->
                            <div v-show="signOrdered" class="recipient-order receiverList-li-bottom-item">
                                <input type="text"
                                    v-model.number="recipient.routeOrder"
                                    @change="changeOrderNo"
                                    :disabled="templateStatus === 'use'"
                                >
                            </div>
                            <!-- 拖拽手柄 -->
                            <div class="recipient-handle el-icon-ssq-yidongbiaoqian receiverList-li-bottom-item"
                                v-show="signOrdered"
                            >
                            </div>
                            <!-- 头像 -->
                            <div class="recipient-photo receiverList-li-bottom-item">
                                <img v-if="recipient.photoHref"
                                    width="30"
                                    height="30"
                                    :src="`${recipient.photoHref}?access_token=${$cookie.get('access_token')}`"
                                >
                                <template v-else>
                                    <i v-if="recipient.userType == 'PERSON'" class="el-icon-ssq-user-filling"></i>
                                    <i v-else class="el-icon-ssq-company"></i>
                                </template>
                            </div>
                        </div>
                        <!-- 可变签约主体，账号占位符 -->
                        <div
                            v-if="isChangeableRole(recipient)"
                            class="receiverList-li-bottom-item receiverList-li-bottom-item__account"
                        >
                            <div class="sign-principal-account receiverList-li-bottom-item-top">
                                {{ recipient.userType === 'PERSON' ? '姓名' : $t('addReceiver.signSubjectEnt') }}
                            </div>
                            <div class="receiverList-li-bottom-item-bottom">
                                <el-input
                                    placeholder="占位符（使用时再填写）"
                                    class="recipient-useraccout"
                                    :disabled="true"
                                >
                                </el-input>
                            </div>
                        </div>
                        <!-- 固定签约主体 || 使用模板时，可变签约主体单个输入 -->
                        <template v-if="isRoleCanInput(recipient)">
                            <!-- 固定签约主体为个人 -->
                            <template v-if="recipient.userType == 'PERSON'">
                                <!-- 姓名 input -->
                                <div class="receiverList-li-bottom-item receiverList-li-bottom-item__person-username">
                                    <div class="receiverList-li-bottom-item-top sign-principal-body">
                                        <template v-if="templateStatus === 'use' && recipient.roleType === 'PLACEHOLDER' && singleToggle && recipient.passIdNumber">姓名(代传真实姓名)</template>
                                        <template v-else>
                                            <span class="red" v-if="recipient.personNameNeeded">*</span>
                                            姓名
                                        </template>
                                    </div>
                                    <div
                                        class="receiverList-li-bottom-item-bottom"
                                    >
                                        <el-input placeholder="用于签约身份核对"
                                            class="recipient-username"
                                            v-model.trim="recipient.userName"
                                            :disabled="templateStatus == 'edit' && !recipient.assignFullInfo || (templateStatus == 'use' &&recipient.notUsed)"
                                            @focus="onFocusUsername(index)"
                                            @blur="onBlurUsername(index)"
                                        >
                                        </el-input>
                                        <p
                                            v-if="judgeErrorShow(recipient.errors, 'userName')"
                                            class="receiverList-li-bottom-item-bottom__error"
                                        >
                                            {{ judgeErrorShow(recipient.errors, 'userName') }}
                                        </p>
                                    </div>
                                </div>
                                <!-- 身份证号 -->
                                <div
                                    class="receiverList-li-bottom-item receiverList-li-bottom-item__idNumber"
                                >
                                    <div class="receiverList-li-bottom-item-top">
                                        <span class="red" v-if="recipient.personIdNumberNeeded">*</span>
                                        身份证号
                                        <span
                                            v-if="recipient.passIdNumber"
                                        >
                                            (代传身份证号)
                                        </span>
                                    </div>
                                    <div class="receiverList-li-bottom-item-bottom">
                                        <el-input
                                            placeholder="用于签约身份核对"
                                            :disabled="templateStatus == 'edit' && !recipient.assignFullInfo || (templateStatus == 'use' &&recipient.notUsed)"
                                            class="recipient-idNumber"
                                            v-model.trim="recipient.idNumber"
                                            @focus="onFocusIdNumber(index)"
                                            @blur.native.capture="onBlurIdNumber(index)"
                                        >
                                        </el-input>
                                        <p
                                            v-if="judgeErrorShow(recipient.errors, 'idNumber')"
                                            class="receiverList-li-bottom-item-bottom__error"
                                        >
                                            {{ judgeErrorShow(recipient.errors, 'idNumber') }}
                                        </p>
                                    </div>
                                </div>
                            </template>
                            <!-- 固定签约主体为企业 -->
                            <template v-else>
                                <!-- 企业名称 input -->
                                <div class="receiverList-li-bottom-item receiverList-li-bottom-item__ent-companyname">
                                    <div class="receiverList-li-bottom-item-top sign-principal-body">
                                        <span
                                            v-if="templateStatus === 'use'"
                                            class="red"
                                        >
                                            *
                                        </span>
                                        {{ $t('addReceiver.signSubjectEnt') }}
                                    </div>
                                    <div class="receiverList-li-bottom-item-bottom">
                                        <el-autocomplete
                                            v-model="recipient.enterpriseName"
                                            placeholder="用于签约身份核对"
                                            class="recipient-companyname inline-input fcl-input"
                                            :popper-class="`ent-name-popper sign-el-autocomplete-popper sign-el-autocomplete-popper--type-one ${recipient.enterpriseName.trim() ? '' : 'receiver-autocomplete-popper'}`"
                                            :disabled="templateStatus == 'edit' && !recipient.assignFullInfo || (templateStatus == 'use' && recipient.notUsed)"
                                            :fetch-suggestions="onFetchSuggestions.bind(null, index)"
                                            @select="selectEnpName"
                                            @blur.native.capture="onBlurEnpName"
                                            @focus.native.capture="onFocusEntName(index)"
                                        >
                                        </el-autocomplete>
                                        <p
                                            v-if="judgeErrorShow(recipient.errors, 'enterpriseName')"
                                            class="receiverList-li-bottom-item-bottom__error"
                                        >
                                            {{ judgeErrorShow(recipient.errors, 'enterpriseName') }}
                                        </p>
                                        <p
                                            class="receiverList-li-bottom-item-bottom__tips"
                                            v-if="!(recipient.errors && recipient.errors.length)"
                                        >
                                            提示：签约方的企业名称完全一致才能签署
                                        </p>
                                    </div>
                                </div>
                                <!-- 经办人-姓名 input -->
                                <div class="receiverList-li-bottom-item receiverList-li-bottom-item__ent-username">
                                    <div class="receiverList-li-bottom-item-top agent">
                                        经办人
                                    </div>
                                    <div class="receiverList-li-bottom-item-bottom">
                                        <el-input
                                            placeholder="经办人姓名"
                                            class="recipient-username"
                                            v-model.trim="recipient.userName"
                                            :disabled="templateStatus == 'edit' && !recipient.assignFullInfo || (templateStatus == 'use' && recipient.notUsed)"
                                            @focus="onFocusUsername(index)"
                                            @blur="onBlurUsername(index)"
                                        >
                                        </el-input>
                                        <p
                                            v-if="judgeErrorShow(recipient.errors, 'userName')"
                                            class="receiverList-li-bottom-item-bottom__error"
                                        >
                                            {{ judgeErrorShow(recipient.errors, 'userName') }}
                                        </p>
                                    </div>
                                </div>
                            </template>
                        </template>
                        <!-- 账号 -->
                        <div v-if="!isChangeableRole(recipient)" class="receiverList-li-bottom-item receiverList-li-bottom-item__account">
                            <div class="sign-principal-account receiverList-li-bottom-item-top">
                                <span class="red">
                                    *
                                </span>
                                <!-- 使用模板时，判断是否配置了前台代收 -->
                                <template
                                    v-if="templateStatus == 'use' && recipient.userType === 'ENTERPRISE'"
                                >
                                    <span
                                        class="receiverListUseAccountReceptionCollection__common"
                                    >
                                        接收手机/邮箱
                                    </span><span class="grey" v-if="recipient.receiverType==='SIGNER'">（最多支持5个，可用分号隔开）</span>
                                </template>
                                <template v-else>
                                    接收手机/邮箱
                                </template>

                                <!-- 切换详细信息 -->
                                <el-checkbox
                                    class="switchDetail"
                                    v-if="templateStatus == 'edit'
                                        && recipient.roleType != 'PLACEHOLDER'"
                                    :value="!recipient.assignFullInfo"
                                    @change="changeDetailedInfoSwitch(index)"
                                >
                                    使用模板时填写
                                </el-checkbox>
                            </div>
                            <div class="receiverList-li-bottom-item-bottom">
                                <template>
                                    <el-autocomplete
                                        v-model.trim="recipient.userAccount"
                                        :placeholder="isSelectProxy(recipient) ? '' : '填写手机/邮箱'"
                                        :props="{
                                            label: 'text'
                                        }"
                                        class="recipient-useraccout"
                                        popper-class="receiver-autocomplete-popper sign-el-autocomplete-popper sign-el-autocomplete-popper--type-one"
                                        :disabled="isSelectProxy(recipient) ||templateStatus == 'edit' && !recipient.assignFullInfo || (templateStatus == 'use' && recipient.notUsed)"
                                        :fetch-suggestions="searchAccounts.bind(null, index)"
                                        @select="selectUserAccount($event, index)"
                                        @focus.native.capture="onFocusUseraccout(index)"
                                        @blur.native.capture="onBlurUseraccout(index)"
                                    >
                                    </el-autocomplete>
                                    <div
                                        v-if="isSelectProxy(recipient)"
                                        class="recipient-useraccout-proxy"
                                    >
                                        <span>对方前台代收</span>
                                        <i
                                            class="el-icon-ssq-delete"
                                            @click="handleSwitchReceptionCollection(index, false)"
                                        >
                                        </i>
                                    </div>
                                    <div
                                        v-if="receptionCollection &&recipient.userType === 'ENTERPRISE'&&templateStatus==='use'&&recipient.receiverType==='SIGNER'"
                                        class="recipient-useraccout-suffix"
                                        @click="handleSwitchReceptionCollection(index, true)"
                                    >
                                        <el-tooltip class="item" effect="dark" placement="top">
                                            <div slot="content">
                                                {{ $t('addReceiver.accountReceptionCollectionTip1') }}<br />{{ $t('addReceiver.accountReceptionCollectionTip2') }}
                                            </div>
                                            <span>代</span>
                                        </el-tooltip>
                                    </div>
                                    <p
                                        v-if="judgeErrorShow(recipient.errors, 'userAccount')"
                                        class="receiverList-li-bottom-item-bottom__error"
                                    >
                                        {{ judgeErrorShow(recipient.errors, 'userAccount') }}
                                    </p>
                                    <p v-if="recipient.foreigners === 1 && !(recipient.errors || []).some((item) => item.fieldName === 'userAccount')" class="receiverList-li-bottom-item-bottom__tips receiverList-li-bottom-item__account">
                                        提示：该签约方为境外人士，实名认证有风险，请先核对该人员的身份
                                    </p>
                                </template>
                            </div>
                        </div>
                        <!-- 身份证号 -->
                        <!-- 1. 使用模板，可变签约主体，个人，签约方单个添加时展示 -->
                        <!-- 2. 使用/编辑模板，固定签约主体，个人 -->
                        <!-- <div
                            v-if="
                                recipient.userType === 'PERSON' &&
                                    (
                                        (templateStatus === 'use' && recipient.roleType === 'PLACEHOLDER' &&  singleToggle)
                                        ||
                                        recipient.roleType !== 'PLACEHOLDER'
                                    )"
                            class="receiverList-li-bottom-item receiverList-li-bottom-item__idNumber">
                            <div class="receiverList-li-bottom-item-top">
                                <span class="red" v-if="recipient.personIdNumberNeeded">*</span>
                                身份证号
                                <span
                                    v-if="recipient.passIdNumber">
                                    (代传身份证号)
                                </span>
                            </div>
                            <div class="receiverList-li-bottom-item-bottom">
                                <el-input
                                    placeholder="用于签约身份核对"
                                    :disabled="templateStatus == 'edit' && !recipient.assignFullInfo || (templateStatus == 'use' &&recipient.notUsed)"
                                    class="recipient-idNumber"
                                    v-model.trim="recipient.idNumber"
                                    @focus="onFocusIdNumber(index)"
                                    @blur.native.capture="onBlurIdNumber(index)">
                                </el-input>
                                <p
                                    v-if="judgeErrorShow(recipient.errors, 'idNumber')"
                                    class="receiverList-li-bottom-item-bottom__error">
                                    {{ judgeErrorShow(recipient.errors, 'idNumber') }}
                                </p>
                            </div>
                        </div> -->
                        <!-- 代号、业务角色 -->
                        <div class="receiverList-li-bottom-item receiverList-li-bottom-item__roleName">
                            <div class="recipient-single-code-name receiverList-li-bottom-item-top">
                                <span
                                    v-if="isRoleNameNecessary(recipient)"
                                    class="red"
                                >
                                    *
                                </span>
                                业务角色
                                <el-tooltip class="item" effect="dark" placement="top">
                                    <div slot="content">能帮助您识别签约方，方便管理</div>
                                    <i class="el-icon-ssq-bangzhu cursor-point"></i>
                                </el-tooltip>
                            </div>
                            <div class="receiverList-li-bottom-item-bottom">
                                <el-input
                                    placeholder="如员工/经销商"
                                    class="recipient-single-code"
                                    v-model.trim="recipient.roleName"
                                    @focus="spliceErr(recipient.errors, 'roleName')"
                                    @blur="onBlurRoleName(index)"
                                    :disabled="templateStatus == 'use'"
                                    :maxlength="30"
                                >
                                </el-input>
                                <p
                                    v-if="judgeErrorShow(recipient.errors, 'roleName')"
                                    class="receiverList-li-bottom-item-bottom__error"
                                >
                                    {{ judgeErrorShow(recipient.errors, 'roleName') }}
                                </p>
                            </div>
                        </div>
                        <!-- 通知手机。合同接收人，考虑到老模版，notification存在值吗，则展示 saas-11313 -->
                        <!-- 1. 使用模板，可变签约主体，签约方单个添加时展示 -->
                        <!-- 2. 固定签约主体，recipient.status_notification为true时展示 -->
                        <div
                            v-if="
                                recipient.notification && ((templateStatus === 'use' && recipient.roleType === 'PLACEHOLDER' && singleToggle) || recipient.status_notification)"
                            class="receiverList-li-bottom-item receiverList-li-bottom-item__notification"
                        >
                            <div class="receiverList-li-bottom-item-top">
                                通知手机
                            </div>
                            <div class="receiverList-li-bottom-item-bottom">
                                <el-input
                                    placeholder="通知手机"
                                    class="recipient-notification"
                                    :disabled="!recipient.ifProxyClaimer && notAllowNotify"
                                    v-model.trim="recipient.notification"
                                >
                                </el-input>
                            </div>
                        </div>
                        <!-- excel批量导入说明 -->
                        <div
                            v-if="
                                recipient.roleType === 'PLACEHOLDER'
                                    &&
                                    ((templateStatus === 'use' && !singleToggle) || templateStatus === 'edit')"
                            class="receiverList-li-bottom-item mult-import-tips"
                        >
                            <i class="el-icon-ssq-tishi1"></i>
                            <template v-if="templateStatus === 'edit'">
                                在当前设置模板时可设置对该接收方的签约要求，无需填写具体账号
                            </template>
                            <template v-else>
                                等待批量导入
                            </template>
                        </div>
                        <!-- 固定签署方在使用模版时 控制 是否参与改合同的签署 -->
                        <span class="console-link"
                            v-if="isShowCanJoinInContractBtn(recipient)"
                        >
                            <span class="inline-block-con switch-btn" @click.prevent.stop="changeRecipientNotUsed(index)">
                                <input type="checkbox" name="using" class="chooseBtn" :checked="!recipient.notUsed">
                                <label for="using" class="choose-label"></label>
                            </span>
                        </span>
                    </div>
                    <!-- 删除按钮 -->
                    <div class="recipient-delete"
                        v-if="templateStatus == 'edit'"
                    >
                        <i class="delete-btn el-icon-ssq-delete"
                            @click="clickDeleteRecipientBtn(index)"
                        >
                        </i>
                    </div>

                    <!-- 更多列表 -->
                    <ExpandItemList
                        v-show="!(templateStatus == 'use' && recipient.notUsed)"
                        :index="index"
                        :recipient="recipient"
                        :isEdit="templateStatus === 'edit'"
                        @close="handleExpandClose"
                        @add-annex-item="addAnnexItem"
                        @remove-annex-item="removeAnnexItem"
                    >
                    </ExpandItemList>
                </li>
            </Draggable>
        </ul>
        <!-- 编辑模板，添加固定和可变签约主体 -->
        <div class="add-subject-btns" v-if="templateStatus == 'edit'">
            <!-- 固定签约主体 -->
            <div class="add-subject-btns-fixed">
                <div class="add-subject-btns-fixed__main">
                    添加固定签约主体&nbsp;
                    <i class="el-icon-ssq-xialajiantou"></i>
                </div>
                <div class="add-subject-btns-fixed__personal add-subject-btns-fixed__default-none"
                    @click="clickAddPersonalRecipientBtn(recipients.length + 1)"
                >
                    <i class="el-icon-ssq-jia"></i>&nbsp;
                    添加固定签约个人
                </div>
                <div
                    class="add-subject-btns-fixed__enterprise add-subject-btns-fixed__default-none"
                    @click="clickAddEnterpriseRecipientBtn(recipients.length + 1)"
                >
                    <i class="el-icon-ssq-jia"></i>&nbsp;
                    添加固定签约企业
                </div>
            </div>
            <!-- 可变签约主体 -->
            <div
                class="add-subject-btns-variable"
                :class="{
                    'add-subject-btns-variable__disable': (!multipleDynamicSigner && placeholderUser.length >= 1)
                }"
            >
                <div class="add-subject-btns-variable__main">
                    添加可变签约主体&nbsp;
                    <i class="el-icon-ssq-xialajiantou"></i>
                </div>
                <div
                    v-if="!multipleDynamicSigner && placeholderUser.length >= 1"
                    class="add-subject-btns-variable__disable-info"
                >
                    <i class="el-icon-ssq-warm-filling"></i>
                    <span>签约方中已经添加了可变签约主体，如想改变签约主体，</span><br>
                    &nbsp;&nbsp;&nbsp;&nbsp;<span>请先删除后重新添加</span>
                </div>
                <template v-else>
                    <div
                        class="add-subject-btns-variable__personal add-subject-btns-variable__default-none"
                        @click="addVariableSubject('PERSON')"
                    >
                        <i class="el-icon-ssq-jia"></i>&nbsp;
                        <p class="add-subject-btns-variable__description-con">
                            <span class="add-subject-btns-variable__description-one">添加可变签约个人</span>
                            <span class="add-subject-btns-variable__description-two">可变签约个人可支持批量导入和单个手动添加</span>
                        </p>
                    </div>
                    <div
                        class="add-subject-btns-variable__enterprise add-subject-btns-variable__default-none"
                        @click="addVariableSubject('ENTERPRISE')"
                    >
                        <i class="el-icon-ssq-jia"></i>&nbsp;
                        <p class="add-subject-btns-variable__description-con">
                            <span class="add-subject-btns-variable__description-one">添加可变签约企业</span>
                            <span class="add-subject-btns-variable__description-two">可变签约企业可支持批量导入和单个手动添加</span>
                        </p>
                    </div>
                </template>

            </div>
        </div>
        <Disclaimer ref="disclaimer" />
        <ChoseBox v-if="choseBoxDialog.visible" :type="choseBoxDialog.type" @close="choseBoxDialog.visible=false" @confirm="choseBoxConfirm"></ChoseBox>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import Draggable from 'vuedraggable';
import resRules from 'src/common/utils/regs.js';
import { checkExcelUploadLimit } from 'src/common/utils/fileLimit';
import ExpandItemList from 'foundation_pages/template/common/addReciver/moreList/ExpandItemList.vue';
import ContractDescriptions from '../contractDescriptions/ContractDescriptions.vue';
import Disclaimer from './Disclaimer.vue';
import ChoseBox from '@/components/choseBox/';
import Bus from 'components/bus/bus.js';
import { resendTemplateMinxin } from 'src/mixins/resendTemplate.js';
import { signerInfoAssociateMixin } from 'src/mixins/signerInfoAssociate.js';

import { defaultRecipient, defaultPlaceHolder } from './static.js';
import HybridAlpha from 'utils/hybrid/alpha/index.js';
const hybridAlpha = new HybridAlpha();
import { businessLocalField } from 'utils/hybrid/hybridBusiness.js';
import { advancedFeatureMixin } from 'src/mixins/advancedFeature.js';

export default {
    components: {
        Draggable,
        ExpandItemList,
        ContractDescriptions,
        Disclaimer,
        ChoseBox,
    },
    inject: ['getDefaultConfig'],
    mixins: [resendTemplateMinxin, signerInfoAssociateMixin, advancedFeatureMixin],
    // eslint-disable-next-line vue/require-prop-types
    props: ['templateStatus', 'ordered', 'isDynamicTemplate', 'noticeSpecificUser', 'notAllowNotify'],
    data() {
        return {
            contract_id: this.$route.query.templateId || '',
            operateMarkId: this.$route.query.operateMarkId || '',
            // 拖拽
            isDraged: false,

            // 发件人信息
            sender: null,
            // 卡片
            recipients: [],
            // 阻止删除卡片时触发风险提醒
            selectDisabled: true,
            // 是否顺序签署
            signOrdered: false,

            selectedIndex: 0,

            // selector视图
            participationTypeOption: [{
                value: 'SIGNATURE',
                label: '签字',
            }, {
                value: 'CC_USER',
                label: this.$t('addReceiver.cc'),
            }],
            participationTypeOptionEnt: [{
                value: 'SEAL',
                label: '盖章',
            }, {
                value: 'SEAL_AND_SIGNATURE',
                label: '盖章并签字',
            }, {
                value: 'CC_USER',
                label: this.$t('addReceiver.cc'),
            }],
            // {
            //     value: 'OPERATOR_SIGNATURE',
            //     label: '仅签字'
            // },
            realNameOption: [{
                value: true,
                label: '需要实名',
            }, {
                value: false,
                label: '不需要实名',
            }],
            realNameOptionEnt: [{
                value: true,
                label: '经办人需要实名',
            }, {
                value: false,
                label: '经办人不需要实名',
            }],
            userTypeOption: [{
                value: 'PERSON',
                label: '个人',
            }, {
                value: 'ENTERPRISE',
                label: '企业',
            }],

            // 查询账号信息
            accountInfo: {},
            enpInfo: {},
            enpList: [],
            canEnpSelectChange: false,

            // excel
            // uploadUrl: `${tempPath}/templates/${this.$route.query.templateId}/user-list-read`,
            uploadHeaders: { Authorization: `bearer ${this.$cookie.get('access_token')}` },
            excelUrl: `${tempPath}/multiple-dynamic-signer/${this.$route.query.templateId}/web/generate-excel?fileType=EXCEL&access_token=${this.$cookie.get('access_token')}`,
            importedNum: 0,
            webHandInputForm: {}, // 使用模板-签约方单个输入 字段key
            singleToggle: false, // 使用模板- 切换批量和单个
            singleList: [], // 手动单个输入数据
            descriptionsInputFields: [], // 合同描述字段
            hasDynamicTable: false, // 是否有动态表格
            hasClause: false, // 是否有条款
            needReadBeforeSign: null,

            entDefaultRecipient: {},
            personDefaultRecipient: {},
            hasPictureField: false, // 业务字段中是否有图片类型
            hasMustFillPictureField: false, //  业务字段中是否有必填的图片类型
            hasBatchUploadPic: false, // 是否已经批量上传过图片业务字段
            choseBoxDialog: { // 输入/选择档案柜弹窗
                visible: false,
                type: 0,
            },
            picUploadUrl: `/template-api/multiple-dynamic-signer/${this.$route.query.templateId}/web/upload-pictures`,
            isTrialTipShowIndex: -1, // 试用tooltip是否显示对应数据的index

        };
    },
    computed: {
        ...mapState({
            hybridServer: state => state.commonHeaderInfo.hybridServer,
            hybridVersion: state => {
                return state.hybridVersion;
            },
            extendFields: state => state.commonHeaderInfo.extendFields,
            localFieldSupportType: state => state.localFieldSupportType,
        }),
        ...mapState('template', ['multipleDynamicSigner', 'canModifyWhenUsed', 'isLimitFaceConfig']),
        ...mapState('sendingPrepare', ['receptionCollection']), // 是否开通前台代收
        ...mapGetters(['checkFeat', 'checkAdvancedFeatureData']),
        isDisDrag() {
            return {
                handle: '.recipient-handle',
                disabled: this.templateStatus === 'use' || !this.signOrdered, // 模板使用不允许拖拽
            };
        },
        recipientsLength() {
            return this.recipients.length;
        },
        uploadUrl() {
            const { templateId } = this.$route.query;
            // 以下两个接口都直接调用公有云 https://wiki.bestsign.tech/pages/viewpage.action?pageId=36418953
            // 混合云1.0低版本的jar包调用老接口
            if (this.$hybrid.isAlpha() && this.hybridServer && !this.hybridVersion) {
                return `${tempPath}/templates/${templateId}/user-list-read`;
            }
            return `${tempPath}/multiple-dynamic-signer/${templateId}/web/import-excel`;
        },
        // 可能存在多个
        placeholderUser() {
            return this.recipients.filter(rec => {
                return rec.roleType === 'PLACEHOLDER';
            });
        },
        hybridUser() {
            return !!this.hybridServer && this.$hybrid.isAlpha();
        },
        // 可变签约主体，账号占位符
        isChangeableRole() {
            return recipient => recipient.roleType === 'PLACEHOLDER' &&
                ((this.templateStatus === 'use' && !this.singleToggle) ||
                    this.templateStatus === 'edit');
        },
        // 固定签约主体 || 使用模板时，可变签约主体单个输入
        isRoleCanInput() {
            return recipient => recipient.roleType === 'SPECIFIC_USER' ||
                (recipient.roleType === 'PLACEHOLDER' && (this.templateStatus === 'use' && this.singleToggle));
        },
        // 是否选择了前台代收
        isSelectProxy() {
            return recipient => {
                // 如果是编辑模版 并且是 固定签署人 使用模版时填写
                if (!recipient.assignFullInfo && this.templateStatus === 'edit') {
                    return false;
                }
                return this.receptionCollection && recipient.userType === 'ENTERPRISE' && recipient.ifProxyClaimer && !(recipient.proxyClaimerAccounts || []).length;
            };
        },
        // 固定签署方在使用模版时 控制 是否参与改合同的签署
        isShowCanJoinInContractBtn() {
            return recipient => this.templateStatus === 'use' && this.canModifyWhenUsed && recipient.roleType !== 'PLACEHOLDER' && !this.isDynamicTemplate;
        },
        // 高级功能试用信息
        trialFeatureData() {
            return {
                attachmentRequired: this.checkAdvancedFeatureData('34'), // 添加合同附属资料
                handWritingRecognition: this.checkAdvancedFeatureData('79'), // 必须手写并开启笔迹识别
            };
        },
    },
    watch: {
        showDataBoxInviteDialog(val) {
            val && this.$emit('showDataBoxInvite');
        },
        ordered: {
            handler(v) {
                this.signOrdered = v;
            },
            immediate: true,
        },
        singleToggle: {
            handler(v) {
                typeof v === 'boolean' && Bus.$emit('toggleSignersType', v);
            },
            immediate: true,
        },
        queryContractId: {
            handler(v) {
                if (v) {
                    // 重新发起的话，默认展示单个输入
                    this.singleToggle = true;
                }
            },
            immediate: true,
        },
    },
    methods: {
        // 签约方'更多'是否展示new标志
        checkShowDropDownNew(recipient) {
            if (!recipient) {
                return false;
            }
            if (recipient.userType === 'ENTERPRISE') {
                return  this.checkTrialInfoStatus('attachmentRequired', this.trialFeatureData, 2);
            } else if (recipient.userType === 'PERSON')  {
                return Object.keys(this.trialFeatureData).some(key => this.checkTrialInfoStatus(key, this.trialFeatureData, 2));
            }
            return false;
        },
        // dropdown状态控制试用tooltip是否显示
        handleVisibleChange(e, index) {
            setTimeout(() => {
                this.$nextTick(() => {
                    this.isTrialTipShowIndex =  e ? index : -1;
                });
            }, 500);
        },
        getReadBeforeSignConfig() {
            this.$http.get('/ents/configs/read-before-sign-config')
                .then(res => {
                    this.needReadBeforeSign = res.data.value;
                });
        },

        isRoleNameNecessary(recipient) {
            return (this.templateStatus === 'edit' && this.isDynamicTemplate) ||
                (recipient.roleType === 'PLACEHOLDER' && this.placeholderUser.length > 1);
        },
        judgeErrorShow(ary, key) {
            let res = '';
            if (ary && ary.length > 0) {
                ary && ary.forEach(item => {
                    if (item.fieldName === key) {
                        res += item.errorInfo;
                    }
                });
            }
            return res;
        },

        pushErr(ary, key, errMsg) {
            Array.isArray(ary.errors) &&
                !ary.errors.some(item => item.fieldName === key) &&
                ary.errors.push({
                    fieldName: key,
                    errorInfo: errMsg,
                });
        },

        spliceErr(ary, key) {
            ary = ary || [];
            if (!ary.length) {
                return;
            }
            const i = ary.findIndex(item => item.fieldName === key);
            i > -1 && ary.splice(i, 1);
        },

        clearError(i) {
            this.recipients[i].errors = [];
        },

        /**
         * 顺序签署
         */
        // 改变顺序输入框
        changeOrderNo() {
            this.recipients.sort((a, b) => a.routeOrder > b.routeOrder);
        },
        // 拖拽卡片调换签署顺序
        onDragEnd() {
            this.$parent.resetOrder();
            this.toggleSelectDisabled();
        },

        // 确保在每次 recipients Dom 更新完毕后再触发 onChangeRequireIdentityAssurance
        toggleSelectDisabled() {
            this.$nextTick().then(() => {
                this.selectDisabled = false;
            });
        },

        /**
         * 切换填写详细信息
         */
        changeDetailedInfoSwitch(i) {
            const recipient = this.recipients[i];
            // 使用模版时填写，userAccount不需要清空 https://jira.bestsign.tech/browse/CFD-5946
            const updatedRecipient = {
                assignFullInfo: !recipient.assignFullInfo,
                // userAccount: '',
                // userId: '',
                // userName: '',
                // enterpriseId: '',
                // enterpriseName: '',
                errors: [],
                photoHref: '', // 头像清空
                notification: '',
            };
            this.$set(this.recipients, i, {
                ...recipient,
                ...updatedRecipient,
            });
        },

        // 业务角色失去焦点
        onBlurRoleName(i) {
            if (!this.isDynamicTemplate) {
                return;
            }
            const recipient = this.recipients[i];

            if (!recipient.roleName) {
                this.pushErr(recipient, 'roleName', '业务角色不能为空');
                return;
            }
            if (!resRules.roleNameStr.test(recipient.roleName)) {
                this.pushErr(recipient, 'roleName', '格式不正确，请输入中文、英文或者数字');
                return;
            }

            this.spliceErr(recipient.errors, 'roleName');
        },

        /**
         * 姓名操作
         */
        // 姓名获取焦点
        onFocusUsername(i) {
            const recipient = this.recipients[i];
            this.spliceErr(recipient.errors, 'userName');
        },
        onBlurUsername(i) {
            const recipient = this.recipients[i];
            const userName = recipient.userName;

            if (recipient.personNameNeeded && !userName) {
                this.pushErr(recipient, 'userName', '姓名不能为空');
            }
            if (userName && !resRules.longUserName.test(userName)) {
                this.pushErr(recipient, 'userName', '请输入正确的姓名');
            }
        },
        // 身份证号校验
        onFocusIdNumber(i) {
            const recipient = this.recipients[i];
            this.spliceErr(recipient.errors, 'idNumber');
        },
        onBlurIdNumber(i) {
            const recipient = this.recipients[i];
            const idNumber = recipient.idNumber;

            if (recipient.personIdNumberNeeded && !idNumber && !recipient.passIdNumber) {
                this.pushErr(recipient, 'idNumber', '身份证号不能为空');
            } else if (idNumber && !resRules.IDCardReg.test(idNumber)) {
                this.pushErr(recipient, 'idNumber', '请输入正确的身份证号');
            } else {
                this.spliceErr(recipient.errors, 'idNumber');
            }
        },
        /**
         * 点击添加按钮添加签约方
         */
        addRecipient(RecipientsObj) {
            const signType = RecipientsObj.userType === 'PERSON' ? 'SIGNATURE' : 'SEAL';
            const newRecipient = {
                ...JSON.parse(JSON.stringify(defaultRecipient)),
                ...RecipientsObj.userType === 'PERSON' ? this.personDefaultRecipient : this.entDefaultRecipient,
                ...RecipientsObj,
                signType: signType,
                participationType: signType,
            };
            this.recipients.push(newRecipient);
        },
        // 增加企业
        clickAddEnterpriseRecipientBtn(i) {
            this.addRecipient({
                userType: 'ENTERPRISE',
                routeOrder: i,
            });
        },
        // 增加个人
        clickAddPersonalRecipientBtn(i) {
            this.addRecipient({
                userType: 'PERSON',
                routeOrder: i,
            });
        },

        /**
         * Excel操作
         */
        // 切换用户类型
        changeUserType(i) {
            console.log('changeUserType');
            const recipient = this.recipients[i];
            if (recipient.userType === 'ENTERPRISE') {
                recipient.requireIdentityAssurance = true;
            } else {
                recipient.requireEnterIdentityAssurance = false;
            }
        },
        async beforeUpload(file) {
            const passRes = await this.$hybrid.offlineTip();

            if (!passRes) {
                return Promise.reject(new Error());
            }

            this.importedNum = 0;
            const isPassLimit = checkExcelUploadLimit(file);

            return isPassLimit;
        },
        uploadRequest(opts) {
            const headers = this.uploadHeaders;

            const formData = new FormData();
            // 混合云3.0不带附加参数
            if (!this.$hybrid.isGammaLocalField(this.localFieldSupportType)) {
                for (const k in opts.data) {
                    const val = opts.data[k];
                    formData.append(k, val);
                }
            }
            formData.append('file', opts.file);
            const loadInstance = this.$loading();
            // 是否是混合云3.0版本，并且支持本地存储
            if (this.$hybrid.isGammaLocalField(this.localFieldSupportType)) {
                this.uploadRequestGamma(opts, formData);
            } else {
                this.$http.post(this.uploadUrl, formData, { headers, noToast: 1 })
                    .then(res => {
                        opts.onSuccess(res.data);
                    })
                    .catch(err => {
                        opts.onError(err);
                    }).finally(() => {
                        loadInstance.close();
                    });
            }
        },
        // 混合云3.0文件上传
        async uploadRequestGamma(opts, formData) {
            try {
                const excelResult = await businessLocalField({
                    url: '/hybrid/field/file',
                    parameterMap: {
                        target: '/templates/import-excel',
                        params: JSON.stringify({ // 所有需要传递给公有云的参数，放params里面，需要JSON.stringify
                            templateId: this.$route.query.templateId,
                        }),
                    },
                    requestData: formData,
                });
                opts.onSuccess(excelResult.data);
            } catch (err) {
                opts.onError(err);
            } finally {
                this.$loading().close();
            }
        },
        // 使用模板 导入excel 回调
        onUploadSuccess(res) {
            if (this.$hybrid.isAlpha() && this.hybridServer && !this.hybridVersion) {
                // SAAS-2941,多方签署兼容旧的war包，保证混合云平滑升级
                res = hybridAlpha.tempEXUploadSuccessHandle(res);
            }

            const { code, message, result, localFields } = res;
            if (code && code !== '140001') {
                return this.$MessageToast.error(message);
            }
            const { existError, missingFields, header, rows } = result;
            if (!existError) {
                this.$MessageToast.success('导入成功！');
                this.importedNum = rows.length;
                return true;
            }
            // 过滤错误信息
            const excelErrorData = [];
            const excelHeaderData = {
                line: {
                    行数: '行数',
                },
                singers: [],
                inputFields: [],
            };

            const excelErrorLineData = [];

            // 多方签署表格渲染
            if (header.signers) {
                // 表头
                header.signers.map(signer => {
                    const t = {
                        fields: [],
                    };
                    t.signerName = signer.signerName;
                    signer.signerFields.map((field) => {
                        t.fields.push(field);
                    });
                    excelHeaderData.singers.push(t);
                });
                header.inputFields.map(field => {
                    excelHeaderData.inputFields.push(field);
                });
                // 表格内容数据
                rows.map((row, index) => {
                    let cacheText = '';
                    excelErrorData.push({
                        行数: index + 1,
                    });
                    const { signers, inputFields } = row;
                    signers.map(signer => {
                        signer.signerFields.map(field => {
                            excelErrorData[index][`${signer.signerName}-${field.fieldName}`] = field.value;
                            // 保存errormessage
                            excelErrorData[index][`${signer.signerName}-${field.fieldName}-error`] = field.errorMessage;

                            if (field.errorMessage && typeof field.errorMessage !== 'object') {
                                cacheText += `${signer.signerName || ''}-${field.errorMessage.replace(/,/g, '，')}；`;
                            }
                        });
                    });
                    inputFields.map(field => {
                        // 混3.0 savelocation为Local时，存本地，需要对照来取值
                        excelErrorData[index][`${field.fieldName}`] = field.saveLocation !== 'LOCAL' ? field.value : field.value ? localFields.find(item => item.fieldId === field.value).value : '';
                        excelErrorData[index][`${field.fieldName}-error`] = field.errorMessage;

                        if (field.errorMessage && typeof field.errorMessage !== 'object') {
                            cacheText += `${field.errorMessage.replace(/,/g, '，')}；`;
                        }
                    });

                    if (cacheText !== '') {
                        excelErrorLineData.push({
                            lineNumber: index + 1,
                            errMsg: cacheText,
                        });
                    }
                });
            }

            this.$parent.excelHeaderData = excelHeaderData;
            this.$parent.excelRowData = rows;

            this.$parent.excelErrorLineData = excelErrorLineData;
            this.$parent.excelErrorMissingData = missingFields;
            this.$parent.excelErrorData = excelErrorData;
            this.$parent.excelErrorDialogVisible = true;
            // this.$parent.excelLineMap = excelLineMap;
        },

        onUploadError(err) {
            // 160007 模板授权被取消后，没有权限使用
            if (err.message.indexOf('160007') > -1) {
                this.$messageBox({
                    title: '无法使用该模板',
                    message: '您对该模板的使用权限被模板创建者收回',
                    confirmBtnText: '回到模板列表',
                })
                    .then(() => {
                        this.$router.push('/template/list');
                    });
                return;
            } else if (err.message.indexOf('160008') > -1) { // 160008 模板被创建人删除后，被授权人无法使用
                this.$messageBox({
                    title: '无法使用该模板',
                    message: '此模板已被创建者删除',
                    confirmBtnText: '回到模板列表',
                })
                    .then(() => {
                        this.$router.push('/template/list');
                    });
                return;
            }

            this.$alert(err.response.data.message, '导入失败', {
                confirmButtonText: '确定',
                customClass: 'excel-error-toast',
                lockScroll: false,
            });
        },

        /**
         * 删除卡片
         */
        clickDeleteRecipientBtn(index) {
            const roleId = this.recipients[index].roleId;
            new Promise((resolve) => {
                this.selectDisabled = true;

                if (!roleId) {
                    resolve();
                } else {
                    this.deleteRecipient(roleId, { noToast: 1 })
                        .then(() => {
                            resolve();
                        });
                }
            })
                .then(() => {
                    this.recipients.splice(index, 1);
                    this.toggleSelectDisabled();
                    // this.resetOrder(); // 不能重置，因为有两个接受者顺序一致的情况
                });
        },

        /**
         * 实名操作
         */
        onChangeRequireIdentityAssurance(v, i) {
            const recipient = this.recipients[i];
            // 如果企业签署人，就是切换经办人实名
            if (recipient.userType === 'ENTERPRISE') {
                recipient.requireEnterIdentityAssurance = v;
                return;
            }
            if (v === false && this.selectDisabled === false) {
                this.$messageBox({
                    iClass: 'sign-add-tip-msgbox',
                    title: '风险提示',
                    message: '如果签约方未实名签署，在文件发生纠纷时，需要您自己提供该签约方身份认定的证据。如需避免风险，请选择需要实名。',
                    confirmBtnText: '选择需要实名',
                    cancelBtnText: '选择不需要实名',
                })
                    .then(() => {
                        recipient.requireIdentityAssurance = true;
                    })
                    .catch(() => {
                        recipient.requireIdentityAssurance = false;
                        recipient.handWriteNotAllowed = false;
                        recipient.status_handWriteNotAllowed = false;
                    });
            }
        },
        // <!-- 签署／抄送 select -->
        onChangeSignType(event, index) {
            // 如果是抄送，企业经办人切换为不需要实名
            const cur = this.recipients[index];
            if (event === 'CC_USER') {
                cur.requireEnterIdentityAssurance = false;
                cur.faceFirst = false;
                cur.status_faceFirst = false;
                cur.status_mustReadBeforeSign = false;
                cur.faceVerify = false;
                cur.status_faceVerify = false;
                cur.handWriteNotAllowed = false;
                cur.status_handWriteNotAllowed = false;
                cur.forceHandWrite = false;
                cur.status_forceHandWrite = false;
                cur.handWritingRecognition = false;
                cur.status_handWritingRecognition = false;
                cur.contractDownloadControl = false;
                cur.receiverType = 'CC_USER';
                cur.signType = null;
            } else if (event === 'SEAL_AND_SIGNATURE') {
                cur.requireEnterIdentityAssurance = true;
                cur.receiverType = 'SIGNER';
                cur.signType = event;
            } else {
                cur.receiverType = 'SIGNER';
                cur.signType = event;
            }
        },
        checkIsMutex(command, selectedRecipient, mutexKeyNames) {
            // 互斥条件
            const mutex1 = (command === mutexKeyNames[0].key && selectedRecipient[mutexKeyNames[1].key]);
            const mutex2 = (command === mutexKeyNames[1].key && selectedRecipient[mutexKeyNames[0].key]);
            if (mutex1 || mutex2) {
                const msg = mutex1 ? mutexKeyNames[1].name : mutexKeyNames[0].name;
                this.$MessageToast.error(`已设置“${msg}”，请先删除“${msg}”的设置后再选择`);
                return true;
            }
            return false;
        },

        /**
         * 更多操作
         */
        // 点击更多
        async moreSelectClick(command, vm) {
            if (Object.keys(this.trialFeatureData).includes(command)) {
                // 高级功能未开启，提示
                if (!this.checkTrialInfo(this.trialFeatureData[command])) {
                    return;
                }
            }
            const selectedIndex = this.selectedIndex = vm.$attrs.index;
            const selectedRecipient = this.recipients[selectedIndex];

            if (command === 'contractDownloadControl' && !selectedRecipient[command]) {
                await this.$refs.disclaimer.confirm()
                    .then(() => {
                        this.$set(selectedRecipient, command, true);
                    })
                    .catch(e => {
                        console.log(e);
                    });
                return;
            }
            // 必须手写和不允许手写互斥 saas-5490
            if (this.checkIsMutex(command, selectedRecipient, [{
                key: 'handWriteNotAllowed',
                name: '不允许手写签名',
            }, {
                key: 'forceHandWrite',
                name: '必须手写签名',
            }])) {
                return;
            }
            if (this.checkIsMutex(command, selectedRecipient, [{
                key: 'handWriteNotAllowed',
                name: '不允许手写签名',
            }, {
                key: 'handWritingRecognition',
                name: '必须手写签名',
            }])) {
                return;
            }
            // saas-5890
            if (this.checkIsMutex(command, selectedRecipient, [{
                key: 'faceFirst',
                name: '优先刷脸，备用验证码签署',
            }, {
                key: 'faceVerify',
                name: '必须刷脸签署',
            }])) {
                return;
            }
            if (this.checkIsMutex(command, selectedRecipient, [{
                key: 'attachmentRequired',
                name: '添加合同附属资料',
            }, {
                key: 'newAttachmentRequired',
                name: '提交签约主体资料',
            }])) {
                return;
            }
            if (['faceFirst', 'faceVerify'].indexOf(command) > -1 && this.isLimitFaceConfig) {
                this.$MessageToast.error(this.$t('addReceiver.limitFaceConfigTip'));
                return;
            }
            selectedRecipient[`status_${command}`] = true;
            if (['faceFirst', 'faceVerify', 'handWriteNotAllowed', 'forceHandWrite', 'mustReadBeforeSign', 'handWritingRecognition', 'contractPayer'].indexOf(command) > -1) {
                selectedRecipient[command] = true;
            }
            // 开启手写笔迹识别，默认开启必须手写签名
            if (command === 'handWritingRecognition') {
                selectedRecipient.status_forceHandWrite = true;
                selectedRecipient.forceHandWrite = true;
            }
            if (command === 'attachmentRequired') {
                selectedRecipient[command] = true;
                selectedRecipient.attachmentList = [{ name: '', comment: '' }];
            }
            if (command === 'newAttachmentRequired') {
                this.choseBoxDialog = {
                    visible: true,
                    type: selectedRecipient.userType === 'PERSON' ? 0 : 1,
                };
            }
        },
        // 删除已添加的操作选项
        handleExpandClose(index, command) {
            this.recipients[index][`status_${command}`] = false;
            if (['faceFirst', 'faceVerify', 'handWriteNotAllowed', 'forceHandWrite', 'handWritingRecognition'].indexOf(command) > -1) {
                this.recipients[index][command] = false;
            } else if (command === 'attachmentRequired') {
                this.recipients[index][command] = false;
                this.recipients[index].attachmentList = [];
            } else if (command === 'privateLetter') {
                this.recipients[index][command] = '';
                this.recipients[index].privateLetterFileVOList = [];
                this.recipients[index].privateLetterFileList = [];
            } else if (command === 'newAttachmentRequired') {
                this.recipients[index][command] = false;
                this.recipients[index].archiveId = '';
            } else {
                this.recipients[index][command] = '';
            }
            // 手写笔迹识别依赖必须手写签名配置
            if (command === 'forceHandWrite') {
                this.recipients[index].status_handWritingRecognition = false;
                this.recipients[index].handWritingRecognition = false;
            }
        },
        // ajax
        getRoles() {
            const { templateId } = this.$route.query;
            let url = `${tempPath}/templates/${this.contract_id}/roles`;
            if (this.templateStatus === 'use') {
                if (this.$hybrid.isGammaLocalField(this.localFieldSupportType)) {
                    return businessLocalField({
                        url: '/hybrid/field',
                        requestData: {
                            target: '/templates/signer-info',
                            params: JSON.stringify({ // 所有需要传递给公有云的参数，放params里面，需要JSON.stringify
                                templateId: this.$route.query.templateId,
                            }),
                        },
                    });
                }
                url = this.handleRequestLink(`${tempPath}/multiple-dynamic-signer/${templateId}/signer-info${this.operateMarkId ? '?operateMarkId=' + this.operateMarkId : ''}`);
            }
            return this.$http.get(url);
        },
        // 模板使用列表配置信息
        handleTemplateUseRolesInfo(data) {
            if (data.code && data.code !== '140001') {
                return this.$MessageToast.error(data.message);
            }
            const { roles, importGroupCount, webHandInputForm, webReadCache, contractName, expireDays, contractTypeId, isUploadedPictures } = data.result;
            this.$emit('updateTemInfo', {
                templateName: contractName,
                expireDays: expireDays,
                contractTypeId: contractTypeId,
            });
            (webHandInputForm.inputFields || []).forEach(field => {
                if (field.saveLocation === 'LOCAL' && field.value) {
                    field.value = data.localFields.find(item => item.fieldId === field.value).value;
                }
            });
            this.webHandInputForm = webHandInputForm;
            this.singleList = webReadCache || [];
            const recipients = roles.map(item => {
                item.errors = item.errors || [];
                item.status_faceFirst = !!item.faceFirst;
                item.status_mustReadBeforeSign = !!item.mustReadBeforeSign;
                item.status_faceVerify = !!item.faceVerify;
                item.status_handWriteNotAllowed = !!item.handWriteNotAllowed;
                item.status_forceHandWrite = !!item.forceHandWrite;
                item.status_handWritingRecognition = !!item.handWritingRecognition;
                item.status_notification = !!item.notification;
                item.status_newAttachmentRequired = !!item.newAttachmentRequired;
                item.status_privateLetter = !!((
                    item.privateLetter                        ||
                    (item.privateLetterFileList && item.privateLetterFileList.length > 0)
                ));
                item.assignFullInfo = !!((item.assignFullInfo === null || item.assignFullInfo === true));
                item.attachmentList = item.attachmentList || [];
                item.contractDownloadControl = !!item.contractDownloadControl;
                item.contractPayer = item.contractPayer || false;
                // 后端数据，只为初始化展示使用
                item.ifProxyClaimer_end = item.ifProxyClaimer;
                item.singleToggle = false;
                item.userAccount = (item.proxyClaimerAccounts || []).join(';');
                item.participationType = item.receiverType === 'CC_USER' ? item.receiverType : item.signType; // 参与方式
                return item;
            });
            this.recipients = JSON.parse(JSON.stringify(recipients));
            this.importedNum = importGroupCount;
            this.hasBatchUploadPic = !!isUploadedPictures;

            // 排序：条款和动态表格排最后
            this.descriptionsInputFields = ((webHandInputForm || {}).inputFields || []).sort((next, prev) => {
                // 动态表格、条款、图片，初始化进来时，使用单个变量输入
                if (next.type === 'DYNAMIC_TABLE' || prev.type === 'DYNAMIC_TABLE') {
                    this.hasDynamicTable = true;
                    this.singleToggle = true;
                }
                if (next.type === 'TERM' || prev.type === 'TERM') {
                    this.hasClause = true;
                    this.singleToggle = true;
                }
                if (next.type === 'PICTURE') {
                    this.hasPictureField = true;
                    if (next.notNull) {
                        this.hasMustFillPictureField = true;
                    }
                }
                if (prev.type === 'PICTURE') {
                    this.hasPictureField = true;
                    if (prev.notNull) {
                        this.hasMustFillPictureField = true;
                    }
                }
                const orders = ['TERM', 'DYNAMIC_TABLE', 'PICTURE'];
                return orders.indexOf(next.type) - orders.indexOf(prev.type);
            });

            this.descriptionsInputFields.forEach(item => {
                if (item.fieldName === '合同到期日' && !item.value) {
                    // 合同到期日没有值，再去拿行业包的配置
                    this.getDefaultConfig.then(({ contractLifeEnd }) => {
                        item.value = contractLifeEnd;
                    });
                }
            });
            this.toggleSelectDisabled();

            return Promise.resolve();
        },
        getSender() {
            let url = `${tempPath}/templates/${this.contract_id}/sender`;
            url = this.handleRequestLink(url);
            return this.$http.get(url);
        },

        // 删除签约方
        deleteRecipient(roleId, opts) {
            return this.$http.delete(`${tempPath}/templates/${this.contract_id}/roles/${roleId}`, opts);
        },
        // 提交签约方数据
        postRecipients(data) {
            return this.$http.post(`${tempPath}/templates/${this.contract_id}/roles/create-and-modify`, {
                saveTemplate: this.templateStatus === 'use',
                roles: data,
            });
        },
        // 校验合同描述字段
        checkContractDescriptionsInputsFinished() {
            let defaultMsg = '合同基本信息填写有误';
            // const fieldIndex = null;
            const contractDescriptionsInputsRef = this.$refs.contractDescriptionsInputs;
            const inputFields = (contractDescriptionsInputsRef &&
                contractDescriptionsInputsRef.cacheFields &&
                contractDescriptionsInputsRef.cacheFields.inputFields) ? contractDescriptionsInputsRef.cacheFields.inputFields : [];
            const e2 = inputFields.some((el) => check(el));
            return {
                error: e2,
                msg: defaultMsg,
            };

            function check(el) {
                let error = false;
                if (el.notNull && !el.value) {
                    el.errorMessage = `${el.fieldName}不能为空`;
                    error = true;
                }
                if (el.type === 'TEXT_NUMERIC' && !resRules.numberLabelCode.test(el.value)) {
                    el.errorMessage = '请输入正确的数字';
                    error = true;
                }
                // 条款的报错处理
                if (el.type === 'TERM') {
                    if (el.errorMessage) {
                        defaultMsg = el.errorMessage;
                        error = true;
                        Bus.$emit('clause-error-check');
                    }
                }
                return error;
            }
        },
        // 添加附件
        addAnnexItem(recipientIndex) {
            this.recipients[recipientIndex].attachmentList = this.recipients[recipientIndex].attachmentList || [];
            this.recipients[recipientIndex].attachmentList.push({
                name: '',
                comment: '',
            });
        },
        // 删除附件
        removeAnnexItem(recipientIndex, annexIndex) {
            this.recipients[recipientIndex].attachmentList.splice(annexIndex, 1);
        },
        handleBatchInput() {
            if (this.hasDynamicTable || this.hasClause) {
                return;
            }
            this.singleToggle = false;
        },
        handleClearExcel(callback, isSwitchSingeInput = false) {
            this.$http.post(`/template-api/multiple-dynamic-signer/${this.$route.query.templateId}/web/excel-clear`)
                .then(async() => {
                    if (this.hasPictureField && isSwitchSingeInput) {
                        await this.$http.post(`/template-api/multiple-dynamic-signer/${this.$route.query.templateId}/web/clear-pictures`);
                        this.hasBatchUploadPic = false;
                    }
                    this.importedNum = 0;
                    if (callback && typeof callback === 'function') {
                        callback();
                    }
                });
        },
        handleSingeInput() {
            if (!this.importedNum && !this.hasBatchUploadPic) {
                return this.singleToggle = true;
            }
            this.$confirm('使用单个手动添加合同，将会清除您已批量上传的可变签署人信息。是否继续？', '提示', {
                confirmButtonText: '继续',
                cancelButtonText: '取消',
                type: 'warning',
                customClass: 'message-box-confirm-custom',
            }).then(() => {
                this.handleClearExcel(() => {
                    this.singleToggle = true;
                }, true);
            }).catch(() => {
            });
        },
        // 添加可变签约主体
        addVariableSubject(userType) {
            // if (!this.beforeAddReceiver()) {
            //     return;
            // }
            const signType = userType === 'PERSON' ? 'SIGNATURE' : 'SEAL';
            const temObj = {
                ...defaultPlaceHolder,
                ...userType === 'PERSON' ? this.personDefaultRecipient : this.entDefaultRecipient,
                routeOrder: this.recipients.length,
                signType: signType,
                participationType: signType,
                errors: [], // 错误信息数组
            };
            userType && Object.assign(temObj, {
                userType: userType,
            });
            this.recipients.push(temObj);
            this.$parent.resetOrder();
        },
        // 是否展示 必须刷脸签署
        isShowFaceVerify(recipient) {
            const isPersonCan = recipient.userType === 'PERSON' && recipient.requireIdentityAssurance;
            const isEntCan = (recipient.userType !== 'PERSON' && recipient.requireEnterIdentityAssurance); // 企业经办人刷脸打开 前提在 配置企业经办人需要实名， 企业经办人刷脸未打开 可以直接指定刷脸

            if (recipient.receiverType === 'SIGNER' &&
                this.checkFeat.faceVerifyForSign &&
                (isPersonCan || isEntCan)
            ) {
                return true;
            }
            return false;
        },
        // 固定签署方在使用模版时 参与改合同的签署 开关打开/关闭, notUsed: true 表示不加入合同中
        changeRecipientNotUsed(index) {
            const recipient = this.recipients[index];
            this.$MessageToast(recipient.notUsed ? '该签署人加入合同中' : '该签署人不加入合同中');
            this.$set(this.recipients, index, {
                ...recipient,
                notUsed: !recipient.notUsed,
            });
        },
        // 账号前台代收切换
        handleSwitchReceptionCollection(i, val) {
            const recipient = this.recipients[i];
            if (recipient.notUsed) {
                return false;
            }
            const temObj = {
                ifProxyClaimer: val,
            };
            if (!val) {
                this.spliceErr(recipient.errors, 'proxyClaimerAccounts');
            } else {
                this.spliceErr(recipient.errors, 'userAccount');
                // 前台代收不能抄送
                if (recipient.receiverType === 'CC_USER') {
                    Object.assign(temObj, {
                        receiverType: 'SIGNER',
                        participationType: 'SEAL',
                        signType: 'SEAL',
                    });
                }
                recipient.userAccount = '';
                recipient.proxyClaimerAccounts = [];
            }
            this.$set(this.recipients, i, {
                ...recipient,
                ...temObj,
            });
        },
        // 前台代收数据保存
        handleSaveReceptionCollection(accounts, index) {
            const recipient = this.recipients[index];
            this.$set(this.recipients, index, {
                ...recipient,
                proxyClaimerAccounts: accounts,
            });
            this.spliceErr(recipient.errors, 'proxyClaimerAccounts');
        },
        // 前台代收数据校验
        handleCheckReceptionCollection() {
            return true;
        },
        // 选中档案柜后显示
        choseBoxConfirm(archiveId) {
            const selectedRecipient = this.recipients[this.selectedIndex];
            selectedRecipient.newAttachmentRequired = true;
            selectedRecipient.archiveId = archiveId;
            this.choseBoxDialog.visible = false;
        },
        onPicUploadSuccess() {
            this.hasBatchUploadPic = true;
        },
        uploadPicRequest(opts) {
            const headers = this.uploadHeaders;

            const formData = new FormData();
            for (const k in opts.data) {
                const val = opts.data[k];
                formData.append(k, val);
            }
            formData.append('file', opts.file);
            const loadInstance = this.$loading();
            this.$http.post(this.picUploadUrl, formData, { headers, noToast: 1 })
                .then(res => {
                    opts.onSuccess(res.data);
                })
                .catch(err => {
                    opts.onError(err);
                }).finally(() => {
                    loadInstance.close();
                });
        },
        isAableContractPayer() {
            // 使用模版 不可设置付费方
            if (this.templateStatus === 'use') {
                return false;
            }
            return !this.recipients.filter(i => !i.notUsed && i.contractPayer).length;
        },
    },
    created() {
        // 动态模版不支持盖章并签字
        if (this.isDynamicTemplate) {
            this.participationTypeOptionEnt.splice(1, 1);
        }
        this.getDefaultConfig.then(({ entDefaultRecipient, personDefaultRecipient }) => {
            this.entDefaultRecipient = entDefaultRecipient;
            this.personDefaultRecipient = personDefaultRecipient;
        });

        this.getReadBeforeSignConfig();
        // SAAS-15287 开启 合同签署校验身份 时，不再强制默认选择「需要经办人实名」
        // 获取接受人列表
        this.getRoles()
            .then(res => {
                // 模板使用数据单独处理
                if (this.templateStatus === 'use') {
                    return this.handleTemplateUseRolesInfo(res.data);
                }
                if (res.data.length > 0) {
                    res.data.map(item => {
                        if (item.roleType === 'PLACEHOLDER') {
                            item.singleList = [];
                            // 循环 singleList 增加 expanded 属性
                            if (item.webInputRecipientResult) {
                                item.webInputRecipientResult.rows.forEach(row => {
                                    const fields = JSON.parse(JSON.stringify(item.fields));

                                    row.fields.filter(fieldVal => {
                                        fields.splice(fieldVal.templateLabelId - 1, 1, fieldVal);
                                    });

                                    item.singleList.push({
                                        expanded: true,
                                        fields: fields,
                                        lineNumber: row.lineNumber,
                                    });
                                });
                            }
                        }
                        item.errors = item.errors || [];
                        item.status_faceFirst = !!item.faceFirst;
                        item.status_mustReadBeforeSign = !!item.mustReadBeforeSign;
                        item.status_faceVerify = !!item.faceVerify;
                        item.status_handWriteNotAllowed = !!item.handWriteNotAllowed;
                        item.status_forceHandWrite = !!item.forceHandWrite;
                        item.status_handWritingRecognition = !!item.handWritingRecognition;
                        item.status_notification = !!item.notification;
                        item.status_newAttachmentRequired = !!item.newAttachmentRequired;
                        // item.status_privateLetter = item.privateLetter ? true : false;
                        item.status_privateLetter = !!((
                            item.privateLetter                                ||
                            (item.privateLetterFileList && item.privateLetterFileList.length > 0)
                        ));
                        item.assignFullInfo = !!((item.assignFullInfo === null || item.assignFullInfo === true));
                        item.attachmentList = item.attachmentList || [];
                        item.contractDownloadControl = !!item.contractDownloadControl;

                        item.singleToggle = false;
                        item.archiveId = item.archiveId ? item.archiveId : '';
                        item.userAccount = (item.proxyClaimerAccounts || []).join(';');
                        item.participationType = item.receiverType === 'CC_USER' ? item.receiverType : item.signType; // 参与方式
                        item.contractPayer = item.contractPayer || false;
                        return item;
                    });
                    this.recipients = JSON.parse(JSON.stringify(res.data));
                    this.importedNum = res.data[0].importUserCount;
                } else {
                    // this.recipients.push({...defaultPlaceHolder});
                }
                // 因为 recipients 有默认值所以会触发 onChangeRequireIdentityAssurance
                // 默认阻止，recipients DOM 更新后放开
                this.toggleSelectDisabled();
            })
            .finally(() => {
                this.$emit('loaded');
            });

        // 获取发件人信息（用于签署顺序展示）
        this.getSender()
            .then(res => {
                this.sender = res.data;
            })
            .catch(() => {
            });
    },
};
</script>
<style lang="scss">
   .dropdown-tool-tip{
        cursor: pointer;
        .popper__arrow::after {
            border-left-color: #FDF4F4 !important;
        }
        .el-icon-ssq-jingshitanhaox{
            margin-right: 5px;
        }

    }
    .dropdown-tool-tip.el-tooltip__popper.is-light {
        background: #FDF4F4;
        // border-color: transparent;
        color: #FD6666;
        font-size: 12px;
        border:0 !important ;
        z-index: 2001 !important;
    }
    // .el-icon-ssq-biaoqiannew{
    //     margin-left: 10px;
    //     color: #FF5500;
    // }
    //   .el-icon-ssq-biaoqiannew{
    //         font-size: 12px !important;
    //         margin-left: 3px !important;
    //     }

</style>
