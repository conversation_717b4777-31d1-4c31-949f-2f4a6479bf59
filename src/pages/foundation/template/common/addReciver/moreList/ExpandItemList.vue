<!-- 模板-添加签署人-更多下拉选项 -->
<template>
    <div class="morelist-container">
        <!-- 优先刷脸，备用验证码签署 -->
        <ExpandItem
            :key="`faceFirst${index}`"
            v-show="recipient.status_faceFirst"
            class="face-sign morelist"
            @clickClose="clickClose(index, 'faceFirst')"
            title="优先刷脸，备用验证码签署"
            iClass="el-icon-ssq-shenji"
        >
            <div slot="title-error" class="err" v-if="isLimitFaceConfig">
                <span>({{ $t('addReceiver.limitFaceConfigTip') }})</span>
            </div>
            <div slot="moreList-content" class="moreList-content">
                签署时系统默认采用刷脸校验，当刷脸不通过的次数达到当日上限时自动切换为验证码校验
            </div>
        </ExpandItem>
        <!-- 刷脸签署 -->
        <ExpandItem
            :key="`faceVerify${index}`"
            v-show="recipient.status_faceVerify"
            class="face-sign morelist"
            @clickClose="clickClose(index, 'faceVerify')"
            title="刷脸签署"
            iClass="el-icon-ssq-shenji"
        >
            <div slot="title-error" class="err" v-if="isLimitFaceConfig">
                <span>({{ $t('addReceiver.limitFaceConfigTip') }})</span>
            </div>
            <div slot="moreList-content" class="moreList-content">
                <i class="el-icon-ssq-tishi1"></i>
                该用户需要通过刷脸认证才能完成签署（刷脸签署暂只支持大陆居民使用）
            </div>
        </ExpandItem>
        <!-- 不允许手写签名 -->
        <ExpandItem class="manual-sign morelist"
            :key="`handWriteNotAllowed${index}`"
            v-show="recipient.status_handWriteNotAllowed"
            title="不允许手写签名"
            iClass="el-icon-ssq-edit"
            @clickClose="clickClose(index, 'handWriteNotAllowed')"
        >
            <div slot="moreList-content" class="moreList-content">
                该用户只能选择已经设置的签名或者使用默认字体签名才能完成签署
            </div>
        </ExpandItem>
        <!-- 必须手写签名 -->
        <ExpandItem class="manual-sign morelist"
            :key="`forceHandWrite${index}`"
            v-show="recipient.status_forceHandWrite"
            title="必须手写签名"
            iClass="el-icon-ssq-edit"
            @clickClose="clickClose(index, 'forceHandWrite')"
        >
            <div slot="moreList-content" class="moreList-content">
                <i class="el-icon-ssq-tishi1"></i>
                <span v-if="recipient.status_handWritingRecognition">
                    该用户需要在签署时手写清晰可辨的姓名才能完成签署
                </span>
                <template v-else>
                    该用户需要手写签名才能完成签署
                </template>
            </div>
        </ExpandItem>
        <!-- 填写通知手机 -->
        <!-- 逻辑移到 ReceiverList.vue SAAS-5470 -->
        <!-- <ExpandItem class="notification morelist"
            :key="`notification${index}`"
            v-show="recipient.status_notification"
            title="填写通知手机"
            iClass="el-icon-ssq-shouji"
            @clickClose="clickClose(index, 'notification')">
            <div slot="moreList-content" class="moreList-content">
                <input class="input-notification"
                    type="text"
                    placeholder="请填写通知手机"
                    v-model.trim="recipient.notification">
                <div class="notification-err"
                    v-show="recipient.errors && recipient.errors.some( item => item.fieldName == 'notification' )"
                    v-html="joinErr(recipient.errors, 'notification')">
                </div>
            </div>
        </ExpandItem> -->
        <!-- 添加私信 -->
        <ExpandItem
            :key="`privateLetter${index}`"
            class="morelist"
            title="添加签约须知<span class='description'>（限255字）</span>"
            iClass="el-icon-ssq-duanxin"
            v-if="recipient.status_privateLetter"
            @clickClose="clickClose(index, 'privateLetter')"
        >
            <div slot="moreList-content" class="moreList-content textarea-con" style="padding-bottom:10px;">
                <!-- <el-input -->
                <!--     type="textarea" -->
                <!--     :rows="4" -->
                <!--     :maxlength="300" -->
                <!--     v-model.trim="recipient.privateLetter"> -->
                <!-- </el-input> -->
                <!-- <div class="privateLetter-footer">1300</div> -->
                <PrivateMessage
                    :maxContentLength="255"
                    :privateLetter="recipient.privateLetter"
                    :privateLetterFileList="recipient.privateLetterFileVOList || []"
                    @change="updatePrivateMessage(...arguments, recipient)"
                />
            </div>
        </ExpandItem>
        <!-- 查看文件前验证身份 -->
        <ExpandItem
            :key="`messageVerifyCode${index}`"
            v-show="recipient.status_messageVerifyCode"
            class="check-identity morelist"
            @clickClose="clickClose(index, 'messageVerifyCode')"
            title="查看文件前验证身份"
            iClass="el-icon-ssq-shenji"
        >
            <div slot="moreList-content" class="moreList-content">
                验证身份: <input type="text" v-model.trim="recipient.messageVerifyCode" placeholder="最多20字">
                <i class="el-icon-ssq-warm-filling"></i>
                <span>您须将此验证信息提供给该用户</span>
            </div>
        </ExpandItem>
        <!-- 发送给第三方平台 -->
        <ExpandItem
            :key="`thirdPartyPlatformId${index}`"
            v-show="recipient.status_thirdPartyPlatformId"
            class="third-party morelist"
            @clickClose="clickClose(index, 'thirdPartyPlatformId')"
            title="发送给第三方平台"
            iClass="el-icon-ssq-shenji"
        >
            <div slot="moreList-content" class="moreList-content">
                平台名称: <input type="text" v-model.trim="recipient.thirdPartyPlatformId" placeholder="请输入第三方平台名称">
            </div>
        </ExpandItem>

        <ExpandItem
            :key="`annex${index}`"
            v-show="recipient.attachmentList.length"
            class="annex-list morelist"
            @clickClose="clickClose(index, 'attachmentRequired')"
            title="合同附属资料"
            iClass="el-icon-ssq-fujian"
        >
            <div slot="moreList-content" class="moreList-content">
                <!--附件:-->
                <template v-for="(attachment, attachmentIndex) in recipient.attachmentList">
                    <div :key="`annexItem${attachmentIndex}`" class="annex-item">
                        <div class="morelist-annex-list-item morelist-annex-list-item-name">
                            <div class="morelist-annex-list-item-top">
                                <span class="red">*</span>
                                <label>资料名称{{ attachmentIndex + 1 }}:</label>
                            </div>
                            <div class="morelist-annex-list-item-bottom">
                                <el-input v-model="attachment.name"
                                    class="input-name"
                                    @change="handleAttachmentName($event, attachmentIndex, recipient.attachmentList)"
                                    placeholder="例：身份证照片"
                                    :disabled="!canModifyWhenUsed"
                                    :maxlength="10"
                                >
                                </el-input>
                            </div>
                        </div>
                        <div class="morelist-annex-list-item morelist-annex-list-item-desc">
                            <div class="morelist-annex-list-item-top">
                                <label>备注:</label>
                            </div>
                            <div class="morelist-annex-list-item-bottom">
                                <el-input v-model="attachment.comment"
                                    class="input-desc"
                                    placeholder="例：请上传本人的身份证照片（选填）"
                                    :disabled="!canModifyWhenUsed"
                                    :maxlength="30"
                                >
                                </el-input>
                            </div>
                        </div>
                        <i class="el-icon-ssq-delete"
                            v-if="recipient.attachmentList.length !== 1"
                            @click="removeAnnexItem(index, attachmentIndex)"
                        >
                        </i>
                    </div>
                </template>
                <span class="add-btn" @click="addAnnexItem(index)" v-if="canModifyWhenUsed">
                    <i class="el-icon-ssq-jia"></i>
                    <span>增加资料</span>
                </span>
            </div>
        </ExpandItem>
        <ExpandItem
            v-if="recipient.status_newAttachmentRequired && recipient.archiveId"
            :key="`annex${index}`"
            :startShow="false"
            class="annex-list morelist"
            @clickClose="clickClose(index, 'newAttachmentRequired')"
            title="签约主体资料"
            iClass="el-icon-ssq-fujian"
        >
            <div slot="moreList-content" class="moreList-content">
                <BoxContent :archiveId="recipient.archiveId"></BoxContent>
            </div>
        </ExpandItem>

        <ExpandItem class="manual-sign morelist"
            :key="`contractDownloadControl${index}`"
            v-show="recipient.contractDownloadControl"
            title="启用下载码"
            iClass="el-icon-ssq-edit"
            @clickClose="clickClose(index, 'contractDownloadControl')"
        >
            <div slot="moreList-content" class="moreList-content">
                签约方在下载合同时，需填写下载码，发件方可在合同详情页中查看下载码。
            </div>
        </ExpandItem>
        <ExpandItem
            :key="`contractPayer${index}`"
            :canClose="isEdit"
            v-show="recipient.contractPayer"
            class="face-sign morelist "
            @clickClose="clickClose(index, 'contractPayer')"
            title="付费方"
        >
            <div slot="moreList-content" class="moreList-content">本合同由该签署方付费</div>
        </ExpandItem>
        <!-- 阅读完毕再签署 -->
        <ExpandItem
            :key="`mustReadBeforeSign${index}`"
            v-show="recipient.status_mustReadBeforeSign"
            class="manual-sign morelist"
            @clickClose="clickClose(index, 'mustReadBeforeSign')"
            title="阅读完毕再签署"
            iClass="el-icon-ssq-edit"
        >
            <div slot="moreList-content" class="moreList-content">
                <span>签署人必须阅读，并且知晓合同内容才可进行后续操作。</span>
            </div>
        </ExpandItem>
        <!-- 手写笔迹识别 -->
        <ExpandItem
            class="manual-sign morelist"
            :key="`handWritingRecognition${index}`"
            v-show="recipient.status_handWritingRecognition"
            title="开启手写笔迹识别"
            iClass="el-icon-ssq-edit"
            @clickClose="clickClose(index, 'handWritingRecognition')"
        >
            <div slot="moreList-content" class="moreList-content">
                <span>
                    该用户手写的姓名将与发件人指定的或实名信息中的姓名进行比对，比对一致才可完成签署
                </span>
            </div>
        </ExpandItem>
        <!-- 盖章并签字 -->
        <ExpandItem
            class="manual-sign morelist"
            :key="`signType${index}`"
            v-show="recipient.signType ==='SEAL_AND_SIGNATURE'"
            title="盖章并签字"
            iClass="el-icon-ssq-edit"
            :canClose="false"
        >
            <div slot="moreList-content" class="moreList-content">
                <i class="el-icon-ssq-tishi1"></i>
                <span>
                    使用企业印章签署时，需同时添加个人签名完成签署。签名前需完成个人实名认证
                </span>
            </div>
        </ExpandItem>
    </div>
</template>

<script>
import ExpandItem from './ExpandItem.vue';
import PrivateMessage from 'src/pages/foundation/sign/field/approvalApp/PrivateMessage.vue';
import { mapState, mapGetters } from 'vuex';
import BoxContent from '@/components/boxContent/';
export default {
    components: {
        ExpandItem,
        PrivateMessage,
        BoxContent,
    },
    // eslint-disable-next-line vue/require-prop-types
    props: ['index', 'recipient', 'isEdit'],
    data() {
        return {

        };
    },
    computed: {
        ...mapState('template', ['canModifyWhenUsed', 'isLimitFaceConfig']),
        ...mapGetters(['checkFeat']),
    },
    methods: {
        // 删除当前选项
        clickClose(index, command) {
            this.$emit('close', index, command);
        },
        // 添加附件
        addAnnexItem(recipientIndex) {
            this.$emit('add-annex-item', recipientIndex);
        },
        // 删除附件
        removeAnnexItem(recipientIndex, annexIndex) {
            this.$emit('remove-annex-item', recipientIndex, annexIndex);
        },
        updatePrivateMessage(data, recipient) {
            if (data.privateLetter === '' || data.privateLetter) {
                recipient.privateLetter = data.privateLetter;
            }
            if (data.privateLetterFileList) {
                recipient.privateLetterFileVOList =
                    data.privateLetterFileList;

                recipient.privateLetterFileList = data.privateLetterFileList.map(i => i.fileId);
            }
        },
        // 处理附件不能重名
        // eslint-disable-next-line no-unused-vars
        handleAttachmentName($event, attachmentIndex, attachmentList) {
            if ($event) {
                attachmentList = attachmentList.map((l, i) => {
                    if ($event && l.name === $event && i !== attachmentIndex) {
                        l.name = '';
                        this.$MessageToast.error('合同附属资料名称不能一致');
                    }
                    return l;
                });
            }
        },
    },
};
</script>
