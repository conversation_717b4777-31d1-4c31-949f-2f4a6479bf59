<!-- 模板-添加签署人-单个下拉选项内容 -->
<template>
    <transition>
        <div class="addrecipient-more-list-tem">
            <div class="head">
                <h1 class="title fl" v-html="title">
                    <!-- {{ title }} -->
                </h1>
                <slot name="title-error"></slot>
                <div class="fr">
                    <span class="retract" @click="retract">{{ isConShow ? '收起' : '展开' }}</span>
                    <span class="close" @click="close" v-if="canModifyWhenUsed && canClose">删除</span>
                </div>
            </div>
            <slot name="moreList-content" v-if="isConShow"></slot>
        </div>
    </transition>
</template>
<script>
import { mapState } from 'vuex';
export default {
    props: {
        'title': {
            type: String,
            default: '',
        },
        'iClass': {
            type: String,
            default: '',
        },
        'startShow': {
            type: Boolean,
            default: true,
        },
        'canClose': {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            isConShow: this.startShow,
        };
    },
    computed: {
        ...mapState('template', ['canModifyWhenUsed']),
    },
    methods: {
        // 收起、展开下拉选项
        retract() {
            this.isConShow = !this.isConShow;
        },
        // 删除当前选项
        close() {
            this.$emit('clickClose');
        },
    },
};
</script>
<style lang="scss">

    .addrecipient-more-list-tem {
        background-color: #f8f8f8;
        border-top: 1px solid #EBEBEB;
        margin-left: 0;
        // 头
        .head {
            margin-top: 8px;
            height: 27px;
            font-size: 12px;
        }
        .title {
            color: #333;
            .description {
                color: #999;
            }
        }
         .err{
                color: #FF5500;
            }
        .retract, .close {
            color: $base-color;
            cursor: pointer;
        }
        .retract {
            margin-right: 10px;
        }
        .close {
            margin-right: 15px;
        }
    }
</style>
