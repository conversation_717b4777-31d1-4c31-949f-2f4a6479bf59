<template>
    <g :type="marquee.type">
        <rect
            v-if="isResizing"
            :x="position.x - 30"
            :y="position.y - 30"
            :width="marquee.width+500"
            :height="marquee.height+500"
            fill="transparent"
            :stroke="true?'transparent':'#127fd2'"
            stroke-width="2"
            rx="2"
            ry="2"
            style="cursor: nwse-resize;"
            @mousemove="move"
        />
        <circle
            r="3.5"
            :cx="position.x"
            :cy="position.y"
            fill="#fff"
            stroke="#127fd2"
            stroke-width="2"
        />
        <circle
            r="3.5"
            :cx="position.x + orgWidth + disX"
            :cy="position.y + orgHeight + disY"
            fill="#fff"
            stroke="#127fd2"
            stroke-width="2"
            style="cursor: nwse-resize;"
            @mousedown="start"
            @mouseup="end"
        />
        <circle
            r="3.5"
            :cx="position.x"
            :cy="position.y + orgHeight + disY"
            fill="#fff"
            stroke="#127fd2"
            stroke-width="2"
        />
    </g>
</template>
<script>
export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['mark', 'initialPosition'],
    data() {
        return {
            isResizing: 0,
            startX: 0,
            startY: 0,
            disX: 0,
            disY: 0,

            orgWidth: this.mark.width,
            orgHeight: this.mark.height,
            marquee: this.mark,
        };
    },
    computed: {
        position() {
            return this.initialPosition;
        },
    },
    methods: {
        start(e) {
            this.isResizing = 1;
            this.startX = e.x;
            this.startY = e.y;
        },
        move(e) {
            if (this.isResizing) {
                this.disX = e.x - this.startX;
                this.disY = e.y - this.startY;
                // 边界控制
                if (this.orgWidth + this.disX <= 35) {
                    this.disX = 35 - this.orgWidth;
                }
                if (this.orgHeight + this.disY <= 20) {
                    this.disY = 20 - this.orgHeight;
                }
                this.marquee.width = this.orgWidth + this.disX <= 35 ? 35 : this.orgWidth + this.disX;
                this.marquee.height = this.orgHeight + this.disY <= 20 ? 20 : this.orgHeight + this.disY;
            }
        },
        end() {
            this.isResizing = 0;
            this.disX = 0;
            this.disY = 0;
            this.$emit('end', this.marquee.width, this.marquee.height);
        },
    },
    updated() {
        if (!this.isResizing) {
            this.marquee = this.mark;
            this.orgWidth = this.mark.width;
            this.orgHeight = this.mark.height;
            this.disX = 0;
            this.disY = 0;
        }
    },
};
</script>
<style lang="scss">

</style>
