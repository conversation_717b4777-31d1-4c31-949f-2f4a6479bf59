<template>
    <g class="TempSignature-cop"
        :class="{ 'qrcode': type=='QR_CODE' }"
        :type="type"
        :transform="translateMatrix(1,0,0,1, x, y)"
    >

        <!-- 头部 -->
        <g class="owner-title"
            v-if="type == 'SEAL'
                || type == 'SIGNATURE'"
        >
            <rect
                x="0"
                y="-26"
                :width="width"
                height="24"
                fill="#fff7b6"
            />
            <text
                x="6"
                y="-10"
                fill="#333"
                font-size="10"
                text-anchor="start"
            >
                {{ owner || '' }}
            </text>
        </g>

        <!-- 背景 -->
        <rect
            :width="width"
            :height="height"
            :rx="reactStyle.rx"
            :ry="reactStyle.rx"
            :fill="reactFill"
            fill-opacity="0.75"
            :stroke="reactStyle.stroke"
            :stroke-width="reactStyle.strokeWidth"
            :style="isDraging ? 'pointer-events: none;' : ''"
            @mousedown="handleMousedown"
            @contextmenu.prevent="openContextmenu"
        >
            <title v-if="type == 'QR_CODE'">签署后扫码，即可查看签署详情、验证签名有效性及该合同是否被篡改</title>
        </rect>

        <!-- 日期 -->
        <text
            v-if="type=='DATE'"
            x="34"
            y="25"
            fill="#333"
            style="pointer-events: none;"
            font-size="18"
        >签署日期</text>

        <!-- 印章 -->
        <template v-else-if="type=='SEAL'">
            <circle cx="140"
                cy="106"
                r="84"
                fill="#fff"
                opacity=".3"
                style="pointer-events: none;"
            />
            <svg class="icon" aria-hidden="true">
                <use style="pointer-events: none;"
                    x="60"
                    y="54"
                    width="160"
                    height="100"
                    :xlink:href="`#${style.type}`"
                ></use>
            </svg>
        </template>

        <!-- 签名 -->
        <template v-else-if="type=='SIGNATURE'">
            <svg class="icon" aria-hidden="true">
                <use style="pointer-events: none;"
                    x="0"
                    :y="-6"
                    width="134"
                    height="86"
                    :xlink:href="`#${style.type}`"
                ></use>
            </svg>
        </template>

        <!-- 文本 -->
        <template v-else-if="type=='TEXT'">
            <template v-if="value">
                <text
                    v-for="(item, index) in value"
                    :key="index"
                    x="0"
                    :y="textPaddingTop + (textFontSize + textSpace) * index"
                    fill="#333"
                    style="pointer-events: none;"
                    font-size="14"
                    font-weight="bold"
                >
                    {{ item }}
                </text>
            </template>
            <template v-else>
                <text
                    x="4"
                    y="15"
                    fill="#333"
                    style="pointer-events: none;"
                    font-size="14"
                >
                    {{ name }}
                </text>
            </template>

        </template>

        <!-- 二维码 -->
        <template
            v-else-if="type=='QR_CODE'"
        >
            <image style="pointer-events: none;"
                :xlink:href="`${imageHref}?access_token=${$cookie.get('access_token')}`"
                :width="width"
                :height="height"
            >
            </image>
        </template>

        <!-- 删除按钮 -->
        <g class="del-btn"
            v-if="type!='QR_CODE' && templateStatus == 'edit'"
            @click="handleClickdelBtn"
        >
            <circle :cx="width" cy="0" r="11" fill="#333" opacity=".75" />
            <use style="pointer-events: none;"
                :x="width - 5"
                :y="-5"
                width="10"
                height="10"
                fill="#fff"
                xlink:href="#el-icon-ssq-guanbi"
            ></use>
        </g>
    </g>
</template>
<script>
export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['owner', 'isFocus', 'isDraging', 'type', 'x', 'y', 'width', 'height', 'name', 'value', 'fill', 'imageHref'],
    data() {
        return {
            templateStatus: this.$route.params.templateStatus,
            textPaddingTop: 12,
            textSpace: 0,
            textFontSize: 14,

            reactStyle: {
                stroke: this.focusing ? '#127fd2' : 'transparent',
                strokeWidth: '2',
                rx: '2',
            },
        };
    },
    computed: {
        style() {
            let style = {};
            switch (this.type) {
                case 'SEAL':
                    style =  {
                        type: 'el-icon-ssq-gaizhang1',
                        // rectWidth: 320,
                        // rectHeight: 280,
                        iconWidth: 200,
                        iconHeight: 130,
                    };
                    break;
                case 'SIGNATURE':
                    style = {
                        type: 'el-icon-ssq-qianzi',
                        // rectWidth: 70,
                        // rectHeight: 46,
                        iconWidth: 100,
                        iconHeight: 70,
                    };
                    break;
                case 'DATE':
                    style = {
                        // rectWidth: 43,
                        // rectHeight: 15,
                        // iconWidth: 42,
                        // iconHeight: 14,
                    };
                    break;
                case 'TEXT':
                    style = {
                        // rectWidth: 43,
                        // rectHeight: 15,
                        // iconWidth: 42,
                        // iconHeight: 14,
                    };
                    break;
            }
            return style;
        },
        reactFill() {
            if (this.templateStatus === 'use' && this.type === 'TEXT') {
                return 'transparent';
            }
            return this.fill;
        },
        reactStroke() {
            // 二维码，有边框#ddd
            if (this.type === 'QR_CODE') {
                return '#ddd';
                // 非二维码，没有边框
            } else {
                return this.isFocus ? '#127fd2' : 'transparent';
            }
        },
    },
    methods: {
        translateMatrix(a, b, c, d, e, f) {
            return `matrix(${a},${b},${c},${d},${e},${f})`;
        },
        handleClickdelBtn() {
            this.$emit('delete-signature');
        },
        handleMousedown(e) {
            if (e.button === 0) {
                this.$emit('click-signature', e);
            }
        },
        openContextmenu(e) {
            if (this.type === 'QR_CODE') {
                return;
            }
            this.$emit('openContextmenu', e);
        },
    },
};
</script>
<style lang="scss">
	.TempSignature-cop {
		cursor: move;
		&.qrcode {
			cursor: pointer;
		}
		.owner-title {
			cursor: default;
		}
		.del-btn {
			cursor: pointer;
		}
	}
</style>
