<!-- 业务组件：外部联系人 -->
<template>
    <div class="Temp-OuterContract-comp">

        <!-- 分组左边栏 -->
        <div class="OuterContract-content OuterContract-left fl" @keyup.enter.prevent.stop="handleSearchMember">
            <el-input placeholder="搜索" size="small" class="ssq-search department-search" v-model="memberAccount" @blur="handleSearchMember">
                <el-button
                    slot="prepend"
                    icon="search"
                    size="small"
                    class="department-search-btn"
                    id="department-search-btn"
                    @click="handleSearchMember"
                ></el-button>
            </el-input>

            <el-tree
                :data="groups"
                :props="defaultProps"
                :check-strictly="selectAble"
                @node-click="handleNodeClick"
                :highlight-current="true"
                class="ssq-tree"
                :show-checkbox="selectAble"
                ref="companyDeptTree"
            >
            </el-tree>
        </div>

        <!-- 组员右边栏 -->
        <div class="OuterContract-content OuterContract-right fl" :class="{ 'radio': selectType === 'radio' }" v-loading="memberLoading">
            <!-- 单选成员 -->
            <div v-if="selectType === 'radio'">
                <el-radio-group
                    v-model="selectedMember"
                    @change="handleSelectChange"
                >
                    <p v-for="(member, index) in members" :key="index">
                        <i class="el-icon-ssq-user-filling"></i>
                        <el-radio :label="JSON.stringify(member)">{{ member.contactAccount }} {{ member.contactName }} {{ member.entName || '' }}</el-radio>
                    </p>
                </el-radio-group>
            </div>
            <!-- 多选成员 -->
            <div v-else-if="selectType === 'checkBox'">
                <el-checkbox-group
                    v-model="selectedMembers"
                    @change="handleSelectChange"
                >
                    <p v-for="(member, index) in members" :key="index">
                        <el-checkbox :label="member" :key="member.contactAccount">{{ member.contactAccount }} {{ member.contactName }} {{ member.entName || '' }}</el-checkbox>
                    </p>
                </el-checkbox-group>
            </div>

        </div>

        <!-- 已选成员列表 -->
        <div class="member-block selected-block fl" v-if="selectType === 'checkBox'">
            <div class="cloumn selected-cloumn">
                <p>{{ $t('CSCommon.choosedMember') }}：</p>
                <div>
                    <p v-for="(member, index) in selectedMembers" :key="index">
                        <span>{{ member.contactName }}</span>
                        <i class="el-icon-ssq-delete" @click="deleteSelectedMember(index)"></i>
                        <i class="clear"></i>
                    </p>
                </div>
            </div>
        </div>

    </div>
</template>
<script>
export default {
    components: {
    },
    // eslint-disable-next-line vue/require-prop-types
    props: ['selectType'],
    data() {
        return {
            selectAble: false,
            groups: [],
            members: [],
            defaultProps: {
                children: 'children',
                label: 'groupName',
            },
            selectedMember: '',
            selectedMembers: [],
            memberAccount: '',
            memberLoading: false,
        };
    },
    methods: {

        // 格式化groups
        formatMembers(data) {
            const groups = [{
                groupName: '全部',
                groupId: 0,
                sharedGroup: true,
            }];
            data.forEach(item => {
                groups.push(item);
            });
            return groups;
        },

        // 点击组
        handleNodeClick(data) {
            // 避免重复调用
            if (this.memberLoading) {
                return;
            }
            this.memberLoading = true;
            this.getMembers(data.groupId, data.sharedGroup)
                .then(res => {
                    this.members = res.data;
                }).finally(() => {
                    this.memberLoading = false;
                });
        },

        // 点击组员
        handleSelectChange(value) {
            if (this.selectType === 'radio') {
                const data = JSON.parse(value);
                this.selectedMembers = [{
                    account: data.contactAccount,
                    contactName: data.contactName,
                    entId: data.entName ? data.entId : '0',
                }];
            } else if (this.selectType === 'checkBox') {
                this.selectedMembers = value;
            }
            this.$emit('choose', this.selectedMembers, {
                type: 'outerContract',
            });
        },

        // 取消选择已选成员
        deleteSelectedMember(index) {
            this.selectedMembers.splice(index, 1);
            this.$emit('choose', this.selectedMembers, {
                type: 'outerContract',
            });
        },

        // 搜索成员，并将数据返回给父组件
        handleSearchMember() {
            if (!this.memberAccount.replace(/\s+/g, '').length) {
                this.getMembers('0', true)
                    .then(res => {
                        this.members = res.data;
                    });
            } else {
                this.$http.get(`/ents/contacts/out-search?keyWord=${this.memberAccount}`)
                    .then(res => {
                        this.members = res.data;
                    });
            }
            return false;
        },

        // ajax
        getGroups() {
            return this.$http.get(`/ents/contacts/out-groups`);
        },
        getMembers(groupId, sharedGroup) {
            return this.$http.get(`/ents/contacts/out-groups/${groupId}?isSharedGroup=${sharedGroup}`);
        },
    },
    created() {
        Promise.all([this.getGroups(), this.getMembers('0', true)])
            .then(res => {
                if (!res[1].data.length) {
                    return;
                }
                this.groups = this.formatMembers(res[0].data);
                this.members = res[1].data;
            });
    },
};
</script>
<style lang="scss">
	.Temp-OuterContract-comp {
		.OuterContract-content {
			display: inline-block;
			height: 308px;
			overflow-y: auto;
		}
		.OuterContract-left:first-child {
			width: 226px;
			padding: 22px 19px 0 16px;
			border-right: 1px solid $border-color;
		}

		.OuterContract-right {
			// width: 186px;
			padding: 22px 19px 0 19px;
			p{
				position: relative;
				height: 40px;
				margin-bottom: 5px;
				line-height: 40px;

				.el-checkbox {
					display: block;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}

				.el-icon-ssq-user-filling{
					margin-left: 20px;
					margin-right: 5px;
					font-size: 30px;
					line-height: 34px;
					background: #ccc;
					color: #e9e9e9;
					vertical-align: middle;
				}

				.el-radio{
					width: 340px;
					vertical-align: middle;
					// overflow: hidden;
					// white-space: nowrap;
					// text-overflow: ellipsis;

					.el-radio__input{
						position: absolute;
						left: -60px;
						top: 10px;
					}
				}

			}
		}

		.member-block{
			display: inline-block;
			float: left;

			.cloumn{
				// margin-top: 15px;
				height: 329px;
				border-left: 1px solid #eee;
				background: #fff;
			}
		}

		.selected-block{
			width: 190px;
			// margin-top: -36px;

			.selected-cloumn{
				overflow-y: auto;

				p{
					padding: 10px 10px 0;
					line-height: 30px;

					.el-icon-ssq-user-filling{
						float: left;
						width: 30px;
						height: 30px;
						margin-right: 10px;
						font-size: 30px;
                        line-height: 34px;
						background: #ccc;
						color: #e9e9e9;
					}

					span{
						float: left;
						font-size: 12px;
					}

					.el-icon-ssq-delete{
						float: right;
						line-height: 30px;
						color: #999;
						cursor: pointer;
					}
				}

			}
		}
	}
</style>
