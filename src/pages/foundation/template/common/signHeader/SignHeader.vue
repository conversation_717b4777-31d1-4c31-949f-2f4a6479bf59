<template>
    <div class="tpl-header">
        <ul class="clear">
            <li class="back" @click="toBack" v-if="!backConfig || backConfig.visible"></li>
            <li class="title">{{ title }}</li>
            <slot name="otherBtn"></slot>
            <div class="right">
                <div class="video" v-if="showVideoBtn">
                    <a
                        class="operate-video"
                        href="javascript:void(0)"
                        @click="showVideo"
                    >
                        <i class="el-icon-ssq-shipinyaofang"></i>
                        <span>操作演示</span>
                    </a>
                </div><div class="next" @click="toNext">{{ rText || '下一步' }}</div>
            </div>
        </ul>
    </div>
</template>

<script>
export default {
    components: {
    },
    // eslint-disable-next-line vue/require-prop-types
    props: ['title', 'lText', 'rText', 'showVideoBtn', 'backConfig'],
    data() {
        return {

        };
    },
    methods: {
        toBack() {
            this.$emit('to-back');
        },
        toNext() {
            this.$emit('to-next');
        },
        showVideo() {
            this.$emit('showVideo');
        },
    },
};
</script>

<style lang="scss">
	.tpl-header {
		z-index: 999;
		width: 100%;
		color: #fff;
		background-color: #00263C;
		.back, .title {
			float: left;
		}
		.right {
			float: right;
			font-size: 14px;
		}
		li {
			height: 50px;
			font-size: 14px;
		}
		.back {
			position: relative;
			width: 55px;
			cursor: pointer;
			&:after {
				content: "";
				position: absolute;
				top: 18px;
				left: 24px;
				border-left: 2px solid #fff;
				border-top: 2px solid #fff;
				padding: 5px;
				display: inline-block;
				transform: rotate(-45deg);
				-webkit-transform: rotate(-45deg);
			}
			&:hover {
				background-color: #0072CF;
			}
		}
		.title {
			width: 60%;
			height: 33px;
			line-height: 33px;
			border-left: 1px solid #2B4B5F;
			padding-left: 30px;
			margin-top: 9px;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}
		.video{
			margin-top: 7px;
			height: 33px;
			line-height: 33px;
			margin-right: 15px;
			display: inline-block;
			a{
				.el-icon-ssq-shipinyaofang{
					font-size: 20px;
					position: relative;
					top: 2px;
				}
				color: #44aeff;
			}
			&:hover a{
				color: #fff;
			}
		}
		.next {
			width: 85px;
			height: 30px;
			line-height: 30px;
			text-align: center;
			font-size: 14px;
			font-weight: bold;
			background-color: #127fd2;
			margin-top: 10px;
			margin-right: 22px;
			cursor: pointer;
			border-radius: 2px;
			display: inline-block;
			&:hover {
				background-color: #1687dc;
			}
		}
	}
</style>
