<!-- 升级为业务字段弹窗 -->
<template>
    <el-dialog
        class="temp-field-update-dialog"
        title="升级为业务字段"
        :visible.sync="param.show"
        size="tiny"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
    >
        <el-transfer
            v-model="toField"
            :data="labelList"
            :titles="['自定义字段', '业务字段']"
            @change="handleTransferChange"
            :render-content="renderEditLabel"
        ></el-transfer>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="handleConfirm">确定</el-button>
            <el-button @click="handleClose">取消</el-button>
        </div>
    </el-dialog>
</template>

<script>
export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['labels', 'param'],
    data() {
        return {
            labelList: [],
            toField: [],
        };
    },
    watch: {
        labels: {
            handler(v) {
                this.renderTransfer(v);
            },
            immediate: true,
            deep: true,
        },
    },
    methods: {
        // 生成 Transfer 的数据源
        renderTransfer(labels) {
            this.labelList = [];
            const stashSet = new Set();
            labels.forEach(label => {
                this.labelList.push({
                    key: label.labelMetaId,
                    label: label.name,
                    disabled: !!stashSet.has(label.name),
                });
                stashSet.add(label.name);
            });
        },
        // 列表元素变化
        handleTransferChange() {
        },
        // 显示编辑框
        clickShowEdit(e) {
            const targetInput = document.querySelector(`.up_label_input_${e.target.getAttribute('label_key')}`);

            if (targetInput.style.display === 'block') {
                if (!targetInput.value.trim().length) {
                    this.$MessageToast.error('请输入自定义字段名称');
                    return;
                }

                targetInput.style.display = 'none';
                this.submitEdit(e.target.getAttribute('label_key'), targetInput.value);
                return;
            } else {
                // let targetWidth = document.querySelector(`#up_label_${e.target.getAttribute('label_key')}`).offsetWidth;
                targetInput.style.width = '120px';
                targetInput.style.display = 'block';
            }
        },
        // 编辑自定义字段
        submitEdit(labelId, value) {
            const editLabel = this.labels.find(item => {
                return item.labelMetaId === labelId;
            });

            this.$http.put(`${tempPath}/meta-labels/${labelId}/`, {
                fontSize: editLabel.fontSize,
                labelType: editLabel.labelDataType,
                name: value,
                necessary: editLabel.necessary,
                receiverFill: editLabel.receiverFill,
            })
                .then(() => {
                    this.$emit('edit');
                });
        },

        // 失去焦点隐藏
        editBlurHide(e) {
            const targetInput = document.querySelector(`.up_label_input_${e.target.getAttribute('label_key')}`);

            this.submitEdit(e.target.getAttribute('label_key'), targetInput.value);
            targetInput.style.display = 'none';
        },
        // 渲染穿梭框，判断重名的自定义标签可以编辑名称
        renderEditLabel(h, option) {
            const childs = [h('span', {
                attrs: {
                    'id': `up_label_${option.key}`,
                    'class': 'up_label_name',
                },
            }, `${option.label}`)];

            childs.push(...[
                h('i', {
                    attrs: {
                        'class': 'el-icon-ssq-qianmingguanli',
                        'label_key': option.key,
                    },
                    on: {
                        click: this.clickShowEdit,
                    },
                }, null),
                h('input', {
                    attrs: {
                        'class': `up_label_input up_label_input_${option.key}`,
                        'value': option.label,
                        'label_key': option.key,
                    },
                    on: {
                        blur: this.editBlurHide,
                    },
                }, null),
            ]);

            return h('span', null, childs);
        },
        // 提交升级字段
        handleConfirm() {
            if (!this.toField.length) {
                this.$MessageToast.error('请选择要升级的自定义字段');
                return;
            }

            this.$http.post(`${tempPath}/meta-labels/transfer-to-bizfield`,
                            this.toField,
            ).then(res => {
                this.$emit('edit');
                this.$emit('update');

                if (res.data.result.length) {
                    this.toField = [];
                    this.$MessageToast('与业务字段重名，请在重命名后再升级');
                } else {
                    this.$MessageToast.success('升级成功');
                    this.handleClose();
                }
            });
        },
        // 关闭弹窗
        handleClose() {
            this.$emit('close');
        },
    },
};
</script>

<style lang="scss">
    .temp-field-update-dialog{
        .el-dialog{
            width: 530px;

            .el-transfer{
                .el-transfer-panel{
                    .el-transfer-panel__body{
                        box-sizing: content-box;

                        .el-transfer-panel__item {

                            .el-checkbox__label{
                                position: relative;
                                height: 33px;
                                line-height: 33px;

                                 span{
                                    display: inline-block;
                                }

                                .up_label_name{
                                    float: left;
                                    max-width: 120px;
                                    overflow: hidden;
                                    text-overflow: ellipsis;
                                }

                                .el-icon-ssq-qianmingguanli{
                                    display: none;
                                    margin-left: 5px;
                                    color: #2298f1;
                                    cursor: pointer;
                                }

                                .up_label_input{
                                    display: none;
                                    position: absolute;
                                    left: 28px;
                                    top: 4px;
                                    width: 70px;
                                    height: 25px;
                                    padding: 0 5px;
                                    border: 1px solid #ddd !important;
                                    outline: none;
                                }
                            }

                            &:hover{
                                background: none;

                                .up_label_name{
                                    color: #127fd2;
                                }

                                .el-icon-ssq-qianmingguanli{
                                    display: inline-block;
                                }
                            }
                        }
                    }
                }

                .el-transfer__buttons{
                    .el-button{
                        margin-left: 0;
                    }
                }
            }
        }
    }
</style>
