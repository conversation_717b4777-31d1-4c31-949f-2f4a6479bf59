<template>
    <div class="cus-field-editor-cpn">
        <el-form ref="form" :model="form" @submit.native.prevent>
            <el-form-item label="名称" class="form-name">
                <el-input placeholder="必填"
                    v-model="form.name"
                    :disabled="isSelectField"
                    :minlength="1"
                    :maxlength="30"
                    @blur="onChange('name')"
                    @submit.native.prevent
                >
                </el-input>
            </el-form-item>

            <el-form-item label="格式" v-if="getItemVisible('tableCellWidthType')">
                <el-radio-group
                    v-model="currentTableCellWidthType"
                >
                    <el-radio :label="0">列表等宽</el-radio>
                    <el-radio :label="1">自适应宽度</el-radio>
                </el-radio-group>
            </el-form-item>

            <!-- DYNAMIC_TABLE、TERM固定为发件人填写 -->
            <el-form-item label="内容填写人" v-if="getItemVisible('receiverFill')">
                <el-radio-group
                    v-model="form.receiverFill"
                    @change="onChange('receiverFill')"
                >
                    <el-radio :label="false">
                        发件人
                        <el-tooltip
                            class="item"
                            effect="dark"
                            content="选择“发件人”，则此合同内容在合同发出前由发件人填写"
                            placement="top"
                        >
                            <i class="el-icon-ssq-wenhao tips"></i>
                        </el-tooltip>
                    </el-radio>
                    <el-radio :label="true" :disabled="isSelectField">
                        签署人
                        <el-tooltip
                            class="item"
                            effect="dark"
                            content="选择“签署人”，则此合同内容在签署人签署时填写"
                            placement="top"
                        >
                            <i class="el-icon-ssq-wenhao tips"></i>
                        </el-tooltip>
                    </el-radio>
                </el-radio-group>
            </el-form-item>

            <!-- 签署人多个变量时添加身份选择 -->
            <el-form-item v-if="getItemVisible('roleId')" class="cus-field-editor-identity">
                <el-radio-group v-model="form.roleId" @change="onChange('roleId')">
                    <div v-for="item in receivers"
                        :key="item.roleId"
                    >
                        <el-radio :label="item.roleId">
                            <span>{{ item.showName }}</span>
                        </el-radio>
                    </div>
                </el-radio-group>
            </el-form-item>

            <!-- 合同条款类型选择 -->
            <el-form-item label="请选择条款类别" v-if="getItemVisible('termTypeId')" class="term-select-group">
                <el-radio-group v-model="form.termTypeId" @change="onChange('termTypeId')">
                    <div v-for="term in termTypeList"
                        :key="term.termTypeId"
                    >
                        <el-radio :label="term.termTypeId">
                            <span>{{ term.termTypeName }}</span>
                        </el-radio>
                    </div>
                </el-radio-group>
            </el-form-item>
            <el-form-item class="font-style-part" label="字号" v-if="getItemVisible('fontSize')">
                <el-select popper-class="cus-field-editor-select"
                    v-model="form.fontSize"
                    placeholder="请选择字号"
                    @change="onChange('fontSize')"
                >
                    <el-option
                        v-for="(item, index) in fontSizeRange"
                        :key="index"
                        :label="item.label"
                        :value="Number(convertPtToPx(item.pt, dpi).toFixed(3))"
                    ></el-option>
                </el-select>
                <ul class="font-style">
                    <li class="bold" :class="{'active': form.ifBold, 'disabled': form.receiverFill}" @click="onChange('ifBold')">B</li>
                    <li class="italic" :class="{'active': form.ifItalic, 'disabled': form.receiverFill}" @click="onChange('ifItalic')">I</li>
                    <li class="underline" :class="{'active': form.ifUnderline}" @click="onChange('ifUnderline')">U</li>
                </ul>
            </el-form-item>

            <!-- 签署人字段设定字号、字数 -->
            <template v-if="getItemVisible('wordNum')">
                <el-form-item class="form-word-num">
                    <div class="lable">
                        内容字数
                        <el-tooltip
                            class="item"
                            effect="dark"
                            content="用于计算字段内容的预留宽度，不限制字段内容字数，默认为5，超出界面部分会被截断"
                            placement="top"
                        >
                            <i class="el-icon-ssq-bangzhu cursor-point"></i>
                        </el-tooltip>
                    </div>
                    <el-input placeholder="内容字数"
                        v-model="form.wordNum"
                        @blur="onChange('wordNum')"
                    >
                    </el-input>
                </el-form-item>
            </template>

            <el-form-item label="备选项" v-if="getItemVisible('buttons')">
                <div class="buttons">
                    <div v-for="(item,index) in form.buttons" :key="index">
                        <span class="option-checkbox"></span>
                        <el-input :maxlength="30"
                            @blur="onChange('buttons', index)"
                            :disabled="item.bookmark !== buttonBookmark"
                            type="text"
                            clearable
                            v-model.trim="item.buttonValue"
                        >
                        </el-input>
                    </div>
                </div>
            </el-form-item>

            <!-- 设置文本字段填写说明 -->
            <el-form-item label="填写说明" v-if="getItemVisible('description')">
                <el-input
                    type="textarea"
                    placeholder="选填，不超过20个字符"
                    v-model="form.description"
                    :maxlength="20"
                    @blur="onChange('description')"
                >
                </el-input>
            </el-form-item>

            <el-form-item label="填写要求" v-if="getItemVisible('necessary')">
                <el-checkbox-group
                    v-model="form.necessary"
                    @change="onChange('necessary')"
                >
                    <el-checkbox label="必填，不填不能发送或签署" name="type"></el-checkbox>
                </el-checkbox-group>
            </el-form-item>
        </el-form>
    </div>
</template>
<script>
import { convertPtToPx, fontSizeRange } from 'utils/fontSize.js';
import { SELECT_TYPES } from 'src/pages/foundation/sign/common/info/info.js';
import { mapMutations, mapState } from 'vuex';
import resRules from 'utils/regs';
import cloneDeep from 'lodash/cloneDeep';
export default {
    props: {
        editorMetaData: {
            type: Object,
            default: () => ({
                type: '',
                name: '',
                receiverFill: false,
                fontSize: 14,
                necessary: true,
                fieldType: '',
                roleId: '', // 角色Id
                wordNum: 5,
                termTypeId: '',
            }),
        },
        buttonBookmark: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            form: {
                ... this.editorMetaData,
            },
            cacheOldData: {},
            dpi: 96, // 目前写死
            convertPtToPx: convertPtToPx,
            fontSizeRange,
        };
    },
    computed: {
        ...mapState('dynamic', {
            receivers: state => state.receivers,
            termTypeList: state => state.termTypeList,
            tableCellWidthType: state => state.tableCellWidthType,
        }),
        isSelectField() {
            return SELECT_TYPES.includes(this.editorMetaData.type);
        },
        currentTableCellWidthType: {
            get() {
                return this.tableCellWidthType;
            },
            set(v) {
                this.$http.post(`/template-api/v2/dynamic-template/${this.$route.query.templateId}/format`, {
                    selfAdapting: v,
                })
                    .then(() => {
                        this.setTableCellWidthType(v);
                    });
            },
        },
    },
    watch: {
        editorMetaData: {
            handler(v) {
                this.cacheOldData = cloneDeep(v); // 缓存form数据
                this.form = cloneDeep({
                    ...v,
                    fontSize: v.fontSize || 14,
                    wordNum: v.wordNum || 5,
                });
                if (v.type === 'TERM' && !v.termTypeId) {
                    this.form.termTypeId = (this.termTypeList && this.termTypeList[0].termTypeId) || '';
                }
            },
            deep: true,
            immediate: true,
        },
    },
    methods: {
        ...mapMutations('dynamic', ['setTableCellWidthType']),
        getItemVisible(itemType) {
            switch (itemType) {
                case 'receiverFill':
                    return this.editorMetaData.type !== 'DYNAMIC_TABLE' && this.editorMetaData.type !== 'TERM';
                case 'roleId':
                    return this.form.receiverFill && this.receivers.length > 1;
                case 'termTypeId':
                    return this.editorMetaData.type === 'TERM';
                case 'fontSize':
                    return this.editorMetaData.type !== 'DYNAMIC_TABLE' && this.editorMetaData.type !== 'TERM' && !this.isSelectField;
                case 'necessary':
                    return this.editorMetaData.type !== 'DYNAMIC_TABLE' && this.editorMetaData.type !== 'TERM';
                case 'wordNum':
                    return this.editorMetaData.receiverFill;
                case 'buttons':
                    return this.isSelectField;
                case 'description':
                    return this.form.receiverFill && this.editorMetaData.type === 'TEXT';
                case 'tableCellWidthType':
                    return this.editorMetaData.type === 'DYNAMIC_TABLE';
            }
        },
        onChange(k, index) {
            if (k === 'name' && this.form[k].trim().length === 0) {
                this.$MessageToast.error('请输入名称');
                return;
            }

            if (k === 'wordNum' && this.form[k] < 1) {
                this.$MessageToast.error('请填写字段内容字数！');
                return;
            }

            if (k === 'buttons') {
                const now = this.form[k][index].buttonValue;
                const old = this.cacheOldData[k][index].buttonValue;
                const sameItems = this.form[k].filter(item => item.buttonValue === now);
                if (!now.trim()) {
                    this.$MessageToast.error('请输入备选项');
                    return;
                } else if (old === now) {
                    return;
                } else if (sameItems.length > 1) {
                    this.$MessageToast.error('备选项不能相同');
                    return;
                }
                // 备选项名字需要做格式校验，不能包括特殊字符, CFD-6264，SAAS-12570
                if (resRules.fieldValueReg.test(now)) {
                    this.$MessageToast.error('备选项名字不能包含特殊字符\\/#()');
                    return;
                }
            }

            if (['ifBold', 'ifItalic', 'ifUnderline'].includes(k)) { // 字体样式
                this.form[k] = !this.form[k];
            }

            if (this.cacheOldData[k] === this.form[k]) { // 利用缓存数据，在名称未发生变化时，直接返回，不触发数据更新
                return;
            }

            const data = {};
            data[k] = this.form[k];
            if (k === 'buttons') { // 修改备选项
                data[k][index].buttonValue = this.form[k][index].buttonValue;
            }
            this.$emit('change-value', data);
        },

        clickConfirmBtn() {
            console.log(this.form);
            this.$emit('confirm', this.form);
        },

        clickCancelBtn() {
            this.$emit('cancel');
        },
    },
};
</script>
<style lang="scss">
.cus-field-editor-cpn {
    label {
        width: 100%;
        height: 20px;
        font-size: 14px;
        color: #333;
        text-align: left;
        padding-top: 0;
        padding-bottom: 0;
    }
    .form-name .el-form-item__label::after {
        content: "*";
        color: #f86b26;
        margin-left: 4px;
    }
    .el-form-item__content .table-config-row {
        .el-col {
            padding-top: 10px;
            line-height: 26px;
            label {
                font-size: 12px;
            }
        }
    }
    .el-form-item__content .el-input {
        padding-top: 0;
        .el-input__inner {
            height: 28px;
            line-height: 28px;
            font-size: 12px;
        }
        .el-input__icon {
            color: #333;
        }
    }
    .font-style-part .el-form-item__content {
        .el-select {
            width: 80px;
        }
        .font-style {
            float: right;
            display: inline-block;
            height: 36px;
            font-size: 0;
            li {
                display: inline-block;
                text-align: center;
                cursor: pointer;
                width: 30px;
                height: 24px;
                line-height: 24px;
                margin: 6px 0;
                font-size: 16px;
                font-weight: 500;
                font-family: monospace;
                border: 1px solid #ddd;
                border-right: none;
                background: #f8f8f8;
                &.active {
                    color: #127fd2;
                    background: #fff;
                }
                &.disabled {
                    pointer-events: none;
                    cursor: not-allowed;
                    background: #f6f6f6;
                    color: #ddd;
                }
            }
            .italic {
                font-style: italic;
            }
            .underline {
                text-decoration: underline;
            }
            li:last-child {
                border-right: 1px solid #ddd;
            }
        }
    }

    .buttons {
        .option-checkbox {
            display: inline-block;
            background: #FFFFFF;
            border: 1px solid #CCCCCC;
            border-radius: 2px;
            width: 14px;
            height: 14px;
            vertical-align: middle;
        }

        .el-input {
            background: #FFFFFF;
            width: 136px;
            height: 28px;
            margin-left: 8px;
            border: 1px solid #CCCCCC;
            box-sizing: border-box;
            line-height: 100%;

            &.is-disabled {
                opacity: 0.5;
            }
            &:not(.is-disabled) {
                border: 1px solid #127fd2;
                box-shadow: 0 0 2px #47acfc;
            }
        }
    }
    .el-radio {
        width: 72px;
        .el-radio__label {
            padding-left: 2px;
        }
    }
    .tips {
        margin-left: 1px;
        font-size: 12px;
        color: #666;
        cursor: pointer;
    }
    .cus-field-editor-identity  {
        margin-top: -16px;
        .el-radio-group {
            .el-radio {
                width: 100%;
                overflow: hidden;
                height: auto;
                margin-bottom: 3px;
                .el-radio__input {
                    width: 20px;
                    float: left;
                }
                .el-radio__label {
                    display: block;
                    margin-left: 20px;
                    white-space: normal;
                    line-height: 16px;
                }
            }
        }
    }
    .term-select-group {
        .el-radio-group {
            padding-top: 3px;
            .el-radio {
                padding: 4px 0;
                .el-radio__label {
                    margin-left: 4px;
                }
            }

        }
    }
}

.cus-field-editor-select {
    li {
        height: 28px;
        line-height: 28px;
        padding: 0 10px;
    }
}
.cus-field-editor-role {
    margin-top: -16px;
}
</style>
