<template>
    <div class="common-header send-header clear">
        <div class="left y-center">
            <template v-if="true">
                <i
                    :class="leftIcon"
                    class="cur-pointer"
                    @click="$emit('Close')"
                ></i>
                <span>{{ title }}</span>
            </template>
        </div>
        <div class="right y-center">
            <el-button
                type="primary"
                @click="$emit('Save')"
            >{{ saveBtnText }}</el-button>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        leftIcon: {
            type: String,
            default: 'el-icon-close',
        },
    },
    data: function() {
        return {
            title: '合同装饰',
            saveBtnText: '保存',
        };
    },
};
</script>

<style lang="scss">
.send-header {
    height: 60px;
    color: #FFFFFF;
    font-size: 16px;
    padding: 0 30px;
    box-sizing: border-box;
    .left {
        float: left;
        i {
            display: inline-block;
            margin-right: 30px;
            font-size: 28px;
            line-height: 60px;
            &:hover {
                color: #127FD2;
            }
        }
        span {
            display: inline-block;
            padding-left: 30px;
            border-left: 1px solid #26475A;
            line-height: 60px;
            height: 60px;
            vertical-align: top;
            &.hide-border {
                border: none;
            }
        }
        .el-icon-close {
            font-size: 12px;
            font-weight: bold;
            color: #999;
		}
    }
    .right {
        float: right;
    }
    .y-center {
        line-height: 60px;
    }
}
</style>
