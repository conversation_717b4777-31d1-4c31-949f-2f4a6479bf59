<!--合同装饰页面布局-->
<template>
    <div class="page-container">
        <div class="content-container" :class="{'hd-hidden':true, 'ft-hidden': true}">
            <DocHeader @Close="closeHandle" @Save="closeHandle"></DocHeader>
            <div ref="fieldDynamicBody" class="temp-dynamic-field">
                <div class="site-content clear" ref="docContent">
                    <Navbar
                        class="FieldSite fl"
                        :receivers="receivers"
                        @switch-receiver="onSwitchReceiver"
                        v-if="templateStatus === 'edit'"
                    >
                    </Navbar>
                    <DocPreview
                        :canDrag="false"
                        :canSendlabelEdit="false"
                        :templateId="templateId"
                        :iconDragStatus="false"
                        :float="float"
                        @change-doc="changeDoc"
                        :decorateCanChange="false"
                    ></DocPreview>
                    <MiniDoc
                        :documents="docList"
                        @change-page="changePage"
                        @change-doc="changeDoc"
                    ></MiniDoc>
                </div>
            </div>
        </div>
        <RegisterFooter class="footer-follow"></RegisterFooter>
    </div>
</template>
<script>
import { mapGetters, mapMutations, mapState } from 'vuex';
import RegisterFooter from 'components/register_footer/RegisterFooter.vue';
import DocHeader from './DocHeader';
import DocPreview from './DocPreview';
import MiniDoc from './MiniDoc';
import Navbar from './NavBar';
import { scrollToYSmooth } from 'src/common/utils/dom.js';
import { initWatermark, initRidingSeal } from 'src/common/utils/decorateTool.js';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash';
import { rgbColorInfo } from 'src/common/utils/decorateConst.js';
export default {
    components: {
        RegisterFooter,
        DocHeader,
        Navbar,
        DocPreview,
        MiniDoc,
    },
    data() {
        return {
            templateId: this.$route.query.templateId,
            tempPath: '/template-api',
            receivers: [],
            receiverIndex: 0,
            float: {
                show: false,
            },
            holderTriggerList: [],
            rgbColorInfo: rgbColorInfo.slice(1),
            templateStatus: this.$route.params.templateStatus,
        };
    },
    computed: {
        ...mapState('template', ['docList', 'currentDocIndex', 'watermarkList', 'ridingSealList', 'focusLabelOpt', 'zoom']),
        ...mapGetters('template', ['currentDoc']),
    },
    methods: {
        ...mapMutations('template', ['setDocList', 'setCurrentDocIndex', 'setCurrentPageIndex', 'setFocusLabelOpt', 'setReceivers', 'setWatermarkList', 'setRidingSealList']),
        closeHandle() {
            this.$router.go(-1);
        },
        getDocInfo() {
            return this.$http.put(`${this.tempPath}/dynamic-template/${this.templateId}/decoration-preview`);
        },
        onSwitchReceiver(index) {
            this.receiverIndex = index;
        },
        // 初始化收件人数据
        initReceiversData(data) {
            return data.map((item) => {
                // 后端做了判断，直接取值
                const labelName = item.roleName;
                return {
                    ...item,
                    labelName,
                };
            });
        },
        handleResponseData(docData, inialData) {
            const initialDocData = cloneDeep(docData);
            const handledData = initialDocData.map((doc) => {
                doc.documentPages = new Array(doc.pageSize).fill({});
                const newArr = [];
                doc.documentPages.map((page, index) => {
                    newArr.push(Object.assign({}, page, {
                        width: inialData.maxWidth,
                        height: inialData.maxHeight,
                        imagePreviewUrl: `/template-api/templates/${this.templateId}/documents/${doc.documentId}/view/${index + 1}?_=${dayjs(new Date()).valueOf()}&access_token=${this.$cookie.get('access_token')}`,
                        highQualityPreviewUrl: `/template-api/templates/${this.templateId}/documents/${doc.documentId}/view/${index + 1}?_=${dayjs(new Date()).valueOf()}&access_token=${this.$cookie.get('access_token')}`,
                        pageNumber: index + 1,
                        documentName: doc.fileName,
                    }));
                });
                doc.documentPages =  newArr;
                return doc;
            });
            return handledData;
        },
        initDoc(resData) {
            resData.forEach((doc) => {
                doc.documentPages.forEach((page) => {
                    page.documentName = doc.documentName;
                    // page.imagePreviewUrl = this.$hybrid.getContractImg(page.imagePreviewUrl);
                    // page.highQualityPreviewUrl = this.$hybrid.getContractImg(page.highQualityPreviewUrl);
                });
                doc.image = doc.documentPages[0].imagePreviewUrl; // 缩略图只需 第一页
            });
            return resData;
        },
        // 右侧修改页数
        changePage(index) {
            const $scrollEl = document.querySelector('.point-position-doc-list');
            const docPages = document.querySelectorAll('.point-position-doc-pages')[this.currentDocIndex].children;
            const initY = document.querySelector('.point-position-doc-wrapper').offsetTop;
            const y = docPages[index].offsetTop * this.zoom + initY;
            scrollToYSmooth($scrollEl, y, 400, 'ease-out');
            setTimeout(() => {
                this.setCurrentPageIndex(index);
            }, 400);
        },
        // 切换文档
        changeDoc(docIndex) {
            this.setCurrentDocIndex(docIndex);
            this.$nextTick(() => {
                setTimeout(() => {
                    document.querySelector('.point-position-doc-list').scrollTo(0, 0);
                }, 400);
            });
        },
        initData() {
            const loading = this.$loading();
            this.setDocList([]);
            this.setWatermarkList([]);
            this.setRidingSealList([]);
            this.getDocInfo()
                .then(res => {
                    const labelsAndReceiversData = res.data;
                    // 初始化receivers数据
                    this.receivers = this.initReceiversData(labelsAndReceiversData.receivers);
                    this.setReceivers(this.receivers);
                    this.setDocList(this.initDoc(this.handleResponseData(labelsAndReceiversData.documents, labelsAndReceiversData)));
                    this.setWatermarkList(initWatermark(labelsAndReceiversData.decorate.watermarks, this.receivers));
                    this.setRidingSealList(initRidingSeal(labelsAndReceiversData.decorate.ridingSeals, this.receivers));
                }).finally(() => {
                    loading.close();
                });
        },
    },
    created() {
        this.initData();
    },
};
</script>

<style lang="scss">
 @import "./index.scss";
.register-footer{
            position: absolute;
            bottom: 0;
        }
.page-container {
        position: relative;
        min-height: 100vh;
        .content-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            padding-top: 63px;
            padding-bottom: 35px;
            background: #F6F6F6;
            font-size: 12px;
            box-sizing: border-box;
            &.hd-hidden{
                padding-top:0;
            }
            &.ft-hidden{
                padding-bottom: 0;
            }
        }
        .footer-follow {
            position: fixed;
            z-index: 1;
            bottom: 0;
            left: 0;
        }
    }
.temp-dynamic-field {
    position: absolute;
    top: 60px;
    bottom: 35px;
    width: 100%;
    overflow: hidden;
    .site-content {
        height: 100%;
        .FieldSite {
            position: relative;
            width: 210px;
            height: 100%;
            border-right: 1px solid #DDDDDD;
        }
    }
}
</style>
