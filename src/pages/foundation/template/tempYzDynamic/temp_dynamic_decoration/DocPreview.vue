<template>
    <div class="point-position-doc" :style="{'margin-left': canAdd === 'none' ? '0': '211px'}">
        <!-- 合同内容 -->
        <div class="point-position-doc-con documents-content" ref="docContent">
            <template v-if="docList.length">
                <p class="point-position-doc-title">{{ currentDoc.fileName }}</p>
                <div class="point-position-doc-list" @scroll="onDocScroll">
                    <!-- 水印头部标签 -->
                    <div class="watermarks-container" :style="{'width': `${docWidth - 100}px`}">
                        <Watermark
                            @delete="handleDeleteLabel(watermark.decorateId, watermarkList, index, 'watermarkList')"
                            v-for="(watermark, index) in watermarkList"
                            :key="watermark.receiverId"
                            :watermark="watermark"
                            :color="computeRgbColor(watermark, true)"
                            :readonly="true"
                            :decorateCanChange="true"
                            :templateStatus="templateStatus"
                        >
                        </Watermark>
                    </div>
                    <div class="point-position-doc-wrapper" :style="{'width': `${docWidth - 100}px`}">
                        <!-- 文档循环 -->
                        <template v-for="(doc, docIndex) in docList">
                            <div :style="{transform:`scale(${zoom})`,'transform-origin': 'left top', width: `${docMaxWidth}px`}" v-show="currentDocIndex == docIndex" :key="docIndex" class="point-position-doc-pages">
                                <!-- 所有页面 -->
                                <div v-for="(page, pageIndex) in doc.documentPages" class="point-position-doc-page" :key="pageIndex" :style="{'padding-right': isShowCurRidingSeal ? '116px': ''}">
                                    <div class="point-position-doc-page-con"
                                        :style="{width: `${page.width}px`,height: `${page.height}px`}"
                                        :page-index="pageIndex"
                                    >
                                        <img
                                            class="image"
                                            :width="page.width"
                                            :height="page.height"
                                            v-lazy="{ src: page.highQualityPreviewUrl, split: 2, index:pageIndex, total: doc.documentPages.length }"
                                        />
                                        <div class="water-mark-back" :style="{background: waterMarkPng ? `url(${waterMarkPng}) repeat` : 'none'}"></div>
                                        <Labels
                                            v-for="(mark, markIndex) in markList(pageIndex)"
                                            :key="markIndex"
                                            :pageHeight="page.height"
                                            :pageWidth="page.width"
                                            :scale="scale"
                                            :color="computeRgbColor(mark)"
                                            :receiverName="computeOwner(mark)"
                                            :mark="mark"
                                            :needCloseIcon="false"
                                        >
                                        </Labels>
                                        <!-- 骑缝章 -->
                                        <div class="riding-seals" v-if="isShowCurRidingSeal">
                                            <div class="riding-seals-bg"></div>
                                            <RidingSeal
                                                v-for="(ridingseal, sealIndex) in ridingSealList"
                                                :key="ridingseal.receiverId"
                                                :ridingseal="ridingseal"
                                                :pageHeight="docList[0].documentPages[0].height"
                                                :color="computeRgbColor(ridingseal, true)"
                                                :canDrag="canDrag"
                                                :canAdd="canAdd"
                                                :sealIndex="sealIndex"
                                                :zoom="zoom"
                                                @delete="handleDeleteLabel(ridingseal.decorateId, ridingSealList, sealIndex, 'ridingseal')"
                                            >
                                            </RidingSeal>
                                        </div>
                                    </div>
                                    <!-- 页脚 -->
                                    <p class="point-position-doc-page-footer" :key="pageIndex+'size'" :style="{width: `${page.width}px`}">{{ currentDoc.fileName }}<span class="fr">{{ $t('pointPositionDoc.pageTip', { pageNum: pageIndex + 1, pageSize: doc.documentPages.length}) }}</span></p>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
                <!-- 进入下一份文档 -->
                <div class="next-doc-btn" v-if="currentDocIndex < docList.length-1" @click="changeDoc">{{ $t('pointPositionDoc.nextDoc') }}</div>
            </template>
        </div>
    </div>
</template>

<script>
import { mapState, mapMutations, mapGetters } from 'vuex';
import { createWaterMark, RIDESEAL_SHADOW_WIDTH } from 'utils/decorate.js';
import { debounce } from 'utils/decorateTool.js';
import RidingSeal from './RidingSeal.vue';
import Watermark from 'components/watermarkEdit/WatermarkEdit.vue';
import Labels from 'components/label/Label.vue';
import { rgbColorInfo } from 'src/common/utils/decorateConst.js';
import { COLOR_TYPES } from 'src/common/utils/labelStyle.js';
export default {
    components: {
        Watermark,
        RidingSeal,
        Labels,
    },
    data() {
        return {
            docWidth: 0,
            tempPath: '/template-api',
            templateId: this.$route.query.templateId,
            templateStatus: this.$route.params.templateStatus || '',
        };
    },
    computed: {
        ...mapState('template', ['docList', 'zoom', 'currentDocIndex', 'receivers', 'receiverIndex', 'watermarkList', 'ridingSealList', 'focusLabelOpt']),
        ...mapGetters('template', ['currentDoc']),
        markList() {
            return (pageIndex) => {
                // 当前文档的markList
                const marks = this.currentDoc.labels;
                const markList = (marks || []).concat(this.tempMark || []).filter(mark => mark.pageNumber === pageIndex + 1);
                return markList;
            };
        },
        canAdd() { // 'all','some','none  新增和删除标签权限
            return this.templateStatus === 'edit' ? 'all' : 'none';
        },
        canDrag() {
            return this.templateStatus === 'edit';
        },
        // 水印图片
        waterMarkPng() {
            const watermarkText = (this.watermarkList || []).map(item => item.watermarkText);
            if (!watermarkText.length) {
                return null;
            }
            return createWaterMark(watermarkText, 300, 200);
        },
        isShowCurRidingSeal() {
            return  this.ridingSealList.length && (this.currentDoc.pageSize > 1);
        },
        docMaxWidth() { // 文档宽度(是否有骑缝章计算宽度)
            const maxWidth = this.currentDoc.documentPages ? this.currentDoc.documentPages[0].width : 0;
            return this.isShowCurRidingSeal > 0 ? maxWidth + RIDESEAL_SHADOW_WIDTH : maxWidth;
        },
        zoom() { // 合适的缩放
            return (this.docWidth - 100) / this.docMaxWidth;
        },
        // 计算页面的缩放比
        scale() {
            const { maxWidth, docWidth } = this;
            if (!maxWidth || !docWidth) {
                return 1;
            }
            return docWidth / maxWidth;
        },
        // 文档的最大宽度
        maxWidth() {
            // 切换文档时，需要重新计算文档的最大宽高度
            const docMaxWidth = this.currentDoc.documentPages[0].height;
            // 除与合同重叠部分以外，骑缝章宽度/阴影背景部分宽度
            return docMaxWidth + RIDESEAL_SHADOW_WIDTH;
        },
    },
    watch: {
        zoom(v) {
            if (v) {
                this.setZoom(v);
            }
        },
    },

    methods: {
        ...mapMutations('template', ['setDocList', 'setCurrentDocIndex', 'setCurrentPageIndex', 'setFocusLabelOpt', 'setZoom', 'setWatermarkList', 'setRidingSealList']),
        computeOwner(mark) {
            const receiver = (this.receivers || []).find(receiver => receiver.receiverId === mark.roleId) || {};
            return receiver.showName || '';
        },
        // 切换文档
        changeDoc() {
            this.$emit('change-doc', this.currentDocIndex + 1);
        },
        // 删除标签
        handleDeleteLabel(id, list, index, type) {
            // 删除查验码
            if (list[index].type === 'QR_CODE') {
                return this.confirmDelQRCodeLabel(id);
            }
            this.deleteLabel(id, type).then(() => {
                this.$MessageToast.success(this.$t('pointPositionDoc.deleteTip'));
                list.splice(index, 1);
                this.resetLabelFocus();
            });
        },
        // 重置选中的标签
        resetLabelFocus() {
            this.setFocusLabelOpt({
                labelId: '',
                labelButtonInd: -1,
            });
        },
        onDocScroll: debounce(function(e) {
            const initY = document.querySelector('.point-position-doc-wrapper').offsetTop;
            const scrollTop = (e.target.scrollTop - initY)  / this.zoom;
            const $currentDoc = document.querySelectorAll('.point-position-doc-pages')[this.currentDocIndex];
            const docPages = $currentDoc.querySelectorAll('.point-position-doc-page');
            let pageIndex = 0;
            for (let i = 0; i < docPages.length; i++) {
                if (docPages[i].offsetTop + docPages[i].offsetHeight > scrollTop + 1) {
                    pageIndex = i;
                    break;
                }
            }
            this.setCurrentPageIndex(pageIndex);
        }, 100),
        // 删除标签(包括水印与骑缝章)
        deleteLabel(id, type) {
            if (type === 'label') {
                return Vue.$http.delete(`${this.tempPath}/v2/templates/${this.templateId}/label/${id}`);
            } else if (type === 'ridingseal') {
                return Vue.$http.delete(`${this.tempPath}/v2/templates/${this.templateId}/riding-seal/${id}`);
            } else {
                return Vue.$http.delete(`${this.tempPath}/v2/templates/${this.templateId}/watermark/${id}`);
            }
        },
        findReceiverIdx(id) {
            return this.receivers.findIndex(item => item.receiverId === id);
        },
        computeRgbColor(label, isDecotate) {
            const id = isDecotate ? label.receiverId : label.roleId;
            // 签署方或者印章、签名、日期类型时，根据身份选择
            if (isDecotate || COLOR_TYPES.includes(label.type) || (label.labelExtends && label.labelExtends.receiverFill)) {
                return rgbColorInfo[this.findReceiverIdx(id) % 8] || 'transparent';
            } else {
                // 发件方使用固定颜色
                return  rgbColorInfo[0];
            }
        },
    },
    mounted() {
        this.docWidth = this.$refs.docContent.getBoundingClientRect().width;
        const _this = this;
        window.onresize = () => {
            _this.docWidth = _this.$refs.docContent.getBoundingClientRect().width;
        };
    },
};

</script>
<style lang="scss">
 @import "./index.scss";
.point-position-doc {
    height: 100%;
    overflow: auto;
    margin-right: 211px;
    .point-position-doc-con{
        overflow: auto;
        height: 100%;
        background: $--background-color-regular;
        display: flex;
        flex-direction: column;
        height: 100%;
        .point-position-doc-wrapper{
            height: 100%;
            margin: 0 auto;
            flex: 1;
            padding: 0 10px;
        }
        .point-position-doc-title{
            line-height: 38px;
            font-size: 16px;
            color: $--color-text-primary;
            border-bottom: 1px solid $--border-color-light;
            text-align: center;
        }
        .point-position-doc-list{
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }
        .next-doc-btn {
            color: $theme-color;
            margin: 15px 0;
            text-align: center;
            font-weight: 700;
            font-size: 14px;
            cursor: pointer;
        }
    }
    .point-position-doc-page-con{
        position: relative;
        margin: 0 auto;
        .image{
            background-color: $--color-white;
            background-repeat: repeat;
            box-shadow: 0 0 8px 0 rgba(0,0,0,0.15);
            width: 100%;
            height: 100%;
        }
        .water-mark-back{
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
        }
        .trigger-con{
            border: 1px dashed $--color-primary;
            position: absolute;
            box-sizing: border-box;
        }
        .riding-seals {
            position: absolute;
            box-sizing: border-box;
            padding-top: 1px;
            width: 160px;
            border: 1px dashed $--color-primary;
            background: rgba(18, 127, 210, 0.05);
            top: 0;
            bottom: 0;
            right: -116px;
            z-index: 100;
            height: 100%;

            &-bg {
                margin-left: 41px;
                width: 116px;
                height: 100%;
                background: $--background-color-secondary;
                background-image: linear-gradient(270deg, rgba(255, 255, 255, 0.50) 0%, rgba(217, 217, 217, 0.50) 100%);
                background-size: 23px;
            }
        }
    }
    .point-position-doc-page-footer{
        font-size: 12px;
        color: $--color-info;
        line-height: 20px;
        text-align: left;
        margin: 0 auto;
    }

    .QRCode-preview-dialog *{
        box-sizing: border-box;
    }
}
</style>
