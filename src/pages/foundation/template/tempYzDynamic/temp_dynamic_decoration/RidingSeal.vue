<template>
    <div class="riding-seal-item"
        :class="{'active-seal': isMoving && theCanDrag, 'widden': !theCanDrag}"
        :style="{ top: `${(1 - top - height) * 100}%`}"
    >
        <p class="seal-title">{{ ridingseal.showName }}</p>
        <div
            class="seal-content"
            @mousedown.stop="onMousedown($event)"
            :style="{
                backgroundColor: `rgba(${color}, 0.8)`,
                pointerEvents: theCanDrag ? 'all' : 'none',
            }"
        >
            <div class="seal-icon">
                <i class="el-icon-ssq-qifengzhang1"></i>
            </div>
        </div>
        <i class="close-icon el-icon-ssq-guanbi" v-if="canAdd!=='none'" @click="handleDelete"></i>
    </div>
</template>
<script>
import { mapState } from 'vuex';
export default {
    props: {
        ridingseal: {
            type: Object,
            default: function() {
                return {};
            },
        },
        color: {
            type: String,
            default: '',
        },
        pageHeight: {
            type: Number,
            default: 0,
        },
        canDrag: {
            type: Boolean,
            default: false,
        },
        sealIndex: {
            type: Number,
            default: 0,
        },
        canAdd: { // 是否可以新增删除
            type: String,
            default: 'all',
        },
    },
    data() {
        return {
            top: 0,
            moveY: 0, // 记录mousedown时鼠标的起始位置
            isMoving: false,
            start: 0,
            height: 0,
            templateId: this.$route.query.templateId,
            tempPath: '/template-api',
            templateStatus: this.$route.params.templateStatus,
        };
    },
    computed: {
        ...mapState('template', ['ridingSealList', 'docList', 'zoom', 'receivers']),
        theCanDrag() { // 使用阶段移动不受权限控制，都不可移动
            return this.templateStatus === 'edit' && this.canDrag;
        },
    },
    watch: {
        'ridingseal.y': {
            handler(newVal) {
                this.top = newVal;
            },
            immediate: true,
        },
    },
    methods: {
        updateRideSealLabel(opts) {
            return  Vue.$http.post(`${this.tempPath}/v2/templates/${this.templateId}/riding-seal`, opts);
        },
        handleDelete() {
            this.$emit('delete');
        },
        onMousedown(e) {
            if (!this.theCanDrag) {
                return;
            }
            this.isMoving = true;
            this.moveY = e.clientY;
            // 这里不将事件绑定在label上的原因是，鼠标跟随有延迟时，会导致label leave
            document.addEventListener('mousemove', this.onMousemove);
            document.addEventListener('mouseup', this.onMouseup);
        },
        onMousemove(e) {
            if (!this.isMoving) {
                return;
            }
            const { y: sealY } = this.ridingseal;
            const distanceY = e.clientY - this.moveY;
            // // Y轴方向移动的距离
            let y = sealY - distanceY / (this.pageHeight * this.zoom);
            if (y < 0) {
                y = 0;
            }
            if (y > 1 - this.height) {
                y = 1 - this.height;
            }
            this.top = y;
        },
        onMouseup() {
            if (!this.isMoving) {
                return;
            }
            this.isMoving = false;
            this.updateRidingSeal();
            document.removeEventListener('mousemove', this.onMousemove);
            document.removeEventListener('mouseup', this.onMouseup);
        },
        updateRidingSeal() {
            const rideSeal = this.ridingSealList[this.sealIndex];
            const newRideSeal = {
                ...rideSeal,
                y: this.top, // 骑缝章左下角据据页面左下角的距离
            };
            const receiver = this.receivers.find(item => item.receiverId === rideSeal.receiverId);
            this.updateRideSealLabel(newRideSeal).then(res => {
                this.$set(this.ridingSealList, this.sealIndex, {
                    ...res.data,
                    showName: receiver.showName,
                });
            }).catch(() => {
                this.top = this.start;
            });
        },
    },
    mounted() {
        this.top = this.ridingseal.y;
        this.start =  this.ridingseal.y;
        this.height = this.ridingseal.height || 0.01;
    },
};
</script>

<style lang="scss">
 @import "./index.scss";
.riding-seal-item {
    position: absolute;
    transform: translate(0px, -20px);
    cursor: move;
    .seal-title {
        padding: 0 4px;
        margin-bottom: 2px;
        height: 18px;
        line-height: 18px;
        max-width: 157px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        box-sizing: border-box;
        background-color: rgb(255, 247, 182);
        user-select: none;
        font-size: 12px;
    }
    .seal-content {
        border-radius: 4px;
        width: 158px;
        height: 160px;
        border: 2px solid transparent;
        box-sizing: border-box;
        .seal-icon {
            width: 130px;
            height: 130px;
            border-radius: 50%;
            text-align: center;
            margin: 13px auto 0;
            background-color: rgba(255, 255, 255, 0.3);
            .el-icon-ssq-qifengzhang1 {
                transform: translate(0, 45%);
                font-size: 70px;
                color: $--color-black;
            }
        }
        &:hover {
            cursor: move;
        }
    }

    &.widden {
        .seal-title {
            max-width: 158px;
        }
        .seal-content {
            width: 154px;
            .seal-icon {
                margin-left: 12px;
            }
        }
    }
    &.active-seal {
        .seal-title {
            // position: relative;
            z-index: 999;
        }
        .seal-content {
            border: 2px solid $theme-color;
            // position: relative;
            z-index: 999;
        }
        .close-icon {
            z-index: 9999;
        }
    }
    .close-icon {
        color: $--color-white;
        font-size: 12px;
        background-color: rgba(51, 51, 51, .75);
        padding: 5px;
        border-radius: 12px;
        top: 9px;
        position: absolute;
        right: -6px;
        cursor: pointer;
    }
}
</style>
