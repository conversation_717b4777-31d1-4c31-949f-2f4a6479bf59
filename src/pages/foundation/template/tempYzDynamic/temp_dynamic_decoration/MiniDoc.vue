<template>
    <div class="doc-mini-documents">
        <div class="title">
            {{ $t('pointPositionMiniDoc.document') }}
            <!-- 共xx份 -->
            <span class="fr">{{ $t('pointPositionMiniDoc.documentsLength', {documentsLength: documents.length}) }}</span>
        </div>
        <ul>
            <li class="doc" :class="{active:currentDocIndex === docIndex}" v-for="(doc, docIndex) in documents" :key="docIndex">
                <div class="doc-title">{{ doc.fileName }}（{{ doc.pageSize }}{{ $t('pointPositionMiniDoc.page') }}）</div>
                <div class="doc-thumbnail-container">
                    <div
                        @click="handleClickMiniImage(docIndex)"
                        class="doc-thumbnail"
                        :style="{ backgroundImage: `url(${doc.image})`}"
                    >
                    </div>
                </div>
                <div class="doc-totalPage" :class="{ ['lang_' + $t('lang')]: true }">
                    <template v-if="doc.documentPages.length > 1">
                        <el-input
                            v-if="currentDocIndex === docIndex"
                            v-model="page"
                            :placeholder="$t('pointPositionMiniDoc.pager')"
                            @blur="onBlur()"
                            @keyup.enter.native="onBlur()"
                        >
                        </el-input>
                        <el-input v-else :disabled="true" :placeholder="$t('pointPositionMiniDoc.pager')">
                        </el-input>
                        / </template>{{ doc.documentPages.length }}{{ $t('pointPositionMiniDoc.page') }}
                </div>
                <div v-if="doc.attachments && doc.attachments.length" class="doc-attachments">
                    <p class="doc-attachments-title">附属文件：</p>
                    <div class="doc-attachments-line" v-for="attachment in doc.attachments" :key="attachment.attachmentId">
                        {{ attachment.attachmentName }}<span>（{{ attachment.pageSize }}页）</span>
                    </div>
                </div>
            </li>
        </ul>
    </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
let timer;

export default {
    props: {
        documents: {
            type: Array,
            default() {
                return [];
            },
        },
    },
    data() {
        return {
            page: 1,
        };
    },
    computed: {
        ...mapState('template', ['currentDocIndex', 'currentPageIndex']),
    },
    watch: {
        currentDocIndex() {
            this.setCurrentPageIndex(0);
        },
        currentPageIndex(v) {
            this.page = v + 1;
        },
    },
    methods: {
        ...mapMutations('template', ['setCurrentPageIndex']),
        handleClickMiniImage(docIndex) {
            clearTimeout(timer);
            this.page = 1;
            this.$emit('change-doc', docIndex);
        },
        onBlur() {
            const _this = this;
            // 防止onblur与handleClickMiniImage同时执行
            timer = setTimeout(function() {
                _this.page = parseInt(_this.page) || 1; // 取整数
                _this.page = _this.page < 1 ? 1 : _this.page;
                const maxPage = _this.documents[_this.currentDocIndex].documentPages.length;
                _this.page = _this.page > maxPage ? maxPage : _this.page;
                _this.$emit('change-page', _this.page - 1);
            }, 120);
        },
    },
};
</script>

<style lang="scss">
    @import "./index.scss";
    .doc-mini-documents {
        width: 210px;
        height: 100%;
        position: absolute;
        top: 0;
        right: 0;
        overflow: auto;
        font-size: 12px;
        box-shadow: 6px 0px 6px -6px $--border-color-light;
        border-left: 1px solid $border-color;
        .title{
            line-height: 38px;
			padding: 0 15px;
			font-size: 14px;
			color: $--color-text-primary;
            border-bottom: 1px solid $border-color;
            .fr{
                font-size: 12px;
                color: $--color-info;
            }
        }

        .doc {
            padding-top: 6px;
            padding-bottom: 6px;
            border-bottom: 1px solid $border-color;
            &.active {
                background: $--color-white;
                border:2px solid $theme-color;
                .doc-thumbnail-container{
                    background-image: url(~img/doc-active.png);
                }
            }
            .doc-title {
                position: relative;
                height: 18px;
                line-height: 18px;
                color: $--color-text-primary;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                padding: 6px 40px 6px 19px;
            }
            .doc-totalPage {
                color: $--color-text-secondary;
                margin-bottom: 10px;
                line-height: 30px;
                color: $--color-text-secondary;
                text-align: center;
                &.lang_ru {
                    line-height: 10px;
                }
                .el-input{
                    width: 52px;
                    .el-input__inner{
                        box-sizing: border-box;
                        height: 24px;
                        border-radius: 2px;
                        padding: 3px 5px;
                    }
                }
            }
            .doc-thumbnail-container {
                margin: 5px auto;
                width: 132px;
                height: 161px;
                background-image: url(~img/doc.png);
                background-position: top left;
                background-repeat: no-repeat;
                background-size: 132px 161px;
                cursor: pointer;
                .doc-thumbnail {
                    width: 115px;
                    height: 144px;
                    background-repeat: no-repeat;
                    background-size: 115px 144px;
                    background-position: 4px 4px;
                }
            }
            .doc-attachments{
                padding: 6px 0 6px 19px;
                line-height: 18px;
                font-weight: 400;
                .doc-attachments-title{
                    color: $--color-text-secondary;
                }
                .doc-attachments-line{
                    color: $--color-text-primary;
                    line-height: 26px;
                    span{
                        color: $--color-text-secondary;
                    }
                }
            }
        }
    }
</style>
