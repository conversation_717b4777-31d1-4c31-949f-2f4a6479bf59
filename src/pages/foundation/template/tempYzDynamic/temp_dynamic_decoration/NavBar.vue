<template>
    <div class="temp-field-site-cpn dynamic-yz" v-if="receivers.length">

        <div class="site-bar-head">
            <span class="order-num">第一步：</span>
            <span class="head-title">选择签约方</span>
        </div>
        <div class="signers">
            <ul>
                <el-tooltip class="item"
                    effect="dark"
                    v-for="(item, index) in receivers"
                    :key="item.userAccount"
                    :content="item.showName"
                    popper-class="field-site-popper"
                    placement="right"
                >
                    <li @click="switchReceiver(index)">
                        <span class="signers-color" :style="computeBgc(index)"></span>
                        <span class="signers-name etc-sigle">{{ item.showName }}</span>
                        <i class="signers-active el-icon-ssq-qianyuewancheng" v-show="index == receiverIndex"></i>
                    </li>
                </el-tooltip>
            </ul>
        </div>

        <!-- 合同装饰：水印和骑缝章 -->
        <div>
            <div class="site-bar-head" :class="{ ['lang_' + $t('lang')]: true }">
                <span class="order-num">{{ $t('field.decoration') }}</span>
                <span class="order-num-suffix">({{ $t('field.optional') }})</span>
            </div>
            <div class="site-bar-content">
                <ul>
                    <li v-for="item in decorationIcons"
                        :key="item.class"
                        v-show="item.show"
                        @click="onAddDecorator(item.type)"
                    >
                        <i :class="item.class" :style="computeBgc(receiverIndex)"></i>
                        <span>{{ item.name }}</span>
                    </li>
                </ul>
            </div>
        </div>

    </div>
</template>
<script>
import { factoryWatermarkText, calcRideSeal } from 'src/common/utils/decorateTool.js';
import { calcRgbColorByIndex } from 'utils/colorInfo.js';
import { mapState } from 'vuex';
// 模板中骑缝章宽高，签署时骑缝章容器宽度160，保证宽度100%
export const DEFAULT_RS_HEIGHT = 160;
// 除与合同重叠部分以外，骑缝章宽度/阴影背景部分宽度
export const RIDESEAL_SHADOW_WIDTH = 120;
export default {
    props: {
        receivers: {
            type: Array,
            default: function() {
                return [];
            },
        },
    },
    data() {
        return {
            tempPath: '/template-api',
            receiverIndex: 0,
            templateId: this.$route.query.templateId,
        };
    },
    computed: {
        ...mapState('template', ['watermarkList', 'docList', 'ridingSealList']),
        decorationIcons() {
            const userType = this.receivers[this.receiverIndex].userType;
            return [
                {
                    class: 'el-icon-ssq-shuiyin', // 水印
                    type: 'WATERMARK',
                    name: this.$t('field.watermark'),
                    show: true,
                }, {
                    class: 'el-icon-ssq-qifengzhang', // 骑缝章
                    type: 'DECORATE_RIDING_SEAL',
                    name: this.$t('field.ridingStamp'),
                    show: userType === 'ENTERPRISE',
                },
            ];
        },
    },
    methods: {
        // 切换接受人
        switchReceiver(v) {
            this.receiverIndex = v;
            this.$emit('switch-receiver', {
                receiverIndex: v,
            });
        },
        computeBgc(i) {
            const color = calcRgbColorByIndex(i);
            return `background-color: rgba(${color}, 0.6);
                        border: 1px solid rgb(${color})`;
        },
        onAddDecorator(type) {
            if (type === 'WATERMARK') {
                return this.addWatermark();
            }
            if (type === 'DECORATE_RIDING_SEAL') {
                return this.addRideSeal();
            }
        },
        // 新增水印
        addWaterLabel(opts) {
            return Vue.$http.post(`${this.tempPath}/v2/templates/${this.templateId}/watermark`, opts);
        },

        // 新增骑缝章
        updateRideSealLabel(opts) {
            return  Vue.$http.post(`${this.tempPath}/v2/templates/${this.templateId}/riding-seal`, opts);
        },

        // 新增或者编辑水印
        addWatermark() {
            const { receiverId, showName } = this.receivers[this.receiverIndex] || {};
            const watermarkList = this.watermarkList;
            if (watermarkList.find(item => item.receiverId === receiverId)) {
                return;
            }
            this.addWaterLabel({ receiverId }).then((res) => {
                watermarkList.push({
                    ...res.data,
                    watermarkText: factoryWatermarkText(this.receivers[this.receiverIndex]),
                    showName,
                    receiverId,
                });
            });
        },
        addRideSeal() {
            if (!this.blankDocument && this.docList.every(doc => doc.pageSize === 1)) {
                this.$MessageToast.error(this.$t('pointPositionSite.singlePageRidingSealTip')); // '单页文档无法添加骑缝章'
                return;
            }
            const { receiverId, showName } = this.receivers[this.receiverIndex] || {};
            if (this.ridingSealList.find(item => item.receiverId === receiverId)) {
                return;
            }
            const pageInfo = this.docList[0].documentPages[0];
            const y = calcRideSeal(this.ridingSealList, pageInfo.height);
            const rideSeal = {
                labelId: '',
                receiverId: receiverId,
                y,
                height: DEFAULT_RS_HEIGHT / pageInfo.height,
                width: DEFAULT_RS_HEIGHT / pageInfo.width,
            };
            this.updateRideSealLabel(rideSeal).then((res) => {
                this.ridingSealList.push({
                    ...res.data,
                    showName,
                });
            });
        },
    },
};
</script>
<style lang="scss">
$site-bgclr: #00ffff;
.temp-field-site-cpn.dynamic-yz {
    .site-bar-head {
        height: 38px;
        line-height: 42px;
        font-size: 16px;
        color: #333;
        padding-left: 15px;
        border-top: 2px solid #127fd2;
        border-bottom: 1px solid #ddd;
        span {
            vertical-align: middle;
        }
        &:first-child {
            margin-top: -2px;
        }
        .order-num {
            font-size: 16px;
            color: #127fd2;
        }
    }

    // 接收人
    .signers {
        height: 143px;
        background-color: #f6f6f6;
        padding-top: 3px;
        overflow-y: auto;
        li {
            height: 30px;
            line-height: 30px;
            cursor: pointer;
            .signers-color {
                display: inline-block;
                float: left;
                width: 14px;
                height: 14px;
                border-radius: 50%;
                margin: 7px 7px 0 18px;
            }
            .signers-name {
                display: inline-block;
                width: 120px;
            }
            .signers-active {
                display: inline-block;
                float: right;
                font-weight: bold;
                margin-top: 9px;
                margin-right: 14px;
            }
            &.active {

            }
            &:hover {
                background-color: #eee;
            }
        }
    }

    // 字段
    .site-bar-body {
        height: calc(100% - 238px);
        overflow: auto;
    }
    .site-bar-title {
        height: 38px;
        line-height: 38px;
        font-size: 14px;
        font-weight: bold;
        color: #333;
        padding-left: 22px;
        border-bottom: 1px solid $border-color;

        .el-icon-ssq-wenhao{
            margin-left: 5px;
            font-size: 12px;
            color: #666;
            cursor: pointer;
        }
    }
    .site-bar-content {
        background-color: #fafafa;
        padding-top: 10px;
        padding-bottom: 15px;
        border-bottom: 1px solid $border-color;
        li {
            display: block;
            height: 32px;
            line-height: 32px;
            white-space: nowrap;
            position: relative;
            &:hover {
                background-color: #eee;
                cursor: pointer;
                .metaLabel-operate-block {
                    display: inline-block;
                }
            }
            i {
                width: 24px;
                height: 24px;
                line-height: 24px;
                text-align: center;
                font-size: 16px;
                background-color: $site-bgclr;
                border-radius: 3px;
                margin-top: 4px;
                margin-left: 22px;
                margin-right: 10px;
            }
            .el-icon-ssq-tiaokuantubiao {
                font-size: 14px;
            }
            .metaLabel-name {
                display: inline-block;
                width: 125px;
                overflow: hidden;
                vertical-align: top;
                text-overflow: ellipsis;
            }

            .metaLabel-operate-block{
                right: 0;
                position: absolute;
                display: none;
                margin-right: 12px;

                &:hover{
                    .metaLabel-hidden-operate{
                        display: block;
                    }

                    .metaLabel-operate-icon{
                        color: #1687dc;
                    }
                }

                .metaLabel-operate-icon{
                    margin-left: 0;
                    margin-right: 0;
                    background-color: transparent;
                }

                .metaLabel-hidden-operate{
                    display: none;
                    position: absolute;
                    left: -30px;
                    top: 29px;
                    background: #fff;
                    border: 1px solid #ddd;
                    text-align: center;
                    box-shadow: 0px 2px 3px 1px #ddd;;
                    z-index: 1;

                    li{
                        width: 60px;
                        height: 36px;
                        line-height: 36px;
                        color: #333;
                        text-align: center;
                        font-size: 12px;

                        &:hover{
                            background: #f6f6f6;
                        }
                    }
                }
            }
        }
    }
    .metaLabel {
        position: relative;
        .add-metaLabel {
            position: absolute;
            top: 12px;
            right: 42px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;

            &:hover{
                color: #127fd2;
            }
        }

        .toManageLink{
            display: inline-block;
            position: absolute;
            top: 1px;
            right: 15px;

            .el-icon-ssq-shezhi{
                color: #666;
                font-size: 16px;

                &:hover{
                    color: #127fd2;
                }
            }
        }
    }
    .metaLabel-content {
        .metaLabel-content-ul{
            padding-bottom: 20px;
        }
    }

    .site-bar-bizname-search {
        margin: 0 10px 10px 10px;
    }

    .biz-metaLabel-content .metaLabel-content-ul{
        padding-bottom: 20px;
    }

    .box-sizing-dialog{
        .dialog-confirm-deleteLabel .el-dialog{
            width: 435px;
            .el-dialog__body p{
                line-height: 25px;
            }
        }
        .dialog-no-promission .el-dialog{
            width: 360px;
        }

        .dialog-tempMeta-desc .el-dialog, .dialog-meta-desc .el-dialog{
            width: 655px;

            .el-dialog__body{
                p{
                    color: #333;
                    line-height: 30px;
                }
            }
        }
    }
}
.field-site-popper {
    &.is-dark {
        background: #333;
    }
}
.temp-field-site-cpn__metaLabel-name-popper {
    width: 125px;
    line-height: 1.5;
}
.site-bar-decoration {
    display: flex;
    flex-direction: column;
    align-items:  center;
    .decoration-title {
        margin: 19px 0 3px 0;
        cursor: pointer;
        width: 180px;
        height: 30px;
        line-height: 30px;
        background: #FFFFFF;
        border: 1px solid #127FD2;
        font-size: 14px;
        color: #127FD2;
        border-radius: 2px;
        text-align:center;
    }
    .decoration-toast {
        font-size: 12px;
        font-weight: 400;
        color: #999999;
    }
}
</style>
