<template>
    <div class="dynamic-field-doc"
        ref="FieldDoc"
        v-loading.fullscreen.lock="pageLoading"
        :element-loading-text="loadingText"
    >
        <iframe class="yz-iframe" ref="yz_editor" width="100%" height="100%" :src="iframeSrcUrl"></iframe>
        <!-- 动态模版高亮提示弹窗 -->
        <el-dialog
            title="提醒"
            :visible.sync="showFieldChangeTipDialog"
            custom-class="marks-change-tip-dialog"
            size="tiny"
            top="35%"
            @close="onUpdateBkContent"
        >
            <h2 class="tip-text">
                模板字段名称的修改必须通过页面右侧弹窗中完成，在文本中直接修改是不生效的，会被弹窗中的名称覆盖。
            </h2>
            <div class="tip-img">
                <img src="~/img/mark-change-tip.png" alt="" width="388" />
            </div>
            <h2 class="tip-text">经系统检测，部分字段名称的修改操作不符合规范，请核对后重新保存。</h2>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="onUpdateBkContent">我知道了</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { yzBgColorIndex, calcYzHighlightBgndex } from 'src/common/utils/yzEditor.js';

import { FLOAT_TYPES, SELECT_TYPES } from 'src/pages/foundation/sign/common/info/info.js';
import { mapGetters, mapMutations, mapState } from 'vuex';
import { customErrorReport } from 'src/utils/sentryReport';
import pull from 'lodash/pull';

import { postYZDocOpt } from 'src/common/utils/yzEditor.js';

export default {
    props: {
        templateId: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            iframeSrcUrl: '',
            iframeWin: {},
            pageLoading: true,
            showFieldChangeTipDialog: false,
            marksChangedInEditor: [],
            cacheDocIndex: 0,
            loadingText: '',
        };
    },
    computed: {
        ...mapGetters(['getUserId']),
        ...mapState('dynamic', {
            preparingItem: state => state.preparingItem,
            docList: state => state.docList,
            marks: state => state.marks,
            receivers: state => state.receivers,
            termTypeList: state => state.termTypeList,
            activeDocIndex: state => state.activeDocIndex,
            currentPageIndex: state => state.currentPageIndex,
            activeReceiverIndex: state => state.activeReceiverIndex,
            focusItem: state => state.focusItem,
        }),
        currentDocLabels() {
            return this.marks.filter(mark => {
                return this.docList[this.cacheDocIndex].documentId === mark.documentId && mark.type !== 'QR_CODE'; // 数据库中字段
            });
        },
    },
    watch: {
        docList: {
            handler: function(v) {
                if (v.length) {
                    this.initIframeEditor();
                }
            },
        },
        currentPageIndex(v) {
            this.iframeWin.pageJump(v);
        },
        activeDocIndex: {
            async handler(v, ov) {
                // 字段名称校验未通过时，不需要再次同步数据
                if (this.showFieldChangeTipDialog) {
                    return;
                }
                if (await this.syncBkInEditorAndData(false, true)) {
                    this.cancelHighlightMark()
                        .then(() => {
                            this.initIframeEditor(v);
                            this.cacheDocIndex = v;
                        });
                } else {
                    this.setActiveDocIndex(ov);
                }
            },
        },
    },
    methods: {
        ...mapMutations('dynamic', ['resetPreparingItem', 'setFocusItem', 'resetFocusItem', 'setActiveDocIndex']),
        onUpdateBkContent() {
            this.showFieldChangeTipDialog = false;
            this.batchUpdateFieldsInEditor(this.marksChangedInEditor);
            this.marksChangedInEditor = [];
        },
        initIframeEditor(v = 0) {
            this.pageLoading = true;
            this.loadingText = '模板字段及编辑器初始化中，请耐心等待......';
            this.$http.get(`/template-api/dynamic-template/open?documentId=${this.docList[v].documentId}&templateId=${this.templateId}&userId=${this.getUserId}`)
                .then(res => {
                    if (res.data) {
                        window.ssqAuthUserId = this.getUserId; // 提供给永中进行userId的获取，修改时注意同步
                        // 测试环境编辑器服务配置返回带端口
                        this.iframeSrcUrl = !res.data.includes('yz-editor') ? `/yz-editor${res.data.split('8080')[1]}` : `/yz-editor${res.data.split('yz-editor')[1]}`;
                        this.iframeWin = this.$refs.yz_editor.contentWindow;
                    }
                }).catch(() => {
                    this.pageLoading = false;
                    this.$MessageToast.error('文档初始化失败'); // 表格和条款不能重名
                });
        },
        handleMessage(e) {
            try {
                const data = JSON.parse(e.data);
                console.log('data', data);
                if (data.params) {
                    if (data.cmd === 'hasLoaded') { // 编辑器加载完毕
                        this.editorLoadedHandler();
                    } else if (data.cmd === 'Complete') { // 批量更新、取消高亮结束时返回
                        this.pageLoading = false;
                    } else if (data.params.isMark && data.markName) { // 点击书签
                        this.resetPreparingItem();
                        // 1）marks中filter书签名和事件书签名一致的mark,emit focus，弹出字段编辑弹窗
                        this.triggerEdit(data.markName);
                    } else if (this.preparingItem.show) { // 插入字段
                        this.addMark();
                    } else {
                        this.resetFocusItem();
                    }
                    this.resetPreparingItem();
                }
            } catch (err) {
                console.log('message', err);
            }
        },
        editorLoadedHandler() {
            this.initBookmarkWhenOpenPage();
            this.iframeWin.openRevise(0); // 关闭修订模式
            this.iframeWin.addEventListener('keyup', (e) => {
                if (e.keyCode === 8 || e.keyCode === 46) { // 如果是键盘删除操作
                    setTimeout(() => {
                        // 延时至编辑器修正书签位置完成，保证getBookmarksInEditor()数据是准确的
                        // 书签内容通过文本坐标定位，修改文案时会重新计算
                        console.log('editDelete === ', this.getBookmarksInEditor());
                        this.syncBkInEditorAndData(true);
                    }, 400);
                }
            });
        },

        initBookmarkWhenOpenPage() {
            if (!this.iframeWin.deleteBookMark || !this.iframeWin.getBookmark) {
                return;
            }

            const bookmarksInEditor = this.getBookmarksInEditor(); // 获取编辑器中的书签
            const bookmarksInEditorIds = Object.keys(bookmarksInEditor); // key为书签ID，对应label.bookmark

            // 如果存在字段数据，但永中返回书签数据为空则不做处理
            if (!bookmarksInEditorIds.length && this.currentDocLabels.length) {
                return;
            }
            if (!this.currentDocLabels.length && bookmarksInEditorIds.length > 0) {
                return this.deleteBookmarkConfirm(bookmarksInEditor, bookmarksInEditorIds);
            }

            // 获取要移除的书签、label数据、要编辑的label数据
            const {
                bkInEditorIdsCopyForDelete,
                deleteMarkIds,
                batchUpdateInfo,
            } = this.getOptDataOfLabels(bookmarksInEditor);
            const promiseList = [];

            // 删除字段数据：1）当数据库中有数据，并且永中返回的数据不为空（为空时deleteMarkIds就是currentlabels）；2）非最终切文档/效果预览/保存场景，或者删除场景
            if (deleteMarkIds.length && deleteMarkIds.length !== this.currentDocLabels.length) {
                customErrorReport({
                    errName: '自动删除永中编辑器不存在的字段数据',
                    tag: 'yzeditor',
                    tagContent: 'bookmarks',
                    account: this.getUserId,
                    data: {
                        bookmarks: bookmarksInEditor,
                        deleteMarkIds: deleteMarkIds,
                        labels: JSON.stringify(this.currentDocLabels),
                        documentId: this.docList[this.activeDocIndex].documentId,
                    },
                });
                promiseList.push(this.deleteInvaidMarks(deleteMarkIds));
            }
            this.pageLoading = true;
            promiseList.length && Promise.all(promiseList);
            this.batchUpdateFieldsInEditor(batchUpdateInfo); // 高亮当前业务字段

            this.$nextTick().then(() => {
                // 批量高亮会逐个跳转到书签位置，最终要手动滚动到顶部
                // eslint-disable-next-line new-cap
                this.iframeWin.YozoOffice.Application.ActiveDocument.Range(0, 0).Select();
            });
            this.resetFocusItem();
            // 初始化时，提示移除无对应label数据的文档书签
            if (bkInEditorIdsCopyForDelete.length) {
                this.deleteBookmarkConfirm(bookmarksInEditor, bkInEditorIdsCopyForDelete);
            }
        },
        // 移除数据中没有，文档内仍存在的书签
        // removeBKOnlyInEditor(bookmarksInEditorIds) {
        //     const bksTobeRemove = bookmarksInEditorIds.filter((bkId) => {
        //         return !this.currentDocLabels.some(a => {
        //             if (SELECT_TYPES.includes(a.type)) { // 单复选判断选项
        //                 return a.buttons.map(button => button.bookmark).includes(bkId);
        //             } else {
        //                 return a.bookmark === bkId;
        //             }
        //         });
        //     });
        //     this.iframeWin.deleteBookMark(bksTobeRemove.join(','));
        // },
        // 获取要移除的书签、label数据、要编辑的label数据
        getOptDataOfLabels(bookmarksInEditor) {
            const bookmarksInEditorIds = Object.keys(bookmarksInEditor); // key为书签ID，对应label.bookmark
            const bkInEditorIdsCopyForDelete = [...bookmarksInEditorIds]; // 缓存ID，过滤数据中有的书签用于移除
            const editMarks = []; // 单复选项中，选项需要更新的字段数据(是否被移除)
            const deleteMarkIds = []; // 需要删除的字段数据（编辑器中不存在的字段）
            const batchUpdateInfo = []; // 需要高亮的数据
            let hasNameChangedLabel = false; // 标记是否有字段被手动修改
            this.currentDocLabels.forEach((label) => {
                if (SELECT_TYPES.includes(label.type)) { // 单复选用选项做判断
                    const buttonsInEditor = label.buttons.filter(button => {
                        if (!bookmarksInEditorIds.includes(button.bookmark)) {
                            return false;
                        }
                        pull(bkInEditorIdsCopyForDelete, button.bookmark);
                        return true;
                    });
                    if (!buttonsInEditor.length) { // 全部移除则删除label
                        deleteMarkIds.push(label.labelId);
                    } else if (buttonsInEditor.length < label.buttons.length) { // 移除了选项，则编辑label，并高亮
                        const newLabel = {
                            ...label,
                            buttons: [...buttonsInEditor],
                        };
                        editMarks.push(newLabel);
                        batchUpdateInfo.push(newLabel);
                    } else { // 未处理
                        // 删除、更新字段时会自动修正name，只有单独做高亮时才要判断展示文本是否正确
                        label.buttons.forEach(button => {
                            if (`#${label.name}-${button.buttonValue}#` !== bookmarksInEditor[button.bookmark]) {
                                hasNameChangedLabel = true;
                            }
                        });
                        batchUpdateInfo.push(label);
                    }
                } else if (!bookmarksInEditorIds.includes(label.bookmark)) {
                    deleteMarkIds.push(label.labelId);
                } else {
                    if (bookmarksInEditor[label.bookmark] !== `#${label.name}#`) {
                        hasNameChangedLabel = true;
                    }
                    batchUpdateInfo.push(label);
                    pull(bkInEditorIdsCopyForDelete, label.bookmark);
                }
            });
            return {
                bkInEditorIdsCopyForDelete,
                editMarks,
                deleteMarkIds,
                batchUpdateInfo,
                hasNameChangedLabel,
            };
        },
        // deleteOnly：是否是删除时调用匹配, isFinal：是保存文档时的跳转
        syncBkInEditorAndData(deleteOnly, isFinal) {
            return new Promise((resolve) => {
                // 如果获取不到iframe 则取消操作
                if (!this.iframeWin.deleteBookMark || !this.iframeWin.getBookmark) {
                    resolve(true);
                }

                const bookmarksInEditor = this.getBookmarksInEditor(); // 获取编辑器中的书签
                const bookmarksInEditorIds = Object.keys(bookmarksInEditor); // key为书签ID，对应label.bookmark

                // 如果存在字段数据，但永中返回书签数据为空则不做处理
                if (!bookmarksInEditorIds.length && this.currentDocLabels.length) {
                    resolve(true);
                }

                // 获取要移除的书签、label数据、要编辑的label数据
                const {
                    editMarks,
                    deleteMarkIds,
                    hasNameChangedLabel,
                } = this.getOptDataOfLabels(bookmarksInEditor);
                const promiseList = [];

                if (editMarks.length) { // 更新字段数据
                    editMarks.forEach(mark => {
                        promiseList.push(this.saveMark({
                            ...mark,
                        }).then(res => {
                            const markIndex = this.marks.findIndex(a => a.labelId === mark.labelId);
                            const data = res.data.filter(a => a.labelId === mark.labelId);
                            this.$set(this.marks, markIndex, {
                                ...(this.marks[markIndex]),
                                ...data[0],
                            });
                        }));
                    });
                }
                // 删除字段数据：1）当数据库中有数据，并且永中返回的数据不为空（为空时deleteMarkIds就是currentlabels）；2）非最终切文档/效果预览/保存场景，或者删除场景
                if (deleteMarkIds.length && deleteMarkIds.length !== this.currentDocLabels.length && (!isFinal || deleteOnly)) {
                    customErrorReport({
                        errName: '自动删除永中编辑器不存在的字段数据',
                        tag: 'yzeditor',
                        tagContent: 'bookmarks',
                        account: this.getUserId,
                        data: {
                            bookmarks: bookmarksInEditor,
                            deleteMarkIds: deleteMarkIds,
                            labels: JSON.stringify(this.currentDocLabels),
                            documentId: this.docList[this.activeDocIndex].documentId,
                        },
                    });
                    promiseList.push(this.deleteInvaidMarks(deleteMarkIds));
                }
                promiseList.length && Promise.all(promiseList);
                if (hasNameChangedLabel && !deleteOnly) {
                    // 如果编辑器内标签名称发生变化，则弹提示
                    this.showFieldChangeTipDialog = true;
                    resolve(false);
                } else {
                    this.resetFocusItem();
                    resolve(true);
                }
            });
        },
        // 删除文档内的书签确认
        deleteBookmarkConfirm(bookmarksInEditor, ids = [], callback, value) {
            if (!ids.length) {
                return;
            }
            const h = this.$createElement;
            const isMoreThen10 = ids.length > 10;
            const showIds = isMoreThen10 ? ids.slice(0, 10) : ids;
            const message = h('div', null, [
                h('p', null, '以下字段在模板内未保存生效，如需使用请重新添加，若不再使用请点击删除。'),
                h('br'),
                showIds.map(id => h('p', null, `【${bookmarksInEditor[id]}】：${id}`)),
                isMoreThen10 ? h('p', null, `等${ids.length}个书签。`) : null,
                h('br'),
                h('p', null, '（注意：模板保存中点击刷新或关闭页面，可能导致字段保存失败）'),
            ]);
            this.$confirm(message, '提示', {
                confirmButtonText: '确认删除',
                cancelButtonText: '取消',
            }).then(() => {
                customErrorReport({
                    errName: '手动删除文档中非字段书签数据',
                    tag: 'yzeditor',
                    tagContent: 'bookmarks',
                    account: this.getUserId,
                    data: {
                        bookmarks: bookmarksInEditor,
                        deleteBookmarkIds: ids,
                        labels: JSON.stringify(this.currentDocLabels),
                        documentId: this.docList[this.activeDocIndex].documentId,
                    },
                });
                this.iframeWin.deleteBookMark(ids.join(','));
                this.$MessageToast.success('删除成功');
                callback && callback(value);
            }).catch(() => {
                callback && callback(value);
            });
        },
        // 批量删除文档内不存在的业务字段
        deleteInvaidMarks(emptyMarkIds) {
            return this.$http.delete(`/template-api/dynamic-template/${this.templateId}/labels`, {
                data: {
                    labelIds: emptyMarkIds,
                },
            }).then(() => {
                emptyMarkIds.forEach((markId) => {
                    const tempIndex = this.marks.findIndex(a => a.labelId === markId);
                    this.marks.splice(tempIndex, 1);
                });
            });
        },
        // 1) call add api; 2) insert in editor; 3) 弹出编辑边栏
        addMark() {
            const { type, necessary, receiverFill, refBizFieldId, fontSize, buttonValue } = this.preparingItem;
            if (buttonValue) { // 插入单复选项选项时调用编辑的接口
                return this.addSelectOption();
            }
            const name = this.initStaticMarkName(this.preparingItem);
            // 由后端指定特殊ID，将Id作为书签标记，这里更新this.marks，
            this.pageLoading = true;
            this.loadingText = '数据保存中，请勿刷新或关闭页面，以免数据丢失';
            this.saveMark({
                documentId: this.docList[this.activeDocIndex].documentId,
                docI: this.activeDocIndex,
                templateId: this.templateId,
                type,
                receiverFill,
                necessary,
                refBizFieldId,
                roleId: this.receivers[this.activeReceiverIndex].roleId,
                name,
                fontSize,
                termTypeId: type === 'TERM' ? this.termTypeList[0].termTypeId : '',
                ifBold: false,
                ifItalic: false,
                ifUnderline: false,
            }).then(res => {
                this.updateMarks(res.data, this.marks.length);
            }).catch(() => {
                this.resetPreparingItem();
                this.pageLoading = false;
            });
        },
        addSelectOption() {
            const { name, type, necessary, refBizFieldId, fontSize, labelId, buttons } = this.preparingItem;
            const i = labelId ? this.marks.findIndex(mark => mark.labelId === labelId) : this.marks.length;
            const selectItem = labelId ? this.marks[i] : {
                documentId: this.docList[this.activeDocIndex].documentId,
                docI: this.activeDocIndex,
                templateId: this.templateId,
                type,
                receiverFill: false, // 当前只支持发件人填写
                necessary,
                refBizFieldId,
                roleId: this.receivers[this.activeReceiverIndex].roleId,
                name,
                fontSize,
                termTypeId: '',
                ifBold: false,
                ifItalic: false,
                ifUnderline: false,
            };
            this.pageLoading = true;
            this.loadingText = '数据保存中，请勿刷新或关闭页面，以免数据丢失';
            this.saveMark({
                ...selectItem,
                buttons,
            }).then(res => {
                this.updateSelectMarks(res.data, i, selectItem);
                this.resetPreparingItem();
            }).catch(() => {
                this.resetPreparingItem();
                this.pageLoading = true;
            });
        },
        triggerEdit(markId) {
            const markIndex = this.marks.findIndex(a => {
                // 单复选框需要判断选项的bookmarkID
                if (SELECT_TYPES.includes(a.type)) {
                    return a.buttons.some(button => button.bookmark === markId);
                }
                return a.bookmark === markId;
            });
            if (markIndex < 0) {
                return;
            }
            const signerIndex = this.receivers.findIndex(a => a.roleId === this.marks[markIndex].roleId);
            this.setFocusItem({
                markIndex,
                signerIndex,
                buttonBookmark: markId,
                mark: this.marks[markIndex],
            });
        },

        getBookmarksInEditor() {
            if (!this.iframeWin.getBookmark) {
                return {};
            }
            // fix CFD-6768：local bookmark problem，永中同步更新SSQ_Content.js
            const bksObj = this.iframeWin.getBookmark(); // 永中返回的Map
            const result = {};
            bksObj.forEach((value, key) => {
                if (key.includes('SSQ')) {
                    result[key] = value;
                }
            });

            return result;
        },
        getMarkInfo(list, mark) {
            const { type, receiverFill, roleId, name, fontSize } = mark;
            let signerIndex = -1;
            let bgIndex = yzBgColorIndex[0]; // 默认发件方填写
            let newFontSize = fontSize * 72 / 96;

            if (FLOAT_TYPES.includes(type) || receiverFill) {
                signerIndex = list.findIndex(a => a.roleId === roleId);
                newFontSize = receiverFill ? (mark.fontSize || fontSize) * 72 / 96 : ''; // dpi转换
                bgIndex = calcYzHighlightBgndex(signerIndex); // 第一位默认预留给发件方
            }

            // const markName = signerIndex === -1 ? `${name}` : `${name}&${list[signerIndex].showName}`; // 发件方只显示字段名

            return {
                markName: `#${name}#`,
                bgIndex,
                signerIndex,
                fontSize: newFontSize,
            };
        },
        // 批量更新字段 isOpt标记是编辑、新增操作的批量更新，此时对应更新的属性是一致的
        batchUpdateFieldsInEditor(marks, isOpt) {
            if (!marks.length) {
                this.pageLoading = false;
                return;
            }

            this.pageLoading = true;
            const updateObject = {};
            const baseMarkProp = this.getMarkInfo(this.receivers, marks[0]);

            marks.forEach(mark => {
                // 单复选要更新每一项
                if (SELECT_TYPES.includes(mark.type)) {
                    const { bgIndex, fontSize } = this.getMarkInfo(this.receivers, mark);
                    mark.buttons.forEach(button => {
                        updateObject[button.bookmark] = `#${mark.name}-${button.buttonValue}#,${bgIndex},,${fontSize},`;
                    });
                } else {
                    const {
                        markName, bgIndex, fontSize,
                    } = !isOpt ? this.getMarkInfo(this.receivers, mark) : baseMarkProp;
                    const {
                        ifBold,
                        ifItalic, ifUnderline,
                    } = mark;
                    updateObject[mark.bookmark] = `${markName},${bgIndex},,${fontSize},,${ifBold ? 1 : 2},${ifUnderline ? 1 : 2},${ifItalic ? 1 : 2}`;
                }
            });
            console.log('updateObject', updateObject);
            this.iframeWin.batchUpdateBookMark(JSON.stringify(updateObject));
            this.pageLoading = false;
        },
        initStaticMarkName({ type, name }) {
            // 合同条款、动态表格字段名称计算
            if (!['DYNAMIC_TABLE', 'TERM'].includes(type)) {
                return name;
            }
            const bookmarkNames = Object.values(this.getBookmarksInEditor());
            let typeMaxIndex = 0;
            let newName = `${name}1`;
            while (bookmarkNames.includes(`#${newName}#`)) {
                typeMaxIndex++;
                newName = `${name}${typeMaxIndex + 1}`;
            }
            return `${name}${typeMaxIndex + 1}`;
        },
        cancelHighlightMark() {
            if (!this.iframeWin.cancelHighlightColor) {
                return Promise.resolve();
            }
            return new Promise((resolve) => {
                this.iframeWin.cancelHighlightColor();
                resolve();
            });
        },

        // ==================== 数据、Dom处理 ====================
        // 更新标签数据
        updateMarks(data, i) {
            const { markName, bgIndex, signerIndex, fontSize } = this.getMarkInfo(this.receivers, data[0]);
            if (data.length === 1) {
                this.iframeWin.insertBookMark(data[0].bookmark, `${markName},${bgIndex},,${fontSize},`); // 书签名不能以数字开头且不能有特殊字符等
                this.marks.push({
                    ...data[0],
                });
            } else {
                let index = -1;
                data.forEach((updateItem, i) => {
                    index = this.marks.findIndex((a) => a.labelId === updateItem.labelId);
                    if (index === -1) { // 如果是新增的标签
                        // console.log('insert', bgIndex);
                        this.iframeWin.insertBookMark(updateItem.bookmark, `${markName},18,,${fontSize},`); // 插入时不做高亮
                        this.marks.push({
                            ...updateItem,
                        });
                    } else {
                        this.$set(this.marks, index, {
                            ...(this.marks[i] || {}),
                            ...updateItem,
                        });
                    }
                });
                this.$nextTick().then(() => {
                    this.pageLoading = true;
                    this.loadingText = '数据保存中，请勿刷新或关闭页面，以免数据丢失';
                    this.batchUpdateFieldsInEditor(data, true);
                });
            }

            this.pageLoading = false; // 插入完成移除loading
            this.setFocusItem({
                markIndex: i,
                signerIndex,
                buttonBookmark: '',
                mark: this.marks[i],
            });
        },
        updateSelectMarks(data, i, oldMark) {
            const indexInData = !oldMark.labelId ? data.length - 1 : data.findIndex(label => label.labelId === oldMark.labelId);
            const { bgIndex, signerIndex, fontSize } = this.getMarkInfo(this.receivers, data[indexInData]);
            const { name, buttons } = data[indexInData];
            const { bookmark, buttonValue } = buttons[buttons.length - 1];
            this.iframeWin.insertBookMark(bookmark, `#${name}-${buttonValue}#,${bgIndex},,${fontSize},`); // 书签名不能以数字开头且不能有特殊字符等
            this.$set(this.marks, i, {
                ...(this.marks[i] || {}),
                docI: this.activeDocIndex,
                ...data[indexInData],
            });

            data.forEach((updateItem, index) => {
                const indexInMarks = this.marks.findIndex((a) => a.labelId === updateItem.labelId);
                if (indexInMarks !== i) { // 如果是新增的标签
                    this.$set(this.marks, indexInMarks, {
                        ...(this.marks[index] || {}),
                        ...updateItem,
                    });
                }
            });
            // 插入单选项后，更新其他
            this.$nextTick().then(() => {
                this.pageLoading = true;
                this.loadingText = '数据保存中，请勿刷新或关闭页面，以免数据丢失';
                this.batchUpdateFieldsInEditor(data, true);

                this.pageLoading = false; // 插入完成移除loading
                this.setFocusItem({
                    markIndex: i,
                    signerIndex,
                    buttonBookmark: bookmark,
                    mark: this.marks[i],
                });
            });
        },

        // ===================== api请求 =====================
        saveMark(data) {
            return this.$http.post(
                `${tempPath}/dynamic-template/${this.templateId}/labels/create-and-modify`,
                data,
            );
        },
        saveYzDoc() {
            // 永中保存接口，需要在开档状态下调用
            return postYZDocOpt(2, {
                fileId: this.docList[this.activeDocIndex].documentId,
            });
        },
    },
    mounted() {
        this.noticeDlgVisible = localStorage.getItem(`dynamicHighlightColorTip_${this.getUserId}`) !== '1';
        window.addEventListener('message', this.handleMessage);
        this.$on('hook:beforeDestroy', () => {
            window.removeEventListener('message', this.handleMessage);
        });
    },
    beforeDestroy() {
        // 永中关档接口
        postYZDocOpt(3, {
            fileId: this.docList[this.activeDocIndex].documentId,
        }); // 在多人编辑时，关档会导致其他编辑状态被关闭
        this.pageLoading = true;
    },
};
</script>

<style lang="scss">
.dynamic-field-doc {
    height: 100%;
    min-height: 600px;
    // remove to avoid float position problem
    /*overflow: auto;*/
    margin: 0px 249px 0;
    border-top: 1px solid $border-color;
    background-color: #f6f6f6;
}
.marks-change-tip-dialog {
    width: 520px;
    height: 450px;
    box-shadow: 0 0 8px 0 rgba(142,142,142,0.50);
    border-radius: 8px;
    .el-dialog__header {
        .el-dialog__title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }
    }
    .el-dialog__body {
        font-size: 12px;
        padding: 10px 33px;
        .tip-text {
            font-size: 14px;
        }
        .tip-img {
            padding: 15px;
        }
    }
    .el-dialog__footer {
        padding-right: 33px;
        text-align: center;
        .el-button {
            height: 30px;
            line-height: 30px;
            padding: 0 10px;
            margin-left: 20px;
        }
    }
}
    iframe.yz-iframe {
        border: none;
        margin-top: -30px;
    }

</style>
