<!-- 指定位置页重构 -->
<template>
    <div class="temp-field-page" v-loading.fullscreen.lock="loading">
        <!-- 页头 -->
        <SignHeader class="header"
            :title="contractTitle"
            rText="保存"
            :rShow="true"
            @to-back="toBack"
            @to-next="toNext"
        >
            <span slot="subTitle" v-show="preparingItem.show">{{ preparingItem.showName }}插入中...</span>
            <template slot="otherBtn">
                <el-button class="preview-btn" type="primary" @click="validateFieldAndJump">效果预览</el-button>
                <el-popover
                    v-model="showHelpPoper"
                    placement="left"
                    width="240"
                    trigger="manual"
                >
                    <div class="help-poper-content">
                        <p>动态模板已全面升级，如需了解更多使用小技巧，请点击使用帮助查看。</p>
                        <i class="el-icon-ssq--bs-guanbi cur-pointer"
                            @click="showHelpPoper=false"
                        ></i>
                    </div>
                    <div slot="reference" class="help-btn" @click="showHelpSlider = true">
                        使用帮助
                    </div>
                </el-popover>
            </template>

        </SignHeader>

        <!-- 主体 -->
        <div ref="fieldDynamicBody" class="temp-dynamic-field">
            <!-- 签约方和字段区域 -->
            <div class="site-content clear">

                <!-- 选择签约方 & 字段仓库 -->
                <FieldSite class="FieldSite fl"
                    :receivers="receivers"
                    :termTypeList="termTypeList"
                    @jumpToDecorate="validateFieldAndJump"
                >
                </FieldSite>

                <!-- 编辑器 -->
                <DynamicDoc
                    ref="dynamicDoc"
                    :templateId="templateId"
                    :termTypeList="termTypeList"
                ></DynamicDoc>

                <!-- 缩略图区 -->
                <section class="FieldMiniDoc">
                    <DynamicFieldMiniDoc></DynamicFieldMiniDoc>
                </section>

                <!-- 编辑区 -->
                <DynamicFieldEdit class="dynamic-field-edit"
                    :receivers="receivers"
                    :termTypeList="termTypeList"
                    @change-signer="onSignerChange"
                    @save-meta="onMetaSave"
                >
                </DynamicFieldEdit>
                <SlidePopModel v-show="showHelpSlider" :outsideShow.sync="showHelpSlider" class="help-slider-pop">
                    <HelpSlider slot="slide-pop-content" class="help-slider"></HelpSlider>
                </SlidePopModel>
            </div>
        </div>

        <!-- 页脚 -->
        <RegisterFooter class="footer" v-if="ftVisible"></RegisterFooter>

        <!-- 新手指引 -->
        <Guide :guideUserType="guideUserType"></Guide>
    </div>
</template>

<script>
import FieldSite from './temp_dynamic_site/TempDynamicSite.vue';
import DynamicFieldMiniDoc from './dynamic_field_minidoc/DynamicFieldMiniDoc.vue';
import DynamicFieldEdit from './dynamic_field_edit/DynamicFieldEdit.vue';
import DynamicDoc from './temp_dynamic_doc/TempDynamicDoc.vue';
import SignHeader from '../../sign/common/signHeader/SignHeader.vue'; // 复用普通发起的header
import RegisterFooter from 'src/components/register_footer/RegisterFooter.vue';
import Guide from '../temp_field/temp_field_guide/TempFeildGuide.vue';
import { goReturnUrl } from 'src/common/utils/returnUrl.js';

import { mapGetters, mapMutations, mapState } from 'vuex';
import HelpSlider from './helpSlider/HelpSlider';
import SlidePopModel from 'components/slide_pop/SlidePopModel.vue';
import store from '@/store/store';
import { get } from 'lodash';

export default {
    components: {
        HelpSlider,
        SignHeader,
        SlidePopModel,
        RegisterFooter,
        Guide,
        FieldSite,
        DynamicFieldMiniDoc,
        DynamicFieldEdit,
        DynamicDoc,
    },
    data() {
        return {
            // 合同信息
            contractTitle: '',
            returnUrl: ['null', ''].includes(this.$route.query.returnUrl) ? null : this.$route.query.returnUrl,

            templateId: this.$route.query.templateId,
            // 页面loading
            loading: true,

            // guide
            guideUserType: 'PERSON',

            currentDocPageIndex: 1,
            currentDocIndex: 0,
            showHelpSlider: false,
            showHelpPoper: false,
        };
    },
    computed: {
        ...mapState('dynamic', {
            preparingItem: state => state.preparingItem,
            docList: state => state.docList,
            marks: state => state.marks,
            receivers: state => state.receivers,
            termTypeList: state => state.termTypeList,
            activeReceiverIndex: state => state.activeReceiverIndex,
            focusItem: state => state.focusItem,
        }),
        ...mapGetters(['getSsoConfig', 'getUserId', 'checkFeat']),
        ftVisible() {
            return !this.ssoTmpEdit.tmpEdit_ft || this.ssoTmpEdit.tmpEdit_ft.visible;
        },
        // 单点登录模板编辑页配置
        ssoTmpEdit() {
            return this.getSsoConfig.tmpEdit || {};
        },
    },
    methods: {
        ...mapMutations('dynamic', ['setDocListData', 'setDocMarks', 'setReceiversData', 'setTermTypeList', 'resetDynamicInfo', 'setTableCellWidthType']),
        async toBack() {
            const result = await this.deleteEmptyMark();
            if (result) {
                const loading = this.$loading({
                    text: '数据保存中，请勿刷新或关闭页面，以免数据丢失',
                });
                setTimeout(() => {
                    loading.close();
                    this.$router.push(`/template/edit/prepare?templateId=${this.templateId}&dynamicMark=DYNAMIC_YOZO`);
                }, 2000);
            }
        },
        async toNext() {
            const loading = this.$loading({
                text: '数据保存中，请勿刷新或关闭页面，以免数据丢失',
            });
            const result = await this.deleteEmptyMark(); // 先删除文档内的空书签
            if (!result) {
                loading.close();
                return;
            }

            // 每个单复选框至少需要两个选项
            const marksOptionsNotEnough = this.marks.filter(mark => mark.type === 'SINGLE_BOX' && mark.buttons.length < 2);
            if (marksOptionsNotEnough.length > 0) {
                this.$confirm(
                    `单复选框 ${marksOptionsNotEnough.map(mark => mark.name).join('、')} 备选项不足两项，请补充`,
                    '提示',
                    {
                        confirmButtonText: '知道了', // 确定
                        showCancelButton: false,
                    },
                );
                loading.close();
                return;
            }
            const allDone = this.receivers.every(receiver => {
                return this.marks.some(mark => {
                    return (mark.roleId === receiver.roleId) && (mark.type === 'SEAL' || mark.type === 'SIGNATURE');
                });
            });
            const someTextNoName = this.marks.some(item => {
                return item.type === 'TEXT' && !item.name;
            });

            if (!allDone) {
                loading.close();
                this.$MessageToast.error('请为每个签署方指定签署位置');
                return;
            }

            if (someTextNoName) {
                loading.close();
                this.$MessageToast.error('请为每个文本标签填写名称');
                return;
            }

            // 保存成功直接去模板列表页
            this.$refs.dynamicDoc.cancelHighlightMark();
            setTimeout(async() => { // hold for cancel highlight
                // 永中关档接口，保存时，后端会做书签与字段的对比
                // await postYZDocOpt(3, {
                //     fileId: this.docList[this.currentDocIndex].documentId
                // });
                await this.putSaveTemplate()
                    .then(() => {
                        this.$MessageToast.success('保存成功').then(() => {
                            /* 如果客户定义了跳转地址，则首先跳转 */
                            if (this.returnUrl) {
                                goReturnUrl(this.returnUrl);
                                return;
                            }
                            if (this.ssoTmpEdit.tmpEdit_1_save && this.ssoTmpEdit.tmpEdit_1_save.url) {
                                this.$router.push(this.ssoTmpEdit.tmpEdit_1_save.url);
                            } else {
                                if (store.state.commonHeaderInfo.openNewContractTemplate && store.getters.checkFeat.dynamicTemplate) {
                                    this.$router.push('/template/list?isDynamic=1');
                                } else {
                                    this.$router.push('/template/list');
                                }
                            }
                        });
                    }).catch(() => {
                        loading.close();
                    });
            }, 3000);
        },
        async deleteEmptyMark() {
            if (!this.$refs.dynamicDoc || !this.$refs.dynamicDoc.iframeWin) {
                return true;
            }
            // 文档退出时先调编辑器保存，避免转pdf时获取的word数据不是实时数据
            const { data } = await this.$refs.dynamicDoc.saveYzDoc();
            if (data.errorCode !== '0') {
                return false;
            }
            return await this.$refs.dynamicDoc.syncBkInEditorAndData(false, true);
        },
        async validateFieldAndJump() {
            const result = await this.deleteEmptyMark();
            setTimeout(() => {
                result && this.$router.push(`/template/edit/decoration?templateId=${this.$route.query.templateId}`);
            }, 500);
        },
        // ajax
        getContractInfo() {
            return this.$http.get(`${tempPath}/templates/${this.templateId}?type=send`);
        },
        // 保存
        putSaveTemplate() {
            return this.$http.put(`${tempPath}/dynamic-template/${this.templateId}/save`);
        },
        // 编辑， 同一签名 改变签署人
        onSignerChange(i) {
            // 改变对应mark的roleId即可
            this.$set(this.focusItem, 'signerIndex', i);
            const { mark, markIndex } = this.focusItem;
            const oriRoleId = mark.roleId;
            mark.roleId = this.receivers[i].roleId;
            this.saveMark({
                labelId: mark.labelId,
                roleId: this.receivers[i].roleId,
            })
                .then(() => {
                    const markObject = {
                        ...this.marks[markIndex],
                        roleId: this.receivers[i].roleId,
                    };
                    this.$set(this.marks, markIndex, markObject);
                    this.$refs.dynamicDoc.batchUpdateFieldsInEditor([markObject]);
                })
                .catch(() => {
                    mark.roleId = oriRoleId;
                });
        },
        // 编辑区保存标签修改
        onMetaSave(i, metaData) {
            const mark = this.marks[i];
            const isSenderOnly = mark.type === 'DYNAMIC_TABLE' || mark.type === 'TERM';
            if (Object.keys(metaData)[1] === 'name' && isSenderOnly) {
                const newName = metaData[Object.keys(metaData)[1]];
                const bookmarkIds = Object.keys(this.$refs.dynamicDoc.getBookmarksInEditor());
                // 同名同类型并且仍在编辑器里
                const sameNameMarks = this.marks.filter(a => (a.type === mark.type && a.name === newName && bookmarkIds.includes(a.bookmark)));
                const maxNum = mark.name === newName ? 1 : 0; // 实际应该不存在为1的情况，等于表示名称未变，会被上层拦截掉
                if (sameNameMarks.length > maxNum) {
                    this.$MessageToast.error('已存在同名字段，请修改名称'); // 表格和条款不能重名
                    return;
                }
            }
            this.$refs.dynamicDoc.pageLoading = true;
            this.saveMark({
                labelId: mark.labelId,
                ...metaData,
            })
                .then(res => {
                    const { fontSize, receiverFill, roleId, necessary, wordNum, name } = res.data[0];
                    this.marks.forEach((a, index) => { // 更新同名字段
                        if (a.name === name || a.labelId === mark.labelId) { // marks中name为修改前的数据，需要用labelId来做修正
                            this.$set(this.marks, index, {
                                ...this.marks[index],
                                fontSize,
                                receiverFill,
                                roleId,
                                necessary,
                                wordNum: wordNum || 5,
                                [Object.keys(metaData)[1]]: metaData[Object.keys(metaData)[1]],
                            });
                        }
                    });
                    // update bookmark text in editor, cooperator other labels with same name
                    !(isSenderOnly && Object.keys(metaData)[1] !== 'name') && this.$refs.dynamicDoc.batchUpdateFieldsInEditor(res.data, true);
                    // 更新focus数据
                    this.$set(this.focusItem, 'mark', this.marks[this.focusItem.markIndex]);
                }).finally(() => {
                    this.$refs.dynamicDoc.pageLoading = false;
                });
        },
        saveMark(data) {
            return this.postMarks(data);
        },

        // ajax
        getRecivers() {
            return this.$http.get(`${tempPath}/templates/${this.templateId}/roles?isOnlyNeedSigner=1&displayRuleName=true`); // isOnlyNeedSigner不要抄送人，displayRuleName按规则格式化接收人名称
        },

        getDocs() {
            return this.$http.get(`${tempPath}/dynamic-template/${this.templateId}/documents`); // showLabels 取标签信息
        },

        getTermTypeList() {
            return this.$http.get(`${tempPath}/templates/terms/search?termType=`);
        },

        postMarks(labelObj = {}, opts = {}) {
            return this.$http.post(
                `${tempPath}/dynamic-template/${this.templateId}/labels/create-and-modify/`,
                labelObj,
                opts,
            );
        },
        // 初始化收件人数据
        initReceiversData(data) {
            return data.map((item) => {
                // 后端做了判断，直接取值
                const labelName = item.userName;
                return {
                    ...item,
                    labelName,
                };
            });
        },
        // 初始化标签数据
        initMarksData() {
            const labels = [];
            this.docList.forEach((doc, docI) => {
                doc.labels.forEach((mark) => {
                    labels.push({
                        ...mark,
                        docI,
                        documentId: doc.documentId,
                    });
                });
            });
            return labels;
        },
    },
    created() {
        // 判断混合云环境，动态模版不支持混合云
        if (this.$store.getters.getHybridUserType === 'hybrid') {
            this.$router.push('/account-center/home');
        }
        // 获取合同信息
        this.getContractInfo()
            .then((resp) => {
                this.contractTitle = resp.data.templateName;
            }).catch(() => {
                this.$router.push('/template/list');
            });

        // 获取条款类型数据，单独查询，避免接口报错影响文件数据
        this.getTermTypeList().then(res => {
            this.setTermTypeList(res.data || []);
        });

        // 获取所有文件和收件人信息并展示
        Promise.all([
            this.getRecivers(),
            this.getDocs(),
        ]).then(res => {
            this.showHelpPoper = localStorage.getItem(`dynamicHelpPoper_${this.getUserId}`) !== '1';
            if (this.showHelpPoper) {
                localStorage.setItem(`dynamicHelpPoper_${this.getUserId}`, 1);
            }
            const res0Data = res[0].data;
            const res1Data = res[1].data;
            // 初始化数据
            this.setReceiversData(this.initReceiversData(res0Data));
            this.setDocListData(res1Data);
            this.setDocMarks(this.initMarksData());
            this.setTableCellWidthType(get(res1Data[0], 'docExtendedAttribute.selfAdapting', 0) || 0);
            this.resetDynamicInfo();
            this.$nextTick(() => {
                this.guideUserType = this.receivers[0].userType;
            });
        }).finally(() => {
            this.loading = false;
        });
    },
    beforeDestroy() {
        // 离开文档编辑界面时，取消字段高亮
        this.$refs.dynamicDoc && this.$refs.dynamicDoc.cancelHighlightMark();
    },
};
</script>

<style lang="scss">
.temp-field-page {
    user-select: none;
    position: relative;
    height: 100vh;
    font-size: 12px;
    color: #333;
    background-color: #f6f6f6;
    .header {
        z-index: 100;
        position: fixed;
        top: 0;
        span:nth-child(2) {
            font-size: 12px;
            color: #20a0ff;
        }
    }
    .preview-btn {
        height: 30px;
        line-height: 4px;
        vertical-align: bottom;
        background: #127fd2;
        border: none;
        margin-right: 10px;
    }
    .help-btn {
        display: inline-block;
        height: 20px;
        width: 80px;
        font-size: 14px;
        font-weight: 400;
        color: #47AFFF;
        cursor: pointer;
    }
    .footer {
        position: fixed;
        bottom: 0;
    }
}
.field-error-alert {
    p {
        white-space: pre-wrap;
        word-wrap: break-word;
    }
}
.temp-dynamic-field {
    position: absolute;
    top: 50px;
    bottom: 35px;
    width: 100%;
    overflow: hidden;
    .scale {
        z-index: 100;
        position: fixed;
        top: 50px;
        width: 100%;
        height: 40px;
        line-height: 40px;
        text-align: center;
        background-color: #f6f6f6;
        padding-right: 210px;
        border-bottom: 1px solid $border-color;
        .scale-opt {
            width: 100px;

            .el-input__icon { // el
                color: #333;
            }
            input { // el
                font-size: 12px;
                background-color: #f6f6f6;
                border: 0;
                border-radius: 0;
            }
        }

        .analyzeTip{
            position: absolute;
            top: 0;
            left: 20px;
            font-size: 12px;
            color: #43adfd;

            .el-icon-loading{
                margin-right: 5px;
                font-size: 20px;
                color: #0092ff;
            }

            i, span{
                vertical-align: middle;
            }
        }
    }

    .site-content {
        height: 100%;
        .FieldSite {
            position: relative;
            width: 210px;
            height: 100%;
            border-right: 1px solid $border-color;
        }
        .FieldMiniDoc {
            position: absolute;
            top: 0;
            right: 0;
            width: 210px;
            height: 100%;
            border-left: 1px solid #ddd;
            overflow-y: auto;
            .temp-field-minidoc-cpn {
                border-left: none;
            }
        }
        .dynamic-field-edit {
            position: absolute;
            top: 1px;
            right: 0;
            width: 211px;
            height: 100%;
        }
        .help-slider-pop .slide-pop-model {
            top: 50px;
            width: 600px;
            .slide-pop-close {
                top: 10px;
            }
        }
    }

    .flying-icon {
        z-index: 9999;
        pointer-events: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 28px;
        height: 28px;
        line-height: 27px;
        text-align: center;
        font-size: 18px;
        background-color: #fff;
        border-radius: 4px;
        box-shadow:1px 1px 13px #ccc, -1px 1px 13px #ccc;
    }
}

.help-poper-content {
    color: #333;
    p {
        display: inline-block;
        width: 220px;
        line-height: 20px;
    }
    i {
        position: relative;
        top: -23px;
        right: -5px;
    }
}

.scale-opts-el-select {
    .el-select-dropdown__wrap {
        max-height: 285px;
    }
}
</style>
