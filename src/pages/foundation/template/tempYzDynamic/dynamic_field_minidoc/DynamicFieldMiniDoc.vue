<template>
    <div class="dynamic-field-minidoc-cpn">

        <!-- 缩略图区 -->
        <div class="title">
            {{ title }}
            <span v-if="false">编辑</span>
        </div>

        <div class="doc-edit-view">
            <ul>
                <li class="doc"
                    :data-docId="doc.documentId"
                    v-for="(doc, docIndex) in docList"
                    :key="doc.documentId"
                >
                    <div :class="`doc-title ${activeDocIndex === docIndex ? 'active' : ''}`" @click="switchDoc(docIndex)">
                        {{ doc.fileName }}
                    </div>
                    <div class="doc-totalPage">
                        页数：{{ doc.pageSize }}页
                        <div class="page-switch" v-if="activeDocIndex === docIndex">
                            跳转至：第
                            <el-select
                                size="mini"
                                v-model="pageIndex"
                                placeholder="请选择"
                                @change="handlePageSwitch"
                            >
                                <el-option
                                    v-for="item in doc.pageSize"
                                    :key="item"
                                    :value="item"
                                >
                                    {{ item }}
                                </el-option>
                            </el-select>
                            页
                        </div>
                    </div>

                </li>
            </ul>
        </div>
    </div>
</template>
<script>
import { mapMutations, mapState } from 'vuex';

export default {
    props: {
        'title': {
            type: String,
            default: '文档',
        },
    },
    data() {
        return {
            pageIndex: 1,
        };
    },
    computed: {
        ...mapState('dynamic', {
            docList: state => state.docList,
            marks: state => state.marks,
            activeDocIndex: state => state.activeDocIndex,
        }),
    },
    watch: {
        activeDocIndex() {
            this.pageIndex = 1;
        },
    },
    methods: {
        ...mapMutations('dynamic', ['setActiveDocIndex', 'setCurrentPageIndex']),
        handlePageSwitch() {
            this.setCurrentPageIndex(this.pageIndex);
        },
        switchDoc(index) {
            const marksOptionsNotEnough = this.marks.filter(mark => (mark.type === 'SINGLE_BOX' && mark.documentId === this.docList[this.activeDocIndex].documentId && mark.buttons.length < 2));
            // 存在待补充选项的单选框，则不切换
            if (marksOptionsNotEnough.length > 0) {
                this.$confirm(
                    `单复选框 ${marksOptionsNotEnough.map(mark => mark.name).join('、')} 备选项不足两项，请补充`,
                    '提示',
                    {
                        confirmButtonText: '知道了', // 确定
                        showCancelButton: false,
                    },
                );
                return;
            }
            this.setActiveDocIndex(index);
        },
    },
};
</script>
<style lang="scss">
.dynamic-field-minidoc-cpn {
    border-left: 1px solid $border-color;
    overflow-y: auto;

    // 缩略图区
    .title {
        height: 38px;
        line-height: 38px;
        font-size: 14px;
        font-weight: bold;
        padding-left: 20px;
        padding-right: 15px;
        span {
            float: right;
            font-size: 12px;
            color: #3B86D4;
            &:hover {
                cursor: pointer;
            }
        }
    }

    .doc-edit-view {
        border-bottom: 1px solid $border-color;
        .doc {
            border-top: 1px solid $border-color;
            .text-blue{
                color: #108EE8;
            }
            &.no-bottom{
                border-top:0;
            }
        }
        .doc-title {
            position: relative;
            height: 18px;
            line-height: 18px;
            color: #333;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            cursor: pointer;
            padding: 6px 30px 6px 19px;
            // border-top: 1px solid $border-color;
            &.active, &:hover {
                color: #3B86D4;
            }
            /*&:after {*/
                /*position: absolute;*/
                /*top: 9px;*/
                /*right: 24px;*/
                /*content: "";*/
                /*border-left: 1px solid #333;*/
                /*border-top: 1px solid #333;*/
                /*padding: 3px;*/
                /*display: inline-block;*/
                /*transform: rotate(225deg);*/
            /*}*/
        }
        .doc-totalPage {
            line-height: 18px;
            color: #333333;
            padding: 0 0 10px 39px;
            .page-switch {
                display: block;
                .el-select {
                    width: 60px;
                }
            }
        }
    }
}
</style>
