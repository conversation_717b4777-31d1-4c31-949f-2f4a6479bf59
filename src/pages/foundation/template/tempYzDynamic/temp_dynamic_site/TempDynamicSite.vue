<template>
    <div class="temp-field-site-cpn dynamic-yz" v-if="receivers.length">

        <!-- 选择收件人菜单 -->
        <div class="site-bar-head">
            <span class="order-num">第一步：</span>
            <span class="head-title">选择签约方</span>
        </div>
        <div class="signers">
            <ul>
                <el-tooltip class="item"
                    effect="dark"
                    v-for="(item, index) in receivers"
                    :key="item.userAccount"
                    :content="item.showName"
                    popper-class="field-site-popper"
                    placement="right"
                >
                    <li @click="switchReceiver(index)">
                        <span class="signers-color" :style="computeBgc(index)"></span>
                        <span class="signers-name etc-sigle">{{ item.showName }}</span>
                        <i class="signers-active el-icon-ssq-qianyuewancheng" v-show="index == receiverIndex"></i>
                    </li>
                </el-tooltip>
            </ul>
        </div>

        <!-- 字段 -->
        <div class="site-bar-head">
            <span class="order-num">第二步：</span>
            <span class="head-title">插入签署位置</span>
        </div>

        <div class="site-bar-body">
            <div class="site-bar-title">签署字段</div>
            <div class="site-bar-content">
                <ul>
                    <li v-for="item in signIcons"
                        :key="item.class"
                        v-show="item.show"
                        @mouseup="onInsertStart(item.type, item.name, true)"
                    >
                        <i :class="item.class" :style="computeBgc(receiverIndex)"></i>
                        <span>{{ item.name }}</span>
                    </li>
                </ul>
            </div>
            <!-- 编辑模板的时候，展示签署字段外的其他字段 -->
            <!-- 内容字段是否展示 -->
            <div class="site-bar-title">
                临时字段
                <i class="el-icon-ssq-wenhao" @click="dialogTempMetaDesc.show = true"></i>
            </div>
            <div class="site-bar-content">
                <ul>
                    <li v-for="item in contentIcons"
                        :key="item.class"
                        @mousedown="handleTermTip(item)"
                        @mouseup="onInsertStart(item.type, item.name, false, 14, item.type !== 'DYNAMIC_TABLE' && item.type !== 'TERM')"
                    >
                        <i :class="item.class"></i>
                        <span>{{ item.name }}</span>
                    </li>
                </ul>
            </div>

            <!-- 业务字段是否显示 -->
            <div class="site-bar-title metaLabel">
                业务字段
                <i class="el-icon-ssq-wenhao" @click="dialogMetaDesc.show = true"></i>

                <el-tooltip class="item"
                    effect="dark"
                    content="添加业务字段"
                    placement="top"
                >
                    <i class="add-metaLabel el-icon-ssq-jia"
                        v-if="couldManageBizField && (bizFields.length < 500)"
                        @click="handleAddBizField"
                    ></i>
                </el-tooltip>

                <el-tooltip class="item"
                    effect="dark"
                    content="管理业务字段"
                    placement="top"
                    v-if="couldManageBizField"
                >
                    <a href="/console/enterprise/contract/bizField" class="toManageLink" target="_blank">
                        <i class="el-icon-ssq-shezhi"></i>
                    </a>
                </el-tooltip>
            </div>
            <div class="site-bar-content metaLabel-content biz-metaLabel-content"
                v-show="bizFields.length"
            >
                <div class="site-bar-bizname-search">
                    <el-input placeholder="输入需查找的业务字段名称" icon="search" size="small" v-model="bizNameSearchValue" @change="handleChange"></el-input>
                </div>
                <ul class="metaLabel-content-ul">
                    <li
                        v-for="item in filterBizFields"
                        :key="item.fieldId"
                        :class="getFieldItemClass(item)"
                        @mouseup="onInsertStart(item.labelDataType, item.name, item.receiverFill, item.fontSize, item.necessary, item.labelMetaId)"
                    >
                        <i :class="`${fieldIconMap[item.labelDataType]}`"></i>

                        <span
                            v-if="item.name.length < 10"
                            class="metaLabel-name"
                        >
                            {{ item.name }}
                        </span>
                        <el-tooltip
                            v-else
                            class="item"
                            effect="dark"
                            :content="item.name"
                            placement="bottom"
                            popper-class="temp-field-site-cpn__metaLabel-name-popper"
                        >
                            <span class="metaLabel-name">
                                {{ item.name }}
                            </span>
                        </el-tooltip>
                        <span class="metaLabel-operate-block" @mouseup.prevent.stop v-if="couldManageBizField">
                            <i class="metaLabel-operate-icon el-icon-ssq-gengduo"></i>

                            <ul class="metaLabel-hidden-operate">
                                <li @mousedown.prevent.stop="handleEditBizField(item)">编辑</li>
                            </ul>
                        </span>
                        <template v-if="selectTypes.includes(item.labelDataType)">
                            <span
                                v-for="buttonItem in item.buttons"
                                :key="buttonItem"
                                :class="getSelectOptionsClass(item, buttonItem)"
                                @click="onInsertStart(item.labelDataType, item.name, item.receiverFill, item.fontSize, item.necessary, item.labelMetaId, buttonItem)"
                            >
                                <i :class="`${fieldIconMap[item.labelDataType + '_ITEM']}`"></i>
                                <span
                                    v-if="buttonItem.length < 6"
                                    class="metaLabel-name"
                                >
                                    {{ buttonItem }}
                                </span>
                                <el-tooltip
                                    v-else
                                    class="item"
                                    effect="dark"
                                    :content="buttonItem"
                                    placement="bottom"
                                    popper-class="temp-field-site-cpn__metaLabel-name-popper"
                                >
                                    <span class="metaLabel-name">
                                        {{ buttonItem }}
                                    </span>
                                </el-tooltip>
                            </span>
                        </template>
                    </li>
                </ul>
            </div>

            <!-- 合同装饰 -->
            <div class="site-bar-decoration" v-if="checkFeat.contractDecoration">
                <div class="decoration-title" @click="toDerorationPage">进入合同装饰</div>
                <div class="decoration-toast">提示：水印和骑缝章去合同装饰</div>
            </div>
        </div>

        <!-- ================================== 华丽的分割线 ====================================== -->

        <div class="box-sizing-dialog">
            <!-- 临时字段的概念描述 -->
            <el-dialog class="el-dialog-bg dialog-tempMeta-desc"
                title="什么是临时字段？"
                :visible.sync="dialogTempMetaDesc.show"
                size="tiny"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
            >
                <p>临时字段可用于设置模板变量，设置后只在该模板生效，无法在其他模板重复使用。</p>
                <p>发起模板后在临时字段内填入的合同内容暂时不支持搜索。</p>
                <div slot="footer" class="dialog-footer">
                    <el-button type="primary" @click="dialogTempMetaDesc.show = false">知道了</el-button>
                </div>
            </el-dialog>

            <!-- 业务字段的概念描述 -->
            <el-dialog class="el-dialog-bg dialog-meta-desc"
                title="什么是业务字段？"
                :visible.sync="dialogMetaDesc.show"
                size="tiny"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
            >
                <p>业务字段可用于设置模板变量，设置后企业成员均可在设置模板时重复使用。</p>
                <p>发起模板后在业务字段内填入的合同内容可搭配合同管理的“列表配置”功能支持查看和搜索。</p>
                <div slot="footer" class="dialog-footer">
                    <el-button type="primary" @click="dialogMetaDesc.show = false">知道了</el-button>
                </div>
            </el-dialog>

            <!-- 创建、编辑业务字段 -->
            <EditBizFieldDialog
                v-if="dialogBizField.show"
                :curRow="dialogBizField.curField"
                @update="getValidBizFields"
                @close="handleCloseBizDialog"
            >
            </EditBizFieldDialog>
        </div>

    </div>
</template>
<script>
    // 复用sign colorInfo
import { calcRgbColorByIndex } from 'src/common/utils/yzEditor.js';
import { FLOAT_TYPES, SELECT_TYPES } from 'src/pages/foundation/sign/common/info/info.js';
import EditBizFieldDialog from 'src/pages/enterprise/console/contract/bizField/DialogCreate.vue';
import { mapState, mapGetters, mapMutations } from 'vuex';

export default {
    components: {
        EditBizFieldDialog,
    },
    props: {
        receivers: {
            type: Array,
            default: () => ([]),
        },
        termTypeList: {
            type: Array,
            default: () => ([]),
        },
    },
    data() {
        return {
            receiverIndex: 0,
            bizFields: [],
            dialogTempMetaDesc: {
                show: false,
            },
            dialogMetaDesc: {
                show: false,
            },
            dialogBizField: {
                show: false,
                curField: null,
            },
            bizNameSearchValue: '', // 搜索业务字段的输入内容
            filterBizFields: [], // 搜索过滤后的业务字段数组
            fieldIconMap: {
                'BIZ_DATE': 'el-icon-ssq-riqi',
                'TEXT': 'el-icon-ssq-wenben',
                'SINGLE_BOX': 'el-icon-ssq-wenjianjia',
                'MULTIPLE_BOX': 'el-icon-ssq-wenjianjia',
                'SINGLE_BOX_ITEM': 'el-icon-ssq-danxuananniu',
                'MULTIPLE_BOX_ITEM': 'el-icon-ssq-fuxuankuang',
            },
            selectTypes: SELECT_TYPES,
        };
    },
    computed: {
        ...mapState(['commonHeaderInfo']),
        ...mapState('dynamic', {
            marks: state => state.marks,
            docList: state => state.docList,
            activeDocIndex: state => state.activeDocIndex,
        }),
        ...mapGetters(['getUserPermissons', 'checkFeat']),
        signIcons() {
            const userType = this.receivers[this.receiverIndex].userType;
            return [
                {
                    class: 'el-icon-ssq-gongzhang',
                    type: 'SEAL',
                    name: '盖章',
                    show: userType === 'ENTERPRISE',
                },
                {
                    class: 'el-icon-ssq-bi',
                    type: 'SIGNATURE',
                    name: '签名',
                    show: userType === 'PERSON',
                },
                {
                    class: 'el-icon-ssq-riqi',
                    type: 'DATE',
                    name: '签署日期',
                    show: true,
                },
            ];
        },
        contentIcons() {
            return [
                {
                    class: 'el-icon-ssq-wenben',
                    type: 'TEXT',
                    name: '文本',
                }, {
                    class: 'el-icon-ssq-table',
                    type: 'DYNAMIC_TABLE',
                    name: '动态表格',
                }, {
                    class: 'el-icon-ssq-tiaokuantubiao',
                    type: 'TERM',
                    name: '合同条款',
                },
            ];
        },
        // 是否有管理业务字段权限
        couldManageBizField() {
            // 集团垂直管控子企业不具有业务管理权限
            return this.getUserPermissons.sign_m && !(this.commonHeaderInfo.groupVersion === 2 && this.commonHeaderInfo.hasGroupConsole === false);
        },
    },
    methods: {
        ...mapMutations('dynamic', ['setPreparingItem', 'setActiveReceiverIndex']),
        getInsertedSelectItem({ name, labelDataType }) {
            return this.marks.find(mark => mark.name === name && mark.type === labelDataType && mark.documentId === this.docList[this.activeDocIndex].documentId) || {};
        },
        getFieldItemClass(item) {
            if (!SELECT_TYPES.includes(item.labelDataType)) {
                return '';
            }
            let classStr = 'select-group';
            const mark = this.getInsertedSelectItem(item);
            if (mark.buttons && mark.buttons.length === item.buttons.length) {
                classStr += ' disabled';
            }
            return classStr;
        },
        getSelectOptionsClass(item, buttonValue) {
            let classStr = 'field-select-item';
            const mark = this.getInsertedSelectItem(item);
            if (mark.buttons && mark.buttons.map(button => button.buttonValue).includes(buttonValue)) {
                classStr += ' disabled';
            }
            return classStr;
        },
        handleChange() {
            if (!this.bizNameSearchValue.trim()) {
                this.filterBizFields = this.bizFields;
            }
            this.filterBizFields = this.bizFields.filter(item => {
                return item.bizName.toLowerCase().includes(this.bizNameSearchValue);
            });
        },
        // utils
        computeBgc(i) {
            return `background-color: rgba(${calcRgbColorByIndex(i)}, 0.6);border: 1px solid rgb(${calcRgbColorByIndex(i)})`;
        },
        // 切换接受人
        switchReceiver(v) {
            this.receiverIndex = v;
            this.setActiveReceiverIndex(v);
        },
        handleTermTip(item) {
            if (item.type === 'TERM' && this.termTypeList.length < 1) {
                this.$MessageToast.error('当前暂无可用条款，请前往企业控制台添加'); // 尚未添加类型
            }
        },
        onInsertStart(type, name, receiverFill, fontSize, necessary = true, labelMetaId = '0', buttonValue) {
            // 单复选只允许插入具体选项
            if (SELECT_TYPES.includes(type) && !buttonValue) {
                return;
            }
            if (type === 'TERM' && this.termTypeList.length < 1) {
                this.$MessageToast.error('当前暂无可用条款，请前往企业控制台添加'); // 尚未添加类型
                return;
            }
            const buttonObj = this.getInsertButtons(name, type, buttonValue);
            console.log('buttonValue', buttonValue);
            const item = {
                show: true,
                type,
                name,
                showName: `${name}${buttonValue ? ('-' + buttonValue) : ''} `,
                refBizFieldId: labelMetaId,
                fontSize: '',
                necessary: false,
                buttons: buttonObj.buttons,
                buttonValue,
                labelId: buttonObj.labelId,
            };
            if (!FLOAT_TYPES.includes(type)) {
                item.receiverFill = receiverFill;
                item.fontSize = fontSize;
                item.necessary = necessary;
            }
            this.setPreparingItem(item);
        },
        getInsertButtons(name, type, buttonValue) {
            let buttons = [];
            let labelId = null;
            if (SELECT_TYPES.includes(type)) {
                const mark = this.getInsertedSelectItem({ name, labelDataType: type });
                if (mark.buttons) {
                    buttons = buttons.concat(mark.buttons);
                }
                labelId = mark.labelId;
                buttons.push({
                    buttonValue,
                    buttonX: 0,
                    buttonY: 0,
                });
            }
            return {
                buttons,
                labelId,
            };
        },
        // 显示添加业务字段弹窗
        handleAddBizField() {
            this.dialogBizField.curField = {
                operateType: 'new',
            };
            this.dialogBizField.show = true;
        },
        // 显示编辑业务字段弹窗
        handleEditBizField(item) {
            // 编辑或删除之前首先查询是否曾经应用过模板
            this.dialogBizField.curField = {
                ...item,
                operateType: 'edit',
            };
            this.dialogBizField.show = true;
        },
        // 刷新业务字段
        handleRefreshFields() {
            this.getValidBizFields()
                .then(res => {
                    const result = (res.data.result || []).filter(item => item.bizFieldType !== 'COMBO_BOX');
                    this.mappingFieldToMata(result);
                });
        },
        // 关闭业务字段弹窗
        handleCloseBizDialog(status) {
            this.dialogBizField.show = false;
            if (status === true) {
                this.handleRefreshFields();
            }
        },
        // 获取启用中的业务字段
        getValidBizFields() {
            return this.$http.get(`${tempPath}/bizfields/valid`);
        },
        // 映射业务字段值为自定义标签值，保持原来代码逻辑不动
        mappingFieldToMata(data) {
            if (!data.length) {
                return;
            }

            // 初始化 bizFields数据
            data.forEach(item => {
                item.labelMetaId = item.fieldId;
                item.labelDataType = item.bizFieldType;
                item.name = item.bizName;
                item.receiverFill = item.writeBy !== 'SENDER';
            });
            this.bizFields = [];
            this.bizFields = this.bizFields.concat(data);
            // 业务字段初始化或者更新之后，触发一下搜索事件，重新获取当前搜索的结果
            this.handleChange();
        },
        // 跳转到合同装饰编辑页面
        toDerorationPage() {
            this.$emit('jumpToDecorate');
        },
    },
    created() {
        this.handleRefreshFields();
    },
};
</script>
<style lang="scss">
$site-bgclr: #00ffff;
.temp-field-site-cpn.dynamic-yz {
    .site-bar-head {
        height: 44px;
        line-height: 42px;
        font-size: 16px;
        color: #333;
        padding-left: 15px;
        border-top: 2px solid #127fd2;
        border-bottom: 1px solid #ddd;
        span {
            vertical-align: middle;
        }
        &:first-child {
            margin-top: 1px;
        }
        .order-num {
            font-size: 16px;
            color: #127fd2;
        }
    }

    // 接收人
    .signers {
        height: 143px;
        background-color: #f6f6f6;
        padding-top: 3px;
        overflow-y: auto;
        li {
            height: 30px;
            line-height: 30px;
            cursor: pointer;
            .signers-color {
                display: inline-block;
                float: left;
                width: 14px;
                height: 14px;
                border-radius: 50%;
                margin: 7px 7px 0 18px;
            }
            .signers-name {
                display: inline-block;
                width: 120px;
            }
            .signers-active {
                display: inline-block;
                float: right;
                font-weight: bold;
                margin-top: 9px;
                margin-right: 14px;
            }
            &.active {

            }
            &:hover {
                background-color: #eee;
            }
        }
    }

    // 字段
    .site-bar-body {
        height: calc(100% - 238px);
        overflow: auto;
    }
    .site-bar-title {
        height: 38px;
        line-height: 38px;
        font-size: 14px;
        font-weight: bold;
        color: #333;
        padding-left: 22px;
        border-bottom: 1px solid $border-color;

        .el-icon-ssq-wenhao{
            margin-left: 5px;
            font-size: 12px;
            color: #666;
            cursor: pointer;
        }
    }
    .site-bar-content {
        background-color: #fafafa;
        padding-top: 10px;
        padding-bottom: 15px;
        border-bottom: 1px solid $border-color;
        li {
            display: block;
            height: auto !important;
            line-height: 32px;
            white-space: nowrap;
            position: relative;
            &:hover {
                background-color: #eee;
                cursor: pointer;
                .metaLabel-operate-block {
                    display: inline-block;
                    opacity: 1;
                }
            }
            &.select-group {
                cursor: default;
                &.disabled {
                    .metaLabel-name,
                    .field-select-item,
                    .el-icon-ssq-danxuananniu,
                    .el-icon-ssq-fuxuankuang,
                    .el-icon-ssq-wenjianjia {
                        opacity: 0.5;
                        cursor: not-allowed;
                    }
                }
            }
            i {
                width: 24px;
                height: 24px;
                line-height: 24px;
                text-align: center;
                font-size: 16px;
                background-color: $site-bgclr;
                border-radius: 3px;
                margin-top: 4px;
                margin-left: 22px;
                margin-right: 10px;
            }
            .el-icon-ssq-tiaokuantubiao {
                font-size: 14px;
            }
            .metaLabel-name {
                display: inline-block;
                width: 125px;
                overflow: hidden;
                vertical-align: top;
                text-overflow: ellipsis;
            }

            .metaLabel-operate-block{
                right: 0;
                position: absolute;
                cursor: pointer;
                display: none;
                margin-right: 12px;

                &:hover{
                    .metaLabel-hidden-operate{
                        display: block;
                    }

                    .metaLabel-operate-icon{
                        color: #1687dc;
                    }
                }

                .metaLabel-operate-icon{
                    margin-left: 0;
                    margin-right: 0;
                    background-color: transparent;
                }

                .metaLabel-hidden-operate{
                    display: none;
                    position: absolute;
                    left: -30px;
                    top: 29px;
                    background: #fff;
                    border: 1px solid #ddd;
                    text-align: center;
                    box-shadow: 0px 2px 3px 1px #ddd;;
                    z-index: 1;

                    li{
                        width: 60px;
                        height: 36px;
                        line-height: 36px;
                        color: #333;
                        text-align: center;
                        font-size: 12px;

                        &:hover{
                            background: #f6f6f6;
                        }
                    }
                }
            }
            .field-select-item {
                display: block;
                cursor: pointer;
                line-height: 20px;
                padding-left: 34px;
                .metaLabel-name {
                    width: 115px;
                }
                i {
                    width: 20px;
                    height: 20px;
                    line-height: 20px;
                    margin-top: 0px;
                    margin-right: 5px;
                }
                &+.field-select-item {
                    margin-top: 4px;
                }
                &.disabled {
                    opacity: 0.5;
                    cursor: not-allowed;
                    pointer-events: none;
                }
                &:hover {
                    background: aliceblue;
                }
            }
        }
    }
    .metaLabel {
        position: relative;
        .add-metaLabel {
            position: absolute;
            top: 12px;
            right: 42px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;

            &:hover{
                color: #127fd2;
            }
        }

        .toManageLink{
            display: inline-block;
            position: absolute;
            top: 1px;
            right: 15px;

            .el-icon-ssq-shezhi{
                color: #666;
                font-size: 16px;

                &:hover{
                    color: #127fd2;
                }
            }
        }
    }
    .metaLabel-content {
        .metaLabel-content-ul{
            padding-bottom: 20px;
        }
    }

    .site-bar-bizname-search {
        margin: 0 10px 10px 10px;
    }

    .biz-metaLabel-content .metaLabel-content-ul{
        padding-bottom: 20px;
    }

    .box-sizing-dialog{
        .dialog-confirm-deleteLabel .el-dialog{
            width: 435px;
            .el-dialog__body p{
                line-height: 25px;
            }
        }
        .dialog-no-promission .el-dialog{
            width: 360px;
        }

        .dialog-tempMeta-desc .el-dialog, .dialog-meta-desc .el-dialog{
            width: 655px;

            .el-dialog__body{
                p{
                    color: #333;
                    line-height: 30px;
                }
            }
        }
    }
}
.field-site-popper {
    &.is-dark {
        background: #333;
    }
}
.temp-field-site-cpn__metaLabel-name-popper {
    width: 125px;
    line-height: 1.5;
}
.site-bar-decoration {
    display: flex;
    flex-direction: column;
    align-items:  center;
    .decoration-title {
        margin: 19px 0 3px 0;
        cursor: pointer;
        width: 180px;
        height: 30px;
        line-height: 30px;
        background: #FFFFFF;
        border: 1px solid #127FD2;
        font-size: 14px;
        color: #127FD2;
        border-radius: 2px;
        text-align:center;
    }
    .decoration-toast {
        font-size: 12px;
        font-weight: 400;
        color: #999999;
    }
}
</style>
