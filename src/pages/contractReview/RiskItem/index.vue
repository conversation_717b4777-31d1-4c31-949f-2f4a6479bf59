<template>
    <div class="risk-item">
        <Risk
            v-for="(risk, index) in data"
            :activeRisk="activeRisk"
            :index="index"
            :key="index"
            :data="risk"
            @selectRisk="selectRisk"
        />
    </div>
</template>

<script>
import Risk from './risk.vue';

export default {
    components: { Risk },
    props: {
        data: {
            type: Array,
            default: () => [],
        },
        activeRisk: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            activeName: '',
        };
    },
    methods: {
        selectRisk(val) {
            this.$emit('selectRisk', val);
        },
    },
};
</script>

<style lang="scss">
.risk-item {
    margin: 10px 15px;
}
</style>
