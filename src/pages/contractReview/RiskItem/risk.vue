<template>
    <div class="risk">
        <div class="risk-title" :class="active ? ' active' : ''" @click="selectRisk">{{ title }}</div>
        <div class="risk-content" v-show="active">
            <div class="content-comment">{{ data.comment }}</div>
            <div class="show-more" @click="showMore = !showMore">更多详情<i :style="{ transform: showMore ? '180deg' : 0 }" class="el-icon-ssq-xialajiantou"></i></div>
            <div class="content-detail" v-show="showMore">{{ data.detail }}</div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        index: {
            type: Number,
            default: 0,
        },
        data: {
            type: Object,
            default: () => {},
        },
        activeRisk: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            showMore: false,
        };
    },
    computed: {
        title() {
            return `风险点${this.index + 1}`;
        },
        active() {
            return this.activeRisk === `${this.index}`;
        },
    },
    methods: {
        selectRisk() {
            this.$emit('selectRisk', `${this.index}`);
        },
    },
};
</script>

<style lang="scss">
$--color-white: #FFFFFF;
$--color-text-primary: #333333;
$--color-primary: #127FD2;
$--color-text-second: #999999;
$--color-border: #EEEEEE;

.risk {
    background: $--color-white;
    color: $--color-text-primary;
    border-bottom: 1px solid $--color-border;
    padding-bottom: 16px;
    &:last-of-type {
        border-bottom: none;
    }
    .risk-title {
        height: 34px;
        font-size: 14px;
        padding-top: 10px;
        cursor: pointer;
        &:hover {
            color: $--color-primary;
        }
    }
    .show-more {
        margin-top: 10px;
        cursor: pointer;
        color: $--color-text-second;
        font-size: 12px;
        &:hover {
            color: $--color-primary;
        }
        i {
            margin-left: 5px;
        }
    }
    .active {
        color: $--color-primary;
    }
    .content-comment, .content-detail {
        margin-top: 10px;
        font-size: 12px;
    }
}
</style>
