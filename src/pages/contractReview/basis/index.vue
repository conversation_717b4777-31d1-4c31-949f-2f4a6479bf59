<template>
    <div style="height: 100%">
        <div class="review-basis" v-show="!regulationData">
            <Step :steps="steps" :currentStep="currentStep" />
            <div class="review-basis-content">
                <KeyWord
                    ref="keyword"
                    :extractTaskId="taskId"
                    v-show="currentStep === '01'"
                    title="您需要审查合同的哪些内容？请从下方的“审查项”中勾选。"
                    info="为了大模型能更准确理解关键词的内涵，建议在释义中写出它“是什么”和“不是什么”"
                    keyword="审查项"
                    :addInfo="addReviewInfo"
                    :deleteInfo="deleteReviewInfo"
                    @changeFields="(val) => fields = val"
                />
                <BasisFile
                    v-show="currentStep === '02'"
                    ref="rightDocument"
                    @uploadSuccess="(obj) => $emit('uploadSuccess', obj)"
                    :docReady="docReady"
                    :uploadUrl="uploadUrl"
                    :fragment="fragment"
                    @changeLoading="(val) => $emit('changeLoading', val)"
                    @changeFileName="(val) => $emit('changeFileName', val)"
                />
                <Regulation
                    ref="regulation"
                    :taskId="taskId"
                    @showRegulation="showRegulation"
                    @getCompleteStatus="val => completeStatus = val"
                    v-if="currentStep === '03'"
                />
            </div>
            <div class="review-basis-operate">
                <template v-if="currentStep === '01'">
                    <el-button type="primary" :disabled="!fields.length" @click="setStep('02')">下一步</el-button>
                </template>
                <template v-else-if="currentStep === '02'">
                    <el-button type="default" :disabled="!docReady" @click="$emit('clear')">重新上传</el-button>
                    <el-button type="default" @click="setStep('01')">上一步</el-button>
                    <el-button type="primary" :disabled="!docReady" @click="setStep('03')">下一步</el-button>
                </template>
                <template v-else>
                    <el-button type="default" @click="setStep('02')">上一步</el-button>
                    <el-button type="primary" @click="complete" :disabled="!completeStatus">完成</el-button>
                </template>
            </div>
        </div>
        <div class="review-basis" v-show="regulationData">
            <div class="contract-review__result-content">
                <div class="review-keywords">
                    <div class="regulation-tags">
                        <el-tag :class="['keyword', tagId === '' ? 'active' : '']" @click.native="getDetails()">全部</el-tag>
                        <el-tag
                            :class="['keyword', tagId === tag.keywordId ? 'active' : '']"
                            v-for="tag in reviewKeyword"
                            :key="tag.keywordId"
                            @click.native="getDetails(tag.keywordId)"
                        >{{ tag.keyword }}</el-tag>
                    </div>
                </div>
                <div class="contract-review__result-content-position" v-if="regulationData && !regulationData.init">审查者立场：{{ position }}</div>
                <div class="regulation-list">
                    <el-collapse v-if="regulationData && !regulationData.init" v-model="activeRisk" accordion @change="hideDetail">
                        <el-collapse-item v-for="(reviewBasis, index) in reviewBasisWithKeywordsList" :key="index" :name="index">
                            <template slot="title">
                                {{ reviewBasis.ruleInfo.ruleContent }}
                                <el-tag v-if="reviewBasis.keyword">{{ reviewBasis.keyword }}</el-tag>
                            </template>
                            <div v-for="(result, order) in resultList(reviewBasis.reviewUnitResults)" :key="order" class="risk-item">
                                <p class="risk-item-title">{{ result.reviewPassStatus === "NO" ? "风险点" : "友情提示" }}</p>
                                <div class="risk-item-body">
                                    <ul class="risk-item-body-list">
                                        <div>
                                            <li>{{ result.reviewPassStatus === "NO" ? "批注" : "备注" }}</li>
                                            <p>{{ result.detail }}</p>
                                        </div>
                                        <div>
                                            <li>详情：
                                                <el-button type="text" @click="selectRisk(reviewBasis.ruleInfo.ruleId, order)">查看</el-button>
                                            </li>
                                        </div>
                                    </ul>
                                    <div class="risk-item-more" v-if="order === 0 && reviewBasis.reviewUnitResults.length > 1">
                                        <el-button type="text" @click="moreDetail = !moreDetail">更多详情<i :style="{ transform: moreDetail ? 'rotate(180deg)' : 'rotate(0deg)' }" class="el-icon-ssq-xialajiantou"></i></el-button>
                                    </div>
                                </div>
                            </div>
                            <div class="risk-item-result" v-if="reviewBasis.reviewComment">
                                <p class="risk-item-title">审查结果提示</p>
                                <p class="risk-item-result-detail">{{ reviewBasis.reviewComment }} <el-button v-if="reviewBasis.explanation" type="text" @click="riskDetail = !riskDetail">详情<i :style="{ transform: riskDetail ? 'rotate(180deg)' : 'rotate(0deg)' }" class="el-icon-ssq-xialajiantou"></i></el-button></p>
                                <p v-if="riskDetail">{{ reviewBasis.explanation }}</p>
                            </div>
                        </el-collapse-item>
                    </el-collapse>
                    <div v-if="regulationData && regulationData.init">
                        <p v-for="basis in basisWithKeywordsList" :key="basis.knowledgeKeywordsId" class="risk-basis">
                            {{ basis.content }}
                            <el-tag v-for="tag in basis.markKeywords" :key="tag.keywordId">{{ tag.keyword }}</el-tag>
                        </p>
                    </div>
                </div>
            </div>
            <div style="text-align: right;">
                <el-button v-show="hasResult" class="contract-review__download" @click="downloadResult" type="primary">下载审查结果</el-button>
            </div>
        </div>
    </div>
</template>

<script>
import BasisFile from './basisFile.vue';
import Step from './step.vue';
import KeyWord from '../../common/contractExtract/exTract/index.vue';
import Regulation from './regulation.vue';
import { addReviewInfo, deleteReviewInfo } from 'src/api/review.js';
import { download } from 'src/common/utils/download.js';

export default {
    components: { BasisFile, Step, KeyWord, Regulation },
    props: {
        docReady: {
            type: Boolean,
            default: false,
        },
        uploadUrl: {
            type: String,
            default: '',
        },
        fragment: {
            type: Array,
            default: () => [],
        },
        taskId: { // 共用了合同抽取的业务组件
            type: String,
            default: '',
        },
        positionInContractReview: {
            type: String,
            default: '',
        },
        hasResult: {
            type: Boolean,
            default: false,
        },

    },
    data() {
        return {
            steps: [
                {
                    mark: '01',
                    text: '定义审查项',
                },
                {
                    mark: '02',
                    text: '上传依据文件',
                },
                {
                    mark: '03',
                    text: '形成审查依据',
                },
            ],
            currentStep: '01',
            fields: [],
            regulationData: null,
            activeRule: '',
            riskType: '',
            tagId: '',
            moreDetail: false,
            riskDetail: false,
            activeRisk: '',
            completeStatus: false,
        };
    },
    computed: {
        reviewBasisWithKeywordsList() {
            const initList = this.regulationData ? this.regulationData.reviewBasisWithKeywordsList : [];
            const totalResult = [];
            if (this.tagId) {
                return initList.find(item => item.keywordInfo.keywordId === this.tagId)?.ruleReviewResultList;
            } else {
                initList.forEach(element => {
                    totalResult.push(...element.ruleReviewResultList);
                });
                return totalResult;
            }
        },
        reviewKeyword() {
            const initList = this.regulationData ? this.regulationData.reviewBasisWithKeywordsList : [];
            const keywordList = this.regulationData ? this.regulationData.keywordList ? this.regulationData.keywordList : [] : [];
            const totalKeyword = [];
            if (keywordList.length) {
                totalKeyword.push(...keywordList);
            } else {
                initList.forEach(element => {
                    totalKeyword.push(element.keywordInfo);
                });
            }
            return totalKeyword;
        },
        basisWithKeywordsList() {
            const initList = this.regulationData ? this.regulationData.reviewBasisWithKeywordsList : [];
            const result = [];
            if (this.tagId) {
                initList.forEach(item => {
                    if (item.markKeywords.some(mark => mark.keywordId === this.tagId)) {
                        result.push(item);
                    }
                });
            } else {
                initList.forEach(item => {
                    if (item.markKeywords.length) {
                        result.push(item);
                    }
                });
            }
            return result;
        },
        getBackground() {
            return (item) => {
                const riskBgColor = 'rgba(255, 0, 0,0.2)';
                const noRiskColor = 'rgba(0, 200, 0, .3)';
                return item.ruleId === this.activeRule ? this.riskType === 'YES' ? noRiskColor : riskBgColor : '';
            };
        },
        position() {
            const map = {
                'PARTY_A': '甲方',
                'PARTY_B': '乙方',
            };
            return this.positionInContractReview ? map[this.positionInContractReview] : '';
        },
        resultList() {
            return (totalList) => {
                if (this.moreDetail) {
                    return totalList;
                } else {
                    const singleResult = totalList[0] ? [totalList[0]] : [];
                    return singleResult;
                }
            };
        },
    },
    watch: {
        regulationData: {
            deep: true,
            handler(v) {
                this.$emit('changeRegulationData', v);
            },
        },
    },
    methods: {
        addReviewInfo,
        deleteReviewInfo,
        showShadow(val) {
            this.activeRule = val.ruleId;
            this.riskType = val.reviewPassStatus;
            if (val.ruleId) {
                document.querySelector(`.review-basis .regulation-item_${val.ruleId}`).scrollIntoView();
            }
        },
        setStep(val) {
            this.currentStep = val;
            this.completeStatus = false;
        },
        async complete() {
            await this.$refs.regulation.save();
        },
        showRegulation(data) {
            this.regulationData = data;
            Object.assign(this.regulationData, { init: true });
            this.currentStep = '01';
            this.$emit('changeCompleteStatus', true);
        },
        clearRegulation() {
            this.regulationData = null;
            this.$emit('changeCompleteStatus', false);
        },
        reset() {
            this.activeRule = '';
            this.riskType = '';
            this.currentStep = '01';
            this.tagId = '';
            this.regulationData = null;
            this.$emit('changeCompleteStatus', false);
            this.$refs.keyword.reset();
        },
        setRegulation(data) {
            this.regulationData = data;
        },
        getDetails(id) {
            this.activeRisk = '';
            this.moreDetail = false;
            this.riskDetail = false;
            this.tagId = id || '';
            this.selectRisk();
        },
        hideDetail() {
            this.moreDetail = false;
            this.riskDetail = false;
            this.selectRisk();
        },
        selectRisk(ruleId, index) {
            this.$emit('selectRisk', { ruleId, index });
        },
        downloadResult() {
            download(`/web/hubble/contract-review/${this.taskId}/reviewed-contract-file-with-comments`);
        },
    },
};
</script>

<style lang="scss">
$--color-primary: #127FD2;
$--border-color-extra-light: #F8F8F8;
$--color-border: #EEEEEE;
.review-basis {
    height: 100%;
    padding: 8px 15px 8px;
    background: #FFFFFF;
    border-radius: 6px;
    &-content {
        height: calc(100% - 90px);
    }
    &-operate {
        height: 30px;
        margin-top: 8px;
        text-align: center;
        .el-button {
            padding: 8px 15px;
        }
    }
    .regulation-list {
        height: calc(100% - 98px);
        overflow-y: auto;
        .regulation-keyword {
            padding: 3px 10px;
            margin: 10px;
            font-size: 14px;
            line-height: 30px;
            color: $--color-primary;
            background-color: #E7F3FB;
            border-radius: 4px;
        }
        .regulation-item {
            padding: 10px 0;
            margin: 0 10px;
            font-size: 12px;
            border-bottom: 1px solid $--color-border;
            .regulation-keyword {
                position: relative;
                display: inline-block;
                height: 22px;
                line-height: 22px;
                font-size: 12px;
                padding: 0px 7px;
                margin: 4px;
                i {
                    position: absolute;
                    top: -4px;
                    right: -4px;
                    cursor: pointer;
                }
            }
        }
    }
}
</style>

<style lang="scss" scoped>
.review-keywords {
    overflow-x: auto;
    .regulation-tags {
        height: 42px;
        width: max-content;
        .keyword {
            background-color: #f6f6f6;
            color: #000;
            padding: 10px 22px;
            margin-right: 10px;
            font-size: 16px;
            line-height: 16px;
            height: 36px;
            cursor: pointer;
            &.active {
                background-color: #e7f3fb;
                color: #0c8aee;
            }
        }
    }
}
.regulation-list::v-deep .risk-item {
    position: relative;
    margin-bottom: 10px;
    &-title {
        font-size: 16px;
        font-weight: 500;
    }
    &-body {
        &-list {
            list-style: disc;
            border-bottom: 1px solid #ccc;
            padding-bottom: 10px;
            li {
                margin-left: 10px;
                font-size: 13px;
                font-weight: 500;
            }
            p {
                line-height: 24px;
            }
        }
    }
    &-more {
        position: absolute;
        right: 10px;
        bottom: 6px;
        .el-button {
            font-size: 12px;
        }
    }
    &-result {
        line-height: 24px;
        &-detail .el-button {
            font-size: 14px;
            margin-left: 10px;
        }
    }
}

.regulation-list::v-deep .el-collapse-item__header {
    height: auto;
}

.risk-basis {
    // height: 49px;
    line-height: 49px;
    font-size: 14px;
    color: #3D3D3D;
    border-bottom: 1px solid #eeeeee;
}
.el-tag {
    margin-left: 10px;
    padding: 0 6px;
    background: #E7F3FB;
    font-size: 12px;
    line-height: 20px;
    height: 22px;
    color: #0C8AEE;
}
</style>
