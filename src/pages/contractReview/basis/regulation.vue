<template>
    <div class="basis-regulation" v-loading="loading">
        <div class="basis-regulation-keyword">
            <div class="regulation-title">
                审查项
                <span class="title-info">可拖拽 <i class="el-icon-ssq-yidong"></i></span>
            </div>
            <div class="regulation-content">
                <div
                    v-for="item in markKeywords"
                    :key="item.keywordId"
                    class="regulation-keyword"
                    draggable
                    @dragstart="dragstart(item)"
                >{{ item.keyword }}</div>
            </div>
        </div>
        <div class="basis-regulation-item">
            <div class="regulation-title">
                审查依据条例
                <span class="edit" @click="editable = !editable">{{ operateText }} <i class="el-icon-ssq-xiugaihetongzhuangtai"></i></span>
            </div>
            <div class="regulation-content">
                <div class="regulation-item" @dragover.prevent @drop="drop(index, item.markKeywords)" v-for="(item, index) in reviewBasisWithKeywordsList" :key="index">
                    <span v-if="!editable">{{ item.content }}</span>
                    <el-input
                        v-else
                        resize="none"
                        type="textarea"
                        placeholder="选填"
                        v-model="item.content"
                    />
                    <span
                        v-for="(mark, num) in item.markKeywords"
                        :key="mark.keywordId"
                        class="regulation-keyword"
                    >
                        {{ mark.keyword }}
                        <i v-if="editable" class="el-icon-ssq-guanbi1" @click="deleteMark(index, num)"></i>
                    </span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { reviewAsyncTaskStart, reviewAsyncTaskInterval, getReviewRegulation, saveReviewRegulation } from 'src/api/review.js';

export default {
    props: {
        taskId: { // 共用了合同抽取的业务组件
            type: String,
            default: '',
        },
    },
    data() {
        return {
            editable: false,
            loading: true,
            regulationData: {
                reviewBasisWithKeywordsList: [],
            },
            markKeywords: [],
            operateMark: null,
            timer: null,
        };
    },
    computed: {
        operateText() {
            return this.editable ? '完成' : '编辑';
        },
        reviewBasisWithKeywordsList() {
            return this.regulationData.reviewBasisWithKeywordsList || [];
        },
    },
    methods: {
        dragstart(e) {
            this.operateMark = e;
        },
        drop(index, keywords) {
            if (this.operateMark && !keywords.find((item) => item.keywordId === this.operateMark.keywordId)) {
                this.regulationData.reviewBasisWithKeywordsList[index].markKeywords.push(this.operateMark);
                this.operateMark = null;
            }
        },
        deleteMark(index, num) {
            this.regulationData.reviewBasisWithKeywordsList[index].markKeywords.splice(num, 1);
        },
        save() {
            this.loading = true;
            saveReviewRegulation(this.taskId, this.regulationData).then(() => {
                this.$emit('showRegulation', this.regulationData);
            }).catch((e) => {
                console.log(e);
            }).finally(() => {
                this.loading = false;
            });
        },
        async handleAsyncTask() {
            await reviewAsyncTaskStart(this.taskId);
            const query = async() => {
                const { data } = await reviewAsyncTaskInterval(this.taskId);
                if (data.data === true) {
                    this.timer && clearTimeout(this.timer);
                    getReviewRegulation(this.taskId).then((res) => {
                        this.regulationData = res.data;
                        this.markKeywords = res.data.keywordList;
                        this.$emit('getCompleteStatus', true);
                    }).finally(() => {
                        this.loading = false;
                    });
                } else {
                    this.timer = setTimeout(() => {
                        query();
                    }, 3000);
                }
            };
            query();
        },
    },
    created() {
        this.handleAsyncTask();
    },
    beforeDestroy() {
        console.log('destory');
        this.timer && clearTimeout(this.timer);
    },
};
</script>

<style lang="scss">
$--color-text-second: #999999;
$--color-primary: #127FD2;
$--border-color-extra-light: #F8F8F8;
$--color-border: #EEEEEE;

.basis-regulation {
    height: 100%;
    display: flex;
    justify-content: space-between;
    &-keyword {
        height: 100%;
        width: 190px;
        border: 1px solid $--color-border;
        border-radius: 8px;
    }
    &-item {
        height: 100%;
        width: calc(100% - 202px);
        border: 1px solid $--color-border;
        border-radius: 8px;
    }
    .regulation-title {
        height: 44px;
        padding: 0 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: $--border-color-extra-light;
        border-radius: 8px 8px 0 0;
        border-bottom: 1px solid $--color-border;
        .title-info {
            font-size: 12px;
            color: $--color-text-second;
        }
        .edit {
            font-size: 12px;
            color: $--color-primary;
            cursor: pointer;
        }
    }
    .regulation-content {
        height: calc(100% - 44px);
        overflow-y: scroll;
        .regulation-keyword {
            padding: 3px 10px;
            margin: 10px;
            font-size: 14px;
            line-height: 30px;
            color: $--color-primary;
            background-color: #E7F3FB;
            border-radius: 4px;
            cursor: move;
        }
        .regulation-item {
            padding: 10px 0;
            margin: 0 10px;
            font-size: 12px;
            border-bottom: 1px solid $--color-border;
            .regulation-keyword {
                position: relative;
                display: inline-block;
                height: 22px;
                line-height: 22px;
                font-size: 12px;
                padding: 0px 7px;
                margin: 4px;
                i {
                    position: absolute;
                    top: -4px;
                    right: -4px;
                    cursor: pointer;
                }
            }
        }
    }
}
</style>
