<template>
    <div class="basis-file">
        <Upload
            type="right"
            v-if="!docReady"
            :uploadUrl="uploadUrl"
            @uploadSuccess="(obj) => $emit('uploadSuccess', obj)"
        ></Upload>
        <Document
            v-else
            ref="rightDocument"
            :fileUrl="uploadUrl"
            :fragment="fragment"
            @changeLoading="(val) => $emit('changeLoading', val)"
            @changeFileName="(val) => $emit('changeFileName', val)"
            type="right"
        />
    </div>
</template>

<script>
import Upload from '../upload';
import Document from '../document';
export default {
    components: { Upload, Document },
    props: {
        docReady: {
            type: Boolean,
            default: false,
        },
        uploadUrl: {
            type: String,
            default: '',
        },
        fragment: {
            type: Array,
            default: () => [],
        },
    },
    methods: {
        showShadow(val) {
            this.$refs.rightDocument.showShadow(val);
        },
    },
};
</script>

<style lang="scss">
.basis-file {
    height: 100%;
}
</style>
