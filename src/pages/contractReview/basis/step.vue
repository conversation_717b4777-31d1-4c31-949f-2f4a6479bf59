<template>
    <div class="step">
        <template v-for="item in steps">
            <span class="step-mark" :class="{ 'current-step': currentStep === item.mark }" :key="item.mark + item.text">{{ item.mark }}</span>
            <span :key="item.mark" :class="{ 'current-step': currentStep === item.mark }">{{ item.text }}</span>
            <span class="step-line" :key="item.text"></span>
        </template>
    </div>
</template>

<script>
// import Template from '../../enterprise/template/list/Template.vue';
export default {
    // eslint-disable-next-line vue/no-unused-components
    // components: { Template },
    props: {
        steps: {
            type: Array,
            default: () => [],
        },
        currentStep: {
            type: String,
            default: '',
        },
    },
};
</script>

<style lang="scss">
$--color-primary: #127FD2;

.step {
    display: flex;
    height: 40px;
    justify-content: center;
    align-items: center;
    margin-bottom: 8px;
    .step-mark {
        width: 24px;
        height: 24px;
        margin-right: 8px;
        color: #979797;
        font-size: 12px;
        background: #EEEEEE;
        border-radius: 50%;
        text-align: center;
        line-height: 24px;
        &.current-step {
            color: #FFFFFF;
            background-color: $--color-primary;
        }
    }
    .step-line {
        border: 1px solid #979797;
        flex-grow: 1;
        margin: 0 10px;
        &:last-of-type {
            display: none;
        }
    }
    .current-step {
        color: $--color-primary;
    }
}
</style>
