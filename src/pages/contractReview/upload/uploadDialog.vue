<template>
    <el-dialog
        class="contract-review-reupload"
        :visible="show"
        :modal-append-to-body="true"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :before-close="handleClose"
    >
        <div slot="title" class="contract-review-reupload_header">
            <span class="title">{{ $t('contractCompare.uploadReviewFile') }}</span>
            <i class="el-icon-close" @click="handleClose"></i>
        </div>
        <div class="reupload-content">
            <el-upload
                class="contract-review__upload"
                :action="uploadUrl"
                :show-file-list="false"
                :on-success="handleSuccess"
                :on-error="handleFail"
                :before-upload="beforeUpload"
                v-loading="loading"
            >
                <div>+ 上传文件</div>
            </el-upload>
        </div>
        <div class="history-title">历史审查依据文件：</div>
        <div class="history-item" v-for="item in fileList" :key="item.fileId">
            <i class="el-icon-ssq-zidingyibiaodan"></i>
            <span @click="handleSuccess(item)">{{ item.fileName }}</span>
        </div>
    </el-dialog>
</template>

<script>
import { getUploadHistory } from '@/api/review';
export default {
    props: {
        show: {
            type: Boolean,
            default: false,
        },
        uploadUrl: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            loading: false,
            fileList: [
                // {
                //     fileId: '3470255267629780994',
                //     fileName: 'rule_2',
                // },
                // {
                //     fileId: '3465180331034823687',
                //     fileName: 'service_bestsign_v2',
                // },
            ],
        };
    },
    watch: {
        show(val) {
            if (val) {
                this.getUploadHistory();
            }
        },
    },
    methods: {
        handleClose() {
            this.$emit('handleClose');
        },
        handleSuccess(res) {
            this.loading = false;
            this.handleClose();
            this.$emit('uploadSuccess', res);
        },
        handleFail(err) {
            this.loading = false;
            if (err.status === 401) {
                Vue.$http.get('/users/head-info');
                return;
            }
            try {
                const index = err.message.indexOf('{');
                const errData = JSON.parse(err.message.slice(index));
                errData.message && this.$MessageToast.error(errData.message);
            } catch (error) {
                this.$MessageToast.error(err.message);
            }
        },
        beforeUpload(file) {
            const fileTypeArr = file.name.split('.');
            const fileType = fileTypeArr[fileTypeArr.length - 1];
            if (!['pdf', 'doc', 'dot', 'docx'].includes(fileType)) {
                this.$MessageToast.error(this.$t('contractCompare.uploadError'));
                return false;
            }
            this.loading = true;
        },
        getUploadHistory() {
            getUploadHistory('RULE_FILE')
                .then((res) => {
                    this.fileList = res.data.historyFiles;
                });
        },
    },
    created() {
        this.getUploadHistory();
    },
};
</script>

<style lang="scss">
    $--color-text-primary: #333333;
    $--color-text-second: #999999;
    $--color-border: #EEEEEE;
    $--color-bg: #F8F8F8;
    $--color-white: #FFFFFF;
    $--color-primary: #127FD2;

    .contract-review-reupload {
        .el-dialog--small {
            width: 400px;
        }
        .el-dialog__header {
            padding: 0;
            border-bottom: 1px solid $--color-border;
            .contract-review-reupload_header {
                height: 50px;
                padding: 0 20px 0 30px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                .title {
                    font-size: 16px;
                    color: $--color-text-primary;
                }
                .el-icon-close {
                    cursor: pointer;
                    font-size: 12px;
                    color: $--color-text-second;
                }
            }
            .el-dialog__headerbtn {
                display: none;
            }
        }
        .el-dialog__body {
            padding: 26px 30px 30px;
            .contract-review__upload {
                height: 50px;
                border: 1px dashed $--color-primary;
                border-radius: 2px;
                color: $--color-primary;
                line-height: 50px;
                &:hover {
                    background-color: #F5F9FD;
                }
            }
            .history-title {
                margin-top: 30px;
                font-size: 12px;
                color: $--color-text-second;
            }
            .history-item {
                margin-top: 12px;
                font-size: 12px;
                color: $--color-text-primary;
                span {
                    margin-left: 8px;
                    &:hover {
                        color: $--color-primary;
                        cursor: pointer;
                    }
                }
            }
        }
    }
</style>
