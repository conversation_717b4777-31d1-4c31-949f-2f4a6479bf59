<template>
    <div class="contract-review">
        <Header ref="header"
            :config="{
                productType: 17,
                toolType: '审查',
            }"
        ></Header>
        <div class="contract-review__head">
            <div class="contract-review__head-box">
                <div class="contract-review__head-left">
                    <el-tooltip :disabled="!leftName" :content="leftName" placement="bottom">
                        <span class="ellipsis">{{ $t('contractCompare.reviewOriginFile') }}：{{ leftName }}</span>
                    </el-tooltip>
                    <el-button :disabled="!leftDocReady || leftDocumentLoading || needNewReviewId" @click="handleScan">识别扫描件</el-button>
                    <el-button :disabled="!leftDocReady || leftDocumentLoading" @click="clear('left')">重新上传</el-button>
                </div>
                <div class="contract-review__head-right">
                    <el-tooltip :disabled="!rightName" :content="rightName" placement="bottom">
                        <span class="ellipsis">{{ $t('contractCompare.reviewTargetFile') }}：{{ rightName }}</span>
                    </el-tooltip>
                    <div>
                        <el-button :disabled="!regulationData || needNewReviewId" @click="resetRegulation">重新生成</el-button>
                        <el-button type="primary" :loading="btnLoading" :disabled="needNewReviewId || !(leftDocReady && rightDocReady) || leftDocumentLoading || rightDocumentLoading || !optComplete" @click="chooseStand">{{ $t('contractCompare.startReview') }}</el-button>
                    </div>
                </div>
            </div>
            <div class="contract-review__result flex">
                <!-- <div class="head-tab" @click="selectedTab = 1" :class="selectedTab === 1 ? 'selected-tab' : ''">
                    <div class="head-tab_title">{{ $t('contractCompare.reviewResult') }}</div>
                    <div class="head-tab_info">{{ risksNum }}</div>
                </div> -->
                <div class="head-tab selected-tab">
                    <div class="head-tab_title">{{ $t('contractCompare.history') }}</div>
                    <div class="head-tab_info">{{ $t('contractCompare.historyLog', {num: total}) }}</div>
                </div>
            </div>
        </div>
        <div class="contract-review__content">
            <div class="contract-review__content-box">
                <div class="contract-review__content-left" v-loading="leftLoading">
                    <Upload
                        v-if="!leftDocReady"
                        :uploadUrl="leftUploadUrl"
                        @uploadSuccess="(obj) => readyCompare(obj, 'left')"
                    ></Upload>
                    <Document
                        v-else
                        ref="leftDocument"
                        :fileUrl="leftUploadUrl"
                        :fragment="leftFragment"
                        @changeLoading="(val) => leftDocumentLoading = val"
                        @changeFileName="(val) => leftName = val"
                    />
                </div>
                <div class="contract-review__content-right" v-loading="rightLoading">
                    <Basis
                        ref="rightDocument"
                        @uploadSuccess="(obj) => readyCompare(obj, 'right')"
                        :docReady="rightDocReady"
                        :uploadUrl="rightUploadUrl"
                        :fragment="rightFragment"
                        :taskId="reviewId"
                        @changeLoading="(val) => rightDocumentLoading = val"
                        @changeFileName="(val) => rightName = val"
                        @clear="clear('right')"
                        @changeRegulationData="val => regulationData = val"
                        @changeCompleteStatus="val => optComplete = val"
                        :positionInContractReview="positionInContractReview"
                        @selectRisk="selectRisk"
                        :hasResult="hasResult"
                    />
                </div>
            </div>
            <div class="contract-review__result">
                <!-- <template v-if="selectedTab === 1">
                    <div class="contract-review__result-content">
                        <div class="contract-review__result-content-position">审查者立场：{{ position }}</div>
                        <RiskItem ref="riskItem" :activeRisk="activeRisk" :data="risks" @selectRisk="selectRisk" />
                    </div>
                </template> -->
                <template>
                    <div class="contract-review__result-content">
                        <div
                            class="contract-review__result-content-item history"
                            v-for="(his, i) in history"
                            :key="i"
                            @click="goHistory(his)"
                        >
                            <el-tooltip :content="his.title" placement="top">
                                <p class="title ellipsis">{{ his.title }}</p>
                            </el-tooltip>
                            <p class="info">
                                <span>{{ moment(his.startTime || his.createTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
                                <span>{{ getReviewStatus(his.reviewStatus) }}</span>
                            </p>
                        </div>
                        <NoData v-if="!history.length" />
                    </div>
                    <el-pagination
                        small
                        layout="pager"
                        @current-change="handleCurrentChange"
                        :pager-count="5"
                        :page-size="10"
                        :current-page.sync="page"
                        :total="total"
                    >
                    </el-pagination>
                </template>
                <!-- <el-button v-show="hasResult" class="contract-review__download" @click="downloadResult" type="primary">下载审查结果</el-button> -->
            </div>
        </div>
        <RegisterFooter v-if="isPC" class="login-footer absolute-login-footer"></RegisterFooter>
        <div class="compare-loading" :class="fullLoading.loading ? ' recognize-loading' : ''" v-show="reviewLoading || fullLoading.loading">
            <div class="compare-loading_content">
                <img src="~img/contractHubble/hubble-loading.gif" alt="">
                <p v-show="reviewLoading">{{ $t('contractCompare.reviewing') }}</p>
                <p v-show="fullLoading.loading">{{ fullLoading.text }}</p>
            </div>
        </div>
        <el-dialog
            :visible="showChooseStand"
            :modal-append-to-body="true"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            class="contract-review-choose"
            :before-close="() => showChooseStand = false"
        >
            <div slot="title" class="contract-review-choose_header">
                <span class="title">选择身份</span>
                <i class="el-icon-close" @click="showChooseStand = false"></i>
            </div>
            <el-select v-model="standpoint" placeholder="">
                <el-option label="甲方" value="PARTY_A"></el-option>
                <el-option label="乙方" value="PARTY_B"></el-option>
            </el-select>
            <div slot="footer">
                <el-button type="default" @click="showChooseStand = false">取消</el-button>
                <el-button type="primary" @click="startReview">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import io from 'socket.io-client';
import moment from 'dayjs';
import Upload from './upload';
import Header from 'components/hubbleApplyHeader';
import RegisterFooter from 'components/register_footer/RegisterFooter.vue';
import NoData from 'components/noData';
import Document from './document';
// import RiskItem from './RiskItem';
import Basis from './basis';
import {
    getReviewHistory,
    startReview,
    // getReviewResult,
    initReviewTask,
    recognizeScan,
    getBillingInfo,
    getReviewResultV2,
} from 'src/api/review.js';
import { isPC } from 'src/common/utils/device.js';
// import { download } from 'src/common/utils/download.js';

const statusMap = {
    COMPLETED: '已完成',
    NOT_STARTED: '未开始',
    PROCESSING: '审查中',
};

export default {
    components: { Upload, Header, RegisterFooter, NoData, Document, Basis },
    data() {
        return {
            leftFragment: [],
            rightFragment: [],
            leftName: '',
            rightName: '',
            risks: [],
            leftLoading: false,
            rightLoading: false,
            // selectedTab: 1,
            history: [],
            btnLoading: false,
            leftDocumentLoading: false,
            rightDocumentLoading: false,
            noDataText: this.$t('contractCompare.noData'),
            standpoint: 'PARTY_A',
            showChooseStand: false,
            needNewReviewId: false,
            reviewLoading: false,
            reviewId: '',
            leftDocReady: false,
            rightDocReady: false,
            page: 1,
            total: 0,
            activeRisk: '',
            positionInContractReview: '',
            hasResult: false,
            isPC: isPC(),
            fullLoading: {
                loading: false,
                text: '',
            },
            regulationData: null,
            optComplete: false,
        };
    },
    computed: {
        leftUploadUrl() {
            return `/web/hubble/contract-review/${this.reviewId}/reviewed-contract-file`;
        },
        rightUploadUrl() {
            return `/web/hubble/contract-review/${this.reviewId}/rule-document-file`;
        },
        risksNum() {
            return `${this.risks.length}处审查结果`;
        },
        position() {
            const map = {
                'PARTY_A': '甲方',
                'PARTY_B': '乙方',
            };
            return this.positionInContractReview ? map[this.positionInContractReview] : '';
        },
    },
    methods: {
        moment,
        clear(type) {
            if (this.needNewReviewId) {
                this.clearTimer('reviewTimer');
                this.initTask().then(() => {
                    this.needNewReviewId = false;
                    this.leftDocReady = false;
                    this.rightDocReady = false;
                    this.$refs.rightDocument.reset();
                    this.leftName = '';
                    this.rightName = '';
                    this.reviewLoading = false;
                });
            } else {
                this[`${type}DocReady`] = false;
                this[`${type}Name`] = '';
            }
            this.risks = [];
            this.leftFragment = [];
            this.noDataText = '';
            this.activeRisk = '';
        },
        readyCompare({ fileName }, type) {
            this[`${type}DocReady`] = true;
            this[`${type}Name`] = fileName;
        },
        calcTip({ balanceEnough, enterpriseName, description }) {
            const content1 = description;
            const content2 = `扣除主体：${enterpriseName}`;
            const content3 = '可用页数不足，请购买充值。';
            const btnText = balanceEnough ? this.$t('contractCompare.confirm') : this.$t('contractCompare.toBuy');
            const h = this.$createElement;
            const content = balanceEnough ? h('p', null, [
                h('p', null, content1),
                h('p', { style: 'margin-top: 40px' }, content2),
            ]) : h('p', null, [
                h('p', null, content1),
                h('p', { style: 'margin-top: 30px' }, content2),
                h('p', { style: 'margin-top: 30px' }, content3),
            ]);
            this.$confirm(content, this.$t('contractCompare.tip'), {
                confirmButtonText: btnText,
                showCancelButton: true,
                customClass: 'compare-calc-billing',
            }).then(() => {
                if (balanceEnough) {
                    startReview(this.reviewId, this.standpoint)
                        .then(() => {
                            this.handleStartReview();
                        });
                } else {
                    this.$refs.header.openChargeDialog();
                }
            }).catch(() => {});
        },
        chooseStand() {
            this.showChooseStand = true;
        },
        startReview() {
            this.showChooseStand = false;
            this.btnLoading = true;
            getBillingInfo(this.reviewId)
                .then((res) => {
                    this.calcTip(res.data);
                }).finally(() => {
                    this.btnLoading = false;
                });
        },
        handleStartReview() {
            const h = this.$createElement;
            this.$confirm(h('p', null, [
                h('p', null, '已开始审查，预计需要5分钟，请耐心等待！'),
                h('p', { style: 'margin-top: 10px' }, '可在“历史记录”中找到此次审查记录。'),
            ]), this.$t('contractCompare.tip'), {
                confirmButtonText: this.$t('contractCompare.resumeReview'),
                cancelButtonText: this.$t('contractCompare.close'),
                showCancelButton: true,
                beforeClose: (action, instance, done) => {
                    if (action === 'confirm') {
                        this.initTask().then(() => {
                            this.leftDocReady = false;
                            this.rightDocReady = false;
                            this.$refs.rightDocument.reset();
                            done();
                        });
                    } else {
                        this.needNewReviewId = true;
                        this.reviewLoading = true;
                        this.reviewTimer = setInterval(() => {
                            this.getResult(this.reviewId);
                        }, 20000);
                        done();
                    }
                    this.getHistory();
                },
            }).then(() => {}).catch(() => {});
        },
        getResult(id) { // 3520417079008336903
            this.activeRisk = '';
            this.$refs.rightDocument.reset();
            getReviewResultV2(id)
                .then((res) => {
                    const { reviewId, basicInfo: { positionInContractReview, reviewStatus }, keywordReviewResultList } = res.data;
                    if (reviewStatus === 'SUCCESS') {
                        const positionList = [];
                        keywordReviewResultList.forEach(element => {
                            element.ruleReviewResultList.forEach(risk => {
                                Object.assign(risk, element.keywordInfo);
                                risk.reviewUnitResults.forEach((result, index) => {
                                    const position = Object.assign({}, result, { ruleId: risk.ruleInfo.ruleId, index });
                                    positionList.push(position);
                                });
                            });
                        });
                        this.clearTimer('reviewTimer');
                        this.reviewId = reviewId;
                        this.leftFragment = positionList;
                        // this.leftFragment = reviewPointList;
                        this.regulationData = { reviewBasisWithKeywordsList: keywordReviewResultList };
                        this.$refs.rightDocument.setRegulation(this.regulationData);
                        this.positionInContractReview = positionInContractReview;
                        // this.risks = reviewPointList;
                        this.leftDocReady = true;
                        this.rightDocReady = true;
                        // this.leftName = reviewedContract.filename;
                        // this.rightName = ruleDocument.filename;
                        this.noDataText = this.$t('contractCompare.noRisk');
                        this.$nextTick(() => {
                            this.reviewLoading = false;
                        });
                        this.getHistory();
                        this.hasResult = true;
                    }
                }).catch(() => {
                    this.clearTimer('reviewTimer');
                    this.reviewLoading = false;
                });
        },
        selectRisk(val) {
            // console.log(val);
            // this.activeRisk = val;
            this.$refs.leftDocument.showShadow(val);
            // this.$refs.rightDocument.showShadow(this.risks[val]);
        },
        // downloadResult() {
        //     download(`/web/hubble/contract-review/${this.reviewId}/reviewed-contract-file-with-comments`);
        // },
        getHistory() {
            getReviewHistory(this.page)
                .then((res) => {
                    const { records, totalRecord } = res.data;
                    this.history = records;
                    this.total = +totalRecord;
                });
        },
        goHistory(obj) {
            if (obj.reviewStatus === 'PROCESSING') {
                this.$MessageToast.error('正在审查中，请稍后再试');
                return;
            }
            this.hasResult = false;
            this.clearTimer('reviewTimer');
            this.needNewReviewId = true;
            this.reviewLoading = true;
            this.leftDocReady = false;
            this.rightDocReady = false;
            this.getResult(obj.reviewId);
            // this.selectedTab = 1;
        },
        initTask() {
            return new Promise((resolve) => {
                initReviewTask()
                    .then((res) => {
                        this.hasResult = false;
                        this.reviewId = res.data.reviewId;
                        this.$router.push({
                            query: { reviewId: this.reviewId },
                        });
                        resolve();
                    });
            });
        },
        clearTimer(timer) {
            this[timer] && clearInterval(this[timer]);
        },
        getReviewStatus(status) {
            return statusMap[status];
        },
        handleCurrentChange(page) {
            this.page = page;
            this.getHistory();
        },
        handleScan() {
            this.fullLoading.text = '扫描件识别中';
            this.fullLoading.loading = true;
            recognizeScan(this.reviewId).catch(() => {
                this.fullLoading.loading = false;
            });
        },
        initSocket() {
            this.socket = io('/web-hubble/ask-pdf', {
                path: '/web-hubble',
                reconnectionDelay: 5000,
                query: {
                    topicId: this.topicId,
                },
                transports: ['polling', 'websocket'],
                extraHeaders: {
                    Authorization: 'bearer ' + this.$cookie.get('access_token'),
                },
            });
            // 监听连接成功事件
            this.socket.on('connect', () => {
                console.log('连接成功');
            });
            this.socket.on('connect_error', (error) => {
                console.log('连接失败', error);
            });

            this.socket.on('scanned-copy-recognition-event', (data) => {
                console.log('socket:', data);
                this.fullLoading.loading = false;
                this.leftDocReady = false;
                this.leftLoading = true;
                setTimeout(() => {
                    this.leftLoading = false;
                    this.leftDocReady = true;
                }, 1500);
            });
        },
        resetRegulation() {
            this.needNewReviewId = true;
            this.clear('right');
        },
    },
    created() {
        this.initTask();
        this.getHistory();
        // this.initSocket();
    },
    beforeDestroy() {
        this.clearTimer('reviewTimer');
    },
};
</script>

<style lang="scss">
@import './index.scss';
</style>
