// 授权 角色/成员 使用/编辑 复制 模板
<template>
    <div class="dialogAuthorization" :class="{'dialogAuthorization_copy': isCopy}">
        <component
            class="dialogAuthorizationContent"
            :ref="componentName"
            :is="componentName"
            selectType="checkBox"
            :state="true"
            :onlyActive="true"
            :submitEmpty="!isCopy"
            :exsitedUserIds="authorizedUserIds"
            :exsitedMemberTitle="curTemplateId && !isCopy ? '已授权成员：' : '已选成员：'"
            :dialogTitle="dialogTitle"
            :title="dialogTitle"
            :showAdmin="true"
            :selectedValue="authorizedRoleIds"
            :templateId="curTemplateId"
            :permissionName="checkedType"
            @choose="handleChoose"
            @close="handleClose"
            @selectedDataChange="handleSelectedDataChange"
        >
            <div
                v-if="!isCopy"
                class="dialogAuthorizationTabsContent fl"
                slot="templateAuthorizationTab"
            >
                <p class="dialogAuthorizationTabs--title">
                    可分配的权限:
                </p>
                <div class="dialogAuthorizationTabs">
                    <p
                        class="dialogAuthorizationTab"
                        :class="{ 'dialogAuthorizationTab--current': checkedType === tab.type }"
                        v-for="tab in tabs"
                        :key="tab.type"
                        @click="handleClickTab(tab.type)"
                    >
                        {{ tab.title }}
                    </p>
                </div>
            </div>
        </component>
    </div>
</template>

<script>
import CompanyFullDialog from 'enterprise_pages/console/common/CompanyFullDialog.vue';
import CompanySelectRoleDialog from 'enterprise_pages/console/common/CompanySelectRoleDialog.vue';
import GroupSelectRoleDialog from 'enterprise_pages/template/common/GroupSelectRoleDialog.vue';

const componentsObj = {
    staff: {
        // 成员
        componentName: 'CompanyFullDialog',
        saveMethodName: 'handleShareToMebers',
    },
    companyRole: {
        // 企业角色
        componentName: 'CompanySelectRoleDialog',
        saveMethodName: 'handleAppendSealUsers',
    },
    groupRole: {
        // 集团角色
        componentName: 'GroupSelectRoleDialog',
        saveMethodName: 'handleAdminAuthorizeRole',
    },
    staffCopy: {
        // 模版复制给成员
        componentName: 'CompanyFullDialog',
        saveMethodName: 'handleCopyToMebers',
    },
};
export default {
    components: {
        CompanyFullDialog,
        CompanySelectRoleDialog,
        GroupSelectRoleDialog,
    },
    props: {
        curTemplateId: {
            default: '',
            type: String,
        },
        curTemplateName: {
            default: '',
            type: String,
        },
        componentType: {
            // 'staff': 成员, 'companyRole': 企业角色, 'groupRole': 集团角色, 'staffCopy': 复制给成员
            default: '',
            type: String,
        },
        authorizedStaffs: { // 授权过的成员
            default: function() {
                return [];
            },
            type: Array,
        },
        authorizedRoles: { // 授权过的角色
            default: function() {
                return [];
            },
            type: Array,
        },
        selectedLines: {
            // 批量授权模板数据
            default: function() {
                return [];
            },
            type: Array,
        },
    },
    data() {
        return {
            tabs: [
                {
                    title: '使用模板',
                    type: 'USE',
                },
                {
                    title: '编辑模板',
                    type: 'EDIT',
                },
            ],
            checkedType: 'USE',
            // 授权成员相关数据
            authorizedUseUserIds: [],
            authorizedEditUserIds: [],
            cacheAuthorizedUseUserIds: [],
            cacheAuthorizedEditUserIds: [],
            cacheAuthorizedUseEmpIds: [],
            cacheAuthorizedEditEmpIds: [],
            authorizedUserIds: [],
            // 企业授权角色相关数据
            authorizedUseRoleIds: [],
            authorizedEditRoleIds: [],
            cacheAuthorizedUseRoleIds: [],
            cacheAuthorizedEditRoleIds: [],
            authorizedRoleIds: [],
            // 集团授权角色相关数据
            cacheAuthorizedUseGroupData: {},
            cacheAuthorizedEditGroupData: {},
            // 复制给成员的相关数据
            cacheCopyRoleIds: [],
        };
    },
    computed: {
        curComponentData() { // 当前展示组件相关数据
            return componentsObj[this.componentType];
        },
        componentName() { // 当前展示组件名称
            return this.curComponentData && this.curComponentData.componentName;
        },
        isCopy() {
            return this.componentType === 'staffCopy';
        },
        dialogTitle() {
            if (this.curTemplateName) {
                return this.curTemplateName;
            }
            let title = '';
            switch (this.componentType) {
                case 'staffCopy':
                    title = '复制模版';
                    break;
                case 'companyRole':
                    title = '分配模板';
                    break;
                case 'groupRole':
                    title = '分配模板';
                    break;
                case 'staff':
                    title = '授权成员';
                    break;
            }
            return title;
        },
    },
    watch: {
        authorizedStaffs: { // 授权成员数据初始化
            handler(val) {
                if (val && val.length > 0) {
                    this.cacheAuthorizedUseUserIds = this.authorizedUseUserIds = this._commonFilterFun(val, 'USE', 'userId');
                    this.cacheAuthorizedEditUserIds = this.authorizedEditUserIds = this._commonFilterFun(val, 'EDIT', 'userId');
                    this.cacheAuthorizedUseEmpIds = this._commonFilterFun(val, 'USE', 'empId');
                    this.cacheAuthorizedEditEmpIds = this._commonFilterFun(val, 'EDIT', 'empId');
                    this.initChildrenProps(this.checkedType);
                }
            },
            immediate: true,
            deep: true,
        },
        authorizedRoles: { // 授权角色数据初始化
            handler(val) {
                if (val && val.length > 0) {
                    this.cacheAuthorizedUseRoleIds = this.authorizedUseRoleIds = this._commonFilterFun(val, 'USE', 'roleId');
                    this.cacheAuthorizedEditRoleIds = this.authorizedEditRoleIds = this._commonFilterFun(val, 'EDIT', 'roleId');
                    this.initChildrenProps(this.checkedType);
                }
            },
            immediate: true,
            deep: true,
        },
    },
    methods: {
        handleClickTab(type) {
            if (this.checkedType === type) {
                return;
            }
            if (this.componentType === 'groupRole') {
                // 如果是集团角色的tab切换，需要保存当前数据，再进行切换
                this.handleAdminAuthorizeRole();
            }
            this.checkedType = type;
            this.saveCacheDataBeforeSwitch(type);
            this.initChildrenProps(type);
        },
        saveCacheDataBeforeSwitch(type) {
            // 切换tab的时候，先reset弹框的数据，然后把切换之前的type，对应的最新的临时数组数据赋值
            this.$refs[this.componentName].resetValues();
            if (type === 'USE') {
                this.authorizedEditUserIds = this.cacheAuthorizedEditUserIds;
                this.authorizedEditRoleIds = this.cacheAuthorizedEditRoleIds;
            }
            if (type === 'EDIT') {
                this.authorizedUseUserIds = this.cacheAuthorizedUseUserIds;
                this.authorizedUseRoleIds = this.cacheAuthorizedUseRoleIds;
            }
        },
        initChildrenProps(type) {
            if (this.isCopy) {
                return false;
            }
            if (type === 'USE') {
                this.authorizedUserIds = this.authorizedUseUserIds;
                this.authorizedRoleIds = this.authorizedUseRoleIds;
            }
            if (type === 'EDIT') {
                this.authorizedUserIds = this.authorizedEditUserIds;
                this.authorizedRoleIds = this.authorizedEditRoleIds;
            }
        },
        _commonFilterFun(arr, type, mapFlag) {
            const congruentRelationship = {
                USE: 'useable',
                EDIT: 'editable',
            };
            return arr.filter(item => item.permission[congruentRelationship[type]]).map(item => item[mapFlag]);
        },
        handleSelectedDataChang__use(data) {
            if (data.type === 'member') {
                const membersData = data.membersData;
                this.cacheAuthorizedUseUserIds = membersData.map(member => member.userId);
                this.cacheAuthorizedUseEmpIds = membersData.map(member => member.empId);
            } else if (data.type === 'company') {
                this.cacheAuthorizedUseRoleIds = data.companyRoleData;
            } else if (data.type === 'group') {
                this.cacheAuthorizedUseGroupData = data.groupData;
            }
        },
        handleSelectedDataChang__edit(data) {
            if (data.type === 'member') {
                const membersData = data.membersData;
                this.cacheAuthorizedEditUserIds = membersData.map(member => member.userId);
                this.cacheAuthorizedEditEmpIds = membersData.map(member => member.empId);
            } else if (data.type === 'company') {
                this.cacheAuthorizedEditRoleIds = data.companyRoleData;
            } else if (data.type === 'group') {
                this.cacheAuthorizedEditGroupData = data.groupData;
            }
        },
        handleSelectedDataChang__copy(data) {
            this.cacheCopyRoleIds = data.membersData.map(item => {
                return {
                    employeeId: item.empId,
                    enterpriseId: item.entId,
                    userId: item.userId,
                    empName: item.empName,
                };
            });
        },
        handleSelectedDataChange(data) { // 选中的成员数据变动监听，赋值到临时数组
            if (this.isCopy) {
                // 模版复制给成员
                return this.handleSelectedDataChang__copy(data);
            }
            if (this.checkedType === 'USE') {
                this.handleSelectedDataChang__use(data);
            }
            if (this.checkedType === 'EDIT') {
                this.handleSelectedDataChang__edit(data);
            }
        },
        // 模版复制给成员
        async handleCopyToMebers() {
            const loading = this.$loading();
            try {
                await this.$http.post(`/template-api/templates/${this.curTemplateId}/copy `, this.cacheCopyRoleIds);
                this.$MessageToast.success('模版复制成功');
                loading.close();
            } catch (e) {
                loading.close();
            }
        },
        // 授权成员
        handleShareToMebers() {
            const data = this._authorizationCommonData();
            const url = '/template-api/templates/permission/all/grant/employee';
            const useData = {
                employeeIds: this.cacheAuthorizedUseEmpIds,
                permissionName: 'USE',
                ...data,
            };
            const editData = {
                employeeIds: this.cacheAuthorizedEditEmpIds,
                permissionName: 'EDIT',
                ...data,
            };
            this.$http.post(url, [useData, editData]).then(res => {
                if (res.data.code === '140001') {
                    this._handleAuthorizationSuccess();
                }
            });
        },
        // 非管理员授权角色（授权当前登录的企业角色）
        handleAppendSealUsers() {
            const data = this._authorizationCommonData();
            const url = '/template-api/templates/permission/all/grant/role';
            const useData = {
                roleIds: this.cacheAuthorizedUseRoleIds,
                permissionName: 'USE',
                ...data,
            };
            const editData = {
                roleIds: this.cacheAuthorizedEditRoleIds,
                permissionName: 'EDIT',
                ...data,
            };
            this.$http.post(url, [useData, editData]).then(res => {
                if (res.data.code === '140001') {
                    this._handleAuthorizationSuccess();
                }
            });
        },
        // 管理员授权集团公有角色、集团内某企业角色
        handleAdminAuthorizeRole(isClose = false) {
            isClose && this.$loading();
            const data = this._authorizationCommonData();
            const commonUrl = '/template-api/templates/permission/grant/group-all-have-role'; // 授权集团公有角色
            const enterpriseUrl = '/template-api/templates/permission/grant/group-ent-role'; // 授权集团内某企业角色
            const permissionName = this.checkedType;
            // 判断checkedType是USE还是EDIT
            const saveData = permissionName === 'USE' ? this.cacheAuthorizedUseGroupData : this.cacheAuthorizedEditGroupData;
            if (!saveData || !(saveData.entRoleInfoParams || saveData.enterpriseId)) {
                return;
            }
            this.$http.post(saveData.enterpriseId ? enterpriseUrl : commonUrl, {
                ...saveData,
                permissionName,
                ...data,
            }).then(res => {
                if (res.data.code === '140001' && isClose) {
                    this._handleAuthorizationSuccess();
                }
            }).finally(() => {
                isClose && this.$loading().close();
            });
        },
        _authorizationCommonData() {
            const tempIds = [];
            this.selectedLines.forEach(line => {
                tempIds.push(line.templateId);
            });

            const data = {
                templateIds: this.curTemplateId ? [this.curTemplateId] : tempIds,
            };
            return data;
        },
        _handleAuthorizationSuccess() {
            this.$MessageToast({
                message: '授权成功',
                iconClass: 'el-icon-ssq-qianyuewancheng',
            });
            this.handleClose();
            this.$emit('update');
        },
        handleChoose(data) {
            this[this.curComponentData.saveMethodName](data);
        },
        resetValues() {
            this.checkedType = 'USE';
            // 授权成员相关数据
            this.authorizedUseUserIds = [];
            this.authorizedEditUserIds = [];
            this.cacheAuthorizedUseUserIds = [];
            this.cacheAuthorizedEditUserIds = [];
            this.cacheAuthorizedUseEmpIds = [];
            this.cacheAuthorizedEditEmpIds = [];
            this.authorizedUserIds = [];
            // 企业授权角色相关数据
            this.authorizedUseRoleIds = [];
            this.authorizedEditRoleIds = [];
            this.cacheAuthorizedUseRoleIds = [];
            this.cacheAuthorizedEditRoleIds = [];
            this.authorizedRoleIds = [];
            // 集团授权角色相关数据
            this.cacheAuthorizedUseGroupData = {};
            this.cacheAuthorizedEditGroupData = {};
        },
        handleClose() {
            this.resetValues();
            this.$emit('close');
        },
    },
};
</script>

<style lang="scss">
.dialogAuthorization {
    .company-full-tree-dialog,
    .company-select-role-dialog,
    .group-select-role-dialog {
        .el-dialog {
            width: 880px;
        }
    }
    &.dialogAuthorization_copy .company-full-tree-dialog .el-dialog{
        width: 720px;
    }
    .dialogAuthorizationTabsContent {
        display: inline-block;
        width: 145px;
    }
    .dialogAuthorizationTabs {
        margin-top: 15px;
    }
    .dialogAuthorizationTab {
        margin-bottom: 10px;
        padding-left: 10px;
        line-height: 37px;
        text-align: left;
        font-size: 14px;
        color: #48576a;
        cursor: pointer;
    }
    .dialogAuthorizationTab--current {
        background-color: #e4eef3;
        border-right: 2px solid #108bf2;
        font-weight: bold;
    }
    .dialogAuthorizationInfo {
        color: #666;
        font-size: 12px;
    }
}
</style>
