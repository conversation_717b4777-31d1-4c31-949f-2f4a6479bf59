<template>
    <div class="footer-follow-page template-page">
        <!-- 公共头部 -->
        <CommonHeader pageModule="tplRcdDtl"></CommonHeader>

        <div class="template-container-fluid">
            <div class="template-container" ref="templateContainer">
                <div class="template-container-title">
                    <h2>
                        <span class="template-container-titleContent">模板发送详情</span>

                        <p class="template-title-right">
                            <router-link to="/template/record">
                                <el-button size="small" class="console-btn console-btn-whiteBg">
                                    <i class="el-icon-ssq-shangyiji"></i>
                                    返回
                                </el-button>
                            </router-link>
                        </p>
                    </h2>
                </div>

                <div class="template-collapse-container">
                    <el-collapse v-model="activeNames" @change="handleChange">
                        <el-collapse-item name="1">

                            <template slot="title">
                                <img class="header-icon" src="../images/send_failure.png" height="23" width="24" alt="">
                                <span>发送失败： <i class="failNum">{{ templateDetail.failure.length }}</i> 份</span>
                                <a v-if="templateDetail.failure.length !== 0" href="javascript:void(0)" @click="handleDownload()" class="download_failure">下载发送失败的Excel数据</a>
                            </template>

                            <DetailCollapse type="failure" :data="templateDetail.failure" :templateName="templateDetail.templateName"></DetailCollapse>
                        </el-collapse-item>
                        <el-collapse-item name="2">
                            <template slot="title">
                                <img class="header-icon" src="../images/sending.png" height="21" width="20" />
                                <span>发送中： <i>{{ templateDetail.inProcess.length }}</i> 份</span>
                                <i class="sendingTip" v-if="templateDetail.inProcess.length !== 0">（系统会自动发送，请耐心等待……）</i>
                            </template>

                            <DetailCollapse type="inProcess" :data="templateDetail.inProcess" :templateName="templateDetail.templateName"></DetailCollapse>
                        </el-collapse-item>
                        <el-collapse-item name="3">
                            <template slot="title">
                                <img class="header-icon" src="../images/send_success.png" height="22" width="21" alt="">
                                <span>发送成功： <i>{{ templateDetail.success.length }}</i> 份</span>
                            </template>

                            <DetailCollapse type="success" :data="templateDetail.success" :templateName="templateDetail.templateName"></DetailCollapse>
                        </el-collapse-item>
                    </el-collapse>
                    <QRCode
                        v-if="QRImgSrc"
                        :qrImgSrc="QRImgSrc"
                    >
                    </QRCode>
                </div>
            </div>
        </div>

        <!-- 公共底部 -->
        <CommonFooter class="template-page-footer footer-follow"></CommonFooter>
    </div>
</template>

<script>

import CommonHeader from 'components/header/Header.vue';
import CommonFooter from 'components/register_footer/RegisterFooter.vue';

import DetailCollapse from './TemplateRecordDetailCollapse.vue';
import QRCode from './QRCode.vue';

export default {
    components: {
        CommonHeader,
        CommonFooter,
        DetailCollapse,
        QRCode,
    },
    data() {
        return {
            templateDetail: {
                failure: [],
                inProcess: [],
                success: [],
                templateName: '',
            },
            templateId: '',
            activeNames: ['1'],
            QRImgSrc: '',
        };
    },
    methods: {
        // 获取模板发送详情
        getRecordDetail() {
            this.$http.get(`/template-api/send-records/${this.$route.params.templateId}`)
                .then(res => {
                    this.templateDetail.failure = res.data.failResults;
                    this.templateDetail.inProcess = res.data.processOnResults;
                    this.templateDetail.success = res.data.successResults;
                    this.templateDetail.templateName = res.data.templateName;
                    this.templateDetail.resultId = res.data.resultId;
                    this.templateId = res.data.templateId;
                })
                .catch(err => {
                    if (err.response.data.message === '数据不存在') {
                        this.$router.push('/template/record');
                    }
                });
        },
        // 面板展开折叠事件
        handleChange() {

        },
        // 下载错误数据
        handleDownload() {
            window.open(`/template-api/send-records/${this.templateDetail.resultId}/error-fields-file-generator?access_token=${this.$cookie.get('access_token')}`);
        },
    },
    beforeMount() {
        this.getRecordDetail();
    },
};
</script>

<style lang="scss">
	.template-page{
		.template-container-fluid{
			position: absolute;
			top:0;
			bottom:0;
			padding-top: 63px;
			padding-bottom: 35px;
			left: 0;
			right: 0;
			min-width: 100%;
            @include base-width;
			overflow-x: visible;
			overflow-y: hidden;
			background: #f6f6f6;

			.template-container{
				position: relative;
				@include base-width;
				height: 100%;
				margin: 0 auto;
				background: #fff;
				box-shadow: 0px 0px 5px 1px #ccc;

				.template-container-title{
					h2{
						padding-left: 22px;
						height: 55px;
						line-height: 55px;

						.template-container-titleContent{
							margin-right: 20px;
							font-size: 16px;
							font-weight: bold;
							vertical-align: middle;
							color: #000;
						}
					}
				}

				.template-collapse-container{
					height: calc(100% - 50px);
					overflow-y: auto;

					.el-collapse{
						padding: 0 22px;
						border-bottom: none;

						.el-collapse-item__header{
							position: relative;
							padding-left: 0;
							font-size: 14px;
							color: #333;

							span{
								font-weight: bold;
							}

							.header-icon{
								margin-right: 10px;
								vertical-align: middle;
							}

							.failNum{
								color: #f86b26;
							}

							.download_failure{
								margin-left: 15px;
								font-size: 12px;
								text-decoration: underline;
							}

							.sendingTip{
								color: #999999;
							}

							.el-collapse-item__header__arrow{
								position: absolute;
								right: 10px;
								top: 18px;
								color: #127fd2;
							}
						}

						.el-collapse-item__wrap{
							background: #f7fbfe;

							.el-collapse-item__content{
								padding-left: 35px;
							}
						}
					}
				}
			}
		}

		.template-page-footer.footer-follow{
			position: fixed;
			box-sizing: border-box;
		}
	}
</style>
