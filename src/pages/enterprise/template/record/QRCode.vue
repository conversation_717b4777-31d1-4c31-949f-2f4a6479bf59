<template>
    <div class="QR-Code">
        <p class="info">
            <span class="info1">用支付宝扫一扫签署合同</span>
            <span class="info2 common-font-color" @click="isShow = true">放大</span>
        </p>
        <img :src="qrImgSrc" class="QRImg">
        <transition name="fade">
            <div v-show="isShow" class="prepare-preview">

                <div class="content">
                    <span class="close thick" @click="clickClose"></span>
                    <div class="title">
                        用<span>支付宝</span>扫一扫 签署合同
                    </div>
                    <div class="img-con">
                        <img :src="qrImgSrc" alt="">
                    </div>
                </div>
            </div>
        </transition>
    </div>
</template>
<script>
export default {
    components: {

    },
    props: {
        qrImgSrc: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            isShow: false,
        };
    },
    computed: {

    },
    watch: {

    },
    methods: {
        clickClose() {
            this.isShow = false;
        },
    },
    created: function() {

    },
};
</script>
<style lang="scss">
.QR-Code {
    width: 252 - 29 - 29px;
    height: 283 - 20 -20px;
    margin-top: 32px;
    margin-left: auto;
    margin-right: auto;
    padding: 20px 29px;
    border-radius: 4px;
    box-shadow: 0px 0px 4px #eee;
    .info {
        font-size: 14px;
        color: #333;
    }
    .info2 {
        float: right;
        cursor: pointer;
    }
    .QRImg {
        margin-top: 20px;
        width: 196px;
        height: 196px;
    }
    .prepare-preview {
        z-index: 1001;
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        background-color: rgba(0, 0, 0, 0.6);
        overflow: scroll;
        .close {
                z-index: 999;
                position: absolute;
                top: 10px;
                right: -30px;
                display: inline-block;
                width: 20px;
                height: 20px;
                overflow: hidden;
                cursor: pointer;
            }
            .thick::before,
            .thick::after {
                height: 4px;
                margin-top: -2px;
            }
            .thick::before,
            .thick::after {
                content: '';
                position: absolute;
                height: 2px;
                width: 100%;
                top: 50%;
                left: 0;
                margin-top: -1px;
                background: #fff;
            }
            .thick::before {
                transform: rotate(45deg);
            }
            .thick::after {
                transform: rotate(-45deg);
            }
        .content {
            position: relative;
            width: 900px;
            height: 100%;
            background-color: #EAEBED;
            border-radius: 4px;
            margin: 0 auto;

            .title {
                height: 100px;
                line-height: 100px;
                font-size: 30px;
                text-align: center;
                span {
                    color: #249700;
                }
            }
            .img-con{
                position: relative;
                height: 84%;
                max-height: 740px;
                padding: 0 30px 18px;
                text-align: center;
                img {
                    height: 100%;
                }
            }
        }
    }
}
</style>
