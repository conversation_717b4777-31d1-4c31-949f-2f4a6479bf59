<template>
    <!-- 模板发送详情-折叠内容 -->
    <div class="collapse-component">
        <div v-if="data.length" class="collapse-table">
            <div class="collapse-header">
                <p>合同名称</p>
                <p>批量签约方</p>
                <p>{{ type === 'failure' ? '失败原因' : '发送状态' }}</p>
                <div class="clear"></div>
            </div>
            <div v-for="(item, index) in data" class="collapse-line" :key="index">
                <el-tooltip
                    effect="dark"
                    placement="bottom-start"
                    :visible-arrow="false"
                    :content="item.contractTitle"
                >
                    <p>{{ item.contractTitle }}</p>
                </el-tooltip>
                <el-tooltip
                    effect="dark"
                    :visible-arrow="false"
                    placement="bottom-start"
                    :content="item.placeHolderRecipientName"
                >
                    <p>{{ item.placeHolderRecipientName }}</p>
                </el-tooltip>
                <p class="collapse-line__error" v-if="type === 'failure'">{{ item.message }}</p>
                <p v-else>
                    {{ type === 'success' ? '发送成功' : '发送中...' }}
                </p>
            </div>
        </div>
        <div v-else class="nodata">
            暂无此类合同
        </div>
    </div>
</template>

<script>
export default {
    // eslint-disable-next-line
    props: ['type', 'data', 'templateName'],
    data() {
        return {

        };
    },
    methods: {

    },
};
</script>

<style lang="scss">
.collapse-component{
    .collapse-table{
        font-size: 14px;
        color: #333;

        p {
            float: left;
            width: 33.3%;
            min-height: 39px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;

        }

        .collapse-header{
            height: 45px;
            line-height: 45px;
            font-weight: bold;
        }

        .collapse-line{
            &::after {
                clear: both;
                visibility: hidden;
                display: block;
                font-size: 0;
                content: " ";
                height: 0;
            }
            p {
                padding: 9px 0;
                line-height: 1.5;
                min-height: auto;
            }

            border-top: 1px solid #eee;
            .collapse-line__error {
                white-space: normal;
            }
        }
    }
    .nodata{
        text-align: center;
    }
}
</style>
