<template>
    <div class="footer-follow-page template-page">
        <!-- 公共头部 -->
        <CommonHeader pageModule="tplRcd"></CommonHeader>

        <div class="template-container-fluid">
            <div class="template-container">
                <div class="template-container-title">
                    <h2>
                        <span class="template-container-titleContent">模板发送记录</span>

                        <p class="template-title-right">
                            <router-link to="/template/list">
                                <el-button size="small" class="console-btn console-btn-whiteBg">
                                    <i class="el-icon-ssq-shangyiji"></i>
                                    返回
                                </el-button>
                            </router-link>
                        </p>
                    </h2>
                </div>

                <div class="template-recordTable-container" ref="templateContainer">
                    <el-table
                        :data="templateRecords"
                        class="ssq-table w template-list-table"
                        v-autoH:table="{
                            static: true,
                            container: $refs.templateContainer,
                        }"
                    >
                        <el-table-column
                            label="时间"
                            width="250"
                        >
                            <template slot-scope="scope">
                                <span class="recordTime">
                                    <i class="el-icon-ssq-dengdaitarenqianshu"></i>
                                    {{ formatedSendTime(scope.row) }}
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column
                            label="模板名称"
                        >
                            <template slot-scope="scope">
                                <span class="template-list-table-name">
                                    {{ scope.row.templateName }}
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column
                            label="合同发送总份数"
                            width="200"
                        >
                            <template slot-scope="scope">
                                {{ scope.row.counter.total }}
                            </template>
                        </el-table-column>
                        <el-table-column
                            label="失败份数"
                            width="200"
                        >
                            <template slot-scope="scope">
                                <span :class="scope.row.counter.failure != 0 ? 'failureNum' : ''">
                                    {{ scope.row.counter.failure }}
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column
                            label="操作"
                            width="120"
                        >
                            <template slot-scope="scope">
                                <a :href="renderDetailUrl(scope.row)" class="toRecordDetail" target="_blank">
                                    查看详情
                                    <i class="arrow"></i>
                                </a>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>

                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="curPage"
                    :page-sizes="[50, 100, 150]"
                    :page-size="curPageSize"
                    :total="totalRecord"
                    layout="total, sizes, prev, pager, next, jumper"
                    class="doc-content-pagination"
                >
                </el-pagination>
            </div>
        </div>

        <!-- 公共底部 -->
        <CommonFooter class="template-page-footer footer-follow"></CommonFooter>
    </div>
</template>

<script>

import CommonHeader from 'components/header/Header.vue';
import CommonFooter from 'components/register_footer/RegisterFooter.vue';

import { formatDateToString } from 'utils/date.js';

export default {
    components: {
        CommonHeader,
        CommonFooter,
    },
    data() {
        return {
            templateRecords: [],
            curPage: 1,
            curPageSize: 50,
            totalRecord: 0, // 总记录
        };
    },
    methods: {
        // 获取模板发送记录
        getRecords() {
            this.$http.get(`/template-api/send-records?page=${this.curPage}&pageSize=${this.curPageSize}`)
                .then(res => {
                    this.templateRecords = res.data.contents;
                    this.totalRecord = parseInt(res.data.totalRecord);
                });
        },
        // 格式化模板发送时间
        formatedSendTime(row) {
            return formatDateToString({
                date: row.sendTime,
                format: 'YYYY.MM.DD hh:mm',
            });
        },
        // 查看详情 url
        renderDetailUrl(row) {
            return `/template/record/${row.resultId}`;
        },
        // 每页显示条数改变
        handleSizeChange(val) {
            this.curPageSize = val;
            this.getRecords();
        },
        // 当前页改变
        handleCurrentChange(val) {
            this.curPage = val;
            this.getRecords();
        },
    },
    beforeMount() {
        this.getRecords();
    },
};
</script>

<style lang="scss">
.doc-content-pagination {
    position: absolute;
    right: 20px;
    bottom: 10px;
    &.el-pagination .el-pager li {
        display: none;
        &.active {
            display: block;
        }
    }
}
	.template-page{
		.template-container-fluid{
			position: absolute;
			top:0;
			bottom:0;
			padding-top: 63px;
			padding-bottom: 35px;
			left: 0;
			right: 0;
			min-width: 100%;
            @include base-width;
			overflow-x: visible;
			overflow-y: hidden;
			background: #f6f6f6;

			.template-container{
				position: relative;
				@include base-width;
				height: 100%;
				margin: 0 auto;
				background: #fff;
				box-shadow: 0px 0px 5px 1px #ccc;

				.template-container-title{
					h2{
						padding-left: 22px;
						height: 55px;
						line-height: 55px;

						.template-container-titleContent{
							margin-right: 20px;
							font-size: 16px;
							font-weight: bold;
							vertical-align: middle;
							color: #000;
						}
					}
				}

				.template-recordTable-container{
					position: absolute;
					top: 55px;
					bottom: 40px;
					width: 100%;
					overflow-y: scroll;

					.ssq-table.el-table.template-list-table{
						font-size: 14px;

						.el-table__header-wrapper thead{
							tr th:first-child{
								.cell{
									margin-left: 4px;
								}
							}

							div{
								font-size: 14px;
							}
						}

						.el-table__body-wrapper{
							.el-table__row{
								.recordTime{
									color: #999;
									font-weight: normal;
								}

								.failureNum{
									color: #f86b26;
								}

								td:last-child .cell{
									text-align: left;

									.toRecordDetail{
										color: #2298f1;

										.arrow{
											position: relative;
											display: inline-block;
											margin-left: 5px;
											border: 6px solid transparent;
                                            width: 0;
                                            height: 0;
                                            border-left-color: #2298f1;
                                            vertical-align: middle;

                                            &:after{
                                            position: absolute;
                                            top: -6px;
                                            right: -4px;
                                            content: '';
                                            display: inline-block;
                                            border: 6px solid transparent;
                                            width: 0;
                                            height: 0;
                                            border-left-color: #fff;
                                            z-index: 1;
                                            }
										}
									}
								}
							}
						}

					}

				}

				.doc-content-pagination{
					right: 0;
					bottom: 0;
                    padding: 6px 25px 6px 0px;
                    width: calc(100% - 30px);
                    text-align: right;
                    background: #fff;
                    z-index: 2;
				}

			}
		}

		.template-page-footer.footer-follow{
			position: fixed;
			box-sizing: border-box;
		}
	}
</style>
