<template>
    <el-dialog
        :title="dialogTitle || '分配模板'"
        class="company-select-role-dialog group-select-role-dialog el-dialog-bg console-dialog-bg"
        :visible="true"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :before-close="handleClose"
    >
        <el-form :model="companySelectRoleForm">
            <!-- 模板授权tab -->
            <slot name="templateAuthorizationTab"></slot>
            <div class="role-block role-list-block block-column block-column-company">
                <p>选择企业：</p>
                <div class="cloumn role-cloumn" v-loading="getEnterprisesLoading">
                    <!-- 企业列表 -->
                    <div class="role-list-cloumn">
                        <div
                            class="list-item"
                            :class="{active: checkAllCompany}"
                            @click="checkAllCompanyHandler"
                        ><i class="el-icon-ssq-xuanzhong"></i>全部企业</div>
                        <div
                            class="list-item"
                            :class="{active: company.selected === true}"
                            v-for="(company, index) in companyList"
                            :key="index"
                            @click="selectCompany(company)"
                        >
                            <i class="el-icon-ssq-xuanzhong"></i>
                            {{ company.entName }}
                        </div>
                    </div>
                </div>
            </div>
            <div class="role-block role-list-block block-column block-column-role">
                <p>选择角色：</p>
                <div class="cloumn role-cloumn" v-loading="getRoleLoading">
                    <!-- 角色列表 -->
                    <div class="role-list-cloumn">
                        <el-checkbox-group v-model="companySelectRoleForm.selectedRoles">
                            <p v-for="role in roleList" :key="role.roleName">
                                <el-checkbox style="display: block;" :label="role.roleName">{{ role.roleName }}</el-checkbox>
                            </p>
                        </el-checkbox-group>
                    </div>
                </div>
            </div>
            <!-- 已选角色列表 -->
            <div class="role-block selected-block block-column block-column-role">
                <p>已选角色：</p>
                <div class="cloumn selected-cloumn" v-loading="getRoleLoading">
                    <p class="column-item" v-for="(role, index) in selectedRoleComputed" :key="index">
                        <i class="el-icon-ssq-user-filling"></i>
                        <span class="role-name">{{ (role || {}).roleName }}</span>
                        <i class="el-icon-ssq-delete" @click="cancelSelectedRole(index)"></i>
                        <i class="clear"></i>
                    </p>
                </div>
            </div>
            <div class="clear"></div>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-checkbox class="config-checkbox" v-model="needSubmitOnSwitch">
                切换企业自动授权已勾选角色
                <el-tooltip
                    class="item"
                    effect="dark"
                    content="勾选后，在“选择企业”列表中切换企业时，切换前的企业勾选的授权角色会被授权，无需点击“确定”按钮；不勾选时，必须点击确定按钮，才会对选中的企业完成授权。"
                    placement="top"
                >
                    <i class="el-icon-ssq-icon-bs-bangzhuzhongxin"></i>
                </el-tooltip>
            </el-checkbox>
            <el-button class="console-btn console-btn-primary" @click="chooseRoles(true)" type="primary">确 定</el-button>

            <!-- :disabled="companySelectRoleForm.selectedRoles.length == 0 || companySelectRoleForm.selectedRoles[0] == undefined" -->
        </div>
    </el-dialog>
</template>

<script>
export default {
    props: {
        templateId: {
            type: String,
            default: '',
        },
        permissionName: {
            // 权限名称 'USE' 'EDIT'
            type: String,
            default: 'USE',
        },
        dialogTitle: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            companySelectRoleForm: {
                selectedRoles: [],
            },
            cacheGroupAuthorizedUseRoleIds: [],
            cacheGroupAuthorizedEditRoleIds: [],
            currentEnterpriseId: null,
            checkAllCompany: true,
            companyList: [],
            roleList: [],
            getRoleLoading: true,
            getEnterprisesLoading: true,
            needSubmitOnSwitch: false,
        };
    },
    computed: {
        selectedRoleComputed() {
            return this.companySelectRoleForm.selectedRoles.map(v => {
                return this.roleList.find(item => item.roleName === v);
            });
            // return this.roleList.filter(v => this.companySelectRoleForm.selectedRoles.indexOf(v.roleName) > -1); // 按顺序列出来
        },
    },
    watch: {
        permissionName: {
            handler() {
                this.getCompanies();
            },
        },
        'companySelectRoleForm.selectedRoles': {
            handler(val) {
                if (this.permissionName === 'USE') {
                    this.cacheGroupAuthorizedUseRoleIds = val;
                } else if (this.permissionName === 'EDIT') {
                    this.cacheGroupAuthorizedEditRoleIds = val;
                }
            },
        },
        selectedRoleComputed: {
            handler() {
                this.$emit('selectedDataChange', {
                    groupData: this._handlePostData(),
                    type: 'group',
                });
            },
            immediate: true,
            deep: true,
        },
    },
    methods: {
        initSelectedRoles() {
            if (this.permissionName === 'USE') {
                this.companySelectRoleForm.selectedRoles = this.cacheGroupAuthorizedUseRoleIds;
            } else if (this.permissionName === 'EDIT') {
                this.companySelectRoleForm.selectedRoles = this.cacheGroupAuthorizedEditRoleIds;
            }
        },
        // 全部企业
        checkAllCompanyHandler() {
            // 在企业切换之前，先保存当前企业的信息
            this.chooseRoles();
            this.checkAllCompany = true;
            this.companyList.forEach(v => (v.selected = false));
            this.getRoles();
        },
        // 选择企业
        selectCompany(company) {
            // 在企业切换之前，先保存当前企业的信息，CFD-24062：支持配置自动提交
            if (this.needSubmitOnSwitch) {
                this.chooseRoles();
            }
            this.checkAllCompany = false;
            this.companyList.forEach(v => (v.selected = false));
            company.selected = true;
            this.getRoles(company.entId);
        },
        // 获取企业
        getCompanies() {
            this.getEnterprisesLoading = true;
            this.$http.get('/ents/group/enterprises').then(res => {
                this.getEnterprisesLoading = false;
                this.companyList = res.data;
                this.getRoles();
            });
        },
        // 获取已勾选的角色（角色接口【集团公有角色接口 or 集团内某企业角色接口】+ 已授权角色接口进行数据聚合）
        getRoles(entId) {
            this.getRoleLoading = true;
            let url =
                '/template-api/templates/permission/query/group-ents-all-have-role';
            this.currentEnterpriseId = null;
            if (entId) {
                this.currentEnterpriseId = entId;
                url = `/template-api/templates/permission/query/group-ent-have-role/${entId}`;
            }
            let roleData;
            this.$http
                .get(url)
                .then(res => {
                    const { code, result, message } = res.data;
                    if (code === '140001' && result) {
                        if (!entId) {
                            return result.entGrantRoleVOS; // 集团公有角色
                        }
                        return result.entGrantRoleIdsVOS; // 集团内某企业角色
                    } else {
                        throw message;
                    }
                })
                // 根据角色列表去获取已经授权（已勾选）的角色
                .then(data => {
                    roleData = data;
                    let queryGrantedRoleVOS = data;
                    // 这段代码兼容接口的数据接口（集团公有角色、集团内某企业角色的接口的返回数据结构是不同的，也就是上面那个 then 里面）
                    if (entId) {
                        queryGrantedRoleVOS = data.map(v => {
                            return {
                                entRoleInfoParams: v.roleIds.map(roleId => ({
                                    enterpriseId: entId,
                                    roleId: roleId,
                                })),
                                roleName: v.roleName,
                            };
                        });
                    }
                    if (this.templateId) {
                        return this.$http
                            .post(
                                '/template-api/templates/permission/query/group-ent-role-grant',
                                {
                                    queryGrantedRoleVOS,
                                    templateId: this.templateId,
                                },
                            )
                            .then(res => {
                                const { code, result, message } = res.data;
                                if (code === '140001' && result) {
                                    return result.grantedRoleVOS;
                                } else {
                                    throw message;
                                }
                            });
                    } else {
                        return [];
                    }
                })
                .then(grantedRoleData => {
                    if (this.currentEnterpriseId && this.currentEnterpriseId !== entId) {
                        return;
                    }
                    this.getRoleLoading = false;
                    grantedRoleData.forEach(v => {
                        const roleDataItem = roleData.find(
                            role => role.roleName === v.roleName,
                        );

                        roleDataItem.permission = v.permission
                            ? JSON.parse(v.permission)
                            : {};
                    });
                    this.roleList = roleData;
                    this.cacheGroupAuthorizedUseRoleIds = roleData
                        .filter(v => v.permission && v.permission.useable)
                        .map(v => v.roleName);
                    this.cacheGroupAuthorizedEditRoleIds = roleData
                        .filter(v => v.permission && v.permission.editable)
                        .map(v => v.roleName);
                    this.initSelectedRoles();
                })
                .catch(msg => {
                    this.$MessageToast({
                        message: `服务器接口错误：${msg}`,
                    });
                });
        },
        resetValues() {
            // 重置表单
            this.companySelectRoleForm.selectedRoles = [];
            this.checkAllCompany = true;
        },
        handleClose() {
            this.resetValues();
            this.$emit('close');
        },
        cancelSelectedRole(index) {
            this.companySelectRoleForm.selectedRoles.splice(index, 1);
        },
        _handlePostData() {
            const deepFlatten = arr =>
                [].concat(...arr.map(v => (Array.isArray(v) ? deepFlatten(v) : v)));
            let data;
            if (this.currentEnterpriseId) {
                // 授权集团下某企业角色参数
                data = {
                    roleIds: deepFlatten(this.selectedRoleComputed.map(v => v.roleIds)),
                    enterpriseId: this.currentEnterpriseId,
                };
            } else {
                // 授权集团公有角色参数
                data = {
                    entRoleInfoParams: deepFlatten(
                        this.selectedRoleComputed.map(v => v.entRoleInfoParams),
                    ),
                };
            }
            return data;
        },
        chooseRoles(isClose = false) {
            this.$emit('choose', isClose);
        },
    },
    mounted() {
        this.needSubmitOnSwitch = false;
        this.getCompanies();
    },
};
</script>
<style lang="scss" scoped>
.group-select-role-dialog {
    .block-column {
        display: inline-block;
        &.block-column-company {
            width: 250px;
            margin-right: -1px;
        }
        &.block-column-role {
            width: 185px;
        }
        .list-item {
            padding: 15px 0;
            font-size: 12px;
            border-bottom: 1px solid #eee;
            cursor: pointer;

            i {
                visibility: hidden;
                color: #F2A93E;
                padding-right: 3px;
                font-weight: bold;
            }
            &.active {
                //background-color: rgba(255, 255, 255, 0.1);
                i {
                    visibility: visible;
                }
            }
            &:last-child {
                border-bottom: 0;
            }
        }
    }
    .selected-cloumn {
        .column-item {
            padding-left: 0;
            padding-right: 0;
        }
        .role-name {
            max-width: 85px;
            display: inline-block;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
    .config-checkbox {
        margin-right: 10px;
    }
}
</style>
