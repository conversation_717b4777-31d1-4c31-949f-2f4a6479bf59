<!-- 视频显示 -->
<template>
    <el-dialog
        :title="title"
        size="big"
        class="uptate-tip-pop el-dialog-bg"
        :visible.sync="visible"
        :before-close="handleClose"
    >
        <div class="content">
            <video :width="width" class="video-element" controls preload v-if="visible">
                <source :src="videoPath" type="video/mp4" />
                您的浏览器不支持 video 标签。
                Your browser does not support HTML5 video.
            </video>
        </div>
    </el-dialog>
</template>
<script>
export default {
    props: {
        title: {
            type: String,
            default: '',
        },
        videoPath: {
            type: String,
            default: '',
        },
        width: {
            type: Number,
            default: 800,
        },
        visible: {
            type: Boolean,
        },
    },
    data() {
        return {
        };
    },
    methods: {
        handleClose() {
            // const duration = (Date.parse(new Date()) - this.enterPageDate) / 1000;
            // if (Object.keys(this.sensorsTrackContractInfo).length) {
            //     this.$sensors.track({
            //         eventName: `Ent_HubbleEnterWindow_BtnClick`,
            //         eventProperty: {
            //             page_name: '合同签署页',
            //             ...this.sensorsTrackContractInfo,
            //             window_name: 'Hubble视频弹窗',
            //             video_name: `${this.title}视频`,
            //             icon_name: '关闭',
            //             $event_duration: duration,
            //         },
            //     });
            // }
            this.$emit('update:visible', false);
        },
    },
};
</script>
<style lang="scss">

</style>
