<template>
    <el-dialog
        :visible.sync="dialogVisible"
        class="send-code-dialog"
        top="10vh"
    >
        <div slot="title"><i class="el-icon-message-solid"></i>{{ $t('template.sendCodeGuide.title') }}</div>
        <div>
            <p class="send-code-dialog-info">{{ $t('template.sendCodeGuide.info') }}</p>
            <el-row v-for="key in [1,2,3,4]" :key="key" :gutter="20">
                <el-col :span="10">
                    <p class="line-main">
                        <span>{{ $t(`template.sendCodeGuide.tip${key}.main`) }}</span>
                        <label for="">{{ $t(`template.sendCodeGuide.tip${key}.sub`) }}</label>
                    </p>
                    <p class="line-1">{{ $t(`template.sendCodeGuide.tip${key}.line1`) }}</p>
                    <p class="line-2">{{ $t(`template.sendCodeGuide.tip${key}.line2`) }}</p>
                </el-col>
                <el-col :span="14">
                    <img v-if="key === 1" src="~img/send-code/1.png" alt="">
                    <img v-else-if="key === 2" src="~img/send-code/2.png" alt="">
                    <img v-else-if="key === 3" src="~img/send-code/3.png" alt="">
                    <img v-else src="~img/send-code/4.png" alt="">
                </el-col>
            </el-row>
        </div>
    </el-dialog>
</template>

<script>
export default {
    props: {
        value: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            dialogVisible: false,
        };
    },
    watch: {
        value(value) {
            this.dialogVisible = value;
        },
        dialogVisible(v) {
            this.$emit('input', v);
        },
    },

};
</script>

<style lang="scss">
    $--color-primary: #127FD2 !default;
    $--color-text-primary: #333333 !default;
    $--color-white: #ffffff !default;
    $--color-text-primary: #333333 !default;
    $--color-primary-light-9: #F6F9FC !default;
    $--color-info: #999999 !default;
    .send-code-dialog {
        .el-dialog {
            width: 860px;
        }
        line-height: 1.6;
        .el-dialog__header {
            background: $--color-primary;
            color: $--color-white;
            line-height: 1;
            i.el-icon-message-solid {
                font-size: 16px;
                margin-right: 4px;
                position: relative;
                top: 1px;
            }
            i.el-icon-close {
                position: relative;
                top: -16px;
            }
        }
        &-info {
            color: $--color-text-primary;
            text-indent: 30px;
            margin-bottom: 15px;
        }
        .el-row {
            margin-bottom: 15px;
            line-height: 1.4;
            .el-col {
                &:first-child {
                    padding: 20px !important;
                    background: $--color-primary-light-9;
                    border-radius: 4px;
                    height: 119px;
                }
                .line-main {
                    color: $--color-text-primary;
                    font-weight: 500;
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 8px;
                }
                .line-1 {
                    color: $--color-text-primary;
                    font-weight: 400;
                    font-size: 12px;
                    margin-bottom: 8px;
                }
                .line-2 {
                    color: $--color-info;
                    font-size: 12px;
                }
                img {
                    width: 100%;
                }
            }
        }
    }
</style>
