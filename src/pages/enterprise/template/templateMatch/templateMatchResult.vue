<template>
    <div class="template-match-page footer-follow-page">
        <!-- 头部 -->
        <SignHeader
            class="header"
            :title="'导入详情'"
            :rText="btnText"
            @to-back="toBack"
            @to-next="toNext"
        >
        </SignHeader>
        <div class="match-container footer-follow-page-container" v-loading="loading">

            <!-- 导入数据总体匹配概况 -->
            <p class="detail">共导入{{ totalCount }}条数据，
                其中
                <span v-for="(item,index) in templatesMatched"
                    :key="index"
                >
                    {{ item.count }}条匹配至《{{ item.templateName }}》。
                </span>
                <span v-if="hasMismatch">{{ notMatched.count }}条未匹配到任何模板。</span>
            </p>

            <!-- 没有匹配到模板的数据 -->
            <div v-if="hasMismatch" class="table-container">
                <div class="table-header">
                    <div class="table-title not-match">未匹配的数据</div>
                    <div class="des">*未匹配的数据无法生成合同</div>
                </div>
                <div class="err-line" v-if="showErrorInfo">
                    <p>根据匹配规则：<span v-if="mismatchRows">第{{ mismatchRows }}行未匹配到模板；</span>
                        <span v-for="(item,index) in fieldMiss" :key="index">第{{ item.rows.join() }}行没有模板{{ item.templateName ? `《${item.templateName}》` : '' }}必填字段"{{ item.fieldName }}"的内容；</span>
                        <span v-for="(item,index) in linesErrorInfo" :key="index">第{{ item.lineNumber }}行{{ item.errMsg }};&nbsp;</span></p>
                </div>
                <el-table
                    class="template-match-table"
                    :data="notMatchTableData"
                    border
                    stripe
                    style="width: 100%"
                >
                    <el-table-column
                        v-for="(value, key) in notMatchTableData[0]"
                        :key="key"
                        :prop="key"
                        :label="key"
                    >
                    </el-table-column>
                </el-table>
            </div>

            <!-- 匹配到模板的数据 -->
            <div v-for="(item, index) in templatesMatched" :key="index" class="table-container">
                <div class="table-header">
                    <div class="table-title">{{ item.templateName }}</div>
                    <div class="match-rule">&nbsp;匹配规则</div>
                </div>
                <el-table
                    class="template-match-table"
                    :data="matchedTableData[index]"
                    border
                    stripe
                    style="width: 100%"
                >
                    <el-table-column
                        v-for="(itemA, key) in matchedTableData[index][0]"
                        :label-class-name="active(index, key)"
                        :class-name="active(index, key)"
                        :key="key"
                        :prop="key"
                        :label="key"
                    >
                    </el-table-column>
                </el-table>
            </div>
        </div>

        <!-- 公共底部 -->
        <CommonFooter class="template-page-footer footer-follow"></CommonFooter>
    </div>
</template>

<script>
import CommonFooter from 'components/register_footer/RegisterFooter.vue';
import SignHeader from '../../../foundation/template/common/signHeader/SignHeader';

export default {
    name: 'TemplateMatchResult',
    components: {
        SignHeader,
        CommonFooter,
    },
    data() {
        return {
            resultId: this.$route.query.resultId,
            totalCount: 0,   // 导入的总数据条数
            mismatchRows: '', // 未匹配到模板的数据
            notMatched: { count: 0 },    // 未匹配的数据

            templatesMatched: [],
            // 成功匹配的表格数据，二维数组，内层数组为对象数组，包含单个模板的数据[{'账号':'123','类型'：'类型a'},...]
            matchedTableData: [],
            // 匹配失败的数据， 对象数组
            notMatchTableData: [],
            // 行错误信息，对象数组[{lineNumber:1,errMsg:''},...]
            linesErrorInfo: [],
            // 模板规则字段，二维数组，内层数组为单个模板的规则字段集合
            ruleFields: [],
            // 匹配到模板，但是缺失模板必填字段的行信息
            fieldMiss: [],
            btnText: '下一步',

            loading: false,

        };
    },
    computed: {
        showErrorInfo() {
            return this.fieldMiss.length > 0 || this.linesErrorInfo.length > 0 || this.mismatchRows;
        },
        hasMismatch() {
            return this.notMatchTableData.length > 0;
        },
    },
    methods: {
        // 是否高亮列
        active(tempIndex, colName) {
            const isHighlight = Array.isArray(this.ruleFields[tempIndex]) && this.ruleFields[tempIndex].includes(colName);
            if (isHighlight) {
                return 'rule';
            }
        },
        toBack() {
            this.$router.push(`/template/list`);
        },
        toNext() {
            // 点下一步去第一个匹配到的模板指定位置页
            // 普通模板发起和模板匹配都会跳转到模板指定位置页，这里调过去通过templateMatchId查询计费
            if (this.templatesMatched.length) {
                const firstTemplateId = this.templatesMatched[0].templateId;
                this.$router.push(`/template/use/field?templateMatchId=${this.resultId}&templateId=${firstTemplateId}`);
            } else {
                // 没有匹配到模板时，'下一步'变'返回'
                this.$router.push(`/template/list`);
            }
        },
    },
    created() {
        this.loading = true;
        this.$hybrid.makeRequestAdaptToPublic({
            method: 'get',
            url: `/template-api/templates/match/${this.resultId}`,
            hybridServer: this.$store.state.commonHeaderInfo.hybridServer,
        }).then(res => {
            this.loading = false;
            const result = res.data;
            this.totalCount = result.totalCount;
            this.templatesMatched = result.templatesMatched;
            this.notMatched = result.notMatched;
            this.fieldMiss = result.notMatched.fieldMiss;
            // 未匹配到模板的行，末尾有个逗号去掉
            this.mismatchRows = result.mismatchRows.replace(/,$/gi, '');

            // 生成匹配成功的表格数据
            const matchedTableData = [];
            if (this.templatesMatched.length) {
                this.templatesMatched.forEach((item, tempIndex) => {
                    this.ruleFields.push(item.ruleFields);
                    matchedTableData[tempIndex] = [];
                    item.rows.forEach((row) => {
                        matchedTableData[tempIndex].push({
                            '行数': row.lineNumber,
                        });
                        row.fields.forEach((field) => {
                            matchedTableData[tempIndex][matchedTableData[tempIndex].length - 1][field.fieldName] = field.value;
                        });
                    });
                });
                this.matchedTableData = matchedTableData;
            } else {
                this.btnText = '返回';
            }

            if (this.notMatched.rows && this.notMatched.rows.length > 0) {
                const notMatchTableData = [];
                const linesErrorInfo = [];
                const rows = this.notMatched.rows;
                rows.forEach((row) => {
                    // 生成匹配失败的表格数据
                    notMatchTableData.push({
                        '行数': row.lineNumber,
                    });
                    row.fields.forEach((field) => {
                        notMatchTableData[notMatchTableData.length - 1][field.fieldName] = field.value;
                    });
                    // 生成匹配失败表格的报错信息
                    // let hasLineErr = row.fields.some(field => field.errorMessage)
                    if (row.errorMessage) {
                        const lineError = {
                            lineNumber: row.lineNumber,
                            errMsg: row.errorMessage,
                        };
                        linesErrorInfo.push(lineError);
                    }
                });
                this.notMatchTableData = notMatchTableData;
                this.linesErrorInfo = linesErrorInfo;
            }
        })
            .catch(() => {
                this.loading = false;
            });
    },
};
</script>

<style lang="scss">
    .template-match-page {
        .header {
            position: fixed;
            top: 0;
        }
        .rule {
            background-color: #e1ffc6 !important;
        }
        .match-container {
            width: 1100px;
            background-color: #fff;
            margin: 50px auto 0;
            .table-container {
                margin-bottom: 25px;
                .result-table {
                    .el-table__body-wrapper {
                        .el-table__body {
                            tr {
                                &:hover {
                                    background-color: #fff !important;
                                }
                            }
                        }
                    }
                }
            }
            .detail {
                font-size: 14px;
                padding: 20px 0;
            }
            .err-line {
                font-size: 12px;
                background-color: #e2f2fe;
                padding: 5px 10px;
                p {
                   line-height: 19px;
                }
            }
            .table-header {
                border-left: 2px solid #127fd2;
                background-color: #f8f8f8;
                font-size: 14px;
                padding: 0 20px 0 10px;
                line-height: 34px;
                .table-title {
                    display: inline-block;
                }
                .not-match{
                    color: red;
                }
                .des {
                    display: inline-block;
                    margin-left: 15px;
                }
                .match-rule {
                    width: 90px;
                    float: right;
                    &:before{
                        content: "";
                        display: inline-block;
                        vertical-align: middle;
                        width: 14px;
                        height: 14px;
                        background-color: #e1ffc6;
                    }
                }
            }
        }

    }
</style>
