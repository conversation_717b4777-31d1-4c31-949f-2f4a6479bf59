<template>
    <div class="footer-follow-page template-page">
        <!-- 公共头部 -->
        <CommonHeader v-if="hdVisible" :logoConfig="ssoTmps.tmps_1_logo" pageModule="tmps"></CommonHeader>

        <div class="template-container-fluid" v-loading="loading" :class="{'hd-hidden':!hdVisible, 'ft-hidden': !ftVisible}">
            <component
                :is="showPage"
                :listData="templateListData"
                :pagination="pagination"
                :templateMatchConf="templateMatchConf"
                @update="getTemplateData"
                @doSearch="setSearchData"
                @updateCurrentPage="updateCurrentPage"
                @updateCurrentSize="updateCurrentSize"
                @updateList="list => templateListData = list "
            ></component>
        </div>

        <!-- 公共底部 -->
        <CommonFooter class="template-page-footer footer-follow" v-if="ftVisible"></CommonFooter>
    </div>
</template>

<script>

import CommonHeader from 'components/header/Header.vue';
import CommonFooter from 'components/register_footer/RegisterFooter.vue';

import TemplateIndex from './TemplateIndex.vue';
import TemplateList from './TemplateList.vue';
import store from 'src/store/store';
import { mapGetters } from 'vuex';

export default {
    components: {
        CommonHeader,
        CommonFooter,
        TemplateIndex,
        TemplateList,
    },
    data() {
        return {
            showPage: '',
            templateListData: [],
            loading: false,
            ssoTmps: {}, // 单点登录配置
            pagination: {
                currentPage: 1,
                pageSize: 50,
                total: 0,
            },
            searchData: {
                contractTypeId: '',
                templateName: '',
                templateId: '',
                createUsers: [],
            },
            templateMatchConf: {},
        };
    },
    computed: {
        ...mapGetters(['getSsoConfig', 'checkFeat', 'getCurrentEntInfo']),
        hdVisible() {
            return !this.ssoTmps.tmps_1 || this.ssoTmps.tmps_1.visible;
        },
        ftVisible() {
            return !this.ssoTmps.tmps_ft || this.ssoTmps.tmps_ft.visible;
        },
    },
    methods: {
        /**
         * @param  {Object}   row 模板列表一行的数据
         * @return {Boolean}  该模板是自己创建的返回true，否组返回false
         */
        isTempOwner(row) {
            const currentEntId = this.$store.state.commonHeaderInfo.currentEntId;
            const currentUserId = this.$store.getters.getUserId;
            // 是否是自己创建的
            return (row.createUserId === currentUserId && row.createEnterpriseId === currentEntId);
        },
        updateCurrentPage(currentPage) {
            this.pagination.currentPage = currentPage;
            this.getTemplateData();
        },
        updateCurrentSize(currentSize) {
            this.pagination.currentPage = 1;
            this.pagination.pageSize = currentSize;
            this.getTemplateData();
        },
        setSearchData(data) {
            this.searchData = data;
            this.updateCurrentPage(1);
        },
        isSearchConditionEmpty() {
            const searchData = this.searchData;
            return (
                searchData.contractTypeId === '' &&
                searchData.templateName === '' &&
                searchData.templateId === '' &&
                searchData.createUsers.length === 0
            );
        },
        getTemplateData() {
            const {  ...paginationParams } = this.pagination;
            if (this.$route.query.isDynamic === '1') {
                paginationParams.filterDynamic = true;
                paginationParams.newSkipOld = true;
            }
            this.loading = true;
            // this.$http.get('template-api/templates')
            this.$http.post('/template-api/templates/search', this.searchData, {
                params: paginationParams,
            })
                .then(res => {
                    this.loading = false;
                    const data = res.data.results || [];
                    this.pagination.total = res.data.totalRecord;
                    this.showPage = 'TemplateList';
                    const arr = data.map(el => {
                        if (el.templateStatus === 'USING') {
                            el.templateStatus = 1;
                        } else {
                            el.templateStatus = 0;
                        }
                        return el;
                    });
                    this.templateListData = arr.filter(el => el.templateStatus || this.isTempOwner(el));
                    if (this.templateListData.length === 0 && this.isSearchConditionEmpty()) {
                        // SAAS-13651 如果是从新模板跳过来查找动态模板的情况, 就不进入空模板组件，而是直接进入list组件，并把"创建模板隐藏掉"
                        if (this.$route.query.isDynamic === '1') {
                            this.showPage = 'TemplateList';
                        } else {
                            this.showPage = 'TemplateIndex';
                        }
                    }
                })
                .catch(() => {
                    this.loading = false;
                    this.showPage = 'TemplateIndex';
                });
        },
        // 获取模板智能匹配的相关配置
        // https://jira.bestsign.tech/browse/SAAS-23086
        getSpecialConfig() {
            this.$http.get(`/template-api/ent/special-config?entId=${this.getCurrentEntInfo.entId}`).then(res => {
                this.templateMatchConf = res.data || {};
            });
        },
    },
    beforeMount() {
        this.getSpecialConfig();
        this.getTemplateData();
        this.ssoTmps = this.getSsoConfig.tmps || {};
    },
    beforeRouteEnter(to, from, next) {
        // SAAS-13651 开了新合同，且不是动态模板列表
        if (store.state.commonHeaderInfo.openNewContractTemplate && !(store.getters.checkFeat.dynamicTemplate && to.query.isDynamic === '1')) {
            return location.href = '/sign-flow/template/list';
        }
        next();
    },
    beforeRouteUpdate(to, from, next) {
        if (store.state.commonHeaderInfo.openNewContractTemplate && !(store.getters.checkFeat.dynamicTemplate && to.query.isDynamic === '1')) {
            return location.href = '/sign-flow/template/list';
        }
        next();
    },
};
</script>

<style lang="scss">
    .template-page{
        .common-header{
            z-index: 30;
        }
        .template-container-fluid{
            position: absolute;
            padding-top:63px;
            padding-bottom: 35px;
            left: 0;
            right: 0;
            top:0;
            bottom:0;
            min-width: 100%;
            @include base-width;
            overflow-x: visible;
            overflow-y: hidden;
            background: #f6f6f6;

            &.hd-hidden{
                padding-top:0;
            }
            &.ft-hidden{
                padding-bottom: 0;
            }

            .template-container{
                position: relative;
                @include base-width;
                height: 100%;
                margin: 0 auto;
                background: #fff;
                box-shadow: 0px 0px 5px 1px #ccc;
                overflow-y: scroll;
                .template-container-title{
                    h2{
                        padding-left: 20px;
                        height: 55px;
                        line-height: 55px;

                        .template-container-titleContent{
                            margin-right: 20px;
                            font-size: 16px;
                            font-weight: bold;
                            vertical-align: middle;
                            color: #000;
                        }

                        .template-button{
                            margin-right: 5px;
                            padding: 9px 21px;
                        }
                    }
                }
            }
        }

        .template-page-footer.footer-follow{
            position: fixed;
            box-sizing: border-box;
        }
    }
</style>
