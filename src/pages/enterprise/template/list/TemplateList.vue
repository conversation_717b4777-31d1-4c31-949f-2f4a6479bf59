<template>
    <div class="template-container" ref="templateContainer">
        <div class="template-button-line">
            <template v-if="couldCreate || hasActiveTemplateMatch">
                <!-- SAAS-13651 开了新合同，且不是动态模板列表，隐藏创建模板（静态） -->
                <el-button
                    class="btn-type-one template-button"
                    size="small"
                    @click="handleCreateNow"
                    v-if="couldCreate && ssoCreateVisible && this.$route.query.isDynamic !== '1'"
                >创建模板</el-button>
                <el-button
                    class="btn-type-one template-button"
                    size="small"
                    @click="handleCreateDynamic"
                >创建动态模板
                </el-button>
                <template v-if="hasActiveTemplateMatch">
                    <el-button class="btn-type-one template-button"
                        size="small"
                        @click="handleSmartMatch"
                    >智能匹配模版</el-button>
                    <a class="template-button-description"
                        size="small"
                        :href="smartMatchDescriptionLink"
                        target="_blank"
                    >使用说明</a>
                </template>
            </template>
            <p class="template-title-right">
                <a
                    class="operate-video"
                    href="javascript:void(0)"
                    @click="jumpPageLead"
                >
                    <span>新版尝鲜</span>
                </a>
                <!-- 开启了模板匹配时不显示操作演示 -->
                <a
                    v-if="!hasActiveTemplateMatch && ssoOperateVideoVisible"
                    class="operate-video"
                    href="javascript:void(0)"
                    @click="videoVisible = !videoVisible"
                >
                    <i class="el-icon-ssq-shipinyaofang"></i>
                    <span>操作演示</span>
                </a>
                <router-link to="/template/record" v-if="ssoRecordsVisible">
                    <i class="el-icon-ssq-lishijilu"></i>
                    <span>模板发送记录</span>
                </router-link>
            </p>
        </div>
        <TemplateSearch @doSearch="$emit('doSearch', $event)" ref="templateSearch" />

        <div class="template-table-container">
            <div
                v-if="hasOwnedTemp && ssoShareVisible && selectedLines.length > 0"
                class="template-table-opeartionLine"
            >
                <span class="template-table-opeartionLine-info">
                    {{ `已选中${selectedLines.length}份模板` }}
                </span>
                <el-button
                    size="small"
                    @click="handleCommandChange('share')"
                >
                    批量授权成员
                </el-button>
                <el-button
                    size="small"
                    @click="handleCommandChange('shareRole')"
                >
                    批量授权角色
                </el-button>
            </div>
            <el-table
                :data="templateList"
                class="ssq-table w template-list-table"
                @selection-change="handleSelectionChange"
                v-autoH:table="{
                    static: true,
                    container: $refs.templateContainer,
                    top: 111,
                    bottom: 100,
                }"
            >
                <!-- 选择列 -->
                <!-- 不是自己创建的模板不可批量授权 该行checkbox禁用 -->
                <el-table-column
                    v-if="hasOwnedTemp"
                    type="selection"
                    width="60"
                    :selectable="isTempOwner"
                >
                </el-table-column>

                <el-table-column
                    :render-header="handleSelectAllContent"
                >
                    <template slot-scope="scope">
                        <i class="el-icon-ssq-mobantubiao"></i>
                        <span class="template-list-table-name">
                            {{ scope.row.templateName }}

                        </span>
                    </template>
                </el-table-column>
                <el-table-column
                    label="创建人"
                    v-if="isGroupArchitecture"
                >
                    <template slot-scope="scope">
                        {{ formatCreateUser(scope.row) }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="模板编号"
                    prop="templateId"
                >
                </el-table-column>
                <el-table-column
                    label="合同类型"
                    width="210"
                >
                    <template slot-scope="scope">
                        {{ getContractTypeName(scope.row) }}
                    </template>
                </el-table-column>
                <el-table-column
                    v-if="features.includes('175')"
                    :render-header="(h) => h('p', {}, [
                        h('span', {}, '发送码('),
                        h('a', {
                            style: {
                                cursor: 'pointer'
                            },
                            on: {
                                click: () => isSendCodeGuideDialogShow = true,
                            },
                        }, '高级功能'),
                        h('span', {}, ')'),
                    ])"
                    width="210"
                >
                    <div slot-scope="scope" class="template-list-table-send-code">
                        <span v-if="!scope.row.templateSendCodeInfo || !scope.row.templateSendCodeInfo.open" @click="generateSendCode(scope.row.templateId)">生成</span>
                        <template v-else>
                            <el-popover
                                placement="left"
                                width="200"
                                trigger="manual"
                                popper-class="template-send-code-popover"
                                v-model="sendCodeViewVisible[scope.row.templateId]"
                            >
                                <div :id="'send-code-' + scope.row.templateId">
                                    <img :src="templateSendCodeImg(scope.row.templateId)" alt="">
                                    <p><a :href="templateSendCodeImg(scope.row.templateId)" :download="`${scope.row.templateName}.png`">下载模板发送码</a></p>
                                    <p class="copy-link">
                                        <el-input
                                            :value="templateSendCodeLink(scope.row)"
                                        >
                                            <template slot="append">
                                                <el-button
                                                    v-clipboard:copy="templateSendCodeLink(scope.row)"
                                                    v-clipboard:success="onCopy"
                                                    v-clipboard:error="onError"
                                                    class="copy-btn"
                                                    size="small"
                                                >{{ $t("docDetail.copy") }}</el-button>
                                            </template>
                                        </el-input></p>
                                </div>
                                <span slot="reference"
                                    @click="templateSendCodeView(scope.row.templatePermission.useable, scope.row.templateId)"
                                >查看</span>
                            </el-popover>
                            <span class="grey">|</span>
                            <span @click="closeSendCode(scope.row.templateId)">关闭</span>
                        </template>
                    </div>
                </el-table-column>
                <el-table-column
                    width="130"
                    label="启用状态"
                >
                    <template slot-scope="scope">
                        <span v-if="isTempOwner(scope.row)" class="console-link" @click.prevent.stop="reChangeStatus(scope.row)">
                            <span class="inline-block-con switch-btn">
                                <input type="checkbox" name="using" class="chooseBtn" :checked="scope.row.templateStatus">
                                <label for="using" class="choose-label"></label>
                            </span>
                            <span class="cursor-point approval-flow-status">
                                {{ scope.row.templateStatus ? '开启中' : '停用中' }}
                            </span>
                        </span>
                    </template>
                </el-table-column>
                <el-table-column
                    width="200"
                >
                    <template slot-scope="scope">
                        <OperateBtn :row="scope.row"
                            :isGroupAdmin="isGroupAdmin"
                            v-if="showBtn(scope.row)"
                            :expandVisible="ssoOperateExpandVisible"
                            :templateMatchConf="templateMatchConf"
                            @commandChange="handleCommandChange"
                        >
                        </OperateBtn>
                    </template>
                </el-table-column>
            </el-table>

            <el-pagination
                v-if="pagination.total > 0"
                class="template-pagination"
                :current-page="pagination.currentPage"
                @current-change="$emit('updateCurrentPage', $event)"
                @size-change="$emit('updateCurrentSize', $event)"
                :page-size="pagination.pageSize"
                layout="total, sizes, prev, pager, next"
                :page-sizes="[50, 100]"
                :total="pagination.total"
            ></el-pagination>
        </div>

        <div class="box-sizing-dialog">
            <!-- 授权相关弹框 -->
            <DialogAuthorization
                v-if="dialogAuthorizationData.show"
                :authorizedStaffs="dialogAuthorizationData.authorizedStaffs"
                :authorizedRoles="dialogAuthorizationData.authorizedRoles"
                :curTemplateId="curTemplateId"
                :curTemplateName="curTemplateName"
                :componentType="dialogAuthorizationData.type"
                :selectedLines="selectedLines"
                @close="handleDialogClose"
                @update="handleDialogUpdate"
            >
            </DialogAuthorization>

            <!-- 删除模板二次确认 -->
            <el-dialog
                class="el-dialog-bg delete-temp-dialog"
                :class="{
                    'delete-temp-dialog-large': currentTemplateExistMatch
                }"
                title="删除模板"
                :visible.sync="deleteConfirm.show"
                size="tiny"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                :before-close="handleCloseDeleteConfirm"
            >
                <p v-if="deleteConfirm.shared">
                    删除模板后，被授权使用人将无法使用该模板，
                    <span v-if="currentTemplateExistMatch">且对应的匹配规则也将一并删除</span>
                </p>
                <p v-else>
                    删除模板后，将无法恢复，
                    <span v-if="currentTemplateExistMatch">且对应的匹配规则也将一并删除</span>
                </p>
                <p>确定删除模板吗？</p>
                <div slot="footer" class="dialog-footer">
                    <el-button type="primary" @click="handleConfirmDelete">删除</el-button>
                    <el-button @click="handleCloseDeleteConfirm">取 消</el-button>
                </div>
            </el-dialog>
        </div>

        <VideoDialog
            title="操作演示"
            :visible.sync="videoVisible"
            videoPath="https://download.bestsign.cn/video/%E6%A8%A1%E7%89%88%E6%93%8D%E4%BD%9C%E6%BC%94%E7%A4%BA.mp4"
        >
        </VideoDialog>
        <el-dialog
            class="smart-match-dialog ssq-dialog"
            title="智能匹配模版"
            :visible.sync="smartMatchShow"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
        >
            <el-upload
                class="upload-btn"
                :action="uploadUrl"
                :headers="uploadHeaders"
                :data="{fileType: 'EXCEL'}"
                :before-upload="beforeUpload"
                :on-progress="uploading"
                :on-success="onUploadSuccess"
                :on-error="onUploadError"
                :show-file-list="false"
            >
                <img src="~img/import-excel.png" alt="">
                <p>上传Excel</p>
            </el-upload>
            <p class="download">没有Excel模板? <a href="javascript:;" @click="handleDoadlown">下载Excel</a></p>
        </el-dialog>
        <!-- 动态模版高亮提示弹窗 -->
        <el-dialog
            title="是否开通动态模板？"
            :visible.sync="dynamicTemplateConfigShow"
            custom-class="dynamic-config-dialog"
            size="tiny"
            top="35%"
            :show-close="false"
        >
            <div>
                开启动态模板后，你可以：
                <ul>
                    <li>（1）在上传的word文档内直接设置业务字段，不需要在web页面上拖拽了；</li>
                    <li>（2）在线编辑模板文稿；</li>
                    <li>（3）插入任意行列数的动态表格。</li>
                </ul>
            </div>
            <span slot="footer" class="dialog-footer">
                <span class="tip-text">马上联系上上签销售人员开通</span>
                <el-button type="primary" @click="dynamicTemplateConfigShow = false;">我知道了</el-button>
            </span>
        </el-dialog>
        <!-- sso 提示弹窗 -->
        <SsoNotBelongToEntDialog
            :params="ssoParams"
            @closeDialog="handleCloseSSoDialog"
        />
        <UnverifyConfirmDialog
            :params="unverifyDialog"
            @close="unverifyDialog.visible = false"
            :isGroupProxyAuthStatus="isGroupProxyAuthStatus"
        ></UnverifyConfirmDialog>
        <SendCodeDialog v-model="isSendCodeGuideDialogShow" />
    </div>
</template>

<script>
import { formatDateToString } from 'utils/date.js';
import { checkExcelUploadLimit } from 'src/common/utils/fileLimit';
import OperateBtn from './TemplateListBtn.vue';
import TemplateSearch from './TemplateSearch.vue';
import VideoDialog from '../common/VideoDialog.vue';

import DialogAuthorization from 'enterprise_pages/template/components/DialogAuthorization/index.vue';

import SsoNotBelongToEntDialog from 'src/components/ssoNotBelongToEntDialog/index.vue';
import { mapGetters, mapState } from 'vuex';
import UnverifyConfirmDialog from 'common_pages/home/<USER>';
import SendCodeDialog from '../common/SendCodeDialog.vue';

export default {
    components: {
        OperateBtn,
        VideoDialog,
        // MessageToast,
        DialogAuthorization,
        TemplateSearch,
        UnverifyConfirmDialog,
        SsoNotBelongToEntDialog,
        SendCodeDialog,
    },
    // eslint-disable-next-line
    props: ['listData', 'pagination', 'templateMatchConf'],
    data() {
        return {
            continueText: '继续创建',
            expandedClass: '',
            dialogAuthorizationData: {
                show: false,
                type: '', // 'staff': 成员, 'companyRole': 企业角色, 'groupRole': 集团角色,
                authorizedStaffs: [], // 授权过的员工
                authorizedRoles: [], // 授权过的角色
            },
            deleteConfirm: {
                show: false,
                shared: false,
            },
            curTemplateId: null,
            curTemplateName: '',
            selectedLines: [],
            videoVisible: false,

            loading: '',
            allUsers: [],
            allUsersRequested: false, // 是否请求过所有用户
            ssoTmps: {}, // 单点登录配置

            dynamicTemplateOn: false, // 是否开通动态模版
            dynamicTemplateConfigShow: false,

            uploadHeaders: { Authorization: `bearer ${this.$cookie.get('access_token')}` },
            isGroupAdmin: false,
            isGroupArchitecture: true,
            currentTemplateExistMatch: false,
            smartMatchShow: false, // 智能匹配模版上传弹窗
            smartMatchDescriptionLink: `${location.origin}/feature-detail/100000`, // 智能匹配模版使用说明链接
            smartMatchCanNotDownloadMessage: '',
            unverifyDialog: {
                visible: false,
                // callback: this.doCreate
            },
            ssoParams: {
                visible: false,
                developerName: '',
            },
            isGroupProxyAuthStatus: this.$store.state.commonHeaderInfo.enterprises[0].isGroupProxyAuthStatus,
            sendCodeViewVisible: {},
            isSendCodeGuideDialogShow: false,
        };
    },
    computed: {
        ...mapState(['features']),
        ...mapGetters([
            'getUserId',
            'getAuthStatus',
        ]),
        templateList() {
            return this.listData;
        },
        // 模板列表中是否有自己创建的模板
        hasOwnedTemp() {
            return this.templateList.some(item => this.isTempOwner(item));
        },
        couldCreate() {
            return this.$store.getters.getUserPermissons.createTemp;
        },
        // 是否开启了模板匹配功能
        hasActiveTemplateMatch() {
            return this.checkFeat.templateIntelligentMatch;
        },
        ssoCreateVisible() {
            return !this.ssoTmps.tmps_main_crt || this.ssoTmps.tmps_main_crt.visible;
        },
        ssoShareVisible() {
            return !this.ssoTmps.tmps_btn_shareMbr || this.ssoTmps.tmps_btn_shareMbr.visible;
        },
        ssoOperateExpandVisible() {
            return !this.ssoTmps.tmps_btn_oprt_expand || this.ssoTmps.tmps_btn_oprt_expand.visible;
        },
        ssoOperateVideoVisible() {
            return !this.ssoTmps.tmps_lnk_video || this.ssoTmps.tmps_lnk_video.visible;
        },
        ssoRecordsVisible() {
            return !this.ssoTmps.tmps_lnk_rcd || this.ssoTmps.tmps_lnk_rcd.visible;
        },
        ...mapGetters(['getSsoConfig', 'checkFeat']),
        // excel uploadUrl
        uploadUrl() {
            return `${this.$store.state.commonHeaderInfo.hybridServer}${tempPath}/templates/match-rule-user-list-read`;
        },
        templateSendCodeUrl() {
            return token => `${window.location.origin.includes('localhost:') ? 'https://ent3-k8s.bestsign.info' : window.location.origin}/s/${token}`;
        },
        templateSendCodeImg() {
            return templateId => `/template-api/v2/template/${templateId}/generate-send-code-image?access_token=${this.$cookie.get('access_token')}`;
        },
        templateSendCodeLink() {
            return ({ templateSendCodeInfo: { token } = {} }) => `${location.origin}/s/${token}`;
        },
    },
    methods: {
        // 复制成功
        onCopy: function() {
            this.$MessageToast.success(this.$t('docDetail.copySucc'));
        },
        // 复制失败
        onError: function() {
            this.$MessageToast.success(this.$t('docDetail.copyFail'));
        },
        handleCloseSSoDialog() {
            this.ssoParams.visible = false;
        },
        // 停用/启用确认弹框
        reChangeStatus(row) {
            if (row.templateStatus) {
                this.$confirm('停用后该模板将无法被使用，确定停用吗？', '停用模板', {
                    confirmButtonText: '停用',
                    cancelButtonText: '取消',
                }).then(() => {
                    this.changeStatus(row);
                }).catch(() => {
                    return;
                });
            } else {
                this.$confirm('启用后，被授权使用人将可以使用该模板，确定启用吗？', '启用模板', {
                    confirmButtonText: '启用',
                    cancelButtonText: '取消',
                }).then(() => {
                    this.changeStatus(row);
                }).catch(() => {
                    return;
                });
            }
        },
        // 停用/启用
        changeStatus(row) {
            this.$http.post(`/template-api/templates/${row.templateId}/change-status?templateStatus=${row.templateStatus}`).then(() => {
                row.templateStatus = row.templateStatus ? 0 : 1;
                this.$MessageToast.success(row.templateStatus ? '启用成功' : '停用成功');
            });
        },
        /**
         * @param  {Object}   row 模板列表一行的数据
         * @return {Boolean}  该模板是自己创建的返回true，否组返回false
         */
        isTempOwner(row) {
            const currentEntId = this.$store.state.commonHeaderInfo.currentEntId;
            const currentUserId = this.$store.getters.getUserId;
            // 是否是自己创建的
            return (row.createUserId === currentUserId && row.createEnterpriseId === currentEntId);
        },
        // 开启了模板匹配且模板不是自己创建的，隐藏模板操作按钮
        showBtn(row) {
            return !(this.hasActiveTemplateMatch && !this.isTempOwner(row)) || this.templateMatchConf.hasUseAlone;
        },
        // 格式化时间
        formatDate(time) {
            return formatDateToString({
                date: time,
                format: 'YYYY-MM-DD hh:mm:ss',
            });
        },
        // 格式化创建人
        formatCreateUser(row) {
            const { createUserName, createUserAccount, createEnterpriseName } = row.createUserInfo || {};
            if (createEnterpriseName) {
                return `${createUserName || createUserAccount || ''}（${createEnterpriseName || ''}）`;
            }
            return createUserName || createUserAccount || '';
        },
        // 过滤错误
        matchErrMsg(message) {
            if (!message) {
                return {
                    message: null,
                };
            }
            const matchedAry = message.match(/\{(.+?)\}/g);
            return JSON.parse(matchedAry[0]);
        },
        jumpPageLead() {
            if (location.href.indexOf('cn') > 0) {
                // 生产的链接不一样
                window.open('https://ent.bestsign.cn/feature-detail/100019');
            } else {
                window.open('/feature-detail/100034');
            }
        },
        handleCreateDynamic() {
            if (this.isGroupProxyAuthStatus) {
                // this.continueText = '继续创建';
                this.unverifyDialog.visible = true;
                // this.unverifyDialog.callback = this.doCreateDynamic;
            } else {
                this.doCreateDynamic();
            }
        },
        // 创建动态模板
        // 注：点击创建按钮后不调用单独的接口来判断是否可以创建模板
        doCreateDynamic() {
            // 不支持混合云
            if (this.$store.state.commonHeaderInfo.hybridServer) {
                this.$MessageToast.error('您采用的是合同文档本地部署方式，暂不支持此功能，敬请期待');
                return;
            }
            const loading = this.$loading();
            // 实时查询是否开启动态模版
            this.$http.get('/template-api/templates/permission/query/group-ent-info')
                .then(res => {
                    const { code, result, message } = res.data;
                    if (code === '140001') {
                        this.dynamicTemplateOn = result.dynamicTemplate;

                        if (!this.dynamicTemplateOn) { // 未开通或未配置
                            this.dynamicTemplateConfigShow = true;
                        } else {
                            this.$http.post('/template-api/templates/?systemType=ALIYUN_FINANCE')
                                .then(res => {
                                    const templateId = res.data.templateId || res.data;
                                    this.$router.push(`/template/edit/prepare?templateId=${templateId}&dynamicMark=DYNAMIC_YOZO`);
                                });
                        }
                    } else {
                        throw message;
                    }
                })
                .catch(msg => {
                    this.$MessageToast({
                        message: `服务器接口错误：${msg}`,
                    });
                }).finally(() => {
                    loading.close();
                });
        },

        handleCreateNow() {
            if (this.isGroupProxyAuthStatus) {
                this.continueText = '继续创建';
                this.unverifyDialog.visible = true;
                // this.unverifyDialog.callback = this.doCreate;
            } else {
                this.doCreate();
            }
        },
        // 创建模板
        // 注：点击创建按钮后不调用单独的接口来判断是否可以创建模板
        async doCreate() {
            const res = await this.$hybrid.offlineTip();
            if (!res) {
                return;
            }
            const loading = this.$loading();
            this.$http.post(`/template-api/templates/?systemType=${this.$store.state.commonHeaderInfo.hybridServer ? 'HYBRID_CLOUD' : 'ALIYUN_FINANCE'}`,
            )
                .then(res => {
                    const templateId = res.data.templateId || res.data;
                    this.$router.push(`/template/edit/prepare?templateId=${templateId}`);
                }).finally(() => {
                    loading.close();
                });
        },
        /**
         * @param  {Object}   file 上传的文件
         * @return {Boolean}  返回值false或reject的promise则停止上传
         * @desc
         */

        async beforeUpload(file) {
            const { hybridServer }  = this.$store.state.commonHeaderInfo;
            if (hybridServer) {
                const { data } = await this.$hybrid.makeHeader({ url: this.uploadUrl, method: 'GET', requestData: {}, isFormType: 0 });
                this.uploadHeaders = data;
            }
            this.importedNum = 0;
            return checkExcelUploadLimit(file);
        },
        uploading() {
            this.loading = this.$loading({
                text: '正在匹配模板',
            });
        },
        //  上传成功跳至模板匹配结果页面
        onUploadSuccess(res) {
            this.loading.close();
            if (res) {
                this.$router.push(`/template/match-result?resultId=${res}`);
            }
        },
        // 上传失败的提示
        onUploadError(err) {
            this.loading && this.loading.close && this.loading.close();
            this.smartMatchShow = false;
            const message = this.$store.state.commonHeaderInfo.hybridServer && err.status === 405
                ? '当前Jar包版本不支持智能匹配，请联系企业管理员升级Jar包。'
                : (this.matchErrMsg(err.message).message || '当前网络不可用，请检查你的网络设置');
            this.$alert(message, '导入失败', {
                confirmButtonText: '确定',
                customClass: 'excel-error-toast',
                lockScroll: false,
            });
        },
        // 列表选择事件
        handleSelectionChange(list) {
            this.selectedLines = list;
        },
        // 全选时显示选中的数量
        handleSelectAllContent() {
            if (!this.hasOwnedTemp) {
                return '模板名称';
            } else {
                return '全选';
            }
        },
        // 检查模板是否存在模板匹配
        checkCurrentTemplateExistMatch(templateId) {
            return this.$http.get(`/template-api/rules/is-exist-rule/${templateId}`)
                .then(res => {
                    if (res.data) {
                        return true;
                    }
                    return false;
                });
        },
        doUseTemplate(row) {
            // 开启模版组合的合同先进入选择文档页面
            this.$router.push(row.allowComposeDocument
                ? `/template/doc-select?templateId=${this.curTemplateId}` : `/template/use/prepare?templateId=${this.curTemplateId}&dynamicMark=${row.templateCategory}`);
        },
        showDynamicUpdateTip() {
            const h = this.$createElement;
            const style = {
                style: 'line-height: 26px; color: #666666;margin-bottom: 5px;',
            };
            const vNode = [
                h('p', style, this.$t('template.dynamicTemplateUpdate.newVersionDesc')),
                h('p', style, this.$t('template.dynamicTemplateUpdate.updateTip')),
                h('p', style, this.$t('template.dynamicTemplateUpdate.connectUs')),
            ];
            return this.$msgbox({
                title: this.$t('template.dynamicTemplateUpdate.title'),
                customClass: 'dynamic-update-tip',
                message: h('div', null, vNode),
                showCancelButton: true,
                confirmBtnText: '确认',
                cancelBtnText: '取消',
            });
        },
        // 处理模板事件
        async handleCommandChange(command, row) {
            if (command !== 'share') {
                const res = await this.$hybrid.offlineTip();
                if (!res) {
                    return;
                }
            }
            this.curTemplateId = row ? row.templateId : null;
            this.curTemplateName = row ? row.templateName : '';
            switch (command) {
                case 'use':
                    if (this.isGroupProxyAuthStatus || this.getAuthStatus !== 2) {
                        this.continueText = '继续使用';
                        this.unverifyDialog.visible = true;
                        this.unverifyDialog.callback = this.doUseTemplate.bind(this, row);
                    } else {
                        if (row.templateCategory === 'DYNAMIC') {
                            this.showDynamicUpdateTip().then(() => {
                                this.doUseTemplate(row);
                            }).catch(() => {});
                        } else {
                            this.doUseTemplate(row);
                        }
                    }
                    break;
                case 'edit':
                    this.$http.put(`/template-api/templates/${row.templateId}/start-edit`)
                        .then(({ data: { code, result, message } }) => {
                            if (['180050', '180023'].includes(code)) {
                                const h = this.$createElement;
                                const msg = h('div', null, [
                                    h('div', null, message),
                                    h('br'),
                                    h('p', null, `${this.$t('template.templateList.linkBoxTip')}${result})`),
                                ]);
                                return this.$confirm(msg, this.$t('templateCommon.tip'), {
                                    showCancelButton: false,
                                    dangerouslyUseHTMLString: true,
                                });
                            }
                            if (row.templateCategory === 'DYNAMIC') {
                                this.showDynamicUpdateTip().then(() => {
                                    this.$router.push(`/template/edit/prepare?templateId=${row.templateId}&dynamicMark=${row.templateCategory}`);
                                }).catch(() => {});
                            } else {
                                this.$router.push(`/template/edit/prepare?templateId=${row.templateId}&dynamicMark=${row.templateCategory}`);
                            }
                        });
                    break;
                case 'share':
                    if (row) {
                        this.getSharedMember(row.templateId);
                    } else {
                        const authorizedStaffs = this.dialogAuthorizationData.authorizedStaffs;
                        authorizedStaffs.splice(0, authorizedStaffs.length);
                    }
                    this.dialogAuthorizationData.show = true;
                    this.dialogAuthorizationData.type = 'staff';
                    break;
                case 'shareRole':
                    if (this.isGroupAdmin) {
                        // 授权集团角色
                        this.dialogAuthorizationData.show = true;
                        this.dialogAuthorizationData.type = 'groupRole';
                    } else {
                        // 授权企业角色
                        if (row) {
                            await this.getSelectedRoles(row.templateId).then(data => {
                                this.dialogAuthorizationData.authorizedRoles = data;
                            });
                        } else {
                            this.dialogAuthorizationData.authorizedRoles = [];
                        }
                        this.dialogAuthorizationData.show = true;
                        this.dialogAuthorizationData.type = 'companyRole';
                    }
                    break;
                case 'delete':
                    this.currentTemplateExistMatch = await this.checkCurrentTemplateExistMatch(row.templateId);
                    this.deleteConfirm.show = true;
                    this.deleteConfirm.shared = row.distributeToOthers;
                    break;
                case 'copy':
                    // if (row) {
                    //     this.getSharedMember(row.templateId);
                    // } else {
                    //     let authorizedStaffs = this.dialogAuthorizationData.authorizedStaffs;
                    //     authorizedStaffs.splice(0, authorizedStaffs.length);
                    // }
                    this.dialogAuthorizationData.show = true;
                    this.dialogAuthorizationData.type = 'staffCopy';
                    break;
            }
        },
        // 关闭弹窗
        handleDialogClose() {
            this.dialogAuthorizationData.show = true;
            this.dialogAuthorizationData.type = '';
        },
        handleDialogUpdate() {
            this.$emit('update');
        },
        _getUserIdByEmpId(empId) {
            const data = this.allUsers.find(v => v.empId === empId);
            return (data || {}).userId;
        },
        _getStaffAndCompanyRolePermissionAjax(templateId) {
            // 获取企业员工和角色的权限
            return this.$http.get(`/template-api/templates/${templateId}/permission`);
        },
        // 获取模板所有已授权使用人列表
        getSharedMember(tempId) {
            // 这段代码是为了获取当前企业的所有用户，然后和当前勾选上的员工 empId 匹配，获取当前勾选上的员工 userId
            let getAllUsers;
            if (this.allUsersRequested) {
                getAllUsers = Promise.resolve();
            } else {
                getAllUsers = this.$http({
                    method: 'get',
                    url: '/ents/depts',
                }).then(res => {
                    return this.$http({
                        method: 'get',
                        url: `/ents/depts/${res.data.deptId}/out-employees`,
                    });
                }).then(res => {
                    this.allUsersRequested = true;
                    this.allUsers = res.data;
                });
            }

            getAllUsers.then(() => {
                this._getStaffAndCompanyRolePermissionAjax(tempId)
                    .then(res => {
                        this.dialogAuthorizationData.authorizedStaffs = [];
                        const data = res.data.filter(v => v.userInfo && v.userInfo.grantType === 'EMPLOYEE');
                        data.forEach(item => {
                            // !! 这儿可能是一个坑，后端重构后没有了 userId，只有 grandId，代表员工id !!
                            // grandId 授权给角色的时候是roleId
                            // grandId 授权给成员的时候是empId
                            item.userId = this._getUserIdByEmpId(item.userInfo.grantId);

                            this.dialogAuthorizationData.authorizedStaffs.push({
                                userId: item.userId,
                                empId: item.userInfo.grantId,
                                permission: item.permission ? JSON.parse(item.permission) : {},
                            });
                        });
                    });
            });
        },
        // 获取模板当前企业所有已授权角色列表
        getSelectedRoles(templateId) {
            return this._getStaffAndCompanyRolePermissionAjax(templateId)
                .then(res => {
                    const data = res.data.filter(v => v.userInfo && v.userInfo.grantType === 'ROLE');
                    return data.map((item) => {
                        return {
                            roleId: item.userInfo.grantId,
                            permission: item.permission ? JSON.parse(item.permission) : {},
                        };
                    });
                });
        },
        // 是否是集团（集团授权角色和企业授权角色是两套逻辑）
        getGroupInfo() {
            return this.$http.get('/template-api/templates/permission/query/group-ent-info')
                .then(res => {
                    const { code, message } = res.data;
                    let  {  result } = res.data;
                    if (!result) {
                        result = {
                            groupAdminEnt: false,
                            groupMember: false,
                            dynamicTemplate: false,
                        };
                    }
                    if (code === '140001') {
                        this.isGroupAdmin = result.groupAdminEnt;
                        this.isGroupArchitecture = result.groupMember;
                        this.dynamicTemplateOn = result.dynamicTemplate;
                    } else {
                        throw message;
                    }
                })
                .catch(msg => {
                    this.$MessageToast({
                        message: `服务器接口错误：${msg}`,
                    });
                });
        },
        // 确认删除模板
        handleConfirmDelete() {
            this.loading = this.$loading();
            this.$http({
                method: 'delete',
                url: `/template-api/templates/${this.curTemplateId}`,
            })
                .then(({ data: { code, result, message } }) => {
                    this.loading.close();
                    if (['180050', '180023'].includes(code)) {
                        const h = this.$createElement;
                        const msg = h('div', null, [
                            h('div', null, message),
                            h('br'),
                            h('p', null, `${this.$t('template.templateList.linkBoxTip')}${result})`),
                        ]);
                        return this.$confirm(msg, this.$t('localCommon.tip'), {
                            showCancelButton: false,
                            dangerouslyUseHTMLString: true,
                        });
                    }

                    this.$MessageToast({
                        message: '删除成功',
                        iconClass: 'el-icon-ssq-qianyuewancheng',
                    });
                    this.deleteConfirm.show = false;
                    this.$emit('update');
                    this.curTemplateId = null;
                }).catch(() => {
                    this.loading.close();
                });
        },
        // 关闭确认删除弹窗
        handleCloseDeleteConfirm() {
            this.deleteConfirm.show = false;
            this.curTemplateId = null;
        },
        async handleSmartMatch() {
            if (this.templateMatchConf.hasActiveMatchRule) {
                this.smartMatchShow = true;
                return;
            }
            this.$alert('请先设置匹配规则，或联系企业管理员设置。');
        },
        async handleDoadlown() {
            try {
                await this.$http('/template-api/templates/match/must-prepared', {
                    noToast: 1,
                });
                this.smartMatchCanNotDownloadMessage = '';
            } catch (e) {
                this.smartMatchCanNotDownloadMessage = e && e.response &&  e.response.data && e.response.data.message;
            }
            if (!this.smartMatchCanNotDownloadMessage) {
                return location.href = `${location.origin}/template-api/templates/rules/generate-excel?access_token=${this.$cookie.get('access_token')}`;
            } else {
                this.smartMatchShow = false;
                this.$alert(this.smartMatchCanNotDownloadMessage);
            }
        },
        getContractTypeName(row) {
            if (row.contractTypeName) {
                return row.contractTypeName;
            }
            const contractType = (this.$refs.templateSearch.contractTypes || []).find(v => v.folderId === row.contractTypeId);
            return (contractType || {}).folderName || '未分类';
        },
        generateSendCode(templateId) {
            return this.$http.post(`/template-api/v2/template/${templateId}/generate-send-code`, {}).then(res => {
                const { success, token, hasNoBlankOrBillDoc, checkPlaceHolderRoleCondition, hasNoEmptyAccountForSpecificUser, hasNoApprovalBeforeSend, hasNoNecessaryForSenderFill, hasNoDocGroup } = res.data;
                if (success) {
                    return this.$emit('updateList', this.listData.map(e => {
                        if (e.templateId === templateId) {
                            return {
                                ...e,
                                templateSendCodeInfo: {
                                    open: true,
                                    token,
                                },
                            };
                        }
                        return e;
                    }));
                }
                const h = this.$createElement;
                const common = (v) => ({
                    style: {
                        margin: '4px 0',
                        color: !v ? 'red' : '#333333',
                    },
                });
                const message = h('ul', {
                    style: {
                        listStyle: 'disc',
                        marginTop: '20px',
                    },
                }, [
                    h('li', common(hasNoBlankOrBillDoc), this.$t('template.sendCode.fail.1')),
                    h('li', common(checkPlaceHolderRoleCondition), this.$t('template.sendCode.fail.2')),
                    h('li', common(hasNoEmptyAccountForSpecificUser), this.$t('template.sendCode.fail.3')),
                    h('li', common(hasNoApprovalBeforeSend), this.$t('template.sendCode.fail.4')),
                    h('li', common(hasNoNecessaryForSenderFill), this.$t('template.sendCode.fail.5')),
                    h('li', common(hasNoDocGroup), this.$t('template.sendCode.fail.6')),
                ]);
                this.$alert(message, this.$t('template.sendCode.tip'), {
                    confirmButtonText: '确定',
                    dangerouslyUseHTMLString: true,
                });
            });
        },
        closeSendCode(templateId) {
            return this.$http.post(`/template-api/v2/template/
            ${templateId}/send-code/close`, {}, { noToast: 1 }).then(() => {
                this.$emit('updateList', this.listData.map(e => {
                    if (e.templateId === templateId) {
                        return {
                            ...e,
                            templateSendCodeInfo: {
                                open: false,
                                token: null,
                            },
                        };
                    }
                    return e;
                }));
            });
        },
        downloadSendCode(templateId, templateName) {
            const img = document.getElementById('send-code-' + templateId).querySelector('img');
            const el = document.createElement('a');
            el.href = img.getAttribute('src');
            el.download = `${templateName}`;
            const event = new MouseEvent('click');
            el.dispatchEvent(event);
        },
        templateSendCodeView(useable, templateId) {
            if (useable) {
                this.sendCodeViewVisible[templateId] = !this.sendCodeViewVisible[templateId];
                setTimeout(() => {
                    const listener = () => {
                        this.sendCodeViewVisible[templateId] = false;
                        document.removeEventListener('click', listener);
                    };
                    document.addEventListener('click', listener);
                }, 0);
                return;
            }
            this.$MessageToast.error('错误的权限操作');
        },
    },
    created() {
        // 如果是单点登录
        if (this.$store.state.commonHeaderInfo.extendFields.isLoginFromDeveloper) {
            // 判断当前用户是否属于开发者企业/集团子公司
            this.$http.get('/ents/belong-to-developer').then(res => {
                this.ssoParams.developerName = res.data.developerName || '';
                // 用户不属于开发者 企业/集团子公司
                if (!res.data.ifBelong) {
                    this.ssoParams.visible = true;
                } else {
                    this.getGroupInfo();
                }
            }).catch(() => {
                this.getGroupInfo();
            });
        } else {
            this.getGroupInfo();
        }
    },
    beforeMount() {
        this.ssoTmps = this.getSsoConfig.tmps || {};
    },
};
</script>

<style lang="scss">
    .el-message-box__btns{
        overflow: auto;
        .el-button{
            float: right;
            margin-left: 10px;
        }
    }
    .template-container{
        .template-button-line {
            position: relative;
            padding-left: 20px;
            height: 55px;
            line-height:55px;
            border-bottom: 1px solid #bebebe;
            button {
                min-width: 60px;
                &:nth-child(2) {
                    margin-right: 24px;
                }
            }
            .upload-btn {
                display: inline-block;
                position: relative;
                font-size: 14px;
                cursor: pointer;
                &:hover span {
                    display: block;
                }
            }
            .new-function-icon {
                position: absolute;
                top: 2px;
                width: 48px;
                height: 30px;
            }
            .template-button-description {
                border: none;
                color: #127FD2;
                font-size: 12px;
                display: inline-block;
                margin-left: 10px;
            }
            .template-title-right{
                position: absolute;
                top: 0;
                right: 18px;
                height: 55px;
                line-height: 55px;
                font-size: 12px;
                a{
                    vertical-align: middle;
                    color: #2298f1;
                    display: inline-block;
                    line-height: 24px;
                }
                .operate-video{
                    padding-right: 10px;
                    margin-right: 10px;
                }
                span{
                    vertical-align: middle;
                    font-weight: normal;
                }
                i{
                    vertical-align: middle;
                    font-size: 16px;
                    vertical-align: middle;
                }
            }
        }

        .template-table-container{
            position: relative;
            .template-table-opeartionLine{
                z-index: 10;
                position: absolute;
                left: 50px;
                top: 1px;
                transition: left 0.3s;
                width: calc(100% - 50px);
                height: 40px;
                line-height: 40px;
                background: #f6f6f6;
                font-size: 12px;
                .template-table-opeartionLine-info {
                    display: inline-block;
                    margin-right: 6px;
                }
                .el-button{
                    padding: 0 20px;
                    margin-right: 5px;
                    height: 28px;
                    border-color: #6dbdf9;
                    border-radius: 1px;
                    background: #e4f3ff;
                    color: #333;

                    &:hover{
                        color: #1687dc;
                    }

                    &.is-disabled{
                        border-color: #d1e4f3;
                        background: #e4f3ff;
                        color: #bcd5e8;
                    }
                }
            }
            table {
                width: 100% !important;
            }
        }

        .ssq-table.el-table.template-list-table{
            border-top-color: #ddd;

            .el-table__header-wrapper{
                th>div{
                    padding-left: 22px;
                    padding-right: 22px;
                }
                th:first-child{
                    .cell{
                        text-align: left;
                    }
                }
                th:nth-child(2){
                    .cell{
                        margin-left: -18px;
                    }
                }
            }

            .el-table__body-wrapper{
                .el-table__row{
                    td{
                        .cell{
                            padding: 11px 22px;
                            line-height: 18px;

                            .el-icon-ssq-mobantubiao{
                                margin-right: 10px;
                                font-size: 18px;
                                color: #888;
                                vertical-align: middle;
                            }

                            .template-list-table-name{
                                vertical-align: middle;
                            }
                        }

                        &:first-child{
                            .cell{
                                font-weight: bold;
                            }
                        }

                        &:nth-child(2){
                            .cell{
                                margin-left: -18px;
                            }
                        }

                        &:last-child{
                            .cell{
                                padding-right: 18px;
                                text-align: right;
                            }
                        }
                    }

                }
                .template-list-table-send-code {
                    color: $theme-color;
                    span {
                        margin-right: 4px;
                        cursor: pointer;
                        &.grey {
                            color: #DDDDDD;
                        }
                    }
                }
            }
        }

        .company-full-tree-dialog{
            * {
                box-sizing: border-box;
            }
        }

        .delete-temp-dialog{
            .el-dialog{
                width: 360px;
            }
        }

        .approval-flow-status{
            color: #bbb;
        }
        .switch-btn {
            margin-right: 10px;
            vertical-align: middle;
            .chooseBtn {
                display: none;
            }
            .choose-label {
                width: 31px;
                height: 12px;
                display: inline-block;
                border-radius: 20px;
                position: relative;
                background-color: #989898;
            }

            .choose-label:before {
                cursor: pointer;
                box-shadow: #ccc -1px 2px 3px 1px;
                content: '';
                position: absolute;
                left: 0;
                top: -2px;
                width: 16px;
                height: 16px;
                display: inline-block;
                border-radius: 20px;
                background-color: #fff;
                z-index: 20;
                -webkit-transition: all 0.5s;
                transition: all 0.5s;
            }
            .chooseBtn:checked + label.choose-label:before {
                left: 20px;
                background-color: #2298f1;
            }
            .chooseBtn:checked + label.choose-label {
                background-color: #aadaff;
            }
        }
        .popover_type-one_bg {
            .img_con {
                position: fixed;
                width: 94px;
                img {
                    width: 100%;
                }
            }
        }
    }
    .smart-match-dialog {
        .el-dialog {
            width: 417px;
        }
        &.delete-temp-dialog-large {
            .el-dialog {
                width: 450px;
            }
        }
        .el-dialog__body {
            padding: 22px 100px !important;
            background: #ffffff !important;
        }
        .upload-btn {
            background: #FBFBFB;
            border: 1px dashed #E1E1E1;
            text-align: center;
            padding-top: 26px;
            padding-bottom: 16px;
            margin-bottom: 20px;
            img {
                height: 42px;
                margin-bottom: 20px;
            }
            p {
                font-size: 14px;
                color: #127FD2;
            }
            &:hover, &:active {
                border: 1px solid #127FD2;
            }
        }
        .download {
            font-size: 12px;
            color: #333333;
            line-height: 17px;
            text-align: center;
            margin-bottom: 10px;
            span {
                color: #127FD2;
            }
        }
    }
    .template-pagination {
        text-align: right;
        margin-top: 15px;
        margin-right: 10px;
    }
    .dynamic-config-dialog {
        width: 460px;
        /*height: 230px;*/
        box-shadow: 0 0 8px 0 rgba(142,142,142,0.50);
        border-radius: 8px;
        padding: 35px 22px 33px 33px;
        .el-dialog__header {
            padding: 0;
            .el-dialog__title {
                font-size: 13px;
                font-weight: 600;
                color: #333;
            }
        }
        .el-dialog__body {
            font-size: 12px;
            padding: 10px 0 20px;
        }
        .el-dialog__footer {
            padding: 0 11px;
            .tip-text {
                font-size: 12px;
                color: #333;
            }
            .el-button {
                height: 30px;
                line-height: 30px;
                padding: 0 10px;
                margin-left: 5px;
            }
        }
    }
    .dynamic-update-tip {
        width: 500px;
    }
    .template-send-code-popover {
        text-align: center;
        img {
            width: 200px;
            height: 200px;
        }
        p {
            color: $theme-color;
            margin: 4px auto;
            cursor: pointer;
        }
        .copy-link {
            .el-input__inner {
                padding: 0 10px;
                font-size: 14px;
            }
            .copy-btn {
                color: $theme-color;
            }
        }
    }
</style>
