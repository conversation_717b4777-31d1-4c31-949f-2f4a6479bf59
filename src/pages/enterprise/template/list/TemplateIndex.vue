<template>
    <div class="template-container template-index">
        <div class="template-container-title">
            <h2>
                <span class="template-container-titleContent">全部模板</span>
            </h2>
        </div>
        <div class="template-index-contentBlock">
            <h3>
                批量发送制式合同
            </h3>
            <div class="template-index-content">
                <div class="index-content-item">
                    <div class="index-content-item-thum">
                        <img src="../images/step1.png" height="73" width="62" alt="">
                    </div>
                    <h4>上传合同</h4>
                    <p>这是您将会多次使用的合同</p>
                </div>
                <div class="index-content-item index-content-item-symbol">
                    +
                </div>
                <div class="index-content-item">
                    <div class="index-content-item-thum">
                        <img src="../images/step2.png" height="73" width="76" alt="">
                    </div>
                    <h4>添加接收方</h4>
                    <p>添加固定的接收方或者接收方类型</p>
                </div>
                <div class="index-content-item index-content-item-symbol">
                    +
                </div>
                <div class="index-content-item">
                    <div class="index-content-item-thum">
                        <img src="../images/step3.png" height="72" width="59" alt="">
                    </div>
                    <h4>添加字段</h4>
                    <p>放置需要添加的内容字段</p>
                </div>
                <div class="index-content-item index-content-item-symbol">
                    =
                </div>
                <div class="index-content-item">
                    <div class="index-content-item-thum">
                        <img src="../images/step4.png" height="72" width="59" alt="">
                    </div>
                    <h4>模板创建成功</h4>
                    <p>可多次重复使用</p>
                </div>
            </div>
            <div>
                <el-button class="btn-type-one" id="template-index-create" @click="handleCreateNow" v-if="couldCreate">立即创建模板</el-button>
            </div>
            <div class="video-line">
                <a
                    class="operate-video"
                    href="javascript:void(0)"
                    @click="videoVisible = !videoVisible"
                >
                    <i class="el-icon-ssq-shipinyaofang"></i>
                    <span>操作演示</span>
                </a>
            </div>
        </div>

        <UnverifyConfirmDialog
            :params="unverifyDialog"
            @close="unverifyDialog.visible = false"
            :isTemplate="true"
            :isGroupProxyAuthStatus="isGroupProxyAuthStatus"
        ></UnverifyConfirmDialog>

        <VideoDialog
            title="操作演示"
            :visible.sync="videoVisible"
            videoPath="https://download.bestsign.cn/video/%E6%A8%A1%E7%89%88%E6%93%8D%E4%BD%9C%E6%BC%94%E7%A4%BA.mp4"
        >
        </VideoDialog>
    </div>
</template>

<script>
import VideoDialog from '../common/VideoDialog.vue';
import UnverifyConfirmDialog from 'common_pages/home/<USER>';
import { mapGetters } from 'vuex';

export default {
    components: {
        VideoDialog,
        UnverifyConfirmDialog,
    },
    data() {
        return {
            videoVisible: false,
            unverifyDialog: {
                visible: false,
                // callback: this.doCreate
            },
            isGroupProxyAuthStatus: this.$store.state.commonHeaderInfo.enterprises[0].isGroupProxyAuthStatus,
        };
    },
    computed: {
        ...mapGetters(['getAuthStatus']),
        couldCreate() {
            return this.$store.getters.getUserPermissons.createTemp;
        },
    },
    methods: {
        handleCreateNow() {
            if (this.getAuthStatus !== 2 || this.isGroupProxyAuthStatus) {
                this.unverifyDialog.visible = true;
            } else {
                this.doCreate();
            }
        },
        async doCreate() {
            const res = await this.$hybrid.offlineTip();
            if (!res) {
                return;
            }

            const loading = this.$loading();
            this.$http.post(`/template-api/templates/?systemType=${this.$store.state.commonHeaderInfo.hybridServer ? 'HYBRID_CLOUD' : 'ALIYUN_FINANCE'}`,
            )
                .then(res => {
                    const templateId = res.data.templateId || res.data;
                    this.$router.push(`/template/edit/prepare?templateId=${templateId}`);
                }).finally(() => {
                    loading.close();
                });
        },
    },
};
</script>

<style lang="scss">
	.template-container.template-index{
		position: relative;

		.template-container-title{
			position: relative;
			border-bottom: 1px solid #ddd;
            z-index: 1;
            background: #fff;
		}

		.template-index-contentBlock{
			position: absolute;
			left: 0;
			top: 105px;
			width: 100%;
			text-align: center;

			h3{
				margin-bottom: 50px;
				font-size: 18px;
				color: #000;
			}

			.template-index-content{
				margin-bottom: 80px;

				.index-content-item{
					display: inline-block;
					margin-right: 60px;

					.index-content-item-thum{
						width: 123px;
						height: 123px;
						margin: 0 auto;
						margin-bottom: 15px;
						line-height: 123px;
						border-radius: 50%;
						background: #f1f7fc;

						img{
							vertical-align: middle;
						}
					}

					h4{
						margin-bottom: 12px;
						font-size: 14px;
						color: #000;
					}
					p{
						font-size: 12px;
						color: #666;
					}

					&.index-content-item-symbol{
						height: 185px;
						line-height: 150px;
						font-size: 40px;
						color: #2390e2;
						vertical-align: top;
					}
				}
			}

			.btn-type-one{
				padding: 0 35px;
				height: 45px;
			}

			.video-line{
				margin-top: 20px;
				a{
					color: #2298f1;

					i{
						font-size: 18px;
                        vertical-align: text-bottom;
					}
				}
			}
		}
	}
</style>
