<template>
    <div class="template-search-wrap">
        <div class="template-search-item" v-if="cascaderTree.length !== 0">
            <span class="search-key">创建人</span>
            <div class="search-value">
                <PopperCascader
                    :size="size"
                    :data="cascaderTree"
                    clearable
                    @keyup.native.enter="searchHandler"
                    @updateData="setCreateUsers"
                />
            </div>
        </div>
        <div class="template-search-item">
            <span class="search-key">合同类型</span>
            <div class="search-value">
                <el-select
                    :size="size"
                    clearable
                    v-model="params.contractTypeId"
                    @keyup.native.enter="searchHandler"
                    popper-class="contract-type-select"
                    placeholder="请选择"
                >
                    <el-option
                        v-for="item in contractTypes"
                        :key="item.folderId"
                        :label="item.folderName"
                        :value="item.folderId"
                    >
                    </el-option>
                </el-select>
            </div>
        </div>
        <div class="template-search-item">
            <span class="search-key">模板名称</span>
            <div class="search-value">
                <el-input
                    :size="size"
                    v-model="params.templateName"
                    placeholder="请输入"
                    @keyup.native.enter="searchHandler"
                    :on-icon-click="resetTemplateName"
                    :icon="params.templateName.length > 0 ? 'circle-close' : ''"
                ></el-input>
            </div>
        </div>
        <div class="template-search-item">
            <span class="search-key">模板编号</span>
            <div class="search-value">
                <el-input
                    :size="size"
                    v-model="params.templateId"
                    placeholder="请输入"
                    :maxlength="19"
                    @keyup.native.enter="searchHandler"
                    :on-icon-click="resetTemplateId"
                    :icon="params.templateId.length > 0 ? 'circle-close' : ''"
                ></el-input>
            </div>
        </div>
        <div class="template-search-item">
            <span class="search-key"></span>
            <div class="search-value">
                <el-button :size="size" class="search-btn" type="primary" @click="searchHandler">搜索</el-button>
            </div>
        </div>
    </div>
</template>

<script>
import PopperCascader from 'components/popperCascader/PopperCascader.vue';
export default {
    components: {
        PopperCascader,
    },
    props: {
        size: {
            type: String,
            default: 'small',
        },
    },
    data() {
        return {
            params: {
                contractTypeId: '',
                templateName: '',
                templateId: '',
                createUsers: [],
            },
            contractTypes: [],
            cascaderTree: [],
        };
    },
    methods: {
        getContractTypes() {
            this.$loading();
            this.$http.get('/template-api/contract-types/with-auth')
                .then(({ data }) => {
                    this.contractTypes = data;
                }).catch(error => {
                    console.error(error);
                }).finally(() => {
                    this.$loading().close();
                });
        },
        resetTemplateName() {
            this.params.templateName = '';
        },
        resetTemplateId() {
            this.params.templateId = '';
        },
        getCreator() {
            const formatDataStructure = (data) => {
                const result = data.map(ent => {
                    const children = ent.employeeCreators.map(v => ({
                        name: v.empName,
                        value: JSON.stringify(v),
                    }));
                    const result = {
                        name: ent.entName,
                        value: ent.entId,
                    };
                    if (children && children.length > 0) {
                        result.children = children;
                    }
                    return result;
                });
                return result.filter(v => v.children);
            };
            /* eslint-disable */
                this.$http.get(`/template-api/templates/creator`)
                    .then(({ data }) => {
                        this.cascaderTree = formatDataStructure(data.result);
                    })
                    .catch(error => {
                        console.error(error);
                    });
            },
            setCreateUsers(data) {
                this.params.createUsers = data.filter(v => !v.children).map(v => JSON.parse(v.value));
            },
            searchHandler() {
                if (this.params.templateId.match(/\D/)) {
                    this.$MessageToast.error('模板编号只能含有数字！');
                    return;
                }
                this.$emit('doSearch', this.params);
            }
        },
        created() {
            this.getContractTypes();
            this.getCreator();
        },
    };
</script>

<style lang="scss" scoped>
    .template-search-wrap {
        padding: 20px;
        font-size: 12px;
        white-space: nowrap;
        .template-search-item {
            vertical-align: bottom;
            display: inline-block;
            margin-right: 60px;
            &:last-child {
                margin-right: 0;
            }
            .search-value {
                margin-top: 6px;
                width: 194px;
            }
        }
    }
</style>
<style lang="scss">
.template-search-wrap .popper-cascader-wrap .cascader-item,
.contract-type-select .el-select-dropdown__item {
    font-size: 12px;
}
</style>
