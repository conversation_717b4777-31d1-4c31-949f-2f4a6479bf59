<template>
    <div
        class="template-list-btnCon"
        @mouseover="tooltipShow = true"
        @mouseleave="tooltipShow = false"
    >
        <el-dropdown
            split-button
            trigger="click"
            @click="handleClick"
            @command="handleCommand"
            size="small"
            @visible-change="visibleChange"
            class="blue"
            :class="[expandedClass, isSigleBtn]"
            :disabled="true"
        >
            <el-tooltip
                v-if="dropdownContent === ''"
                popper-class="template-list-btnCon__tooltip"
                content="该模版仍在编辑中，如需使用请联系模板创建人。"
                :value="tooltipShow"
                :manual="true"
            >
                <span class="option-btn">
                    {{ dropdownContent ? dropdownContent : '编辑' }}
                </span>
            </el-tooltip>
            <span
                v-else
                class="option-btn"
            >
                {{ dropdownContent ? dropdownContent : '编辑' }}
            </span>
            <el-dropdown-menu slot="dropdown" class="doc-cloumn-dropdown template-list-dropdown" v-if="expandVisible">
                <el-dropdown-item command="edit" v-if="row.templatePermission.editable && dropdownContent != '编辑'">编辑</el-dropdown-item>
                <el-dropdown-item command="share" v-if="row.templateStatus && row.templatePermission.shareable && isGroupAdminCreated(row) && dropdownContent != '授权成员'">授权成员</el-dropdown-item>
                <el-dropdown-item command="shareRole" v-if="row.templateStatus && row.templatePermission.shareable && dropdownContent != '授权角色' && checkFeat.roleManage">授权角色</el-dropdown-item>
                <el-dropdown-item command="delete" v-if="row.templatePermission.deletable && dropdownContent != '删除'">删除</el-dropdown-item>
                <!-- judgeCurrentIsCreator(row)  -->
                <el-dropdown-item
                    command="copy"
                    v-if="row.templatePermission.copyable && dropdownContent != '编辑'"
                >
                    复制
                </el-dropdown-item>
            </el-dropdown-menu>
        </el-dropdown>
        <!--
        <div class="template-btn-hover" v-if="dropdownContent === ''">
            该模版仍在编辑中，如需使用请联系模板创建人。
        </div> -->
    </div>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
    // eslint-disable-next-line
    props: ['row', 'isGroupAdmin', 'expandVisible', 'isCreator', 'templateMatchConf'],
    data() {
        return {
            defaultCommand: null,
            expandedClass: '',
            tooltipShow: false,
        };
    },
    computed: {
        ...mapGetters(['getCurrentEntInfo', 'getUserAccount', 'checkFeat']),
        // 是否开启了模板匹配功能
        hasActiveTemplateMatch() {
            return this.checkFeat.templateIntelligentMatch;
        },
        // 是否开启了模板匹配-单独使用模版功能
        hasUseAlone() {
            return this.templateMatchConf.hasUseAlone;
        },
        dropdownContent() {
            let content = '';
            if (!this.row.templateStatus) {
                if (this.row.templatePermission.editable === true) {
                    // eslint-disable-next-line
                    this.defaultCommand = 'edit';
                    return content = '编辑';
                } else if (this.row.templatePermission.deletable === true) {
                    // eslint-disable-next-line
                    this.defaultCommand = 'delete';
                    return content = '删除';
                }
                return content;
            }
            if ((!this.hasActiveTemplateMatch &&
                this.row.templatePermission.useable === true &&
                this.row.editInfo.usable === true)                ||
                (this.hasActiveTemplateMatch && this.hasUseAlone && this.row.editInfo.usable === true)) {
                // eslint-disable-next-line
                this.defaultCommand = 'use';
                return content = '使用';
            } else if (this.row.templatePermission.editable === true) {
                // eslint-disable-next-line
                this.defaultCommand = 'edit';
                return content = '编辑';
            }

            if (this.row.templatePermission.shareable === true) {
                // eslint-disable-next-line
                this.defaultCommand = 'share';
                // 是管理员但是不是管理员创建的，只显示授权角色
                if (!this.isGroupAdminCreated(this.row)) {
                    // eslint-disable-next-line
                    this.defaultCommand = 'shareRole';
                    return '授权角色';
                }
                return content = '授权成员';
            }

            if (this.row.templatePermission.deletable === true) {
                // eslint-disable-next-line
                this.defaultCommand = 'delete';
                return content = '删除';
            }

            return content;
        },
        // 判断如果下拉菜单没有选项则显示单个按钮
        isSigleBtn() {
            return this.expandVisible ? '' : 'cloumn-btn-sigle';
        },
    },
    methods: {
        handleClick() {
            this.$emit('commandChange', this.defaultCommand, this.row);
        },
        handleCommand(command) {
            this.$emit('commandChange', command, this.row);
        },
        visibleChange(expand) {
            this.expandedClass = expand ? 'expanded' : '';
        },
        // 是集团管理员的话，得是他自己的模板才能看到授权成员
        isGroupAdminCreated(row) {
            const currentEntId = this.$store.state.commonHeaderInfo.currentEntId;
            const currentUserId = this.$store.getters.getUserId;

            const templateCreatedByMe = row.createUserId === currentUserId && row.createEnterpriseId === currentEntId;
            if (this.isGroupAdmin) {
                return templateCreatedByMe;
            }
            return true;
        },
        // 当前身份是否是该模版的创建人
        // judgeCurrentIsCreator(row) {
        //     const {
        //         createUserInfo: {
        //             createUserAccount,
        //             createEnterpriseName,
        //         },
        //     } = row;
        //     return this.getCurrentEntInfo.fullEntName === createEnterpriseName && this.getUserAccount === createUserAccount;
        // },
    },
};
</script>
<style lang="scss">
.doc-cloumn-dropdown {
    padding: 0;
    margin-top: -4px;
    font-size: 12px;
    color: #666;
    border: 1px solid #ddd;
    border-radius: 1px;
    box-shadow: none;

    li.el-dropdown-menu__item{
        // padding-left: 11px;
        padding:0 0 0 10px;
        height: 28px;
        line-height: 28px;
        color: #666;

        &:not(.is-disabled):hover{
            background-color: #f6f6f6;
        }
    }
}
.doc-cloumn-dropdown-none {
    display: none;
}
.template-list-btnCon__tooltip {
    width: 150px;
}
    .template-list-btnCon{
        position: relative;

        // .template-btn-hover{
        //     display: none;
        // }

        // &:hover{
        //     .template-btn-hover{
        //         display: block;
        //         position: absolute;
        //         left: 0;
        //         top: -8px;
        //         padding: 5px 14px 5px;
        //         text-align: left;
        //         line-height: 14px;
        //         z-index: 1;
        //         color: #fff;
        //         border-radius: 2px;
        //         background: #333;
        //     }
        // }
        .option-btn {
            display: inline-block;
            width: 50px;
        }

        /* 无下拉菜单，单个按钮 */
        .cloumn-btn-sigle{
            margin-right: 28px;
            .el-dropdown__caret-button{
                display: none;
            }
        }
    }
    .template-list-dropdown {
        width: 114px;
    }
</style>
