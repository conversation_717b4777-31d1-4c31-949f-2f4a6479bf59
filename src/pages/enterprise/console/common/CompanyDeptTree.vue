<!-- 部门架构树+成员搜索 -->
<template>
    <div class="dept-tree">
        <div v-if="showMemberSearch" @keyup.enter.prevent.stop="handleSearchMember">
            <el-input :placeholder="$t('CSMembers.searchTip')" size="small" class="ssq-search department-search" @blur="handleSearchMember" v-model.trim="memberAccount">
                <el-button
                    slot="prepend"
                    icon="search"
                    size="small"
                    class="department-search-btn"
                    id="department-search-btn"
                    @click="handleSearchMember"
                ></el-button>
            </el-input>
        </div>

        <el-tree
            :data="departmentData"
            :props="defaultProps"
            :check-strictly="selectAble"
            @node-click="handleNodeClick"
            @node-expand="handleNodeExpand"
            @check-change="handleCheckChange"
            :highlight-current="true"
            node-key="deptId"
            :current-node-key="expandedKeys[0]"
            :default-expanded-keys="expandedKeys"
            class="ssq-tree dept-tree"
            :show-checkbox="selectAble"
            ref="companyDeptTree"
        >
        </el-tree>
    </div>

</template>

<script>
export default {
    props: {
        selectAble: {
            type: Boolean,
            default: false,
        },
        showMemberSearch: {
            type: Boolean,
            default: true,
        },
        showDeptSearch: {
            type: Boolean,
            default: false,
        },
        companySelectAble: {
            type: Boolean,
            default: true,
        },
        onlyActive: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            expandedKeys: [],
            // 组织架构树数据
            departmentData: [

            ],
            // 设置子节点和节点名称的参数名
            defaultProps: {
                children: 'childDeptVOS',
                label: 'deptName',
            },
            memberAccount: '',
            curDept: null,
        };
    },
    methods: {

        /**
         * * 获取所有部门列表
         * * option.type 用于在成员管理页判断是否需要获取部门成员
         * * 如果 type = update 则只更新当前部门的数据
         * @param  option {type: 'init','update'}
         *
         */
        getDepartments(option) {
            this.$http({
                method: 'get',
                url: '/ents/depts',
            }).then((res) => {
                // 设置根节点的 node-key 为 1，默认展开
                // res.data.id = 1;
                // this.rootKey.push(res.deptId);

                // 设置根节点的 disabled 为 true，则禁用选中
                res.data.disabled = !this.companySelectAble;

                res.data.type = option.type;

                this.departmentData = [];
                this.departmentData.push(res.data);

                this.expandedKeys = [];
                this.expandedKeys.push(res.data.deptId);

                if (option.expandedKey) {
                    this.expandedKeys.push(option.expandedKey);
                }

                // console.log(this.$refs.companyDeptTree.filter(option.expandedKey));

                this.curDept = res.data;
                this.$emit('setTopDeptId', res.data.deptId);
                this.$emit('getCurrentDept', res.data);
                this.$emit('getWholeCompanyDept', res.data);
            })
                .catch(() => {

                });
        },
        // 搜索成员，并将数据返回给父组件
        handleSearchMember() {
            const searchAllPath = `/ents/employees/search?searchContent=${this.memberAccount}&deptId=${this.curDept.deptId}`;
            const searchActivePath = `/ents/employees/active/search?searchContent=${this.memberAccount}&deptId=${this.curDept.deptId}`;
            const searchPath = this.onlyActive ? searchActivePath : searchAllPath;
            if (this.memberAccount === '') {
                this.$emit('getCurrentDept', this.curDept);
            } else {
                this.$http.get(searchPath)
                    .then(res => {
                        this.$emit('seachResult', {
                            keyword: this.memberAccount,
                            data: res.data,
                        });
                    });
            }
            return false;
        },
        // 点击节点时，父组件可以获取当前节点的数据
        handleNodeClick(data) {
            this.curDept = data;
            this.$emit('getCurrentDept', data);
        },
        // 展开节点，返回当前节点的数据
        handleNodeExpand() {
            // this.$emit('getCurrentDept', data);

        },
        // 选中节点时，父组件可以获取当前节点的数据
        handleCheckChange(data, checked) {
            if (!this.companySelectAble && data.deptLevel === 1) {
                return false;
            }

            data.checked = checked;

            if (checked === true) {
                this.$emit('getCheckedDept', data);
            } else {
                this.$emit('getDisCheckDept', data);
            }
            // if (data.deptLevel != 1){
            // data.checked = checked;

            // }
        },
        // 通过 deptId[] 选择节点
        handleCheckNode(list) {
            this.$refs.companyDeptTree.setCheckedKeys(list);
        },
        // 通过 deptId 取消选择节点
        handleUnCheckNode(key) {
            this.$refs.companyDeptTree.setChecked(key, false);
        },
    },
    beforeMount() {
        this.getDepartments({ type: 'init' });
    },
};
</script>
<style lang="scss">
    .el-tree.dept-tree{
        overflow: hidden;
        .el-tree-node__content{
            height: unset;
            .el-tree-node__label::before{
                height: 0;
            }
        }
        .el-tree-node__label{
            white-space: pre-wrap;
            padding: 10px 25px 10px 0;
            padding-right: 25px;
            line-height: 20px;
        }
        .el-tree-node__expand-icon{
            border-width: 5px;
        }
    }
</style>
