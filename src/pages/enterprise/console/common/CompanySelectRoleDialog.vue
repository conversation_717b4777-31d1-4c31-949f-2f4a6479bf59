<template>
    <el-dialog
        :title="dialogTitle || $t('CSCommon.chooseRole')"
        class="company-select-role-dialog el-dialog-bg console-dialog-bg"
        v-show="state"
        :visible.sync="state"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :before-close="handleClose"
    >
        <el-form @submit.native.prevent :model="companySelectRoleForm" ref="companySelectRoleForm">
            <!-- 模板授权tab -->
            <slot name="templateAuthorizationTab"></slot>
            <div class="role-block role-list-block">
                <p>{{ $t('CSCommon.chooseRole') }}：</p>
                <div class="cloumn role-cloumn">
                    <!-- 角色列表 -->
                    <div class="role-list-cloumn">
                        <el-checkbox-group
                            v-model="companySelectRoleForm.selectedRoleIds"
                        >
                            <p v-for="(role, index) in roleList" :key="index">
                                <el-checkbox :label="role.roleId" :key="role.roleId">{{ role.name }}</el-checkbox>
                            </p>
                        </el-checkbox-group>
                    </div>
                </div>
            </div>
            <!-- 已选角色列表 -->
            <div class="role-block selected-block">
                <p>{{ $t('CSCommon.choosedRole') }}：</p>
                <div class="cloumn selected-cloumn">

                    <p v-for="(role, index) in companySelectRoleForm.selectedRoles" :key="index">
                        <i class="el-icon-ssq-user-filling"></i>
                        <span>{{ (role || {}).name }}</span>
                        <i class="el-icon-ssq-delete" @click="cancelSelectedRole(index)"></i>
                        <i class="clear"></i>
                    </p>

                </div>
            </div>
            <div class="clear"></div>
            <slot name="authDuration"></slot>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button
                class="console-btn console-btn-primary"
                @click="chooseRoles"
                type="primary"
            >{{ $t('field.confirm') }}</el-button>

            <!-- :disabled="companySelectRoleForm.selectedRoles.length == 0 || companySelectRoleForm.selectedRoles[0] == undefined" -->
        </div>
    </el-dialog>
</template>

<script>
import { stringLangTransformMixin } from 'src/mixins/stringLangTransform';

export default {
    mixins: [stringLangTransformMixin],
    props: {
        state: {
            type: Boolean,
            required: true,
        },
        selectType: {
            type: String,
            default: 'checkBox',
        },
        dialogTitle: {
            type: String,
            default: '',
        },
        showAdmin: {
            type: Boolean,
            default: true,
        },
        // eslint-disable-next-line
        selectedValue: Array,
    },
    data() {
        return {
            roleList: [],
            companySelectRoleForm: {
                selectedRole: null,
                // 已选角色 roleId 数组
                selectedRoleIds: [],
                // 已选角色对象数组
                selectedRoles: [],
            },
        };
    },
    watch: {
        'selectedValue': {
            handler() {
                this.watchSelectedValue();
            },
            deep: true,
            immediate: true,
        },
        'companySelectRoleForm.selectedRoleIds': {
            handler(val) {
                this.watchSelectRole();
                this.$emit('selectedDataChange', {
                    companyRoleData: val,
                    type: 'company',
                });
            },
            deep: true,
            immediate: true,
        },
    },
    methods: {
        // 获取角色列表
        getRoleList() {
            this.$http({
                method: 'get',
                url: '/ents/roles',
            }).then((res) => {
                const roleList = [];
                res.data = this.handleRoleNameTransform(res.data, ['主管理员', '员工'], [this.$t('CSCommon.admin'), this.$t('CSCommon.staff')]);

                res.data.forEach(role => {
                    if ((this.showAdmin === true) || (this.showAdmin === false && role.name !== this.$t('CSSetting.admin'))) {
                        roleList.push(role);
                    }
                });

                this.roleList = roleList;
                this.watchSelectRole();
            })
                .catch(() => {

                });
        },
        // 检测父组件传入的已选角色的值发生变化，同步角色 roleId 数组 selectedRoleIds
        watchSelectedValue() {
            if (this.selectedValue) {
                this.selectedValue.forEach((item) => {
                    let id = item;
                    if (typeof item === 'object') {
                        id = item.roleId;
                    }
                    this.companySelectRoleForm.selectedRoleIds.push(id);
                });
            }
        },
        // 检测 roldId 数组 selectedRoleIds 发生变化，同步修改角色对象数组 selectedRoles
        watchSelectRole() {
            const curRole = [];
            this.companySelectRoleForm.selectedRoles = [];
            this.companySelectRoleForm.selectedRoleIds.forEach((id) => {
                const filterVal = this.roleList.filter((role) => {
                    return role.roleId === id;
                });
                if (filterVal[0]) {
                    curRole.push(filterVal[0]);
                }
            });

            this.companySelectRoleForm.selectedRoles = curRole;
        },
        // 取消选择已选角色
        cancelSelectedRole(index) {
            // 如果是 checkBox 则将 selectedRoles 中对应的角色删除
            this.companySelectRoleForm.selectedRoles.splice(index === 0 ? 0 : index, 1);
            this.companySelectRoleForm.selectedRoleIds.splice(index === 0 ? 0 : index, 1);
        },
        resetValues() {
            // 重置表单
            this.companySelectRoleForm.selectedRoles = [];
            this.companySelectRoleForm.selectedRoleIds = [];
        },
        // 关闭弹框
        handleClose() {
            this.resetValues();
            this.$emit('close');
        },
        // 选择完成员，传回父组件
        chooseRoles() {
            this.$emit('choose', this.companySelectRoleForm.selectedRoles);
            this.handleClose();
        },
    },
    beforeMount() {
        this.getRoleList({ type: 'init' });
    },
    created() {
        // this.companySelectRoleForm.selectedRoleIds = this.selectedValue || [];
    },
};
</script>

<style lang="scss">
	.company-select-role-dialog{
		.el-dialog{
            width: 720px;
		}

		.role-block{
			display: inline-block;
            float: left;

            .cloumn{
                margin-top: 15px;
                height: 329px;
                padding: 10px 20px;
                background: #fff;
                border: 1px solid #eee;
                overflow-y: auto;
            }

            .role-list-cloumn{
                p{
                    height: 40px;
                    margin-bottom: 5px;
                    line-height: 40px;

                    .el-checkbox__label{
                        display: inline-block;
                        padding-left: 15px;
                        max-width: 280px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        vertical-align: middle;
                    }
                }
            }

.selected-cloumn{
                p{
                    padding: 10px 10px 0;
					line-height: 30px;
					font-size: 12px;

					.el-icon-ssq-user-filling{
						display: inline-block;
                        width: 30px;
                        height: 30px;
                        margin-right: 10px;
                        font-size: 30px;
                        line-height: 34px;
                        background: #ccc;
                        color: #e9e9e9;
                        vertical-align: top;
					}

                    span{
                        display: inline-block;
                        max-width: 165px;
                        line-height: 18px;
                        vertical-align: middle;
                        }

                    .el-icon-ssq-delete{
						float: right;
                        line-height: 30px;
                        color: #999;
                        cursor: pointer;
                        }
                    }

            }
		}

		.role-list-block{
			width: 335px;
            margin-right: 15px;
		}

		.selected-block{
			width: 300px;
		}
	}
</style>
