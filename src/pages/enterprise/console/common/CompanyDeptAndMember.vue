<template>
    <div class="company-deptAndMember">

        <div class="company-inner-cloumn">
            <!-- 组织架构树 -->
            <CompanyDeptTree
                :selectAble="false"
                @getCurrentDept="handleGetCurrentDept"
                @seachResult="handleMemberSearchResult"
                ref="CompanyDeptTree"
            ></CompanyDeptTree>
        </div>

        <!-- 部门成员 -->
        <div class="company-inner-cloumn department-member-cloumn" :class="{ 'radio': selectType === 'radio' }">
            <div v-if="selectType === 'radio'">
                <!-- 单选成员 -->
                <el-radio-group
                    v-model="companyFullDialogForm.selectedMember"
                    @change="handleSelectChange"
                >
                    <p v-for="(member, index) in companyFullDialogForm.members" :key="index">
                        <i class="el-icon-ssq-user-filling"></i>
                        <el-radio :label="member.userId">{{ member.empName }}</el-radio>
                    </p>
                </el-radio-group>
            </div>
            <div v-else-if="selectType === 'checkBox'">
                <!-- 多选成员 -->
                <p>
                    <el-checkbox :indeterminate="isSelectedAll" v-model="companyFullDialogForm.selectedAllMembers" @change="handleCheckAllChange">{{ $t('docContentTable.searchAll') }}</el-checkbox>
                </p>
                <el-checkbox-group
                    v-model="companyFullDialogForm.selectedMembers"
                    @change="handleSelectChange"
                >
                    <p v-for="(member, index) in companyFullDialogForm.members" :key="index">
                        <el-checkbox :label="member" :key="member.userId">
                            {{ `${member.empName}(${member.account})` || `${member.account}` }}
                        </el-checkbox>
                    </p>
                </el-checkbox-group>
            </div>
        </div>

        <!-- 已选成员列表 -->
        <div class="member-block selected-block" v-if="selectType === 'checkBox'">
            <div class="cloumn selected-cloumn">
                <p>{{ $t('CSCommon.choosedMember') }}：</p>
                <div v-if="selectType === 'radio'">
                    <p v-if="companyFullDialogForm.selectedMember != null">
                        <span>{{ cacheMembers[companyFullDialogForm.selectedMember].empName }}</span>
                        <i class="el-icon-ssq-delete" @click="cancelSelectedMember"></i>
                        <i class="clear"></i>
                    </p>
                </div>
                <div v-else-if="selectType === 'checkBox'">
                    <p v-for="(member, index) in companyFullDialogForm.selectedMembers" :key="index">
                        <span>{{ `${member.empName}(${member.account})` || `${member.account}` }}</span>
                        <i class="el-icon-ssq-delete" @click="cancelSelectedMember(index)"></i>
                        <i class="clear"></i>
                    </p>
                </div>
            </div>
        </div>

        <div class="clear"></div>
    </div>
</template>

<script>
import CompanyDeptTree from './CompanyDeptTree.vue';

export default {
    components: {
        CompanyDeptTree,
    },
    props: {
        selectType: {
            type: String,
            default: 'checkBox',
        },
        onlyActive: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            cacheMembers: {

            },
            companyFullDialogForm: {
                count: 1,
                members: [],
                selectedMember: null, // 只作为 radio 状态下单选成员的数据显示
                selectedMembers: [], // 返回给父组件的选中的成员
            },
            // 是否选中了所有成员
            selectedAllMembers: true,
            // 全选按钮的不确定状态
            isSelectedAll: true,
        };
    },
    watch: {
        'companyFullDialogForm.selectedMembers': 'handleChoose',
    },
    methods: {
        // 从 <CompanyDeptTree> 组件中获取当前选中的树节点
        handleGetCurrentDept(val) {
            this.getMembers(val.deptId);
        },
        // 从 <CompanyDeptTree> 组件中获取搜索后的数据
        handleMemberSearchResult(val) {
            this.pushMember(val.data);
        },
        // 获取部门成员
        getMembers(deptId) {
            // 判断获取所有用户还是所有可用用户
            if (this.onlyActive) {
                this.$http({
                    method: 'get',
                    url: `/ents/depts/${deptId}/out-employees`,
                }).then((res) => {
                    this.pushMember(res.data);
                })
                    .catch(() => {

                    });
            } else {
                this.$http({
                    method: 'get',
                    url: `/ents/depts/${deptId}/in-employees`,
                }).then((res) => {
                    this.pushMember(res.data);
                })
                    .catch(() => {

                    });
            }
        },
        // 处理获取到的成员信息
        pushMember(memberData) {
            const memberList = [];
            const _this = this;
            this.cacheMembers = {};

            memberData.forEach(member => {
                memberList.push(member);

                // 通过 userId 标志缓存部门成员
                _this.cacheMembers[member.userId] = member;
            });

            this.companyFullDialogForm.members = memberList;
        },
        // 选择部门成员
        handleSelectChange(value) {
            if (this.selectType === 'radio') {
                this.companyFullDialogForm.selectedMembers = [this.cacheMembers[value]];
                this.companyFullDialogForm.selectedMember = value;
            } else if (this.selectType === 'checkBox') {
                const checkedCount = value.length;

                this.companyFullDialogForm.selectedAllMembers = checkedCount === this.companyFullDialogForm.members.length;
                this.isSelectedAll = checkedCount > 0 && checkedCount < this.companyFullDialogForm.members.length;
            }
        },
        // checkBox 时全选部门成员
        handleCheckAllChange(event) {
            // 如果全选则将 cacheMember 中的成员添加到已选成员，否则将已选成员置空
            // 并且切换全选 checkBox 的 indeterminate 状态
            if (event.target.checked) {
                for (const i in this.cacheMembers) {
                    this.companyFullDialogForm.selectedMembers.push(this.cacheMembers[i]);
                }
                this.isSelectedAll = false;
            } else {
                this.companyFullDialogForm.selectedMembers = [];
                this.isSelectedAll = true;
            }
        },
        handleChoose() {
            this.$emit('choose', this.companyFullDialogForm.selectedMembers, {
                type: 'innerMember',
            });
        },
        // 取消选择已选成员
        cancelSelectedMember(index) {
            if (this.selectType === 'radio') {
                // 如果是 radio 则直接将 selectedMembers 置空，并将 selectedMember 设为 null
                this.companyFullDialogForm.selectedMembers = [];
                this.companyFullDialogForm.selectedMember = null;
            } else if (this.selectType === 'checkBox') {
                // 如果是 checkBox 则将 selectedMembers 中对应的成员删除
                this.companyFullDialogForm.selectedMembers.splice(index === 0 ? 0 : index, 1);
                this.isSelectedAll = true;
            }
        },
    },
};
</script>

<style lang="scss">
	.company-deptAndMember{

		.el-dialog{
            width: 720px;
		}

		.company-inner-cloumn{
			display: inline-block;
			float: left;
			padding: 10px;
			overflow-y: auto;
		}

		.company-tree-cloumn{
			padding: 10px;
			border-right: 1px solid #eee;

			.ssq-tree{
				margin-top: 10px;
			}
		}

		.department-member-cloumn{
			width: 185px;
			p{
				position: relative;
				height: 40px;
				margin-bottom: 5px;
				line-height: 40px;

				.el-icon-ssq-user-filling{
					margin-left: 20px;
					margin-right: 5px;
					font-size: 30px;
					line-height: 34px;
					background: #ccc;
					color: #e9e9e9;
					vertical-align: middle;
				}

				.el-radio{
					vertical-align: middle;

					.el-radio__input{
						position: absolute;
						left: -60px;
						top: 10px;
					}
				}

			}

		}

		.member-block{
			display: inline-block;
			float: left;

			.cloumn{
				// margin-top: 15px;
				height: 329px;
				border-left: 1px solid #eee;
				background: #fff;
			}
		}

		.selected-block{
			width: 190px;
			// margin-top: -36px;

			.selected-cloumn{
				overflow-y: auto;

				p{
					padding: 10px 10px 0;
					line-height: 30px;

					.el-icon-ssq-user-filling{
						float: left;
						width: 30px;
						height: 30px;
						margin-right: 10px;
						font-size: 30px;
                        line-height: 34px;
						background: #ccc;
						color: #e9e9e9;
					}

					span{
						float: left;
						font-size: 12px;
					}

					.el-icon-ssq-delete{
						float: right;
						line-height: 30px;
						color: #999;
						cursor: pointer;
					}
				}

			}
		}
	}
</style>
