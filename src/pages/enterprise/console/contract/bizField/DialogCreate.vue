<template>
    <el-dialog
        :title="dialogTitle"
        :visible.sync="show"
        width="500"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :before-close="handleSaveClose"
        class="console-dialog-bg field-edit"
    >
        <div class="field-block">
            <div class="left-side">
                <div class="up">
                    <p>选择字段类型：</p>
                    <el-radio class="radio" v-model="bizFieldType" label="TEXT">文本</el-radio><br>
                    <!-- <el-radio class="radio" :disabled="curRow.operateType === 'edit'" v-model="bizFieldType" label="TEXT_NUMERIC">数字</el-radio><br> -->
                    <el-radio class="radio" v-model="bizFieldType" label="BIZ_DATE" v-if="!isDescriptionField">日期</el-radio><br>
                    <template v-if="isDescriptionField">
                        <!-- 描述字段 -->
                        <el-radio class="radio" v-model="bizFieldType" label="SINGLE_BOX">单选</el-radio><br>
                    </template>
                    <template v-else-if="!isHybridV1">
                        <!-- 混1 内容字段 不展示单选/复选入口 -->
                        <el-radio class="radio" v-model="bizFieldType" label="SINGLE_BOX">单选</el-radio><br>
                        <el-radio class="radio" v-model="bizFieldType" label="MULTIPLE_BOX">复选</el-radio>
                    </template>
                </div>
            </div>
            <div class="right-side">
                <el-form :model="curRow" label-position="top">
                    <el-form-item label="名称" prop="name">
                        <el-input v-model="bizName"
                            auto-complete="off"
                            placeholder="请输入名称"
                            :maxlength="30"
                            size="small"
                            @blur="checkName"
                        ></el-input>
                        <span class="name-error" v-show="errorMsg !== ''">
                            <i class="el-icon-ssq-warm-filling"></i>
                            {{ errorMsg }}
                        </span>
                    </el-form-item>
                    <!-- 单选/复选 管理 -->
                    <template v-if="isSelectField">
                        <el-form-item
                            label="备选项"
                            v-show="buttons.length"
                        >
                            <div
                                class="option-box"
                                v-for="(item, index) in buttons"
                                :key="index"
                            >
                                <el-input
                                    class="option-input"
                                    v-model.trim="item.text"
                                    @blur="checkOptionText(index)"
                                    auto-complete="off"
                                    :maxlength="30"
                                    size="small"
                                ></el-input>
                                <span class="name-error" v-show="item.errorMsg !== ''">
                                    <i class="el-icon-ssq-warm-filling"></i>
                                    {{ item.errorMsg }}
                                </span>
                                <i v-show="buttons.length > 2"
                                    class="el-icon-ssq--bs-guanbi delete-icon"
                                    @click="deleteOption(index)"
                                ></i>
                            </div>
                        </el-form-item>
                    </template>
                    <el-form-item v-show="showAddOptionBtn">
                        <span class="add-btn" @click="addOption"><i class="el-icon-ssq--bs-jia"></i>&nbsp;新增备选项</span>
                    </el-form-item>
                    <!-- 描述字段没有字体和填写人 -->
                    <el-form-item label="内容填写人" v-if="!isDescriptionField">
                        <el-radio-group v-model="writeBy">
                            <el-radio label="SENDER">
                                发件人
                                <el-tooltip class="item" effect="dark" content="选择“发件人”，则此合同内容 在合同发出前由发件人填写" placement="top">
                                    <i class="el-icon-ssq-wenhao"></i>
                                </el-tooltip>
                            </el-radio>
                            <el-radio label="SIGNER">
                                签署人
                                <el-tooltip class="item" effect="dark" content="选择“签署人”，则此合同 内容在签署人签署时填写" placement="top">
                                    <i class="el-icon-ssq-wenhao"></i>
                                </el-tooltip>
                            </el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="字号" v-if="!isSelectField">
                        <el-select popper-class="cus-field-editor-select"
                            v-model="fontSize"
                            placeholder="请选择字号"
                        >
                            <el-option
                                v-for="(item, index) in fontSizeRange"
                                :key="index"
                                :label="item.label"
                                :value="convertPtToPx(item.pt, dpi)"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="填写要求" prop="required">
                        <el-checkbox v-model="necessary">必填</el-checkbox>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button
                        class="confirm"
                        type="primary"
                        @click="handleSaveFieldType"
                    >确 定</el-button>
                    <el-button class="cancel" @click="handleSaveClose">取 消</el-button>
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<script>
import { convertPtToPx, fontSizeRange } from 'utils/fontSize.js';
import {  mapState } from 'vuex';
import resRules from 'utils/regs.js';
export default {
    name: 'DialogCreate',
    props: {
        curRow: {
            type: Object,
            default: () => {},
        },
        isDescriptionField: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            dialogTitle: '添加字段',
            errorMsg: '',
            dpi: 96,
            fontSizeRange,
            convertPtToPx,
            show: true,
            bizFieldType: 'TEXT',
            fieldId: '',
            fontSize: 14,
            bizName: '',
            initName: '',
            necessary: false,
            writeBy: 'SENDER',
            buttons: [],
            // hasApplied: undefined,
            isNewGroupPath: false, // 组件 代码从企业控制台拷贝过来的，抹平store isNewGroupPath
        };
    },
    computed: {
        // 是否为单选/复选业务字段
        isSelectField() {
            return ['SINGLE_BOX', 'MULTIPLE_BOX'].indexOf(this.bizFieldType) > -1;
        },
        showAddOptionBtn() {
            return this.isSelectField && this.buttons.length < (this.isDescriptionField ? 30 : 20);
        },
        postData() {
            const obj = {
                bizFieldType: this.bizFieldType,
                fontSize: this.fontSize,
                necessary: this.necessary,
                writeBy: this.writeBy,
                bizName: this.bizName,
            };
            if (this.isDescriptionField) {
                delete obj.fontSize;
                delete obj.writeBy;
            }
            this.isSelectField && (obj.buttons = this.buttons.map(el => {
                return el.text;
            }));
            if (this.curRow.operateType === 'edit') {
                obj.fieldId = this.curRow.fieldId;
            }
            return obj;
        },
        postUrl() {
            return {
                content: {
                    new: '/template-api/bizfield/content/add-bizfield',
                    edit: '/template-api/bizfield/content/edit-bizfield',
                },
                describe: {
                    new: '/template-api/bizfield/describe/add-bizfield',
                    edit: '/template-api/bizfield/describe/edit-bizfield',
                },
            }[this.isDescriptionField ? 'describe' : 'content'][this.curRow.operateType];
        },
        // ...mapGetters(['isNewGroupPath']),
        ...mapState(['commonHeaderInfo']),
        // 混合云1.0不支持单选框，复选框
        isHybridV1() {
            return !!this.commonHeaderInfo.hybridServer && this.$hybrid.isAlpha();
        },
    },
    methods: {
        deleteOption(index) {
            this.buttons.splice(index, 1);
        },
        addOption() {
            this.buttons.push({
                text: '',
                errorMsg: '',
            });
        },
        checkOptionText(index) {
            if (this.trim(this.buttons[index].text) === '')  {
                this.buttons[index].errorMsg = '备选项名不能为空';
                return;
            }
            // 业务字段需要做格式校验，不能包括特殊字符, CFD-6264, SAAS-12570
            if (!this.isDescriptionField && resRules.fieldValueReg.test(this.buttons[index].text)) {
                this.buttons[index].errorMsg = '备选项名字不能包含特殊字符\\/#()';
                return;
            }
            const sameIndex = this.buttons.findIndex((el, idx) => {
                return el.text === this.buttons[index].text && idx !== index;
            });
            if (sameIndex > -1) {
                this.buttons[sameIndex].errorMsg = '备选项名不能重复';
                this.buttons[index].errorMsg = '备选项名不能重复';
            } else {
                this.buttons[index].errorMsg = '';
            }
            console.log(index);
        },
        trim(data) {
            return data.replace(/\s+/g, '');
        },
        checkName() {
            if (this.trim(this.bizName) === '') {
                this.errorMsg = '请输入字段名称';
            } else if (this.isDescriptionField) {
                this.errorMsg = '';
            } else if (this.curRow.operateType === 'new' || (this.curRow.operateType === 'edit' && this.bizName !== this.initName)) {
                const bizName = encodeURI(this.bizName);
                this.$http.get(`/template-api/bizfield/selectByName?bizName=${bizName}`, {
                    params: {
                        isNewGroup: this.isNewGroupPath,
                    },
                }).then(res => {
                    if (res.data.code !== '140008') {
                        if (res.data.result.onOff === true) {
                            this.errorMsg = '该字段名称已存在，请重新取名';
                        } else if (res.data.result.onOff === false) {
                            this.errorMsg = '该字段名称已存在，请启用重名的字段';
                        }
                    } else {
                        this.errorMsg = '';
                    }
                });
            }
        },
        // 添加新合同类型
        handleSaveFieldType() {
            this.checkName();
            if (this.errorMsg !== '') {
                return;
            }
            if (this.isSelectField) {
                if (this.buttons.length < 2) {
                    this.$MessageToast.error('请至少添加两个备选项');
                    return;
                }
                for (let i = 0; i < this.buttons.length; i++) {
                    this.checkOptionText(i);
                    if (this.buttons[i].errorMsg !== '') {
                        return;
                    }
                }
            }
            this.$http.post(
                this.postUrl,
                this.postData,
                {
                    params: {
                        isNewGroup: this.isNewGroupPath,
                    },
                }).then(() => {
                this.$MessageToast.success(`${this.curRow.operateType === 'new' ? '新增' : '编辑'}成功`);
                this.handleSaveClose(true);
            });
            // let postData = {
            //     bizFieldType: this.bizFieldType,
            //     fontSize: this.fontSize,
            //     necessary: this.necessary,
            //     writeBy: this.writeBy,
            //     bizName: this.bizName,
            // };
            // if (this.curRow.operateType === 'new'){
            //     this.$http.post('/template-api/bizfield/content/add-bizfield', postData)
            //     .then((res) => {
            //         this.$MessageToast.success('新增成功');
            //         this.handleSaveClose(true);
            //     })
            // } else if(this.curRow.operateType === 'edit'){
            //     postData.fieldId = this.fieldId;
            //     this.$http.post('/template-api/bizfield/content/edit-bizfield', postData)
            //     .then((res) => {
            //         this.$MessageToast.success('编辑成功');
            //         this.handleSaveClose(true);
            //     })
            // }
        },
        // 关闭弹窗
        handleSaveClose(status) {
            this.$emit('close', status);
        },
    },
    created() {
        console.log(this.curRow);
        if (this.curRow.operateType === 'edit') {
            this.dialogTitle = '编辑字段';
            this.bizFieldType = this.curRow.bizFieldType;
            this.fieldId = this.curRow.fieldId;
            this.fontSize = this.curRow.fontSize;
            this.bizName = this.curRow.bizName;
            this.necessary = this.curRow.necessary;
            this.writeBy = this.curRow.writeBy;
            this.initName = this.curRow.bizName;
            if (this.isSelectField) {
                this.buttons = this.curRow.buttons.map(el => {
                    return {
                        text: el,
                        errorMsg: '',
                    };
                });
            }
            // this.hasApplied = this.curRow.hasApplied;
        }
    },
};
</script>

<style lang="scss">
    $--color-primary: #127FD2 !default;
    $--color-text-secondary: #999999 !default;
    .field-edit .el-dialog{
        width: 500px;
        font-weight: 500;
        box-sizing: border-box;
        .el-dialog__body {
            padding: 0 !important;
        }
        .el-radio+.el-radio {
            margin-left: 0;
        }
        .name-error{
            font-size: 12px;
            color: #f86b26;
            // position: absolute;
        }
        .el-input{
            display: block;
            input{
                display: block;
            }
        }
        .field-block {
            width: 100%;
            height: 400px;
            .left-side {
                width: 160px;
                height: 100%;
                padding: 24px 28px;
                background-color: #f6f9fc;
                float: left;
                .el-radio {
                    margin-top: 16px;
                }
                .up {
                    padding-bottom: 40px;
                }
                .down {
                    padding-top: 20px;
                }
            }
            .right-side{
                width: 340px;
                height: 100%;
                background-color: #fff;
                padding: 30px;
                float: right;
                overflow: auto;
                .el-form-item__content{
                    line-height: unset;
                    position: relative;
                }
                .option-box{
                    position: relative;
                    margin-top: 5px;
                }
                .delete-icon{
                    position: absolute;
                    left: 280px;
                    font-size: 12px;
                    color: $--color-text-secondary;
                    top: 10px;
                    cursor: pointer;
                    &:hover{
                        color: $--color-primary;
                    }
                }
                .add-btn{
                    cursor: pointer;
                    font-size: 14px;
                    color: $--color-primary;
                }
                .el-button {
                    height: 34px;
                    font-size: 12px;
                }
                .el-input__inner{
                    height: 30px;
                    width: 270px;
                }
                .dialog-footer{
                    text-align: right;
                }
                .cancel {
                    padding: 0 21px;
                    border-color: #ccc;
                    background: #f8f8f8;
                    &:hover {
                        border-color: #ccc;
                        background: #fff;
                    }
                }
                .confirm {
                    padding: 0 36px;
                    border-color: #127fd2;
                    background: #127fd2;
                    color: #fff;
                    &:hover {
                        background: #1687dc;
                        border-color: #1687dc;
                    }
                }
                .el-radio+.el-radio {
                    margin-left: 35px;
                }
                .el-icon-ssq-wenhao {
                    color: #666;
                }
            }
        }
    }
</style>
