<template>
    <el-upload
        class="contract-comparison__upload"
        :action="uploadUrl"
        :show-file-list="false"
        :on-success="handleSuccess"
        :on-error="handleFail"
        :before-upload="beforeUpload"
        v-loading="loading"
        :data="uploadPosition"
        drag
    >
        <div class="upload-icon">
            <i class="el-icon-ssq-shangchuanbendiwenjian"></i>
            <div class="tip1">{{ $tc('contractCompare.uploadLimit', 1) }}</div>
            <div class="tip2">{{ $tc('contractCompare.uploadLimit', 2) }}</div>
            <div class="tip3">{{ $t('contractCompare.documentSelect') }}</div>
        </div>
        <div class="upload-drag-info">{{ $t('contractCompare.dragInfo') }}</div>
    </el-upload>
</template>

<script>
export default {
    props: {
        uploadUrl: {
            type: String,
            default: '',
        },
        contractCompareEventId: {
            type: String,
            default: '',
        },
        // headers: {
        //     type: Object,
        //     default: () => {},
        // },
    },
    data() {
        return {
            loading: false,
            uploadPosition: {
                fileType: 1,    // 0表示左侧原始文件，1表示上传右侧比对文件
            },
        };
    },
    methods: {
        handleSuccess({ code, data }) {
            this.loading = false;
            if (code === '0') {
                const document = [];
                for (let i = 1; i <= data.totalPageCount; i++) {
                    document.push({
                        loaded: false,
                        imagePreviewUrl: `/web/document-compare/documents/${this.contractCompareEventId}/${this.uploadPosition.fileType}/view/${i}`,
                    });
                }
                this.$emit('uploadSuccess', {
                    // fileId: data.fileId,
                    document,
                    name: data.fileName,
                    // pdfFileId: data.pdfFileId,
                });
            }
        },
        handleFail(err) {
            this.loading = false;
            try {
                const index = err.message.indexOf('{');
                const errData = JSON.parse(err.message.slice(index));
                errData.message && this.$MessageToast.error(errData.message);
            } catch (error) {
                this.$MessageToast.error(err.message);
            }
        },
        beforeUpload(file) {
            if (this.$el.id === 'leftUpload') {
                this.uploadPosition.fileType = 0;
            } else {
                this.uploadPosition.fileType = 1;
            }
            const fileTypeArr = file.name.split('.');
            const fileType = fileTypeArr[fileTypeArr.length - 1];
            if (!['pdf', 'doc', 'dot', 'docx'].includes(fileType)) {
                this.$MessageToast.error(this.$t('contractCompare.uploadError'));
                return false;
            }
            this.loading = true;
        },
    },
};
</script>
