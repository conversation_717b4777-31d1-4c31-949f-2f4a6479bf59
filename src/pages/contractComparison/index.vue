<template>
    <div class="contract-comparison">
        <Header ref="header"></Header>
        <div class="contract-comparison__head">
            <div class="contract-comparison__head-box">
                <div class="contract-comparison__head-left">
                    <el-tooltip :disabled="!leftName" :content="leftName" placement="bottom">
                        <span class="ellipsis">{{ $t('contractCompare.originFile') }}：{{ leftName }}</span>
                    </el-tooltip>
                    <!-- <el-button :disabled="!leftDocument.length || btnLoading" @click="clear('left')">重新上传</el-button> -->
                </div>
                <div class="contract-comparison__head-right">
                    <el-tooltip :disabled="!rightName" :content="rightName" placement="bottom">
                        <span class="ellipsis">{{ $t('contractCompare.compareFile') }}：{{ rightName }}</span>
                    </el-tooltip>
                    <div>
                        <!-- <el-button :disabled="!rightDocument.length || btnLoading" @click="clear('right')">重新上传</el-button> -->
                        <el-button type="primary" :loading="btnLoading" :disabled="!(leftDocument.length && rightDocument.length)" @click="handleCompare">开始比对</el-button>
                        <el-button type="primary" :loading="btnLoading" :disabled="!leftDocument.length && !rightDocument.length" @click="resetAll">重新比对</el-button>
                    </div>
                </div>
            </div>
            <div class="contract-comparison__result flex">
                <div class="head-tab" @click="changeTab(1)" :class="selectedTab === 1 ? 'selected-tab' : ''">
                    <div class="head-tab_title">{{ $t('contractCompare.comparisonResult') }}</div>
                    <div class="head-tab_info">{{ $t('contractCompare.differences', {num: differences.length}) }}</div>
                </div>
                <div class="head-tab" @click="changeTab(2)" :class="selectedTab === 2 ? 'selected-tab' : ''">
                    <div class="head-tab_title">{{ $t('contractCompare.history') }}</div>
                    <div class="head-tab_info">{{ $t('contractCompare.historyLog', {num: history.length}) }}</div>
                </div>
                <div v-if="$route.params.contractId" class="head-tab" @click="changeTab(3)" :class="selectedTab === 3 ? 'selected-tab' : ''">
                    <div class="head-tab_title">{{ $t('contractCompare.currentHistory') }}</div>
                    <div class="head-tab_info">{{ $t('contractCompare.historyLog', {num: currentHistory.length}) }}</div>
                </div>
            </div>
        </div>
        <div class="contract-comparison__content">
            <div class="contract-comparison__difference-line" :style="differenceLineStyle"></div>
            <div class="contract-comparison__content-box">
                <div class="contract-comparison__content-left" @scroll="scrollChange" v-loading="documentLoading">
                    <Upload
                        id="leftUpload"
                        v-if="!leftDocument.length"
                        :uploadUrl="uploadUrl"
                        @uploadSuccess="(obj) => readyCompare(obj, 'left')"
                        :contractCompareEventId="contractCompareEventId"
                    ></Upload>
                    <div
                        class="contract-comparison__content-preview"
                        v-for="(page, index) in leftDocument"
                        :key="index"
                    >
                        <div class="contract-comparison__content-preview-box">
                            <img alt=""
                                @load="page.loaded = true"
                                v-lazy="{ src: page.imagePreviewUrl || page.pagePreviewUrl || page.previewFileUrl, split: 3,
                                          index,
                                          total: leftDocument.length }"
                            />
                            <template v-for="(difference, i) in pageDifferenceList(index, 'source')">
                                <div
                                    :key="i"
                                    v-show="page.loaded"
                                    :class="`contract-comparison__difference difference-left-${difference.sort}`"
                                    :style="differenceStyle(difference)"
                                ></div>
                            </template>
                        </div>
                        <p>{{ $t('contractCompare.pageNum', {page:index + 1}) }}</p>
                    </div>
                </div>
                <div class="contract-comparison__content-right" @scroll="scrollChange">
                    <Upload
                        id="rightUpload"
                        v-if="!rightDocument.length"
                        :uploadUrl="uploadUrl"
                        @uploadSuccess="(obj) => readyCompare(obj, 'right')"
                        :contractCompareEventId="contractCompareEventId"
                    ></Upload>
                    <div
                        class="contract-comparison__content-preview"
                        v-for="(page, index) in rightDocument"
                        :key="index"
                    >
                        <div class="contract-comparison__content-preview-box">
                            <img
                                alt=""
                                v-lazy="{ src: page.imagePreviewUrl || page.pagePreviewUrl || page.previewFileUrl, split: 3, index, total: rightDocument.length}"
                                @load="page.loaded = true"
                            >
                            <template v-for="(difference, i) in pageDifferenceList(index, 'target')">
                                <div
                                    :key="i"
                                    v-show="page.loaded"
                                    :class="`contract-comparison__difference difference-right-${difference.sort}`"
                                    :style="differenceStyle(difference)"
                                ></div>
                            </template>
                        </div>
                        <p>{{ $t('contractCompare.pageNum', {page:index + 1}) }}</p>
                    </div>
                </div>
            </div>
            <div class="contract-comparison__result">
                <template v-if="selectedTab === 1">
                    <div class="contract-comparison__result-content">
                        <div
                            :class="{ 'contract-comparison__result-content-item': true, active: i === activeDifferenceIndex }"
                            v-for="(difference, i) in differences"
                            :key="i"
                            @click="selectDifference(i)"
                        >
                            {{ $t('contractCompare.difference', {num: i + 1}) }}
                        </div>
                        <NoData v-if="!differences.length" />
                    </div>
                </template>
                <template v-else-if="selectedTab === 2">
                    <div class="contract-comparison__result-content">
                        <div
                            class="contract-comparison__result-content-item history"
                            v-for="(his, i) in history"
                            :key="i"
                            @click="goHistory(his)"
                        >
                            <el-tooltip :content="his.fileName" placement="top">
                                <p class="title ellipsis">{{ his.fileName }}</p>
                            </el-tooltip>
                            <p class="info">
                                <span>{{ moment(Number(his.contractCompareTime)).format('YYYY-MM-DD HH:mm:ss') }}</span>
                                <span>{{ $t('contractCompare.differences', {num: his.differentCount}) }}</span>
                            </p>
                        </div>
                        <NoData v-if="!history.length" />
                    </div>
                </template>
                <template v-else-if="selectedTab === 3">
                    <div class="contract-comparison__result-content">
                        <div
                            class="contract-comparison__result-content-item history"
                            v-for="(his, i) in currentHistory"
                            :key="i"
                            @click="goHistory(his)"
                        >
                            <el-tooltip :content="his.fileName" placement="top">
                                <p class="title ellipsis">{{ his.fileName }}</p>
                            </el-tooltip>
                            <p class="info">
                                <span>{{ moment(Number(his.contractCompareTime)).format('YYYY-MM-DD HH:mm:ss') }}</span>
                                <span>{{ $t('contractCompare.differences', {num: his.differentCount || 0}) }}</span>
                            </p>
                        </div>
                        <NoData v-if="!currentHistory.length" />
                    </div>
                </template>
                <div class="contract-comparison__operate">
                    <el-button v-if="annotationTaskStatus === 2" class="contract-comparison__download" @click="getCompareSummary" type="primary">总结比对差异</el-button>
                    <el-button v-if="annotationTaskStatus === 2 && origin === 'history'" class="contract-comparison__download" @click="downloadResult" type="primary">{{ $t('contractCompare.download') }}</el-button>
                </div>
                <!-- <el-button v-if="annotationTaskStatus === 2 && origin === 'history'" class="contract-comparison__download" @click="downloadResult" type="primary">{{ $t('contractCompare.download') }}</el-button> -->
            </div>
        </div>
        <RegisterFooter v-if="isPC" class="login-footer"></RegisterFooter>
        <div class="compare-loading" v-show="comparisonLoading" style="top: 70px">
            <div class="compare-loading_content">
                <img src="~img/contractHubble/hubble-loading.gif" alt="">
                <p>{{ $t('contractCompare.comparing') }}</p>
            </div>
        </div>
        <div class="compare-loading" v-show="summarizeLoading" style="top: 70px">
            <div class="compare-loading_content">
                <img src="~img/contractHubble/hubble-loading.gif" alt="">
                <p>正在获取比对总结</p>
            </div>
        </div>
        <el-dialog
            title="比对差异总结"
            :visible.sync="summaryVisible"
        >
            <div v-for="(summary, index) in summaries" :key="index">
                <p class="summary-title">{{ summary.difference }}</p>
                <p class="summary-content origin">
                    {{ summary.pointOfOriginal }}
                </p>
                <p class="summary-content compare">
                    {{ summary.pointOfComparison }}
                </p>
            </div>
            <div v-if="!summaries.length">
                <p class="summary-title">尚未获取到总结内容</p>
            </div>
            <div slot="footer">
                <el-button @click="summaryVisible = false">关闭</el-button>
            </div>
        </el-dialog>
        <FeedBack />
    </div>
</template>

<script>
import moment from 'dayjs';
import Upload from './upload';
import Header from 'components/hubbleApplyHeader';
import RegisterFooter from 'components/register_footer/RegisterFooter.vue';
import NoData from 'components/noData';
import FeedBack from 'components/hubbleFeedBack';
import { getComparisonTaskStatus, getComparisonDifferences, startCompare, getHistory, initTask, newCalcBilling, getCurrentDocHistory, getSummary, summarize } from 'src/api/comparison.js';
import { isPC } from 'src/common/utils/device.js';
import { initHubbleTask } from 'src/api/docTranslation.js';

const COMPARISON_TASK_STATUS_MAP = {
    COMPARISON_TASK_NOT_START: 0,
    COMPARISON_TASK_PROCESSING: 1,
    COMPARISON_TASK_SUCCESS: 2,
    COMPARISON_TASK_FAILURE: 3,
};
const ANNOTATION_TASK_STATUS_MAP = {
    ANNOTATION_TASK_NOT_START: 0,
    ANNOTATION_TASK_PROCESSING: 1,
    ANNOTATION_TASK_SUCCESS: 2,
    ANNOTATION_TASK_FAILURE: 3,
};

export default {
    components: { Upload, Header, RegisterFooter, NoData, FeedBack },
    data() {
        return {
            contractName: '',
            contractId: this.$route.params.contractId,
            documentId: this.$route.params.documentId,
            // leftFileId: '',
            // rightFileId: '',
            // leftPdfId: '',
            // rightPdfId: '',
            leftName: '',
            rightName: '',
            leftDocument: [],
            rightDocument: [],
            differences: [],
            contractCompareEventId: '',
            showDifference: false,
            currentDifference: {},
            activeDifferenceIndex: -1,
            comparisonLoading: false,
            documentLoading: false,
            hadRequestResult: false,
            comparisonTaskStatus: -1,
            annotationTaskStatus: -1,
            selectedTab: 1,
            history: [],
            btnLoading: false,
            isPC: isPC(),
            fromDocument: false,
            needNewCompareEventId: false,
            currentHistory: [],
            origin: 'history',
            summaryVisible: false,
            summaries: [],
            summarizeLoading: false,
        };
    },
    computed: {
        differenceLineStyle() {
            if (this.activeDifferenceIndex === -1) {
                return { display: 'none' };
            }
            const source = document.getElementsByClassName(`difference-left-${this.activeDifferenceIndex}`)[0].getBoundingClientRect();
            const target = document.getElementsByClassName(`difference-right-${this.activeDifferenceIndex}`)[0].getBoundingClientRect();
            const x_start = source.x + source.width;
            const y_start = source.y + source.height / 2;
            const x_end = target.x;
            const y_end = target.y + target.height / 2;
            // 用勾股定律计算出斜边长度及其夹角（即连线的旋转角度）
            const lx = x_end - x_start;
            const ly = y_end - y_start;
            // 计算连线长度
            const length = Math.sqrt(lx * lx + ly * ly);
            // 弧度值转换为角度值
            const c = 360 * Math.atan2(ly, lx) / (2 * Math.PI);

            // 连线中心坐标
            const midX = (x_end + x_start) / 2;
            const midY = (y_end + y_start) / 2;
            const deg = c <= -90 ? (360 + c) : c;  // 负角转换为正角
            return {
                top: `${midY}px`,
                left: `${midX - length / 2}px`,
                width: `${length}px`,
                transform: `rotate(${deg}deg)`,
            };
        },
        uploadUrl() {
            return `/web/document-compare/documents/${this.contractCompareEventId}/upload`;
        },
        // headers() {
        //     return {
        //         'x-authenticated-userid': JSON.stringify({
        //             userId: '2240439313374409729',
        //             bizNo: '',
        //             chosenEntId: 2252029358808005639,
        //             empId: 2252029358833171464,
        //         }),
        //     };
        // },
    },
    methods: {
        moment,
        clear(type) {
            // this[`${type}FileId`] = '';
            this[`${type}Document`] = [];
            this[`${type}Name`] = '';
            this.differences = [];
            this.activeDifferenceIndex = -1;
            this.annotationTaskStatus = -1;
        },
        readyCompare({ document, name }, type) {
            // this[`${type}FileId`] = fileId;
            this[`${type}Document`] = document;
            this[`${type}Name`] = name;
            // this[`${type}PdfId`] = pdfFileId;
        },
        async handleCompare() {
            this.btnLoading = true;
            newCalcBilling(this.contractCompareEventId)
                .then((res) => {
                    this.calcTip(res.data);
                })
                .finally(() => {
                    this.btnLoading = false;
                });
        },
        calcTip({ description, balanceEnough, enterpriseName }) {
            const content1 = description;
            const content2 = `扣除主体：${enterpriseName}`;
            const content3 = '可用页数不足，请购买充值';
            const btnText = balanceEnough ? this.$t('contractCompare.confirm') : this.$t('contractCompare.toBuy');
            const h = this.$createElement;
            const content = h('p', null, [
                h('p', null, content1),
                h('p', { style: 'margin-top: 25px' }, content2),
                balanceEnough ? '' : h('p', { style: 'margin-top: 25px' }, content3),
            ]);
            this.$confirm(content, this.$t('contractCompare.tip'), {
                confirmButtonText: btnText,
                showCancelButton: true,
                customClass: 'compare-calc-billing',
            }).then(() => {
                if (balanceEnough) {
                    this.comparisonLoading = true;
                    this.needNewCompareEventId = true;
                    startCompare(this.contractCompareEventId)
                        .then((res) => {
                            const { data: { data } } = res;
                            this.contractCompareEventId = data.contractCompareEventId;
                            this.activeDifferenceIndex = -1;
                            this.hadRequestResult = false;
                            this.getComparisonStatus();
                        }).catch(() => {
                            this.comparisonLoading = false;
                            this.btnLoading = false;
                        });
                } else {
                    this.$refs.header.openChargeDialog();
                }
            }).catch(() => {
            });
        },
        getComparisonStatus() {
            getComparisonTaskStatus(this.contractCompareEventId)
                .then((res) => {
                    this.needNewCompareEventId = true;
                    const { data: { data } } = res;
                    const { comparisonTaskStatus, annotationTaskStatus } = data;
                    // comparisonTaskStatus 合同比对状态 0, "未开始" 1, "处理中" 2, "成功" 3, "失败"
                    // annotationTaskStatus 批注文件生成状态 0, "未开始" 1, "处理中" 2, "成功" 3, "失败"
                    this.comparisonTaskStatus = COMPARISON_TASK_STATUS_MAP[comparisonTaskStatus];
                    this.annotationTaskStatus = ANNOTATION_TASK_STATUS_MAP[annotationTaskStatus];
                    // this.comparisonLoading = this.comparisonTaskStatus < 2;
                    this.comparisonTaskStatus === 2 && !this.hadRequestResult && this.getComparisonResult();
                    if (this.comparisonTaskStatus < 2 || (this.comparisonTaskStatus === 2 && this.annotationTaskStatus < 2)) {
                        setTimeout(() => {
                            this.getComparisonStatus();
                        }, 5000);
                        return;
                    }
                }).catch(() => {
                    this.comparisonLoading = false;
                });
        },
        getComparisonResult() {
            this.hadRequestResult = true;
            getComparisonDifferences(this.contractCompareEventId)
                .then((res) => {
                    const { data: { data } } = res;
                    // this.leftFileId = data.sourceFile.fileId;
                    // this.leftPdfId = data.sourceFile.pdfFileId;
                    this.leftName = data.sourceFile.fileName;
                    this.leftDocument = data.sourceFile.previewFiles;
                    // this.rightFileId = data.comparisonFile.fileId;
                    // this.rightPdfId = data.comparisonFile.pdfFileId;
                    this.rightName = data.comparisonFile.fileName;
                    this.rightDocument = data.comparisonFile.previewFiles;
                    this.differences = data.fileCompareDifferenceItems.map(el => {
                        return {
                            source: el.contractFileDifferenceLocation,
                            target: el.compareFileDifferenceLocation,
                        };
                    });
                    this.comparisonLoading = false;
                    this.getHistory();
                    if (this.$route.params.contractId) {
                        this.getCurrentHistory();
                    }
                }).catch(() => {
                    this.hadRequestResult = false;
                });
        },
        pageDifferenceList(pageIndex, type) {
            const arr = [];
            this.differences.forEach((el, i) => {
                if (el[type].pageNumber === pageIndex + 1) {
                    arr.push({
                        ...el[type],
                        sort: i,
                    });
                }
            });
            return arr;
        },
        scrollChange() {
            if (this.showDifference) {
                this.activeDifferenceIndex = -1;
                this.showDifference = false;
            }
        },
        selectDifference(index) {
            document.getElementsByClassName(`difference-left-${index}`)[0].scrollIntoView();
            document.getElementsByClassName(`difference-right-${index}`)[0].scrollIntoView();
            setTimeout(() => {
                this.showDifference = true;
                this.activeDifferenceIndex = index;
            }, 50);
        },
        differenceStyle(difference) {
            return {
                width: `${difference.width * 100}%`,
                height: `${difference.height * 100}%`,
                left: `${difference.x * 100}%`,
                top: `${difference.y * 100}%`,
            };
        },
        downloadResult() {
            Vue.$http.get(`/web/document-compare/documents/${this.contractCompareEventId}/download`).then(() => {
                const downloadUrl = `/web/document-compare/documents/${this.contractCompareEventId}/download`;
                const $el = document.querySelector('#J_download-link') || document.createElement('a');
                $el.setAttribute('id', 'J_download-link');
                $el.setAttribute('style', 'visibility:hidden;height:0;width:0;');
                $el.setAttribute('target', '_self');
                $el.setAttribute('href', downloadUrl);
                $el.setAttribute('download', 'annotation-file.zip');
                document.body.appendChild($el);
                $el.click();
            });
        },
        getHistory() {
            getHistory()
                .then((res) => {
                    const { data: { data } } = res;
                    this.history = data;
                });
        },
        getCurrentHistory() {
            const { contractId, documentId } = this.$route.params;
            getCurrentDocHistory(contractId, documentId, 'COMPARE')
                .then((res) => {
                    const { data: { result } } = res;
                    this.currentHistory = result;
                });
        },
        goHistory(obj) {
            if (this.btnLoading) {
                return;
            }
            if (this.selectedTab === 2) {
                this.origin = 'history';
            }
            if (this.selectedTab === 3) {
                this.origin = 'document';
            }
            this.contractCompareEventId = obj.contractCompareEventId;
            this.comparisonLoading = true;
            this.activeDifferenceIndex = -1;
            this.hadRequestResult = false;
            this.getComparisonStatus();
        },
        initCompareTask() {
            initTask().then(({ data: { data } }) => {
                this.contractCompareEventId = data.contractCompareEventId;
                this.needNewCompareEventId = false;
            });
        },
        resetAll() {
            this.clear('left');
            this.clear('right');
            this.origin = 'history';
            if (this.needNewCompareEventId) {
                this.initCompareTask();
            }
        },
        changeTab(num) {
            if (this.selectedTab !== num && num === 2) {
                this.selectedTab = num;
                this.getHistory();
            } else if (this.selectedTab !== num && num === 3) {
                this.selectedTab = num;
                this.getCurrentHistory();
            } else {
                this.selectedTab = num;
            }
        },
        initDocumentV2(contractId, documentId) {
            return new Promise(resolve => {
                initHubbleTask(contractId, documentId, 'COMPARE').then(({ data: { result } }) => {
                    this.leftName = result.contractName;
                    // this.leftPdfId = pdfFileId;
                    this.leftDocument = result.pagePreviewUrls;
                    this.contractCompareEventId = result.taskId;
                    this.needNewCompareEventId = true;
                    resolve();
                });
            });
        },
        getCompareSummary() {
            this.summarizeLoading = true;
            getSummary(this.contractCompareEventId).then(({ data: { summaryCompleted, summary } }) => {
                if (!summaryCompleted) {
                    this.summarizeComparision(this.contractCompareEventId);
                } else if (summaryCompleted && !summary) {
                    this.summarizeLoading = false;
                    this.$confirm('暂未获取到总结内容, 是否重试?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning',
                    }).then(() => {
                        this.getCompareSummary();
                    }).catch(() => {});
                } else {
                    this.summaries = summary.summaries || [];
                    this.summaryVisible = true;
                    this.summarizeLoading = false;
                }
            }).catch(() => {
                this.summarizeLoading = false;
            });
        },
        summarizeComparision(contractCompareEventId) {
            summarize(contractCompareEventId).then(() => {
                this.getCompareSummary();
            }).catch(() => {
                this.summarizeLoading = false;
            });
        },
    },
    created() {
        const { contractId, documentId } = this.$route.params;
        if (contractId && documentId) {
            this.initDocumentV2(contractId, documentId).then(() => {
                this.getCurrentHistory();
                this.origin = 'document';
            });
        } else {
            this.initCompareTask();
        }
        this.getHistory();
        // if (this.$route.params.contractId) {
        //     this.getCurrentHistory();
        // }
        this.fromDocument = !!sessionStorage.getItem('fromDocument');
        window.onscroll = () => {
            this.scrollChange();
        };
    },
    beforeDestroy() {
        sessionStorage.removeItem('fromDocument');
        sessionStorage.removeItem('signingPagePath');
    },
};
</script>

<style lang="scss">
@import './index.scss';
</style>

<style lang="scss" scoped>
.contract-comparison__result-content {
    height: calc(100% - 75px);
}
.contract-comparison__operate {
    padding: 0 20px;
    .contract-comparison__download {
        margin-left: 0;
        margin-bottom: 5px;
    }
}
.contract-comparison::v-deep .el-dialog__body {
    max-height: 70vh;
    overflow-y: auto;
}

.summary-title {
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
}
.summary-content {
    text-indent: 2ch;
    line-height: 22px;
    &.compare {
        margin-bottom: 10px;
    }
}
</style>
