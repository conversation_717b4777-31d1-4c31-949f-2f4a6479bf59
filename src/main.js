import ElementUI from 'element-ui';
import router from './router/router.js';
import store from './store/store.js';
import Axios from './service/http.js';

import 'element-ui/lib/theme-default/index.css';
import 'animate.css';
import 'src/common/assets/iconfont/iconfont.css';

import i18n from './lang'; // Internationalization
Vue.use(ElementUI, {
    i18n: (key, value) => i18n.t(key, value),
});
import _global from 'src/common/utils/staticVariable.js';
import VueQriously from 'vue-qriously';
import Validation from 'src/common/plugins/validation/validation.js';
import Focus from 'src/common/plugins/focus/focus.js';
import Cookie from 'src/common/plugins/cookie/index.js';
import Token from 'src/common/plugins/token/token.js';
import Utils from 'src/common/plugins/utils/utils.js';
import Lazyload from 'src/common/plugins/lazyload/lazyload.js';
import AutoHeight from 'src/common/plugins/autoHeight/autoHeight.js';
// import AdaptImgSize from 'src/common/plugins/adaptImgSize/adaptImgSize.js';
import ModuleId from 'src/common/plugins/moduleIdVisible/module-id.js';
import VueClipboard from 'vue-clipboard2';
// import VueScrollTo from 'vue-scrollto';
import 'src/common/polyfill';
// import VueAgile from 'vue-agile';

import MessageToastNew from 'components/messageToast/MessageToastNew.js';
import AppToast from 'components/appToast/AppToast.js';
import MessageBox from 'components/messageBox/MessageBox.js';
import 'utils/hybrid/index.js';

// import { initFee } from 'utils/feeSDK.js';

import 'components/svg_icon/iconfont'; // svg-icon, 目前只有发合同等svg部分使用
/* import VConsole from 'vconsole' //import vconsole
let vConsole = new VConsole()*/
Vue.use(MessageToastNew);
Vue.use(AppToast);
Vue.use(MessageBox);
Vue.use(VueClipboard);
// Vue.use(VueScrollTo);

// import { Picker, Popup, DatetimePicker } from 'vant';
// Vue.use(Picker);
// Vue.use(Popup);
// Vue.use(DatetimePicker);

import LocalStorage from 'src/common/plugins/localStorage';
Vue.use(LocalStorage);

import FunctionSupport from 'src/common/plugins/functionSupport';
Vue.use(FunctionSupport);

// 调试暂用
// import VConsole from 'vconsole';
// new VConsole();

import 'css/main.scss';
import App from './main.vue';

Vue.use(VueQriously);
Vue.use(VueRouter);
Vue.use(Axios);
Vue.use(Validation);
Vue.use(Focus);
Vue.use(Lazyload);
// Vue.use(VueAgile);
Vue.use(AutoHeight);
// Vue.use(AdaptImgSize);
Vue.use(ModuleId, { store });

// 全局变量
// vue文件使用全局变量方法：this.GLOBAL.变量名
Vue.prototype.GLOBAL = _global;
Vue.GLOBAL = _global;

// 提示框
Vue.prototype.$MessageToast = MessageToastNew;
Vue.$MessageToast = MessageToastNew;

// Tell Vue to use the plugin
Vue.use(Cookie);
Vue.use(Token);
Vue.use(Utils);

import 'src/utils/sensors.js';
// 前端监控，依赖 cookie
// initFee();

// import LockButton from 'components/lock-button/LockButton.vue';
// Vue.component('LockButton', LockButton);

import ResetPassword from 'components/resetPassword/ResetPassword.js';
Vue.prototype.$resetPassword = ResetPassword.install;
Vue.$resetPassword = ResetPassword.install;

import DownloadPwdDialog from 'components/downloadPwdDialog/index.js';
Vue.prototype.$downloadPwdDialog = DownloadPwdDialog.install;
Vue.$downloadPwdDialog = DownloadPwdDialog.install;

window.addEventListener('load', function() {
    const app = new Vue({
        router,
        store,
        i18n,
        render: createEle => createEle(App),
    }).$mount('#app');
    window.app = app; // 暂时暴露出来，排查 普通发起 “合同不存在” 问题
});

localStorage.setItem('firstPage', location.href);

document.addEventListener('UniAppJSBridgeReady', function() {
    uni.getEnv(function(res) {
        if (res.miniprogram || (window.sessionStorage && window.sessionStorage.getItem && window.sessionStorage.getItem('isQyWx'))) {
            document.body.classList.add('qywx');
            window.sessionStorage.setItem('isQyWx', 1); // 为企业微信小程序
            store.commit('setIsQyWx', true);
            let signPlatform = window.sessionStorage && window.sessionStorage.getItem && window.sessionStorage.getItem('signPlatform');
            if (window.location.search.indexOf('signPlatform=QWX') > -1) {
                signPlatform = 'QWX';
            }
            if (signPlatform) {
                window.sessionStorage.setItem('signPlatform', signPlatform); // 为企业微信小程序
                store.commit('setSignPlatform', signPlatform);
            }
        }
    });
});

// sentry错误上报日志平台
import * as Sentry from '@sentry/vue';
import { Integrations } from '@sentry/tracing';
// import { sentryErrorReport } from 'src/utils/sentryReport.js';
const releaseNum = 'ent@2022_1';
const notReportEventMsg = ['bannerNight', 'WebViewJavascriptBridge', 'yxk_share', 'getID', 'renderedBuffer', 'openBackAudio', 'pauseBackAudio'];
const onlyReportEventType = ['TypeError', 'ReferenceError'];
process.env.NODE_ENV === 'production' && Sentry.init({
    Vue,
    dsn: 'https://<EMAIL>/5',
    integrations: [
        new Integrations.BrowserTracing({
            routingInstrumentation: Sentry.vueRouterInstrumentation(router),
        }),
    ],
    tracesSampleRate: 0.6,
    release: releaseNum,
    environment: location.host,
    allowUrls: ['bestsign.cn'],
    beforeSend(event) {
        if (event.exception && event.exception.values.length) {
            const eventType = event.exception.values[0].type;
            const eventMsg = event.exception.values[0].value;
            const isNoNeedReportEvent = notReportEventMsg.filter(item => eventMsg.includes(item));
            if (onlyReportEventType.includes(eventType) && !isNoNeedReportEvent.length) {
                return event;
            }
        } else if (event.extra && event.extra.errType) {
            if (event.extra.errType === 'selfReport') {
                return event;
            }
        }
        return null;
    },
    beforeBreadcrumb() {
        return null;
    },
});
