import Vuex from 'vuex';
Vue.use(Vuex);
import template from './modules/template.js';
import dynamic from './modules/dynamicTemplate.js';
import doc from './modules/doc.js';
import hubble from './modules/hubble.js';
import approval from './modules/approval.js';
import sendingPrepare from './modules/sendingPrepare.js';
import i18n from 'src/lang';
import { FEATURE_MAP } from 'src/common/const/featureMap.js';
import docTranslation from './modules/docTranslation.js';
import { getSensorsCurrentEntInfo, getSensorsCurrentRoleName } from 'src/utils/sensorsUtils.js';

const state = {
    login: false,
    v3Login: false, // v3 登录状态
    userInfo: null,
    companyDetailInfo: {
        adminEmployee: {

        },
        corpAuth: {

        },
        entGroup: {

        },
        enterprise: {

        },
        rolePrivileges: {

        },
        roleDetails: [],
        haveData: false,
    },
    initiPerCerInfo: { // 初始化个人实名数据
        certificationType: '',	// 1：手持身份证认证；2： 银行卡认证；3：刷脸认证；4：银行卡四要素；5：护照认证；6-其他认证；7：手机认证；8-港澳通行证认证；9：台湾通行证
        idInforFinishStatus: 0, // 0 未完成 1 已完成
        name: '',
        IDNumber: '',
        authinfoStatus: '',	// 0-未审核；1-审核通过；2-未完成；3-自动审核通过；4-审核驳回；5-重新提交审核；
    },
    personalCerInfo: { // 个人实名数据
        name: '',
        IDNumber: '',
    },
    commonHeaderInfo: {	// 公共头部
        SECOND_CHECK: '0',
        status: false,
        currentEntId: '',
        hasManager: '',
        hasMobile: true,
        userType: '',
        enterprises: [],
        extendFields: {},
        platformUser: {},
        roleDetails: [],
        // 模板匹配规则设置
        templateMatchConf: {
            hasSetTemplateMatch: false,   // 是否在boss系统开启了模板匹配规则
            hasSetExcelCharMatch: false, // 是否开启了Excel系统字段匹配
            hasActiveMatchRule: false,   // 是否已经添加了规则
            hasUseAlone: false, // 模板匹配开始时,是否开启单独使用模版
        },
        // 混合云host
        hybridServer: '',
        LANConnected: null,
        // 签署人添加附件功能
        receiverAttachent: false,
        hasGroupConsole: false, // 是否展示集团控制台
        loginFromSSOFlag: false, // 是否是SSO方式登录
        extractDialog: false,   // 签署页是否展示抽取合同信息的弹窗
        annotationId: '',
        judgeDialog: false,
    },
    home: {
        version: null,
    },
    // 1、public 公有云用户 2、hybrid 混合云用户 3、 publicHybrid 公有云用户进入混合云合同 4、 otherHybrid 混合云用户进入其他混合云合同  5、publicAliyun 公有云用户进入公有云合同 6、hybridAliyun 混合云用户进入公有云合同
    hybridUserType: null,
    // 认证步骤
    authStep: 0,
    // 单点登录配置
    ssoConfig: {},
    // 功能点列表, 在使用乐高成配置时，请使用checkFeat
    features: [],
    // 高级功能点列表
    advancedFeatures: [], // isOpen:是否开启，daysRemaining：试用结束时间，onTrial(0:未试用，1：试用中，2：试用到期)
    reportHttpData: [], // 主动上报的接口信息(日志平台查看)
    // 账号版本
    // 1、智能版，2、专业版，3、自定义版，4、标准版
    // 跟东蔚确认过，后端代码2019/02开始已经不维护version了
    version: null,
    noticeCount: {
        noticeUnreadMessageCount: 0,
        contractUnreadMessageCount: 0,
        unreadMessageCount: 0,
    }, // 未读消息
    signImageBase64: '', // 手绘签名base64
    newContract: false, // 是否走新签约管理，默认走旧版
    hybridVersion: null, // 混合云版本，从4.19.3开始
    isQyWx: false, // 是否在企业微信小程序
    isFirstQywx: true, // 从企业微信进入
    signPlatform: 'WAP', // 企业微信小程序与手机端区分来源，也可区分微信与企业微信小程序
    isBrand: false, // 是否拥有企业品牌
    currentEntId: null, // 当前主体entId
    isCurrentDomainLogin: true, // false为从其他域名的签约管理列表跳转进入
    newFromExperience: false, // 新官网注册流程的新客户,
    authPassTime: new Date(), // 实名通过时间 展示在赠送合同组件 精确到日 可以默认为当天
    personalIdentifyWay: false, // 开发者个人实名配置开关是否开启
    specifiedEntName: '', // 开发者通过账号绑定接口提交的企业名称
    noMorePhoneSetTip: false, // 不在提醒提交通知手机
    ifInfoFromDeveloper: false, // 基础信息是不是由开发者代传
    localFieldSupportType: 0, // 混合云3.0是否支持本地存储，0表示存云端，大于1表示存本地
    downloadFileName: '', // 合同下载加密文件名
    downloadFilePassword: '', // 合同下载加密随机密码
    currentSignDocumentId: '',
    noLoginLanguage: sessionStorage.getItem('noLoginLanguage') || '', // 未登录页面 设置的语言，在登录成功之后 携带过去
    currentSummarySentence: {},
    scrollProgress: 0, // 签署页要求滚动底部才可签署时，滚动进度
    docList: [], // 签署页合同列表
    step: 1,    // 抽取合同信息进度：1、选择文件，2、选择文件类型，3、选择术语，4、结果展示
};

// 提交 mutation，可包含异步操作，通过 store.dispatch 调用
const actions = {
    console_updateLogo() {

    },
};

// 从 store 中的 state 中派生出一些状态
const getters = {
    // 外文版（非国内版，目前有：getIsJa || getIsUae）
    getIsForeignVersion(state, getters) {
        return getters.getIsJa || getters.getIsUae;
    },
    // 是否是阿联酋版用户
    getIsUae() {
        const jaPath = ['uae.bestsign.com', 'ent.bestsign.ae', 'ent-ae.bestsign.info'];
        // 本地先默认返回true
        return process.env.NODE_ENV === 'development-uae' || jaPath.includes(location.host);
    },
    // 是否是日文版用户
    getIsJa() {
        const jaPath = ['ent-jp.bestsign.tech', 'ent-jp.bestsign.info', 'ent.bestsign.com'];
        // 本地先默认返回true
        return process.env.NODE_ENV === 'development-ja' || jaPath.includes(location.host);
    },
    console_getEntName: state => {
        return state.companyDetailInfo.enterprise.entName;
    },
    console_authStatus: state => {
        return (
            (+state.companyDetailInfo.corpAuth.authinfoStatus === 1) ||
            (+state.companyDetailInfo.corpAuth.authinfoStatus === 3)
        );
    },
    console_getRolePrivileges: state => {
        return state.companyDetailInfo.rolePrivileges;
    },

    doc_getUserRole: state => {
        return state.commonHeaderInfo.roleDetails;
    },

    getUserType: state => {
        return state.commonHeaderInfo.userType;
    },
    isPerson: state => state.commonHeaderInfo.userType === 'Person',
    isEnt: state => state.commonHeaderInfo.userType === 'Enterprise',
    getPlatformUserAuthStatus: state => {
        return state.commonHeaderInfo.platformUser.authStatus;
    },
    getAuthStatus: state => {
        let authStatus = '';

        if (state.commonHeaderInfo.userType === 'Person') {
            authStatus = state.commonHeaderInfo.platformUser.authStatus;
        } else {
            authStatus = getters.getCurrentEntInfo(state) ? getters.getCurrentEntInfo(state).authStatus : '';
        }

        return authStatus;
    },
    isAuth: (state, getters) => getters.getAuthStatus === 2,
    getUserId: state => {
        return state.commonHeaderInfo.platformUser.userId;
    },
    getUserFullName: state => {
        return state.commonHeaderInfo.platformUser.fullName;
    },
    getUserAccount: state => {
        return state.commonHeaderInfo.platformUser.account;
    },
    getCurrentEntInfo: state => {
        const temObj = state.commonHeaderInfo;
        const ents = temObj.enterprises;
        const curEntId = temObj.currentEntId;
        let curEntInfo;
        ents.forEach((val) => {
            if (val.entId === curEntId) {
                curEntInfo = val;
            }
        });
        return curEntInfo || {};
    },
    getUserPermissons: state => {
        const roles = state.commonHeaderInfo.roleDetails;
        const permissionObj = {
            DOWNLOAD_CONTRACT: false,
            SEND_TEMPLATE_CONTRACT: false,
            SEND_LOCAL_FILE: false,
            // SEND_CONTRACT_OUT: false,
            SHARE_CONTRACT: false,
            SIGN_CONTRACT: false,
            CREATE_CONTRACT_TEMPLATE: false,
            STATS_CONTRACT: false,
            SIGN_M: false,
            ACCOUNT_M: false,
            CHARGE_M: false,
        };

        // 遍历角色权限
        if (roles) {
            roles.forEach(role => {
                role.privileges.forEach(privilege => {
                    permissionObj[privilege.name] = true;
                });
            });
        }
        return {
            download: permissionObj['DOWNLOAD_CONTRACT'],
            sendTemp: permissionObj['SEND_TEMPLATE_CONTRACT'],
            sendLocal: permissionObj['SEND_LOCAL_FILE'],
            // sendOut: permissionObj['SEND_CONTRACT_OUT'],
            share: permissionObj['SHARE_CONTRACT'],
            sign: true, // permissionObj['SIGN_CONTRACT'] 默认都有签署权限,
            createTemp: permissionObj['CREATE_CONTRACT_TEMPLATE'],
            statistics: permissionObj['STATS_CONTRACT'],
            sign_m: permissionObj['SIGN_M'],
            account_m: permissionObj['ACCOUNT_M'],
            recharge_m: permissionObj['CHARGE_M'],
        };
    },
    hasManager: state => {
        let status = false;
        const roles = state.commonHeaderInfo.roleDetails;

        // 遍历角色权限
        if (roles) {
            roles.forEach(role => {
                if (role.name === '主管理员' || role.name === i18n.t('CSSetting.admin')) {
                    status = true;
                }
            });
        }

        return status;
    },
    // 判断在集团群中，只在企业控制台中使用
    hasGroup: state => {
        return state.companyDetailInfo.entGroup !== void (0) && Object.keys(state.companyDetailInfo.entGroup).length > 0;
    },
    // 判断是集团群主企业，只在企业控制台中使用
    isGroupMaster: state => {
        return state.companyDetailInfo.entGroup !== void (0) && (
            state.commonHeaderInfo.currentEntId === state.companyDetailInfo.entGroup.adminEntId
        );
    },
    // 是否开启签约密码二次校验 '0'/'1'
    SIGN_SECOND_CHECK: state => state.commonHeaderInfo.SECOND_CHECK === '1',
    // 获取当前用户引导版本
    // getVersion: state => {
    //     return state.home.version;
    // },
    // 判断混合云用户处于内网环境中
    getInLAN: state => {
        return state.commonHeaderInfo.LANConnected;
    },
    getHybridUserType: state => {
        return state.hybridUserType;
    },
    // 是否是单点登录进来的
    getIsLoginFromDeveloper: state => {
        return state.commonHeaderInfo.extendFields.loginType === 'SSO';
    },
    // 是否是单点登录进来的内部成员
    getIsInnerMember: state => {
        return state.commonHeaderInfo.extendFields.isInnerMember;
    },
    // 标识来源 BESTSIGN, OAUTH, SSO
    getLoginType: state => {
        return state.commonHeaderInfo.extendFields.loginType;
    },
    // 初始业务线
    getIsMainBusinessLine: state => {
        return state.commonHeaderInfo.extendFields.bizLineRole === 'CREATOR';
    },
    // 非初始业务线
    getIsJoinedBusinessLine: state => {
        return state.commonHeaderInfo.extendFields.bizLineRole === 'JOINED';
    },

    // 是否启用了模板匹配功能
    // 需要打开后台'模板匹配规则'且至少有添加了一条规则
    // hasActiveTemplateMatch: state => {
    //     let conf = state.commonHeaderInfo.templateMatchConf;
    //     // return conf.hasSetTemplateMatch && conf.hasActiveMatchRule
    //     return conf.hasSetTemplateMatch
    // },
    // 单点登录配置
    getSsoConfig: (state) => {
        return state.ssoConfig;
    },
    // 检测账号是否包含功能点, 乐高成配置
    checkFeat: ({ features }) => {
        const map = {};
        for (const featureId in FEATURE_MAP) {
            const key = FEATURE_MAP[featureId];
            map[key] = features.includes(featureId);
        }
        return map;
    },
    // 单点登录配置
    getAdvancedFeatures: (state) => {
        return state.advancedFeatures;
    },
    // 检测高级功能是否开启
    checkAdvancedFeatureData: (state) => {
        return (featureId) => {
            const mapData = state.advancedFeatures.find(e => e.featureId === featureId);
            return mapData ?? {};
        //    let map = {};
        //    if(!mapData) return map; //非高级功能
        //    map = mapData;
        //    const {isOpen,onTrial} = mapData;
        //    if(isOpen && onTrial !== 1){ //( onTrial => 0:未试用，1：试用中，2：试用到期)
        //     map.status = 4;//已购买，开启状态
        //    }
        //    return map;
        };
    },
};

// 更改 store 中的状态，通过 store.commit 调用
const mutations = {
    setAuthPassTime(state, data) {
        state.authPassTime = data;
    },
    pushCompanyDetailInfo(state, data) {
        state.companyDetailInfo = data;
        state.companyDetailInfo.haveData = true;
    },
    logout(state) {
        state.userInfo = null;
        state.login = false;
        state.noMorePhoneSetTip = false;
        state.companyDetailInfo = {
            adminEmployee: {

            },
            corpAuth: {

            },
            entGroup: {

            },
            enterprise: {

            },
            rolePrivileges: {

            },
            haveData: false,
        };
        /*
        * fix SAAS-5065 未实名的企业在合同管理页面点退出的时候页面卡死
        * 报错 vue.runtime.esm.js:559 DOMException: Failed to execute 'insertBefore' on 'Node': The node before which the new node is to be inserted is not a child of this node.
        * 暂时找不到具体引起报错的模版代码，可能是v-if和v-for使用有问题https://stackoverflow.com/questions/51653178/vue-warn-error-in-nexttick-notfounderror-failed-to-execute-insertbefore
        *
        */
        const commonHeaderInfo = {
            SECOND_CHECK: '0',
            status: false,
            currentEntId: '',
            hasManager: '',
            // userType: '', // 注释。打开， 未实名的企业在合同管理页面点退出 js报错
            enterprises: [],
            platformUser: {},
            extendFields: {},
            hybridServer: '',
            LANConnected: null,
            templateMatchConf: {},
        };
        state.commonHeaderInfo = {
            ...state.commonHeaderInfo,
            ...commonHeaderInfo,
        };
        Vue.$cookie.delete('sensorsUserInfo');
    },
    pushInitiPerCerInfo(state, data) {
        state.initiPerCerInfo = data;
    },
    pushInitCommonHeaderInfo(state, data) {
        // 增加业务线之后，企业的全名为:entName_bizName
        data.enterprises.map(item => {
            const { bizName, entName } = item;
            item.fullEntName = bizName ? `${entName}_${bizName}` : entName;
            return item;
        });
        if (data.ecologyEnterprises) {
            data.ecologyEnterprises.forEach(item => {
                item.entId = item.entId + '-ecology';
            });
        }
        // 将生态版企业主体和旗舰版正常的企业合并在一起
        data.enterprises.push(...(data.ecologyEnterprises || []));
        for (const key in data) {
            const val = data[key];
            // state.commonHeaderInfo[key] = val === null ? '' : val;
            Vue.set(state.commonHeaderInfo, key, val === null ? '' : val);
        }
        // state.login = true;
        Vue.set(state, 'login', true);
        Vue.set(state.commonHeaderInfo, 'status', true);

        // sensors公共属性不能取对象的属性，只能把属性放在cookie中
        const userType = state.commonHeaderInfo?.userType || '';
        Vue.$cookie.set('sensorsUserInfo', JSON.stringify({
            currentEntId: state.commonHeaderInfo?.currentEntId || '',
            currentAccountType: userType ? (userType.toLowerCase() === 'enterprise' ? '企业账号' : '个人账号') : '',
            currentEntName: getSensorsCurrentEntInfo(state.commonHeaderInfo),
            currentRoleName: getSensorsCurrentRoleName(state.commonHeaderInfo),
        }));
    },
    // 更新当前用户引导版本
    // pushVersion(state, data) {
    // 	state.home.version = data;
    // },
    // 混合云网络状态改变
    changeLANStatus(state, data) {
        state.commonHeaderInfo.LANConnected = data;
    },

    // changeCacheLANStatus(state, data) {
    // 	state.commonHeaderInfo.cacheLANConnected = data;
    // },
    // 替换 hybridAccessToken 和 hybridServer
    replaceHybridAccessToken(state, { hybridServer, hybridAccessToken }) {
        state.commonHeaderInfo.hybridServer = hybridServer;
        state.commonHeaderInfo.hybridAccessToken = hybridAccessToken;
    },
    // 修改 hybridUserType
    changeHybridUserType(state, data) {
        state.hybridUserType = data;
    },
    // 认证步骤
    setAuthStep(state) {
        state.authStep = 1;
    },
    // 单点登录配置相应隐藏
    pushSsoConfig(state, data) {
        state.ssoConfig = data;
    },
    // 根据已开启的功能点显示页面元素
    pushFeatures(state, data) {
        state.version = data.version;
        state.features = data.featureIds;
    },
    // 高级功能点列表
    pushAdvancedFeatures(state, data) {
        state.advancedFeatures = data;
    },
    // 保存主动上报日志的接口数据
    setReportHttpData(state, data) {
        const arr = state.reportHttpData;
        // 接口去重
        const isRepeatUrl =  (arr || []).some(val => val.url === data.url);
        if (isRepeatUrl) {
            (arr || []).forEach((val) => {
                if (val.url === data.url) {
                    val.data = data.data;
                }
            });
            state.reportHttpData = arr;
            return;
        }
        // 保留10个接口
        if (arr.length >= 10) {
            arr.shift(); // 移除数组中第一条数据
        }
        arr.push(data);
        state.reportHttpData = arr;
    },
    pushNoticeCount(state, data) {
        state.noticeCount = data;
    },
    setSignImageBase64(state, base64) {
        state.signImageBase64 = base64;
    },
    setNewContract(state, data) {
        state.newContract = data;
    },
    setHybridVersion(state, data) {
        // console.log('hybridVersion', data)
        state.hybridVersion = data;
    },
    setIsQyWx(state, data) {
        state.isQyWx = data;
    },
    setIsFirstQywx(state, data) {
        state.isFirstQywx = data;
    },
    setBrandStatus(state, status) {
        state.isBrand = status;
    },
    setSignPlatform(state, data) {
        state.signPlatform = data;
    },
    updateCurrentEntId(state, entId) {
        state.currentEntId = entId;
    },
    setIsCurrentDomainLogin(state, data) {
        state.isCurrentDomainLogin = data;
    },
    setConsoleBizName(state, bizName) {
        state.companyDetailInfo.enterprise.bizName = bizName;
    },
    setLocalFieldSupportType(state, supportType) {
        state.localFieldSupportType = supportType;
    },
    setNoLoginLanguage(state, noLoginLanguage) {
        state.noLoginLanguage = noLoginLanguage;
        sessionStorage.setItem('noLoginLanguage', noLoginLanguage);
    },
    resetNoLoginLanguage(state) {
        state.noLoginLanguage = '';
        sessionStorage.setItem('noLoginLanguage', '');
    },
    setSignPageScrollProgress(state, progress) {
        state.scrollProgress = progress;
    },
};

export default new Vuex.Store({
    state,
    actions,
    getters,
    mutations,
    modules: {
        template,
        doc,
        hubble,
        approval,
        sendingPrepare,
        dynamic,
        docTranslation,
    },
});
