import dayjs from 'dayjs';
export default {
    name: 'approval',
    namespaced: true,
    state: {
        contractId: '',
        receiverId: '',
        documentId: '',
        annotateList: [],
        currentEditAnnotateData: {},
        showEditDialog: false,
        currentAnnotationId: '',
        isMgapprovenoteMove: false,
        currentRect: null,
        hasAuthorized: false,
        showAuthDialog: false,
    },
    actions: {
        getAnnotationHistory({ state }) {
            return Vue.$http(`/contract-api/contract/${state.contractId}/receiver/${state.receiverId}/approval-history`).then(res => {
                state.annotateList = (res.data || []).map(el => {
                    el.createTime = dayjs(el.createTime).format('YYYY-MM-DD HH:mm:ss');
                    return el;
                });
            });
        },
        saveAnnotation({ state }, data) {
            return Vue.$http.post(`/contract-api/contract/${state.contractId}/receiver/${state.receiverId}/approval-annotation/save`, data);
        },
        deleteAnnotation({ state }, annotationId) {
            return Vue.$http.post(`/contract-api/contract/${state.contractId}/receiver/${state.receiverId}/approval-annotation/${annotationId}/delete `);
        },
        handleOtherTask({ state }, { type, data }) {
            return Vue.$http.post(`/contract-api/contract/${state.contractId}/receiver/${state.receiverId}/approval-operation?operation=${type}`, data, { noToast: 1 });
        },
        checkApprovalAuth({ state }) {
            return Vue.$http(`/contract-api/check-if-grant-approval-operation`).then(res => {
                state.hasAuthorized = res.data;
            });
        },
    },
    mutations: {
        pushAnnotateList(state, data) {
            state.annotateList.unshift(data);
        },
        deleteAnnotateList(state, annotationId) {
            state.annotateList = state.annotateList.filter(item => item.annotationId !== annotationId);
        },
        setCurrentAnnotationId(state, id) {
            state.currentAnnotationId = id;
        },
        setMgapprovenoteMove(state, bool) {
            state.isMgapprovenoteMove = bool;
        },
    },
    getters: {

    },
};
