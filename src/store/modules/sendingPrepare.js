import Vue from 'vue';

export default {
    name: 'sendingPrepare',
    namespaced: true,
    state: {
        receptionCollection: false, // 乐高城是否开通前台代收功能
    },
    actions: {
        getReceptionCollectionConfig({ commit }) {
            return Vue.$http.get('/template-api/templates/receiver/claim')
                .then(res => {
                    const { result } = res.data;
                    commit('setReceptionCollection', result);
                });
        },
    },
    mutations: {
        setReceptionCollection(state, data) {
            state.receptionCollection = data;
        },
    },
};
