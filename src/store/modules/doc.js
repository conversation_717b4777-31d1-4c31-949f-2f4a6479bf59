// 合同管理的状态
export default {
    name: 'doc',
    namespaced: true,
    state: {
        folderObject: {}, // 文件夹列表
        fieldConfig: { // 列表配置相关数据
            unselectedFields: [],
            selectedFields: [],
        },
        tableData: {
            contractPage: {},
            headerList: [],
        },
        searchParams: { // 搜索选项的值，分为folderId 或者 特殊状态、右侧搜索框内容 、列表排序状态
            contractSourceSubjects: [],
            contractStateNames: [], // 合同状态
            pageRequest: {},
            searchEntryParams: {}, // 入口参数，默认在发件箱
            searchFieldParams: [], // 其他剩余搜索参数
            tagIds: [], // 标签
        },
        tableLoading: false,
        tagManageEnabled: false, // 是否启用合同标签，不只当前企业，包含对应的集团
        shortcuts: [], // 快捷入口列表
        tagOptionItems: [], // 可进行搜索的标签
        tagOptionItemsMerged: [], // 所有企业的标签
        operationsLoading: false, // 是否正在加载批量操作
        totalDocLength: 0,
    },
    mutations: {
        // 切换文件夹
        switchFolder(state, folder) {
            // 切换store中的当前文件夹位置
            state.searchParams.searchEntryParams = {
                folderId: folder.folderId,
                shortcutId: folder.shortcutId,
            };
            // 更新页码
            state.searchParams.pageRequest = {
                currentPage: 1,
                pageSize: 50,
            };
        },
        // 更新配置列表
        setFieldConfig(state, data) {
            state.fieldConfig = data;
        },
        setTableRecords(state, records) {
            state.tableData.contractPage.records = records;
        },
        setTableData(state, data) {
            state.tableData = data;
        },
        setTableContractPageData(state, data) {
            state.tableData.contractPage = data;
        },
        setFolderObject(state, folderObject) {
            state.folderObject = folderObject;
        },
        // 更新搜索的参数，包括更多，合同状态和合同持有人的搜索参数
        setSearchBoxParams(state, params) {
            state.searchParams.contractSourceSubjects = params.contractSourceSubjects;
            state.searchParams.contractStateNames = params.contractStateNames;
            state.searchParams.searchFieldParams = params.searchFieldParams;
        },
        setSearchPageIndex(state, pageIndex) {
            state.searchParams.pageRequest.currentPage = pageIndex;
        },
        setSearchPageSize(state, pageSize) {
            state.searchParams.pageRequest.pageSize = pageSize;
        },
        setTableLoading(state, tableLoading) {
            state.tableLoading = tableLoading;
        },
        setShortCuts(state, data) {
            state.shortcuts = data;
        },
        setTagManageEnabled(state, enabled) {
            state.tagManageEnabled = enabled;
        },
        setTagOptionItems(state, items) {
            state.tagOptionItems = [...items];
        },
        setTagOptionItemsMerged(state, items) {
            state.tagOptionItemsMerged = [...items];
        },
        selSearchTagId(state, tagId) {
            state.searchParams.tagIds.push(tagId);
        },
        releaseSelSearchTagId(state, tagId) {
            state.searchParams.tagIds.splice(state.searchParams.tagIds.indexOf(tagId), 1);
        },
        resetSearchTagId(state) {
            state.searchParams.tagIds = [];
        },
        mergeTagsData(state, tagsInfo) {
            let { records } = state.tableData.contractPage;
            const tagsDataObj = {};
            tagsInfo.forEach(a => {
                tagsDataObj[a.contractNo] = a.tags;
            });
            records = records.map(record => ({
                ...record,
                tags: tagsDataObj[record.contractNo],
            }));
            state.tableData.contractPage.records = [...records];
        },
        setOperationsLoading(state, operationsLoading) {
            state.operationsLoading = operationsLoading;
        },
        setContractsOperations(state, contractsOperations) {
            let { records } = state.tableData.contractPage;
            records = records.map(record => {
                const { operations, reSendParam } = contractsOperations.find(item => item.contractId === record.contractId);
                return {
                    ...record,
                    operations,
                    ...reSendParam,
                };
            });
            state.tableData.contractPage.records = records;
        },
    },
    actions: {
        // 获取文件夹列表actions提取
        querySearch({ dispatch, commit, state }) {
            commit('setTableLoading', true);
            const searchParams = state.searchParams;
            setTimeout(() => {
                return Vue.$http.post('/contract-search/web/search', searchParams, { loading: true })
                    .then(({ data: { data: result } }) => {
                        commit('setTableData', result);
                        dispatch('queryContractsOperations', result);
                        dispatch('queryContractsTags', result);
                    })
                    .catch(() => {})
                    .finally(() => {
                        commit('setTableLoading', false);
                    });
            }, 1500);
        },
        queryContractsTags({ commit, state }, { contractPage: { records = [] } }) {
            return Vue.$http.post('/contract-center-bearing/business-tags/show-tags-for-contracts', {
                contractNos: records.map(a => a.contractNo),
                contractSourceSubjects: state.searchParams.contractSourceSubjects,
            }).then(({ data: { data } }) => {
                commit('mergeTagsData', data);
            });
        },
        // 查询文件夹列表
        queryFolderList({ commit }) {
            Vue.$http.get('/contract-center-bearing/folder/list')
                .then(({ data: { data } }) => {
                    commit('setFolderObject', data);
                });
        },
        // 共享/取消共享文件夹
        updateShareFolder({ dispatch }, folder) {
            const { shareable, folderId } = folder;
            Vue.$http.put(`/contract-center-bearing/folder/share/${folderId}`, { shareable: !shareable })
                .then(() => {
                    dispatch('queryFolderList');
                });
        },
        // 查询快捷入口列表
        queryShortCuts({ commit }) {
            return Vue.$http.get('/contract-center/contract-search/shortcuts?queryCount=true')
                .then(({ data: { data: result } }) => {
                    commit('setShortCuts', result);
                    return result;
                });
        },
        queryTagManageStatus({ commit }) {
            // 接口调用优化
            // https://jira.bestsign.tech/browse/INFRASTRUC-2533
            Vue.$http.get('/contract-center-bearing/business-tags/service-enable-info')
                .then(({ data: { data: { enabled = false } } }) => {
                    commit('setTagManageEnabled', enabled);
                }).catch(() => {});
        },
        queryAllTagOptions({ commit }) {
            // 接口调用优化
            // https://jira.bestsign.tech/browse/INFRASTRUC-2533
            Vue.$http.get('/contract-center-bearing/business-tags/visible-tags')
                .then(res => {
                    const data = res.data || {};
                    let tagList = [];
                    data.data.map(ent => {
                        tagList = tagList.concat(ent.tags.map(a => ({
                            ...a,
                            enterpriseName: ent.enterpriseName,
                            enterpriseId: ent.enterpriseId,
                        })));
                    });
                    commit('setTagOptionItems', data.data);
                    commit('setTagOptionItemsMerged', tagList);
                });
        },
        updateTagIdsSearchParam({ state, commit, dispatch }, tagId) {
            const { tagIds } = state.searchParams;
            if (tagIds.includes(tagId)) {
                commit('releaseSelSearchTagId', tagId);
            } else {
                commit('selSearchTagId', tagId);
            }
            dispatch('querySearch');
        },
        // 查询搜索列表对应的合同操作
        queryContractsOperations({ commit, state }, { contractPage: { records = [] } }) {
            commit('setOperationsLoading', true);
            const searchEntryParams = state.searchParams.searchEntryParams;
            Vue.$http.post(`/contract-center/search-contracts/contract-operations`, {
                searchEntryParams,
                contractIds: records.map(a => a.contractId),
            }).then(({ data }) => {
                commit('setContractsOperations', data.data);
            }).finally(() => {
                commit('setOperationsLoading', false);
            });
        },
    },
    getters: {
        // 是否在左侧特殊状态文件夹下
        isStateFolder: (state) => {
            return !state.searchParams.searchEntryParams.folderId;
        },
        isClaimedShortcut: (state) => {
            if (!state.searchParams.searchEntryParams.shortcutId) { // 如果不是快捷入口返回false
                return false;
            }
            const currentShortcut = state.shortcuts.filter(a => a.shortcutId === state.searchParams.searchEntryParams.shortcutId)[0];
            return currentShortcut.name === '待认领合同';
        },
        isSharedFolder: (state) => {
            const folderId = state.searchParams.searchEntryParams.folderId;
            // folderId不存在，说明在特殊状态文件夹内
            if (!folderId)  {
                return false;
            }
            const findIndex = (state.folderObject.sharedFolders || []).findIndex(item => folderId === item.folderId);
            return findIndex !== -1;
        },
    },
};
