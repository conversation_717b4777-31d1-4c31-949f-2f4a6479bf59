export default {
    name: 'template',
    namespaced: true,
    state: {
        multipleDynamicSigner: false, // 是否开启多方签署
        canModifyWhenUsed: true, // 使用模版是否可以 修改合同配置，true表示可以修改
        receivers: [], // 签署方列表
        receiverIndex: 0, // 当前第几个签署方
        currentDocIndex: 0, // 当前第几份文档
        currentPageIndex: 0, // 第几份文档的第几页
        zoom: 1, // 文档缩放
        docList: [], // 文档
        watermarkList: [],
        ridingSealList: [],
        focusLabelOpt: { // 选中的标签
            labelId: '',
            labelButtonInd: -1,
        },
        templateStatus: '', // 编辑edit/使用use
        isLimitFaceConfig: false, // 配置刷脸售是否受限（乐高城低于刷脸成本价，不能使用刷脸签）
    },
    actions: {
        getIsLimitFaceConfig({ commit }) {
            return Vue.$http.get('/template-api/v2/draft/sender/config')
                .then(({ data }) => {
                    const faceFeatureLimit = data?.result?.faceFeatureLimit;
                    commit('setIsLimitFaceConfig', faceFeatureLimit);
                });
        },
        getMultipleDynamicSignerConfig({ commit }) {
            return Vue.$http.get('/template-api/templates/permission/query/group-ent-info')
                .then(res => {
                    const { result } = res.data;
                    commit('setMultipleDynamicSigner', result.multipleDynamicSigner);
                });
        },
    },
    mutations: {
        setIsLimitFaceConfig(state, isLimitFaceConfig) {
            state.isLimitFaceConfig = isLimitFaceConfig;
        },
        setMultipleDynamicSigner(state, data) {
            state.multipleDynamicSigner = data;
        },
        setCanModifyWhenUsed(state, data) {
            state.canModifyWhenUsed = data;
        },
        setDocList(state, docList) {
            state.docList = docList;
        },
        setCurrentDocIndex(state, currentDocIndex) {
            state.currentDocIndex = currentDocIndex;
        },
        setCurrentPageIndex(state, currentPageIndex) {
            state.currentPageIndex = currentPageIndex;
        },
        setFocusLabelOpt(state, focusLabelOpt) {
            state.focusLabelOpt = focusLabelOpt;
        },
        setZoom(state, zoom) {
            state.zoom = zoom;
        },
        setWatermarkList(state, watermarkList) {
            state.watermarkList = watermarkList;
        },
        setRidingSealList(state, ridingSealList) {
            state.ridingSealList = ridingSealList;
        },
        setTemplateStatus(state, templateStatus) {
            state.templateStatus = templateStatus;
        },
        setReceivers(state, receivers) {
            state.receivers = receivers;
        },
    },
    getters: {
        currentDoc: (state) => {
            return state.docList.length ? state.docList[state.currentDocIndex] : { labels: [] };
        },
    },
};
