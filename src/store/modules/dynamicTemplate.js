import cloneDeep from 'lodash/cloneDeep';
export default {
    name: 'dynamic',
    namespaced: true,
    state: {
        preparingItem: { // 待插入的项
            show: false,
            type: '',
            // ========
            name: '',
            showName: '', // 在头部模版名称后显示的提示信息名称
            receiverFill: false,
            fontSize: 14,
            necessary: true,
            buttonValue: '', // 单复选备选项值
        },
        focusItem: { // 当前编辑项
            markIndex: -1,
            signerIndex: -1,
            mark: {},
            buttonBookmark: '', // 单复选项bookmark
        },
        canModifyWhenUsed: true, // 使用模版是否可以 修改合同配置，true表示可以修改
        marks: [],
        docList: [],
        activeDocIndex: 0, // 当前打开的文档
        currentPageIndex: 1, // 当前页码用于页码切换滚动
        receivers: [],
        activeReceiverIndex: 0, // 左侧当前选中的签署人
        termTypeList: [], // 合同条款数据
        tableCellWidthType: 0, // 动态表格列宽格式：0: 等宽，1: 自适应
    },
    // actions: {
    //     getMultipleDynamicSignerConfig({ commit }) {
    //         return Vue.$http.get('/template-api/templates/permission/query/group-ent-info')
    //             .then(res => {
    //                 let { result } = res.data;
    //                 commit('setMultipleDynamicSigner', result.multipleDynamicSigner);
    //             })
    //     }
    // },
    mutations: {
        setFocusItem(state, item) {
            state.focusItem = cloneDeep(item);
        },
        resetFocusItem(state) {
            state.focusItem = {
                markIndex: -1,
                signerIndex: -1,
                mark: {},
                buttonBookmark: '', // 单复选项bookmark
            };
        },
        setTermTypeList(state, data) {
            state.termTypeList = data;
        },
        setActiveReceiverIndex(state, index) {
            state.activeReceiverIndex = index;
        },
        setReceiversData(state, data) {
            state.receivers = data;
        },
        setCurrentPageIndex(state, index) {
            state.currentPageIndex = index;
        },
        setActiveDocIndex(state, index) {
            state.activeDocIndex = index;
        },
        setDocListData(state, data) {
            state.docList = data;
        },
        setDocMarks(state, marks) {
            state.marks = marks;
        },
        setPreparingItem(state, item) {
            state.preparingItem = item;
        },
        setTableCellWidthType(state, type) {
            state.tableCellWidthType = type;
        },
        resetPreparingItem(state) {
            state.preparingItem = {
                show: false,
                type: '',
                name: '',
                showName: '', // 在头部模版名称后显示的提示信息名称
                receiverFill: false,
                fontSize: 14,
                necessary: true,
                buttonValue: '', // 单复选备选项值
            };
        },
        resetDynamicInfo(state) {
            state.activeDocIndex = 0;
            state.currentPageIndex = 1;
            state.focusItem = {
                markIndex: -1,
                signerIndex: -1,
                mark: {},
                buttonBookmark: '', // 单复选项bookmark
            };
            state.preparingItem = {
                show: false,
                type: '',
                name: '',
                showName: '', // 在头部模版名称后显示的提示信息名称
                receiverFill: false,
                fontSize: 14,
                necessary: true,
                buttonValue: '', // 单复选备选项值
            };
        },
    },
};
