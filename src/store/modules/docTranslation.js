export default {
    name: 'docTranslation',
    namespaced: true,
    state: {
        originFileReady: false, // 原文上传完毕
        translationFileId: '', // 译文fileId
        // hoverSentence: -1, // hover第index个条数据
        // translateRecords: [], // 翻译结果数据
        translationStatus: 0, // 翻译状态 0 未翻译  1.翻译中 2.翻译结束
        translationId: '', // 翻译的任务id
    },
    mutations: {
        // setHoverSentence(state, data) {
        //     state.hoverSentence = data;
        // },
        setOriginFileReady(state, data) {
            state.originFileReady = data;
        },
        setTranslationFileId(state, data) {
            state.translationFileId = data;
        },
        // setTranslateRecords(state, data) {
        //     state.translateRecords = data;
        // },
        setTranslationStatus(state, data) {
            state.translationStatus = data;
        },
        setTranslationId(state, data) {
            state.translationId = data;
        },
    },
};
