
// 纸质签署
export function paperSignFn(contractId) {
    // SAAS-30100 混合云纸质签署
    return Vue.$hybrid.makeRequest({
        url: `/contract-api/contracts/${contractId}/paper-sign/sign`,
        hybridTarget: '/contracts/paper-sign/sign',
        method: 'post',
        data: {
            contractId: contractId,
        },
    });
}
/**
 * 获取管理员信息，用在权限申请页
 */
export function getAdminInfo(entName) {
    return Vue.$http.get(`/ents/employees/admin-info?entName=${entName}`);
}

/**
 * 获取所有的企业信息
 */
export function getAllEntInfo() {
    return Vue.$http.get('/users/chosen-enterprises');
}

/**
 * 是否展示主管理员确认弹窗
 */
export function getCheckAdmin(contractId) {
    return Vue.$http.get(`/ents/employees/checkAdmin?contractId=${contractId}`);
}

/**
 * 点击我是主管理员或者转交，通知下后端
 */
export function postCheckAdmin() {
    return Vue.$http.post('/ents/employees/checkAdmin');
}

// 更新FDA签名
export function fillFDASignature(mark) {
    const { contractId, signerFDAReason = '', signerFDAName = '', updateType } = mark;
    if (updateType === 'NAME') {
        return Vue.$http.post(`/contract-api/contracts/${contractId}/fillFDASignatureInfo`, { signerFDAName });
    } else {
        return Vue.$http.post(`/contract-api/contracts/${contractId}/fillFDASignatureInfo`, { signerFDAReason });
    }
}

// 校验ocr印章
export function getSealOcrRecognition(contractId, receiverId) {
    return Vue.$http.get(`/contract-api/contracts/${contractId}/receiver/${receiverId}/seal-ocr-recognition`);
}

/**
 * @desc 实名不一致时文案和弹窗提示
 * @param contractId { String }  合同编号
 * */
export function getAuthChangeCopyWriting(contractId) {
    return Vue.$http.post(`/contract-api/contracts/${contractId}/auth-change-copywriting`);
}

/**
 *
 * @description 获取企业印章
 * @export
 * @param {*} entId
 * @returns
 */
export function getEntSeals(entId) {
    return Vue.$http.get(`/ents/seals/console-manager?entId=${entId}`).then(({ data }) => {
        if (data && data.length > 0) {
            return data.filter(ele => !ele.resetFlag).map(item => ({
                ...item,
                sealImg: `/ents/${item.sealId}/seal/${item.fileId}?access_token=${Vue.$cookie.get('access_token')}`,
            }));
        }
        return [];
    });
}

/**
 * 判断当前用户是否是黑名单用户，黑名单用户不展示广告位
 */
export function checkBlackAd() {
    return Vue.$http.post('/ad-api/black/check');
}

/**
 * 增加定制广告位点击记录
 */
/**
 * 增加定制广告位点击记录
 */
export function addAdRecord({ adId = 0, adPageAddress = '', click = false, acceptPhoneCall = false, acceptSmsNotice = false }) {
    return Vue.$http.post('/ad-api/record/add', {
        adId,
        adPageAddress,
        click,
        acceptPhoneCall,
        acceptSmsNotice,
    });
}

/**
 * 获取广告信息
 */
export function getAdInfo({ scene = '', deliveryChannel = 0, contractId = '' }) {
    return Vue.$http.get(`/ad-api/plan/query?scene=${scene}&deliveryChannel=${deliveryChannel}&contractId=${contractId}`);
}

/**
 * 印章签名同步于所有地方-非扫码签署
 */
export function syncAllSignLabel({ contractId, labelId }) {
    return Vue.$http.post(`/contract-api/contracts/sync-all-sign-label`, {
        contractId,
        labelId,
    });
}

/**
 * 印章签名同步于所有地方-扫码签署
 */
export function syncAllSignLabelIgnore({ token }) {
    return Vue.$http.post('/contract-api/ignore/contracts/sync-all-sign-label', {
        token,
    });
}
// 通过后端接口更新同名字段的值
export function requestFieldValuesByName(contractId, mark, value) {
    return Vue.$http.post(`/contract-api/contracts/${contractId}/labels/fill-content`, {
        name: mark.name,
        receiverId: mark.receiverId,
        value,
    });
}

/**
 * @desc 提交用户发合同时点击添加企业使用引导的提醒状态
 * */
export function postTemplateSendGuideRemindInfo(value) {
    return Vue.$http.post(`/users/configs/TEMPLATE_USE_GUIDE`, {
        name: 'TEMPLATE_USE_GUIDE',
        value,
    });
}

/**
 * @desc 获取模板使用时点击添加企业使用引导的提醒状态记录
 * */
export function getTemplateSendGuideRemindInfo() {
    return Vue.$http.get(`/users/configs/TEMPLATE_USE_GUIDE`);
}

/**
 * hubble合同比对获取跳转权限
 */
export function getComparePermission(contractId) {
    return Vue.$http.get(`/web/document-compare/documents/permission-check/${contractId}`);
}

/**
 * hubble功能是否支持该合同
 */
export function hubblePermissionCheck(contractId, hubbleToolType) {
    return Vue.$http.get(`/contract-api/contracts/contract-upload-permission-check/${contractId}`, {
        params: {
            hubbleToolType,
        },
    });
}

/**
 * 根据操作类型及合同id获取签署人信息
 * @param contractId String 合同ID
 * @param type String 企业ID
 */
export function getMyRecieversByType({ contractId, type }) {
    return Vue.$http.get(`/contract-api/contracts/${contractId}/my-receivers`, {
        params: {
            type,
        },
    });
}

/**
 * 跳转微信小程序生成跳转链接接口
 * @param sourceType String 小程序类型 签署小程序-101
 * @param path String 小程序页面路径
 * @param query Object 小程序页面参数
 * @param envVersion String 需要跳转的小程序版本  正式版-"release"，体验版-"trial"，开发版-"develop"
 */
export function fetchAppletUrl(sourceType, path, query, envVersion = 'release') {
    return Vue.$http.post('/users/ignore/applets/url-scheme', { sourceType, path, query, envVersion });
}
