// 获取workspaceId
// export function requestWorkspaceId() {
//     return Vue.$http.post(`/web/hubble/workspace/init`);
// }

// 获取batchId
export function requestBatchId(workspaceId) {
    return Vue.$http.post(`/web/hubble/workspace/${workspaceId}/agreements/batch-id`);
}

// 获取batchId
export function checkBalance(workspaceId, batchId) {
    return Vue.$http.post(`/web/hubble/workspace/${workspaceId}/agreements/complete-upload?batchId=${batchId}`);
}

export function startBatchExtraction(workspaceId, data) {
    return Vue.$http.post(`/web/hubble/workspace/${workspaceId}/agreements/start-extraction`, data);
}

export function getBatchExtractionProgress(workspaceId, batchId) {
    return Vue.$http.post(`/web/hubble/workspace/${workspaceId}/agreements/extraction-progress?batchId=${batchId}`);
}

export function exportAgreements(workspaceId, data) {
    return Vue.$http.post(`/web/hubble/workspace/${workspaceId}/agreements/list-views/export`, data);
}

export function restartExtraction(workspaceId, data) {
    return Vue.$http.post(`/web/hubble/workspace/${workspaceId}/agreements/restart-extraction`, data);
}

export function autoDefineTerms(workspaceId, data) {
    return Vue.$http.post(`/web/hubble/workspace/${workspaceId}/agreements/term-auto-define`, data);
}

export function perviewPdf(workspaceId, fileId, params) {
    return Vue.$http.get(`/web/hubble/workspace/${workspaceId}/agreement-review/agreement/${fileId}`, {
        responseType: 'blob',
        params,
    });
}
