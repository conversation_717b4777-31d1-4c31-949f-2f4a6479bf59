import Vue from 'vue';

// 获取文档信息，以及足够扣费
export function getCalculateBilling(translationId) {
    return Vue.$http.get(`/web/hubble/users/tool/calculate-billing/translation/${translationId}`);
}

// 翻译结果
export function getTranslateResult(translationId, lang, termBank, translationStyle) {
    return Vue.$http.post(`/web/hubble/translations/${translationId}/start-translation`, {
        translationLanguage: lang, // CN_EN:中文翻译为英文，EN_CN:英文翻译为中文
        ifUseTermBase: termBank,
        translationStyle,
    });
}

// 获取历史记录
export function getHistory(pageNum) {
    return Vue.$http.get('/web/hubble/translations/translation-records', {
        params: {
            pageNum,
            pageSize: 10,
        },
    });
}

// 查看历史翻译
export function getHistoryDetail(translationRecordId) {
    return Vue.$http.get(`/web/hubble/translations/${translationRecordId}/detail`);
}

// 循环获取翻译结果状态
export function getStatus(translationRecordId) {
    return Vue.$http.get(`/web/hubble/translations/${translationRecordId}/status`);
}

// 合同过来自动上传翻译原文
export function uploadFromContract(translationId, contractId, documentId) {
    return Vue.$http.post(`/web/hubble/translations/${translationId}/contract-file`, {
        contractId,
        documentId,
    });
}

/**
 * 获取术语库
 */
export function getTerms() {
    return Vue.$http.get(`/web/hubble/translations/term/list`);
}

/**
 *  新增术语库项
 */
export function addTerms({ originTerm, targetTerm }) {
    return Vue.$http.post(`/web/hubble/translations/term/add`, {
        originTerm,
        targetTerm,
    });
}

/**
 * 删除术语库项
 */
export function deleteTerm(termId) {
    return Vue.$http.post(`/web/hubble/translations/term/delete/${termId}`);
}

/**
 * 初始化翻译任务
 */
export function initTask() {
    return Vue.$http.post('/web/hubble/translations/init');
}

/**
 * 初始化指定合同文档的Hubble任务
 */
export function initHubbleTask(contractId, documentId, hubbleToolType) {
    return Vue.$http.post(`/contract-api/contracts/${contractId}/document/${documentId}/init-hubble-task`, {
        hubbleToolType,
    });
}

/**
 * 查询当前文档的历史记录
 */
export function currentDocumentHistory(contractId, documentId, hubbleToolType) {
    return Vue.$http.get(`/contract-api/contracts/${contractId}/document/${documentId}/hubble-tasks`, {
        params: {
            hubbleToolType,
            // pageNum,
            // pageSize: 10,
        },
    });
}
