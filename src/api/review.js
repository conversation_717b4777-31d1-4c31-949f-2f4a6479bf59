export function getReviewHistory(page) {
    return Vue.$http.get('/web/hubble/contract-review/history', {
        params: {
            page,
            pageSize: 10,
        },
    });
}

// 获取文档信息，以及足够扣费
export function getBillingInfo(reviewId) {
    return Vue.$http.get(`/web/hubble/users/tool/calculate-billing/review/${reviewId}`);
}

export function startReview(reviewId, positionInContractReview) {
    return Vue.$http.post(`/web/hubble/contract-review/${reviewId}/start-review`, { positionInContractReview });
}

export function getReviewResult(reviewId) {
    return Vue.$http.get(`/web/hubble/contract-review/${reviewId}/detail`);
}

export function getUploadHistory(bizType) {
    return Vue.$http.get('/web/hubble/risk-review/files/history', {
        params: {
            bizType,
        },
    });
}

export function initReviewTask() {
    return Vue.$http.post('/web/hubble/contract-review/init');
}

export function recognizeScan(reviewId) {
    return Vue.$http.post(`/web/hubble/contract-review/${reviewId}/recognize-scanned-copy`);
}

export function addReviewInfo(reviewId, data) {
    return Vue.$http.post(`/web/hubble/contract-review/${reviewId}/submit-field`, data);
}

export function deleteReviewInfo(reviewId, reviewSubmitFieldInfoId) {
    return Vue.$http.post(`/web/hubble/contract-review/${reviewId}/remove-submit-field/${reviewSubmitFieldInfoId}`);
}

export function reviewAsyncTaskStart(reviewId) {
    return Vue.$http.get(`/web/hubble/contract-review/${reviewId}/review-basis-start`);
}

export function reviewAsyncTaskInterval(reviewId) {
    return Vue.$http.get(`/web/hubble/contract-review/${reviewId}/review-basis-finished`);
}

export function getReviewRegulation(reviewId) {
    return Vue.$http.get(`/web/hubble/contract-review/${reviewId}/review-basis-result`);
}

export function saveReviewRegulation(reviewId, data) {
    return Vue.$http.post(`/web/hubble/contract-review/${reviewId}/save-review-basis-async`, data);
}

export function getReviewResultV2(reviewId) {
    return Vue.$http.get(`/web/hubble/contract-review/${reviewId}/detail/v2`);
}
