export function predictContractType(contractId, receiverId, documentId) {
    return Vue.$http.post(`/contract-api/contract/${contractId}/receiver/${receiverId}/document/${documentId}/predict-contract-type`);
}

export function queryTerminologyList(contractId, receiverId, documentId, data) {
    return Vue.$http.post(`/contract-api/contract/${contractId}/receiver/${receiverId}/document/${documentId}/terminology-definition`, data);
}

export function extractKeyInfoByTerm(contractId, receiverId, documentId, data) {
    return Vue.$http.post(`/contract-api/contract/${contractId}/receiver/${receiverId}/document/${documentId}/extract`, data);
}

export function queryExtractionResult(contractId, receiverId, data) {
    return Vue.$http.post(`/contract-api/contract/${contractId}/receiver/${receiverId}/extraction-result`, data);
}

export function supportedContractTypes(contractId, receiverId, documentId) {
    return Vue.$http.get(`/contract-api/contract/${contractId}/receiver/${receiverId}/document/${documentId}/supported-contract-types`);
}
