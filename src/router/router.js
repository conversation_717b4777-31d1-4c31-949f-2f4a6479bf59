/**
 * @description router 配置，beforeEach
 * @param meta 公共配置参数
    noLogin [Boolean] 是否登录才可进入
    title [String/Function] 页面标题
    needHeadInfo [Boolean] 该页面需要请求head-info接口
 */
import store from '../store/store.js';
import App from '../main.vue';

import signRoute from './routes/signRoute.js';
import tempRoute from './routes/tempRoute.js';
import docRoute from './routes/docRoute.js';
import LocalStorage from 'src/common/plugins/localStorage';
import { checkModuleJump } from 'utils/moduleJump.js';
import { loadLanguageAsync } from 'src/lang/index.js';
import i18n from 'src/lang';
import Vue from 'vue';
import { isEntTestInfoEnv } from 'src/common/utils/reg';

const WHITE_HOSTS = ['molyguide'];

// 仅作为本地项目登录用，方便调试
const DEVELOPMENT_ROUTES = !process.env.NODE_ENV.includes('development') ? [] : [{
    path: 'login',
    component: () => import(/* webpackChunkName: "user" */ 'common_pages/login/Login.vue'),
    meta: {
        noLogin: true,
    },
},
// {
//     path: '/account-center/home',
//     component: () => import(/* webpackChunkName: "user" */ 'common_pages/home/<USER>'),
//     meta: {
//         noLogin: false,
//     },
// }
];

const NotFound =
    () =>
        import (/* webpackChunkName: "error" */ 'common_pages/error/NotFound.vue');
const Forbidden =
    () =>
        import (/* webpackChunkName: "error" */ 'common_pages/error/Forbidden.vue');

const routes = [
    { path: '*', component: NotFound },
    {
        path: '/',
        alias: ['/ssoinner', '/ssoouter'],
        component: App,
        children: [{
            path: '',
            redirect: 'login',
            meta: {
                noLogin: true,
            },
        },
        {
            path: 'notFound',
            component: NotFound,
            meta: {
                noLogin: true,
            },
        },
        {
            path: 'forbidden',
            component: Forbidden,
            meta: {
                noLogin: true,
            },
        },
        {
            path: 'hubble',
            component: () => import(/* webpackChunkName: "hubble" */ 'common_pages/hubble/index.vue'),
            children: [{
                path: 'chat/:topicId',
                component: () => import(/* webpackChunkName: "hubble" */ 'common_pages/hubble/chat/index.vue'),
                meta: {
                    hasSlider: true,
                    isHubblePage: true,
                    isHubbleNoLoginPage: true,
                },
            }, {
                path: 'shareChat/:topicId',
                component: () => import(/* webpackChunkName: "hubble" */ 'common_pages/hubble/chat/index.vue'),
                meta: {
                    isSharePage: true,
                    isHubblePage: true,
                    isHubbleNoLoginPage: true,
                },
            }, {
                path: 'upload',
                component: () => import(/* webpackChunkName: "hubble" */ 'common_pages/hubble/upload/index.vue'),
                meta: {
                    isUploadPage: true,
                    hasSlider: true,
                    isHubblePage: true,
                    isHubbleNoLoginPage: true,
                },
            }, {
                path: 'share/:token',
                component: () => import(/* webpackChunkName: "hubble" */ 'common_pages/hubble/share/index.vue'),
                meta: {
                    isSharePage: true,
                    isHubblePage: true,
                    isHubbleNoLoginPage: true,
                },
            }, {
                path: 'agent/upload',
                component: () => import(/* webpackChunkName: "hubble" */ 'common_pages/hubble/upload/index.vue'),
                meta: {
                    isAgentPage: true,
                    isUploadPage: true,
                    hasSlider: true,
                    isHubblePage: true,
                    isHubbleNoLoginPage: true,
                },
            }, {
                path: 'agent/chat/:topicId',
                component: () => import(/* webpackChunkName: "hubble" */ 'common_pages/hubble/chat/index.vue'),
                meta: {
                    hasSlider: true,
                    isHubblePage: true,
                    isHubbleNoLoginPage: true,
                },
            }],
        },
        {
            path: 'hubble-apply/doc-translation',
            component: () => import(/* webpackChunkName: "doc-translation" */'common_pages/docTranslation/index.vue'),
            meta: {
                title: () => i18n.t('docTranslation.title'),
                isHubblePage: true,
            },
        },
        {
            path: 'hubble-apply/doc-translation/:contractId/:documentId',
            component: () => import(/* webpackChunkName: "doc-translation" */'common_pages/docTranslation/index.vue'),
            meta: {
                title: () => i18n.t('docTranslation.title'),
                isHubblePage: true,
            },
        },
        {
            path: 'hubble-apply/contract-comparison',
            component: () => import(/* webpackChunkName: "contract-compare" */ 'src/pages/contractComparison/index.vue'),
            meta: {
                title: () => i18n.t('contractCompare.title'),
                isHubblePage: true,
            },
        },
        {
            path: 'hubble-apply/contract-comparison/:contractId/:documentId',
            component: () => import(/* webpackChunkName: "contract-compare" */ 'src/pages/contractComparison/index.vue'),
            meta: {
                title: () => i18n.t('contractCompare.title'),
                isHubblePage: true,
            },
        },
        {
            path: 'hubble-apply/contract-review',
            component: () => import(/* webpackChunkName: "contract-review" */ 'src/pages/contractReview/index.vue'),
            meta: {
                title: () => i18n.t('contractCompare.review'),
                isHubblePage: true,
            },
        },
        {
            path: 'hubble-apply/contract-extract',
            component: () => import(/* webpackChunkName: "contract-extract" */ 'src/pages/common/contractExtract/index.vue'),
            meta: {
                title: () => i18n.t('contractCompare.extract'),
                isHubblePage: true,
            },
        },
        {
            path: 'hubble-apply/risk-judgement',
            component: () => import(/* webpackChunkName: "contract-extract" */ 'src/pages/contractRiskJudgement/index.vue'),
            meta: {
                title: () => i18n.t('contractCompare.riskJudgement'),
                isHubblePage: true,
            },
        },
        {
            path: 'hubble-workspace',
            redirect: 'hubble-workspace/agreement',
            component: () => import(/* webpackChunkName: "hubble" */ 'common_pages/hubbleWorkspace/index.vue'),
            children: [{
                path: 'agreement',
                component: () => import(/* webpackChunkName: "hubble" */ 'common_pages/hubbleWorkspace/agreement/index.vue'),
            }, {
                path: 'review',
                component: () => import(/* webpackChunkName: "hubble" */ 'common_pages/hubbleWorkspace/review/index.vue'),
            }, {
                path: 'term',
                component: () => import(/* webpackChunkName: "hubble" */ 'common_pages/hubbleWorkspace/term/index.vue'),
            }],
        },
        {
            path: 'hubble-workspace/review-details',
            component: () => import(/* webpackChunkName: "hubble" */ 'common_pages/hubbleWorkspace/review/detail/index.vue'),
        },
        {
            path: 'hubble-workspace/review-manage',
            component: () => import(/* webpackChunkName: "hubble" */ 'common_pages/hubbleWorkspace/review/manage/index.vue'),
        },
        {
            path: 'hubble-apply/interpretation',
            component: () => import(/* webpackChunkName: "contract-extract" */ 'src/pages/contractInterpret/index.vue'),
            meta: {
                title: () => i18n.t('judgeRisk.aiInterpret'),
                isHubblePage: true,
            },
        },
        // 本地项目路由
        ...DEVELOPMENT_ROUTES,
        ...docRoute,
        ...tempRoute,
        ...signRoute,
        ],
        auth: function() {},
    },
];

/**
 * 覆写VueRouter push repalce，支持传入参数location为完整的url地址【使用原生location跳转】
 * 原因：新版签署迁移到新项目，参数跳转方式支持
 * 参考VueRouter push repalce 写法 https://github.com/vuejs/vue-router/blob/dev/src/index.js
 */
VueRouter.prototype.push = function(location, onComplete, onAbort) {
    if (typeof location === 'string' && /^http/.test(location)) {
        return window.location.href = location;
    }
    this.history.push(location, onComplete, onAbort);
};
VueRouter.prototype.replace = function(location, onComplete, onAbort) {
    if (typeof location === 'string' && /^http/.test(location)) {
        return window.location.replace(location);
    }
    this.history.replace(location, onComplete, onAbort);
};

const router = new VueRouter({
    mode: 'history',
    routes,
    scrollBehavior() {
        return {
            x: 0,
            y: 0,
        };
    },
});

// 切换导航栏偶尔报错'Loading chunk *** failed'
router.onError((error) => {
    const pattern = /Loading chunk.+.failed/g;
    const isChunkLoadFailed = error.message.match(pattern);
    // const targetPath = router.history.pending.fullPath;
    if (isChunkLoadFailed) {
        Vue.$MessageToast.info(`页面已过期，请刷新重试`);
    }
});

router.beforeEach(async(to, from, next) => {
    loadLanguageAsync()
        .then(() => {
        // 设置网站标题
            setTitle(to.meta.title);
        });
    // 进入第一个页面路由前remove loading
    const $indexLoading = document.querySelector('#index-loading');
    try {
        $indexLoading && $indexLoading.remove();
    } catch (err) {
        $indexLoading.parentNode.removeChild($indexLoading);
    }

    // 如果是query中附带token，则进行登录处理
    handleTokenLogin(to, next);
    // 拦截子模块跳转, 开发环境登录用当前项目登录页，不跳分模块
    if (!(process.env.NODE_ENV.includes('development') && (to.path === '/login'))) {
        const moduleInterceptor = await checkModuleJump({ to, from, router, moduleType: 'main' });
        if (!moduleInterceptor) {
            return false;
        }
    }

    // 如果参数里有密码登录的状态，存到store里并去掉参数
    if (to.query.isPwdLogin === 'true') {
        return handlePwdLoginFlag(to, next);
    }

    if (!process.env.NODE_ENV.includes('development') && process.env.NODE_ENV !== 'development-ccb') {
        // 获取企业品牌状态
        Vue.$http.get('users/ignore/ent-brand/check').then(res => {
            /* 宏祥科技使用反代理，如果不在这里进行处理，会把客户的host改掉引起错误 */
            const hostArr = location.host.split('.');
            if (res.data.value + '' === '1' && !WHITE_HOSTS.includes(hostArr[1])) {
                location.href = location.href.replace(hostArr[0], 'ent');
            }
            if (res.data.value + '' !== '3') {
                Vue.$cookie.delete('isBrand');
                Vue.$cookie.delete('sino-russia');
                Vue.$cookie.delete('homeLogoFileId');
            } else {
                Vue.$cookie.set('isBrand', 1);
            }

            if (res.data.time) {
                Vue.$cookie.set('copyRightRange', res.data.time);
            } else {
                Vue.$cookie.delete('copyRightRange');
            }
        });
    }

    const fromPathHasSsoinner = from.path.indexOf('/ssoinner') > -1;
    const toPathHasSsoinner = to.path.indexOf('/ssoinner') > -1;

    if (fromPathHasSsoinner || toPathHasSsoinner) {
        Vue.GLOBAL.rootPathName = '/ssoinner';
    }
    if (fromPathHasSsoinner && !toPathHasSsoinner) {
        next(`/ssoinner${to.fullPath}`);
    }
    if (to.path.includes('/app/')) {
        // /app/* 相关代码删除
        return location.replace(`/account-center/home`);
    }
    // 不需要登录的页面就继续跳转
    if (to.matched[to.matched.length - 1].meta.noLogin) {
        const needConfigList = ['/register', '/oauth/login']; // 需要获取单点登录页面配置信息
        const needConfig = needConfigList.some(item => {
            return to.fullPath.indexOf(item) > -1;
        });
        if (needConfig && to.query.clientId) {
            Vue.$http.getNoLoginPageConfig(to.query.clientId).then(() => {
                next();
            });
        } else {
            next();
        }
    } else {
        // 从合同管理页面 跳转到 下面的页面，如果带有entId 自动切换主体
        if (to.query.entId &&
            store.currentEntId !== to.query.entId &&
            ['/sign/signing', '/sign/prepare', '/template/use/prepare'].indexOf(to.path) > -1
        ) {
            try {
                await Vue.$http.switchEntId(to.query.entId, 1);
            } catch (err) {
                const { data: { code, message } } = err.response;
                // 未加入企业跳转到权限申请页
                if (code === '110117') {
                    if (store.getters.getIsForeignVersion) {
                        return Vue.MessageBox({
                            message: i18n.t('sign.notJoinTip'),
                        });
                    }
                    const { data: { operationEnum } } = await Vue.$http.get(`/contract-api/papersign/info/${to.query.contractId}`);
                    const canPaperSign = ['ONLY_PAPER_SIGN', 'CHOOSE_PAPER_SIGN'].includes(operationEnum);
                    const { data: { receiverId, senderEnterpriseName, receiverEnterpriseName } } = await Vue.$http.get(`/contract-api/contracts/${to.query.contractId}/acquire-signer`);
                    sessionStorage.setItem('applyPermission', JSON.stringify({ senderEntName: senderEnterpriseName, receiverEntName: receiverEnterpriseName, receiverId: receiverId }));
                    next(`/sign-flow/sign/un-permission-remind/noJoin?canPaperSign=${canPaperSign}&contractId=${to.query.contractId}`);
                } else {
                    Vue.$MessageToast.info(message || this.$t('field.sysError'));
                }
                return;
            }
            setTimeout(() => {
                Vue.$MessageToast.info(i18n.t('sign.switchToReceiver', { receiver: to.query.entName }));
            }, 3000);
        }

        // 判断是否已登录，
        if (store.state.userInfo && !to.meta.needHeadInfo || to.meta.isHubbleNoLoginPage) {
            next();
        } else {
            // 获取commonheader数据
            Vue.$http.get('/users/head-info')
                .then(res => {
                    if (res && res.data) {
                        let lang = store.state.noLoginLanguage;
                        if (lang) {
                            store.commit('resetNoLoginLanguage');
                            Vue.$http.post('/users/configs/VIEW_LANGUAGE', {
                                name: 'VIEW_LANGUAGE',
                                value: lang,
                            });
                        } else if (res.data.VIEW_LANGUAGE) {
                            // 读取用户信息语言偏好
                            lang = res.data.VIEW_LANGUAGE;
                            Vue.$cookie.set('language', lang);
                        }
                        if (lang) {
                            i18n.locale = lang;
                            loadLanguageAsync({ language: lang }).then(() => {
                                // 设置网站标题
                                setTitle(to.meta.title);
                            });
                        }
                        // saas-8438 密码复杂度过低要求先修改密码
                        if (res.data.weekPasswordFlag && LocalStorage.get('isPwdLogin')) {
                            let exist = false;
                            document.body.childNodes.forEach(node => {
                                node.className === 'reset-password' && (exist = true);
                            });
                            !exist && Vue.$resetPassword();
                        }
                        // saml协议的sso用户，登出时根据samlNameId获取第三方登出链接
                        if (res.data.samlNameId) {
                            Vue.$cookie.set('samlNameId', res.data.samlNameId);
                        } else {
                            Vue.$cookie.delete('samlNameId');
                        }
                        store.commit('updateCurrentEntId', res.data.currentEntId); // 更新当前主体entId到store
                        return Promise.all([
                            Vue.$http.headerInfoConfig(res.data).then(() => res.data),
                            Vue.$http.getPageConfig(res.data, to.params.source || ''),
                            // Vue.$http.getConfigsInfo(),
                        ]);
                    } else {
                        return new Promise(function(resolve) {
                            resolve([{}, {}, {}, {}]);
                        });
                    }
                })
                .then(result => {
                    // 用户中心
                    const comHeadData = result[0];
                    // 如果是企业控制台的页面
                    if (to.fullPath.indexOf('/console') > -1) {
                        // 用户打开两个tab, 一个切到个人身份退出，继续在企业控制台操作会退出，此时再登录需要拦截一下
                        // http://jira.bestsign.tech/browse/CFD-2715
                        if (comHeadData.userType === 'Person') {
                            router.push(`${Vue.GLOBAL.rootPathName}/account-center/home`);
                            return false;
                        }
                        // 获取企业详细信息并存入 store
                        Vue.$http.get('/ents/detail')
                            .then((res) => {
                                if (res.data.enterprise) {
                                    store.commit('pushCompanyDetailInfo', res.data);
                                    next();
                                } else {
                                    // 如果企业对象为空，则回到主页
                                    router.push(`${Vue.GLOBAL.rootPathName}/account-center/home`);
                                }
                            })
                            .catch(() => {});
                    } else if (to.fullPath.includes('/statistics')) {
                        // 统计报表页面判断权限
                        if (store.getters.getUserPermissons.statistics) {
                            next();
                        } else {
                            router.push(`${Vue.GLOBAL.rootPathName}/account-center/home`);
                        }
                    } else {
                        next();
                    }
                })
                .catch(() => {});
        }
    }
});

function handlePwdLoginFlag(to, next) {
    const query = to.query;
    LocalStorage.set('isPwdLogin', true);
    delete query.isPwdLogin;
    next({ path: to.path, query });
}

/**
 * query附带token进行界面跳转
 * 使用场景：新版官网注册成功后进行的界面跳转
 */
function handleTokenLogin(to, next) {
    const query = to.query;
    if (query.access_token && query.refresh_token) {
        Vue.$token.save(query.access_token, query.refresh_token);
        delete query.access_token;
        delete query.refresh_token;
        next({ path: to.path, query });
    }
}

// 设置浏览器title
export function setTitle(title) {
    // title可配置为函数，动态设置标题
    if (typeof title === 'function') {
        title = title();
    }
    const isQyWx = window.sessionStorage.getItem('isQyWx');
    if (isQyWx) {
        document.title = title ? `${title}` : i18n.t('commonNomal.ssq'); // 上上签
    } else {
        const commonTitle = isEntTestInfoEnv() ? i18n.t('commonNomal.ssqTestPlatform') : i18n.t('commonNomal.ssqPlatform'); // 上上签电子签约云平台
        if (i18n.locale === 'en' || store.getters.getIsForeignVersion) { // SAAS-30574要求英文环境下统一，不显示模块名称
            document.title = commonTitle;
        } else {
            document.title = title ? `${title} - ${commonTitle}` : commonTitle;
        }
    }
}

export default router;
