// 模板
const TemplateList =
	() => import(/* webpackChunkName: "template" */ 'enterprise_pages/template/list/Template.vue');
// 模板匹配结果
const TemplateMatchResult =
    () => import(/* webpackChunkName: "template" */ 'enterprise_pages/template/templateMatch/templateMatchResult.vue');
const TemplateRecord =
	() => import(/* webpackChunkName: "template" */ 'enterprise_pages/template/record/TemplateRecord.vue');
const TemplateRecordDetail =
	() => import(/* webpackChunkName: "template" */ 'enterprise_pages/template/record/TemplateRecordDetail.vue');
const TemplateDocSelect =
	() => import(/* webpackChunkName: "template" */ 'foundation_pages/template/docSelect/index.vue');
const TemplatePrepare =
	() => import(/* webpackChunkName: "template" */ 'foundation_pages/template/tempPrepare/TempPrepare.vue');
const TemplateField =
	() => import(/* webpackChunkName: "template" */ 'foundation_pages/template/temp_field/TempField.vue');
const TemplateDynamicOld =
	() => import(/* webpackChunkName: "template" */ 'foundation_pages/template/temp_dynamic/TempDynamicField.vue');
const TemplateDynamic =
	() => import(/* webpackChunkName: "template" */ 'foundation_pages/template/tempYzDynamic/TempDynamicField.vue');
const TemplateView =
	() => import(/* webpackChunkName: "template" */ 'foundation_pages/template/view/View.vue');
const TemplateSuc = () => import(/* webpackChunkName: "template" */ 'foundation_pages/template/tempSuc/tempSuc.vue');
const TemplatePreview = () => import(/* webpackChunkName: "template" */ 'foundation_pages/template/temp_preview/index.vue');
const TemplateDecoration =
	() => import(/* webpackChunkName: "template" */ 'foundation_pages/template/tempYzDynamic/temp_dynamic_decoration/index.vue');

export default [
    // 模板
    {
        path: 'template',
        redirect: 'template/list',
        meta: {
            title: '模板管理',
        },
    },
    {
        path: 'template/list',
        component: TemplateList,
        meta: {
            title: '模板管理',
        },
    },
    {
        path: 'template/match-result/',
        component: TemplateMatchResult,
        meta: {
            title: '模板匹配详情',
        },
    },
    {
        path: 'template/record',
        component: TemplateRecord,
        meta: {
            title: '模板发送记录',
        },
    },
    {
        path: 'template/record/:templateId',
        component: TemplateRecordDetail,
        meta: {
            title: '模板发送记录详情',
        },
    },
    {
        path: 'template/doc-select',
        component: TemplateDocSelect,
        props: true,
        meta: {
            title: '选择模板组合',
        },
    },
    {
        path: 'template/:templateStatus(edit|use)/prepare',
        component: TemplatePrepare,
        props: true,
        meta: {
            title: '模板发起签约',
        },
    },
    {
        path: 'template/:templateStatus(edit|use)/field',
        component: TemplateField,
        props: true,
        meta: {
            title: '模板指定位置',
        },
    },
    {
        path: 'template/dynamic/field',
        component: TemplateDynamic,
        props: true,
        meta: {
            title: '动态模板指定位置',
        },
    },
    {
        path: 'template/dynamicOld/field',
        component: TemplateDynamicOld,
        props: true,
        meta: {
            title: '动态模板指定位置',
        },
    },
    {
        path: 'template/edit/suc',
        component: TemplateSuc,
        props: true,
        meta: {
            title: '模板修改成功',
        },
    },
    {
        path: 'template/preview',
        component: TemplatePreview,
        props: true,
        meta: {
            title: '模板预览',
        },
    },
    {
        path: 'template/view',
        component: TemplateView,
        meta: {
            title: '动态模板预览',
        },
    },
    {
        path: 'template/:templateStatus(edit)/decoration',
        component: TemplateDecoration,
        meta: {
            title: '动态模板合同装饰',
        },
    },
];
