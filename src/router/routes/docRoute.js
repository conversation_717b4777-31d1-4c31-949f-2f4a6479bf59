// const Doc = {};
// 合同详情页
// Doc.detail = () => import(/* webpackChunkName: "doc" */ 'common_pages/doc/docDetail/index.vue');
// 合同预览页，暂时只用在单点登录查看合同
// Doc.view = () => import(/* webpackChunkName: "doc" */ 'common_pages/doc/view/Doc-View.vue');
// 异步导出合同列表
// Doc.asyncExport = () => import(/* webpackChunkName: "doc" */ 'common_pages/doc/export/async-export.vue');
// 合同管理列表页
// Doc.list = () => import(/* webpackChunkName: "doc" */ 'common_pages/doc/list/List.vue');

export default [
    {
        path: 'doc/batch',
        redirect: 'sign-flow/doc-manage/batch',
    },
    {
        path: 'doc/detail/:contractId',
        // component: Doc.detail,
        meta: {
            title: '合同详情',
        },
        redirect: 'sign-flow/doc-manage/detail/:contractId',
    },
    // 新系统跳到这个路由
    // {
    //     path: 'doc/cm/detail/:contractId',
    //     component: Doc.detail,
    //     props: { 'source': 'cm' },
    // },
    {
        path: 'doc/view',
        // component: Doc.view,
        redirect: 'sign-flow/doc-manage/view',
    },
    {
        path: 'doc/async-export',
        // component: Doc.asyncExport,
        redirect: 'sign-flow/doc-manage/export',
    },
    {
        name: 'docList',
        path: 'doc/list',
        // component: Doc.list,
        redirect: 'sign-flow/doc-manage/list',
    },
    {
        path: 'doc/shortcut/:shortcutId', // 首页快捷入口
        redirect: 'sign-flow/doc-manage/list/:shortcutId',
    },
    {
        path: 'doc/guide', // 跳转到批量签署二维码引导页
        redirect: 'sign-flow/docs-sign-guide',
    },
    {
        path: 'doc/*', // 老的路由兼容，跳转到doc
        redirect: 'doc/list',
    },
    {
        path: 'doc',
        redirect: 'doc/list',
    },
];
