import i18n from 'src/lang';
// 签署流程
const Prepare =
	() => import(/* webpackChunkName: "send" */ 'foundation_pages/sign/prepare/Prepare.vue');
const Field =
	() => import(/* webpackChunkName: "send" */ 'foundation_pages/sign/field/Field.vue');
const Signing =
	() => import(/* webpackChunkName: "signing" */ 'foundation_pages/sign/signing/Signing.vue');
// 签署合同和查看合同认证拦截页
const personAuthIntercept =
	() => import(/* webpackChunkName: "app-sign" */ 'foundation_pages/sign/personAuthIntercept/index.vue');
// 企业未实名或者个人未加入企业
const entAuthIntercept =
	() => import(/* webpackChunkName: "app-sign" */ 'foundation_pages/sign/entAuthIntercept/index.vue');
// 签署提示页
const SignTip =
	() => import(/* webpackChunkName: "app-sign" */ 'foundation_pages/sign/tip/SignTip.vue');
// 签署提示页
const FaceQrCodeSignTip =
	() => import(/* webpackChunkName: "app-sign" */ 'foundation_pages/sign/tip/FaceQrCodeSignTip.vue');

// 审阅合同预览页
const ShareView =
    () => import(/* webpackChunkName: "app-sign" */ 'foundation_pages/sign/shareView/index.vue');

// 二维码合同信息页
const QRSignInfo =
	() => import(/* webpackChunkName: "app-sign" */ 'common_pages/qr_sign_info/QRSignInfoView.vue');
// 二维码合同信息页
const QRSignTip =
	() => import(/* webpackChunkName: "app-sign" */ 'common_pages/qr_sign_info/QRSignTip.vue');
// 申请印章分配页
const SealDistribute =
() => import(/* webpackChunkName: "app-sign" */ 'common_pages/sealDistribute/SealDistribute.vue');

// 印章确认页
const SealConfirm =
() => import(/* webpackChunkName: "app-sign" */ 'common_pages/sealConfirm/index.vue');

// 印章审批页
const SealApproval =
() => import(/* webpackChunkName: "app-sign" */ 'common_pages/sealApproval/index.vue');

const handPainted =
    () => import(/* webpackChunkName: "app-sign" */ 'foundation_pages/handPainted/index.vue');
export default [
    // 签署流程
    {
        path: 'sign/prepare',
        component: Prepare,
        meta: {
            title: () => i18n.t('home.startSigning'),
        },
    },
    {
        path: 'sign/field',
        component: Field,
        meta: {
            title: () => i18n.t('home.startSigning'),
        },
    },
    {
        path: 'sign/signing',
        component: Signing,
        meta: {
            title: () => i18n.t('docDetail.sign'),
        },
    },
    {
        path: 'personAuthIntercept',
        component: personAuthIntercept,
        meta: {
            title: '个人实名信息出现问题',
        },
    },
    {
        path: 'entAuthIntercept',
        component: entAuthIntercept,
        meta: {
            title: '企业实名信息出现问题',
        },
    },
    {
        path: 'sign/preview',
        component: Signing,
        meta: {
            title: '签署预览',
            view: true,
        },
    },
    {
        path: 'sign/shareView',
        component: ShareView,
    },
    // 印章确认页
    {
        path: 'sealConfirm',
        component: SealConfirm,
    },
    // 印章审批页
    {
        path: 'sealApproval',
        component: SealApproval,
    },
    //  签署提示页
    {
        path: 'sign/sign-tip',
        component: SignTip,
        meta: {
            noLogin: true,
            title: '合同详情',
        },
    },
    //  签署提示页——二维码刷脸
    {
        path: 'sign/sign-tip/face-sign-qrcode',
        component: FaceQrCodeSignTip,
        meta: {
            noLogin: true,
            title: '刷脸签署',
        },
    },
    // 二维码合同信息页
    {
        path: 'sign/qr-sign-info',
        component: QRSignInfo,
        meta: {
            noLogin: true,
        },
    },
    // 发送前的二维码合同信息页
    {
        path: 'sign/qr-sign-tip',
        component: QRSignTip,
        meta: {
            noLogin: true,
        },
    },
    // 申请印章分配页
    {
        path: 'sign/seal-distribute',
        component: SealDistribute,
    },
    // 手写面板
    {
        path: 'signature/handpainted',
        component: handPainted,
        meta: {
            noLogin: true,
            noActivityFloat: true,
        },
    },
];
