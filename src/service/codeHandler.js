// HTTP 请求状态码为 409 的情况下后端返回的 code 字段
import Vue from 'vue';
import axios from 'axios';
import store from 'src/store/store.js';
import router from 'src/router/router.js';
import i18n from 'src/lang';

let responeseData = {};
const codeMethodMap = {
    // 登录过期
    '040003'() {
        if (!router.history.current.meta.noLogin) {
            // 删除登录信息
            Vue.$token.delete();

            Vue.$hybrid.clearAll();
            store.commit('logout');
            redirectAfterPromise(Vue.$MessageToast.error(i18n.t('CSTips.loginOverdue')), '/login');
        }
    },
    // 访问令牌被挪用
    '040017'() {
        // 删除登录信息
        Vue.$token.delete();
        redirectAfterPromise(Vue.$MessageToast.error(responeseData.message), '/login');
    },
    // 错误的权限
    '160001'() {
        redirectAfterPromise(Vue.$MessageToast.error(responeseData.message), '/account-center/home');
    },
    // 模板授权被取消后，没有权限使用
    '160007'() {
        templateErrorHandler('您对该模板的使用权限被模板创建者收回');
    },
    // 模板被创建人删除后，被授权人无法使用
    '160008'() {
        templateErrorHandler('此模板已被创建者删除');
    },
    // 模板被停用后，被授权人无法使用
    '170036'() {
        templateErrorHandler('此模板已被创建者停用，无法继续使用');
    },
    // 当前员工被停用，需要自动登出
    '090018'() {
        Vue.$token.delete();
        Vue.$MessageToast.error(responeseData.message);
        setTimeout(() => {
            redirectAfterPromise(axios.logOut(), '/login');
        }, 1000);
    },
    // 个人用户无法进入模板页面
    '170001'() {
        Vue.$MessageToast.error(responeseData.message);
        router.push('/account-center/home');
    },
    // 资源数量限制
    '120103'() {
        messageBox(responeseData.message || i18n.t('CSTips.errorTip'), responeseData.data, i18n.t('CSCommon.know'), '120103');
    },
    '00032'() {
        Vue.$MessageToast.error('套餐余额不足');
        store.commit('hubble/togglePackageDialog', true, { root: true });
    },
    '00033'() {
        Vue.$MessageToast.error('套餐余额不足');
        store.commit('hubble/togglePackageDialog', true, { root: true });
    },
};

// 模板错误通用方法
function templateErrorHandler(content) {
    redirectAfterPromise(
        messageBox('无法使用该模板', content, '回到模板列表'),
        '/template/list',
    );
}

function redirectAfterPromise(promise, url) {
    return promise.then(() => {
        router.push(url);
    });
}

function messageBox(title, message, confirmBtnText, errCode) {
    // eslint-disable-next-line
    return Vue.MessageBox({
        title,
        message,
        confirmBtnText,
        errCode,
    });
}

/**
 *
 * @param {*} code HTTP 状态码
 * @param {*} resData 接口返回的数据
 * @param {*} defaultMessage 通用的消息提示
 */
export function codeHandler(code, resData, defaultMessage) {
    responeseData = resData || {};
    if (Object.keys(codeMethodMap).indexOf(code) >= 0) {
        codeMethodMap[code]();
    } else if (code === '300001') { // 高级功能不可用，请清除配置，或者续费后，方可继续使用
        const dataInfo = responeseData && responeseData.data;
        Vue.$featureSupport({ endData: dataInfo || {}, type: 'trialEnd' });
    } else {
        defaultMessage && Vue.$MessageToast.error(responeseData.data || defaultMessage);
    }
}

/**
 * 保存接口数据
 * @param {*} config
 * @param {*} data 接口返回的数据
 */
export function saveResponseHandler({ config, data }) {
    store.commit('setReportHttpData', { url: config.url, data });
}
