import axios from 'axios';
import store from 'src/store/store.js';
import router from 'src/router/router.js';
import { codeHandler, saveResponseHandler } from 'src/service/codeHandler.js';
import CryptoJS from 'crypto-js';
import i18n from 'src/lang';
import { getQueryString } from 'src/common/utils/getQueryString.js';
import { addMulProxySignParam } from 'src/common/utils/addMulProxySignParam.js';
// axios 默认配置
axios.defaults.baseURL = `${window.location.protocol}//${window.location.host}`;
// 获取 head-info 后进行一些赋值操作
axios.headerInfoConfig = headerInfo => {
    // head-info存在store的commonHeaderInfo中
    store.commit('pushInitCommonHeaderInfo', headerInfo);
    // 设置混合云版本
    Vue.$hybrid.setVersion(headerInfo.deltaHybridVersion);

    // 区分用户是公有云还是混合云
    // 如果是混合云用户，开始轮询检测内网环境
    if (headerInfo.hybridServer) {
        store.commit('changeHybridUserType', 'hybrid');
        // 不等轮询结果，先resolve
        Vue.$hybrid.checkInLAN(0);
        return Promise.resolve();
    } else {
        Vue.$hybrid.clearAll();
        store.commit('changeLANStatus', null);
        store.commit('changeHybridUserType', 'public');
        store.commit('replaceHybridAccessToken', {
            hybridServer: '',
            hybridAccessToken: '',
        });
        return Promise.resolve();
    }
};
// 登录注册时获取页面配置
axios.getNoLoginPageConfig = function(clientId) {
    return Vue.$http
        .get(`/users/ignore/style/${clientId}`)
        .then(res => {
            if (res && res.data) {
                store.commit('pushSsoConfig', res.data);
            }
        })
        .catch(() => {});
};

// 获取页面配置
axios.getPageConfig = async function(headerInfo, source) {
    if (
        headerInfo.extendFields.isLoginFromDeveloper ||
        source === 'guide'
    ) {
        let styleRes = {};

        // 乐高成-客户管理/页面配置, 单点登陆页面配置信息
        if (source === 'guide') {
            const contractId = getQueryString('contractId', Vue.prototype.$localStorage.get('transitionHref'));
            styleRes = await Vue.$http.get('/users/style', { params: {
                contractId: contractId,
            } });
        } else {
            styleRes = await Vue.$http.get('/users/style');
        }

        store.commit('pushSsoConfig', styleRes.data || {});
    }
    // 乐高成-客户管理/客户配置，即商品化不同版本的功能点
    const { data } =  await Vue.$http.get('/users/features');
    store.commit('pushFeatures', data);
    // 高级功能信息
    const res =  await Vue.$http.get('/ents/advanced-features-detail');
    store.commit('pushAdvancedFeatures', res.data);
    return Promise.resolve();
};

axios.getNoticeCount = (type = 0) => {
    // 头部上面只会显示是否有未读消息，调用参数type改成1
    return Vue.$http
        .get(`/ents/message/count?type=${type}`)
        .then(res => {
            if (res && res.data) {
                store.commit(
                    'pushNoticeCount',
                    res.data || {
                        noticeUnreadMessageCount: 0,
                        contractUnreadMessageCount: 0,
                        unreadMessageCount: 0,
                    },
                );
            }
        })
        .catch(() => {});
};
// 合同管理第一期，查询是否开通过合同管理
// 第二期去掉了这部分逻辑。。。
axios.getConfigsInfo = () => {
    let url = 'ents/configs/default/NEW_CONTRACT_MANAGE/value';
    if (store.getters.getUserType === 'Person') {
        url = '/users/configs/default/NEW_CONTRACT_MANAGE';
    }
    return Vue.$http
        .get(url)
        .then(res => {
            if (res && res.data) {
                store.commit('setNewContract', res.data.value === 'true');
            }
        })
        .catch(() => {});
};
// 退出登录
axios.logOut = async function() {
    let ssoLogoutLink = '';
    if (~~Vue.$cookie.get('isBrand') === 1) {
        // saml协议的sso用户，登出时需要传samlNameId
        const { data } = await axios.get(`users/ignore/ent-brand?samlNameId=${Vue.$cookie.get('samlNameId') || ''}`);
        ssoLogoutLink = data.ssoLogoutLink || '';
    }
    return new Promise((resolve) => {
        localStorage.removeItem('dozenList');
        axios.post(`/auth-center/user/logout`, {
            refreshToken: Vue.$cookie.get('refresh_token'),
        }).finally(() => {
            Vue.$token.delete();
            Vue.$hybrid.clearAll();
            store.commit('logout');
            if (ssoLogoutLink) {
                location.href = ssoLogoutLink;
            } else {
                resolve();
            }
        });
    });
};

axios.addBizPoint = (scene = '', contractId = '', isStart = false) => {
    const reportTime = +new Date();
    const operateUserId = store.getters.getUserId;
    const entId = store.state.currentEntId;
    let operateDuration = 0;
    // 本地存储时间唯一标识
    const pointId = `${scene}-${operateUserId}-${entId}-${contractId || ''}`;
    const request = () => {
        return axios.post('/contract-api/biz-complete-burial-point/add', {
            scene,
            contractId,
            operateUserId,
            reportTime,
            entId,
            operateDuration,
        });
    };
    // 业务开始时本地存储开始时间
    if (isStart) {
        localStorage.setItem(pointId, reportTime);
        // return request();
    } else if (localStorage.getItem(pointId)) { // 业务完成时，如果本地有开始时间，计算耗时并上报
        operateDuration = ((reportTime - localStorage.getItem(pointId)) / 1000).toFixed(0);
        Object.keys(localStorage).forEach(el => {
            el.includes(scene) && localStorage.removeItem(el);
        });
        return request();
    }
};

// http request 拦截器
/**
 * config 动态配置参数
 * noToast: 1, 接口返回非200去除默认toast
 * noUseToken: true, 接口请求中header不使用Authorization,使用场景：混合云3
 */
axios.interceptors.request.use(
    config => {
        // 在headers里添加access_token，用于后端身份认证
        const access_token = Vue.$cookie.get('access_token');

        /**
             *  WEB(1),
                APP(2),
                API(3),
                WPS(4),
                INVITATION(5),
                WPS_ONLINE(6),
                ENT_CREATE(7),
                SANDBOX(8),
                WAP(9);
             */
        config.headers['request-source'] = `WEB`;
        config.headers['terminal'] = `PC`; // 只有PC、H5、APP三个值，用来进行广告位数据统计
        // 避免混3上传config中传headers.Authorization
        if (config.noUseToken && config.headers && config.headers.Authorization) {
            delete config.headers.Authorization;
        }
        if (access_token && !config.noUseToken) {
            config.headers.Authorization = `bearer ${access_token}`;
        }
        // 多方代理签增加接口请求参数
        config = addMulProxySignParam(config);

        // 给 get 请求加上时间戳防缓存
        if (config.method === 'get') {
            const time = Date.parse(new Date().toString());
            config.url =
                config.url +
                (config.url.indexOf('?') > -1 ? '&_=' + time : '?_=' + time);
        }

        // 连接个人服务手动添加header
        // config.headers['x-authenticated-userid'] = '{"userId":2027605557493366822,"developerId":1,"chosenEntId":0,"empId":0}';
        // config.headers['x-authenticated-userid'] = '{"userId":2256367343389120515,"developerId":1,"chosenEntId":2338349521211274241,"empId":2338349521219662853}';

        // config.headers['x-authenticated-userid'] = '{"userId":1849819604218544674,"developerId":1,"chosenEntId":1938824054030991363,"empId":1938824054039379970}';
        // config.headers['x-authenticated-userid'] = '{"userId":2312276764392500224,"developerId":1,"chosenEntId":2312277434675795969,"empId":2312277434700961796}';
        // config.headers['x-authenticated-userid'] = '{"empId": 2375104459786175488, "userId": 2280899940362240003, "chosenEntId": 2375104459744232456, "developerId": "1"}';
        // config.headers['x-authenticated-userid'] = '{"empId": 2262148165442515976, "userId": 2242593796191718400, "chosenEntId": 2262148165425738758, "developerId": "1"}';
        return config;
    },
    error => {
        // Do something with request error
        return Promise.reject(error);
    },
);

// http response 拦截器
axios.interceptors.response.use(
    response => {
        saveResponseHandler(response);
        // 混合云3.0返回的data为null
        // response.data = response.data || {};
        const { config: { url }, data: { code, message } } = response;
        // 合同管理的接口异常处理与旗舰版不同
        // contract-center、contract-center-bearing接口单独处理
        if (url.includes('/contract-center') && code !== '0') {
            if (code === '6000026') {
                Vue.$MessageToast.error('搜索条件的合同持有人中不能超过500个账号');
            } else {
                Vue.$MessageToast.error(message || i18n.t('CSTips.serverError'));
            }
            return Promise.reject(response);
        }
        if (url.includes('/users/head-info')) {
            localStorage.setItem('currentTabEntId', response.data?.currentEntId);
        }
        return response;
    },
    error => {
        if (error.code === 'ECONNABORTED' || !error.response) {
            return Promise.reject(error);
        }
        const originalRequest = error.config;
        const status = +error.response.status;
        const code = error.response.data.code;
        const message = !originalRequest.noToast &&
            error.response.data &&
            error.response.data.message;
        const refreshToken = Vue.$cookie.get('refresh_token');

        // 401先清除cookie中的token
        if (status === 401) {
            Vue.$token.delete();
        } else if (!originalRequest.noToast) {
            // 除了noToast不进行展示的error，其他error进行上报
            sensorsRequestError({
                url: originalRequest.url,
                reason: error.response?.data?.message || error.message,
                errorCode: code,
                httpCode: status,
            });
        }
        // 如果access_token过期，则请求刷新token接口，然后重新调用原接口
        if (status === 401 && !originalRequest._retry) {
            originalRequest._retry = true;

            if (!refreshToken) {
                if (!router.history.current.meta.noLogin) {
                    if (Vue.GLOBAL.rootPathName === '/ssoinner') {
                        router.push(`${Vue.GLOBAL.rootPathName}/sso/notFound`);
                    } else {
                        // router.push(`${Vue.GLOBAL.rootPathName}/login`);
                        /* 将当前需要登录才能访问的页面通过redirect返回 */
                        router.replace({
                            path: '/login',
                            query: { redirect: router.history.pending?.fullPath },
                        });
                    }
                }
                Vue.$hybrid.clearAll();
                return Promise.reject(error);
            }

            return axios({
                url: 'auth-center/user/refresh-token',
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                data: { refreshToken },
            })
                .then(res => {
                    const {
                        access_token,
                        refresh_token,
                        token_type,
                    } = res.data;
                    Vue.$token.save(access_token, refresh_token);
                    axios.defaults.headers.common[
                        'Authorization'
                    ] = `${token_type} ${access_token}`;
                    originalRequest.headers[
                        'Authorization'
                    ] = `${token_type} ${access_token}`;
                    return axios(originalRequest);
                })
                .catch(() => {
                    if (!router.history.current.meta.noLogin) {
                        router.replace({
                            path: '/login',
                            query: { redirect: router.currentRoute.fullPath },
                        });
                        Vue.$hybrid.clearAll();
                    }
                });
        } else if (status === 409) {
            // todo: refresh——token请求失败的提示...
            codeHandler(code, error.response.data, message);
        } else if ((code === 'U-900' || status === 405 || status === 404) && store.state.commonHeaderInfo.hybridServer) {
            // 混合云jar包，版本太低 不支持新功能，统一提示
            Vue.$MessageToast.error('请联系上上签升级jar包');
        } else if (status >= 500) {
            Vue.$MessageToast.error(message || i18n.t('CSTips.serverError'));
        } else {
            // 统一弹窗, 903：上传附件接口参数错误，已有错误提示，重复
            if (message !== '输入的参数错误') {
                message && Vue.$MessageToast.error(message);
            }
        }
        if (error.message === 'Network Error') {
            /* tell user the server is down */
            Vue.$MessageToast.error('当前网络不可用，请检查你的网络设置');
        }
        return Promise.reject(error);
    },
);

// 子项目初始化sensors的,进行接口报错埋点
function sensorsRequestError(obj = {}) {
    Vue.$sensors && Vue.$sensors.track({
        eventName: 'Ent_CommonError_Log',
        eventProperty: {
            module_name: process.env.VUE_APP_PROJECT_NAME,
            request_url: obj.url,
            fail_reason: obj.reason,
            fail_error_code: obj.errorCode,
            fail_http_code: obj.httpCode,
        },
    });
}

axios.getImageVerCode = function(imageKey) {
    return new Promise((resolve) => {
        axios
            .get(`/users/ignore/captcha/base64-image`, {
                params: { imageKey },
            })
            .then(res => {
                resolve(res);
            });
    });
};

/**
 * code:
 * 'B001': 注册
 * 'B002': 忘记密码
 * 'B003': 重置签约密码
 * 'B004': 修改通知方式
 * 'B005': 修改账号
 * 'B006': 更换管理员
 * 'B008': 发送通知到账号
 */

const sendVerCommon = function(opts, type) {
    const {
        code,
        imageCode = '',
        imageKey = '',
        sendType,
        target = '',
        bizTargetKey = '',
        noToast = 0,
        token = '',
    } = opts;
    let url;
    switch (type) {
        case 1:
            url = '/users/captcha/notice';
            break;
        case 2:
            url = '/users/ignore/v2/captcha/notice';
            break;
        case 3:
            url = '/users/ignore/captcha/notice-token';
            break;
    }

    const params = {
        url: url,
        method: 'post',
        data: {
            code,
            sendType,
            target,
            bizTargetKey,
        },
        noToast: noToast || 0,
    };

    if (type === 2 || type === 1) {
        // 使用AES对'{code}:{target}'数据进行加密，目的增加中间人修改手机号频繁调接口的难度
        // mod,和padding和后端约定好
        const key = CryptoJS.enc.Utf8.parse(Vue.GLOBAL.AES_ENCRYPT_KEY);
        const timestamp = Date.parse(new Date());
        const encryptStr = `${code}:${target}:${bizTargetKey ?? ''}:${timestamp}`;
        const encryptToken = CryptoJS.AES.encrypt(encryptStr, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
        }).toString();
        // 可能含有特殊字符，encode下
        const encodeToken = encodeURIComponent(encryptToken);
        params.url = `${url}?encryptToken=${encodeToken}`;
        // 解密
        // CryptoJS.AES.decrypt(encryptToken, key, {
        //     mode:CryptoJS.mode.ECB,
        //     padding:CryptoJS.pad.Pkcs7
        // }).toString(CryptoJS.enc.Utf8)
    }

    if (type === 3) {
        Object.assign(params.data, {
            token: token,
        });
    }

    let headersObj;
    if (imageCode !== '' && imageKey !== '') {
        headersObj = {
            'Content-Type': 'application/json; charset=utf-8',
            additionalImgVerCode: JSON.stringify({
                imageCode: imageCode,
                imageKey: imageKey,
            }),
        };
        Object.assign(params, { headers: headersObj });
    }
    return axios(params);
};

// 发送接口调用成功后逻辑
const sendVerSuccess = function(res) {
    const { msg } = res.data;
    if (msg) {
        // eslint-disable-next-line new-cap
        Vue.MessageBox({
            headerTitle: i18n.t('localCommon.tip'),
            message: msg,
            confirmBtnText: i18n.t('uploadFile.understand'),
            showHeader: true,
        });
    }
};

// 需要登录
axios.sendVerCode = function(opts) {
    return new Promise((resolve, reject) => {
        sendVerCommon(opts, 1)
            .then(res => {
                sendVerSuccess(res);
                resolve(res);
            })
            .catch(err => {
                reject(err);
            });
    });
};
// 不需要登录
axios.sendVerCodeNoLogin = function(opts) {
    return new Promise((resolve, reject) => {
        sendVerCommon(opts, 2)
            .then(res => {
                sendVerSuccess(res);
                resolve(res);
            })
            .catch(err => {
                reject(err);
            });
    });
};
// 不需要登录需要token
axios.sendVerCodeNoLoginNeedToken = function(opts) {
    return new Promise((resolve, reject) => {
        sendVerCommon(opts, 3)
            .then(res => {
                sendVerSuccess(res);
                resolve(res);
            })
            .catch(err => {
                reject(err);
            });
    });
};

axios.getDevInfo = function(clientId) {
    return new Promise((resolve) => {
        axios
            .get('ents/ignore/developer/info', {
                params: { clientId },
            })
            .then(res => {
                resolve(res);
            });
    });
};

// 切换身份
axios.switchEntId = function(entId, noToast = 0) {
    return new Promise((resolve, reject) => {
        axios.post('/authenticated/switch-ent', {
            entId,
            refreshToken: Vue.$cookie.get('refresh_token'),
        }, { noToast }).then(({ data }) => {
            Vue.$token.save(data.access_token, data.refresh_token);
            Vue.$http.get('/users/head-info', { noToast: 1 }).then(res => {
                if (res && res.data) {
                    Vue.$http.headerInfoConfig(res.data);
                    Vue.$http.getPageConfig(res.data);
                    resolve();
                }
            }).catch(e => reject(e));
        }).catch(e => reject(e));
    });
};

function Axios() {
    Vue.$http = axios;
    Vue.prototype.$http = axios;
}

export default Axios;
