
const localAppid = 'SX20250304UMDAYU'; // 本地使用，根据环境切换

const origin = location.origin;
const isLocal = process.env.NODE_ENV.includes('development');

// const isDevelop = origin.includes('bestsign.tech');
const isTest = origin.includes('bestsign.info');
const isProduction = origin.includes('bestsign.cn');
let WPS_APPID = localAppid;
if (!isLocal) {
    WPS_APPID = isProduction ? 'AK20250317AGMDTS' // 生产
        : isTest ? 'SX20250304UMDAYU' // 测试
            : 'SX20250206RYNEAG'; // 开发
}
export {
    WPS_APPID,
};
