/**
 * @description 登录注册模块路由，对应public-library const.js中的ACCOUNT_CENTER_ROUTERS
 */
export const  ACCOUNT_CENTER_ROUTERS = [
    ...['/login', '/mpLogin', '/otherDomainLogin', '/oauth/login', '/sso/login', '/sso/binding', '/sso/sign/claim',
        '/reception/login', '/reception/examination'], // 登录模块
    ...['/register', '/reg/company/mail', '/reg/notFound', '/register/done', '/invite/register', '/invite/reg/staff',
        '/invite/reg/staff/done', '/signing/insteadRegister'], // 注册模块
    ...['/sso/transition', '/sso/signTransition', '/sso/customize', '/sso/notFound', '/oauth/certification/sign', '/oauth/certification/auth',
        '/daAuth', '/daAuth/continue', '/signing/transition', '/signing/transition/take', '/jumper/recharge'], // 中转模块
    ...['/forgotPassword', '/setPassword', '/resetSignPassword'], // 密码
    ...['/service-agreement', '/privacy-policy', '/digital-certificate-protocal'], // 协议
    ...['/enterprise/authentication/baseInfoChecks', '/personal/certification/baseCheck'], // 信息核实页
    ...['/home', '/account-center/home', '/notice', '/operation-video', '/feature-inner', '/feature-detail/', '/feature'], // 首页, 消息，操作视频
];

// 实名pc 路由，部分路由与登录部分冲突，使用正则
export const AUTH_PC_ROUTERS = [
    /^\/ent\/sign\/guide$/,
    /^\/sign\/guide\/sino-russia$/,
    /^\/sign\/guide\/restart\/sino-russia$/,
    /^\/ent\/sign\/guide\/transition$/,
    /^\/enterprise\/authentication$/,
    /^\/enterprise\/authentication\/(?!baseInfoChecks)/,
    /^\/\w+\/enterprise\/authentication\/face/,
    /^\/personal\/certification\/(?!baseCheck)/,
    /^\/\w+\/personal\/certification\/(?!baseCheck)/,
    /^\/foundation\/authentication/,
    /^\/sign\/guide$/,
    /^\/personauth\/face/,
    /^\/\w+\/personauth\/face/,
    /^\/person\/(normal|guide|archive)/,
    /^\/account\/appeal$/,
];
