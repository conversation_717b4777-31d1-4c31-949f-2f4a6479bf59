// el-select
.sign-el-select {
	border: 1px solid $border-color;
	.el-icon-caret-top {
		color: #777;
	}
	.el-select-dropdown__list { // ul
		padding: 0;
	}
	li {
		padding: 6px 10px;
		height: 30px;
		line-height: 15px;
		font-size: 12px;
	}
	li.hover {
		background-color: #F5F5F5;
	}
}

// dropdown下拉框
.sign-el-dropdown-menu {
	box-sizing: border-box;
	font-size: 12px;
	color: #666;
	padding: 0;
	border: 1px solid $border-color;
	border-radius: $border-radius;
	box-shadow: none;
	margin: 1px 0;
	.el-dropdown-menu__item {
		height: 30px;
		line-height: 32px;
	}
	.el-dropdown-menu__item:not(.is-disabled):hover {
		background-color: #f6f6f6;
	}
}

.sign-el-autocomplete-popper {
    opacity: 1;
    &.sign-el-autocomplete-popper--type-one {
        // left: 581px !important;
        width: auto !important;
        min-width: 240px;
        max-width: 525px;
    }
    &.sign-el-autocomplete-popper--type-two {
        // left: 573px !important;
        width: auto !important;
        min-width: 240px;
        max-width: 534px;
    }
    &.sign-el-autocomplete-popper--type-three {
        // left: 488px !important;
        width: auto !important;
        min-width: 213px;
        max-width: 641px;
    }
	.el-autocomplete-suggestion__wrap {
		padding: 0;
	}
	li {
		height: 28px;
		line-height: 28px;
		font-size: 12px;
		&:hover {
			color: #fff;
			background-color: #1c8de0;
		}
	}
}
.sign-el-autocomplete-popper.is-loading {
	opacity: 0;
	li {
		height: 28px;
	}
}

// el-dialog
.sign-el-dialog {

	.el-dialog {
		width: 384px;
		border-radius: 3px;
        box-shadow: #666 0px 0px 6px;
	}
	.el-dialog__header {
		padding: 25px 33px;
		border-bottom: 1px solid $border-color;
		.el-dialog__title {
		    font-size: 16px;
		    font-weight: normal;
		    color: #333;
		}
		.el-icon-close {
		    font-size: 12px; // X大小
		    font-weight: bold;
		    color: #999;
		}
	}
	.el-dialog__body {
		background-color: #F3F9FD;
		padding: 25px 33px;
		border-radius: 3px;
	}
}
