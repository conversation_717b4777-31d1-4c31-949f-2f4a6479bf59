
.ja-page{
    .signature-label{
        .signature-box{
            width: 100% !important;
            height: 100% !important;
            position: relative;
        }
        .signature-ja{
            width: 76px;
            height: 76px;
            background: rgba(255,255,255,0.50);
            border: 3px solid #FFFFFF;
            position: absolute;
            top: 50%;
            left: 50%;
            margin-left: -38px;
            margin-top: -38px;
            box-sizing: border-box;
            font-size: 14px;
            text-align: center;
            border-radius: 50%;
            line-height: 76px;
            font-weight: bold;
        }
    }
    .seal-label{
        left: 0 !important;
        top: 0 !important;
        width: 100% !important;
        height: 100% !important;
        background: transparent !important;
        .seal-box::after{
            display: none !important;
        }
        .seal-content-ja{
            width: 113px;
            height: 113px;
            background: rgba(255,255,255,0.50);
            border: 3px solid #FFFFFF;
            position: absolute;
            top: 10px;
            left: 10px;
            box-sizing: border-box;
            font-size: 14px;
            text-align: center;
            border-radius: 50%;
            line-height: 113px;
            font-weight: bold;
        }
    }
    .en-notify{
        display: none;
    }
    .el-dialog__wrapper.signatures .el-dialog__body{
        padding-bottom: 20px;
        .signatures-container {
            max-height: 200px;
            overflow: auto;
            .signature{
                width: 87px !important;
            }
        }
    }
    .sign-va-dialog .el-dialog{
        width: 500px;
    }
    .SignValidation-comp .el-form-item .verify-flex .countDown{
        width: 125px;
    }
    .SignValidation-comp .el-form-item .el-form-item__label{
        width: 105px!important;
    }
    .SignValidation-comp .signvalidation-comtainer .validationNavList{
        display: none;
    }
    .ApprovalValidation-comp .dialog-footer-btns .ssq-btn-cancel{
        width: unset;
        padding: 0 10px;
    }
    .riskTipDialog .el-dialog{
        width: 480px;
    }
    .CreateElectronicSeal .pop-con .btn-con .btn.btn-type-four{
        width: unset;
        padding: 0 10px;
    }
    .FieldBody-cpn .site-content .FieldSite{
        width: 225px;
    }
    .signing .header .otherBtn .other-option{
        height: auto;
        line-height: 12px;
        padding: 8px 12px;
    }
}
.ja-font {
    body, input, textarea, select, button, div, span {
        font-family: MS PGothic, -apple-system, BlinkMacSystemFont, 'Helvetica Neue', sans-serif !important;
    }
    [class^="el-icon-"], [class*=" el-icon-"] {
        font-family: 'element-icons' !important;
    }
    [class^="el-icon-ssq"], [class*=" el-icon-ssq"] {
        font-family:"iconfont" !important;
    }
}
.ja-page-version-front-end{
    .ja-page-hidden{
        display: none;
    }
}
