// 全局变量设置

$base-width: 1280px;

// 主题色
$theme-color: #127fd2;
// 网页主色
$base-color: #337ccf;
// 导航头主色
$header-color: #0b2b44;
// 通用通过型绿色
$right-color: #52A94A;

// 通用线框颜色
$border-color: #ddd;
$border-radius: 1px;
$background-color: #127fd2;

// 按钮
$btn-primary-color: #127fd2;
$btn-primary-hover-color: #1687dc;

// 文字链接
$link-color: #1687dc;
$link-hover-color: #0070c9;

@mixin base-width($name: width) {
	// @media only screen and (max-width: 1290px) {
	// 	#{$name}: 1180px;
	// }
	// @media only screen and (min-width: 1290px) and (max-width: 1400px) {
	// 	#{$name}: 1280px;
    // }
    @media only screen and (max-width: 1400px) {
		#{$name}: 1280px;
	}
	@media only screen and (min-width: 1400px) {
		#{$name}: 1366px;
	}
}
@mixin nav-li-padding($name: padding) {
    @media only screen and (max-width: 1400px) {
		#{$name}: 0 14px;
	}
	@media only screen and (min-width: 1400px) {
		#{$name}: 0 18px;
	}
}
// Register footer跟随
/**
 * 目的：页面高度不超过一屏幕，footer固定在屏幕最底部；页面高度超过一屏幕，footer跟随页面最底部。
 * 用法：
 * 	<template>
 * 		<div class="footer-follow-page">
 * 			<div class="footer-follow-page-container"></div>
 * 			<RegisterFooter class="footer-follow"></RegisterFooter>
 * 		</div>
 * 	</template>
 * 	<style>
 * 		@include footerFollow;
 * 	</style>
 */
@mixin footerFollow {
	html, body {
		height: 100%;
	}

	.footer-follow-page {
		position: relative;  
		min-height: 100vh;
		.footer-follow-page-container {
			padding-bottom: 100px;
		}
		.footer-follow {
			box-sizing: border-box;
			position: absolute;
			bottom: 0;
			left: 0;
		}
	}
	
}

/**
 * &:after {
		position: absolute;
		top: 12px;
		right: 8px;
		content: '';
		@include solid-triangle(5px, #8295A1)
	}
 */
@mixin solid-triangle($border-width: 6px, $t-color: #9BA6AE, $r-color: #9BA6AE, $b-color: #9BA6AE, $l-color: #9BA6AE) {
    border: $border-width solid transparent;
    border-top: $border-width solid $t-color;
    border-right: $border-width solid $r-color;
    border-bottom: $border-width solid $b-color;
    border-left: $border-width solid $l-color;
    width: 0;
    height: 0px;
}