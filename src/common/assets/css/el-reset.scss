$radio-blue: #2298f1;
$checkBox-blue: #2298f1;
$input-blue: #127fd2;
$button-blue: #127fd2;
$button-hover-blue: #1687dc;
$button-disable-gary: #ededed;
$button-default-border: #ccc;
$button-default-bg: #f8f8f8;
$dropdown-blue: #2298f1;
$pagination-blue: #2298f1;
$checkBox-disabled-checked: #d1dbe5;

// 兼容移动端下拉
.el-scrollbar {
    > .el-scrollbar__bar {
        opacity: 1 !important;
    }
}

.el-button, button{
	border-radius: 2px;
	outline: none;
}

.el-button+.el-button{
	margin-left: 6px;
	[dir=rtl] &{
		margin-left: 0;
		margin-right: 6px;
	}
}

.el-radio{
	.el-radio__label{
		font-size: 12px;
	}
	.el-radio__input{
		.el-radio__inner{
			width: 16px;
			height: 16px;
			border: 1px solid #ccc;

			&:hover{
				border-color: $radio-blue;
			}
		}

		&.is-checked{
			.el-radio__inner{
				border-color: $radio-blue;
				background: $radio-blue;
			}
		}

		&.is-checked.is-disabled {
			.el-radio__inner{
				border-color: $checkBox-disabled-checked;
				background: $checkBox-disabled-checked;
			}
		}
	}
}

.el-checkbox{
	.el-checkbox__label{
		font-size: 12px;
	}
	.el-checkbox__input{
		.el-checkbox__inner{
			width: 16px;
			height: 16px;
			border: 1px solid #ccc;
			border-radius: 2px;
			z-index: 0;
			&:hover{
				border-color: $checkBox-blue;
			}

			&::after{
				left: 4px;
			}
		}

		&.is-checked,&.is-indeterminate{
			.el-checkbox__inner{
				border-color: $checkBox-blue;
				background: $checkBox-blue;
			}
		}
	}
	[dir=rtl] &{
		&+.el-checkbox {
			margin-left: 0;
			margin-right: 15px;
		}
		.el-checkbox__input{
			.el-checkbox__inner{
				&::after{
					right: 4px;
					left: auto;
				}
			}
		}
	}
}

.el-input{
	.el-input__inner{
		border-radius: 1px;
		border-color: #ccc;

        &:focus,
		&:hover{
			border-color: $input-blue;
			box-shadow: 0 0 2px #47acfc;
		}
	}

	.el-input__inner::placeholder {
        color: #999;
        font-size: 12px;
    }

	&.is-disabled {
		.el-input__inner{
			color: #333;
			border-color: #ccc;
			background: #fff;
		}
	}
}

.el-textarea{
	.el-textarea__inner{
		font-size: 12px;
		border-radius: 1px;
		border-color: #ccc;

		&:hover, &:focus{
			border-color: $input-blue;
			box-shadow: 0 0 2px #47acfc;
		}

		&::placeholder {
	        color: #999;
	        font-size: 12px;
	    }
	}
}

.el-tree{
	overflow-x: auto;
	overflow-y: hidden;

	.el-tree-node>.el-tree-node__children{
		overflow: visible;
	}
	.el-tree-node.is-current>.el-tree-node__content{
		background: none;

		.el-tree-node__label{
			color: #2298f1;
		}
	}
	&:not(.dept-tree) .el-tree-node__content{
		position: relative;
		// padding-left: 36px!important;
		overflow: visible;
		clear: both;
		border-bottom: 1px solid #eee;

		&:hover{
			background: #f8f8f8;
		}

		.el-checkbox{
	    	margin-left: -15px;
		}

		.el-tree-node__expand-icon{
			position: relative;
			font-family: "iconfont" !important;
			border: none;


			&::before{
				content: "\E68C";
			    position: absolute;
			    left: -4px;
			    top: -18px;
			    font-size: 12px;
				color: #ccc;
			}

			& + .el-tree-node__label::before, & + .el-checkbox + .el-tree-node__label::before{
				position: absolute;
				top: 14px;
				right: 10px;
				content: "";
				font-size: 12px;
				border: 4px solid transparent;
				border-left-width: 5px;
				border-left-color: #777;
				color: #777;
				transition: all 0.3s;
			}

			&.is-leaf{

				&::before{
					content: '';
				}

				& + .el-tree-node__label, & + .el-checkbox + .el-tree-node__label{
					margin-left: 7px;

					&::before{
						display: none;
					}
				}
			}
		}

		.el-tree-node__expand-icon.expanded{
			transform: rotate(0deg);

			& + .el-tree-node__label::before, & + .el-checkbox + .el-tree-node__label::before{
				transform: rotate(90deg);
			}
		}
	}
    .el-tree-node__label{
        margin-left: 5px;
        font-size: 12px;
        overflow: visible;
    }
}

.el-form{
	.el-form-item{
		.el-form-item__label{
			color: #333;
		}
	}
}

.ssq-home,.doc-page,.console-page,.doc-detail-page,.doc-batch-page,
.recharge-con, .account-con, .certification-pop-con, .content-setAccount,
/* 在 el-dialog 外部用 box-sizing-dialog 包一层，内部的 dialog 会变成 border-box，并且带有UI规范的样式 */
.box-sizing-dialog{
	.el-dialog__wrapper{
		*{
			box-sizing: border-box;
		}

		/* el-dialog 加上 el-dialog-bg 会带上淡蓝色的背景 */
		&.el-dialog-bg{
			.el-dialog{
				background: #f6f9fc;
			}
		}

		.el-dialog{
			box-shadow: 0px 0px 10px 2px #6c6c6c;
			// box-shadow: 0 1px 3px red;

			.el-dialog__header, .el-dialog__body, .el-dialog__footer{
				padding-left: 33px;
				padding-right: 33px;
			}

			.el-dialog__header{
				height: 65px;
				border-bottom: 1px solid #eee;
				border-radius: 2px 2px 0 0;
				background: #fff;

				.el-dialog__title{
					color: #333;
					font-size: 16px;
					font-weight: normal;
				}

				.el-dialog__close{
					font-size: 12px;
					color: #999;
				}
			}

			.el-dialog__body{
				padding-top: 20px;
				padding-bottom: 0;
				color: #333;

				.dialog_body_header{
					padding-left: 20px;
					padding-right: 20px;
					margin-bottom: 25px;
					height: 34px;
					line-height: 34px;
					color: #666;
					background: #ebf1f6;
					font-size: 14px;
					border-radius: 3px;

					i{
						margin-right: 5px;
						color: #1681d3;
					}

					&.error{
						background: #ffebe6;

						i{
							color: #f86e2a;
						}
					}
				}

				.el-form-item__label{
					font-size: 12px;
                }
                .content-tip {
                    margin: -20px -33px 20px;
                    padding: 0 33px;
                    line-height: 30px;
                    background: #F8F8F8;
                    font-size: 12px;
                    color: #999999;
                }
			}

			.el-dialog__footer{
				padding-top: 20px;

				.dialog-footer{
					.el-button{
						height: 34px;
						vertical-align: middle;
						font-size: 12px;
					}

					.el-button--primary{
						padding: 0 36px;
						border-color: $button-blue;
						background: $button-blue;
						color: #fff;

						&:hover{
							background: $button-hover-blue;
							border-color: $button-hover-blue;
						}

						&.is-disabled{
							background: $button-disable-gary;
							border-color: $button-disable-gary;
							color: #999;
						}
					}

					.el-button--default{
						padding: 0 21px;
						border-color: $button-default-border;
						background: $button-default-bg;

						&:hover{
							border-color: $button-default-border;
							background: #fff;
						}

						span{
							font-size: 12px;
							color: #333;
						}
					}
				}
			}

			&.title-hide{
				.el-dialog__header{
					display: none;
				}
			}

			&.footer-hide{
				.el-dialog__footer{
					display: none;
				}
			}
		}
	}
}

/* 移动端 el-dialog 样式 */
.el-dialog-mobile.el-dialog__wrapper{
	@media (max-width: 768px) {
		.el-dialog{
			width: 90%;

			.el-dialog__header, .el-dialog__body, .el-dialog__footer{
				padding-left: 25px;
				padding-right: 25px;
			}

			.el-dialog__header{
				.el-dialog__title{
					font-size: 18px;
					color: #000;
				}
			}

			.el-dialog__body{
				color: #333;
				font-size: 18px;
			}

			.el-dialog__footer{
				padding-top: 15px;

				.dialog-footer{

					.el-button{
						width: 100%;
						height: 40px;

						span{
							font-size: 18px;
						}
					}
				}
			}
		}
	}
}

.el-select .el-input{
	.el-icon-caret-top{
		// font-family:"iconfont" !important;
		color: #888;
		font-size: 12px;
	    // transform: translateY(-50%) rotateZ(0deg);

		// &:before{
		// 	content: '\E647';
		// }
	}

	&:hover{
		.el-input__inner{
			color: #2298f1;

			&::-webkit-input-placeholder{
				color: #2298f1;
			}
			&:-moz-placeholder{
				color: #2298f1;
			}
			&::-moz-placeholder{
				color: #2298f1;
			}
			&:-ms-input-placeholder{
				color: #2298f1;
			}
		}

		.el-icon-caret-top{
			color: #2298f1;

			&.is-reverse{
				transform: translateY(-50%) rotateZ(180deg);
			}
		}
	}
}

.ssq-select-noBorder.el-select .el-input{

	.el-input__inner{
		border: none;
		background: none;
		vertical-align: middle;

		&:hover, &:focus{
			border: none;
			box-shadow: none;
		}

		&::-webkit-input-placeholder{
			color: #333;
		}
		&:-moz-placeholder{
			color: #333;
		}
		&::-moz-placeholder{
			color: #333;
		}
		&:-ms-input-placeholder{
			color: #333;
		}
	}
}

.el-select-dropdown{
	.el-scrollbar > .el-select-dropdown__wrap > .el-select-dropdown__list{
		.selected{
			color: #333;
			background-color: $dropdown-blue;
		}
	}
}

.el-dropdown-menu.doc-cloumn-dropdown{
	min-width: 88px;
}

.doc-page,.template-page{
	.el-dropdown{ // 这个地方对上传文件页造成了影响
		border: 1px solid #ccc;
		border-radius: 2px;

		.el-button{
			background: #f8f8f8;
			color: #333;
			padding: 0 19px;
			height: 28px;
			border: none;

			.el-icon-caret-bottom{
				color: #777;
				font-family:"iconfont" !important;

				&:before{
					content: "\E647";
				}
			}

			&.el-dropdown__caret-button{
				padding: 0 4px;
				border-left: 1px solid #ccc;
			}

			&:hover{
				color: #333;
			}
		}

		&:hover{
			.el-button{
				background: #fff;
				border-color: #ccc;
				z-index: 0;
			}
		}

		&.expanded{
			background: #f8f8f8;

			.el-button{
				background: #f8f8f8;

				&.el-dropdown__caret-button{
					background: #eee;
				}
			}
		}

		&.blue{
			border-color: #2298f1;
			background: #2298f1;

			.el-button{
				color: #fff;
				background: #2298f1;

				&.el-dropdown__caret-button{
					border-left: 1px solid #127fd2;

					.el-icon-caret-bottom{
						color: #fff;
					}
				}
			}

			&:hover{
				.el-button{
					color: #fff;
					background: #118dea;

					&.el-dropdown__caret-button{
						background: #118dea;
					}
				}
			}

			&.expanded{
				background: #118dea;

				.el-button{
					background: #118dea;

					&.el-dropdown__caret-button{
						background: #127fd2;
					}
				}
			}
		}

		/* 审批按钮组 */
		&.green{
			border-color: #0fb396;
			background: #0fb396;

			.el-button{
				color: #fff;
				background: #0fb396;

				&.el-dropdown__caret-button{
					border-left: 1px solid #0b9c82;

					.el-icon-caret-bottom{
						color: #fff;
					}
				}
			}

			&:hover{
				.el-button{
					background: #00ac8e;

					&.el-dropdown__caret-button{
						background: #00ac8e;
					}
				}
			}

			&.expanded{
				background: #00ac8e;

				.el-button{
					background: #00ac8e;

					&.el-dropdown__caret-button{
						background: #079c82;
					}
				}
			}
		}
	}
}

.el-pagination{
	.el-pager{
		.active{
			border-color: $pagination-blue;
			background: $pagination-blue;
		}
	}
}

.ssq-steps {
	// 完成图标
	.el-step-done {
		.el-step__icon {
			// display: inline-block;
			line-height: 20px;
		}
		i {
			font-size: 15px;
		}
	}
	.el-step__head.is-wait {
		border-width: 2px;
    	border-style: solid;
		}
}
.el-message{
    box-sizing: border-box;
    min-width: 666px;
    padding: 11px 15px 11px 20px;
    border-radius: 4px;
    background-color: #F3FAF0;
    border: 1px solid #e7f5e0;
    .el-message__img{
        border-radius: 12px;
        width: 12px;
        height: 12px;
        margin: 0 10px 0 20px;
        top: 14px;
    }
    .el-message__group{
        margin-left: 20px;
    }
}
[dir=rtl] {
	.el-dialog {
		.el-dialog__headerbtn {
			float: left;
		}
		.el-dialog__footer {
			text-align: left;
		}
	}
	.el-table th{
		text-align: right;
	}
}
