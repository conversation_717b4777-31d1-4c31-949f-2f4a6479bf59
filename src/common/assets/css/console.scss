// 企业控制台的样式，部分文件会用到
.console-btn{
    padding-top: 8px;
    padding-bottom: 8px;
    height: 34px;
    border-radius: 2px;
    border: 1px solid #ccc;
    background: #f8f8f8;
    color: #666;
    transition: all 0.2s;


    &:hover{
        color: #666;
        border-color: #ccc;
        background: #fff;
    }

    &:active,&:focus,&:visited{
        color: #666;
    }

    &.el-button--small{
        padding-top: 0;
        padding-bottom: 0;
        height: 28px;
    }

    span{
        font-size: 12px;
    }
}

.console-btn-primary{
    border-color: $btn-primary-color;
    color: #fff;
    background: $btn-primary-color;

    &:active,&:hover,&:focus,&:visited{
        color: #fff;
        border-color: $btn-primary-hover-color;
        background: $btn-primary-hover-color;
    }
}

.console-btn-whiteBg{
    border-color: $btn-primary-color;
    background: #fff;
    color: $btn-primary-color;

    &:focus,&:hover,&:visited{
        background: $btn-primary-hover-color;
        color: #fff;
        border-color: $btn-primary-hover-color;
    }
}

.console-btn-addBtn{
    background: $btn-primary-color;
    color: #fff;
    border-color: $btn-primary-color;
    height: 36px;
    font-size: 14px;

    &:focus,&:hover,&:visited{
        background: $btn-primary-hover-color;
        color: #fff;
        border-color: $btn-primary-hover-color;
    }

    .el-icon-ssq-jia{
        font-size: 14px;
        font-weight: bold;
        margin-right: 6px;
    }
}

.console-btn.el-button.is-disabled, .console-btn.el-button.is-disabled:focus, .console-btn.el-button.is-disabled:hover{
    border-color: #ccc;
    color: #ccc;
    background: #f8f8f8;
}

span.console-link{
    color: $link-color;
    cursor: pointer;

    &:hover{
        color: $link-hover-color;
    }
}