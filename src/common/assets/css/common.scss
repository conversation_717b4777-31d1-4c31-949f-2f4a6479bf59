
// html,body{
//     height: 100%;
// }

main{
    width: 100%;
    height: 100%;
}

// a标签
a {
    color: #337ccf;
}

// 清除浮动
.clear {
    clear:both;
    &:after {
        clear:both; content:'.'; display:block; width: 0; height: 0; visibility:hidden;
    }
}

// 浮动
.fl {
    float: left;
}
.fr {
    float: right;
}

// cursor: default
.cur-default {
    cursor: default;
}

// cursor: pointer
.cur-pointer {
    cursor: pointer;
}

// iconfont
.icon {
   width: 1em; height: 1em;
   vertical-align: -0.15em;
   fill: currentColor;
   overflow: hidden;
}

// font-size 0 清除 inline-block 的边距
.font-size-zero{
    font-size: 0;
}

// 单行...
.etc-sigle {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

// tabel-cell
.table-cell{
    display: table-cell;
}

// inline-block
.ilBlock{
    display: inline-block;
}

// border-box
.border-box{
    box-sizing: border-box;
}

// vertical-align middle
.vm{
    vertical-align: middle;
}

// text-align center
.tl{
    text-align: left;
}
.tc{
    text-align: center;
}
.tr{
    text-align: right;
}

.transparent{
    opacity: 0;
}

.default-logo{
    vertical-align: middle;
}

.el-dialog{
    transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -webkit-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
}

// input placeholder
:-moz-placeholder { /* Mozilla Firefox 4 to 18 */
    color: #ccc; opacity:1;
}

::-moz-placeholder { /* Mozilla Firefox 19+ */
    color: #ccc;opacity:1;
}

// chrome input黄色背景
input:-webkit-autofill,
textarea:-webkit-autofill,
select:-webkit-autofill {
    -webkit-box-shadow: 0 0 0 1000px white inset;
}
input[type=text]:focus,
input[type=text]:hover,
input[type=password]:focus,
textarea:focus {
    -webkit-box-shadow: 0 0 0 1000px white inset;
}

input:-ms-input-placeholder{
    color: #ccc;opacity:1;
}

input::-webkit-input-placeholder{
    color: #ccc;opacity:1;
}

// 注册模块

.register {
    input {
        width: 335px;
        height: 42px;
    }
}

// 表单错误pop提示
.validation-error {
	display: inline-block;
    z-index: 9;
    position: relative;
    box-sizing: border-box;
    height: 40px;
    line-height: 41px;
    font-size: 12px;
    color: #f76b26;
    text-align: center;
    background-color: #fffaf7;
    padding-left: 18px;
    padding-right: 10px;
    border: 1px solid #f76b26;
    border-radius: 2px;
    margin-top: 1px;
    margin-left: 18px;
    &:after {
        content: '';
        position: absolute;
        top: 15px;
        left: -4px;
        width: 6px;
        height: 6px;
        background-color: #fffaf7;
        border-right: 1px solid #f76b26;
        border-bottom: 1px solid #f76b26;
        transform:rotate(135deg);
    }
    i {
        position: relative;
        top: 1px;
        font-size: 15px;
        margin-top: 1px;
    }
}
// 表单错误底部提示
.validation-error-bottom {
    color: #ff4949;
    font-size: 12px;
    line-height: 1;
    position: absolute;
    top:100%;
    left: 0;
    .el-icon-ssq-wrong-filling{
        color: #ff4949!important;
        font-size: 12px!important;
    }
}

// input框尾部的小图标
.el-input__icon, .cs {
    cursor: pointer;
}

.authentication {
    .el-upload {
        position: relative;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
        // z-index: 10;
        .el-upload__input {
            position: absolute;
            top: 0;
            left: 0;
            z-index: -1;
            display: block;
            opacity: 0;
            -moz-opacity:0 ;
            filter: alpha(opacity=0);
        }
    }
}

// 用户引导
.user-guide{
    z-index: 2001;

    &.active-step-1 .guide-step:first-child,
    &.active-step-2 .guide-step:nth-child(2),
    &.active-step-3 .guide-step:nth-child(3){
        display: block;
    }

    &:after{
        content: '';
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        z-index: 2000;
        opacity: .5;
        background: #000;
    }
}

.guide-step{
    display: none;
    position: absolute;
    z-index: 2001;

    .guide-text{
        position: absolute;
        color: #fff;
        font-size: 14px;
    }

    .guide-trigger{
        position: absolute;
        display: inline-block;
        width: 93px;
        height: 39px;
        cursor: pointer;
        // 必须要加个背景，不然 IE 无法显示。。。
        background: #fff;
        opacity: 0;
    }
}

/**
 *  重写注册页面elementUI表单样式
 *  标签el-form-item
 *  标签el-input
 *  标签el-checkbox-group
 */

.register,.ssq-form {
    .el-form-item {
        margin-bottom: 20px;
        .el-form-item__label {
            width: 90px;
            padding-right: 20px;
            color: #333;
            text-align: right;
        }
        &.el-form-item.is-error {
            .el-input__inner {
                border-radius: 1px;
                border-color: #ccc;
            }
            .el-input__inner:hover {
                border-color: #127fd2;
            }

            .el-input__inner:focus {
                border-color: #127fd2;
                box-shadow: 0 0 2px #47acfc;
            }
        }
        .el-form-item__content{
            font-size: 0;
        }
    }

    .el-input {
        display: inline-block;
        width: auto;
        .el-input__inner {
            border-radius: 1px;
            border-color: #ccc;
        }
        .el-input__inner::-webkit-input-placeholder {
            color: #ccc;
            font-size: 14px;
        }
        .el-input__inner::-moz-placeholder {
            color: #ccc;
            font-size: 14px;
        }

        .el-input__inner:-ms-input-placeholder {
            color: #ccc;
            font-size: 14px;
        }

        .el-input__inner::placeholder {
            color: #ccc;
            font-size: 14px;
        }

        .el-input__inner:hover {
            border-color: #127fd2;
        }

        .el-input__inner:focus {
            border-color: #127fd2;
            box-shadow: 0 0 2px #47acfc;
        }
    }

    .el-checkbox-group {
        .el-checkbox__inner {
            width: 16px;
            height: 16px;
            border-radius: 2px;
            border-color: #ccc;
        }
        .el-checkbox__input.is-focus .el-checkbox__inner {
            background-color: #fff;
            border-color: #ccc;
        }
        .el-checkbox__input.is-checked .el-checkbox__inner {
            background-color: #2298f1;
            border-color: #2298f1;
        }
        .el-checkbox__input.is-disabled .el-checkbox__inner {
            background-color: #d1dbe5 !important;
            border-color: #d1dbe5 !important;
        }
        .el-checkbox__label {
            font-size: 12px;
            color: #666;
            vertical-align: middle;
        }
    }

    // 按钮禁用
    .el-button {
        padding: 0;
        width: 155px;
        height: 42px;
        font-size: 16px;
        font-weight: bold;
        // background-color: #127fd2;
        // color: #fff;
        // border-radius: 2px;
    }
    .is-disabled {
        background-color: #ededed;
        color: #999;
        border: none;
    }

    // step
    .el-step {
        .el-step__title {
            margin-left: -10px;
        }
    }

}


.ssq-search{
    box-sizing: border-box;
    position: relative;
    .el-button--small {
        padding: 7px 9px;
    }
    .el-icon-search{
        font-size: 15px;
        color: #ccc;
        &:hover {
            color: #127fd2;
        }
    }

    .el-input__inner,.el-input-group__prepend,.el-input-group__append{
        border-radius: 0;
        border-color: #cccccc;
    }
    .el-input-group__prepend {
        position: absolute;
        top: 11px;
        right: 13px;
        background: #fff;
        border: 0;
    }

    .el-input__inner{
        padding-right: 35px;
        transition: all 0.5s;
        font-size: 12px;
    }

    .el-input__inner:hover {
        border-color: #127fd2;
        box-shadow: 0 0 2px #47acfc;
    }

    .el-input__inner:focus {
        border-color: #127fd2;
        box-shadow: 0 0 2px #47acfc;
    }
}

.ssq-table{
    .ssq-table-tr-hover-block{
        display: none;
    }
    &::after{
        display: none;
    }
}
.template-match-table.el-table.el-table--enable-row-hover{
    border-top-color: #e7e7e7;
    border-bottom-color: #eee;
    border-right: 1px solid #e7e7e7;
    font-size: 12px;

    &::after, &::before {
        display: none;
    }
    th,.el-table__header-wrapper thead div{
        font-size: 12px;
        font-weight: bold;
    }

    .el-table__body-wrapper {
        overflow-x: auto;
        overflow-y: auto;

        .el-table__empty-block{
            .el-table__empty-text{
                color: #333;
            }
        }

        tr:hover>td{
            background-color: unset;
            &.rule{
                background-color: #e1ffc6;
            }
            .ssq-table-tr-hover-block{
                display: inline-block;
            }
        }
    }

    tr,td,th.is-leaf{
        border-bottom-color: #eeeeee;
    }

    .el-table__empty-block{
        .el-table__empty-text{
            position: absolute;
            top: 40%;
        }
    }

}

.ssq-table.el-table.el-table--enable-row-hover{
    border-left: none;
    border-right: none;
    border-top-color: #e7e7e7;
    border-bottom-color: #eee;
    font-size: 12px;

    &::after, &::before {
        display: none;
    }
    th,.el-table__header-wrapper thead div{
        font-size: 12px;
        font-weight: bold;
        background: #f8f8f8;
    }

    .el-table__body-wrapper {
        overflow-x: auto;
        overflow-y: auto;

        .el-table__empty-block{
            .el-table__empty-text{
                color: #333;
            }
        }

        tr:hover>td{
            background-color: #fbfbfb;

            .ssq-table-tr-hover-block{
                display: inline-block;
            }
        }
    }

    tr,td,th.is-leaf{
        border-bottom-color: #eeeeee;
    }

    .el-table__empty-block{
        .el-table__empty-text{
            position: absolute;
            top: 40%;
        }
    }

}

table.el-table__body tr.hover-row>td{
    background-color: #fbfbfb;
    background: #fbfbfb;
}

// 按钮样式
// 下一步
.btn-type-one {
    box-sizing: content-box;
    cursor: pointer;
    border: none;
    border-radius: 2px;
    border: 1px solid #127fd2;
    background-color: #127fd2;
    color: #fff;
    text-align: center;
    &:hover, &:focus, &:visited {
        background-color: #1687dc;
        border-color: #1687dc;
        color: #fff;
    }
    &.disabled {
        background-color: #ededed;
        border-color: #ededed;
        color: #999;
        font-weight: bold;
    }
}
// 取消
.btn-type-two {
    box-sizing: content-box;
    cursor: pointer;
    border: 1px solid #ccc;
    border-radius: 2px;
    background-color: #f8f8f8;
    color: #666;
    text-align: center;
    &:hover {
        border: 1px solid #ccc;
        color: #666;
        background-color: #fff;
    }

    &:focus {
        border-color: #ccc;
        color: #666;
    }
}
// 新增分组
.btn-type-three {
    box-sizing: content-box;
    cursor: pointer;
    border-radius: 2px;
    background-color: #0fb396;
    color: #fff;
    text-align: center;
    &:hover {
        background-color: #00ac8e;
    }
}
// 编辑
.btn-type-four {
    box-sizing: content-box;
    cursor: pointer;
    border-radius: 2px;
    border: 1px solid #2298f1;
    background-color: #fff;
    color: #2298f1;
    text-align: center;
    &:hover {
        background-color: #2298f1;
        color: #fff;
    }
}
// 批量导入
.btn-type-five, .el-button.btn-type-five {
    box-sizing: content-box;
    cursor: pointer;
    border-radius: 2px;
    border: 1px solid #ccc;
    background-color: #fff;
    color: #666;
    text-align: center;
    &:hover {
        background-color: #f8f8f8;
        color: #555;
    }
    &.disabled {
        border-color: #ececec;
        background-color: #f8f8f8;
        color: #ccc;
    }
}
// 文字样式
.common-font-color {
    color: #127fd2;
    &:hover {
      color: #1687dc;
    }
}

// loading
.el-loading-spinner .path {
    stroke: #127fd2;
}
// el-message-box this.$confirm() 待检验...
.el-message-box {
    border-radius: 2px;
    .el-message-box__header {
        padding: 20px 30px 0;
    }
    .el-message-box__content {
        padding: 40px 30px 48px;
        border-bottom: 1px solid #EAEBED;
        font-size: 14px;
        color: #333;
    }
    .el-message-box__btns {
        padding: 13px 34px;
        .el-button {
            height: 32px;
            font-size: 12px;
            border-radius: 2px;
        }
        .el-button--default {
            background-color: #F6F7F8;

            &:hover{
                color: #333;
                border-color: #ccc;
            }
        }
        .el-button--primary {
            background-color: #127fd2;
            border-color: #127fd2;

            &:hover{
                color: #fff;
                background-color: #1687dc;
                border-color: #1687dc;
            }
        }
    }
}

//  message-box confirm通用弹窗样式
.message-box-confirm-custom.el-message-box {
    .el-message-box__btns .el-button {
        float: none;
    }
    .el-message-box__status {
        top: 43px;
        transform: translateY(0);
        font-size: 16px !important;
    }
    .el-message-box__message {
        margin-left: 30px !important;
    }

}

// input
input:focus {
    border-color: #127fd2;
    box-shadow: 0 0 2px #47acfc;
}

// btn
.ssq-btn-confirm {
    cursor: pointer;
    border-radius: 2px;
    background-color: #127fd2;
    color: #fff;
    text-align: center;
    width: 100px;
    height: 34px;
    border-radius: 2px;
    &:hover {
        background-color: #1687dc;
        color: #fff;
    }
    &.el-button[disabled] {
        background: #127fd2;
        border-color: #127fd2;
        color: rgba(255, 255, 255, 0.5);
    }
    &.disabled {
        background-color: #ededed;
        color: #999;
        font-weight: bold;
    }
}
.ssq-btn-cancel {
    width: 66px;
    height: 34px;
    cursor: pointer;
    border-radius: 2px;
    background-color: #f8f8f8;
    color: #666;
    text-align: center;
    border: 1px solid #ccc;
    border-radius: 2px;
    &:hover {
        background-color: #fff;
    }
}
.ssq-blue-btn {
    box-sizing: content-box;
    width: 98px;
    height: 32px;
    line-height: 34px;
    font-size: 14px;
    cursor: pointer;
    border-radius: 2px;
    background-color: #127fd2;
    color: #fff;
    text-align: center;
    border: 1px solid #127fd2;
    &:hover {
        background-color: #1687dc;
        color: #fff;
    }
    &.disabled {
        background-color: #ededed;
        color: #999;
        font-weight: bold;
    }
}

// dialog
.ssq-dialog__notitle {
    .el-dialog{
        width: 333px;
        border-radius: 1px;
    }
    .el-dialog__header {
        padding: 0;
    }
    .el-dialog__body {
        background-color: #F6FAFD;
    }
    .el-dialog__footer {
        padding-top: 13px;
        padding-bottom: 13px;
        .el-button+.el-button {
            margin-left: 20px;
        }
        button {
            float: left;
            width: 100px;
            height: 34px;
            border-radius: 2px;
            &:first-child {
                margin-left: 38px;
            }
        }
    }
}

// dialog UI规范
.ssq-dialog {
    .el-dialog {
        border-radius: 3px;
        box-shadow: #666 0px 0px 6px;
    }
    .el-dialog__header {
        padding: 25px 33px;
        border: 1px solid #ddd;
        .el-dialog__title {
            font-size: 16px;
            font-weight: normal;
            color: #333;
        }
        .el-icon-close {
            font-size: 12px; // X大小
            font-weight: bold;
            color: #999;
        }
    }
    .el-dialog__body {
        padding: 33px;
        background-color: #f6f9fc;
        button {
            float: right;
        }
    }
    .el-dialog__footer {
        padding: 14px 33px;
    }
}

// 混合云网络不通提醒弹窗
.hybrid-network-tip-msgbox .el-dialog__wrapper{
    z-index: 99999;
    .el-dialog{
        width: 630px;

        .el-dialog__header{
            border-bottom: none;
        }

        div.el-dialog__body{
            padding-top: 20px;
            padding-bottom: 20px;
            background: #fff;
            text-align: left;
        }

        div.el-dialog__footer{
            padding-top: 15px;
            text-align: right;

            .dialog-footer .el-button--primary{
                width: 100px;
                text-align: center;
                padding: 0;
            }
        }
    }

    @media (max-width: 768px) {
        .el-dialog{
            width: 90%;
        }
    }
}
// 批量下载->无法下载合同列表 提示弹窗
.doc-batch-download-tip-Dialog {
    width: 600px;
    .el-message-box__header {
        height: 50px;
        line-height: 50px;
        padding: 0 0 0 30px;
        font-weight: 400;
    }
    .el-message-box__content {
        background-color: rgb(246,249,252);
        border-bottom: none;
        padding: 20px 30px 20px;
        font-size: 14px;
        color: #333;
    }
    .el-message-box__message {
        max-height: 300px;
        overflow-y: auto;
        .tip {
            color: rgb(153,153,153);
            padding-bottom: 15px;
            border-bottom: 1px dashed;
        }
        .name-list {
            margin: 10px 0;
        }
        .file-list {
            width: 540px;
            height: 30px;
            line-height: 30px;
            overflow-x: hidden;
            white-space:nowrap;
            text-overflow:ellipsis;
        }
        .file-list-icon {
            display: inline-block;
            height: 20px;
            width: 25px;
            vertical-align: middle;
            background: {
                image: url('~img/download-file-icon.png');
                size: auto 19px;
                repeat: no-repeat;
            };

        }
    }
    .el-message-box__btns {
        background-color: rgb(246,249,252);
    }
    .el-message-box__btns button:nth-child(2) {
        margin-left: 20px;
    }
}

// .register {
//     .el-dialog--small {
//         width: 20%;
//     }
//     .el-dialog__header {
//         display: none!important;
//     }
//     .el-dialog__body {
//         text-align: center;
//     }
// }

/*
  手机屏幕适配
*/

@media (min-width: 320px) and (max-width: 768px) {
    .register,.ssq-form {
        .el-form-item {
            .el-form-item__label {
                width: 90px;
                padding-right: 10px;
                color: #333;
                text-align: right;
            }
        }
    }
    .el-message-box {
        width: 85%;
    }
}
// 注册协议弹窗样式
.registration-agreement-dialog {
    .el-dialog {
        width: 800px;
        .el-dialog__header {
            display: block !important;
            padding: 20px 35px 20px;
        }
        .el-dialog__title {
            color: #333;
            font-size: 16px;
        }
        .el-dialog__headerbtn .el-dialog__close {
            color: #999;
        }
        .el-dialog__body {
            padding-left: 35px;
            border-top: 1px solid #eee;
            background-color: #f6f9fc;
            font-size: 12px;
        }
        .el-dialog__footer {
            text-align: center;
            background-color: #f6f9fc;
            padding-bottom: 25px;
            padding-top: 0;
        }
        .el-button--primary {
            width: auto;
            height: auto;
            padding: 10px 35px;
            font-weight: normal;
        }
    }
}

.authDialog{
    .el-dialog{
        width: 400px;
    }
    @media (min-width: 320px) and (max-width: 768px) {
        .el-dialog{
            width: 340px;
        }
    }
    .ent-name{
        font-size: 14px;
        color: #333;
        margin-bottom: 20px;
    }
    li{
        font-size: 12px;
        list-style-type: disc;
        list-style-position: inside;
    }
    .el-dialog__header{
        line-height: 65px;
        padding-top: 0;
        border-bottom: 1px solid #ccc;
    }
    .el-dialog__body, .el-dialog__footer{
        background: rgb(246, 249, 252);
    }
    .el-button{
        padding: 10px 25px;
    }
}
.reject-reason-dialog {
    .el-dialog {
        width: 600px;
    }
    .el-dialog__body {
        overflow: hidden;
        padding-bottom: 20px !important;
    }
    p {
        color: #333333;
        margin-bottom: 16px;
        font-size: 14px;
    }
    .el-checkbox {
        display: block !important;
        margin-left: 0;
        margin-bottom: 16px;
        font-size: 12px;
        color: #333333;
        white-space: unset;
        [dir="rtl"] & {
            margin-right: 0;
        }
    }
    &-input {
        position: relative;
        .el-textarea__inner {
            padding: 10px 16px;
        }
        .refuse-description {
            position: absolute;
            right: 10px;
            bottom: 10px;
            [dir="rtl"] & {
                right: auto;
                left: 10px;
            }
        }
    }
}
.reject-check-dialog {
    .el-dialog {
        width: 400px;
    }
}

.config-dialog{
    width: 460px;
    /*height: 230px;*/
    box-shadow: 0 0 8px 0 rgba(142,142,142,0.50);
    border-radius: 8px;
    padding: 35px 22px 33px 33px;
    .el-dialog__header {
        padding: 0;
        .el-dialog__title {
            font-size: 13px;
            font-weight: 600;
            color: #333;
        }
    }
    .el-dialog__body {
        font-size: 12px;
        padding: 10px 0 20px;
    }
    .el-dialog__footer {
        padding: 0 11px;
        .tip-text {
            font-size: 12px;
            color: #333;
        }
        .el-button {
            height: 30px;
            line-height: 30px;
            padding: 0 10px;
            margin-left: 5px;
        }
    }
}

[dir="rtl"] .el-table {
    transform: scaleX(-1);
    direction: rtl;
    text-align: right;
    .el-table__header-wrapper,
    .el-table__body-wrapper,
    .el-table__fixed-right .el-table__fixed-header-wrapper,
    .el-table__fixed-right .el-table__fixed-body-wrapper {
        transform: scaleX(-1);
    }
}
