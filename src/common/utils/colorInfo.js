export const colorInfo = [
    '#c9e7ff',
    '#ffd65b',
    '#add9e3',
    '#e7aecc',
    '#b2bff6',
    '#94d4ad',
    '#f9c2a1',
    '#f39898',
    '#9dd0c5',
    '#e0e199',
];

export const rgbColorInfo = [
    '201, 231, 255', // '#c9e7ff',
    '255, 214, 91', // '#ffd65b',
    '173, 217, 227', // '#add9e3',
    '231, 174, 204', // '#e7aecc',
    '178, 191, 246', // '#b2bff6',
    '148, 212, 173', // '#94d4ad',
    '249, 194, 161', // '#f9c2a1',
    '243, 152, 152', // '#f39898',
    '157, 208, 197', // '#9dd0c5',
    '224, 225, 153', // '#e0e199'
];

// 根据元素在列表中的排序，获取rgbcolor
export function calcRgbColor(list, propsName, propsValue) {
    const index = (list || []).findIndex(item => item[propsName] === propsValue);
    return calcRgbColorByIndex(index);
}
export function calcRgbColorByIndex(index) {
    return rgbColorInfo[index % 9];
}

export function calcFillColor(list, propsName, propsValue) {
    const index = (list || []).findIndex(item => item[propsName] === propsValue);
    return calcFillColorByIndex(index);
}
export function calcFillColorByIndex(index) {
    return colorInfo[index % 9];
}
