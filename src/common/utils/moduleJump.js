/**
 * @desc 拦截子模块跳转
 * <AUTHOR>
 * @date 2020.04.09
 * @returns promise
 */
// 登录注册模块路由，对应public-library const.js中的ACCOUNT_CENTER_ROUTERS
import { ACCOUNT_CENTER_ROUTERS, AUTH_PC_ROUTERS } from '@/common/const/accountCenterRouters.js';

// 子模块名称对应的 port 和 path
const WebModuleMap = new Map([
    [
        'userCenter',
        {
            port: '6001',
            originPath: '/usercenter', // 现有的 path
            jumpPath: '/usercenter', // 拆分子项目的 path 可能发生改变
        },
    ],
    [
        'statistics',
        {
            port: '6002',
            originPath: '/statistics',
            jumpPath: '/statistics',
        },
    ],
    [
        'console',
        {
            port: '6003',
            base: '/console',
            originPath: '/enterprise/console',
            // redirect path map
            pathMapFnPriority(path) {
                if (/^\/enterprise\/console/.test(path)) {
                    return path.replace('/enterprise/console', '/console/enterprise');
                }
                return path;
            },
            jumpPath: '/console',
        },
    ],
    [
        'accountCenter',
        {
            port: '6004',
            originPath: [
                ...ACCOUNT_CENTER_ROUTERS, // 登录模块
            ],
            jumpPath: '/account-center',
            pathMapFnPriority(path) {
                return `/account-center${path.replace('/account-center', '')}`;
            },
        },
    ],
    [
        'dataBox',
        {
            port: '6005',
            originPath: '/box',
            jumpPath: '/box',
        },
    ],
    [
        'contractCenter',
        {
            port: '6006',
            originPath: '/cm',
            jumpPath: '/cm',
        },
    ],
    [
        'contractDraft',
        {
            port: '6007',
            originPath: '/cc',
            jumpPath: '/cc',
        },
    ],
    [
        'signFlow',
        {
            port: '6008',
            originPath: '/sign-flow',
            jumpPath: '/sign-flow',
        },
    ],
    [
        'authPC',
        {
            port: '6009',
            jumpPath: '/auth-p',
            base: '/auth-p',
            originPath: AUTH_PC_ROUTERS,
            pathMapFnPriority(path) {
                let finallyPath = path;
                [[
                    /\/authentication/,
                    '',
                ], [
                    /personal\/certification/,
                    'person',
                ], [
                    /\/personauth/,
                    'person',
                ]].some(arr => {
                    if (arr[0].test(path)) {
                        finallyPath = path.replace(arr[0], arr[1]);
                        return true;
                    }
                    return false;
                });
                return `/auth-p${finallyPath.replace('/auth-p', '')}`;
            },
        },
    ],
]);

// mobile 相关项目暂时只支持从 SaaS 跳转过去，不支持回跳
const MobileModuleMap = new Map([
    [
        'mobile',
        {
            port: '7001',
            originPath: '/mobile',
            jumpPath: '/mobile',
        },
    ],
    [
        'damp',
        {
            port: '7002',
            originPath: '/damp',
            jumpPath: '/damp',
        },
    ],
    [
        'authMobile',
        {
            port: '7003',
            originPath: '/mp/auth-m',
            jumpPath: '/mp/auth-m',
        },
    ],
]);

const ModuleMap = new Map([
    ...WebModuleMap,
    ...MobileModuleMap,
]);

// 从 ModuleMap 映射中找到对应跳转的子系统
function findSubModule(toPath) {
    return [...ModuleMap.values()].filter((item) => {
        if (item.originPath instanceof Array) { // 登录注册模块路由是无规则的，统一放在数组里进行判断
            return item.originPath.some(path => {
                if (Object.prototype.toString.call(path).slice(8, 14) === 'RegExp') {
                    return path.test(toPath);
                }
                return toPath.includes(path);
            }) || toPath.includes(item.jumpPath);
        }
        return toPath.includes(item.originPath) || toPath.includes(item.jumpPath);
    });
}

// 根据 to.fullPath 在映射中寻找对应的子模块，并替换跳转的 url
function getJumpModule(toPath) {
    const module = findSubModule(toPath);
    const SaaSDefault = {
        port: 9000,
        jumpPath: toPath,
    };
    const tempModule = module.length && module[module.length - 1] || [];

    function resolvePath(path) {
        // 如果当前 pathname 上已经有当前模块的前缀了，不重复添加
        return location.pathname.includes(tempModule.jumpPath) ? path.replace(tempModule.jumpPath, '') : path;
    }

    // 找不到对应子模块默认跳转旗舰版
    return module.length ? {
        ...tempModule,
        // 跳转的 jumpPath 重新赋值
        ...{
            jumpPath: resolvePath(tempModule.pathMapFnPriority ? tempModule.pathMapFnPriority(toPath) : toPath.replace(tempModule.originPath, tempModule.jumpPath)),
        },
    } : SaaSDefault;
}

// 组装跳转的 url ，
function getModuleUrl(path, fullPath) {
    const moduleObj = getJumpModule(path);
    // 判断开发环境加上 port
    const objHost = location.hostname + (process.env.NODE_ENV.includes('development') ? (':' + moduleObj.port) : ':' + location.port);
    // 弱水三千，只取 query
    return `${location.protocol}//${objHost}${moduleObj.jumpPath}${fullPath.replace(path, '')}`;
}

// to.path 是否在当前模块路由中匹配
function routerMatchedCurModule(to) {
    const matchList = to.matched;
    return !(matchList.length === 0 || matchList[0].path === '*');
}

// 判断是其他模块的路由
function isToOtherModule(to, router) {
    // 先判断 router 是否能匹配到当前模块的路由
    if (routerMatchedCurModule(to)) {
        // 返回 false 代表是当前模块的路由
        return false;
    }

    // 判断 location.pathname 是否包含本模块，需要根据 router name 标识本模块
    // 这里返回 false 代表本模块的路由无法匹配 to.path 但是路径中又带有本模块的前缀，utilityFunctions 中会重定向至模块的 404
    if (to.path.includes(router.options.name)) {
        return false;
    }

    // 剩下交由 getModuleUrl 对路径进行修正，或者转到入口模块去处理
    return true;
}

// eslint-disable-next-line no-unused-vars
export function checkModuleJump({ to, from, router, moduleType = 'sub' }) {
    return new Promise(resolve => {
        const subModuleJump = isToOtherModule(to, router) && moduleType === 'sub';
        const mainModuleJump = findSubModule(to.path).length > 0 && moduleType === 'main';
        if (subModuleJump || mainModuleJump) {
            location.href = getModuleUrl(to.path, to.fullPath);
            resolve(false);
            return false;
        }
        resolve(true);
    });
}
