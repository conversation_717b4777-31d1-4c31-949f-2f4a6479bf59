import wxSDK from 'weixin-js-sdk';
/* __wxjs_environment这个对象，在测试的一台三星上也不行，Android6.0系统。
 而微信提供的原生方法，wx.miniProgram.getEnv。在小程序和微信浏览器打开，是没有问题的。
 但是在浏览器里面，回调不执行*/
function goReturnUrl(returnUrl) {
    const wx = window.wx || wxSDK;
    returnUrl = decodeURIComponent(returnUrl);
    var ua = navigator.userAgent.toLowerCase();
    /* 判断是微信且不是企业微信，企业微信不支持小程序api并且没有被嵌套在iframe下*/
    if (ua.match(/MicroMessenger/i) === 'micromessenger' && ua.indexOf('wxwork') === -1 && window.self === window.top) {
        // 说明在微信中
        wx.miniProgram.getEnv(function(res) {
            if (location.href.includes('wxBack=')) {
                // eslint-disable-next-line no-undef
                const router = qs.parse(location.href.split('?')[1]);
                const tiers = router.wxBack || 1;
                wx.miniProgram.navigateBack({
                    delta: +tiers,
                });
                return false;
            }
            /* 小程序里面要跳转的链接也可能是h5的*/
            if (res.miniprogram && returnUrl.indexOf('http') === -1) {
                // 走在小程序的逻辑，考虑到returnUrl有可能是小程序的Tab页，同时调用两种方法
                wx.miniProgram.redirectTo(
                    { url: returnUrl },
                );
                wx.miniProgram.switchTab({
                    url: returnUrl,
                });
            } else {
                // 走不在小程序的逻辑
                window.location.replace(returnUrl);
                returnUrl.indexOf('previewSign') > 0 && location.reload();
            }
        });
    } else {
        // 走不在小程序的逻辑
        window.location.replace(returnUrl);
        returnUrl.indexOf('previewSign') > 0 && location.reload();
    }
}
export {
    goReturnUrl,
};
