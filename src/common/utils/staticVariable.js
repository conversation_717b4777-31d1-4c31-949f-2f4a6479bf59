// 全局变量
import i18n from 'src/lang';
import { getHOSTENV, getPresentEnv } from './getHOSTENV.js';

const Version = '1.16.0';

// 有域名时设置cookie domain为bestsign.cn, ip地址访问时直接取ip
const secondaryDomain = 'bestsign';
const COOKIE_DOMAIN = new RegExp(secondaryDomain).test(document.domain)
    ? document.domain.split('.').splice(-2).join('.') : document.domain;

const HOST_ENV = getHOSTENV(); // 判断是官网还是建行
const ENV_NAME = getPresentEnv(); // 判断是预发布还是正式环境,使用：this.GLOBAL.ENV_NAME
// console.log('GLOBAL_ENV_NAME::' + ENV_NAME);

// URI根路径，'', '/ssoinner', '/ssoouter'
const rootPathName = '';

const AES_ENCRYPT_KEY = '44eb2e88a84656e756ec397bd2f18a7d';

const isEn = i18n.locale === 'en';
const LOGO = isEn ? require('img/bestsign-logo-en.png') : require('img/bestsign-logo.png');
const WHITE_LOGO = isEn ? require('img/bestsign-logo-white-en.png') : require('img/bestsign-logo-white.png');

export default {
    HOST_ENV,
    ENV_NAME,
    COOKIE_DOMAIN,
    Version,
    rootPathName,
    AES_ENCRYPT_KEY,
    LOGO,
    WHITE_LOGO,
};
