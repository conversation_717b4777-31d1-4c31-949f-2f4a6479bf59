// 简单防抖
export const debounce = (action, delay) => {
    let timeout = null;
    return function() {
        const context = this;
        const args = arguments;
        if (timeout !== null) {
            clearTimeout(timeout);
        }
        timeout = setTimeout(() => {
            action.apply(context, args);
            clearTimeout(timeout);
        }, delay);
    };
};

import i18n from 'src/lang';
// 模板中骑缝章宽高，签署时骑缝章容器宽度160，保证宽度100%
export const DEFAULT_RS_HEIGHT = 160;
// 除与合同重叠部分以外，骑缝章宽度/阴影背景部分宽度
export const RIDESEAL_SHADOW_WIDTH = 120;

// 编辑模板中需要根据角色信息决定水印的展示
export function factoryWatermarkText({ userType, userAccount, userName, roleName, enterpriseName }) {
    if (userType === 'PERSON') { // 个人账号
        if (userAccount) {
            return `${userAccount}${userName ? ('(' + userName + ')') : ''}`;
        } // userNameNoDesensitization：水印姓名不脱敏
        return `${roleName}(${i18n.t('pointPositionSite.watermarkTip')})`;
    }
    if (userType === 'ENTERPRISE') { // 企业账号
        return enterpriseName || userAccount || `${roleName}(${i18n.t('pointPositionSite.watermarkTip')})`;
    }
}

/**
 * @param  {Array} data 签署人信息
 * @param  {Number} pageHeight 页高
 * @param  {Boolean} inTemplate 是否是模板中的应用
 * @return {Object}  watermarkList 水印信息，ridingSealList骑缝章信息
 * @desc  组装后台返回的合同装饰数据结构
 */
// 计算插入骑缝章的位置，返回y轴相对坐标
export function calcRideSeal(ridingSealList, pageHeight) {
    // 默认占的百分比
    const height = DEFAULT_RS_HEIGHT / pageHeight;
    // 如果为第一个骑缝章，则放在顶部
    if (!ridingSealList.length) {
        return 1 - height;
    }
    // 从上到下排序
    const sortList = ridingSealList.map(rs => rs.y).sort((a, b) => b - a);
    // 在顶部插入一个顶部坐标为1的元素方便计算
    sortList.unshift(1);
    const index = sortList.findIndex((val, i) => {
        const v1 = val;
        let v2;
        if (i === sortList.length - 1) { // 最后一个的话，height就可以了
            v2 = 0; // 起始的最低坐标点为0
            return v1 - v2 >= height;
        } else {
            v2 = sortList[i + 1];
            return v1 - v2 >= height * 2;
        }
    });
    // 未找到，放到最后一个印章的下面放不下，则放到最顶部
    if (index === -1) {
        return 1 - height;
    }
    return sortList[index] - height;
}

// 通过canvas动态生成水印图片
export function createWaterMark(textList, width, height) {
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext('2d');
    ctx.fillStyle = '#999';
    ctx.font = 'normal 10px Arial';
    ctx.translate(0, 100);
    ctx.rotate(-30 * Math.PI / 180);
    textList.forEach((text, index) => {
        ctx.fillText(text, 0, 20 * (index + 1));
    });
    return canvas.toDataURL();
}

// 初始化水印
export function initWatermark(watermarkList, receivers) {
    return  watermarkList.map(watermark => {
        let watermarkText = watermark.watermarkText;
        const receiver = findReceiver(watermark.receiverId, receivers);
        if (!watermarkText) { // 编辑模板中，不记录watermarkText，需要前端特殊处理
            watermarkText = factoryWatermarkText(receiver);
        }
        return {
            ...watermark,
            watermarkText,
            showName: receiver.showName,
        };
    });
}

// 初始化骑缝章
export function initRidingSeal(ridingSeals, receivers) {
    return ridingSeals.map(ridingSeal => {
        return {
            ...ridingSeal,
            showName: findReceiver(ridingSeal.receiverId, receivers).showName,
        };
    });
}

export function findReceiver(receiverId, receivers) {
    return receivers.find(item => item.receiverId === receiverId);
}
