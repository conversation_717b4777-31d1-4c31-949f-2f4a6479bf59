// map px to pt
export const convertPxToPt = (px, dpi) => {
    return px * (72 / dpi);
};

// map pt to px
export const convertPtToPx = (pt, dpi) => {
    return pt * (dpi / 72);
};

// 字号和pt映射关系（非标准）
export const fontSizeRange = [
    {
        label: '一号',
        pt: 27,
    }, {
        label: '小一号',
        pt: 24,
    }, {
        label: '二号',
        pt: 21,
    }, {
        label: '小二号',
        pt: 18,
    }, {
        label: '三号',
        pt: 15.75,
    }, {
        label: '小三号',
        pt: 15,
    }, {
        label: '四号',
        pt: 13.5,
    }, {
        label: '小四号',
        pt: 12,
    }, {
        label: '五号',
        pt: 10.5,
    }, {
        label: '小五号',
        pt: 9,
    },
];
// 字号和pt映射关系（动态模版--永中）
export const fontSizeRangeYZ = [
    {
        label: '初号',
        pt: 31.5,

    }, {
        label: '小初',
        pt: 27,

    }, {
        label: '一号',
        pt: 19.75,

    }, {
        label: '小一',
        pt: 18,
    }, {
        label: '二号',
        pt: 16.5,
    }, {
        label: '小二',
        pt: 13.5,
    }, {
        label: '三号',
        pt: 12,
    }, {
        label: '小三',
        pt: 11.5,
    }, {
        label: '四号',
        pt: 10.5,
    }, {
        label: '小四',
        pt: 9,
    },
];
