/* eslint-disable no-undef */
/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17-11-22.   和原生交互的方法
 * update by <PERSON><PERSON><PERSON> in 2018/11/02
 */
import { isAndroid, isIOS } from './device.js';

var sysPlatform;
if (isIOS) {
    sysPlatform = 'IOS';
} else if (isAndroid) {
    sysPlatform = 'ANDROID';
}

function connectWebViewJavascriptBridgeIOS(callback) {
    if (window.WebViewJavascriptBridge) {
        return callback(WebViewJavascriptBridge);
    }
    if (window.WVJBCallbacks) {
        return window.WVJBCallbacks.push(callback);
    }
    window.WVJBCallbacks = [callback];
    var WVJBIframe = document.createElement('iframe');
    WVJBIframe.style.display = 'none';
    WVJBIframe.src = 'ssqscheme://__BRIDGE_LOADED__';
    document.documentElement.appendChild(WVJBIframe);
    setTimeout(function() {
        document.documentElement.removeChild(WVJBIframe);
    }, 0);
}

function connectWebViewJavascriptBridgeANDROID(callback) {
    if (window.WebViewJavascriptBridge) {
        callback(WebViewJavascriptBridge);
    } else {
        document.addEventListener(
            'WebViewJavascriptBridgeReady', function() {
                callback(WebViewJavascriptBridge);
            },
            false,
        );
    }
}

function initBridgetFunc(bridge) {
    window.ssqBridge = bridge;
    if (afterBridge && afterBridge.length > 0) {
        for (var i = 0; i < afterBridge.length; i++) {
            afterBridge[i].call();
        }
    }
}

if (sysPlatform === 'IOS') {
    connectWebViewJavascriptBridgeIOS(function(bridge) {
        initBridgetFunc(bridge);
    });
} else if (sysPlatform === 'ANDROID') {
    connectWebViewJavascriptBridgeANDROID(function(bridge) {
        initBridgetFunc(bridge);
    });
}

var afterBridge = [];

var ssqBridgeObj = {
    afterReady: function(callback) {
        if (window.ssqBridge) {
            callback();
        } else {
            afterBridge.push(callback);
        }
    },
    // h5 注册方法
    registerCallback: function(callback) {
        this.afterReady(function() {
            ssqBridge && ssqBridge.registerHandler('useH5Function', function(data) {
                var dataObj = JSON.parse(data);
                callback(dataObj);
            });
        });
    },
    inAppSdk() {
        return new Promise((resolve) => {
            // 先调用APP的方法
            this.afterReady(function() {
                var dataJSON = {
                    functionName: 'inAppSdk',
                    data: {},
                };
                ssqBridge.callHandler('useNativeFunction', JSON.stringify(dataJSON), function() {});
            });
            this.registerCallback(function(res) {
                if (res.functionName === 'inAppSdk') {
                    resolve(res.data.isInAppSdk);
                }
            });
        });
    },
    /**
     * 跳到APP微众刷脸
     * @param  {Object} 刷脸需要传给APP的参数
     */
    toAppWeiZhong(params) {
        this.afterReady(function() {
            var dataJSON = {
                functionName: 'weizhongFacePerception',
                data: params || {},
            };
            ssqBridge.callHandler('useNativeFunction', JSON.stringify(dataJSON), function() {
            });
        });
    },
    /**
     * 退出APPSDK
     * @return {[type]} [description]
     */
    exitAppSDK(params) {
        this.afterReady(function() {
            var dataJSON = {
                functionName: 'processResultCallback',
                data: params || { 'result': '1', 'close': '1' },
            };
            ssqBridge.callHandler('useNativeFunction', JSON.stringify(dataJSON), function() {
            });
        });
    },
};
export default ssqBridgeObj;
