const SPECIAL_CHARS_REGEXP = /([\:\-\_]+(.))/g;
const MOZ_HACK_REGEXP = /^moz([A-Z])/;
const ieVersion = Number(document.documentMode);

/* istanbul ignore next */
const trim = function(string) {
    return (string || '').replace(/^[\s\uFEFF]+|[\s\uFEFF]+$/g, '');
};
/* eslint-disable */
const camelCase = function(name) {
  return name.replace(SPECIAL_CHARS_REGEXP, function(_, separator, letter, offset) {
    return offset ? letter.toUpperCase() : letter;
  }).replace(MOZ_HACK_REGEXP, 'Moz$1');
};

export const isUndefined = (val) => {
  return val === void(0);
};

/* istanbul ignore next */
export const on = (function() {
  if (document.addEventListener) {
    return function(element, event, handler) {
      if (element && event && handler) {
        element.addEventListener(event, handler, false);
      }
    };
  } else {
    return function(element, event, handler) {
      if (element && event && handler) {
        element.attachEvent('on' + event, handler);
      }
    };
  }
})();

/* istanbul ignore next */
export const off = (function() {
  if (document.removeEventListener) {
    return function(element, event, handler) {
      if (element && event) {
        element.removeEventListener(event, handler, false);
      }
    };
  } else {
    return function(element, event, handler) {
      if (element && event) {
        element.detachEvent('on' + event, handler);
      }
    };
  }
})();

/* istanbul ignore next */
export const once = function(el, event, fn) {
  var listener = function() {
    if (fn) {
      fn.apply(this, arguments);
    }
    off(el, event, listener);
  };
  on(el, event, listener);
};

/* istanbul ignore next */
export function hasClass(el, cls) {
  if (!el || !cls) return false;
  if (cls.indexOf(' ') !== -1) throw new Error('className should not contain space.');
  if (el.classList) {
    return el.classList.contains(cls);
  } else {
    return (' ' + el.className + ' ').indexOf(' ' + cls + ' ') > -1;
  }
};

/* istanbul ignore next */
export function addClass(el, cls) {
  if (!el) return;
  var curClass = el.className;
  var classes = (cls || '').split(' ');

  for (var i = 0, j = classes.length; i < j; i++) {
    var clsName = classes[i];
    if (!clsName) continue;

    if (el.classList) {
      el.classList.add(clsName);
    } else {
      if (!hasClass(el, clsName)) {
        curClass += ' ' + clsName;
      }
    }
  }
  if (!el.classList) {
    el.className = curClass;
  }
};

/* istanbul ignore next */
export function removeClass(el, cls) {
  if (!el || !cls) return;
  var classes = cls.split(' ');
  var curClass = ' ' + el.className + ' ';

  for (var i = 0, j = classes.length; i < j; i++) {
    var clsName = classes[i];
    if (!clsName) continue;

    if (el.classList) {
      el.classList.remove(clsName);
    } else {
      if (hasClass(el, clsName)) {
        curClass = curClass.replace(' ' + clsName + ' ', ' ');
      }
    }
  }
  if (!el.classList) {
    el.className = trim(curClass);
  }
};

/* istanbul ignore next */
export const getStyle = ieVersion < 9 ? function(element, styleName) {
  if (!element || !styleName) return null;
  styleName = camelCase(styleName);
  if (styleName === 'float') {
    styleName = 'styleFloat';
  }
  try {
    switch (styleName) {
      case 'opacity':
        try {
          return element.filters.item('alpha').opacity / 100;
        } catch (e) {
          return 1.0;
        }
      default:
        return (element.style[styleName] || element.currentStyle ? element.currentStyle[styleName] : null);
    }
  } catch (e) {
    return element.style[styleName];
  }
} : function(element, styleName) {
  if (!element || !styleName) return null;
  styleName = camelCase(styleName);
  if (styleName === 'float') {
    styleName = 'cssFloat';
  }
  try {
    var computed = document.defaultView.getComputedStyle(element, '');
    return element.style[styleName] || computed ? computed[styleName] : null;
  } catch (e) {
    return element.style[styleName];
  }
};

/**
 * @description  根据样式，返回离当前元素最近的符合条件的父元素
 * @todo  目前只支持单个样式检索，可以改进为多个样式检索
 * @param  {Dom Element} element   [当前元素]
 * @param  {String} styleName [样式名]
 * @param  {String} value     [样式值]
 * @return {Promise}           [closest dom element]
 */
export function closestParent(element, styleName, value) {
    let el = element;

    return new Promise((resolve, reject) => {
        function step() {
            if (el === document.body) {
                return el;
            }

            if (getStyle(el, styleName) !== value) {
                el = el.parentElement;
                step();
            } else {
                resolve(el);
            }
        }
        step();
    })
}

/* istanbul ignore next */
export function setStyle(element, styleName, value) {
  if (!element || !styleName) return;

  if (typeof styleName === 'object') {
    for (var prop in styleName) {
      if (styleName.hasOwnProperty(prop)) {
        setStyle(element, prop, styleName[prop]);
      }
    }
  } else {
    styleName = camelCase(styleName);
    if (styleName === 'opacity' && ieVersion < 9) {
      element.style.filter = isNaN(value) ? '' : 'alpha(opacity=' + value * 100 + ')';
    } else {
      element.style[styleName] = value;
    }
  }
};

export function insertAfter(newNode, referenceNode) {
  referenceNode.parentNode.insertBefore(newNode, referenceNode.nextSibling);
}

export function parseDom(arg) {
　　 var objE = document.createElement("div");
　　 objE.innerHTML = arg;
　　 return objE.childNodes[0];
};

export function find(el, nodeName) {
    let inputEl;
    if (el.nodeName === nodeName) {
        inputEl = el;
    } else {
        Array.prototype.some.call(el.children, function(child) {
            return (child.nodeName === nodeName) && (inputEl = child)
        });
    }
    return inputEl;
}

export function toggleClass(el, className) {
    if (hasClass(el, className)) {
      removeClass(el, className)
    } else {
      addClass(el, className);
    }
}

export function scrollToY(el, y) {
  if (el.scrollTo) {
    el.scrollTo({
      "behavior": "smooth",
      "top": y
    });
  } else {
    el.scrollTop = y;
  }
}

export function smoothScroll(el, y) {
  let i = y || 0;
  if (i < 200) {
    setTimeout(() => {
      el.scrollTo(0, i);
      smoothScroll(i + 10);
    }, 10);
  }
}

const TIMINGFUNC_MAP = {
  "linear": t => t,
  "ease-in": t => t * t,
  "ease-out": t => t * ( 2 - t ),
  "ease-in-out": t => ( t < .5 ? 2 * t * t : -1 + ( 4 - 2 * t ) * t )
};

/**
* Scroll from elY to targetY
* @param {number} targetY - target scroll Y
* @param {number} duration - transition duration
* @param {string} timingName - timing function name. Can be one of linear, ease-in, ease-out, ease-in-out
*/
export function scrollToYSmooth( el, targetY, duration = 300, timingName = "linear" ) {
  let callback;
  if (typeof duration === 'function') {
    callback = duration;
    duration = 300;
  }
      
  const timingFunc = TIMINGFUNC_MAP[ timingName ];
  let $domInitY = el.scrollTop;
  let start = null;
  const step = ( timestamp ) => {
    start = start || timestamp;
    const progress = timestamp - start,
          // Growing from 0 to 1
          time = Math.min( 1, ( ( timestamp - start ) / duration ) );

    let y = ( $domInitY + timingFunc( time ) * ( targetY - $domInitY ) );
    if (el.scrollTo) {
      el.scrollTo( 0, y );
    } else {
      el.scrollTop = y;
    }
    if (y >= targetY) {
        // scroll-end-callback
        callback && callback();
    }

    if ( progress < duration ) {
      window.requestAnimationFrame( step );
    }
  };

  window.requestAnimationFrame( step );
}

/**
 * @description  获取元素位置坐标
 * @param  {Dom Element} element   [当前元素]
 * @return {Object}     [相对位置]
 */
export function getElemOffset(elem) {
    return {
        offsetTop: elem.offsetParent ? elem.offsetTop + getElemOffset(elem.offsetParent).offsetTop : elem.offsetTop,
        offsetLeft: elem.offsetParent ? elem.offsetLeft + getElemOffset(elem.offsetParent).offsetLeft : elem.offsetLeft,
    }
}

export const htmlentities = {
    encode(str) {
        return String(str).replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;');
    },
    decode(str) {
        let div = document.createElement('div');
        div.innerHTML = str;
        str = div.innerText || div.textContent;
        div = null;
        return str;
    }
};
