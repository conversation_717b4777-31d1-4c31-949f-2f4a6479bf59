// 定义一些公共的常量
export const signCodeStatusMap = {
    '130536': -1, // 短链接中转页用到
    '130525': 1, // 合同撤回
    '130534': 2, // 合同拒签
    '130526': 3, // 合同过期
    '130529': 4, // 审批终止
    '130523': 5, // 审批完成
    '130527': 6,  // 合同完成状态,已签署成功-当前用户不允许签署操作
    '130513': 6,  // 合同签署中-当前用户已完成签署，不允许签署操作
    '020213': 7, // 短链接已失效
    '130528': 10, // 合同正在审批
    '130542': 11, // 已申请他人盖章
    '130512': 12,  // 没有签署权限或登录身份已过期
    '130013': 14, // 内部决议合同，已无需进行签署
};

export const signDecisionStatusMap = {
    'NORMAL': 1,                // 满足签署条件
    'NOT_AUTHENTICATE': 2,      // 需要实名但未实名认证
    'SIMPLE_AUTHENTICATE': 2,   // 可以进行简单（刷脸）实名，但未实名
    'MORE_AUTHENTICATE': 3,     // 需要补充实名
    'USE_MY_IDENTITY': 4,       // 发起人填的签约主体不在我的签约主体范围内
    'USE_DEFAULT_IDENTITY': 5,  // 未实名使用默认'未实名'身份签署
    'NOT_ADD': 6,                // 不是发起方指定的签约主体公司成员
};
