import { MUL_PROXY_SIGN_URL, NOT_MUL_PROXY_SIGN_URL } from 'src/common/const/mulProxySignUrl.js';
import { getQueryString } from 'src/common/utils/getQueryString.js';
export function addMulProxySignParam(config = {}) {
    let handledConfig = { ...config };
    // 多方代理签增加接口请求参数
    const mulProxySignParam = location.search.includes('proxySignNominalEntId') && getQueryString('proxySignNominalEntId', location.href);
    const canAddMulProxySignParam = MUL_PROXY_SIGN_URL.some(item => handledConfig.url.includes(item)) && (!NOT_MUL_PROXY_SIGN_URL.some(item => handledConfig.url.includes(item)));
    if (canAddMulProxySignParam && mulProxySignParam) {
        if (handledConfig.method === 'get') {
            handledConfig.url += `${handledConfig.url.indexOf('?') > -1 ? '&' : '?'}proxySignNominalEntId=${mulProxySignParam}`;
        } else if (handledConfig.method === 'post') {
            handledConfig = {
                ...handledConfig,
                data: {
                    proxySignNominalEntId: mulProxySignParam,
                    ...handledConfig.data,
                },
            };
        }
    }
    return handledConfig;
}
