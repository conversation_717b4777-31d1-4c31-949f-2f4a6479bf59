export function formatDateToTimestamp(date) {
    // data: 2017-11-23 17:02
    return new Date(date) / 1000;
}
/**
 * 日期格式化
 * @param option {date, format : 'YYYY-MM-DD hh:mm:ss'} 间隔符可以传任意符号
 * 举个栗子 formatDateToString({
                date: 1514390400000,
                format: 'YYYY.MM.DD'
            })
 * @return format
 */
export function formatDateToString(option) {
    const date = option.date ? new Date(option.date) : new Date();
    let format = option.format || 'YYYY-MM-DD hh:mm:ss';

    const obj = {
        'M+': date.getMonth() + 1, // 月份
        'D+': date.getDate(), // 日
        'h+': date.getHours(), // 小时
        'm+': date.getMinutes(), // 分
        's+': date.getSeconds(), // 秒
    };

    if (/(Y+)/.test(format)) {
        format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
    }

    for (const k in obj) {
        if (new RegExp('(' + k + ')').test(format)) {
            format = format.replace(RegExp.$1, (RegExp.$1.length === 1) ? (obj[k]) : (('00' + obj[k]).substr(('' + obj[k]).length)));
        }
    }

    return format;
}

/**
 * 返回两个日期间隔天数
 * @param  {[string]} beginDate [timeStamp]
 * @param  {[string]} endDate   [timeStamp]
 * @return {[number]}
 */
export function dateInterval(beginDate, endDate) {
    // 为空时取当前日期
    const newDate = Date.parse(new Date());
    beginDate = beginDate || newDate;
    endDate = endDate || newDate;

    // 假如时光倒流，交换下起始时间
    const isTimeBack = endDate < beginDate;
    const beginDateCache = isTimeBack ? endDate : beginDate;
    const endDateCache = isTimeBack ? beginDate : endDate;

    const begin = new Date(beginDateCache);
    const end = new Date(endDateCache);

    const beginYear = begin.getFullYear();
    const endYear = end.getFullYear();

    const beginMonth = begin.getMonth();
    const beginDay = begin.getDate();

    const endMonth = end.getMonth();
    const endDay = end.getDate();

    const cacheBegin = begin; // 当前月份的最后一天
    const cacheBegin2 = begin; // 缓存月份最后一天，在循环中增加月份

    cacheBegin.setMonth(beginMonth + 1, 1);
    cacheBegin.setDate(0);

    // 开始月剩余天数 + 最后一月已过的天数
    let days = (cacheBegin.getDate() - beginDay + 1) + endDay;
    let interval = 0;

    // 跨年
    if (endYear > beginYear) {
        interval = (endYear - beginYear) * 12 + -(beginMonth - endMonth);
    } else {
        interval = endMonth - beginMonth;

        if (endMonth === beginMonth) {
            days = endDay - beginDay + 1;
        }
    }

    for (let i = 1; i < interval; i++) {
        // 先将日期设为下下个月的第一天，再 setDate(0) 取最大天数
        cacheBegin2.setMonth(cacheBegin.getMonth() + 2, 1);
        cacheBegin2.setDate(0);

        // 循环中间的月份，取出每个月的天数
        days += cacheBegin2.getDate();
    }

    return days;
}
/**
 * 返回时间类型  今天  昨天  今年  今年之前
 * @param  {[string]} date [timeStamp]
 * @return {[number]} 0 1 2 3
 */
export function dateType(dateString) {
    const nowDate = new Date();
    const nowYear = nowDate.getFullYear();
    const nowMonth = nowDate.getMonth();
    const nowDay = nowDate.getDate();
    const yesterday = new Date(nowDate - 1000 * 60 * 60 * 24).getDate();

    const thisDate = new Date(dateString);
    const thisYear = thisDate.getFullYear();
    const thisMonth = thisDate.getMonth();
    const thisDay = thisDate.getDate();

    if (nowYear !== thisYear) {
        return 3;
    } else if (nowMonth === thisMonth && nowDay === thisDay) {
        return 0;
    } else if (nowMonth === thisMonth && yesterday === thisDay) {
        return 1;
    } else {
        return 2;
    }
}
