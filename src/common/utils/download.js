/*
 * @Author: fan_liu
 * @Date: 2020-11-23 17:36:38
 * @LastEditors: fan_liu
 * @LastEditTime: 2021-03-11 14:21:23
 * @Description: 合同文件下载相关方法
 */
import store from 'src/store/store.js';
import { formatDateToString } from 'utils/date.js';
import { getQueryString } from 'utils/getQueryString.js';
import { randomWord } from 'utils/randomWord.js';
function invokeDownloadHelper(url, isContractDownload = true) {
    // 乐高城开启了合同下载加密
    if (store.state.features.includes('88') && isContractDownload) {
        const rondomPwd = randomWord(false, 6);
        const fileName = `合同${formatDateToString({
            date: new Date(),
            format: 'YYYYMMDDhhmmss',
        })}`;
        const isHybridDownload = url.includes('hybrid');
        if (isHybridDownload) {
            url = handleHybridUrl(url, rondomPwd, fileName);
        } else {
            url += `${url.includes('?') ? '&' : '?'}unzipCode=${rondomPwd}&zipName=${encodeURIComponent(fileName)}`;
        }
        store.state.downloadFileName = fileName;
        store.state.downloadFilePassword = rondomPwd;
        Vue.$downloadPwdDialog();
    }
    // console.log(url);
    download(url);
}
// 处理混合云下载url
function handleHybridUrl(url, rondomPwd, fileName) {
    // params参数里面添加unzipCode和zipName字段
    const params = JSON.parse(getQueryString('params', url));
    params.unzipCode = rondomPwd;
    params.zipName = fileName;
    // eslint-disable-next-line no-eval
    const re = eval('/(params=)([^&]*)/gi');
    url = url.replace(re, `params=${encodeURIComponent(JSON.stringify(params))}`);
    return url;
}
// 所有执行下载的操作都在这个函数里
// (若被调用时是在异步请求后，window.open 在某些浏览器中会存在兼容问题）
function download(url) {
    const $el = document.querySelector('#J_download-link') || document.createElement('a');
    $el.setAttribute('id', 'J_download-link');
    $el.setAttribute('style', 'visibility:hidden;height:0;width:0;');
    $el.setAttribute('target', '_self');
    $el.setAttribute('href', url);
    document.body.appendChild($el);
    $el.click();
}

const COMMON_DOWNLOAD_PATH = '/contract-export/contracts/documents/download';
// 获取公有云的下载路径
function getPublicDownLoadUrl(contractIds) {
    return encodeURI(`${COMMON_DOWNLOAD_PATH}?contractIds=${contractIds}&access_token=${Vue.$cookie.get('access_token')}`);
}
// 根据合同Id列表下载公有云合同
function downLoadPublicContract(contractIds) {
    const url = getPublicDownLoadUrl(contractIds);
    invokeDownloadHelper(url);
}

export {
    invokeDownloadHelper,
    getPublicDownLoadUrl,
    downLoadPublicContract,
    download,
};
