/**
 * @return {String}
 * @description 当前是建行还是其他
 */
export function getHOSTENV() {
    let HOST_ENV = 'official';
    if (
        process.env.NODE_ENV === 'development-ccb' ||
        location.host === 'ccb.bestsign.cn' ||
        location.host === 'ccb.bestsign.info'
    ) {
        HOST_ENV = 'CCB';
    }
    return HOST_ENV;
}

/**
 * @description 当前是正式环境还是预发布环境(之后如果需要更详细的判断dev环境等，可以在后面加别的判断条件)
 */
export function getPresentEnv() {
    // 方式一：通过域名来判断当前环境，暂不支持客户定制多域名
    const hostName = window.location.host;
    let ENV_NAME = 'PE_ENV';
    if (hostName.includes('.info')) {
        ENV_NAME = 'PRE_ENV';
    }

    // 方式二：通过build命令判断
    // let ENV_NAME = 'PRE_ENV';
    // if (process.env.NODE_ENV === "production") {
    //     ENV_NAME = 'PE_ENV';
    // }

    return ENV_NAME;
}

const preHostSuffixList = ['.info', '.tech'];
export function isPreEnv() {
    return preHostSuffixList.some(item => window.location.host.includes(item));
}
