/**
 * userStatus:
 * 0：已注册未激活；1：已激活，正常状态；2：已作废或已注销；3：已激活暂时锁定；如密码输入三次错误后。4：账户冻结；系统维护。5：未注册未激活；
 */

import store from 'src/store/store.js';
export const hasPrivilege = (privilegeName) => {
    const commonHeaderInfo = store.state.commonHeaderInfo;
    const roleDetails = commonHeaderInfo.roleDetails || [];
    let hasPrivilege = false;
    let privileges = [];
    roleDetails.forEach(item => {
        privileges = privileges.concat(item.privileges);
    });
    hasPrivilege = privileges.some(item => {
        return item.name === privilegeName;
    });

    return hasPrivilege;
};
