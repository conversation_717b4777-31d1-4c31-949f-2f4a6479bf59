import { labelInfo } from './labelStyle.js';
/**
 * @param  {Object} button 复选框，单选框按钮
 * @param  {Number} pageWidth 页高
 * @param  {Number} pageHeight 页宽
 * @return {Object}  返回复选框，单选框按钮以标签左下角为原点的直角坐标系在第一象限的百分比(基于页面宽高)坐标值x,y,按钮宽高占页面宽高的百分比width,height
 * @desc  标签坐标转换，换成百分比传给服务端
 */
export function labelCoordinateTransform(label, pageWidth, pageHeight) { // 新建坐标的数值转为百分比
    // fix CFD-4940: 签章位置为0校验
    const labelPosition = label.labelPosition;
    const labelExtends = label.labelExtends;
    const labelType = label.labelType;
    const theLabelInfo = labelInfo(labelType);
    const labelHeight = labelPosition.height || theLabelInfo.height;
    const labelWidth = labelPosition.width || theLabelInfo.width;
    labelPosition.x = (labelPosition.x) / pageWidth;
    labelPosition.y = 1 - (labelPosition.y + Math.round(labelHeight)) / pageHeight;
    labelPosition.width = Math.round(labelWidth) / pageWidth;
    labelPosition.height = Math.round(labelHeight) / pageHeight;
    // 有时候计算结果会为负
    if (labelPosition.y < 0) {
        labelPosition.y = 0;
    }
    if (labelPosition.x < 0) {
        labelPosition.x = 0;
    }
    if (['MULTIPLE_BOX', 'SINGLE_BOX', 'CONFIRMATION_REQUEST_SEAL'].includes(labelType)) {
        const labelHeight =  theLabelInfo.button.height;
        labelExtends.items.forEach(item => {
            item.itemX = item.itemX / pageWidth;
            item.itemY = labelPosition.height - (item.itemY + labelHeight) / pageHeight;
        });
    }
    return { labelPosition, labelExtends };
}

// 数值转换为百分比(老坐标的计算)
export function labelCoordinateReversal(label, pageWidth, pageHeight) {
    const labelPosition = label.labelPosition;
    // 标签宽高如果是百分比的转换为整数数值
    labelPosition.width = labelPosition.width <= 1 ? labelPosition.width : labelPosition.width / pageWidth;
    labelPosition.height = labelPosition.height <= 1 ?  labelPosition.height : labelPosition.height / pageHeight;

    // 旧数据坐标值还是数值需要转换为百分比
    labelPosition.x = labelPosition.x < 1 ? labelPosition.x : labelPosition.x / pageWidth;
    labelPosition.y = labelPosition.y < 1 ? labelPosition.y : (1 - labelPosition.y / pageHeight);
    return labelPosition;
}
