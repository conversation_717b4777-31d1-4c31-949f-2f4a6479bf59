export function swapArray(ary, index1, index2) {
    ary[index1] = ary.splice(index2, 1, ary[index1])[0];
    return ary;
}

export function uniqBy(ary, ...keys) {
    const hash = {};
    const repeat = [];
    ary = ary.reduce(function(item, next) {
        let key = '';
        const randomNum = (Math.random() * 10).toFixed(2);
        keys.forEach(item => {
            key += (next[item] === '0' || next[item] === '' ? randomNum : next[item]);
        });

        hash[key] && key ? (repeat.push(next)) : hash[key] = true && item.push(next);
        return item;
    }, []);
    return {
        res: ary,
        repeat,
    };
}
