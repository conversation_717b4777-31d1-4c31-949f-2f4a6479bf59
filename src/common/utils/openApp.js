import { MessageBox } from 'element-ui';
import i18n from 'src/lang';

function getAttributeState(attribute) {
    const prefixes = ['webkit', 'moz', 'ms', 'o'];
    if (attribute in document) {
        return attribute;
    }
    for (let i = 0; i < prefixes.length; i++) {
        const temp = prefixes[i] + attribute[0].toLocaleUpperCase() + attribute.slice(1);
        if (temp in document) {
            return temp;
        }
    }
    return null;
}
export const visibilityChange = (function() {
    const hidden = getAttributeState('hidden');
    return hidden === 'hidden' ? 'visibilitychange' : hidden.replace(/Hidden/, 'Visibilitychange');
})();

export const visibilityState = (function() {
    return getAttributeState('visibilityState');
})();

export function openApp(openUrl, failFn) {
    const hidden = getAttributeState('hidden');
    let isLeave = false;
    let isLeaveAfterCome = false;

    location.href = openUrl;

    const visibilitychange = hidden === 'hidden' ? 'visibilitychange' : hidden.replace(/Hidden/, 'Visibilitychange');
    const visibilityState = getAttributeState('visibilityState');
    document.addEventListener(visibilitychange, () => {
        if (document[visibilityState] === 'visible' && localStorage.getItem('hidden')) {
            // 如果前往支付宝刷脸成功，然后回到浏览器页面
            localStorage.removeItem('hidden');
            isLeaveAfterCome = true;
        } else {
            // 页面变成隐藏，说明用户可能去支付宝刷脸
            isLeave = true;
            localStorage.setItem('hidden', 1);
        }
    });
    // 5秒之后如果用户没有操作，给用户一个确认的弹窗，引导去阿里云刷脸
    setTimeout(() => {
        if (isLeave && isLeaveAfterCome) {
            return false;
        }
        MessageBox.confirm(i18n.t('utils.faceVerified'), '', {
            showClose: false,
            cancelButtonText: i18n.t('utils.unDone'),
            confirmButtonText: i18n.t('utils.done'),
        }).then(() => {
            return;
        }).catch(() => {
            MessageBox.alert(i18n.t('utils.changeVerifyMethod')).then(() => {
                failFn();
            });
        });
    }, 5000);
}
