// 模版中骑缝章宽高，签署时骑缝章容器宽度160，保证宽度100%
export const DEFAULT_RS_HEIGHT = 160;
// 除与合同重叠部分以外，骑缝章宽度/阴影背景部分宽度
export const RIDESEAL_SHADOW_WIDTH = 120;

// 编辑模版中需要根据角色信息决定水印的展示
export function factoryWatermarkText({ roleName, userType, userAccount, userName, enterpriseName, userNameNoDesensitization }) {
    if (userType === 'PERSON') { // 个人账号
        if (userAccount) {
            return `${userAccount}${userName && userNameNoDesensitization ? ('(' + userNameNoDesensitization + ')') : ''}`;
        } // userNameNoDesensitization：水印姓名不脱敏
        return `${roleName}(发送合同后自动替换为真实信息)`;
    }
    if (userType === 'ENTERPRISE') { // 企业账号
        return enterpriseName || userAccount || `${roleName}(发送合同后自动替换为真实信息)`;
    }
}

/**
 * @param  {Array} data 签署人信息
 * @param  {Number} pageHeight 页高
 * @param  {Boolean} inTemplate 是否是模版中的应用
 * @return {Object}  watermarkList 水印信息，ridingSealList骑缝章信息
 * @desc  组装后台返回的合同装饰数据结构
 */
export function initDecorateInfo(data, pageHeight, inTemplate) {
    let watermarkList = [];
    let ridingSealList = [];
    data.forEach(item => {
        const { watermarkVos, decorateRidingSealVos } = item.decorateInfo || {};
        watermarkList = watermarkList.concat(watermarkVos.map(watermark => {
            let watermarkText = watermark.watermarkText;
            if (watermarkText === undefined) { // 编辑模版中，不记录watermarkText，需要前端特殊处理
                watermarkText = factoryWatermarkText(item);
            }
            return {
                ...watermark,
                roleName: inTemplate ? item.showName : item.userName, // 增加roleName， 模版中用的是showName，本地发起时用userName
                watermarkText,
            };
        }));
        ridingSealList = ridingSealList.concat(decorateRidingSealVos.map(rideSeal => {
            return {
                roleName: inTemplate ? item.showName : item.userName, // 增加roleName， 模版中用的是showName，本地发起时用userName
                ...rideSeal,
                y: inTemplate ? (1 - rideSeal.y) * pageHeight - DEFAULT_RS_HEIGHT : rideSeal.y, // 本地发起不需要转换绝对值
            };
        }));
    });

    return {
        watermarkList,
        ridingSealList,
    };
}

// 根据现有骑缝章位置计算新增骑缝章的位置, 位置最少间隔DEFAULT_RS_HEIGHT
export function calcRidingSeal(ridingSealList, pageHeight) {
    if (!ridingSealList.length) { // 如果为第一个骑缝章，则放在顶部
        return 0;
    }
    if (ridingSealList.length === 1) { // 第二个骑缝章：根据第一个位置决定放在其前面还是后面
        return ridingSealList[0].y > DEFAULT_RS_HEIGHT ? 0 : ridingSealList[0].y + DEFAULT_RS_HEIGHT;
    }
    const yPosArr = ridingSealList.map(rs => rs.y).sort(); // 存下骑缝章坐标值并排序
    let y = -1;
    yPosArr.every((val, index) => {
        if (index === 0 && val > DEFAULT_RS_HEIGHT) { // 如果据顶部最近的骑缝章y坐标大于一个骑缝章高度，则放在顶部
            y = 0;
            return false;
        }
        if (+index === yPosArr.length - 1 && val + DEFAULT_RS_HEIGHT * 2 > pageHeight) {
            y = val + DEFAULT_RS_HEIGHT * 2 - pageHeight; // 超出时，重制
            return false;
        }
        if (yPosArr[index + 1] - val > DEFAULT_RS_HEIGHT * 2) { // 如果两个骑缝章之间的距离大于一个骑缝章，则放在第一个的后面
            y = val + DEFAULT_RS_HEIGHT;
            return false;
        }
        return true;
    });
    if (y === -1) { // 如果以上不满足则放在最后一个骑缝章的后面
        y = yPosArr[yPosArr.length - 1] + DEFAULT_RS_HEIGHT;
    }
    if (y + DEFAULT_RS_HEIGHT > pageHeight) { // 如果坐标超出高度则从顶部重新摆放
        y = y + DEFAULT_RS_HEIGHT - pageHeight; // 超出时，重制
    }

    return y;
}

// 计算插入骑缝章的位置，返回y轴相对坐标
export function calcRideSeal(ridingSealList, pageHeight) {
    // 默认占的百分比
    const height = DEFAULT_RS_HEIGHT / pageHeight;
    // 如果为第一个骑缝章，则放在顶部
    if (!ridingSealList.length) {
        return 1 - height;
    }
    // 从上到下排序
    const sortList = ridingSealList.map(rs => rs.y).sort((a, b) => b - a);
    // 在顶部插入一个顶部坐标为1的元素方便计算
    sortList.unshift(1);
    const index = sortList.findIndex((val, i) => {
        const v1 = val;
        let v2;
        if (i === sortList.length - 1) { // 最后一个的话，height就可以了
            v2 = 0; // 起始的最低坐标点为0
            return v1 - v2 >= height;
        } else {
            v2 = sortList[i + 1];
            return v1 - v2 >= height * 2;
        }
    });
    // 未找到，放到最后一个印章的下面放不下，则放到最顶部
    if (index === -1) {
        return 1 - height;
    }
    return sortList[index] - height;
}

// 通过canvas动态生成水印图片
export function createWaterMark(textList, width, height) {
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext('2d');
    ctx.fillStyle = '#999';
    ctx.font = 'normal 10px Arial';
    ctx.translate(0, 100);
    ctx.rotate(-30 * Math.PI / 180);
    textList.forEach((text, index) => {
        ctx.fillText(text, 0, 20 * (index + 1));
    });
    return canvas.toDataURL();
}
