import * as pdfjsLib from 'pdfjs-dist/build/pdf.js';
import  workerSrc from 'pdfjs-dist/build/pdf.worker.entry.js';
import 'pdfjs-dist/web/pdf_viewer.css';
import 'pdfjs-dist/web/pdf_viewer.js';

pdfjsLib.workerSrc = workerSrc;
pdfjsLib.cMapPacked = true;

// 高清渲染需要的参数
const CSS_UNITS = 96.0 / 72.0;

export default {
    // 循环加载PDF文档
    async loadPDF(docList) {
        let index = 0;
        while (index < docList.length) {
            const doc = docList[index];
            await this.loadPDFDocument(doc, doc.attachmentId || doc.documentId);
            index++;
        }
    },
    // 加载pdf文档
    async loadPDFDocument(url) {
        try {
            const pdfDocument = await pdfjsLib.getDocument(url).promise;
            return pdfDocument;
        } catch (error) {
            console.log('loadPDFDocument-error', error);
            throw error;
        }
    },
    // 渲染每一页
    async renderPDFPage(pdfDocument, pageNum, container, scale = 1) {
        const pdfPage = await pdfDocument.getPage(pageNum);
        const pdfPageView = pdfPage.getViewport({ scale: scale * CSS_UNITS });
        const canvas = container.querySelector('canvas');
        const context = canvas.getContext('2d');

        canvas.height = pdfPageView.height;
        canvas.width = pdfPageView.width;

        const renderContext = {
            canvasContext: context,
            viewport: pdfPageView,
        };
        // 获取原始的pdf文档宽高，兼容后端宽高返回值
        const { width, height } = pdfPage.getViewport({ scale: CSS_UNITS });
        return {
            pdfPage,
            renderContext,
            width,
            height,
            actualWidth: pdfPageView && pdfPageView.width,
            actualHeight: pdfPageView && pdfPageView.height,
        };
    },
    // 导出文档的缩略图
    generateImage(renderContext, width, height, targetWidth, targetHeight) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        let canvasWidth, canvasHeight;
        // 对图片进行压缩
        // 图片比较宽，以高度为scale进行放缩
        if (targetWidth / width > targetHeight / height) {
            canvasWidth = width * (targetHeight / height);
            canvasHeight = targetHeight;
        } else {
            // 图片较高，以宽度为scale进行放缩
            canvasWidth = targetWidth;
            canvasHeight = height * (targetWidth / width);
        }
        canvas.width = canvasWidth * 4;
        canvas.height = canvasHeight * 4;
        const pdfCanvas = renderContext.canvasContext.canvas;
        ctx.drawImage(pdfCanvas, 0, 0, canvas.width, canvas.height);
        return canvas.toDataURL();
    },
};
