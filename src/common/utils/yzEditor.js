/**
 * Created by g<PERSON><PERSON><PERSON> on 20-09-15.   永中编辑器、动态模版相关数据处理方法
 */

/**
 * 永中编辑器能够支持的背景色值，后端同样存有
 */
export const yzRgbColorInfo = [
    '0, 255, 255', // '#00ffff',
    '255, 255, 0', // '#ffff00',
    '0, 128, 128', // '#008080',
    '0, 255, 0', // '#00ff00',
    '128, 128, 128', // '#808080',
    '192, 192, 192', // '#c0c0c0',
];

export const yzBgColorIndex = [3, 7, 10, 4, 15, 16];

export const yzColorInfo = [
    '#00ffff', // 3
    '#ffff00', // 7
    '#008080', // 10
    '#00ff00', // 4
    '#808080', // 15
    '#c0c0c0', // 16
];

export function calcYzHighlightBgndex(index) {
    return yzBgColorIndex.slice(1)[index % 5];
}
export function calcRgbColorByIndex(index) {
    // 将yzRgbColorInfo中第一个默认的蓝色保留给发件方和业务字段颜色使用
    return yzRgbColorInfo.slice(1)[index % 5];
}

export function calcFillColorByIndex(index) {
    return yzColorInfo.slice(1)[index % 5];
}

/**
 * formatAndFilterSSQMark 格式化书签的返回信息并过滤出带有SSQ标记的业务字段书签，
 * 书签接口返回数据：["test1","关于鼓励江苏省软件和信息服务特色产业园","test2","各市经济和信息化主管部门、昆山、泰兴、沭阳经信委（局）","test3"," "]，[key,value,key2,value2, ...]
 * @param markList 永中接口返回的书签数据
 * @returns {array}
 */
export function formatAndFilterSSQMark(markList = []) {
    const ssqMarkList = [];
    for (let i = 0; i < markList.length; i++) {
        if (markList[i] && markList[i].includes('SSQ')) { // 过滤标记为上上签业务字段的书签
            ssqMarkList.push({
                name: markList[i],
                value: markList[i + 1].replace(/{#|#}/g, ''),
            });
        }
        i++; // 两两一组
    }
    console.log('ssqMarkList', ssqMarkList);
    return ssqMarkList;
}

/**
 * postYZDocOpt 根据参数获取编辑器文档的src地址
 * @param params 编辑器配置参数
 * @param methodNum 操作标记：1：开档
 * @returns 请求响应对象
 */
export function postYZDocOpt(methodNum, params) {
    return Vue.$http({
        url: '/yz-editor/api.do',
        method: 'post',
        data: 'jsonParams=' + encodeURIComponent(JSON.stringify({
            method: methodNum,
            params,
        })),
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'X-Content-Type-Options': 'nosniff',
        },
    });
}

/**
 * uploadFileToYZ 永中上传文件接口
 * @param formData 文件参数
 * @returns 请求响应对象
 */
export function uploadFileToYZ(formData) {
    return Vue.$http.post('/yz-editor/uploadFile.do', formData)
        .then(res => {
            console.log('uploadFile', res.data.result.filePath);
            window.localStorage.setItem('filePath', res.data.result.filePath);
            Vue.$http.get(`/yz-dcs/dcs.web/convert?convertType=54&inputDir=${res.data.result.filePath}`)
                .then((res) => {
                    console.log('bookMarkList', formatAndFilterSSQMark(res.data.data[0]));
                });
        });
}
