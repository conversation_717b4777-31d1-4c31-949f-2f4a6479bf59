/*
 * @Author: fan_liu
 * @Date: 2021-01-04 15:33:50
 * @LastEditors: fan_liu
 * @LastEditTime: 2021-02-23 14:23:04
 * @Description: Do not edit
 */
import regRules from './regs.js';
import i18n from 'src/lang';
const {
    userEmail: mailReg,
    userPhone: userPhoneReg,
    entName: entNameReg,
    companyName: companyNameReg,
} = regRules;

export function isPhoneOrMail(str) {
    str = str.trim();
    if (mailReg.test(str)) {
        return 'mail';
    } else if (userPhoneReg.test(str)) {
        return 'phone';
    }
}

export function isComplexPass(str) {
    str = str.trim();
    console.log(str);
    !regRules.complexPass.test(str) && Vue.$MessageToast.error(i18n.t('resetPwd.errorMsg'));
    return regRules.complexPass.test(str);
}

export function getMaskPhoneOrMail(str) {
    if (!str) {
        return '';
    }
    if (mailReg.test(str)) {
        return getMaskEmail(str);
    } else if (userPhoneReg.test(str)) {
        return getMaskPhone(str);
    }
    return str;
}

// 返回脱敏的手机号码
export function getMaskPhone(phone) {
    return phone.replace(regRules.maskPhone, '$1*****$2');
}

// 返回脱敏后的邮箱
export function getMaskEmail(email) {
    const [account, domain] = email.split('@');
    const len = account.length;
    const prefixLen = len > 2 ? 2 : 0;
    const maskStr = Array.from(account.slice(prefixLen)).map(() => '*').join('');
    return account.slice(0, prefixLen) + maskStr + '@' + domain;
}
// 返回脱敏的身份证号码
export function getMaskIdCard(phone) {
    if (phone != null && phone !== undefined && phone !== '') {
        return phone.replace(regRules.maskIDCardReg, '$1 ************ $2');
    }
    return '';
}
// 姓名脱敏
export function getMaskName(str) {
    if (str != null && str !== undefined && str !== '') {
        if (str.length <= 3) {
            return '*' + str.substring(1, str.length);// 三字之内脱敏第一个，
        } else if (str.length >= 4 && str.length <= 6) {
            return '**' + str.substring(2, str.length);// 4-6脱敏前两个
        } else if (str.length > 6) {
            return str.substring(0, 2) + '****'  + str.substring(6, str.length);// 大于6脱敏第3-6个
        }
        return str;
    } else {
        return '';
    }
}
/**
 *
 * @desc 校验 发起页面 企业名称格式
 * @export
 * @param {string} [entName='']
 * @param {boolean} [isCheckEmpty=true]
 * @returns errMsg
 */
export function checkEntNameFormat(entName = '', isCheckEmpty = true/* 是否 校验不能为空的提示*/) {
    entName = entName.trim();
    if (!entName && isCheckEmpty) {
        return i18n.t('addReceiver.noEntNameErr');
    }
    // 企业名称为俄文不报错
    const rupattn = /[\u0400-\u04ff]+/;
    if (!entNameReg.every(i => !i.test(entName)) && !rupattn.test(entName)) {
        return i18n.t('addReceiver.enterpriseNameErr'); // 请填写正确的企业名称
    }
    if (!companyNameReg.test(entName)) {
        return '企业名称不能超过60个字符';
    }
    return ''; // 通过格式校验
}

// 数值精度校验正则
export function numberDecimalReg(n) {
    return n === 0 ? /^-?\d+$/ : new RegExp(`^-?\\d+\(\.\\d{1,${n}})?$`);
}

// 判断是否是ent.bestsign.info环境
export function isEntTestInfoEnv() {
    return window.location.host === 'ent.bestsign.info'; // ent2-k8s.bestsign.info
}
