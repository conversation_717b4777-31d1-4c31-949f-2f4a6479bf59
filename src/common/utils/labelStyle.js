import i18n from 'src/lang';
export const COLOR_TYPES = ['SIGNATURE', 'SEAL', 'DATE'];
export const EXTEND_TYPES = ['TEXT', 'DATE', 'BIZ_DATE', 'TEXT_NUMERIC', 'SINGLE_BOX', 'MULTIPLE_BOX', 'PICTURE', 'CONFIRMATION_REQUEST_SEAL']; // 有扩展字段的表标签

import { rgbColorInfo } from './decorateConst.js';

/**
 * 返回标签信息的常量
 * 指定位置页、模板指定位置页、签署页均有引用
 * @param  {[String]} type [description]
 * @return {[Object]}
 */
export function labelInfo(type) { // 96dpi
    let style = null;
    switch (type) {
        case 'SEAL': // 盖章
            style =  {
                type: 'el-icon-ssq-gaizhang',
                width: 222, // 226
                height: 203, // 211
                name: i18n.t('localCommon.seal'),
            };
            break;
        case 'SIGNATURE': // 签名
            style = {
                type: 'el-icon-ssq-qianming',
                width: 134, // 140
                height: 71, // 74
                name: i18n.t('localCommon.signature'),
            };
            break;
        case 'DATE': // 签署日期
            style = {
                width: 134, // 140
                height: 34, // 35
                name: i18n.t('localCommon.signDate'),
            };
            break;
        case 'TEXT': // 文本
            style = {
                width: 77, // 80
                height: 20, // 20
                name: i18n.t('localCommon.text'),
            };
            break;
        case 'NUMERIC_VALUE': // 数值
            style = {
                width: 77, // 80
                height: 20, // 20
                name: i18n.t('localCommon.text'),
            };
            break;
        case 'BIZ_DATE': // 日期
            style = {
                width: 77, // 80
                height: 20, // 20
                name: i18n.t('localCommon.date'),
            };
            break;
        case 'QR_CODE': // 二维码
            style = {
                width: 110, // 110
                height: 127, // 127
                name: i18n.t('localCommon.qrCode'),
            };
            break;
        case 'TEXT_NUMERIC': // 数字
            style = {
                width: 77, // 110
                height: 22, // 127
                name: i18n.t('localCommon.number'),
            };
            break;
        case 'DYNAMIC_TABLE': // 动态表格
            style = {
                width: 134,
                height: 24,
                name: i18n.t('localCommon.dynamicTable'),
            };
            break;
        case 'TERM': // 合同条款
            style = {
                width: 134,
                height: 24,
                name: i18n.t('localCommon.terms'),
            };
            break;
        case 'MULTIPLE_BOX': // 复选框
            style = {
                width: 46,
                height: 77,
                name: i18n.t('localCommon.checkBox'),
                button: {
                    width: 26,
                    height: 26,
                    split: 5,
                    initSplit: 10,
                },
            };
            break;
        case 'SINGLE_BOX': // 单选框
            style = {
                width: 46,
                height: 77,
                name: i18n.t('localCommon.radioBox'),
                button: {
                    width: 26,
                    height: 26,
                    split: 5,
                    initSplit: 10,
                },
            };
            break;
        case 'PICTURE': { // 图片
            style = {
                width: 65,
                height: 26,
                name: i18n.t('localCommon.image'),
            };
            break;
        }
        // 询证盖章
        case 'CONFIRMATION_REQUEST_SEAL': {
            style = {
                width: 382,
                height: 196,
                name: i18n.t('localCommon.confirmSeal'),
                button: {
                    width: 176,
                    height: 176,
                    split: 10,
                    initSplit: 10,
                },
            };
            break;
        }
        default:
            style = {
                width: 0,
                height: 0,
                name: '',
            };
    }
    return style;
}

/**
 * saas-513
 * 暂时仅在 tempFieldDoc.vue 中使用
 * 用于拖拽自定义标签时，自适应宽高
 * @param  {[Object]} mark [标签对象]
 * @return {[Object]}
 */
export function textLabelInfo(label) {
    if (label.type !== 'TEXT' && label.type !== 'DATE' && label.type !== 'BIZ_DATE' && label.type !== 'TEXT_NUMERIC') {
        return {
            width: 0,
            height: 0,
            name: '',
        };
    }

    // 标签真实宽高，字体大小 * 字数
    let realMarkWidth = label.fontSize * (label.name ? label.name.length : 4);
    // 高度暂时不知道为什么要加 6px ，只不过加上 6px 就等于原来的值了
    const realMarkHeight = label.fontSize + 6;

    if (label.type === 'DATE') {
        // 日期标签
        realMarkWidth = label.fontSize * 6;
    }
    return {
        // 最小宽高为 77 20
        width: (realMarkWidth > 77) ? realMarkWidth : 77, // 80
        height: (realMarkHeight > 20) ? realMarkHeight : 20, // 20
        name: i18n.t('localCommon.text'),
    };
}

export function labelIconInfo(type) {
    let style = null;
    switch (type) {
        case 'SEAL':
            style =  {
                type: i18n.t('lang') === 'zh' ? 'el-icon-ssq-gaizhang1' : (i18n.t('lang') === 'en' ? 'el-icon-ssq-seal' : 'el-icon-ssq-icon-test1'),
            };
            break;
        case 'SIGNATURE':
            style = {
                type: 'el-icon-ssq-qianzi',
            };
            break;
        case 'CONFIRMATION_REQUEST_SEAL':
            style = {
                type: {
                    AGREE: 'el-icon-ssq-fuhegaizhangchu',
                    REFUSE: 'el-icon-ssq-bufugaizhangchu',
                },
            };
            break;
    }
    return style;
}

export function labelDefaultStyle(label) {
    const { x, y, height } = label.labelPosition;
    const left = x;
    // 新的坐标系根据文字定位，标签的左下角相对于pdf左下角的位置
    const top = 1 - y - height;
    const transformHeight = ['SEAL', 'SIGNATURE', 'CONFIRMATION_REQUEST_SEAL'].includes(label.labelType) ? '-26' : 0;
    return {
        top: `${top * 100}%`,
        left: `${left * 100}%`,
        marginTop: `${transformHeight}px`,
        fontSize: `${(label.labelExtends && label.labelExtends.pxFontSize) || 14}px`,
    };
}

export function labelWrapperStyle(label, color, labelWidth, labelHeight) {
    const labelType = label.labelType;
    let backgroundColorStyle = ['SINGLE_BOX', 'MULTIPLE_BOX', 'QR_CODE', 'CONFIRMATION_REQUEST_SEAL'].includes(labelType) ? 'none' : `rgba(${color}, 0.8)`;
    // 单复选框选中样式
    if (['SINGLE_BOX', 'MULTIPLE_BOX'].includes(labelType) && 'valueStr' in label && !label.labelExtends.receiverFill) {
        backgroundColorStyle = `rgba(${color}, 0.8)`;
    }
    // const alignmentMap = {
    //     0: 'left',
    //     1: 'center',
    //     2: 'right',
    // };
    let textAlign = 'left';
    if (['TEXT', 'TEXT_NUMERIC'].includes(labelType)) {
        const { alignment } = label.labelExtends;
        textAlign = alignment;
    }
    return {
        backgroundColor: backgroundColorStyle,
        width: `${labelWidth}px`,
        height: `${labelHeight}px`,
        'text-align': textAlign,
    };
}

export function labelBorderStyle(label, labelWidth, labelHeight, isFocus) {
    const { labelType } = label;
    const borderColor = ['SINGLE_BOX', 'MULTIPLE_BOX', 'CONFIRMATION_REQUEST_SEAL'].includes(labelType) ? '#88CCFF' : 'transparent';

    let borderStyle = 'solid';
    if (['SINGLE_BOX', 'MULTIPLE_BOX'].includes(labelType) && 'valueStr' in label && !label.labelExtends.receiverFill) {
        borderStyle = 'none';
    } else if (['SINGLE_BOX', 'MULTIPLE_BOX', 'CONFIRMATION_REQUEST_SEAL'].includes(labelType)) {
        borderStyle = 'dashed';
    }

    const transformHeight = ['SEAL', 'SIGNATURE', 'CONFIRMATION_REQUEST_SEAL'].includes(labelType) ? '26' : 0;
    return {
        width: `${labelWidth - 4}px`,
        height: `${labelHeight - 4}px`,
        'border-color': isFocus ? '#127fd2' : borderColor,
        'border-style': borderStyle,
        marginTop: `${transformHeight}px`,
    };
}

export function buttonsStyle(label, item, pageWidth, pageHeight) {
    const { height } = label.labelPosition;
    const labelButtoStyle = labelInfo(label.labelType).button;
    return {
        left: `${item.itemX * pageWidth}px`,
        top: `${(height - item.itemY) * pageHeight - labelButtoStyle.height}px`,
        width: `${labelButtoStyle.width}px`,
        height: `${labelButtoStyle.height}px`,
    };
}

export function addBtnStyle(label, pageWidth, pageHeight) {
    const labelPosition = label.labelPosition;
    return {
        left: `${labelPosition.width / 2 * pageWidth - 14}px`,
        top: `${labelPosition.height * pageHeight + 2}px`,
    };
}

export function computeStyleByIndex(index) {
    const colorlist = rgbColorInfo.slice(1); // 第一个默认颜色保留给发件方使用
    return `background-color: rgba(${colorlist[index % 8]}, 0.7);
            border: 1px solid rgb(${colorlist[index % 8]})`;
}

export function computeStyle(list, receiverId) {
    const colorlist = rgbColorInfo.slice(1); // 第一个默认颜色保留给发件方使用
    const index = (list || []).findIndex(item => item.receiverId === receiverId);
    return `background-color: rgba(${colorlist[index % 8]}, 0.7);
            border: 1px solid rgb(${colorlist[index % 8]})`;
}
