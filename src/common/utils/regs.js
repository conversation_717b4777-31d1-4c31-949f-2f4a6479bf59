const resRules = {
    number: /^\d*$/, // 纯数字，用于判断 docmentId 等
    /**
     * 密码的校验规则: 6-18位的数字，字母，或者数字字母组合, 支持特殊字符
     * 支持的特殊字符有 中英文符号
     */
    pass: /^[a-zA-Z0-9 _`~!@#$%^&*()+=|{}':;',\[\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？～「」\-·¥《》"\\]{6,18}$/,

    signpass: /^[0-9]{6}$/, // 6位的数字

    invitationCode: /^[0-9]{6}$/, // 6位的数字

    // companyName: /^(([\u4e00-\u9fa50-9\（\）\(\)]{1,60})|(?![\u4e00-\u9fa5]+$)(?![a-zA-Z\（\）\(\)]+$)[\u4e00-\u9fa5A-Za-z0-9\（\）\(\)]{1,60})$/, // 只能用汉字、英文或（）；必须包含汉字。否则提示“请填写真实的企业名称
    companyName: /^.{1,60}$/,

    userName: /^(([a-zA-Z\s\·\•]{2,35})|([\u0400-\u04FF\s\·\•]{2,35})|([\u4e00-\u9fa5\u4dae\uE863\s\·\•]{2,35})|(((?![\u3000-\u303F])[\u2E80-\uFE4F]{2,35}|\·\•)*(?![\u3000-\u303F])[\u2E80-\uFE4F]{2,35}(\·\•)*))$/, // 不能是英文和中文(俄文)的组合，不能有标点，可以有空格和原点，不区分大小写，最长不超过32个字符
    longUserName: /^(([a-zA-Z\s\·\•]{2,64})|([\u0400-\u04FF\s\·\•]{2,64})|([\u4e00-\u9fa5\u4dae\uE863\s\·\•]{2,64})|(((?![\u3000-\u303F])[\u2E80-\uFE4F]{2,64}|\·\•)*(?![\u3000-\u303F])[\u2E80-\uFE4F]{2,64}(\·\•)*))$/, // 不能是英文和中文(俄文)的组合，不能有标点，可以有空格和原点，不区分大小写，最长不超过64个字符,SAAS-19948

    foreignerUserName: /^(([a-zA-Z\s\·\•\-\,\，]{2,35})|([\u0400-\u04FF\s\·\•\-\,\，]{2,35})|([\u4e00-\u9fa5\u4dae\uE863\s\·\•\-\,\，]{2,35})|(((?![\u3000-\u303F])[\u2E80-\uFE4F\-]{2,35}|\·\•\-\,\，)*(?![\u3000-\u303F])[\u2E80-\uFE4F\-\,\，]{2,35}(\·\•)*))$/, // 和userName相比,多了：外国人实名允许填写逗号，（包括中英文）和短横杠- SAAS-12562

    userNameIgnore: /^(([a-zA-Z\s\·\•\*]{2,35})|([\u0400-\u04FF\s\·\•\*]{2,35})|([\u4e00-\u9fa5\u4dae\uE863\s\·\•\*]{2,35})|(((?![\u3000-\u303F])[\u2E80-\uFE4F\*]{2,35}|\·\•)*(?![\u3000-\u303F])[\u2E80-\uFE4F\*]{2,35}(\·\•)*))$/, // 允许*的用户名
    // 旧的不能识别com.cn

    // userAccount: /^1[0-9]{10}|[a-zA-Z0-9]+([._\\-]*[a-zA-Z0-9])*@([a-zA-Z0-9]*[-a-zA-Z0-9]*){1,63}\.[a-zA-Z0-9]+$/,
    userAccount: /(^1[0-9]{10}$)|(^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-_]+(\.[a-zA-Z0-9-_]+)*\.[a-zA-Z0-9-_]{2,6}$)/,

    // userPhone: /0?(1[0-9])[0-9]{9}$/,
    userPhone: /^1[0-9]{10}$/,

    // userEmail: /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
    userEmail: /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-_]+(\.[a-zA-Z0-9-_]+)*\.[a-zA-Z0-9-_]{2,6}$/,

    phoneVerifyCode: /^\d{6}$/,

    imageVerifyCode: /^\d{4}$/,

    loginCode: /^[a-zA-Z0-9 _`~!@#$%^&*()+=|{}':;',\[\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？～「」\-·¥《》"\\]{6,18}$/, // 6-18位的数字，字母，或者数字字母组合，支持特殊字符

    signCode: /^\d{6}$/, // 6位数字

    weakIdCardReg: /^(\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x))$/, // 身份证号改为弱校验（身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X）

    IDCardReg: /(^([1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2})$)/,

    uniSocCreditCode: /[^_IOZSVa-z\W]{2}\d{6}[^_IOZSVa-z\W]{10}/g,
    dateFormOne: /(^[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$)|长期/, // '2012-09-09'
    specialSymbol: /[~!#$%^&*()+={}<>?\/\\,';:`]/, // 筛选包含特殊字符的账号姓名
    numberLabelCode: /^[-\.\d]*[-\.\d]*[-\.\d]*$/, // 数字标签

    roleNameStr: /(^[\u4E00-\u9FA5A-Za-z0-9]+$)/, // 业务角色校验：中文、英文、数字
    complexPass: /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])[a-zA-Z0-9 _`~!@#$%^&*()+=|{}':;',\[\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？～「」\-·¥《》"\\]{6,18}$/, // 和后端保持一致
    // complexPass: new RegExp('(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z]).{6,18}'),
    maskPhone: /(\d{3})\d*(\d{3})/, // 手机号脱敏
    // maskEmail: /(.{1,2})(.*)(@.+)/g, //邮箱脱敏
    // https://jira.bestsign.tech/browse/CFD-5949 在发送合同页面，填写的企业名称（1）不允许包含@号：避免误输入邮箱（2）不允许是纯数字：避免误输入手机号
    entName: [/@/, /^\d+$/],
    fieldValueReg: /[()#\/\\]/, // 单复选框备选项不能输入\/()#特殊符号：CFD-6264、SAAS-12570
    maskIDCardReg: /^(.{3})(?:\w+)(.{3})$/, // 身份证号脱敏
    normalUrl: /(https?):\/\/[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]/gi,
};

export default resRules;
