/* eslint-disable no-undef */
import 'pdfview/build/pdf.js';
import workerSrc from 'pdfview/build/pdf.worker.js';
import 'pdfview/web/pdf_viewer';
import 'pdfview/web/pdf_viewer.css';

// 高清渲染需要的参数
const CSS_UNITS = 96.0 / 72.0;
PDFJS.workerSrc = workerSrc;
PDFJS.disableWorker = true;
PDFJS.cMapUrl = pdfCmaps;
PDFJS.cMapPacked = true;
// 预览图宽高
const THUMBNAIL_WIDTH = 130;
const THUMBNAIL_HEIGHT = 184;

export default {
    // 循环加载PDF文档
    async loadPDF(docList) {
        let index = 0;
        while (index < docList.length) {
            const doc = docList[index];
            await this.loadPDFDocument(doc, doc.attachmentId || doc.documentId);
            index++;
        }
    },
    // 加载pdf文档
    async loadPDFDocument(url) {
        try {
            const pdfDocument = await PDFJS.getDocument(url);
            return pdfDocument;
        } catch (error) {
            console.log('loadPDFDocument-error', error);
            throw error;
        }
    },
    // 渲染每一页
    async renderPDFPage(pdfDocument, pageNum, container, scale = 1) {
        // TODO 本页面渲染时添加loading提示
        const pdfPage = await pdfDocument.getPage(pageNum);
        const pdfPageView = new PDFJS.PDFPageView({
            container: container,
            id: pageNum,
            // scale: scale / CSS_UNITS,
            scale,
            // defaultViewport: pdfPage.getViewport(scale / CSS_UNITS),
            defaultViewport: pdfPage.getViewport(scale),
        });
        pdfPageView.setPdfPage(pdfPage);
        // await pdfPageView.draw();
        // const { width, height } = pdfPageView.pdfPage.getViewport(1.0);
        const actual = pdfPageView.viewport;
        // 获取原始的pdf文档宽高，兼容后端宽高返回值
        const { width, height } = pdfPageView.pdfPage.getViewport(CSS_UNITS);
        return {
            pdfPageView,
            width,
            height,
            actualWidth: actual && actual.width,
            actualHeight: actual && actual.height,
        };
    },
    // 生成缩略图列表
    generateList(PdfResult, targetWidth = THUMBNAIL_WIDTH, targetHeight = THUMBNAIL_HEIGHT) {
        const thumbnailList = [];
        // width，height为pdf文档每一页的宽高，理论上应该是一样的
        PdfResult.forEach(({ pdfPageView, width, height }) => {
            const thumbnail = this.generateImage(pdfPageView.canvas, width, height, targetWidth, targetHeight);
            thumbnailList.push(thumbnail);
        });
        return thumbnailList;
    },
    // 导出文档的缩略图
    generateImage(pdfCanvas, width, height, targetWidth, targetHeight) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        let canvasWidth, canvasHeight;
        // 对图片进行压缩
        // 图片比较宽，以高度为scale进行放缩
        if (targetWidth / width > targetHeight / height) {
            canvasWidth = width * (targetHeight / height);
            canvasHeight = targetHeight;
        } else {
            // 图片较高，以宽度为scale进行放缩
            canvasWidth = targetWidth;
            canvasHeight = height * (targetWidth / width);
        }
        canvas.width = canvasWidth * 4;
        canvas.height = canvasHeight * 4;
        ctx.drawImage(pdfCanvas, 0, 0, canvas.width, canvas.height);
        return canvas.toDataURL();
    },

    // 渲染每一页
    async renderReviewPDFPage(pdfDocument, pageNum, container, docWidth) {
        // TODO 本页面渲染时添加loading提示
        const pdfPage = await pdfDocument.getPage(pageNum);
        // const scale = 1;
        const scale = docWidth / 793;
        const pdfPageView = new PDFJS.PDFPageView({
            container: container,
            id: pageNum,
            // scale: scale / CSS_UNITS,
            scale,
            // defaultViewport: pdfPage.getViewport(scale / CSS_UNITS),
            defaultViewport: pdfPage.getViewport(1),
        });
        pdfPageView.setPdfPage(pdfPage);
        // await pdfPageView.draw();
        // const { width, height } = pdfPageView.pdfPage.getViewport(1.0);
        const actual = pdfPageView.viewport;
        // 获取原始的pdf文档宽高，兼容后端宽高返回值
        const { width, height } = pdfPageView.pdfPage.getViewport(CSS_UNITS);
        return {
            pdfPageView,
            width,
            height,
            actualWidth: actual && actual.width,
            actualHeight: actual && actual.height,
        };
    },
};
