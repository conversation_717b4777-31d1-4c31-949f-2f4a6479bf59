/*
 * @Author: fan_liu
 * @Date: 2020-09-23 13:55:46
 * @LastEditors: fan_liu
 * @LastEditTime: 2020-11-02 11:32:22
 * @Description: 是否可以使用微信刷脸组件
 *      在企业微信环境下返回false，二级域名在reliableHost范围内或者在小程序webview场景返回true
 */
import wxSDK from 'weixin-js-sdk';
import { isWechat, isQyWechat } from 'utils/device.js';
// cdn引入wx的sdk会导致import的失效，需要兼容一下
const wx = window.wx || wxSDK;
export default () => {
    const reliableHost = [
        'ent.bestsign.cn',
        'ent.bestsign.info',
        'ent2-hwy.bestsign.info',
        'api.bestsign.cn',
        'api.bestsign.info',
    ].includes(location.host) || process.env.NODE_ENV !== 'production';
    let flag = true;
    if (!isWechat) {
        flag = false;
    } else {
        wx.miniProgram.getEnv(res => {
            const isMiniProgram = res.miniprogram;
            flag = !isQyWechat && (isMiniProgram || reliableHost);
        });
    }
    console.log(flag);
    return flag;
};
