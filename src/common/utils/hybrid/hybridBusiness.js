import store from 'src/store/store';
import router from 'src/router/router.js';
import { invokeDownloadHelper } from 'src/common/utils/download.js';

// 是否为不在企业内网中的混合云用户
export function userNotInLan() {
    return store.getters.getHybridUserType === 'hybrid' && !store.getters.getInLAN;
}

// 指定位置、预览页混合云连接中断
export async function fieldLanBreak(callback) {
    if (userNotInLan()) {
        // 比原来代码中间省略了一步 checkInLAN(0)，因为进入发起流程时一直有轮询混合云状态的
        // 带来最大问题：不是实时校验网络状态
        callback && callback();
        const res = await Vue.$hybrid.offlineTip({ fieldPage: true });
        if (!res) {
            router.push('/account-center/home');
        }
    } else {
        callback && callback();
    }
}

/**
 *
 * 混3发起合同上传文件，upload 自定义请求
 * @export
 * @param {*} { hybridServer, hybridTarget, headers, data, opts}
 */
export function prepareUploadRequest({ hybridServer, hybridTarget, headers, data, opts }) {
    const formData = new FormData();
    formData.append('file', opts.file);

    // 其他参数需要转成 json 字符串
    formData.append('params', JSON.stringify({ ...data, ...opts.data }));
    formData.append('target', hybridTarget);

    return Vue.$http({
        method: 'post',
        url: hybridServer + '/hybrid/file',
        data: formData,
        headers,
        noToast: 1,
        noUseToken: true,
    });
}

// 缓存混合云头部
let hybridAjaxHeader = {};

/**
 *
 * @desc 避免后端存储临时文件，模板预览图片请求 base64 数据流
 * @export
 * @param {*} url
 * @returns base64
 */
export function tempGetPreviewImgSrc(url) {
    const { hybridServer } = store.state.commonHeaderInfo;
    // 使用缓存的混合云头部
    if (Object.keys(hybridAjaxHeader) > 0 && hybridServer) {
        return Vue.$http(`${hybridServer}${url}`, { headers: hybridAjaxHeader });
    }
    // 混合云
    if (hybridServer) {
        return Vue.$hybrid.makeHeader({ url: `${hybridServer}${url}`, methods: 'GET' }).then(({ res }) => {
            hybridAjaxHeader = res;
            return Vue.$http(`${hybridServer}${url}`, { headers: hybridAjaxHeader });
        });
    }
    // 公有云
    return Vue.$http(url);
}

/**
 *
 * @desc 合同管理下载单份合同，获取单份合同全部文档或者部分文档的下载路径
 * @param {*} type
 * @param {*} { hybridServer, hybridAccessToken, accessToken, contractId, systemType }
 * @returns
 */
async function _handleGammaSingleDownload(type, opt) {
    const targetMap = {
        single: `/contract/document/download`, // 合同管理下载单份合同的全部文档
        singlePart: `/contract/part/document/download`, // 合同管理下载单份合同的部分文档
    };
    const params = {
        contractId: opt.contractId,
        needMergeLabel: true,
    };

    if (type === 'singlePart') {
        params.documentIds = opt.documentIds;
    }
    if (opt.ifPaperSign) {
        params.ifPaperSign = opt.ifPaperSign;
    }

    const hybridAccessToken = await Vue.$hybrid.getOneTimeToken({ contractId: opt.contractId });
    return encodeURI(`${opt.hybridServer}/hybrid/file?target=${targetMap[type]}&params=${JSON.stringify(params)}&bestsign-access-token=${hybridAccessToken}`);
}

/**
 *
 * @desc 合同管理下载单份合同，获取混合云的下载路径，兼容新混合云1、2、3，数据本地化混合云下载增加access_token
 * @export
 * @param {*} type
 * @param {*} { contractId, systemType, path, query, documentIds, hybridVersion } hybridVersion标记列表里的文档hybrid版本
 * @returns
 */
// eslint-disable-next-line no-undef
export async function docGetSingleDownloadUrl(type, opt = { contractId, systemType, path, query, hybridVersion, ifPaperSign }) {
    opt.query = opt.query || '';
    const accessToken = Vue.$cookie.get('access_token');

    return new Promise((resolve, reject) => {
        if (opt.systemType === 'HYBRID_CLOUD') {
            // 判断新老混合云
            Vue.$hybrid.getContractHost(opt.contractId).then(async res => {
                const { hybridServer, hybridAccessToken = '', deltaHybridVersion } = res;
                if (hybridServer) {
                    // 判断是否访问混合云
                    const ret = await Vue.$hybrid.echoBeforeOperate({ operate: '下载', hybridServer: hybridServer });
                    if (!ret) {
                        reject();
                    }

                    // 详情页下载会预先设置Vue.$hybrid版本，列表页下载时文档版本通过传参标记：CFD-6582
                    if (deltaHybridVersion === '3.0') {
                        resolve(_handleGammaSingleDownload(type, { ...opt, hybridServer, hybridAccessToken }));
                    }

                    // 兼容数据本地化混合云增加access_token
                    resolve(`${hybridServer}${opt.path}?needMergeLabel=true&bestsign-access-token=${hybridAccessToken}${opt.query}&access_token=${accessToken}`);
                }
                reject();
            });
        } else {
            resolve(`${opt.path}?needMergeLabel=true&access_token=${accessToken}${opt.query}`);
        }
    });
}

/**
 *
 * @desc 合同管理批量下载合同，获取混合云的下载路径，兼容新混合云与数据本地话混合云下载增加access_token
 * @export
 * @param {*} { hybridServer, contractIds, hybridAccessToken }
 * @returns
 */
export function docGetBatchDownloadUrl({ hybridVersion, hybridServer, contractIds, hybridAccessToken }) {
    const HYBRID_DOWNLOAD_PATH = '/contract-api/contracts/documents/download';
    if (hybridVersion === '3.0') {
        const hybridParams = JSON.stringify({ contractIds: contractIds.join(','), needMergeLabel: true });
        return encodeURI(`${hybridServer}/hybrid/file?params=${hybridParams}&bestsign-access-token=${hybridAccessToken}&target=/contract/document/batch/download`);
    } else {
        return encodeURI(`${hybridServer}${HYBRID_DOWNLOAD_PATH}?contractIds=${contractIds}&needMergeLabel=true&bestsign-access-token=${hybridAccessToken}&access_token=${Vue.$cookie.get('access_token')}`);
    }
}

/**
 *
 * @desc 合同管理根据合同Id列表批量下载混合云合同
 * @desc 批量下载合同功能已下线 https://jira.bestsign.tech/browse/SAAS-9631
 * @export
 * @param {*} { hybridServer, contractIds, hybridAccessToken }
 */
export function docHandleBatchDownload({ hybridVersion, hybridServer, contractIds, hybridAccessToken }) {
    const url = docGetBatchDownloadUrl({ hybridVersion, hybridServer, contractIds, hybridAccessToken });
    invokeDownloadHelper(url);
}

/**
 *
 * @desc 批量下载合同时，逐条检测合同对应混合云网络环境
 * @export
 * @param {*} hybridContracts
 */
export async function docCheckContractsNetWork(hybridContracts, vm) {
    const loading = vm.$loading({
        text: vm.$t('docContentTable.isCheckingNet'),
    });
    const promiseList = hybridContracts.map(item => {
        return Vue.$hybrid.echo(item.hybridServer, { timeout: 5000 })
            .then(() => {
                return Promise.resolve({ result: true });
            })
            .catch(() => {
                return Promise.resolve({ result: false });
            });
    });

    const hybridDownload = [];
    const netError = [];
    await Promise.all(promiseList)
        .then(res => {
            loading.close();
            res.forEach((item, index) => {
                const hybridEntInfo = hybridContracts[index];
                item.result === true ? hybridDownload.push(hybridEntInfo) : netError.push(hybridEntInfo);
            });
        });
    return {
        hybridDownload,
        netError,
    };
}

/**
 *
 * @desc 批量签署时对混合云、公有云合同进行分类
 * @export
 * @param {*} docList
 * @returns {
        publicRow, // [{contractId,sealId, entId}]
        hybridRow, // {hybridServer: []}
    }
 */
export function docFilterHybridBatchDeal(docList) {
    const publicRow = []; // 公有云合同 [{contractId,sealId, entId}]
    const hybridRow = {}; // 混合云合同 {hybridServer: []}
    docList.map(v => {
        v.data.map(row => {
            const key = {
                contractId: row.contractId,
                sealId: row.sealId,
                entId: row.entId,
                contractTitle: row.contractTitle,
                deltaHybridVersion: row.deltaHybridVersion, // 区分混3和混1
            };
            if (row.systemType === 'HYBRID_CLOUD') {
                if (hybridRow[row.hybridServer]) {
                    hybridRow[row.hybridServer].push(key);
                } else {
                    hybridRow[row.hybridServer] = [key];
                }
            } else {
                publicRow.push(key);
            }
        });
    });

    return {
        publicRow,
        hybridRow,
    };
}

/**
 *
 * @desc 合同详情页判断是其他混合云的合同时，轮询网络情况
 * @export
 * @param {*} hybridUserType
 * @param {*} hybridServer
 * @returns
 */
export function docDetailCheckHybridOnline(hybridUserType, hybridServer) {
    let result = null;
    if (hybridUserType === 'otherHybrid') {
        result = setTimeout(() => {
            Vue.$hybrid.echo(hybridServer, { timeout: 4000 })
                .then(() => {
                    store.commit('changeCacheLANStatus', true);
                })
                .catch(() => {
                    store.commit('changeCacheLANStatus', false);
                });
            docDetailCheckHybridOnline(hybridUserType, hybridServer);
        }, 5000);
    }
    return result;
}

/**
 * 混3.0业务字段本地化通用接口
 * @param url 混合云客户端接口路径
 * @param requestData body上携带的参数，可空
 * @param uriWithParam 混合云客户端接口路径，跟url一致，可空
 * @param parameterMap 混合云接口上面携带的url参数
 * @param contractId 合同Id，签署时携带
 */
export  async function businessLocalField({
    url,
    requestData = {},
    uriWithParam,
    parameterMap = {},
    contractId,
}) {
    const { hybridServer } = store.state.commonHeaderInfo;
    // parameterMap 拼接url参数
    const urlSuffix = [];
    let urlSuffixStr = '';
    for (const key in parameterMap) {
        urlSuffix.push(`${key}=${encodeURIComponent(parameterMap[key])}`);
    }
    if (urlSuffix.length) {
        urlSuffixStr = `?${urlSuffix.join('&')}`;
    }

    // 获取混合云请求头
    const hybridHeader = await Vue.$http({
        method: 'POST',
        url: '/ents/developer/hybrid-header',
        data: {
            uriWithParam: uriWithParam || url, // 请求的客户端路径
            parameterMap, // url上携带的参数，如果路径url上没有携带参数，则为{}
            requestData: { // body上携带的参数
                target: requestData.target,
                params: requestData.params,
            },
            contractId, // 合同签署时需要
        },
    });
    return Vue.$http({
        method: 'POST',
        url: `${hybridServer}${url}${urlSuffixStr}`,  // parameterMap的参数需要合并到url路径上
        data: requestData,
        headers: {
            ...hybridHeader.data,
        },
        noUseToken: true,
    });
}
