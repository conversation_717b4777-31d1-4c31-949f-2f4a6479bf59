
import store from 'src/store/store';
import GammaBasic from './basic';

/**
 *
 * @desc 混合云 3.0 业务逻辑，继承 3.0 通用方法
 * @export
 * @class HybridGamma
 * @extends {GammaBasic}
 */
export default class HybridGamma extends GammaBasic {
    // eslint-disable-next-line no-useless-constructor
    constructor() {
        super();
    }
    /**
     *
     * @desc 签署、模板上传完文件后，将新文件对象 push 到 docList 中，预览图从后端返回的 page 中取
     * @param {*} res
     * @returns
     * @memberof HybridGamma
     */
    signOrTempMergeDocToList(res) {
        const { order, page = [] } = res;
        const imgSrc = this.getImgSrc(page[0].imagePreviewUrl);
        const docData = Object.assign({}, res, {
            uploadStatus: 2,
            imgSrc,
            zoomMaskShow: false,
        });
        return {
            order,
            docData,
        };
    }
    // 公有云 单点预览模板文件 逻辑，递归调用
    // todo 需要后端改成混3接口的机制
    normalTempPreviewList(templateId, templatePreviewId) {
        return new Promise((resolve, reject) => {
            Vue.$http(`/contract-api/contracts/templates/${templateId}/previews?page=1&templatePreviewId=${templatePreviewId}`)
                .then(async({ data }) => {
                    const pageSize = data.pageSize;
                    const cacheList = new Array(pageSize);
                    cacheList[0] = `data:image/jpg;base64,${data.imageData}`;
                    for (let i = 2; i <= pageSize; i++) {
                        await Vue.$http(`/contract-api/contracts/templates/${templateId}/previews?page=${i}&templatePreviewId=${templatePreviewId}`)
                            .then((res) => {
                                cacheList[i - 1] = `data:image/jpg;base64,${res.data.imageData}`;
                            });
                    }

                    resolve({
                        pageSize,
                        list: cacheList,
                    });
                }).catch(() => {
                    reject();
                });
        });
    }
    /**
     *
     * @desc 单点预览模板文件，后端直接返回图片预览地址，如果是混合云会拼好图片 token 、页面、参数
     * @desc 公有云请求走以前调用 get 接口的逻辑，混3走 post 新接口逻辑
     * @param {*} templateId
     * @param {*} templatePreviewId
     * @returns
     * @memberof HybridGamma
     */
    tempPreviewContractImgList(templateId, templatePreviewId) {
        const { hybridServer } = store.state.commonHeaderInfo;

        if (!hybridServer) {
            return this.normalTempPreviewList(templateId, templatePreviewId);
        }

        return new Promise((resolve, reject) => {
            this.handleCreateRequest({
                url: `/contract-api/contracts/templates/${templateId}/previews?page=1&templatePreviewId=${templatePreviewId}`,
                hybridTarget: '/templates/contracts/preview/urls',
                method: 'post',
                data: {
                    templateId,
                    templatePreviewId,
                },
            }).then(res => {
                resolve({
                    list: res.data,
                    pageSize: res.data.length,
                });
            }).catch(() => {
                reject();
            });
        });
    }
}
