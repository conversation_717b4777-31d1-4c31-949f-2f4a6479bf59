import store from 'src/store/store';
import URL from 'url';
import { joinPathnameAndQueryObj } from 'utils/getQueryString.js';
import i18n from 'src/lang';

/**
 *
 * @desc 混合云 1.0 通用方法
 * @class AlphaBasic
 */
class AlphaBasic {
    constructor() {
        this.inLANInterval = null;
        this.name = 'alpha basic';
    }
    echo(host, config) {
        // eslint-disable-next-line no-async-promise-executor
        return new Promise(async(resolve, reject) => {
            try {
                const res = await Vue.$http.get(`${host}/echo`, config, { noToast: true });
                store.commit('setHybridVersion', res.data && res.data.version);
                resolve(res);
            } catch (e) {
                reject(e);
            }
        });
    }
    checkInLAN(interval) {
        clearTimeout(this.inLANInterval);
        interval = typeof interval === 'undefined' ? 5000 : interval;
        if (interval === 0) {
            return this.echo(`${store.state.commonHeaderInfo.hybridServer}`, { timeout: 5000 })
                .then(() => {
                    store.commit('changeLANStatus', true);
                    this.checkInLAN();
                })
                .catch(() => {
                    store.commit('changeLANStatus', false);
                    this.checkInLAN();
                });
        }

        // 混合云用户进入公有云合同则不轮询以第一次进页面的状态为准
        if (!(
            store.getters.getHybridUserType === 'publicAliyun' ||
                store.getters.getHybridUserType === 'hybridAliyun'
        )) {
            this.inLANInterval = setTimeout(() => {
                this.echo(`${store.state.commonHeaderInfo.hybridServer}`, { timeout: 5000 })
                    .then(() => {
                        store.commit('changeLANStatus', true);
                    })
                    .catch(() => {
                        store.commit('changeLANStatus', false);
                    });
                this.checkInLAN();
            }, interval);
        }
    }
    /**
     * 生成混合云请求headers
     * 入参：params, method, requestData, contractId(可不填写)
     * 接口返回：
     * bestsign-client-id
     * bestsign-sign-timestamp
     * bestsign-signature
     * bestsign-signature-type
     * 将以上参数添加到混合云请求的headers
     */
    createHybridCloudHeaders({ url, method, requestData, isFormType, contractId }) {
        const urlParser = URL.parse(url);
        let uriWithParam = urlParser.pathname;
        if (method.toLowerCase() === 'get' && urlParser.search) {
            uriWithParam += urlParser.search;
        }
        if (isFormType) {
            uriWithParam = requestData
                ? joinPathnameAndQueryObj(uriWithParam, requestData)
                : uriWithParam;
            return Vue.$http({
                method: 'post',
                url: 'ents/developer/hybrid-header',
                data: {
                    uriWithParam,
                    requestData: {},
                },
            });
        } else {
            return Vue.$http.post('ents/developer/hybrid-header', {
                uriWithParam,
                requestData,
                contractId,
            });
        }
    }
    /**
     * 不定向请求
     * 请求的host受head-info的hybridServer控制
     * 用法同axios(...config);
     */
    undirectedRequest(config, isFormType) {
        // eslint-disable-next-line prefer-const
        let { url, method, data, headers = {}, contractId, hybridServer, ...otherConfig } = config;
        hybridServer =
            hybridServer === ''
                ? ''
                : hybridServer || store.state.commonHeaderInfo.hybridServer;
        const request = (resolve, reject) => {
            url = hybridServer + url;
            Vue.$http({
                url,
                method,
                data,
                headers,
                ...otherConfig,
            })
                .then(res => {
                    resolve(res);
                })
                .catch(err => {
                    reject(err);
                });
        };
        return new Promise((resolve, reject) => {
            if (hybridServer) {
                this.createHybridCloudHeaders({ url, method, requestData: data, isFormType, contractId })
                    .then(res => {
                        const resData = res.data;
                        const resHeaders = {
                            'bestsign-client-id': resData['bestsign-client-id'] || '',
                            'bestsign-sign-timestamp': resData['bestsign-sign-timestamp'] || '',
                            'bestsign-signature': resData['bestsign-signature'] || '',
                            'bestsign-signature-type': resData['bestsign-signature-type'] || '',
                            'bestsign-user': resData['bestsign-user'] || '',
                        };
                        headers = {
                            ...headers,
                            ...resHeaders,
                        };
                        request(resolve, reject);
                    })
                    .catch(() => {});
            } else {
                request(resolve, reject);
            }
        });
    }
    getImgSrcCros(path, query) {
        query = query || '';
        const hybridServer = store.state.commonHeaderInfo.hybridServer;
        const hybridAccessToken = store.state.commonHeaderInfo.hybridAccessToken;
        const join = path.indexOf('?') === -1 ? '?' : '&';
        if (!hybridServer) {
            return `${path}${join}access_token=${Vue.$cookie.get('access_token')}${query}`;
        } else {
            // 兼容数据本地化混合云增加access_token
            return `${hybridServer}${path}${join}bestsign-access-token=${hybridAccessToken}${query}&access_token=${Vue.$cookie.get('access_token')}`;
        }
    }
    // 返回合同对应的 hybridServer 和 hybridAccessToken
    returnContractHost(contractId) {
        return Vue.$http
            .get('users/hybrid-conf', {
                params: { contractId },
            })
            .then(res => {
                return {
                    hybridServer: res.data.hybridServer,
                    hybridAccessToken: res.data.hybridAccessToken,
                    deltaHybridVersion: res.data.deltaHybridVersion,
                };
            })
            .catch(() => {});
    }

    // 当收件人进入合同相关页面时，替换hybridAccessToken
    getHybridInfo(systemType, contractId) {
        // 如果是混合云合同
        if (systemType === 'HYBRID_CLOUD') {
            return Vue.$http
                .get(`/users/hybrid-conf?contractId=${contractId}`)
                .then(res => {
                    store.commit('replaceHybridAccessToken', {
                        hybridServer: res.data.hybridServer,
                        hybridAccessToken: res.data.hybridAccessToken,
                    });
                    if (store.getters.getHybridUserType === 'public') {
                        // 判断是公有云用户进入混合云合同
                        store.commit('changeHybridUserType', 'publicHybrid');
                    } else if (
                        store.getters.getHybridUserType === 'hybrid' &&
                        store.state.commonHeaderInfo.hybridServer !== res.data.hybridServer
                    ) {
                        // 判断混合云用户进入其他混合云合同
                        store.commit('changeHybridUserType', 'otherHybrid');
                    }
                })
                .then(() => {
                    return this.checkInLAN(0);
                });
        } else {
            // 如果是公有云合同，将 hybridServer 和 hybridAccessToken 设为空
            // 混合云用户进入公有云合同，获取图片时从公有云获取
            store.commit(
                'changeHybridUserType',
                store.state.commonHeaderInfo.hybridServer
                    ? 'hybridAliyun'
                    : 'publicAliyun',
            );
            store.commit('replaceHybridAccessToken', {
                hybridServer: '',
                hybridAccessToken: '',
            });
            clearTimeout(this.inLANInterval);
            return new Promise(function(resolve) {
                resolve();
            });
        }
    }
    /**
     * 混合云网络出错提示
     * *
     * @param  opt = {
     *         confirmBtnText,
     *         operate, 操作类型：签署、拒签、撤销、下载，显示在弹窗 headerTitle，
     *         callback,
     *         fieldPage: false, 指定位置页的提示特殊处理
     *         beforeMessage, 在弹窗提示之前resolve
     * }
     * @return promise
     */
    offlineTip(opt) {
        opt = opt || {};
        const confirm = i18n.t('docContentTable.confirm');
        const confirmBtnText = opt.confirmBtnText || confirm;
        const view = i18n.t('docDetail.recordDialog.view');
        const operate = opt.operate || view;
        const showClose = opt.showClose !== false;

        return new Promise((resolve) => {
            function showMessageBox(title, message, btnText) {
                // eslint-disable-next-line new-cap
                return Vue.MessageBox({
                    icon: 'null',
                    iClass: 'hybrid-network-tip-msgbox el-dialog-mobile',
                    showHeader: true,
                    headerTitle: i18n.t('docDetail.canNotOperateTip', { operate: operate }), // 无法${operate}合同
                    title,
                    message,
                    confirmBtnText: btnText,
                    showClose: showClose,
                })
                    .then(() => {
                        opt.callback && opt.callback();
                    })
                    .catch(() => {})
                    .finally(() => {
                        resolve(false);
                    });
            }
            // 混合云用户
            if (store.getters.getHybridUserType === 'hybrid') {
                // 判断用户是否在内网中
                if (store.getters.getInLAN) {
                    resolve(true);
                } else if (!opt.fieldPage) {
                    const msg = i18n.t('docContentTable.catchMap.checkNet');
                    // 判断当前用户处于局域网内且无法连接
                    Vue.$MessageToast.error(msg).then(() => {
                        resolve(false);
                    });
                } else if (opt.fieldPage) {
                    const boxTitle = i18n.t('docContentTable.catchMap.hybridNotConnect'); // 原因：您的企业采用了合同私有存储的方式，但当前网络无法连接至合同存储服务器。
                    const boxMessage = i18n.t('docContentTable.catchMap.hybridSuggest'); // 建议您：(1)检查网络是否正常；(2)检查合同存储服务器是否正常运行
                    showMessageBox(boxTitle, boxMessage, i18n.t('commonHeader.goHomePage')); // 返回首页
                }
            } else if (
                // 公有云用户或混合云用户进入其他混合云合同
                store.getters.getHybridUserType === 'publicHybrid' ||
                store.getters.getHybridUserType === 'otherHybrid'
            ) {
                // 判断收件人是否在内网中
                if (store.getters.getInLAN) {
                    resolve(true);
                } else {
                    const boxTitle =
                        i18n.t('docContentTable.catchMap.hybridNetHeader'); // 发件方企业采用了合同私有存储的方式，但当前网络无法连接至发件方的合同存储服务器。
                    const boxMessage = i18n.t('docContentTable.catchMap.hybridNetMsg'); // 建议您：检查网络是否正常
                    showMessageBox(boxTitle, boxMessage, confirmBtnText);
                }
            } else if (
                store.getters.getHybridUserType === 'public' ||
                store.getters.getHybridUserType === 'publicAliyun' ||
                store.getters.getHybridUserType === 'hybridAliyun'
            ) {
                resolve(true);
            }
        });
    }
}

export default AlphaBasic;
