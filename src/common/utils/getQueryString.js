export function getQueryString(name, path) {
    let urls;
    if (path.indexOf('?') !== path.lastIndexOf('?')) {
        urls = path.replace(/\?/g, '&').replace(/^.*?&/, '');
    } else {
        urls = path.replace(/^.*\?/, '');
    }
    var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
    var r = ('?' + urls).substr(1).match(reg);
    if (r != null) {
        return unescape(r[2]);
    }
    return null;
}

export const encodeSearchParams = (queryObj) => {
    if (Object.prototype.toString(queryObj) !== '[object Object]') {
        throw new Error('queryObj arguments must be type of Object');
    }
    const tempAry = [];
    Object.keys(queryObj).forEach((key) => {
        let value = queryObj[key];
        if (typeof value === 'undefined') {
            value = '';
        }
        tempAry.push([key, encodeURIComponent(value)].join('='));
    });
    return tempAry.join('&');
};

export const joinPathnameAndQueryObj = (pathname, queryObj) => {
    return `${pathname}?${encodeSearchParams(queryObj)}`;
};

export function urlDelParameter(name) {
    var loca = window.location;
    var baseUrl = loca.origin + loca.pathname;
    var query = loca.search.substr(1);
    if (query.indexOf(name) > -1) {
        var obj = {};
        var arr = query.split('&');
        for (var i = 0; i < arr.length; i++) {
            arr[i] = arr[i].split('=');
            obj[arr[i][0]] = arr[i][1];
        }
        delete obj[name];
        if (JSON.stringify(obj) !== '{}') {
            baseUrl = baseUrl + '?';
        }
        var url = baseUrl + JSON.stringify(obj).replace(/[\"\{\}]/g, '').replace(/\:/g, '=').replace(/\,/g, '&');
        return url;
    } else {
        return loca.href;
    }
}
