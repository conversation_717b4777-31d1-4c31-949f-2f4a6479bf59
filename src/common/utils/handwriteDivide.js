/**
 * 手写画板签名分割工具
 * */

/**
 * 将base64图片分割为 @nums 份
 * @param base64 {string}
 * @param nums {number}
 * @returns {Promise<string[]>}
 */
export async function base64ImageDivide(base64, nums) {
    const { img, width, height } = await imageReader(base64);
    const divideWidth = width / nums;
    let x = 0;
    const y = 0;
    const base64List = [];
    for (let i = 0; i < nums; i++) {
        base64List.push(imageClipToBase64(img, x, y, divideWidth, height));
        x += divideWidth;
    }
    console.log(base64List);
    return base64List;
}

/**
 * 获取base64图片的宽高
 * @param base64 {string}
 * @returns {Promise<{ img: HTMLImageElement, width: number, height: number }>}
 */
function imageReader(base64) {
    const img = new Image();
    img.src = base64;
    return new Promise(resolve => {
        img.onload = function() {
            const width = img.width;
            const height = img.height;
            resolve({
                img,
                width: width,
                height: height,
            });
        };
    });
}

/**
 * 将image的某区域转为base64
 * @param img{HTMLImageElement}
 * @param x{number}
 * @param y{number}
 * @param w{number}
 * @param h{number}
 * @returns {string}
 */
function imageClipToBase64(img, x, y, w, h) {
    const el = document.createElement('canvas');
    el.width = w;
    el.height = h;
    const ctx = el.getContext('2d');
    ctx.drawImage(img, x, y, w, h, 0, 0, w, h);
    return el.toDataURL('image/png');
}

/**
 * 将计算后属于该区域内的笔画过滤出来
 * @param positions{number[][]}
 * @param x1{number}
 * @param x2{number}
 * @param y1{number}
 * @param y2{number}
 * @returns {number[][]}
 */
function positionsFilterFromArea(positions, x1, x2, y1, y2) {
    return [...positions.filter(position => {
        let xSum = 0;
        let ySum = 0;
        const len = Math.round(position.length / 2);
        position.forEach((point, index) => {
            if (index % 2 === 0) {
                // x 坐标
                xSum += point;
            } else {
                // y 坐标
                ySum += point;
            }
        });
        const xMiddle = xSum / len;
        const yMiddle = ySum / len;
        return xMiddle > x1 && xMiddle < x2 && yMiddle > y1 && yMiddle < y2;
    })];
}

/**
 * 将手写笔迹的所有笔画按均分分配到对应的格子中
 * @param positions{number[][]}
 * @param w{number}
 * @param h{number}
 * @param nums{number}
 * @returns {number[][][]}
 */
export function positionsDivide(positions, w, h, nums) {
    const positionsGroup = [];
    let x = 0;
    const y = 0;
    const dw = w / nums;
    for (let i = 0; i < nums; i++) {
        positionsGroup.push(positionsFilterFromArea(positions, x, x + dw, y, y + h));
        x += dw;
    }
    return positionsGroup;
}
