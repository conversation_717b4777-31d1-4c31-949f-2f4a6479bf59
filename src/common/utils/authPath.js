import { isPC } from './device.js';
export default (authType = 'auth', otherQuery = {}) => {
    const mobilePath = `/mp/auth-m/individual/${authType}`;
    let queryStr = '';
    Object.keys(otherQuery).forEach((query, index) => {
        queryStr += `${index === 0 ? '?' : '&'}${query}=${otherQuery[query]}`;
    });
    if (isPC() && authType === 'ca-renewal') {
        return `/auth-p/person/${authType}` + queryStr;
    }
    return (isPC() ? `/auth-p/person/${authType === 'auth' ? 'normal' : 'guide'}` : mobilePath) + queryStr;
};
