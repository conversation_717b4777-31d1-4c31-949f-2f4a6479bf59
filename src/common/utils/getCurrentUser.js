/**
 * @return {当前发件人}
 * @description
 */
export function getCurrentUser(commonHeaderInfo, getAuthStatus) {
    let currentEnt;
    if (('' + commonHeaderInfo.currentEntId) === '0') {
        currentEnt = +getAuthStatus === 2 ? commonHeaderInfo.platformUser.fullName : commonHeaderInfo.platformUser.account;
    } else {
        for (var i = 0; i < commonHeaderInfo.enterprises.length; i++) {
            const temO = commonHeaderInfo.enterprises[i];
            if (temO.entId === commonHeaderInfo.currentEntId) {
                currentEnt = temO.fullEntName;
            }
        }
    }
    return currentEnt || commonHeaderInfo.platformUser.account;
}
