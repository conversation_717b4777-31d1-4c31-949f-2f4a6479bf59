import i18n from 'src/lang';
/**
 * @param  {Object} file 上传的合同文件
 * @return {Boolean}  满足条件返回true，否则返回false
 * @desc  普通发起，模板发起上传文件时对合同文件进行检测
 */
export function checkDocumentUploadLimit(file, size = 10) {
    const FILE_TYPES = ['image/jpeg', 'image/png', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'application/pdf', 'application/rtf', 'application/x-rtf', 'text/rtf', 'text/plain'];
    const FILE_NAME = ['jpg', 'jpeg', 'png', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf', 'rtf', 'txt'];
    const isLt10M = file.size / 1024 / 1024 < size;
    let isTypeAcc = FILE_TYPES.some((item) => {
        return file.type === item;
    });
    const isNameAcc = FILE_NAME.some((item) => {
        const tmpList = file.name.split('.');
        return tmpList[tmpList.length - 1].toLowerCase() === item;
    });
    if (file.type === '') {
        isTypeAcc = true;
    }
    if (!isLt10M) {
        Vue.$MessageToast.error(i18n.t('prepare.fileLessThan', { num: size }));
        return false;
    }
    // 拦截同时不满足文件类型和后缀名的情况
    if (isTypeAcc === false && isNameAcc === false) {
        Vue.$MessageToast.error(i18n.t('prepare.usePdf'));
        return false;
    }
    return isLt10M && !(isTypeAcc === false && isNameAcc === false);
}

/**
 * @param  {Object}  file 文件
 * @return {Boolean} 满足条件返回true，否则返回false
 * @desc   上传小于5M的excel文件（.xls, .xlsx, .csv）
 */
export function checkExcelUploadLimit(file) {
    const FILE_TYPES = [
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/csv',
    ];
    const FILE_NAME = [
        'xls',
        'xlsx',
        'csv',
    ];
    const isLt10M = file.size / 1024 / 1024 < 10;
    let isTypeAcc = FILE_TYPES.some((item) => {
        return file.type === item;
    });
    const isNameAcc = FILE_NAME.some((item) => {
        const tmpList = file.name.split('.');
        return tmpList[tmpList.length - 1].toLowerCase() === item;
    });
    if (file.type === '') {
        isTypeAcc = true;
    }
    if (!isLt10M) {
        Vue.$MessageToast.error(i18n.t('prepare.fileLessThan', { num: 10 }));
    }
    // 拦截同时不满足文件类型和后缀名的情况
    if (isTypeAcc === false && isNameAcc === false) {
        Vue.$MessageToast.error(i18n.t('prepare.beExcel'));
    }
    return isLt10M && !(isTypeAcc === false && isNameAcc === false);
}

/**
 *
 * @param  {Object}  file 文件
 * @param {Number} maxLength 将要限制的maxLength长度
 * @param {Boolean} showMessage 是否显示message
 * @desc   当上传的文件名称长度超过maxlength时，截取相应长度的名称
 */
export function checkFileNameLength(file, maxLength, showMessage) {
    if (maxLength !== null && file.name.length > maxLength) {
        let fileName;
        const keys = Object.getOwnPropertyNames(file); // 额外property
        const fileNames = file.name.split(/\.(?=[^\.]+$)/); // 如果有后缀获取后缀
        if (fileNames.length > 1) {
            /* 有后缀 */
            const maxNameLength = maxLength - (fileNames[fileNames.length - 1].length + 1);
            fileName = `${fileNames[0].slice(0, maxNameLength)}.${fileNames[1]}`;
        } else {
            /* 无后缀 */
            fileName = fileNames[0].slice(0, maxLength);
        }
        /* File类型定义的字段 */
        const newFile = new File([file], fileName, {
            type: file.type,
            lastModified: file.lastModified,
        });
        /* 自定义的字段 */
        keys.forEach(key => newFile[key] = file[key]);
        file = newFile;
        showMessage && Vue.$MessageToast.info(i18n.t('prepare.fileNameMoreThan', { num: maxLength }));
    }
    return file;
}
