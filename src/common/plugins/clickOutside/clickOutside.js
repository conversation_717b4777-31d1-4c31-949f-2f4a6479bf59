export const clickOutside = {
    inserted(el, bindings) { // el真实的dom元素
        // console.log(el, bindings, vnode);
        el.listener = function listener(e) {
            if (e.target === el || el.contains(e.target)) {
                return;
            }

            bindings.value(e); // close事件
        };
        document.addEventListener('click', el.listener);
    },
    unbind(el) { // 指令卸载时执行
        document.removeEventListener('click', el.listener);
    },
};

export default {};
