import { closestParent } from 'src/common/utils/dom.js';

/**
 * 合同签署标签上方 label-header-detail 的高度自适应
 * @param  {dom} el
 */
function labelHeaderHeight(el) {
    el.style.top = `-${el.offsetHeight}px`;
}

/**
 * @description  自动设置 element 表格的高度
 * @description 根据 static 判断表格是否绝对定位，是的话则修改最近的绝对定位的父元素的 top 值
 * @todo  window.resize 时自动触发并添加函数节流
 * @param  {Dom Element} el  [当前表格元素]
 * @param  {Object} opt [top, bottom, container]
 */
function tableHeight(el, opt) {
    const isStatic = opt.static || false;
    const top = opt.top || 0;
    const bottom = opt.bottom || 0;
    let height = 0;
    const tHeader = el.querySelector('.el-table__header-wrapper');
    const tBody = el.querySelector('.el-table__body-wrapper');

    // 计算表格高度，容器的高度 - 上方留白 - 下方留白
    if (opt.container) {
        console.log(opt.container.offsetHeight);
        height = opt.container.offsetHeight - top - bottom;
    }

    // 设置整个表格的高度
    el.style.height = `${height}px`;

    // 动态设置 table body 的 height，不然无法滚动
    // 需要用 table 的高度减去 tHeader 的高度
    tBody.style.height = tHeader ? `${height - tHeader.offsetHeight}px` : `${height}px`;

    // 如果是最对定位的表格
    if (!isStatic) {
        // 设置表格最近的绝对定位的父元素的 top 值
        closestParent(el, 'position', 'absolute').then(ele => {
            ele.style.top = `${top}px`;
        });
    }
}

function AutoHeight() {
    Vue.directive('autoH', function(el, binding) {
        Vue.nextTick(() => {
            // 根据 arg 判断高度自适应的元素
            switch (binding.arg) {
                case 'label':
                    labelHeaderHeight(el);
                    break;
                case 'table':
                    tableHeight(el, binding.value);
                    break;
            }
        });
    });
}

export default AutoHeight;
