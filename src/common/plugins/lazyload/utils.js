const inBrowser = typeof window !== 'undefined';

const style = (el, prop) => {
    return typeof getComputedStyle !== 'undefined'
        ? getComputedStyle(el, null).getPropertyValue(prop)
        : el.style[prop];
};

const overflow = (el) => {
    return style(el, 'overflow') + style(el, 'overflow-y') + style(el, 'overflow-x');
};

const throttle = (action, delay) => {
    let timeout = null;
    let lastRun = 0;
    return function() {
        if (timeout) {
            return;
        }
        const elapsed = Date.now() - lastRun;
        const context = this;
        const args = arguments;
        const runCallback = function() {
            lastRun = Date.now();
            timeout = false;
            action.apply(context, args);
        };
        if (elapsed >= delay) {
            runCallback();
        } else {
            timeout = setTimeout(runCallback, delay);
        }
    };
};

const scrollParentDom = (el) => {
    if (!inBrowser) {
        return;
    }

    let parent = el;

    while (parent) {
        if (parent === document.body || parent === document.documentElement) {
            break;
        }

        if (!parent.parentNode) {
            break;
        }

        if (/(scroll|auto)/.test(overflow(parent))) {
            return parent;
        }

        parent = parent.parentNode;
    }

    return window;
};

export {
    inBrowser,
    overflow,
    scrollParentDom,
    throttle,
};
