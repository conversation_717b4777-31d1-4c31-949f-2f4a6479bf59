/**
 * 功能留资 || 功能试用弹窗
 * 传featureKey或featureId都可以
 * 功能留资 => this.$featureSupport('approvalManage');
 * 功能试用(了解详情无购买入口) => this.$featureSupport({featureKey:'approvalManage',type:'learnDetail'});
 * 功能试用（有购买入口) => this.$featureSupport({featureKey:'approvalManage',type:'trial'});
 */
import FunctionSupport from 'src/components/functionSupport';
import store from 'src/store/store.js';
import router from 'src/router/router.js';

let vm = null;
// let queue = 0;

const Component = Vue.extend(FunctionSupport);

const clearCache = () => {
    // 完全销毁一个实例。清理它与其它实例的连接，解绑它的全部指令及事件监听器。
    vm.$destroy();
    // 删除dom
    vm.$el.parentNode.removeChild(vm.$el);
    // 释放内存
    vm = null;
    // bridge = null;
    // queue = 0;
};

const dispatchCallBack = () => {
    // if (action === 'resolve') {
    //     bridge.resolve('confirm');
    // } else {
    //     bridge.reject('cancel');
    // }
    clearCache();
};

FunctionSupport.install = function(option = {}) {
    // init optios
    let isTrial = false; // 是否是试用类型
    if (typeof option === 'string') {
        option = {
            featureKey: option,
        };
    } else {
        isTrial = true;
    }

    vm = new Component({ store, router }).$mount();

    // 然后把传入的参数赋值到组件实例
    vm.optionData = option;

    vm.dispatch = dispatchCallBack;

    // 最后挂载到body
    document.body.appendChild(vm.$el);

    // 显示FunctionSupport组件
    Vue.nextTick(() => {
        if (option && (option.featureKey || option.type)) {
            if (option.type === 'trialEnd') {
                vm.dialogInfo.isShowTrialEnd = true;
            } else {
                vm.dialogInfo.isShowIntro = isTrial;
                vm.dialogInfo.isShowLeaveMsg = !isTrial;
            }
        }
    });
};

Vue.prototype.$featureSupport = FunctionSupport.install;
Vue.$featureSupport = FunctionSupport.install;

export default FunctionSupport;
