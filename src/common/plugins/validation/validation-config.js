/**
 * [description]
 * @param  {[String]}   when     [什么时候触发校验，非必须，默认'blur']
 * @param  {[Regexp]}   rule     [校验规则，必须]
 * @param  {[String]}   errmsg   [输出信息，非必须]
 * @param  {[String]}   errClass [errDom的自定义样式名，非必须]
 * @param  {[String]}   nodeName [校验的DOM名称，非必须，默认'INPUT']
 * @param  {Function} 	success  [校验成功的回调，非必须]
 * @param  {Function} 	fail 	 [校验失败的回调，非必须]
 * @param  {Function} 	always 	 [校验成功或失败的回调，非必须]
 * @param  {String} 	notice 	 [自动显示隐藏inputNotice组件，非必须]
 */
import regRules from 'utils/regs.js';
export default {
    // 注册／登录模块
    pass: {
        rule: regRules.pass,
        // errmsg: '请输入6-18位数字、字母'
        errmsg_translate: 'common.enter6to18n',
    },
    signpass: {
        rule: regRules.signpass,
        errmsg_translate: 'common.signPwdType', // 请输入6位数字
    },
    invitationCode: {
        rule: regRules.invitationCode,
        errmsg_translate: 'common.signPwdType', // 请输入6位数字
    },
    companyName: {
        rule: regRules.companyName,
        errmsg_translate: 'common.enterActualEntName', // 请填写真实的企业名称
    },
    userName: {
        rule: regRules.userName,
        errmsg_translate: 'common.enterCorrectName', // 请输入正确的姓名
    },
    foreignerUserName: {
        rule: regRules.foreignerUserName,
        errmsg_translate: 'common.enterCorrectName', // 请输入正确的姓名 (外国人实名 SAAS-12562)
    },
    userAccount: {
        rule: regRules.userAccount,
        // errmsg: '请输入正确的账号！',
        errmsg_translate: 'login.errEmailOrTel', // 翻译， value指向 lang/zh.js中的key
    },
    userPhone: {
        rule: regRules.userPhone,
        errmsg_translate: 'common.enterCorrectPhoneNum', // 请输入正确的手机号
    },
    userEmail: {
        rule: regRules.userEmail,
        errmsg_translate: 'common.enterCorrectEmail', // 请输入正确的邮箱
    },
    phoneVerifyCode: {
        rule: regRules.phoneVerifyCode,
        errmsg: '验证码错误',
        errmsg_translate: 'login.verCodeFormatErr',
    },
    imageVerifyCode: {
        rule: regRules.imageVerifyCode,
        errmsg_translate: 'common.imgCodeErr', // 图形验证码错误
    },
    loginCode: {
        rule: regRules.loginCode,
        errmsg: '请输入6-18位数字、大小写字母',
        errmsg_translate: 'common.enter6to18n',
    },
    signCode: {
        rule: regRules.signCode,
        errmsg: '请输入6位数字',
        errmsg_translate: 'CSSeals.signPwdType',
    },
    IDCardReg: {
        rule: regRules.IDCardReg,
        errmsg_translate: 'common.enterCorrectIdNum', // 请输入正确的证件号码
    },
    weakIdCardReg: {
        rule: regRules.weakIdCardReg,
        errmsg_translate: 'common.enterCorrectIdNum', // 请输入正确的证件号码
    },
    uniSocCreditCode: {
        rule: regRules.enterCorrectFormat,
        errmsg_translate: 'common.imgCodeErr', // 请输入正确的格式
    },
    dateFormOne: {
        rule: regRules.dateFormOne,
        errmsg_translate: 'common.enterCorrectDateFormat', // 请输入正确的日期格式
    },

};

// 返回脱敏的身份证号码
export function getMaskIdCard(phone) {
    if (phone != null && phone !== undefined) {
        return phone.replace(regRules.maskIDCardReg, '$1 ************ $2');
    }
    return '';
}

// 姓名脱敏
export function getMaskName(str) {
    if (str != null && str !== undefined) {
        if (str.length <= 3) {
            return '*' + str.substring(1, str.length);// 三字之内脱敏第一个，
        } else if (str.length >= 4 && str.length <= 6) {
            return '**' + str.substring(2, str.length);// 4-6脱敏前两个
        } else if (str.length > 6) {
            return str.substring(0, 2) + '****'  + str.substring(6, str.length);// 大于6脱敏第3-6个
        }
        return str;
    } else {
        return '';
    }
}
