import store from 'src/store/store.js';
import router from 'src/router/router.js';

const utils = {
    changeViewport(type) {
        const viewport = document.querySelector('meta[name="viewport"]');
        let content = viewport.content;
        const maxScaleRegex = /,*maximum\-scale\=\d*\.*\d*/g;
        const scale = type === 'focus' ? '1.0' : '4.0';
        content = content.replace(maxScaleRegex, ',maximum-scale=' + scale);
        viewport.content = content;
    },
    getModuleValue(str) { // entInfoCheck_txt_title.text,获取模块到对应值
        const list = str.split('.');
        const config = store.state.ssoConfig;
        // 来自 store 里面的配置数据
        const moduleIdConfigData = Object.keys(config).reduce((moduleIdValues, menu) => {
            return moduleIdValues.concat(Object.values(config[menu]));
        }, []);
        // 获取该模块 属性 的配置值
        const module = moduleIdConfigData.find(item => item.moduleId === list[0]) || {};
        return module[list[1]];
    },
    getModuleText(str, text) { // 显示模块制定文案
        const configText = utils.getModuleValue(str + '.text');
        return configText || text;
    },
    goModuleUrl(str, url, openNew = false) { // 获取模块指定url
        const ssoAbsUrl = utils.getModuleValue(str + '.absUrl');
        const ssoUrl = utils.getModuleValue(str + '.url');
        const theUrl = ssoAbsUrl || (ssoUrl || url);
        if (theUrl.indexOf('http') <= -1) { // 内部地址跳转
            router.push(theUrl);
        } else if (openNew) { // 是否新打开窗口
            window.open(theUrl);
        } else {
            window.location.href = theUrl;
        }
    },
};

function Utils() {
    Vue.prototype.$utils = utils;
    Vue.$utils = utils;
}

export default Utils;
