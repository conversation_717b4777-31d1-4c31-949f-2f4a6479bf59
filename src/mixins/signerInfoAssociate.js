// 账号联想，地址簿信息查询、第三方企业名称联想
import { debounce } from 'src/common/utils/fn.js';
import resRules from 'src/common/utils/regs.js';
import { isIE } from 'src/common/utils/device.js';
import { checkEntNameFormat } from 'src/common/utils/reg';

export const signerInfoAssociateMixin = {
    data() {
        return {
            catchUserAccount: '',
            searchAccountTemp: [],
            showDataBoxInviteDialog: false,
        };
    },
    computed: {
        // 签署/抄送 是否不可切换
        isDisableSignTypeSelect() {
            return recipient => {
                const ua = recipient.userAccount ? (recipient.userAccount.split(';') || []).filter(v => !!v) : 0;
                return (recipient.ifProxyClaimer && !ua.length) || ua.length > 1;
            };
        },
    },
    methods: {
        // 获取联想账号信息
        getRelativeAccountList(account, isEnt) {
            const url = isEnt ? '/ents/contacts/query/ent/account' : '/ents/contacts/query/person/account';
            return this.$http.get(`${url}?account=${encodeURIComponent(account)}`, {
                noToast: 1,
            });
        },
        // 获取联想企业名称
        getRelativeEntList(entName) {
            return this.$http.get(`/ents/contacts/query/ent?entName=${encodeURIComponent(entName)}`, {
                noToast: 1,
            });
        },
        // 根据账号获取地址簿企业名称/姓名信息
        getAccountInfo(isPerson, userAccount) {
            // 如果企业输入了多个账号 不联想
            if (!userAccount || userAccount.indexOf(';') > -1) {
                return Promise.resolve({});
            }
            const url = isPerson ? '/ents/contacts/query/person/account' : '/ents/employees/receiver';
            return this.$http.get(`${url}?account=${userAccount}`, { noToast: 1 });
        },
        updateRecipentInfo(index, data) {
            const recipient = this.recipients[index];
            const userType = recipient.userType;
            const isPerson = userType.toLowerCase() === 'person';
            let searchedData = null;
            if (isPerson) {
                searchedData = {
                    userName: recipient.userName || data.name || '', // opz SAAS-11247, 如果接口返回fullName为空，使用用户自己输入的name
                };
            } else {
                searchedData = {
                    userName: data.name || recipient.userName,
                };
            }
            const updatedRecipient = {
                ...recipient,
                ...searchedData,
            };
            // 先点击了名字的输入框，然后再去输入手机号 联想带出名字，清掉userName的errorMsg
            if (searchedData.userName && updatedRecipient.errors.length) {
                const position = updatedRecipient.errors.findIndex(item => item.fieldName === 'userName');
                updatedRecipient.errors.splice(position, 1);
            }
            this.$set(this.recipients, index, updatedRecipient);
        },

        /**
         * 账号操作
         */
        // 账号获取焦点
        onFocusUseraccout(i) {
            const recipient = this.recipients[i];
            this.spliceErr(recipient.errors, 'userAccount');
        },
        // 修正账号格式, 返回 []
        correctAccount(i) {
            const recipient = this.recipients[i];
            const t = recipient.userAccount;
            if (recipient.userType !== 'ENTERPRISE' || recipient.receiverType !== 'SIGNER') {
                return [t];
            }
            const useraccount = t && t.replace(/；/g, ';').replace(/\s/g, '').replace(/;{1,}/g, ';');
            this.$set(this.recipients, i, {
                ...recipient,
                userAccount: useraccount,
            });
            return ((useraccount && useraccount.split(';')) || []).filter(v => !!v);
        },
        searchUser: debounce(function(i, queryString, cb) {
            if (!queryString) {
                return '';
            }
            this.$http.get(`/ents/contacts/query/person/name?name=${queryString}`).then(res => {
                const data = res.data;
                console.log(res);
                if (!data) {
                    return;
                }
                const result = data.map(person => {
                    return {
                        ...person,
                        value: person.name,
                        text: `${person.name ? `${person.name}(${person.account})` : person.account}`,
                    };
                });
                if (result.length > 0) {
                    result.unshift({
                        value: '由您的"联系人地址薄"带出',
                        text: '由您的"联系人地址薄"带出',
                    });
                }
                cb(result);
            });
        }, 200),
        selectUserName(data, index) {
            this.$set(this.recipients, index, {
                ...this.recipients[index],
                userAccount: data.account,
                userName: data.name,
            });
        },
        // 获取账号联想数据
        searchAccounts: debounce(function(i, queryString, cb) {
            if (!queryString) {
                this.recipients[i].enterpriseName && this.$http.get(`/ents/contacts/query/ent/info?entName=${encodeURIComponent(this.recipients[i].enterpriseName)}`).then(res => {
                    const data = res.data;
                    if (!data) {
                        return;
                    }
                    const result = data.map(person => {
                        return {
                            value: person.account,
                            text: `${person.name ? `${person.name}(${person.account})` : person.account}`,
                            name: person.name || '',
                        };
                    });
                    if (result.length > 0) {
                        result.unshift({
                            value: '由您的"联系人地址薄"带出',
                            text: '由您的"联系人地址薄"带出',
                        });
                    }
                    cb(result);
                });
                return;
            }
            const userAccountArr = this.correctAccount(i);

            const lastAccount = userAccountArr[userAccountArr.length - 1];

            this.searchAccountTemp = userAccountArr;
            // 如果企业输入了多个账号 不联想, 相当于企业只有输入一个账号的时候才会联想
            if (userAccountArr.length > 1) {
                return;
            }

            this.getRelativeAccountList(lastAccount, this.recipients[i].userType === 'ENTERPRISE')
                .then((res) => {
                    const data = res.data;
                    if (!data) {
                        return;
                    }
                    const result = data.map(person => {
                        return {
                            value: person.account,
                            text: `${person.name ? `${person.name}(${person.account})` : person.account}`,
                            name: person.name || '',
                        };
                    });
                    if (result.length > 0) {
                        result.unshift({
                            value: '由您的"联系人地址薄"带出',
                            text: '由您的"联系人地址薄"带出',
                        });
                    }
                    cb(result);
                });
        }, 200),

        // 选择账号
        selectUserAccount(data, index) {
            if (this.recipients[index].userAccount === data.value) { // 如果选中值和当前输入值相同则不需要走选中逻辑，会默认走blur
                return;
            }
            if (data.value === '由您的"联系人地址薄"带出') { // 规避提示信息选择
                return;
            }
            this.spliceErr(this.recipients[index].errors, 'userAccount');

            const acUpdate = this.searchAccountTemp.slice(0, this.searchAccountTemp.length - 1);
            acUpdate.push(data.value);

            this.$set(this.recipients, index, {
                ...this.recipients[index],
                userAccount: acUpdate.join(';'),
            });
            // 选中值时，blur会先执行，这里补充执行逻辑
            this.onBlurUseraccout(index, data);
        },
        // 账号失去焦点
        onBlurUseraccout(i) {
            const that = this;
            const recipient = this.recipients[i];
            const userAccountArr = this.correctAccount(i);

            if (this.isSelectProxy && this.isSelectProxy(recipient)) {
                return;
            }

            if (!userAccountArr || !userAccountArr.length) {
                that.pushErr(recipient, 'userAccount', that.$t('addReceiver.noAccountErr'));
                return;
            }
            if (userAccountArr.some(t => t && !resRules.userAccount.test(t))) {
                that.pushErr(recipient, 'userAccount', that.$t('addReceiver.accountFormatErr'));
                return;
            }
            if (userAccountArr.length > 5 && recipient.userType === 'ENTERPRISE') {
                that.pushErr(recipient, 'userAccount', '接收手机/邮箱不能超过5个');
                return;
            }

            that.spliceErr(recipient.errors, 'userAccount');

            // if (!recipient.ifProxyClaimer && !recipient.requireEnterIdentityAssurance && data) {
            //     // 没有用前台代收，请求account信息并更新个人姓名、userId等信息
            //     that.updateRecipentInfo(i, data);
            // }
        },

        /**
         * 公司名称操作
         */
        onFetchSuggestions: debounce(function(i, queryString, cb) {
            const recipient = this.recipients[i];
            if (!recipient.enterpriseName && isIE()) {
                this.$set(this.recipients, i, {
                    ...recipient,
                    enterpriseName: ' ',
                });
            }
            const rupattn = /[\u0400-\u04ff]+/;
            const qs = queryString.trim();

            if (recipient.userAccount && rupattn.test(qs)) {
                // 俄文不校验
            } else if (!recipient.ifProxyClaimer && (qs && checkEntNameFormat(qs))) { // 如果没有用前台代收，则校验账号
                return;
            }

            if (!qs && !recipient.ifProxyClaimer) { // 未输入名称时去查地址薄，前台代收不做查询
                this.getAccountInfo(false, recipient.userAccount)
                    .then(res => {
                        const { enterprises = [] } = res.data || {};
                        const entList = [...enterprises.filter(a => a.entName)];
                        if (entList.length > 0) {
                            entList.unshift({
                                entName: '邀请您的合作伙伴', // 地址薄中数据标识,
                            });
                        }
                        const results = entList.map(item => {
                            return {
                                value: `${item.entName}`,
                                item: item,
                            };
                        });
                        cb(results);
                    });
            } else { // 输入名称后去联想工商局信息，由于接口输入单个字符时不返回数据，这里做过滤
                this.getRelativeEntList(qs)
                    .then(res => {
                        if (!res.data) {
                            return;
                        }
                        // 未注册企业的账号，data返回为空，清空经办人信息
                        const result = res.data.map(name => {
                            return {
                                value: name,
                            };
                        });
                        result.length && result.unshift({
                            value: '邀请您的合作伙伴',
                        });
                        cb(result);
                    });
            }
        }, 200),
        // 选择企业
        selectEnpName(data) {
            console.log(data);
            const i = this.selectedIndex;
            const recipient = this.recipients[i];
            if (data.value === '邀请您的合作伙伴') { // 规避提示信息选择
                this.showDataBoxInviteDialog = true;
                this.$set(this.recipients, i, {
                    ...recipient,
                    enterpriseName: '',
                });
                return;
            }
            const { entId, entName, portraitUrl } = data.item || {};

            if (entId) {
                this.$set(this.recipients, i, {
                    ...recipient,
                    enterpriseId: entId,
                    enterpriseName: entName,
                    photoHref: portraitUrl,
                });
            } else {
                this.$set(this.recipients, i, {
                    ...recipient,
                    enterpriseName: data.value,
                });
            }

            this.spliceErr(recipient.errors, 'enterpriseName');
        },
        onFocusEntName(i) {
            const recipient = this.recipients[i];
            this.selectedIndex = i;
            this.spliceErr(recipient.errors, 'enterpriseName');
        },
        onBlurEnpName() {
            const that = this;
            const i = this.selectedIndex;
            const recipient = that.recipients[i];
            const enterpriseName = recipient.enterpriseName.trim();
            const errorMsg = checkEntNameFormat(enterpriseName);
            if (errorMsg) {
                that.pushErr(recipient, 'enterpriseName', errorMsg);
            } else {
                that.spliceErr(recipient.errors, 'enterpriseName');
            }
        },
    },
};
