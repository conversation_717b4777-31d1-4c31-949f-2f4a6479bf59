/**
 * @desc 合同标签相关接口
 * @type {{data(): *, methods: {getTagOptions(*=): 获取设置标签按钮的权限, getTagManageBtnVisible(contractId): 获取合同的tag信息, getContractTagInfo(isBarch): 获取当前可添加的tag}}}
 */
export const tagManageMixin = {
    data() {
        return {
            showTagManageBtn: false, // 是否显示设置标签按钮
            contractTagsInfo: [],
            selectedTagParams: {
                dialogTitle: this.$t('mixin.setLabel'),
                tagOptions: [],
                addedTagIds: [],
            },
        };
    },
    methods: {
        // 获取设置标签按钮可见性
        postTagManageBtnVisible(contractId) {
            return this.$http.post('/contract-center-bearing/business-tags/show-setting-tags', {
                externalContractId: contractId,
            });
        },
        // 获取合同tag信息
        getContractTagInfo(contractId) {
            return this.$http.post('/contract-center-bearing/business-tags/contract', {
                externalContractId: contractId,
            });
        },
        getTagOptions(contractId) {
            return this.$http.post('/contract-center-bearing/business-tags/sender-tags', {
                externalContractId: contractId,
            });
        },
        // 设置标签后更新标题中的标签信息
        onManageTagCallback(selectedTags) {
            this.contractTagsInfo = [...selectedTags]; // 根据设置结果更新页面的标签信息
        },
        // 初始化tag信息
        initTagInfo(contractId) {
            return this.postTagManageBtnVisible(contractId)
                .then(res => {
                    this.showTagManageBtn = res.data.data.enabled || false;
                    // 有权限才需要继续查询已添加标签和可添加标签的信息，
                    // 在点击按钮时需要知道当前有没有可添加标签，没有的话需要加提示，所以在这里统一去查可添加标签
                    if (this.showTagManageBtn) {
                        Promise.all([
                            this.getContractTagInfo(contractId),
                            this.getTagOptions(contractId),
                        ]).then(resp => {
                            this.contractTagsInfo = [...resp[0].data.data.tags];
                            this.selectedTagParams.tagOptions = [...resp[1].data.data];
                        }).catch(() => {
                        }).finally(() => {
                            this.selectedTagParams = {
                                ...this.selectedTagParams,
                                contractId,
                                addedTagIds: this.contractTagsInfo.map(a => a.tagId),
                                callback: this.onManageTagCallback,
                            };
                        });
                    }
                });
        },
    },
};
