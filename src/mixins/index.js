// 创建企业，切换到企业实名
export const createEnterpriseMinxin = {
    methods: {
        goToCreateEnterprise() {
            this.$http.post('/authenticated/enterprise-register').then(res => {
                // 自动切换到企业，需要替换 token
                this.$token.save(res.data.access_token, res.data.refresh_token);
                this.$MessageToast.success(this.$t('mixin.createSuccessful'));
                this.$router.push('/ent/sign/guide');
            });
        },
        // 引导企业去实名。如果企业都已实名，新建一个企业，去实名。如果存在未存在未实名身份A, 切换到未实名身份A，去实名
        async switchIdentityAndAuth() {
            const { isCanCreateEnterprise } = this.$store.state.commonHeaderInfo;
            // 企业都已实名，isCanCreateEnterprise = true 表示需要新建企业
            if (isCanCreateEnterprise) {
                return this.$http.post('/authenticated/enterprise-register').then(res => {
                    // 自动切换到企业，需要替换 token
                    this.$token.save(res.data.access_token, res.data.refresh_token);
                    // this.$MessageToast.success(this.$t('mixin.createSuccessful'));
                    return Promise.resolve();
                });
            }
            const { data } = await this.$http.get('/users/chosen-enterprises');
            const [notAuthentEnterprise] = data.filter(item => ('' + item.entId) !== '0' && ('' + item.authStatus) !== '2');

            // 存在未实名身份A, 切换到未实名身份A
            if (notAuthentEnterprise && notAuthentEnterprise.entId) {
                return this.$http.post('/authenticated/switch-ent', {
                    entId: notAuthentEnterprise.entId,
                    refreshToken: this.$cookie.get('refresh_token'),
                }).then(({ data }) => {
                    this.$token.save(data.access_token, data.refresh_token);
                    return Promise.resolve();
                });
            }
        },
    },
};
