import { throttle } from 'src/common/utils/fn.js';
import { openPage } from 'common_pages/doc/list/list.js';

export const docOperationMixin = {
    data() {
        return {
        };
    },
    methods: {
        // 认领合同，完成后需要确认主体并进去签署页
        handleClaim({ contractId, receiverId, entId }) {
            this.$http.post(`/contract-api/contracts/claim-in-web/${contractId}?claimEntId=${entId}&receiverId=${receiverId}`)
                .then(res => {
                    const { code, message } = res.data || {};
                    if (code === '140001') {
                        return this.handleSwitchSubject({ type: 'SIGN' }, contractId, 'CLAIM'); // 认领成功后，type为sign
                    } else {
                        this.$MessageToast.error(message);
                    }
                });
        },
        // 代理签署认领，切换身份
        handleProxySign(item, contractId) {
            this.$http.post(`/contract-api/contracts/proxy-sign/ready-to-do-proxy-sign?contractId=${contractId}`)
                .then(() => {
                    this.handleSwitchSubject(item, contractId);
                });
        },
        // 切换主体
        handleSwitchSubject: throttle(
            async function(item, contractId, scene) {
                const type = item.type.toLowerCase();
                const { data } = await this.$http.get(`/contract-api/contracts/${contractId}/my-receivers`, {
                    params: {
                        type,
                    },
                });
                // 需要认领的合同，且该合同开通签署前审批流程时，查询到的签署人列表为[],不再进行身份主体的切换
                if (!data || !data.length) {
                    if (scene === 'CLAIM') {
                        this.$MessageToast.success('认领成功');
                        setTimeout(() => {
                            window.location.reload();
                        }, 3000);
                    }
                    return;
                }

                const entList = data.reduce((sum, item) => {
                    const enterpriseId = item.enterpriseId;
                    // entId === '0'表示个人
                    const entName = (enterpriseId === '0' ? item.userName : item.enterpriseName) || item.userAccount;
                    // 去重
                    if (!sum.find(sumItem => sumItem.entId === enterpriseId)) {
                        sum.push({
                            entId: enterpriseId,
                            entName,
                        });
                    }
                    return sum;
                }, []);

                if (entList.length === 1) {
                    return this.handlePush(entList[0], item, contractId);
                }
                this.handleShowDialogSwitchSubject(entList, item);
            }, 1500,
        ),
        // 签署、审批按钮跳转到对应页面，跳转前需要切换身份
        async handlePush(entObj, item, contractId) {
            const { entId, entName } = entObj;
            let url = `/sign/signing?contractId=${contractId}`;
            if (item.type === 'APPROVAL') { // 审批
                url = `${url}&type=approval`;
            }

            if (item.type === 'SIGN' && entId === '1000000000000000000') {
                // 当前企业主体不存在
                // 判断当前账号是否存在未实名的企业，不存在的话新建企业，存在的话切到未实名的企业
                await this.switchIdentityAndAuth();
            } else if (entId !== this.currentEntId) {
                url = `${url}&entId=${entId}&entName=${encodeURIComponent(entName)}`;
            }
            // isContinueNext 是否是继续审批下一份操作
            openPage({ ...entObj, url, contractId, operation: item.type, continueNextType: item.from });
        },
    },
};
