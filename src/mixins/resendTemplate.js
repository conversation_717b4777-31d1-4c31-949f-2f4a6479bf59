// 模板发送的合同重新发起，使用模板的一些公共参数
export const resendTemplateMinxin = {
    computed: {
        queryContractId() {
            // 合同管理&合同详情：重新发起入口，跳过来会带之前的合同ID，使用模板请求到的接口都会带上这个参数
            return this.$route.query.contractId ? this.$route.query.contractId : '';
        },
    },
    methods: {
        handleRequestLink(url) {
            if (this.queryContractId) {
                const temStr = `contractId=${this.queryContractId}`;
                if (url.includes('?')) {
                    url = `${url}&${temStr}`;
                } else {
                    url = `${url}?${temStr}`;
                }
            }
            return url;
        },
    },
};
