// 多业务线相关 mixin
export const bizLineMixin = {
    data() {
        return {
            bizLineList: [],
            selectedLineEntId: null,
        };
    },
    methods: {
        checkHasBizLine(entName, filterByCurUser = false) {
            return new Promise((resolve) => {
                this.$http(`/ents/business-line/list-by-name?entName=${encodeURIComponent(entName)}&filterByCurrentUser=${filterByCurUser}`)
                    .then(res => {
                        if (res.data.length) {
                            this.bizLineList = res.data;
                            resolve(true);
                        } else {
                            resolve(false);
                        }
                    });
            });
        },
    },
};
