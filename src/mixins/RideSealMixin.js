// 骑缝章相关操作mixin
import { DEFAULT_RS_HEIGHT, calcRideSeal } from 'utils/decorate.js';
export const RideSealMixin = {
    data() {
        return { };
    },
    methods: {
        // 骑缝章数据提交到服务端, yData计算后的百分比位置，yPos像素高度
        updateRidingSeal(y, i) {
            const {
                labelId,
                height,
                width,
                roleName,
            } = this.ridingSealList[i];
            this.$http.post(`${signPath}/contracts/${this.contractId}/labels/update-decorate-ride-seal`, {
                contractId: this.contractId,
                labelId,
                height,
                width,
                y, // 骑缝章左下角据据页面左下角的距离
            }).then((res) => {
                // 更新数据
                this.$set(this.ridingSealList, i, {
                    ...res.data,
                    roleName,
                });
            });
        },
        deleteRidingSeal(ridingSeal, index) {
            const { labelId } = ridingSeal;
            this.$http.delete(`${signPath}/contracts/${this.contractId}/labels/${labelId}`)
                .then(() => {
                    this.ridingSealList.splice(index, 1);
                });
        },
        createRidingSeal() {
            if (this.docList.every(doc => (doc.page || []).length <= 1)) {
                return this.$MessageToast.error('单页文档无法添加骑缝章');
            }
            const ridingSealList = this.ridingSealList;
            const {
                receiverId,
            } = this.receivers[this.receiverIndex] || {};
            if (ridingSealList.find(item => item.receiverId === receiverId)) {
                return;
            }
            const pageInfo = this.docList[0].page[0];
            const y = calcRideSeal(ridingSealList, pageInfo.height);
            this.$http.post(`${signPath}/contracts/labels/add-decorate-ride-seal`, {
                contractId: this.contractId,
                receiverId,
                height: DEFAULT_RS_HEIGHT / pageInfo.height,
                width: DEFAULT_RS_HEIGHT / pageInfo.width,
                y,
            }).then(({
                data,
            }) => {
                this.ridingSealList.push({
                    ...data,
                    roleName: this.receivers[this.receiverIndex].userName,
                });
            }).catch(() => {

            });
        },
    },
};
