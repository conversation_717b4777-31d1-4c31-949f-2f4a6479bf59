// 高级功能试用 mixin
export const advancedFeatureMixin = {
    data() {
        return {
        };
    },
    methods: {
        /**
         * * 获取试用功能状态信息
         * @param {String} key 页面中trialFeatureData对象数据的key
         * @param {Map} trialFeatureData  页面中涉及高级功能的数据
         * @param {Number} status 查询展示状态（1：是否显示tooltip信息 2：是否显示new信息 3：tootip文案显示）
         *
         */
        checkTrialInfoStatus(key, trialFeatureData, status) {
            if (Object.keys(trialFeatureData).includes(key)) {
                const trialData = trialFeatureData[key];
                if (status === 1) { // 是否显示tooltip信息
                    return trialData.onTrial === 1 || trialData.onTrial === 2;
                } else if (status === 2) { // 是否显示new信息
                    return !trialData.isOpen && trialData.onTrial === 0;
                } else if (status === 3) { // tootip文案显示
                    return trialData.onTrial === 1 ? this.$t('functionSupportDialog.trialRemainDayTip', { day: trialData.daysRemaining }) : this.$t('functionSupportDialog.trialEndTip');
                }
            }
            return false;
        },
        /**
         * * 检测试用功能信息
         * @param {Map} trialData  该项高级功能的数据
         *
         */
        checkTrialInfo(trialData) {
            if (!trialData.isOpen) { // 未开启
                if (trialData.onTrial === 0 || trialData.onTrial === 2) { // (0:未试用，1：试用中，2：试用到期)
                    this.$featureSupport({ featureId: trialData.featureId, type: 'trial' });
                    return false;
                }
            }
            return true;
        },
        /**
         * * 试用tooltip点击
         * @param {String} key 页面中trialFeatureData对象数据的key
         * @param {Map} trialFeatureData  页面中涉及高级功能的数据
         *
         */
        handleTrialClick(trialFeatureData, key) {
            if (Object.keys(trialFeatureData).includes(key)) {
                const trialData = trialFeatureData[key];
                this.$featureSupport({ featureId: trialData.featureId, type: 'trial' });
            }
        },
    },
};
