// 将一些后端不方便处理的文案转换成当前语言环境的文案
export const stringLangTransformMixin = {
    data() {
        return {};
    },
    methods: {
        handleRoleNameTransform(data, rList, tList) {
            let transformString;

            if (!this.$i18n.locale || this.$i18n.locale === 'zh' || !rList || !tList) {
                return data;
            }

            try {
                transformString = JSON.stringify(data);
                rList.forEach((replaceItem, index) => {
                    const translateItem = tList[index];
                    const reg = new RegExp(`"${replaceItem}"`, 'g');
                    transformString = transformString.replace(reg, `"${translateItem}"`);
                });
            } catch (error) {
                console.error('data can not be stringify');
                return data;
            }
            return JSON.parse(transformString);
        },
    },
};
