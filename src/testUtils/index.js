import { createLocalVue, shallowMount } from '@vue/test-utils';
import ElementUI from 'element-ui';
import defaultsDeep from 'lodash/defaultsDeep';
import Vuex from 'vuex';

const localVue = createLocalVue();
localVue.use(ElementUI);
localVue.use(Vuex);

export function createStore(options = {}) {
    const defaultOptions = {
        state: {
        },
        modules: {
        },
    };
    return new Vuex.Store(defaultsDeep(options, defaultOptions));
}

const MessageToast = jest.fn().mockImplementation(t => t);
['success', 'warning', 'info', 'error', 'loading'].forEach(type => {
    MessageToast[type] = jest.fn().mockReturnValue({
        clear: jest.fn().mockImplementation(t => t),
    });
});

function createMockVueMethod(obj = {}, attribute = []) {
    attribute.forEach(type => {
        obj[type] = jest.fn().mockImplementation(t => t);
    });
    return obj;
}
export function createWrapper(component, store = createStore(), options = {}) {
    const defaultOptions = {
        mocks: {
            $t: jest.fn().mockImplementation((t) => t),
            $tc: jest.fn().mockImplementation((t) => t),
            $localStorage: createMockVueMethod({}, ['set', 'get']),
            $MessageToast: MessageToast,
            $token: createMockVueMethod({}, ['save', 'delete']),
            $cookie: createMockVueMethod({}, ['set', 'get', 'delete']),
        },
        localVue,
        store,
    };
    return shallowMount(component, defaultsDeep(options, defaultOptions));
}

export function initWrapper(component, storeObj, options = {}) {
    const store = createStore(storeObj);
    const defaultOptions = {
        mocks: {
            $t: jest.fn().mockImplementation((t) => t),
            $tc: jest.fn().mockImplementation((t) => t),
        },
        localVue,
        store,
    };
    return shallowMount(component, defaultsDeep(options, defaultOptions));
}

export const locationReplaceMock = jest.fn();
export const locationReloadMock = jest.fn();
export function mockUseLocation(mockHref = 'http://localhost:8080', hash = '123', search = '') {
    // Not implemented: navigation (except hash changes)
    // location mock
    delete window.location;
    Object.defineProperty(window, 'location', {
        value: {
            href: mockHref,
            hash,
            origin: mockHref,
            search,
            replace: locationReplaceMock,
            reload: locationReloadMock,
        },
    });
}
export function MockFile() { }
MockFile.prototype.create = function(name, size, mimeType) {
    name = name || 'mock.txt';
    size = size || 1024;
    mimeType = mimeType || 'plain/txt';

    function range(count) {
        var output = '';
        for (var i = 0; i < count; i++) {
            output += 'a';
        }
        return output;
    }

    var blob = new Blob([range(size)], { type: mimeType });
    blob.lastModifiedDate = new Date();
    blob.name = name;

    return blob;
};
