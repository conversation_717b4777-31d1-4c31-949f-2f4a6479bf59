<template>
    <main :class="[`${lang}-page`, getIsForeignVersion ?'ja-page-version-front-end':'']">
        <transition :name="transitionName">
            <router-view></router-view>
        </transition>
        <HelperFloat v-if="helpVisible && lang === 'zh'"></HelperFloat>
        <ActivityFloat v-if="showActivityFloat"></ActivityFloat>
    </main>
</template>

<script>
import HelperFloat from 'components/helperFloat/HelperFloat.vue';
import ActivityFloat from 'components/anniversaryFloat/index.vue';
import { isPC } from 'src/common/utils/device.js';
import { mapGetters } from 'vuex';
export default {
    components: {
        HelperFloat,
        ActivityFloat,
    },
    data() {
        return {
            isPC: isPC(),
            transitionName: '',
        };
    },
    computed: {
        ...mapGetters(['getSsoConfig', 'getIsForeignVersion']),
        helpVisible() {
            return this.isPC && (!this.getSsoConfig.frame || this.getSsoConfig.frame.frame_help.visible);
        },
        lang() {
            return this.$i18n.locale;
        },
        showActivityFloat() {
            return !this.getIsForeignVersion && this.lang === 'zh' && !this.$route.meta.noActivityFloat;
        },
    },
    watch: {
        lang: {
            handler(val) {
                const html = document.documentElement;
                if (val === 'ja') {
                    html.classList.add('ja-font');
                } else {
                    html.classList.remove('ja-font');
                }
                // if (val !== 'ar' && location.origin.includes('localhost')) {
                if (val === 'ar') {
                    html.setAttribute('dir', 'rtl');
                } else if (html.getAttribute('dir') === 'rtl') {
                    html.setAttribute('dir', 'ltr');
                }
            },
            immediate: true,
        },
    },
    created() {
        const _this = this;
        // storage 事件只在其他窗口或标签页修改 localStorage 中的值时才会触发。
        // 如果当前窗口或标签页修改了 localStorage 中的值，则不会触发 storage 事件。
        window.addEventListener('storage', function(event) {
            // ie浏览器当前窗口也会触发，需要判断是否是当前窗口
            if (document.hasFocus()) {
                return;
            }
            const { key, oldValue, newValue } = event;
            if (key === 'currentTabEntId' && !!oldValue && oldValue !== newValue) {
                console.log('currentTabEntId has changed:', newValue);
                _this.$confirm('请刷新页面。', '提示', {
                    showCancelButton: false,
                    confirmButtonText: '刷新',
                }).then(() => {
                    window.location.reload();
                }).catch(() => {
                    window.location.reload();
                });
            }
        });
    },
};
</script>

<style lang="scss">
	@include footerFollow;
	.fade-enter {
		opacity: 0;
		transform: translateY(2000px);
	}
	.fade-enter-active {
		opacity: 1;
		transition: all 0.5s ease-in;
	}
	.fade-out-leave {
		opacity: 1;
	}
	.fade-out-leave-active {
		opacity: 0;
		transform: translateY(2000px);
		transition: all 0.5s ease-in;
	}
</style>
