<template>
    <!-- 设置标签对话框 -->
    <el-dialog
        :title="params.dialogTitle"
        :visible.sync="show"
        size="tiny"
        :before-close="handleClose"
        class="tag-manage el-dialog-bg"
    >
        <div slot="title" class="tag-manage-title">
            <h3>{{ params.dialogTitle }}</h3>
        </div>

        <el-checkbox-group v-if="params.contractId" v-model="selectedTagIds">
            <el-checkbox
                :disabled="
                    selectedTagIds.length === 6 && !selectedTagIds.includes(tag.tagId)
                "
                :label="tag.tagId"
                v-for="tag in params.tagOptions"
                :key="tag.tagId"
            >
                <ContractTag :tag="tag"></ContractTag>
            </el-checkbox>
        </el-checkbox-group>
        <template v-else>
            <div
                class="tag-manage__ent-module"
                v-for="(ent, index) in tagsInfoNotEmpty"
                :key="index"
            >
                <h3>{{ ent.enterpriseName }}：</h3>
                <el-checkbox-group v-model="selectedTagIds">
                    <el-checkbox
                        :disabled="
                            selectedTagIds.length === 6 && !selectedTagIds.includes(tag.tagId)
                        "
                        :label="tag.tagId"
                        v-for="tag in ent.tags"
                        :key="tag.tagId"
                    >
                        <ContractTag :tag="tag"></ContractTag>
                    </el-checkbox>
                </el-checkbox-group>
            </div>
        </template>
        <div slot="footer" class="dialog-footer">
            <el-button @click="handleClose">{{ $t("docDialog.cancel") }}</el-button>
            <el-button
                :loading="confirmLoading"
                type="primary"
                @click="handleConfirm"
            >{{ $t("docDialog.confirm") }}</el-button>
        </div>
    </el-dialog>
</template>

<script>
import ContractTag from 'components/contractTag/ContractTag.vue';

export default {
    name: 'TagManage',
    components: {
        ContractTag,
    },
    props: {
        params: {
            type: Object,
            default: () => ({
                contractId: '', // contractId标记是否是批量：详情、签署会传，列表不会传值。标记是否增删都支持，合同列表中仅支持添加
                dialogTitle: this.$t('tagManage.title'),
                selectedLines: [],
                addedTagIds: [],
                tagOptions: [], // tag选项
                callBack: () => {},
            }),
        },
        sensorsTrackContractInfo: {
            default: () => {},
            type: Object,
        },
    },
    data() {
        return {
            show: true,
            confirmLoading: false,
            selectedTagIds: [],
        };
    },
    computed: {
        tagsInfoNotEmpty() {
            return this.params.tagOptions.filter((ent) => ent.tags.length > 0); // 过滤无标签的企业
        },
    },
    methods: {
        // 关闭弹窗
        handleClose() {
            this.$emit('close');
        },
        handleConfirm() {
            this.confirmLoading = true;
            const { callback } = this.params;
            if (!this.params.contractId) {
                // 合同列表批量添加标签
                this.$http
                    .post('/contract-center-bearing/business-tags/batch-stick', {
                        externalContractIds: this.params.selectedLines.map(
                            (a) => a.contractId,
                        ),
                        tags: this.selectedTagIds,
                    })
                    .then((res) => {
                        this.confirmLoading = false;
                        this.handleClose();
                        callback(res.data);
                    });
            } else {
                // 签署页、详情页设置标签
                this.$sensors.track({
                    eventName: 'Ent_ContractSign_BtnClick',
                    eventProperty: {
                        page_name: '合同签署页',
                        first_category: '设置标签',
                        icon_name: '确认',
                        ...this.sensorsTrackContractInfo,
                    },
                });
                this.$http
                    .post('/contract-center-bearing/business-tags/reset-contract-tags', {
                        externalContractId: this.params.contractId,
                        tags: this.selectedTagIds,
                    })
                    .then(({ data }) => {
                        this.confirmLoading = false;
                        this.handleClose();
                        callback(data.data || []);
                    });
            }
        },
    },
    mounted() {
        // 合同列表只支持增加，不需要初始化选中值，
        this.selectedTagIds = !this.params.contractId
            ? []
            : [...this.params.addedTagIds];
    },
};
</script>

<style lang="scss">
.tag-manage {
    font-size: 12px;
    .el-dialog {
        width: 560px;

        .tag-manage-title {
            display: inline-block;
            h3 {
                font-size: 16px;
                color: #1c1d1e;
                line-height: 20px;
            }
            p {
                color: #989898;
                line-height: 16px;
            }
        }
        &__body {
            overflow-y: auto;
            padding: 10px 15px !important;
            max-height: 240px;
            background: #f6f9fc;
            .el-checkbox-group {
                .el-checkbox {
                    width: 45%;
                    line-height: 40px;
                    &__label {
                        padding-left: 10px;
                        [dir="rtl"] & {
                            padding-left: 0;
                            padding-right: 10px;
                        }
                    }
                    .tag-item {
                        /*display: inline-flex;*/
                        vertical-align: sub;
                    }
                    &:nth-child(1) {
                        margin-left: 15px;
                        [dir="rtl"] & {
                            margin-left: 0;
                            margin-right: 15px;
                        }
                    }
                }
            }
            .tag-manage__ent-module {
                padding-bottom: 15px;
                h3 {
                    padding-left: 15px;
                    margin-bottom: 5px;
                    font-size: 12px;
                    color: #333333;
                    line-height: 14px;
                    [dir="rtl"] & {
                        padding-left: 0;
                        padding-right: 15px;
                    }
                }
                .el-checkbox {
                    width: 30%;
                    line-height: 32px;
                }
            }
        }
        &__footer {
            background: #f6f9fc;
            .el-button {
                height: 34px;
                padding: 0 21px;
            }
        }
    }
}
</style>
