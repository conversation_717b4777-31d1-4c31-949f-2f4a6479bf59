<template>
    <el-dialog
        class="add-ent-dialog"
        :show-close="false"
        :visible.sync="dialogVisible"
        :before-close="handleClose"
    >
        <p class="title">输入企业名称</p>
        <el-input
            placeholder="请输入您的手机号"
            v-model="mobile"
            @blur="checkMobile"
        ></el-input>
        <span class="error">{{ mobileError }}</span>
        <el-input
            placeholder="请输入您的企业名称"
            v-model="entName"
            @blur="checkEnt"
        ></el-input>
        <span class="error">{{ entNameError }}</span>
        <span class="tips">* 请输入企业全称，确保与工商局登记的企业名称完全一致</span>
        <el-button class="btn-type-one" @click="next">确定</el-button>
    </el-dialog>
</template>

<script>
import resRules from 'utils/regs.js';
export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['type'],
    data() {
        return {
            dialogVisible: true,
            mobile: '',
            entName: '',
            mobileError: '',
            entNameError: '',
        };
    },
    computed: {
        btnDisabled() {
            return this.mobileError !== '' || this.entNameError !== '';
        },
    },
    methods: {
        handleClose() {
            this.$emit('close');
            this.dialogVisible = true;
        },
        next() {
            this.checkEnt();
            this.checkMobile();
            !this.btnDisabled && (this.$router.push(`/ent/register?account=${this.mobile}&entName=${encodeURI(this.entName)}&type=${this.type}`));
        },
        checkMobile() {
            if (this.mobile.trim() === '') {
                this.mobileError = '请输入手机号';
            } else if (!resRules.userPhone.test(this.mobile)) {
                this.mobileError = '请输入正确的手机号';
            } else {
                this.mobileError = '';
            }
        },
        checkEnt() {
            if (this.entName.trim() === '') {
                this.entNameError = '请输入企业名称';
            } else {
                this.entNameError = '';
            }
        },
    },
};
</script>

<style lang="scss">
    .add-ent-dialog .el-dialog{
        width: 300px;
        border-radius: 20px;
        .el-dialog__body{
            height: unset !important;
        }
        .el-dialog__header{
            display: none;
        }
        .title{
            font-size: 16px;
            color: #000;
            text-align: center;
            margin-bottom: 20px;
            display: block;
        }
        .el-input{
            width: 250px;
            margin-bottom: 10px;
        }
        .tips{
            color: #999;
            font-size: 12px;
            display: block;
            margin-bottom: 15px;
            &+button{
                width: 260px;
                line-height: 40px;
                padding: 0;
                margin: 0;
            }
        }
        .error{
            color: red;
            font-size: 12px;
            position: relative;
            top: -5px;
        }
    }
</style>
