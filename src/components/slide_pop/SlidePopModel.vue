<template>
    <div>
        <template v-if="reset">
            <div class="slide-pop-box" v-if="outsideShow">
                <div class="bg-click" @click="changeShowStatus(1)"></div>
                <div class="slide-pop-model slide-pop-animated" :class="animatedClass">
                    <i class="slide-pop-close el-icon-ssq-delete" @click="changeShowStatus(0)"></i>
                    <slot name="slide-pop-content"></slot>
                </div>
            </div>
        </template>
        <template v-else>
            <div class="slide-pop-box" v-show="outsideShow">
                <div class="bg-click" @click="changeShowStatus(1)"></div>
                <div class="slide-pop-model slide-pop-animated" :class="animatedClass">
                    <i class="slide-pop-close el-icon-ssq-delete" @click="changeShowStatus(0)"></i>
                    <slot name="slide-pop-content"></slot>
                </div>
            </div>
        </template>
    </div>
</template>
<script>
export default {
    props: {
        'outsideShow': {
            type: Boolean,
        },
        'needReset': { // :needReset="1",强制每次show的时候重置，:needReset="0"，根据关闭方式来判断是否需要重置
            type: Number,
            default: 0,
        },
    },
    data() {
        return {
            reset: true,
            animatedClass: 'fadeInRight',
        };
    },
    watch: {
        outsideShow: function() { // v-show
            if (this.outsideShow) {
                this.animatedClass = 'fadeInRight';
            } else {
                this.animatedClass = 'fadeOutRight';
            }
        },
        needReset: function(val) {
            if (+val === 0) {
                this.reset = false;
            } else {
                this.reset = true;
            }
        },
    },
    methods: {
        changeShowStatus(f) {
            const _this = this;
            _this.animatedClass = 'fadeOutRight';

            if (+_this.needReset === 0) {
                if (f === 1) {
                    _this.reset = false;
                } else {
                    _this.reset = true;
                }
            } else {
                _this.reset = true;
            }

            setTimeout(function() {
                _this.$emit('update:outsideShow', !_this.outsideShow);
            }, 200);
        },
    },
};
</script>
<style lang="scss">
	$uc-header-height: 63px;
	$uc-footer-height: 35px;
	$left-padding: 17px;

	.slide-pop-box {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 1111;
		.bg-click {
			width: 100%;
			height: 100%;
		}
	}
	.slide-pop-animated {
		animation-duration: .3s;
		animation-fill-mode: both;
	}

	.slide-pop-model {
		position: fixed;
		top: $uc-header-height;
		bottom: $uc-footer-height;
		right: 0;
		width: 300px;
		background-color: #fff;
		border-left: 1px solid #e2e2e2;
		box-shadow: -5px 0 5px -3px #e8e8e8;
		animation-duration: .3s;
		z-index: 1;
		overflow-y: auto;
		box-shadow: none\9;

		i.slide-pop-close {
			position: absolute;
			top: 19px;
			right: $left-padding;
			font-size: 17px;
			color: #d1d1d1;
			cursor: pointer;
			z-index: 1;
			&:hover {
				color: #666;
			}
		}
		.slide-pop-head {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			padding-left: $left-padding;
			height: 55px;
			line-height: 55px;
			border-bottom: 1px solid $border-color;
			font-size: 14px;

			h1{
				font-size: 14px;
				font-weight: bold;
			}
		}
		.slide-pop-body {
			position: absolute;
			top: 55px;
			bottom: 80px;
			width: 100%;
			// height: 100%;
			overflow-y: auto;
			// padding-left: $left-padding;
			padding-bottom: 0px;
			font-size: 14px;
			color: #000;

			.slide-pop-body-section{
				margin-bottom: 18px;
				.slide-pop-body-section-title{
					margin-bottom: 10px;
					font-size: 12px;
				}
			}
		}
		.slide-pop-foot {
			position: absolute;
			bottom: 0;
			right: 0;
			width: 100%;
			height: 80px;
			text-align: center;
			background-color: #f8f8f8;
			line-height: 80px;
		}
	}
</style>
