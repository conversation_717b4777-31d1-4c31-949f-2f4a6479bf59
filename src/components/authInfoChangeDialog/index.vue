<template>
    <el-dialog
        class="auth-change-dialog"
        :show-close="false"
        :visible.sync="dialogVisible"
        :close-on-click-modal="false"
        :title="$t('authInfoChange.title')"
    >
        <div class="top-container">
            <p>{{ $t('authInfoChange.warningTip.tip1', { entName: authChangeData.entName, oldAuthInfo: oldAuthInfo, newAuthInfo: newAuthInfo }) }}</p>
            <p>
                {{ $t('authInfoChange.warningTip.tip2') }}
                <span v-if="hasPermissionToReject">{{ $t('authInfoChange.warningTip.tip3') }}</span>
            </p>
        </div>
        <div class="suggest-container">
            <p v-if="hasPermissionToReject">{{ $t('authInfoChange.suggestTip.tip1') }}</p>
            <p v-else>{{ $t('authInfoChange.suggestTip.tip2', {adminInfo: authChangeData.maskedAdminInfo}) + $t('authInfoChange.suggestTip.tip3') }}</p>
        </div>
        <div slot="footer" class="bottom-btn">
            <el-button
                type="primary"
                @click="handleBtnClick"
            >{{ btnText }}</el-button>
        </div>
    </el-dialog>
</template>

<script>

export default {
    props: {
        dialogVisible: {
            type: Boolean,
            default: false,
        },
        authChangeData: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            hasPermissionToReject: false, // 当前登录主体是否可操作企业实名变更后驳回
        };
    },
    computed: {
        btnText() {
            return this.hasPermissionToReject ? (this.isGroup ? this.$t('authInfoChange.confirm') : this.$t('authInfoChange.changeAuth')) : this.$t('authInfoChange.notifyAdmin');
        },
        newAuthInfo() {
            const arr = [];
            const newEntName = this.authChangeData.changedEntName?.newValue;
            const newCorporationName = this.authChangeData.changedCorporationName?.newValue;
            newEntName && arr.push(`“${newEntName}”`);
            newCorporationName && arr.push(`“${newCorporationName}”`);
            return arr.join('、');
        },
        oldAuthInfo() {
            const arr = [];
            const oldEntName = this.authChangeData.changedEntName?.oldValue;
            const oldCorporationName = this.authChangeData.changedCorporationName?.oldValue;
            oldEntName && arr.push(`“${oldEntName}”`);
            oldCorporationName && arr.push(`“${oldCorporationName}”`);
            return arr.join('、');
        },
        isGroup() {
            return this.$store.state.commonHeaderInfo.hasGroupConsole;
        },
    },
    methods: {
        handleBtnClick() {
            if (this.hasPermissionToReject) {
                this.$emit('closeAuthInfoChangeDialog', !this.isGroup);
            } else {
                // 通知主管理员
                this.$http.post(`/ents/auth/notice-admin-re-auth`).then(() => {
                    this.$MessageToast.success(this.$t('authInfoChange.notifySuccess'));
                }).finally(() => {
                    this.$emit('closeAuthInfoChangeDialog', false);
                });
            }
        },

    },
    created() {
        this.$http.get(`/ents/auth/operation-under-name-changed`).then(res => {
            this.hasPermissionToReject = res.data.value;
        });
    },
};
</script>

<style lang="scss">
    .auth-change-dialog .el-dialog{
        width: 500px;
        border-radius: 5px;
        .el-dialog__body{
            height: unset !important;
        }
        .el-dialog__header{
            border-radius: 15px 15px 0 0 !important;
        }
        .top-container{
            font-size: 14px;
            color: #333333;
            font-weight: 350;
            line-height: 1.5;
            p{
                text-indent: 28px;
            }
        }
        .suggest-container{
            margin-top: 20px;
            font-size: 12px;
            color: #999999;
            font-weight: 400;
            line-height: 1.5;
            p{
                text-indent: 24px;
            }
            .suggest-tip{
                margin-top: 15px;
            }
        }
        .bottom-btn{
            text-align: center;
            color: #127FD2;
            .el-button{
                padding: 10px 40px;
                font-size: 13px;
            }
        }
    }
</style>
