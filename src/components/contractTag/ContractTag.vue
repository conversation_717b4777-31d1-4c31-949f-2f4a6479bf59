<template>
    <!--合同标签-->
    <div
        @click="handleClick"
        class="tag-item"
        :class="{'large': isLarge, 'cursor-default': isCursorDef}"
    >
        <el-tag
            :key="tag.tagId"
            :color="tag.color"
        >
            {{ tag.name }}
        </el-tag>
        <span class="angle-modal top"
            :style="{
                'border-top-color': tag.color
            }"
        >
        </span><span class="angle-modal bottom"
            :style="{
                'border-bottom-color': tag.color
            }"
        >
        </span>
        <svg class="sel-mark-icon" v-show="active" aria-hidden="true">
            <use
                style="pointer-events: none;"
                width="13"
                height="13"
                xlink:href="#el-icon-ssq-xuanzhongbiaoqian"
            >
            </use>
        </svg>
    </div>
</template>

<script>
export default {
    name: 'ContractTag',
    props: {
        tag: {
            type: Object,
            default: () => {},
        },
        isLarge: {
            type: Boolean,
            default: false,
        },
        isCursorDef: {
            type: Boolean,
            default: false,
        },
        active: {
            type: Boolean,
            default: false,
        },
    },
    methods: {
        handleClick() {
            this.$emit('tag-click');
        },
    },
};
</script>

<style lang="scss">
.tag-item {
    display: inline-block;
    height: 16px;
    vertical-align: top;
    .el-tag {
        z-index: 1;
        position: relative;
        float: left;
        height: 100%;
        line-height: 13px;
        border-radius: 1px;
        cursor: pointer;
    }
    .angle-modal {
        z-index: 0;
        display: inline-block;
        position: relative;
        float: left;
        width: 0;
        height: 0;
        border-right: 10px solid transparent;
        &.top {
            left: -1px;
            border-top: 16px solid transparent;
        }
        &.bottom {
            left: -11px;
            border-bottom: 16px solid transparent;
        }
    }
    .sel-mark-icon {
        position: relative;
        margin-left: -13px;
        margin-top: -4px;
        left: -7px;
        width: 13px;
        height: 13px;
    }

    &.large {
        height: 20px;
        .el-tag {
            line-height: 18px;
        }
        .angle-modal {
            border-right-width: 14px;
            &.top {
                left: -1px;
                border-top-width: 20px;
            }
            &.bottom {
                left: -15px;
                border-bottom-width: 20px;
            }
        }
    }
    &.cursor-default .el-tag {
        cursor: default;
    }
    [dir="rtl"] & {
        .el-tag {
            float: right;
        }
        .angle-modal {
            float: right;
            border-left: 10px solid transparent;
            border-right: none;
            &.top {
                right: -1px;
                left: auto;
            }
            &.bottom {
                right: -11px;
                left: auto;
            }
        }
        .sel-mark-icon {
            margin-left: 0;
            margin-right: -13px;
            left: auto;
            right: -7px;
        }
        &.large {
            border-left-width: 14px;
            &.top {
                right: -1px;
                left: auto;
            }
            &.bottom {
                right: -15px;
                left: auto;
            }
        }
    }

}
</style>
