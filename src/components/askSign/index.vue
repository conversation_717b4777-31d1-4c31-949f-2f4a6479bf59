<!--
 * @Author: fan_liu
 * @Date: 2020-10-28 14:21:45
 * @LastEditors: fan_liu
 * @LastEditTime: 2020-10-28 15:12:38
 * @Description: 请求他人认证表单
-->
<template>
    <!--请求他人认证-->
    <div class="ask-sign-component">
        <p class="subject-name-tip" style="margin-bottom: 30px">{{ $t('sign.requestSomeoneList') }}</p>
        <el-form :model="askSignInput" label-width="40px">
            <el-form-item :label="$t('sign.ent')">
                <el-input :placeholder="$t('sign.entName')" v-model="askSignInput.entName"></el-input>
            </el-form-item>
            <el-form-item :label="$t('sign.account')">
                <el-input :placeholder="$t('sign.accountPH')" v-model="askSignInput.account"></el-input>
            </el-form-item>
        </el-form>
        <div class="ent-auth-btn">
            <el-button type="primary" @click.stop.prevent="goToAsk">{{ $t('sign.send') }}</el-button>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        entName: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            askSignInput: {
                entName: this.entName || '',
                account: '',
            },
        };
    },
    methods: {
        goToAsk() {
            const { account, entName } = this.askSignInput;
            const contractId = this.$route.query.contractId;
            // 邮箱和手机
            const reg = new RegExp('(^[\\w.\\-]+@(?:[a-z0-9]+(?:-[a-z0-9]+)*\\.)+[a-z]{2,3}$)|(^1[3|4|5|8]\\d{9}$)');
            if (!entName.trim()) {
                this.$MessageToast.error(this.$t('sign.lackEntName'));
                return;
            }
            if (!reg.test(account)) {
                this.$MessageToast.error(this.$t('sign.errAccount'));
                return;
            }
            this.$http.post('/users/request-auth/notice', {
                account,
                entName,
                contractId,
            }).then(() => {
                this.$MessageToast.success(this.$t('sign.successfulSent'));
            }).finally(() => {
                this.$emit('close');
            });
        },
    },
};
</script>

<style>

</style>
