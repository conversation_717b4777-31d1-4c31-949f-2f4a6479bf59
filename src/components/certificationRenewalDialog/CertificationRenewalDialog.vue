<!-- 证书续期弹窗 -->
<template>
    <div class="renewal-dialog-wrap">
        <el-dialog
            :visible.sync="dialogVisible"
            class="renewal-dialog"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :style="dialogWidth"
            @close="$emit('update:visible',false)"
            center
        >
            <span slot="title">
                {{ $t('certificationRenewalDialog.renewalTitle') }}
                <el-tooltip class="item" effect="dark" placement="top">
                    <p slot="content" style="width: 300px">{{ $t('certificationRenewalDialog.renewalTips1') }}<br><br>{{ $t('certificationRenewalDialog.renewalTips2') }}<br>{{ $t('certificationRenewalDialog.renewalTips3') }}<a target="blank" href="/account-center/legal-agreement/digital-certificate-protocal">{{ $t('certificationRenewalDialog.renewalTips4') }}</a>{{ $t('certificationRenewalDialog.renewalTips5') }}</p>
                    <i class="el-icon-ssq-bangzhu"></i>
                </el-tooltip>
            </span>
            <div class="certification-content">
                <div class="tip">
                    <p>{{ $t('certificationRenewalDialog.renewalTip') }}</p>
                    <p v-if="renewalType.personNeedRenewal">{{ $t('certificationRenewalDialog.renewalTip2') }}</p>
                </div>
                <div v-for="(certificationInfo,index) in certificationInfos" :key="index">
                    <p><span>{{ $t('certificationRenewalDialog.previousIdentity') }}</span><span>{{ certificationInfo.entName || certificationInfo.realName }}<br><label class="certification-content-reject" v-if="renewalType.personNeedRenewal" @click="rejectAuthByRenewal">{{ $t('certificationRenewalDialog.reject') }}</label></span></p>
                    <p><span>{{ $t('certificationRenewalDialog.previousCA') }}</span><span>{{ certificationInfo.caOrgCN }}</span></p>
                    <p><span>{{ $t('certificationRenewalDialog.previousExpiryDate') }}</span><span>{{ certificationInfo.stopTime }}</span></p>
                    <p><span>{{ $t('certificationRenewalDialog.previousId') }}</span><span>{{ certificationInfo.serialNumber }}</span></p>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="$emit('renewal-confirm')">{{ $t('certificationRenewalDialog.renewal') }}</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { isPC } from 'src/common/utils/device.js';
import { mapState } from 'vuex';
export default {
    name: 'CertificationRenewalDialog',
    props: {
        certificationInfos: {
            type: Array,
            default: () => [],
            // default: {
            //     realName: '',   // 个人主体
            //     entName: '',    // 企业主体
            //     caOrgCN: '',    // 证书颁发机构
            //     stopTime: '',   // 证书有效期
            //     serialNumber: ''// 证书序列号
            // }
        },
        visible: {
            type: Boolean,
            default: true,
        },
        renewalType: {
            type: Object,
            default: () => ({ personNeedRenewal: false, entNeedRenewal: false }),
        },
    },
    data() {
        return {
            dialogVisible: false,
            isPC,
        };
    },
    computed: {
        ...mapState(['commonHeaderInfo']),
        dialogWidth() {
            return this.isPC ? {} : { width: '96%', 'max-width': '342px' };
        },
    },
    watch: {
        visible(value) {
            this.dialogVisible = value;
        },
    },
    methods: {
        async rejectAuthByRenewal() {
            await this.$confirm(this.$t('certificationRenewalDialog.rejectMessage', { name: this.commonHeaderInfo.platformUser.fullName }), this.$t('contractCompare.tip'), {
                confirmButtonText: this.$t('certificationRenewalDialog.rejectConfirm'),
            });
            this.$emit('reAuthHandle');
        },
    },
};
</script>

<style lang="scss">
    .renewal-dialog-wrap {
        .renewal-dialog {
            width: 342px;
            overflow: hidden;
            margin: 0 auto;
            .el-dialog {
                width: 100%;
                border-radius: 4px;
                overflow: hidden;
            }
            .el-dialog__header {
                text-align: center;
                padding: 20px;
                border-bottom: 0 !important;
            }
            .el-dialog__body {
                text-align: center;
                padding: 0 30px !important;
                overflow: hidden;
            }
            .el-dialog__footer {
                text-align: center;
                padding: 20px 30px 12px !important;
            }
            .dialog-footer button {
                border: none;
                width: 100%;
                height: 40px;
                background-color: #127fd2;
            }
            .certification-content {
                .tip {
                    text-align: left;
                    margin-bottom: 20px;
                }
                p {
                    overflow: hidden;
                }
                span {
                    display: inline-block;
                    float: left;
                }
                p>span:nth-child(1) {
                    text-align: left;
                    width: 40%;
                    color: #999;
                }
                p>span:nth-child(2) {
                    text-align: left;
                    width: 60%;
                    color: #000;
                    word-wrap: break-word;
                    margin-bottom: 10px;
                }
                &-reject {
                    margin-bottom: 10px;
                    color: $theme-color;
                    font-size: 12px;
                }
            }
        }
    }
</style>
