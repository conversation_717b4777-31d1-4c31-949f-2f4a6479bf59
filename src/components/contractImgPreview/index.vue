<!--合同图片渲染-->
<template>
    <div class="contract-img-container">
        <div class="contract-img-body" ref="contractImgWrapper" id="contractImgWrapper">
            <div
                class="contract-img-wrapper"
                v-for="(page, pageIndex) in doc.page"
                :key="pageIndex"
                :page-index="`${pageIndex}`"
            >
                <div class="contract-img-content" :style="styleObject(pageIndex)">
                    <img
                        class="contract-img-image"
                        alt="img"
                        v-lazy="{
                            src: `${page.highQualityPreviewUrl + (isNoPermissionPage ? '&needMergeLabel=true': '')}`,
                            split: 3,
                            index: pageIndex,
                            total: doc.page.length
                        }"
                    />
                    <div
                        class="contract-page-content"
                        :page-index="`${pageIndex}`"
                        :style="contentStyleObject(pageIndex)"
                    >
                        <slot :data="pageIndex"></slot>
                    </div>
                </div>
                <!-- 页脚 -->
                <div class="contract-img-footer">{{ $t('pdf.pager', {x: pageIndex + 1, y: doc.page.length}) }}</div>
            </div>
        </div>
    </div>
</template>

<script>
import { scrollToYSmooth } from 'src/common/utils/dom.js';
import { throttle } from 'utils/fn.js';
// 页间距20
const PAGE_DISTANCE = 20;
export default {
    name: 'ContractImgPreview',
    props: {
        doc: {
            type: Object,
            default: () => {},
        },
        waterMarkPng: {
            type: String,
            default: '',
        },
        isShowRidingSeal: {
            type: Boolean,
            default: false,
        },
        currentPageIndex: {
            type: Number,
            default: 1,
        },
        scale: {
            type: Number,
            default: 1,
        },
        parentElId: {
            type: String,
            default: '',
        },
        isFromSignPage: {
            type: Boolean,
            default: true,
        },
        isNoPermissionPage: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            visible: [], // 当前可视范围页列表
        };
    },
    computed: {
        styleObject() {
            const { doc, scale, isShowRidingSeal } = this;
            return (pageIndex) => {
                const page = doc.page[pageIndex];
                const width = page && page.actualWidth || 0;
                const height = page && page.actualHeight || 0;
                return {
                    width: `${width}px`,
                    height: `${height}px`,
                    paddingRight: isShowRidingSeal ? `${116 * scale}px` : 0,
                };
            };
        },
        contentStyleObject() {
            const { waterMarkPng, doc, scale } = this;
            return index => {
                const page = doc.page[index];
                if (this.isFromSignPage) {
                    return {
                        width: `${page && page.actualWidth || 0}px`,
                        height: `${page && page.actualHeight || 0}px`,
                    };
                }
                return {
                    width: `${page && page.width}px`,
                    height: `${page && page.height}px`,
                    transform: `scale(${scale})`,
                    background: waterMarkPng ? `url(${waterMarkPng}) repeat` : 'none',
                };
            };
        },
    },
    watch: {
        'doc.documentId': {
            handler() {
                this.getMiniImg();
                this.updateDocWidthAndHeight();
            },
            immediate: true,
        },
        currentPageIndex: {
            handler(newValue) {
                const find = this.visible.reduce((total, item) => {
                    return item.percent >= total.percent ? item : total;
                }, { percent: 0 });
                // 如果切换的新页面已经在当前视图范围内，则不更新
                if (find.pageIndex === newValue - 1) {
                    return;
                }
                this.scrollToNewPage(newValue - 1);
            },
        },
        'doc.page'(val) {
            this.$store.state.doc.totalDocLength = val;
        },
    },
    methods: {
        updateDocWidthAndHeight() {
            const pageInfo = this.doc.page.map((item) => {
                const width = (item.width_init || item.width);
                const height = (item.height_init || item.height);
                return {
                    ...item,
                    width: this.isFromSignPage ? Math.floor(width * this.scale) : Math.floor(width), // 原始宽度，793
                    height: this.isFromSignPage ? Math.floor(height * this.scale) : Math.floor(height),
                    actualWidth: Math.floor(width * this.scale), // 放缩之后的 实际宽度 793*this.scale
                    actualHeight: Math.floor(height * this.scale),
                    width_init: Math.floor(width), // 原始宽度，793
                    height_init: Math.floor(height),
                };
            });
            this.$emit('ready', pageInfo);
        },
        // 滚动到某一页
        scrollToNewPage(pageIndex) {
            // 滚动到文档的那一页
            const top = this.doc.page.slice(0, pageIndex).reduce((total, item) => {
                total += (item.actualHeight + 20);
                return total;
            }, 0);

            scrollToYSmooth(this.getParentEl(), top + this.getStartTop());
        },
        // 获取父组件的外层元素，绑定scroll事件
        getParentEl() {
            if (this.parentElId) {
                return document.getElementById(this.parentElId);
            }
            return this.$parent.$el;
        },
        // 获取外层元素，距离父级滚动元素的距离差
        getStartTop() {
            const el = this.getParentEl();
            const current = this.$refs.contractImgWrapper;
            const distance1 = this.getElementTop(el, document.body);
            const distance2 = this.getElementTop(current, document.body);
            return distance2 - distance1;
        },

        getElementTop(el, targetEl) {
            const parent = el.offsetParent;
            const top = el.offsetTop;
            return parent && parent !== targetEl ? this.getElementTop(parent, targetEl) + top : top;
        },
        handleScroll: throttle(function(e) {
            const initHeight = this.getStartTop();
            const target = e.currentTarget;
            // 视图内的可视范围
            const scrollTop = target.scrollTop;
            const scrollBottom = scrollTop + target.getBoundingClientRect().height;

            this.visible = this.getVisiblePageList({ pageList: this.doc.page, scrollTop, scrollBottom, startTop: initHeight, distance: PAGE_DISTANCE });

            // 上面找到的是最小的那个页码，但是如果页面中一半的内容，已经是下一页的内容，则需要进行下一页的渲染
            const find = this.visible.reduce((total, item) => {
                return item.percent >= total.percent ? item : total;
            });
            if (find.pageIndex !== this.currentPageIndex - 1) {
                this.$emit('updateCurrentPageIndex', find.pageIndex + 1);
            }
        }, 24),
        // 获取可视区域内的列表
        getVisiblePageList({ pageList, scrollTop, scrollBottom, startTop, distance }) {
            const visible = pageList.reduce((total, page, pageIndex) => {
                const pageHeight = this.isFromSignPage ? (page.height + distance) : (page.height + distance) * this.scale;
                const endTop = startTop + pageHeight;
                let percent = 0;
                // 在可视范围内的元素
                if (startTop <= scrollTop && endTop >= scrollTop && endTop <= scrollBottom) { // 下半页在视图范围内
                    percent = (endTop - scrollTop) / pageHeight;
                } else if (startTop >= scrollTop && endTop <= scrollBottom) { // 整页都在视图范围内
                    percent = 1;
                } else if (startTop >= scrollTop && startTop <= scrollBottom && endTop >= scrollBottom) { // 上半页在视图范围内
                    percent = (scrollBottom - startTop) / pageHeight;
                } else if (startTop <= scrollTop && endTop >= scrollBottom) { // 视图范围小于一页的
                    percent = (scrollBottom - scrollTop) / pageHeight;
                }
                startTop += pageHeight;
                if (percent) {
                    total.push({
                        pageIndex: pageIndex,
                        percent,
                    });
                }
                return total;
            }, []);
            return visible;
        },
        // 更新缩略图
        async getMiniImg() {
            const pageList = this.doc.page.slice(0, this.doc.page.length);
            pageList.forEach((page, pageIndex) => {
                if (pageIndex === 0) { // 生成第一页的缩略图
                    this.doc.thumbnail = this.$hybrid.getContractImg(page.imagePreviewUrl);
                    this.$emit('update-thumbnail', this.doc.thumbnail);
                }
            });
        },
    },
    mounted() {
        this.getParentEl().addEventListener('scroll', this.handleScroll);
    },
    beforeDestroy() {
        this.getParentEl().removeEventListener('scroll', this.handleScroll);
    },
};
</script>

<style lang="scss">
    .contract-img-container {
        .contract-img-body {
            user-select: text;
            transition: all 0 ease-out 0;
        }
        .contract-img-footer {
            font-size: 14px;
            line-height: 20px;
            width: 100%;
            height: 20px;
            text-align: center;
            color: #999999;
        }
        .contract-img-wrapper {
            font-size: 0;
            text-align: center;
            .contract-img-content {
                display: block;
                margin: 0 auto;
                position: relative;
            }
            .contract-img-image {
                width: 100%;
                height: 100%;
            }
            .contract-page-content {
                position: absolute;
                top: 0;
                left: 0;
                transform-origin: 0 0 0;
                height: 100%;
                overflow: visible;
                transform: translateZ(120px);
            }
        }
    }
</style>
