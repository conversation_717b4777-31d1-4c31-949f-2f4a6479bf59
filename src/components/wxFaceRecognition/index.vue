<template>
    <div
        class="wx_face-button"
        v-loading="wxLoading"
        :element-loading-text="$t('entAuth.faceInitLoading')"
    >
        <!-- 小程序webview通信 跳转刷脸小程序 -->
        <el-button
            class="btn-type-one submit"
            v-if="isMiniProgram"
            @click="handleWebviewFace"
        >
            <!-- 立即刷脸 -->{{ $t('entAuth.scanFaceNow') }}
        </el-button>
        <!-- 微信浏览器h5打开刷脸小程序 -->
        <div
            class="wx-face"
            v-else-if="wxTagReady"
        >
            <div class="miniprogram-face-box" ref="appletFace">
                <div class="open-tag-box">
                    <wx-open-launch-weapp
                        :path="appletFaceUrl"
                        @launch="handleAppletFace"
                        @error="appletFaceError"
                        id="launch-btn"
                        username="gh_cf199b1f200c"
                    >
                        <script type="text/wxtag-template">
                            <style>
                            .ctn{width: 120px;height: 45px;}
                            </style>
                            <div class="ctn"></div>
                        </script>
                    </wx-open-launch-weapp>
                </div>
                <el-button class="btn-type-one submit"><!-- 立即刷脸 -->{{ $t('entAuth.scanFaceNow') }}</el-button>
            </div>
        </div>
    </div>
</template>

<script>
import wxSDK from 'weixin-js-sdk';
// cdn引入wx的sdk会导致import的失效，需要兼容一下
const wx = window.wx || wxSDK;
import {
    wechatVersion,
    IOSVersion,
    isIOS,
    androidVersion,
    compareVersion,
} from 'utils/device.js';
export default {
    props: {
        hasCustomCallback: {
            type: Boolean,
            default: false,
        },
        faceParams: {
            type: Object,
            default: () => {
                return {};
            },
        },
        faceType: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            isMiniProgram: false,
            faceQuery: '',
            interval: null,
            wxTagReady: true,
            wxLoading: false,
        };
    },
    computed: {
        appletFaceUrl() {
            return `index/index.html?ssqFaceQuery=${this.faceQuery}`;
        },
        appletFaceRequestUrl() {
            return this.faceParams.requestUrl;
        },
        appletFaceRequestParams() {
            return this.faceParams.requestParams;
        },
        appletFaceResultUrl() {
            return this.faceParams.resultUrl;
        },
        pollingUrl() {
            return this.faceParams.pollingUrl;
        },
    },
    methods: {
        appletFaceError(err) {
            console.log(err);
        },
        /**
         * @description: 如果有自定义回调（无身份证刷脸需要先绑定姓名和身份证号）先处理回调逻辑，再走刷脸逻辑
         */
        handleAppletFace() {
            console.log(this.hasCustomCallback);
            if (this.hasCustomCallback) {
                this.$emit('click', this.polling);
            } else {
                this.$emit('click');
                this.polling();
            }
        },
        pollingFaceResult() {
            this.$http.get(this.pollingUrl).then(
                (res) => {
                    console.log(res);
                    if (res.data) {
                        (res.data.value || res.data.success) &&
                            this.$emit('recognitionSuccess', res.data) &&
                            clearInterval(this.interval);
                    }
                },
                () => {
                    this.$emit('recognitionFail');
                    clearInterval(this.interval);
                },
            );
        },
        polling() {
            this.interval && clearInterval(this.interval);
            this.interval = setInterval(this.pollingFaceResult, 5000);
        },
        /**
         * @description: 版本号对比：h5打开小程序 微信版本要求为：7.0.12及以上。 系统版本要求为：iOS 10.3及以上、Android 5.0及以上。
         */
        checkVersion() {
            let flag = true;
            if (wechatVersion && compareVersion(wechatVersion, '7.0.12') < 0) {
                this.$MessageToast.error(this.$t('entAuth.wxFaceVersionTip')); // 刷脸需要微信版本7.0.12及以上，请先升级
                flag = false;
            } else if (IOSVersion && compareVersion(IOSVersion, '10.3') < 0) {
                this.$MessageToast.error(this.$t('entAuth.wxIosFaceVersionTip')); // 刷脸需要ios版本10.3及以上，请先升级
                flag = false;
            } else if (
                androidVersion &&
                compareVersion(androidVersion, '5.0') < 0
            ) {
                this.$MessageToast.error(this.$t('entAuth.wxAndroidVersionTip')); // 刷脸需要android版本5.0及以上，请先升级
                flag = false;
            }
            return flag;
        },
        /**
         * @description: 判断当前环境，如果不在小程序webview内，初始化微信h5跳转小程序开放标签
         */
        checkWxEnvironment() {
            wx.miniProgram.getEnv(async(res) => {
                console.log(res);
                this.isMiniProgram = res.miniprogram;
                if (!res.miniprogram) {
                    if (!this.checkVersion()) {
                        this.wxTagReady = false;
                        return;
                    }
                    this.wxLoading = true;
                    await this.initOpenTag();
                }
            });
        },
        /**
         * @description: 初始化刷脸小程序需要的参数
         */
        async initAppletFaceQuery() {
            const queryString = `?requestUrl=${encodeURIComponent(
                this.appletFaceRequestUrl,
            )}&requestParams=${encodeURIComponent(
                JSON.stringify(this.appletFaceRequestParams),
            )}&resultUrl=${encodeURIComponent(this.appletFaceResultUrl)}`;
            return new Promise((resolve) => {
                this.$http('/users/auto-login/url', {
                    noToast: 1,
                    params: {
                        targetUrl: '/mp/auth-m/faceRecognition/result', // 要跳转的地址，不包含域名
                        attachment: queryString,
                    },
                })
                    .then((res) => {
                        this.faceQuery = res.data.value;
                    })
                    .finally(() => {
                        resolve();
                    });
            });
        },
        /**
         * @description: 初始化微信h5跳转小程序开放标签
         */
        initOpenTag() {
            let wechatUrl = location.href;
            const firstPage = localStorage.getItem('firstPage');
            isIOS && (wechatUrl = firstPage);
            return Vue.$http
                .get('/ents/ignore/wxShare', {
                    params: {
                        url: encodeURIComponent(wechatUrl),
                    },
                })
                .then((res) => {
                    if (res.data) {
                        const resData = res.data;
                        wx.config({
                            debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
                            appId: resData.appId, // 必填，公众号的唯一标识
                            timestamp: resData.timestamp, // 必填，生成签名的时间戳
                            nonceStr: resData.nonceStr, // 必填，生成签名的随机串
                            signature: resData.signature, // 必填，签名
                            jsApiList: ['showMenuItems'], // 必填，需要使用的JS接口列表 这里ios可以传空字符串，但是安卓不行，必须随便填一个 - -！
                            openTagList: [
                                'wx-open-launch-weapp', // 跳转小程序
                            ],
                        });
                        wx.ready(() => {
                            // wx.error之后仍然会到这里，需要注意
                            this.wxLoading = false;
                        });
                        // 通过error接口处理失败验证
                        wx.error((resp) => {
                            console.log(resp);
                            this.wxTagReady = false;
                            this.$MessageToast.error(`${this.$t('entAuth.faceInitFailure')}${resp.errMsg}）`);
                        });
                    }
                });
        },
        handleWebviewFace() {
            if (this.hasCustomCallback) {
                this.$emit('click', this.doWebviewFace);
            } else {
                this.$emit('click');
                this.doWebviewFace();
            }
        },
        /**
         * @description: webview和小程序通信，跳转刷脸小程序
         */
        async doWebviewFace() {
            let pageUrl = `${location.origin}${location.pathname}`;
            const query = this.$route.query;
            this.faceType && (query.faceType = this.faceType);
            for (const key in query) {
                pageUrl += `${pageUrl.includes('?') ? '&' : '?'}${key}=${query[key]}`;
            }
            !pageUrl.includes('startPolling') &&
                (pageUrl += `${pageUrl.includes('?') ? '&' : '?'}startPolling=true`);
            wx.miniProgram.postMessage({
                data: {
                    ssqFaceQuery: this.faceQuery,
                    ssqPageUrl: pageUrl,
                    messageType: 'face',
                },
            });
            wx.miniProgram.navigateBack();
        },
    },
    async created() {
        if (location.href.includes('startPolling')) {
            this.polling();
        }
        await this.initAppletFaceQuery();
        this.checkWxEnvironment();
    },
    beforeDestroy() {
        this.interval && clearInterval(this.interval);
    },
};
</script>

<style lang="scss">
.miniprogram-face-box {
    position: relative;
    width: 100%;
    .open-tag-box {
        width: 100%;
        height: 45px;
        position: absolute;
        background: transparent;
        z-index: 10;
        wx-open-launch-weapp {
            display: block;
            height: 45px;
            background: transparent !important;
        }
    }
}
</style>
