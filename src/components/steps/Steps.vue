<template>
    <div class="common-steps">
        <!-- activeStepIndex 存在时选中为当前激活的索引，不存在时无选中  -->
        <div
            class="common-step"
            v-for="(step, index) in steps"
            :key="index"
            :class="{'active-step':activeStepIndex === index }"
        >
            <i class="el-icon-ssq-ic_list_arrow_qiantaidaishouh"></i>
            <span class="step-number">{{ index + 1 }}</span>{{ step }}
        </div>
    </div>
</template>

<script>
export default {
    props: {
        activeStepIndex: {
            type: Number,
            required: false,
            default: 0,
        },
        steps: {
            type: Array,
            required: true,
        },
    },
};
</script>

<style lang="scss">
    .common-steps {
        text-align: center;
        height: 57.5px;
        line-height: 57.5px;
    }

    .common-step {
        display: inline-block;
        font-size: 14px;
        color: #999;
        position: relative;

        .el-icon-ssq-ic_list_arrow_qiantaidaishouh {
            display: none;
        }

        & + .common-step {
            margin-left: 30px;

            .el-icon-ssq-ic_list_arrow_qiantaidaishouh {
                display: inline-block;
                position: relative;
                left: -15px;
                color: #CCC;
                font-size: 14px;
            }
        }
    }

    .step-number {
        display: inline-block;
        width: 24px;
        height: 24px;
        margin-right: 10px;
        line-height: 24px;
        text-align: center;
        color: #CCC;
        border: 1px solid #CCC;
        border-radius: 50%;
    }

    .active-step {
        color: #127FD2;

        .step-number {
            background: #127FD2;
            border: none;
            color: #fff;
        }
    }
</style>
