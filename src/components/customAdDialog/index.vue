<template>
    <el-dialog
        class="spread-region__dialog"
        :title="$t('footerAd.title')"
        :visible.sync="dialogVisible"
        :modal-append-to-body="true"
        :append-to-body="true"
    >
        <div class="bankContent">{{ $t('footerAd.bankContent') }}</div>
        <el-checkbox class="bankOption" v-model="acceptPhoneCall">{{ $t('footerAd.bankTip1') }}</el-checkbox>
        <el-checkbox class="bankOption" v-model="acceptSmsNotice">{{ $t('footerAd.bankTip2') }}</el-checkbox>
        <div class="bankFooter">
            <img class="bankImg" src="~img/ningboBankImg.png" alt="">
            <div>{{ $t('footerAd.bankFooter') }}</div>
        </div>
        <div slot="footer">
            <el-button @click="closeDialog">{{ $t('footerAd.cancel') }}</el-button>
            <el-button type="primary" @click="handleCustomerJump">{{ $t('footerAd.continue') }}</el-button>
        </div>
    </el-dialog>
</template>
<script>
import { addAdRecord } from 'src/api/sign.js';

export default {
    props: {
        curAdItem: {
            type: Object,
            default: () => {},
        },
        recordMap: {
            type: Object,
            default: () => {},
        },
        dialogVisible: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            acceptPhoneCall: false,
            acceptSmsNotice: false,
        };
    },
    methods: {
        closeDialog() {
            this.$emit('update:dialogVisible', false);
        },
        handleCustomerJump() {
            this.closeDialog();
            addAdRecord({
                adId: this.curAdItem.adId,
                adPageAddress: this.curAdItem.consumerUrl,
                click: this.recordMap['confirmClick'],
                acceptPhoneCall: this.acceptPhoneCall,
                acceptSmsNotice: this.acceptSmsNotice,
            });
            window.open(this.curAdItem.consumerUrl);
        },
    },
};
</script>
<style lang="scss">
.spread-region__dialog {
    width: 800px;
    margin: 0 auto;
        .el-dialog__body {
            color: #383838!important;
        }
        .bankFooter {
            margin-top: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: #F5F5F5;
            border-radius: 5px;
            padding: 20px 0;
        }
        .bankContent {
            margin-bottom: 8px;
        }
        .bankOption {
            margin: 2px 0;
            color:#666;
            font-size: 12px;
        }
        .bankImg {
            width: 100px;
            margin-bottom: 10px;
        }
    .el-button--primary {
        background-color: #127FD2;
        border-color: #127FD2;

        &:hover {
            background: #1687dc;
            border-color: #1687dc;
        }
    }

    .el-button--default {
        background: #F8F8F8;
        color: #666666;
        border: 1px solid rgba(204, 204, 204, 1);
        border-radius: 2px;

        &:hover {
            background: #F8F8F8;
            border: 1px solid rgba(204, 204, 204, 1);
            color: #666666;
        }
    }
}
</style>
