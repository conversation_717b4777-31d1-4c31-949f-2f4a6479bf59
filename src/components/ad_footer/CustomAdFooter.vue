<!--客户定制的广告位-->
<template>
    <div class="spread-area">
        <div class="spread-area__content" v-for="(item, index) in showAdList" :key="index">
            <img class="logo" :src="customerImg(item)" alt="客户" :style="{'height': adImgHeight(item) + 'px'}" @click="handleClick(item)" />
            <div v-if="item.externalFlag">推广</div>
        </div>
        <CustomAdDialog
            :dialogVisible.sync="customAdDialogVisible"
            :curAdItem="curAdItem"
            :recordMap="recordMap"
        ></CustomAdDialog>
    </div>
</template>
<script>
import { addAdRecord } from 'src/api/sign.js';
import CustomAdDialog from 'src/components/customAdDialog/index.vue';
const RECORD_MAP = {
    'adShow': 0,
    'confirmClick': 1,
    'dialogShow': 2,
};
export default {
    components: {
        CustomAdDialog,
    },
    props: {
        status: {
            type: String,
            default: '',  // '2'、'6'
        },
        adList: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            customAdDialogVisible: false,
            customerUrl: 'https://operate.xiaoheipao.com/customer-service',
            recordMap: RECORD_MAP,
            curAdItem: {},
            acceptPhoneCall: false,
            acceptSmsNotice: false,
            showAdList: [],
        };
    },
    computed: {
        customerImg() {
            return (item) => {
                return `/ad-api/plan/file?fileId=${encodeURIComponent(item.pcFileId || item.pcBigFileId)}`; // 字段 pcFileId 和 pcBigFileId 同一时间只有一个有值
            };
        },
        adImgHeight() {
            return (item) => {
                return item.pcBigFileId ? 375 / 750 * 300 :  375 / 750 * 170;
            };
        },
    },
    methods: {
        handleClick(item) {
            // 非上上签投放的广告弹出跳转弹窗
            this.curAdItem = item;
            if (item.externalFlag) {
                // this.dialogVisible = true;
                window.open('https://aapho.nbcb.com.cn/blcs/mgm/#/MarketingPoster?id=1'); // SAAS-40864 宁波银行不弹窗，写死的跳转链接
                addAdRecord({ adId: item.adId, adPageAddress: item.consumerUrl, click: this.recordMap['dialogShow'] });
            } else {
                window.open(item.consumerUrl);
                addAdRecord({ adId: item.adId, adPageAddress: item.consumerUrl, click: this.recordMap['confirmClick'] });
            }
        },
        handleCustomerJump() {
            this.$sensors.track({
                eventName: `Ent_OSPage_BtnClick`,
                eventProperty: {
                    page_name: this.status === '6' ? '云平台签署完成页' : '云平台拒签页',
                    contract_id: this.$route.query.contractId,
                    ad_id: this.curAdItem.adId,
                    ad_url: this.curAdItem.consumerUrl,
                    contract_status: this.status,
                    icon_name: '继续',
                },
            });
            this.customAdDialogVisible = false;
            addAdRecord({
                adId: this.curAdItem.adId,
                adPageAddress: this.curAdItem.consumerUrl,
                click: this.recordMap['confirmClick'],
                acceptPhoneCall: this.acceptPhoneCall,
                acceptSmsNotice: this.acceptSmsNotice,
            });
            window.open(this.curAdItem.consumerUrl);
        },
        addRecord() {
            this.showAdList.forEach((item) => {
                // 每展示一次记录一次
                addAdRecord({ adId: item.adId, adPageAddress: item.consumerUrl, click: this.recordMap['adShow'] });
            });
        },
    },
    async mounted() {
        // showcase: 页面：0，弹窗：1，悬浮按钮：2
        this.showAdList = this.adList.filter(item => item.showcase === 0);
        this.addRecord();
    },
};
</script>
<style lang="scss">
.spread-area {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 40px;
    .el-dialog__body {
        color: #383838!important;
    }
    .bankFooter {
        margin-top: 10px;
        display: flex;
        flex-direction: column;
        align-items: center;
        background-color: #F5F5F5;
        border-radius: 5px;
        padding: 20px 0;
    }
    .bankContent {
        margin-bottom: 8px;
    }
    .bankOption {
        margin: 2px 0;
        color:#666;
        font-size: 12px;
        width: 200px;
    }
    .bankImg {
        width: 100px;
        margin-bottom: 10px;
    }
    &__content {
        position: relative;
        width: 375px;
        margin-bottom: 10px;
        img {
            width: 100%;
            cursor: pointer;
        }
        div {
            position: absolute;
            top: -2px;
            right: 0;
            background-color: #fff;
            color: #CCC;
            opacity: 0.8;
            width: 35px;
            height: 15px;
            line-height: 15px;
            text-align: center;
            font-size: 10px;
        }

    }
    &__dialog {
        width: 100%;
        .el-dialog {
            width: 420px;
            .el-button--primary {
                background-color: #127FD2;
                border-color: #127FD2;

                &:hover {
                    background: #1687dc;
                    border-color: #1687dc;
                }
            }

            .el-button--default {
                background: #F8F8F8;
                color: #666666;
                border: 1px solid rgba(204, 204, 204, 1);
                border-radius: 2px;

                &:hover {
                    background: #F8F8F8;
                    border: 1px solid rgba(204, 204, 204, 1);
                    color: #666666;
                }
            }
        }
    }
}
</style>
