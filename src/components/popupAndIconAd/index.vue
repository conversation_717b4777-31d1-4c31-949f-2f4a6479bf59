<!--签署完成页弹窗或者悬浮图标广告位-->
<template>
    <div>
        <div v-for="adItem in showAdList" :key="adItem.adId">
            <div
                v-if="adItem.show"
                class="spread-region"
                :class="showPopupAd(adItem) ? 'spread-region__popup' : (showIconAd(adItem) ? 'spread-region__icon': '')"
            >
                <div class="spread-region__wrapper">
                    <div class="spread-region__body"
                        :style="{
                            'height': adImgHeight(adItem) + 'px',
                        }"
                    >
                        <img
                            class="spread-region__img"
                            :src="customerImg(adItem)"
                            alt="img"
                            @click="handleClick(adItem)"
                        >
                        <div v-if="adItem.externalFlag">推广</div>
                    </div>
                    <div
                        class="spread-region__close-icon"
                        :class="showPopupAd(adItem) ? 'spread-region__close-icon-big' : (showIconAd(adItem) ? 'spread-region__close-icon-small': '')"
                        @click="closeAd(adItem)"
                    >
                        <img :src="showPopupAd(adItem) ? closeIcon : (showIconAd(adItem) ? closeIconDark : '')" alt="关闭图标">
                    </div>
                </div>
                <CustomAdDialog
                    :dialogVisible.sync="customAdDialogVisible"
                    :curAdItem="adItem"
                    :recordMap="recordMap"
                ></CustomAdDialog>
            </div>
        </div>
    </div>
</template>
<script>
import { addAdRecord } from 'src/api/sign.js';
import CustomAdDialog from 'src/components/customAdDialog/index.vue';
const RECORD_MAP = {
    'adShow': 0,
    'confirmClick': 1,
    'dialogShow': 2,
};
export default {
    components: {
        CustomAdDialog,
    },
    props: {
        adList: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            recordMap: RECORD_MAP,
            customAdDialogVisible: false,
            closeIcon: require('img/close_icon.png'),
            closeIconDark: require('img/close_icon_dark.png'),
            showAdList: [],
        };
    },
    computed: {
        showPopupAd() {
            return (item) => {
                return item.show && item.showcase === 1;
            };
        },
        showIconAd() {
            return (item) => {
                return item.show && item.showcase === 2;
            };
        },
        customerImg() {
            return (item) => {
                return `/ad-api/plan/file?fileId=${encodeURIComponent(item.pcFileId || item.pcBigFileId)}`; // 字段 pcFileId 和 pcBigFileId 同一时间只有一个有值
            };
        },
        adImgHeight() {
            return (item) => {
                return Math.min(item.imageSize.split('*')[1], document.body.clientHeight * 2 / 3);
            };
        },
    },
    methods: {
        handleClick(item) {
            // 非上上签投放的广告弹出跳转弹窗
            if (item.externalFlag) {
                this.customAdDialogVisible = true;
                addAdRecord({ adId: item.adId, adPageAddress: item.consumerUrl, click: this.recordMap['dialogShow'] });
            } else {
                window.open(item.consumerUrl);
                addAdRecord({ adId: item.adId, adPageAddress: item.consumerUrl, click: this.recordMap['confirmClick'] });
            }
        },
        addRecord() {
            this.showAdList.forEach((item) => {
                // 每展示一次记录一次
                addAdRecord({ adId: item.adId, adPageAddress: item.consumerUrl, click: this.recordMap['adShow'] });
            });
        },
        async closeAd(item) {
            this.showAdList.forEach(ad => {
                if (ad.adId === item.adId) {
                    ad.show = false;
                }
            });
        },
        handleShowList() {
            // 目前签署完成页只有弹窗，没有悬浮按钮   页面：0，弹窗：1，悬浮按钮：2
            const tempList = this.adList.filter(item => item.showcase === 1);
            this.showAdList = tempList.map((ad) => {
                return { ...ad, show: true };
            });
        },
    },
    async mounted() {
        this.handleShowList();
        this.addRecord();
    },
};
</script>
<style lang="scss">
.spread-region__popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
}
.spread-region__icon {
    position: fixed;
    right: 5px;
    top: 300px;
}
.spread-region__img {
    height: 100%;
    cursor: pointer;
}
.spread-region {
    z-index: 2000;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    &__wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    &__body {
        position: relative;
        text-align: right;
        div {
            position: absolute;
            top: 0;
            right: 0;
            background-color: #fff;
            color: #CCC;
            opacity: 0.8;
            text-align: center;
            font-size: 13px;
            width: 34px;
            height: 16px;
            line-height: 16px;
        }
    }
    &__close-icon {
        cursor: pointer;
        overflow: hidden;
        img {
            width: 100%;
            height: 100%;
        }
        &-big {
            width: 36px;
            height: 36px;
            border-radius: 18px;
            margin-top: 10px;
        }
        &-small {
            width: 20px;
            height: 20px;
            border-radius: 10px;
            margin-top: 3px;
        }
    }
}
</style>
