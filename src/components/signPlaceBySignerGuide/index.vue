<!-- 新版本签署人指定签署位置引导，该类型合同由新版本的API发送合同发出 -->
<template>
    <el-dialog
        class="sign-place-by-signer-guide"
        :title="tipTitle"
        :visible.sync="dialogVisible"
    >
        <div v-if="step === 1">
            <div class="sign-place-step">{{ $t('signPC.signPlaceBySigner.step.one') }}</div>
            <div v-if="isEnt" class="sign-place-step">
                <p class="sign-place-how-drag">{{ $t('signPC.signPlaceBySigner.step.two1') }}</p>
                <img class="sign-place-img" src="~img/sign/dragSeal.png" alt="">
            </div>
            <div v-else class="sign-place-step">
                <p class="sign-place-how-drag">{{ $t('signPC.signPlaceBySigner.step.two2') }}</p>
                <img class="sign-place-img" src="~img/sign/dragSignature.png" alt="">
            </div>
            <div>
                <p class="sign-place-how-drag">{{ $t('signPC.signPlaceBySigner.step.three') }}</p>
                <img v-if="isEnt" class="sign-place-img" src="~img/sign/sealSign.png" alt="">
                <img v-else class="sign-place-img" src="~img/sign/signatureSign.png" alt="">
            </div>
        </div>
        <div v-else>
            <img v-if="isEnt" class="how-sign-img" src="~img/sign/howDragSeal.png" alt="">
            <img v-else class="how-sign-img" src="~img/sign/howDragSignature.png" alt="">
        </div>
        <div class="not-remind-again" v-if="step === 1">
            <el-checkbox v-model="isNotRemindAgain">{{ $t('signPC.signPlaceBySigner.notRemind') }}</el-checkbox>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button class="i-know" type="primary" @click="handleConfirm">{{ $t('signPC.signPlaceBySigner.iKnow') }}</el-button>
        </span>
    </el-dialog>
</template>

<script>
export default {
    name: 'SignPlaceBySignerGuide',
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        isEnt: {
            type: Boolean,
            default: false,
        },
        // 步骤：1.签署指导 2.如何拖签名/拖章
        step: {
            type: Number,
            default: 1,
        },
    },
    data() {
        return {
            isNotRemindAgain: false,
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                // 如果是关闭弹窗
                if (!value) {
                    this.$emit('close');
                }
            },
        },
        tipTitle() {
            if (this.step === 1) {
                return this.$t('signPC.signPlaceBySigner.signGuide');
            }
            if (this.isEnt) {
                return  this.$t('signPC.signPlaceBySigner.howDragSeal');
            }
            return this.$t('signPC.signPlaceBySigner.howDragSignature');
        },
    },
    watch: {
        'step': {
            handler() {
                this.toggleStyle();
            },
        },
    },
    methods: {
        handleConfirm() {
            this.dialogVisible = false;
            if (this.isNotRemindAgain && this.step === 1) {
                this.$localStorage.set('notShowSignGuideDialog', true);
            }
        },
        toggleStyle() {
            if (this.step === 2) {
                const dialogDOM = document.querySelector('.sign-place-by-signer-guide .el-dialog');
                dialogDOM.style.width = '540px';
            }
        },
    },
};
</script>

<style lang="scss">
.sign-place-by-signer-guide{
    .el-dialog{
        width: 400px;
        .el-dialog__body{
            padding: 24px 30px;
            .sign-place-img{
                width: 340px;
            }
            .sign-place-step{
                margin-bottom: 20px;
            }
            .sign-place-how-drag{
                margin-bottom: 10px;
            }
            .how-sign-img{
                width: 480px;
            }
            .not-remind-again{
                margin-top: 10px;
            }
        }
    }
}
</style>
