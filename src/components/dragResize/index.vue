<template>
    <div class="drag-resize">
        <div class="drag-resize__drag-bar">
            <slot name="drag"></slot>
        </div>
        <slot name="content"></slot>
        <!-- box四条边可拖动区域 -->
        <div class="drag-resize__edge edge-top"></div>
        <div class="drag-resize__edge edge-bottom"></div>
        <div class="drag-resize__edge edge-right"></div>
        <div class="drag-resize__edge edge-left"></div>
        <!-- box四角可拖动区域 -->
        <div class="drag-resize__corner corner-lt"></div>
        <div class="drag-resize__corner corner-lb"></div>
        <div class="drag-resize__corner corner-rt"></div>
        <div class="drag-resize__corner corner-rb"></div>
    </div>
</template>

<script>
// 窗口拖动
let onMove = false;
let offsetX;
let offsetY;
export default {
    data() {
        return {
            container: null,
            oddStyle: null,
        };
    },
    methods: {
        onMouseDown(e) {
            onMove = true;
            offsetX = e.offsetX;
            offsetY = e.offsetY;
            document.addEventListener('mousemove', this.onMouseMove);
            document.addEventListener('mouseup', this.onMouseUp);
        },

        onMouseMove(e) {
            if (onMove) {
                const boxWidth = parseFloat(this.oddStyle.width);
                const boxHeight = parseFloat(this.oddStyle.height);
                this.container.style.left = `${Math.min(window.innerWidth - boxWidth, Math.max(0, e.clientX - offsetX))}px`;
                this.container.style.top = `${Math.min(window.innerHeight - boxHeight, Math.max(0, e.clientY - offsetY))}px`;
            }
        },
        onMouseUp() {
            onMove = false;
            document.removeEventListener('mousemove', this.onMouseMove);
            document.removeEventListener('mouseup', this.onMouseUp);
        },

        // 通过四条边扩大缩小窗口
        onResize(e, edgeClass, moveAxis) {
            /**
             *
             *
             * @param {*} e
             * @param {*} edgeClass 监听的边
             * @param {*} moveAxis 影响的轴（width/height)
             */
            const oddStyle = window.getComputedStyle(this.container);
            const moveDirect = parseFloat(oddStyle[moveAxis]);
            const left = parseFloat(oddStyle.left);
            const top = parseFloat(oddStyle.top);
            const limitX = 0;
            const limitY = 0;
            const movement = moveAxis === 'width' ? e.clientX - left : e.clientY - top;
            if (edgeClass === '.edge-right' || edgeClass === '.edge-bottom') {
                // 使用Math.max限定范围
                this.container.style[moveAxis] = `${Math.min(
                    edgeClass === '.edge-right'
                        ? window.innerWidth - left
                        : window.innerHeight - top,
                    Math.max(200, movement),
                )}px`;
            }
            if (edgeClass === '.edge-top' || edgeClass === '.edge-left') {
                const movement =
                    moveAxis === 'width'
                        ? left - Math.max(limitX, e.clientX)
                        : top - Math.max(limitY, e.clientY);
                this.container.style[moveAxis] = `${Math.max(
                    200,
                    moveDirect + movement,
                )}px`;
                if (moveAxis === 'width') {
                    this.container.style.left = `${Math.max(limitX, e.clientX)}px`;
                } else if (moveAxis === 'height') {
                    this.container.style.top = `${Math.max(limitY, e.clientY)}px`;
                }
            }
        },
        // 使出口可以改变大小
        resizeOnEdge(edgeClass, moveAxis) {
            // 中间函数传参数
            const fn = (e) => {
                this.onResize(e, edgeClass, moveAxis);
            };

            function clearListener() {
                document.removeEventListener('mousemove', fn);
                document.removeEventListener('mouseup', clearListener);
            }
            const target = document.querySelector(edgeClass);
            target.addEventListener('mousedown', () => {
                document.addEventListener('mousemove', fn);
                document.addEventListener('mouseup', clearListener);
            });
        },
        onResizeCorner(e, cornerClass) {
            const left = parseFloat(this.oddStyle.left);
            const top = parseFloat(this.oddStyle.top);
            const width = parseFloat(this.oddStyle.width);
            const height = parseFloat(this.oddStyle.height);
            const limitX = 0;
            const limitY = 0;
            const mX = e.clientX - left;
            const mY = e.clientY - top;
            const mXIn = left - Math.max(limitX, e.clientX);
            const mYIn = top - Math.max(limitY, e.clientY);
            if (cornerClass === '.corner-rt') {
                this.container.style.top = `${Math.max(limitY, e.clientY)}px`;
                this.container.style.width = `${Math.max(200, mX)}px`;
                this.container.style.height = `${Math.max(200, height + mYIn)}px`;
                return;
            }
            if (cornerClass === '.corner-lt') {
                this.container.style.top = `${Math.max(limitX, e.clientY)}px`;
                this.container.style.left = `${Math.max(limitX, e.clientX)}px`;
                this.container.style.width = `${Math.max(200, width + mXIn)}px`;
                this.container.style.height = `${Math.max(200, height + mYIn)}px`;

                return;
            }
            if (cornerClass === '.corner-lb') {
                this.container.style.left = `${Math.max(limitX, e.clientX)}px`;
                this.container.style.width = `${Math.max(200, width + mXIn)}px`;
                this.container.style.height = `${Math.max(200, mY)}px`;

                return;
            }

            this.container.style.width = `${Math.min(window.innerWidth - left, Math.max(200, mX))}px`;
            this.container.style.height = `${Math.min(window.innerHeight - top, Math.max(200, mY))}px`;
        },
        resizeOnCorner(cornerClass) {
            const fn = (e) => {
                this.onResizeCorner(e, cornerClass);
            };

            function clearListener() {
                document.removeEventListener('mousemove', fn);
                document.removeEventListener('mouseup', clearListener);
            }
            const target = document.querySelector(cornerClass);
            target.addEventListener('mousedown', () => {
                document.addEventListener('mousemove', fn);
                document.addEventListener('mouseup', clearListener);
            });
        },
    },
    mounted() {
        this.container = document.querySelector('.drag-resize');
        this.oddStyle = window.getComputedStyle(this.container);
        const dragElement = document.querySelector('.drag-resize__drag-bar');
        dragElement.addEventListener('mousedown', this.onMouseDown);
        this.resizeOnEdge('.edge-right', 'width');
        this.resizeOnEdge('.edge-left', 'width');
        this.resizeOnEdge('.edge-top', 'height');
        this.resizeOnEdge('.edge-bottom', 'height');
        this.resizeOnCorner('.corner-rb');
        this.resizeOnCorner('.corner-rt');
        this.resizeOnCorner('.corner-lt');
        this.resizeOnCorner('.corner-lb');
    },
};
</script>

<style lang="scss">
.drag-resize{
    position: fixed;
    &__edge{
        position: absolute;
        z-index: 10;
        width: 100%;
        height: 100%;
        &.edge-top , &.edge-bottom{
            height: 3px;
            cursor: row-resize;
        }
        &.edge-right , &.edge-left{
            width: 3px;
            cursor: col-resize;
        }
        &.edge-top{
            top: 0;
        }
        &.edge-bottom{
            bottom: 0;
        }
        &.edge-right{
            right: 0;
        }
        &.edge-left{
            left: 0;
        }
    }
    &__corner{
        position: absolute;
        z-index: 20;
        width: 3px;
        height: 3px;
        &.corner-lb, &.corner-rt{
            cursor: nesw-resize;
        }
        &.corner-lt, &.corner-rb{
            cursor: nwse-resize;
        }
        &.corner-lt{
            top: 0;
            left: 0;
        }
        &.corner-lb{
            bottom: 0;
            left: 0;
        }
        &.corner-rt{
            top: 0;
            right: 0;
        }
        &.corner-rb{
            bottom: 0;
            right: 0;
        }
    }
}
</style>
