<!-- 日文版签署前认证 -->
<template>
    <el-dialog
        v-if="visible"
        :title="$t('sign.prompt')"
        :visible.sync="dialogVisible"
        size="tiny"
        :before-close="handleClose"
        class="auth-before-signing el-dialog-bg"
    >
        <div class="dialog-content">
            <!-- 合同链接进入签署页 企业名称不一致，或者当前没有企业主体 -->
            <template v-if="isEnt">
                <!-- 注册地 -->
                <div class="register-area">
                    <div class="ent-label">{{ $t('signJa.areaRegister') }}：</div>
                    <template>
                        <el-radio v-if="getIsJa" v-model="registerRadio" label="JPN">{{ $t('signJa.jp') }}</el-radio>
                        <el-radio v-else-if="getIsUae" v-model="registerRadio" label="ARE">{{ $t('signJa.are') }}</el-radio>
                        <el-radio v-model="registerRadio" label="CHN">{{ $t('signJa.cn') }}</el-radio>
                        <el-radio v-model="registerRadio" label="OTHER">{{ $t('signJa.other') }}
                            <el-select v-show="registerRadio === 'OTHER' " v-model="otherArea" filterable :placeholder="$t('signJa.plsSelect')">
                                <el-option
                                    v-for="item in areaList"
                                    :key="item.abbreviation"
                                    :label="item[countryKey]"
                                    :value="item.abbreviation"
                                >
                                </el-option>
                            </el-select>
                        </el-radio>

                    </template>
                </div>
                <template v-if="registerRadio === 'CHN'">
                    <div class="cn-tip">{{ $t('signJa.tip1') }}</div>
                    <div class="cn-tip">{{ $t('signJa.tip2') }}</div>
                </template>
                <template v-else-if="isJaEnvSelectAre">
                    <div class="cn-tip">{{ $t('signJa.uaeTip1') }}</div>
                    <div class="cn-tip">{{ $t('signJa.uaeTip2') }}</div>
                </template>
                <template v-else-if="isAreEnvSelectOther && otherArea !== 'ARE'">
                    <div class="cn-tip">{{ $t('signJa.uaeTip3') }}</div>
                </template>
                <template v-else>
                    <p><!-- 根据发件方要求, 请以此企业名义进行签署： -->{{ $t('signJa.beforeSignTip1') }}</p>
                    <el-form ref="form" :rules="rules" :model="entInfoForm" :label-width="formItemWidth">
                        <el-form-item prop="entName" :label="`${$t('sign.entName')}：`">
                            <el-input :disabled="!!receiver.enterpriseName" v-model="entInfoForm.entName" :placeholder="$t('signJa.entNamePlaceholder')"></el-input>
                        </el-form-item>
                        <el-form-item prop="businessLicenceNumber" :label="`${copNumLabel}：`">
                            <el-input v-model="entInfoForm.businessLicenceNumber" :placeholder="copNumLabel"></el-input>
                            <div v-if="registerRadio !== 'JPN'" class="tip-hint">{{ $t('signJa.tip3') }}</div>
                        </el-form-item>
                        <el-form-item prop="fileId" :label="`${picLabel}：`">
                            <el-upload
                                ref="upload"
                                action="/ents/charging/bill/pic"
                                class="upload-item"
                                :headers="uploadHeaders"
                                :before-upload="beforeUpload"
                                :on-remove="uploadRemove"
                                :on-success="uploadSuccess"
                                :on-error="uploadError"
                                :on-preview="handlePreview"
                                :show-file-list="true"
                                :file-list="fileList"
                                accept=""
                                :limit="1"
                            >
                                <div>
                                    <el-button class="upload-btn" type="plain" plain :disabled="entInfoForm.fileId != null || uploadStatus > 0">+ <!-- 点击上传 -->{{ $t('entAuth.upload') }}</el-button>
                                </div>
                            </el-upload>
                            <div v-if="registerRadio !== 'JPN'" class="tip-hint">
                                <div class="bold-font top-margin">{{ $t('signJa.tip4') }}：</div>
                                <div><span>{{ $t('signJa.tip5') }}</span> </div>
                                <div><span>{{ $t('signJa.tip7') }}</span> </div>
                                <div><span>{{ $t('signJa.tip9') }}</span> </div>
                                <div><span>{{ $t('signJa.tip10') }}</span> </div>

                                <!-- <div class="bold-font top-margin">{{ $t('signJa.tip11') }}：</div>
                                <div>• {{ $t('signJa.tip12') }}</div>
                                <div>• {{ $t('signJa.tip13') }}</div>
                                <div>• {{ $t('signJa.tip14') }}</div> -->
                            </div>
                        </el-form-item>
                        <!-- <template v-if="signDecision === 'NOT_MATCH_AUTHENTICATE'">
                        <p class="margin-b-12">{{ $t('signJa.beforeSignTip5', { currentUser: '', signer: receiver.enterpriseName }) }}</p>
                        <p class="margin-b-12">{{ $t('signJa.beforeSignTip6', { signer: receiver.enterpriseName }) }}</p>
                        <p class="margin-b-12">{{ $t('signJa.beforeSignTip7') }}</p>
                    </template> -->
                        <el-form-item class="confirm-item">
                            <el-button type="primary" @click="onSubmit"><!-- 确认 -->{{ $t('sign.confirmOk') }}</el-button>
                        </el-form-item>
                    <!-- <el-form-item v-else class="not-meet-requirements">
                        <el-button type="primary" @click="onSubmit">{{ $t('signJa.confirmChange') }}</el-button>
                        <el-button type="default" @click="handleClose">{{ $t('signJa.communicateSender1') }}</el-button>
                    </el-form-item> -->
                    </el-form>
                </template>
            </template>
            <template v-else>
                <!-- 新用户，未注册，没有名字 -->
                <section v-if="personStep === 1">
                    <div class="new-user-auth">
                        <p><!-- 发件方指定了{{ signer }}完成签署。如确认信息正确, 可直接签署。 -->{{ $t('signJa.beforeSignTip2', { signer }) }}</p>
                        <p class="margin-t-12"><!-- 如信息有误, 请与发件方联系, 更换指定的签署人信息。 -->{{ $t('signJa.beforeSignTip3') }}</p>
                        <div class="margin-t-12">
                            <el-button type="primary"><!--是我本人-->{{ $t('signJa.itsMe') }}</el-button>
                        </div>
                        <div class="margin-t-12">
                            <el-button type="default" @click="handleClose"><!--信息有误-->{{ $t('signJa.wrongInformation') }}</el-button>
                        </div>
                    </div>
                </section>
                <!-- 用户已实名注册，且名字与发件方指定的不一致 -->
                <section v-else>
                    <div class="different-names-auth">
                        <p>
                            <!-- 检测到该账号已注册的姓名为{{ currentUser }}, 与当前发件方要求的{{ signer }}不一致, 是否确认更换为{{ signer }} -->{{ $t('signJa.beforeSignTip4', { currentUser, signer }) }}</p>
                        <div class="margin-t-12">
                            <el-button type="primary"><!--确认更换-->{{ $t('signJa.confirmChange') }}</el-button>
                        </div>
                        <div class="margin-t-12">
                            <el-button type="default"><!--取消, 去与发件方沟通-->{{ $t('signJa.communicateSender2') }}</el-button>
                        </div>
                    </div>
                </section>
            </template>
        </div>
    </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
    props: {
        receiver: {
            type: Object,
            default: () => {},
        },
        signDecision: {
            type: String,
            default: '',
        },
        visible: {
            type: Boolean,
            default: false,
        },
        isEnt: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            entInfoForm: {
                entName: '',
                businessLicenceNumber: '',
                fileId: null,
            },
            meetRequirements: false,
            dialogVisible: true,
            personStep: 1,
            fileList: [],
            uploadStatus: 0,
            uploadHeaders: {
                Authorization: `bearer ${this.$cookie.get('access_token')}`,
            },
            dialogImageUrl: '',
            previewShow: false,
            registerRadio: 'JPN',
            areaList: [],
            otherArea: '',
        };
    },
    computed: {
        ...mapGetters([
            'getIsJa',
            'getIsUae',
        ]),        copNumLabel() {
            return this.registerRadio === 'JPN' ? this.$t('signJa.corporateNumber') : this.$t('signJa.comNum');
        },
        picLabel() {
            return this.registerRadio === 'JPN' ? this.$t('signJa.businessPic') : this.$t('signJa.buyRecord');
        },
        formItemWidth() {
            const map = {
                zh: '120px',
                ja: '152px',
                en: '182px',
            };
            return map[this.$i18n.locale] || '152px';
        },
        countryKey() {
            return this.$i18n.locale === 'zh' ? 'chineseName' : 'englishName';
        },
        rules() {
            return {
                entName: [{
                    required: true, message: this.$t('signJa.entNamePlaceholder'), trigger: 'blur',
                }],
                businessLicenceNumber: [{
                    required: true, message: this.copNumLabel, trigger: 'blur',
                }],
                fileId: [{
                    required: true, message: this.picLabel, trigger: 'blur',
                }],
            };
        },
        // ja环境选择are
        isJaEnvSelectAre() {
            return this.getIsJa && this.registerRadio === 'OTHER' && this.otherArea === 'ARE';
        },
        // are环境下选择other
        isAreEnvSelectOther() {
            return this.getIsUae && this.registerRadio === 'OTHER';
        },
    },
    watch: {
        receiver(val) {
            this.entInfoForm.entName = val.enterpriseName || '';
        },
    },
    methods: {
        // 检测图片格式
        checkImgFormat(file) {
            const checkSize = file.size / 1024 / 1024 < 5;
            const acceptType = ['image/png', 'image/jpeg', 'image/jpg'];
            const checkFormat = acceptType.some((type) => {
                return file.type === type;
            });

            // 判断图片大小
            if (!checkSize) {
                this.$MessageToast.error(this.$t('recharge.entSpecialInvoiceMap.picNotExceetTip')); // 图片大小不能超过5M
            }

            // 判断图片格式
            if (!checkFormat) {
                this.$MessageToast.error(this.$t('recharge.entSpecialInvoiceMap.picNotMeetRequire')); // 图片格式不符合要求
            }

            return checkSize && checkFormat;
        },
        // 上传前校验
        beforeUpload(file) {
            if (this.entInfoForm.fileId !== null) {
                return false;
            }
            const checkFormat = this.checkImgFormat(file);
            return checkFormat;
        },
        uploading() {
            this.uploadStatus = 1;
        },
        uploadSuccess(res) {
            this.entInfoForm.fileId = res.value;
            this.$refs.form.validateField('fileId');
            this.uploadStatus = 2;
        },
        uploadError(err) {
            const errMes = (err.response && err.response.data.message) ? err.response.data.message : this.$t('recharge.errorTip'); // 出错啦
            this.uploadStatus = 0;
            this.$MessageToast.error(errMes);
        },
        uploadRemove() {
            this.entInfoForm.fileId = null;
            this.uploadStatus = 0;
        },
        async handlePreview(file) {
            if (file.url) {
                this.dialogImageUrl = file.url;
            } else {
                this.dialogImageUrl = URL.createObjectURL(file.raw);
            }

            this.previewShow = true;
        },
        handleClose() {
            this.$emit('update:visible', false);
        },
        onSubmit() {
            let country = this.registerRadio;
            if (this.registerRadio === 'OTHER') {
                if (!this.otherArea) {
                    return this.$MessageToast.error(this.$t('signJa.selectArea'));
                }
                country = this.otherArea;
            }
            this.entInfoForm.country = country;
            this.$refs.form.validate((valid) => {
                if (valid) {
                    this.$http.post('ents/auth/ja', this.entInfoForm).then(() => {
                        location.reload();
                    });
                }
            });
        },
        getCountryList() {
            this.$http.get('/ents/countries').then(res => {
                this.areaList = res.data || [];
            });
        },
    },
    mounted() {
        this.getCountryList();
        if (this.getIsJa) {
            this.registerRadio = 'JPN';
        } else if (this.getIsUae) {
            this.registerRadio = 'ARE';
        }
    },
};
</script>

<style lang="scss">
$--color-primary: #127fd2 !default;
$--color-text-regular: #666666 !default;
.auth-before-signing .el-dialog{
    width: 680px;
    border-radius: 4px;
    .upload-item {
        .el-upload {
            display: block;
            text-align: left;
        }
        &.upload-item__disabled .upload-button:hover {
            border: 1px dashed #ccc;
            color: #999;
        }
    }
    .upload-button {
        width: 168px;
        height: 30px;
        border: 1px dashed #ccc;
        line-height: 28px;
        margin-top: 4px;
        font-size: 22px;
        color: #999;

        &:hover {
            border: 1px dashed #1280d2;
            color: #1280d2;
        }
    }
    .confirm-item{
        margin-top: 35px !important;
    }
    .upload-btn{
        width:  200px !important;
    }
    .register-area{
        padding-bottom: 20px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
    }
    .cn-tip{
        padding: 10px 10px 30px 10px;
        line-height: normal;
        color: $--color-text-regular;
        word-break: break-word;
        white-space: normal;
    }
    .tip-hint{
        font-size: 12px;
        line-height: 30px;
        color: $--color-text-regular;
        word-break: break-word;
        white-space: normal;
    }
    .bold-font{
        font-weight: 600;
    }
    .nomal-font{
        font-weight: 300;
    }
    .top-margin{
        margin-top: 10px;
    }
    .dialog-content{
        padding: 20px 22px;
        .el-button--primary{
            background-color: $background-color;
        }
        .el-form{
            margin-top: 20px;
            // margin: 28px auto 0;
            // width: 400px;
            .el-form-item{
                // margin: 0 auto 22px;
                // width: 300px;
                .el-button{
                    width: 106px;
                }
            }
            .not-meet-requirements{
                width: 100%;
                .el-form-item__content{
                    margin: 0 !important;
                    text-align: center;
                    .el-button{
                        width: 168px;
                    }
                }
            }
            .margin-b-12{
                margin-bottom: 12px;
            }
        }
        .new-user-auth, .different-names-auth{
            width: 308px;
            margin: 0 auto;
            text-align: center;
            .margin-t-12{
                margin: 12px 0 0 0;
                .el-button{
                    width: 108px;
                }
            }
        }
        .different-names-auth{
            .margin-t-12{
                .el-button{
                    width: 166px;
                }
            }
        }
    }
}
.en-page {
    .el-dialog{
        width: 750px !important;
    }
}
</style>
