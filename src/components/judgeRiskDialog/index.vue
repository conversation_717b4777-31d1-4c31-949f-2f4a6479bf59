<template>
    <div>

        <el-dialog
            :visible.sync="dialogVisible"
            size="tiny"
            append-to-body
            :show-close="false"
            class="share-view-dialog"
            @close="initInfo"
        >
            <div slot="title" class="share-view-dialog__header">
                <div class="share-view-dialog__header-left">
                    <p>{{ $t('judgeRisk.title') }}</p>
                    <!-- <div>
                        <el-button type="primary" @click="toPage('https://jsj.top/f/f6swCM ')">问卷反馈</el-button>
                    </div> -->
                </div>
                <i class="el-icon-ssq-guanbi" @click="$emit('update:visible', false)"></i>
            </div>
            <div class="risk-content" :class="{'locked': locked}" v-loading="loading" :element-loading-text="loadingText">
                <VueMarkdown class="terms-list__result" :source="risk"></VueMarkdown>
                <div v-show="showError">{{ errorContent }}</div>
                <div class="risk-content-rate" v-if="!!results.length && !feedback">
                    <i class="el-icon-ssq-cai-fill" v-if="feedback === 'DISLIKE'"></i>
                    <i class="el-icon-ssq-cai" v-else @click="feedbackRisk('DISLIKE')"></i>
                    <i class="el-icon-ssq-zan-fill" v-if="feedback === 'LIKE'"></i>
                    <i class="el-icon-ssq-zan" v-else @click="feedbackRisk('LIKE')"></i>
                </div>
                <div class="risk-content-rate-result" v-if="!!results.length && !!feedback">
                    <i class="el-icon-ssq-cai-fill" v-if="feedback === 'DISLIKE'"></i>
                    <i class="el-icon-ssq-cai" v-else></i>
                    <i class="el-icon-ssq-zan-fill" v-if="feedback === 'LIKE'"></i>
                    <i class="el-icon-ssq-zan" v-else></i>
                </div>
            </div>
            <div class="risk-content-mask" v-show="locked">
                <p @click="showLockContent">
                    <i class="el-icon-ssq-suo"></i>
                    <span>解锁完整的风险判断报告</span>
                    <i class="el-icon-ssq-xiangxiazhankai"></i>
                </p>
            </div>
        </el-dialog>
        <ChargeDialog
            :show="showChargeDialog"
            :onlyPerson="true"
            :productConfig="productConfig"
            :productType="productType"
            @handleClose="getPayResult"
            @hideDialog="showChargeDialog = false"
        />
    </div>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import * as judgeRisk from '../../api/judgeRisk.js';
import VueMarkdown from 'vue-markdown';
import ChargeDialog from '@/components/charge/index.vue';

export default {
    name: 'JudgeRiskDialog',
    components: {
        VueMarkdown,
        ChargeDialog,
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        contractId: {
            type: String,
            default: '',
        },
        receiverId: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            loading: false,
            loadingText: '正在判断合同风险',
            results: [],
            newAnnotationId: null,
            locked: false,
            feedback: '',
            showChargeDialog: false,
            productConfig: {
                title: this.$t('judgeRisk.title'),
                per: '份',
                unitPer: '份',
            },
            productType: [22, 23],
            risk: '',
            showError: false,
            errorContent: '',
        };
    },
    computed: {
        ...mapState({
            annotationId: state => state.commonHeaderInfo.annotationId,
        }),
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(v) {
                this.$emit('update:visible', v);
            },
        },
    },
    watch: {
        visible: {
            handler(val) {
                if (val && this.annotationId) {
                    this.newAnnotationId = this.annotationId;
                    this.getHistoryInfo();
                    return;
                }
                if (val) {
                    this.judgeRisk();
                }
            },
            immediate: true,
        },
    },
    methods: {
        ...mapActions('approval', ['getAnnotationHistory']),
        judgeRisk() {
            this.loading = true;
            judgeRisk.doRiskJudgment(this.contractId, this.receiverId, {
                contractId: this.contractId,
                receiverId: this.receiverId,
            }).then(res => {
                // this.stringToMarkdown(res.data.lockedContent);
                this.risk = res.data.lockedContent.replace(/\n\n/g, '\n<br/>\n\n');
                this.newAnnotationId = res.data.annotationId;
                this.locked = res.data.locked || false;
                this.feedback = res.data.feedback;
            }).catch(err => {
                this.showError = true;
                this.errorContent = err.response?.data?.message || err.message || err.response?.data?.data;
                if (err.response.data.code === '130001') {
                    this.$MessageToast.error(err.response.data.data);
                }
            }).finally(() => {
                this.loading = false;
            });
        },
        getHistoryInfo() {
            this.loading = true;
            // this.loadingText = '正在获取合同信息';
            judgeRisk.queryRiskJudgmentResult(this.contractId, this.receiverId, {
                annotationId: this.newAnnotationId,
            }).then(res => {
                this.risk = res.data.lockedContent.replace(/\n\n/g, '\n<br/>\n\n');
                // this.results = res.data.lockedContent.split('\n').map(ele => ele += '\n');
                this.newAnnotationId = res.data.annotationId;
                this.locked = res.data.locked || false;
                this.feedback = res.data.feedback;
            }).finally(() => {
                this.loading = false;
            });
        },
        getPayResult(res) {
            this.showChargeDialog = false;
            if (res) {
                this.unlockRisk().catch((err) => {
                    this.$MessageToast.error(err?.data || err?.message);
                });
            }
        },
        unlockRisk() {
            this.loading = true;
            return new Promise((resolve, reject) => {
                judgeRisk.unlockRiskJudgmentResult(this.contractId, this.receiverId, {
                    annotationId: this.newAnnotationId,
                }).then(res => {
                    this.risk = res.data.lockedContent.replace(/\n\n/g, '\n<br/>\n\n');
                    this.newAnnotationId = res.data.annotationId;
                    this.locked = res.data.locked || false;
                    this.feedback = res.data.feedback;
                    resolve();
                }).catch((err) => {
                    reject(err.response.data);
                }).finally(() => {
                    this.loading = false;
                });
            });
        },
        showLockContent() {
            this.unlockRisk().catch(() => {
                this.showChargeDialog = true;
            });
        },
        feedbackRisk(feedback) {
            judgeRisk.feedbackRiskJudgmentResult(this.contractId, this.receiverId, {
                annotationId: this.newAnnotationId,
                feedback,
            }).then(() => {
                this.feedback = feedback;
            });
        },
        initInfo() {
            this.loading = false;
            this.results = '';
            this.newAnnotationId = '';
            this.locked = true;
            this.showError = false;
            this.errorContent = '';
            this.feedback = '';
            this.$store.state.commonHeaderInfo.annotationId = '';
            this.getAnnotationHistory();
        },
        toPage(url) {
            window.open(url);
        },
    },
};
</script>

<style lang="scss" scoped>
.share-view-dialog {
    &__header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        &-left {
            flex-grow: 1;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-right: 10px;
        }
        p {
            font-size: 18px;
        }
        i {
            cursor: pointer;
        }
    }
    .el-button {
        border-radius: 5px;
    }
    &::v-deep .el-dialog {
        width: 580px;
        max-height: 80vh;
        position: relative;
        &__body {
            max-height: calc(80vh - 85px);
            min-height: 235px;
            overflow-y: auto;
            box-sizing: border-box;
            padding-bottom: 40px!important;
        }
        &__footer {
            // position: absolute;
            background-color: #fff;
            // bottom: 0;
            // left: 0;
            width: 100%;
            padding: 0px 30px 10px!important;
        }
        .risk-content {
            position: relative;
            min-height: 235px;
            &.locked {
                max-height: 30vh;
                overflow: hidden;
            }
            .input-tip {
                height: 20px;
                font-size: 12px;
                line-height: 12px;
                color: #999999;
            }
            &-rate, &-rate-result {
                margin-top: 10px;
                direction: rtl;
                i {
                    font-size: 20px;
                    margin-left: 14px;
                }
            }
            &-rate i{
                cursor: pointer;
            }
            &-mask {
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 50%;
                background: linear-gradient(to bottom, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 1));
                display: flex;
                flex-direction: column-reverse;
                p {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding-bottom: 10px;
                    cursor: pointer;
                    span {
                        display: inline-block;
                        margin: 0 6px;
                    }
                    .el-icon-ssq-xiangxiazhankai {
                        font-size: 10px;
                    }
                }
            }
        }
        .terms-list {
            &__result {
                user-select: text;
                ul, ol{
                    padding-left: 20px;
                    font-size: 0;
                    li {
                        font-size: 14px;
                    }
                }
                ol{
                    li {
                        list-style: decimal;
                        li {
                            list-style: disc;
                            li {
                                list-style: circle;
                            }
                        }
                    }
                }
                ul {
                    li {
                        list-style: disc;
                        li {
                            list-style: circle;
                        }
                    }
                }
                code {
                    white-space: normal;
                    word-break: break-all;
                }
            }
        }
    }
}
</style>
