<!--
 * @Author: fan_liu
 * @Date: 2021-01-26 10:42:40
 * @LastEditors: fan_liu
 * @LastEditTime: 2021-03-02 16:00:00
 * @Description: Do not edit
-->
<template>
    <el-dialog
        class="data-box__invite"
        :visible.sync="visible"
        :modal-append-to-body="true"
        :before-close="handleClose"
        :title="$t('dataBoxInvite.title')"
    >
        <div class="data-box__invite_content">
            <div class="data-box__invite_step">
                <div class="data-box__invite_step-title">
                    <i class="step-num">1</i>
                    <span class="step-title">{{ $t('dataBoxInvite.step1') }}</span>
                    <div class="qr-box">
                        <VueQr :logoSrc="logoSrc" :text="shareUrl" :size="110" :margin="0">
                        </VueQr>
                    </div>
                    <a class="download_code" :href="imgSrc" :download="`${$t('dataBoxInvite.imgName')}.png`">
                        <span class="link" @click="handleSaveQrcodeToLocal">{{ $t('dataBoxInvite.saveQrcode') }}</span>
                    </a>
                    <el-input readonly v-model="shareUrl" class="url-clipboard">
                        <template slot="append"><div class="copy-btn" @click="handleCopyUrl">{{ $t('dataBoxInvite.copy') }}</div></template>
                    </el-input>
                </div>
            </div>
            <div class="data-box__invite_step">
                <div class="data-box__invite_step-title">
                    <i class="step-num">2</i>
                    <span class="step-title">{{ $t('dataBoxInvite.step2') }}</span>
                    <img class="default-img" src="~img/data-box-step2.png" alt="">
                </div>
            </div>
            <div class="data-box__invite_step">
                <div class="data-box__invite_step-title">
                    <i class="step-num">3</i>
                    <span class="step-title">{{ $t('dataBoxInvite.step3') }}</span>
                    <img class="default-img" src="~img/data-box-step3.png" alt="">
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<script>
import VueQr from 'vue-qr';
import { LOGO_PNG } from 'utils/constants.js';
export default {
    components: {
        VueQr,
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            shareUrl: 'https://ent.bestsign.tech/s/rJEunmQb',
            imgSrc: '',
            logoSrc: LOGO_PNG,
        };
    },
    methods: {
        handleClose() {
            this.$emit('update:visible', false);
        },
        // 保存二维码到本地
        handleSaveQrcodeToLocal() {
            this.imgSrc = document.querySelector('.qr-box img') ? document.querySelector('.qr-box img').currentSrc : '';
        },
        // 复制短链接
        handleCopyUrl() {
            this.$copyText(this.shareUrl).then(() => {
                this.$MessageToast({
                    message: this.$t('dataBoxInvite.copySuccess'),
                    type: 'success',
                });
            }, () => {
                this.$MessageToast.error(this.$t('dataBoxInvite.copyFailed'));
            });
        },
    },
    created() {
        this.$http.get('/octopus-api/box/archive/default/url').then(res => {
            this.shareUrl = res.data;
        });
    },
};
</script>

<style lang="scss">
    .data-box__invite{
        .el-dialog{
            width: 920px;
            border-radius: 4px;
            .el-dialog__body{
                padding: 0;
            }
            .el-dialog__header{
                padding: 0 30px;
                line-height: 50px;
                border-bottom: 1px solid #eee;
            }
        }
        &_content{
            display: flex;
            height: 328px;
        }
        &_step{
            flex: 1;
            text-align: center;
            padding: 35px 0 50px;
            font-size: 12px;
            &-title{
                line-height: 18px;
                .step-num{
                    color: #fff;
                    display: inline-block;
                    width: 18px;
                    height: 18px;
                    background: #127FD2;
                    border-radius: 18px;
                    margin-right: 5px;
                }
                .step-title{
                    color: #127FD2;
                }
            }
            .default-img{
                width: 240px;
                margin-top: 25px;
            }
            .qr-box{
                width: 120px;
                height: 120px;
                border: 1px solid #ccc;
                box-sizing: border-box;
                margin: 25px auto 15px;
                img{
                    margin: 4px;
                }
            }
            .url-clipboard{
                margin-top: 10px;
                width: 266px;
                input{
                    color: #127FD2;
                    border-radius: 4px 0 0 4px;
                }
                .el-input-group__append{
                    color: #127FD2;
                    cursor: pointer;
                }
            }
        }
    }
</style>
