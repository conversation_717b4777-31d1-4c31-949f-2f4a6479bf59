<!--
    请求他人认证表单
-->
<template>
    <!--请求他人认证-->
    <el-dialog :visible="visible" :before-close="handleClose" class="ask-sign-wrapper">
        <div class="ask-sign-component">
            <p class="subject-name-tip" style="margin-bottom: 30px">{{ $t('authIntercept.requestSomeoneList') }}</p>
            <el-form :model="askSignInput" label-width="40px">
                <el-form-item :label="$t('authIntercept.ent')">
                    <el-input :placeholder="$t('authIntercept.entName')" v-model="askSignInput.entName"></el-input>
                </el-form-item>
                <el-form-item :label="$t('authIntercept.account')">
                    <el-input :placeholder="$t('authIntercept.accountPH')" v-model="askSignInput.account"></el-input>
                </el-form-item>
            </el-form>
            <div class="ent-auth-btn">
                <el-button type="primary" @click.stop.prevent="goToAsk">{{ $t('authIntercept.send') }}</el-button>
            </div>
        </div>
    </el-dialog>
</template>

<script>
export default {
    props: {
        entName: {
            type: String,
            default: '',
        },
        contractId: {
            type: String,
            default: '',
        },
        visible: {
            default: true,
            type: Boolean,
        },
    },
    data() {
        return {
            askSignInput: {
                entName: this.entName || '',
                account: '',
            },
            visable: true,
        };
    },
    methods: {
        goToAsk() {
            const { account, entName } = this.askSignInput;
            const contractId = this.contractId;
            // 邮箱和手机
            const reg = new RegExp(
                '(^[\\w.\\-]+@(?:[a-z0-9]+(?:-[a-z0-9]+)*\\.)+[a-z]{2,3}$)|(^1\\d{10}$)',
            );
            if (!entName.trim()) {
                this.$MessageToast.error(this.$t('authIntercept.lackEntName'));
                return;
            }
            if (!reg.test(account)) {
                this.$MessageToast.error(this.$t('authIntercept.errAccount'));
                return;
            }
            this.$http.post('/users/request-auth/notice', {
                account,
                entName,
                contractId,
            }).then(() => {
                this.$MessageToast.success(this.$t('authIntercept.successfulSent'));
            }).finally(() => {
                this.$emit('close');
            });
        },
        handleClose() {
            this.$emit('close');
        },
    },
};
</script>

<style lang="scss">
    .ask-sign-wrapper {
        width: 50%;
        margin: 0 auto;
        .el-dialog__header {
            border:none;
        }
    }
    .ask-sign-component {
        width: 100%;
    }
    .ent-auth-btn {
        text-align: center;
        button {
            width: 100%;
        }
    }
</style>
