<template>
    <div class="portrait-wrapper">
        <img v-if="src" :src="src" alt="" :width="size" :height="size">
        <template v-else>
            <i class="el-icon-ssq-user-filling i-img"></i>
        </template>
        <slot></slot>
    </div>
</template>
<script>
export default {
    props: {
        src: {
            type: String,
            default: '',
        },
        size: {
            type: Number,
            default: 39,
        },
    },
    data() {
        return {
            token: this.$cookie.get('access_token'),
        };
    },
};
</script>
<style lang="scss">
    .portrait-wrapper {
        position: relative;
        display: inline-block;
        width: 30px;
        height: 30px;
        border-radius: 4px;
        background-color: #eee;
        .i-img {
            color: #fff;
            width: 39px;
            height: 39px;
            font-size: 39px;
        }
    }
</style>
