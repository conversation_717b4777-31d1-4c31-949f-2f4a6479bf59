<!-- 获取验证码倒计时 -->
<template>
    <button class="count-down brand-color"
        :disabled="disabled || time > 0"
        @click.prevent="clickedFn"
        :class="{active: time > 0}"
    >
        {{ text }}
    </button>
</template>
<script>
export default {
    name: 'CountDown',
    props: {
        isWap: {
            type: Boolean,
            default: false,
        },
        second: {
            type: Number,
            default: 60,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        // eslint-disable-next-line vue/require-default-prop
        clickedFn: {
            type: Function,
        },
        runFlag: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            time: 0,
        };
    },
    computed: {
        text: function() {
            if (this.isWap) {
                return this.time > 0 ? `${this.$t('login.resendCode')}(${this.time}s)` : this.$t('login.getVerifyCode');
            } else {
                return this.time > 0 ? `${this.time}s` : this.$t('login.getVerifyCode');
            }
        },
    },
    watch: {
        runFlag: function(val) {
            if (val) {
                this.run();
            }
        },
    },
    methods: {
        reset() {
            this.time = 0;
        },
        run: function() {
            this.time = this.second;
            this.timer();
        },
        timer: function() {
            const inTimer = setInterval(() => {
                if (this.time > 0) {
                    this.time--;
                } else {
                    clearInterval(inTimer);
                }
            }, 1000);
        },
    },
};
</script>
<style lang="scss">
	.count-down {
		background-color: #f6f6f6;
		color: #127fd2;
		border: 1px solid #ccc;
		font-size: 14px;
		text-align: center;
		cursor: pointer;
		border-radius: 1px;
		&:hover {
			background-color: #fff;
		}
		&.active {
			background-color: #fff;
			color: #999;
		}
	}
</style>
