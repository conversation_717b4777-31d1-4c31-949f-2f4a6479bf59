<!-- 通用footer -->
<template>
    <JaFooter v-if="getIsForeignVersion" />
    <!-- 二级域名下显示定制footer -->
    <CustomFooter v-else-if="!isEntHost" class="login-footer"></CustomFooter>
    <footer v-else class="register-footer">
        <ul class="clear font-size-zero">
            <li class="lang-switch-btn">
                <LangSwitch></LangSwitch>
                <i>|</i>
            </li>
            <li class="open-platform">
                <a target="_blank" :href="`https://${openHost}/#/login`">
                    <span>{{ $t('common.openPlatform') }}</span>
                </a>
                <i>|</i>
            </li>
            <li class="about">
                <a target="_blank" @click="handleFooterClick('about')">
                    <span>{{ $t('common.aboutBestSign') }}</span>
                </a>
                <i>|</i>
            </li>
            <li class="contact">
                <a target="_blank" @click="handleFooterClick('contact')">
                    <span>{{ $t('common.contact') }}</span>
                </a>
                <i>|</i>
            </li>
            <li class="recruitment">
                <a target="_blank" @click="handleFooterClick('recruitment')">
                    <span>{{ $t('common.recruitment') }}</span>
                </a>
                <i>|</i>
            </li>
            <li class="help">
                <a target="_blank" @click="handleFooterClick('help')">
                    <span>{{ $t('common.help') }}</span>
                </a>
                <i>|</i>
            </li>
            <li class="copyright">
                <span>V4.0.0 {{ $t('common.copyright') }} © {{ backEndCopyRightRange || defaultCopyRightRange }} {{ $t('common.company') }}</span>
                <i>|</i>
            </li>
            <li class="on-record">
                <span>{{ $t('home.record') }}</span>
            </li>
            <li class="on-record" v-if="isHubblePage">
                <i>|</i>
                <span>{{ $t('commonFooter.hubbleRecordId') }}</span>
            </li>
        </ul>
    </footer>
</template>

<script>

import LangSwitch from 'components/langSwitch';
import CustomFooter from 'components/customFooter/CustomFooter.vue';
import { mapState, mapGetters } from 'vuex';
import JaFooter from './JaFooter/index.vue';
export default {
    components: {
        LangSwitch,
        CustomFooter,
        JaFooter,
    },
    data() {
        return {
            // openHost: window.location.host.includes('info') ? 'openapi.bestsign.info' : 'openapi.bestsign.cn',
            // host: window.location.host.includes('info') ? 'www.bestsign.info' : 'www.bestsign.cn',
            openHost: this.GLOBAL.ENV_NAME === 'PRE_ENV' ? 'openapi.bestsign.info' : 'openapi.bestsign.cn',
            host: this.GLOBAL.ENV_NAME === 'PRE_ENV' ? 'www.bestsign.info' : 'www.bestsign.cn',
            backEndCopyRightRange: this.$cookie.get('copyRightRange'),
            defaultCopyRightRange: `2014-${new Date().getFullYear()}`,
        };
    },
    computed: {
        isBrand() {
            return ~~this.$cookie.get('isBrand') === 1;
        },
        // 判断二级域名是否是ent
        isEntHost() {
            const fullHost = window.location.href;
            const secondHost = fullHost.split('.')[0].split('//')[1];
            return secondHost === 'ent';
        },
        lang() {
            return this.$i18n.locale;
        },
        ...mapState(['commonHeaderInfo']),
        accessToken() {
            return Vue.$cookie.get('access_token') === null ? '' : Vue.$cookie.get('access_token');
        },
        ...mapGetters(['getIsForeignVersion']),
        isHubblePage() {
            return this.$route.meta.isHubblePage === true;
        },
    },
    methods: {
        async handleFooterClick(type) {
            const anonymousID  = await this.$sensors.getAnonymousID();
            const url = `?pageFrom=ent&identifyId=${anonymousID}`;
            const map = {
                about: `https://${this.host}/about/about-us${url}`,
                contact: `https://${this.host}/contact-us${url}`,
                recruitment: `https://${this.host}/about/join-us${url}`,
                help: `https://${this.host}/help/FAQ${url}&entId=${this.commonHeaderInfo.currentEntId}&token=${this.accessToken}`,

            };
            window.open(map[type]);
            return false;
        },
    },
};
</script>

<style lang="scss">
	$border-color: #ddd;
	footer.register-footer {
        box-sizing: border-box;
		width: 100%;
		height: 35px;
		// line-height: 35px;
		padding-top: 10px;
		// padding-bottom: 15px;
		border-top: 1px solid $border-color;
        background-color: #f6f6f6;
		ul {
			display: block;
			width: 100%;
			text-align: center;
			white-space: nowrap;
			overflow: hidden;
			li {
				display: inline-block;
				font-size: 12px;
				color: #666;
                cursor: pointer;
				i {
					display: inline-block;
					margin: 0 5px;
					color: #d6d6d6;
				}

				span:last-child {
					color: #999;
				}

				i:last-child {
					margin: 0 10px;
				}

                &.lang-switch-btn {
                    span {
                        font-size: 12px;
                        color: #999999;
                    }
                    i.el-icon-ssq-diqiu {
                        margin: 0;
                        padding-right: 5px;
                        vertical-align: bottom;
                    }
                }
			}
		}
	}

</style>
