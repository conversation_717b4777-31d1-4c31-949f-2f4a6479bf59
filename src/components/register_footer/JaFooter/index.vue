<!-- 业务/基础组件： -->
<template>
    <footer class="register-footer">
        <ul class="clear font-size-zero">
            <li v-if="isCanSwitchLanguage" class="lang-switch-btn">
                <LangSwitch :cacheOnly="langCacheOnly"></LangSwitch>
                <i>|</i>
            </li>
            <li class="about">
                <a target="_blank" :href="`https://www.bestsign.com/about-us`">
                    <span>{{ $t('commonFooter.aboutBestSign') }}</span>
                </a>
                <i>|</i>
            </li>
            <li class="copyright">
                <span>V4.0.0 {{ $t('commonFooter.copyright') }} © {{ backEndCopyRightRange || defaultCopyRightRange }} {{ $t('commonFooter.company') }}</span>
            </li>
        </ul>
    </footer>
</template>
<script>
import LangSwitch from 'components/langSwitch';
import { mapState } from 'vuex';

export default {
    name: 'JaFooter',
    components: {
        LangSwitch,
    },
    props: {
        langCacheOnly: {
            default: false,
            type: <PERSON>olean,
        },
    },
    data() {
        return {
            backEndCopyRightRange: this.$cookie.get('copyRightRange'),
            defaultCopyRightRange: `2014-${new Date().getFullYear()}`,
        };
    },
    computed: {
        ...mapState(['features']),
        isCanSwitchLanguage() { // 是否可以切换语言
            return true;
            // return this.features.includes('180');
        },
    },
    methods: {
    },
};
</script>
<style lang='scss'>

</style>
