<!--加密签署、二要素认证校验弹窗-->
<template>
    <el-dialog
        :visible.sync="dialogVisible"
        :show-close="true"
        :title="$t('twoFactor.signTip')"
        class="password-two-factor-verify"
    >
        <div class="dialog-content">

            <div class="dialog-top-tip"><i class="el-icon-warning"></i>{{ ['twoFactorSign'].includes(showType) ? $t('twoFactor.twoFactorTip') : (showType==='encryptionSign'? $t('twoFactor.passwordTip') : $t('twoFactor.twoFactorAndPasswordTip')) }}</div>
            <div v-if="['twoFactorSign', 'both'].includes(showType)">
                <span class="required-icon">*</span><span class="dynamic-code-label">{{ $t('twoFactor.dynamicCode') }}</span>
                <el-input v-model="form.dynamicCode" :placeholder="$t('twoFactor.pleaseInput')" class="dynamic-code-input" :maxlength="6"></el-input>
            </div>
            <div v-if="['encryptionSign', 'both'].includes(showType)">
                <span class="required-icon">*</span><span class="password-label">{{ $t('twoFactor.password') }}</span>
                <el-input v-model="form.password" class="password-input" :placeholder="$t('twoFactor.pleaseInput')" :maxlength="8" :minlength="4"></el-input>
            </div>
            <div class="dialog-bottom-tip" v-if="['encryptionSign', 'both'].includes(showType)">{{ $t('twoFactor.passwordTip2') }}</div>
        </div>
        <div slot="footer" class="dialog-footer">
            <el-button
                type="primary"
                @click="handleConfirm"
            >{{ $t('twoFactor.confirmSign') }}
            </el-button>
        </div>
    </el-dialog>
</template>
<script>
export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        encryptionType: {
            type: Object,
            default: () => {
                return {
                    encryptionSignConfig: false,
                    twoFactorAuthentication: false,
                };
            },
        },
    },
    data() {
        return {
            form: {
                password: '',
                dynamicCode: '',
            },
        };
    },
    computed: {
        dialogVisible: {
            set(value) {
                this.$emit('update:visible', value);
            },
            get() {
                return this.visible;
            },
        },
        showType() {
            let type = '';
            if (this.encryptionType.encryptionSignConfig && !this.encryptionType.twoFactorAuthentication) {
                type = 'encryptionSign';
            } else if (!this.encryptionType.encryptionSignConfig && this.encryptionType.twoFactorAuthentication) {
                type = 'twoFactorSign';
            } else if (this.encryptionType.encryptionSignConfig && this.encryptionType.twoFactorAuthentication) {
                type = 'both';
            }
            return type;
        },
    },
    methods: {
        handleConfirm() {
            this.$emit('confirmSavePassword', this.form);
        },
    },
};
</script>
<style lang="scss">
.password-two-factor-verify {
    .el-dialog {
        width: 400px;
    }
    .dialog-content {
        padding: 20px 20px;
    }
    .el-icon-warning{
        font-size: 14px;
        color:#F2A93E;
    }
    .required-icon {
        color: #FF4444;
        margin-right: 2px;
    }
    .password-input, .dynamic-code-input {
        display: inline-block;
        width: 240px;
        margin: 0 0 10px 13px;
    }
    .password-label, .dynamic-code-label {
        display: inline-block;
        width: 90px;
    }
    .dialog-top-tip {
        margin-bottom: 10px;
    }
    .dialog-bottom-tip {
        color: #999;
        font-size: 12px;
    }
}

</style>
