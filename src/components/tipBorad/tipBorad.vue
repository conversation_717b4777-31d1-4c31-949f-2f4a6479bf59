<!-- 公告弹窗 -->
<template>
    <el-dialog
        title="公告"
        class="update-tip-pop el-dialog-bg notice-dialog"
        :class="!isPC && 'mobile-dialog'"
        :visible.sync="showNoticeDialog"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
    >
        <div class="updateTip">
            <span>尊敬的上上签客户，您好:</span>
            <p>因收到全国公民身份号码查询中心的通知，对方计划于2019年6月14日23：00至6月15日04：30（周五-周六）进行系统停机维护和升级，在此期间将暂停对外提供的信息查询服务。届时，您查询个人信息相关的服务会受到影响，如“个人二要素”、“个人三要素”、“个人四要素”、“人脸比对”、“企业四要素”等。</p>
            <p>请您及时做好预警工作，避免因此给您带来困扰。</p>
            <p>感谢您对上上签一如既往的支持!</p>
            <p style="margin-top: 25px">
                <a target="_blank" href="http://www.nciic.com.cn/framework/gongzuo/bgififoa-abbo-bbnp-inlg-mbnalcpcbpan.do?isfloat=1&disp_template=hhecjhljhfhbbbnnldnjnnnkpcfpcodb&fileid=20190603154859332">关于全国公民身份号码查询中心服务暂停（20190614-20190615）的通知</a>
            </p>
        </div>
        <el-button slot="footer"
            type="primary"
            class="btn btn-type-one"
            @click="showNoticeDialog = false"
        >知道了</el-button>
    </el-dialog>
</template>
<script>
import { isPC } from 'src/common/utils/device.js';
export default {
    data() {
        return {
            isPC: isPC(),
            showNoticeDialog: false,
        };
    },
    methods: {
        autoShowNotice() {
            // 2019.06.11关于身份证识别的公告是否显示, 超过endTime自动下线
            const currentTime = new Date().getTime();
            const startTime = new Date('2019/06/14 23:00').getTime();
            const endTime = new Date('2019/06/15 04:30').getTime();
            this.showNoticeDialog = (currentTime > startTime && currentTime < endTime);
        },
    },
    created() {
        this.autoShowNotice();
    },
};
</script>

<style lang="scss">
    .mobile-dialog .el-dialog{
        width: 90%;

        .btn-type-one{
            padding: 10px 15px;
        }
    }
</style>
