<template>
    <!-- 相册组件 -->
    <div class="gallery-wrap" v-if="visible">
        <div class="toolbox-top">
            <i class="close-btn el-icon-ssq-guanbi" @click="$emit('close')"></i>
        </div>
        <div class="content">
            <div class="title">{{ title }}</div>
            <div class="photo-item">
                <div
                    v-for="(item, index) in imgList"
                    :key="item"
                    class="gallery-img-wrap"
                    v-loading="loadings[index]"
                >
                    <img v-lazy="item" @load="doLoad(index)">
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'Gallery',
    // eslint-disable-next-line vue/require-prop-types
    props: ['title', 'src', 'visible'],
    data() {
        return {
            loadings: [],
        };
    },
    computed: {
        imgList() {
            if (!this.src) {
                return [];
            }
            if (typeof this.src === 'string') {
                return [this.src];
            }
            return this.src.map(item => `${item}?access_token=${this.$cookie.get('access_token')}`);
        },
    },
    watch: {
        imgList(nv) {
            this.loadings = nv.map(() => true);
        },
        visible(val) {
            if (val) {
                document.body.appendChild(this.$el);
            }
        },
    },
    methods: {
        doLoad(index) {
            this.loadings.splice(index, 1, false);
        },
    },
};
</script>

<style lang="scss" scoped>
    $height: 50px;
    .gallery-wrap {
        z-index: 9001;
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        background-color: rgba(0, 0, 0, 0.6);
        overflow: scroll;
        .toolbox-top {
            height: $height;
            width: 100%;
            position: fixed;
            background-color: #000;
            .close-btn {
                display: inline-block;
                cursor: pointer;
                float: right;
                height: $height;
                width: $height;
                line-height: $height;
                text-align: center;
                color: #fff;
                font-size: 16px;
                margin-right: 15px;
            }
        }
        .content {
            width: 1000px;
            margin: 100px auto 0;
            background: #eaebed;
            padding: 18px 30px;
            .title {
                padding-bottom: 8px;
                text-align: center;
                font-size: 24px;
            }
            .photo-item {
                text-align: center;
                img {
                    max-width: 100%;
                    margin-top: 10px;
                }
            }
        }
        .gallery-img-wrap {
            min-height: 100px;
        }
    }
</style>
