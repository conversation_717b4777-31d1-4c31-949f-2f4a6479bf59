<template>
    <div class="seal-item"
        :class="{'active-seal': isMoving && canDrag, 'widden': !canDrag}"
        :style="{ top: `${(1 - top - ridingseal.height) * 100}%`}"
    >
        <p class="seal-title" :title="ridingseal.roleName">{{ ridingseal.roleName }}</p>
        <div
            class="seal-content"
            @mousedown.stop="onMousedown($event)"
            :style="{
                backgroundColor: `rgba(${color}, 0.8)`,
                pointerEvents: canDrag ? 'all' : 'none',
            }"
        >
            <div class="seal-icon">
                <i class="el-icon-ssq-qifengzhang1"></i>
            </div>
        </div>
        <i v-if="templateStatus === 'edit'" class="close-icon el-icon-ssq-guanbi" @click="handleDelete"></i>
    </div>
</template>
<script>
export default {
    props: {
        templateStatus: {
            type: String,
            default: 'edit',
        },
        ridingseal: {
            type: Object,
            default: () => {},
        },
        color: {
            type: String,
            default: '',
        },
        pageHeight: {
            type: Number,
            default: 1122,
        },
        canDrag: {
            type: Boolean,
        },
        scale: {
            type: Number,
            default: 1,
        },
    },
    data() {
        return {
            top: this.ridingseal.y,
            moveY: 0, // 记录mousedown时鼠标的起始位置
            isMoving: false,
        };
    },
    watch: {
        'ridingseal.y': {
            handler(newVal) {
                this.top = newVal;
            },
            immediate: true,
        },
    },
    methods: {
        handleDelete() {
            this.$emit('delete');
        },
        onMousedown(e) {
            if (this.templateStatus !== 'edit') {
                return;
            }
            this.isMoving = true;
            this.moveY = e.clientY;
            // 这里不将事件绑定在mark上的原因是，鼠标跟随有延迟时，会导致mark leave
            document.addEventListener('mousemove', this.onMousemove);
            document.addEventListener('mouseup', this.onMouseup);
        },
        onMousemove(e) {
            if (this.templateStatus !== 'edit') {
                return;
            }
            if (!this.isMoving) {
                return;
            }
            const { y: sealY, height } = this.ridingseal;
            const distanceY = e.clientY - this.moveY;
            // // Y轴方向移动的距离
            let y = sealY - distanceY / (this.pageHeight * this.scale);
            if (y < 0) {
                y = 0;
            }
            if (y > 1 - height) {
                y = 1 - height;
            }
            this.top = y;
        },
        onMouseup() {
            if (this.templateStatus !== 'edit') {
                return;
            }
            if (!this.isMoving) {
                return;
            }
            this.isMoving = false;
            this.$emit('update-server', this.top);
            document.removeEventListener('mousemove', this.onMousemove);
            document.removeEventListener('mouseup', this.onMouseup);
        },
    },
};
</script>

<style lang="scss">
    .riding-seals {
        position: absolute;
        box-sizing: border-box;
        padding-top: 1px;
        width: 160px;
        border: 1px dashed #127fd2;
        height: 100%;
        background: rgba(18, 127, 210, 0.05);
        top: 0;
        bottom: 0;
        right: -116px;
        z-index: 100;
        cursor: move;
        box-sizing: border-box;
        padding-top: 1px;
        border: 1px dashed #127fd2;
        height: 100%;
        background: rgba(18, 127, 210, 0.05);

        &-bg {
            margin-left: 41px;
            width: 116px;
            height: 100%;
            background: #FAFAFA;
            background-image: linear-gradient(270deg, rgba(255, 255, 255, 0.50) 0%, rgba(217, 217, 217, 0.50) 100%);
            background-size: 23px;
        }
        .seal-item {
            position: absolute;
            transform: translate(0px, -20px);
            .seal-title {
                padding: 0 4px;
                margin-bottom: 2px;
                height: 18px;
                line-height: 18px;
                max-width: 146px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                box-sizing: border-box;
                background-color: rgb(255, 247, 182);
                user-select: none;
                font-size: 12px;
            }
            .seal-content {
                border-radius: 4px;
                width: 150px;
                height: 160px;
                border: 2px solid transparent;
                box-sizing: border-box;
                .seal-icon {
                    width: 130px;
                    height: 130px;
                    border-radius: 50%;
                    text-align: center;
                    margin: 13px auto 0;
                    background-color: rgba(255, 255, 255, 0.3);
                    .el-icon-ssq-qifengzhang1 {
                        transform: translate(0, 45%);
                        font-size: 70px;
                        color: #000;
                    }
                }
                &:hover {
                    cursor: move;
                }
            }

            &.widden {
                .seal-title {
                    max-width: 158px;
                }
                .seal-content {
                    width: 154px;
                    .seal-icon {
                        margin-left: 12px;
                    }
                }
            }
            &.active-seal {
                .seal-title {
                    // position: relative;
                    z-index: 999;
                }
                .seal-content {
                    border: 2px solid #127fd2;
                    // position: relative;
                    z-index: 999;
                }
                .close-icon {
                    z-index: 9999;
                }
            }
            .close-icon {
                color: white;
                font-size: 12px;
                background-color: rgba(51, 51, 51, .75);
                padding: 5px;
                border-radius: 12px;
                top: 9px;
                position: absolute;
                right: -6px;
                cursor: pointer;
            }
        }
    }
</style>
