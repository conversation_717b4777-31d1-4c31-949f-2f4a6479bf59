<template>
    <g class="seal-item"
        :class="{'active-seal': isActive, 'cur-default': !isEdit}"
        :transform="`matrix(1,0,0,1, 0, ${isActive ? top : ridingseal.y})`"
    >
        <g class="cur-default">
            <rect
                x="0"
                y="-26"
                :width="componentWidth"
                height="24"
                fill="#fff7b6"
            />
            <text
                x="6"
                y="-9"
                fill="#333"
                font-size="12"
                text-anchor="start"
            >
                {{ ridingseal.roleName }}
            </text>
        </g>
        <!-- 矩形背景 -->
        <rect
            class="seal-bg"
            fill-opacity="0.75"
            :fill="color"
            stroke="#127fd2"
            :stroke-width="isActive ? 2 : 0"
            :width="componentWidth"
            height="160"
            rx="2"
            ry="2"
            @mousedown="handleRidingSealMouseStart($event)"
            @contextmenu.prevent="openContextmenu"
        >
        </rect>

        <circle :cx="isEdit ? 75 : 80"
            cy="80"
            r="68"
            fill="#fff"
            opacity=".3"
            style="pointer-events: none;"
        />
        <svg class="icon" aria-hidden="true">
            <use style="pointer-events: none;"
                :x="isEdit ? 25 : 30"
                y="45"
                width="100"
                height="70"
                xlink:href="#el-icon-ssq-qifengzhang1"
            >
            </use>
        </svg>
        <g class="del-btn" @click="handleDelete" v-if="isEdit">
            <circle :cx="componentWidth" cy="0" r="11" fill="#333" opacity=".75" />
            <use style="pointer-events: none;"
                :x="145"
                :y="-5"
                width="10"
                height="10"
                fill="#fff"
                xlink:href="#el-icon-ssq-guanbi"
            >
            </use>
        </g>
    </g>
</template>

<script>
import { DEFAULT_RS_HEIGHT } from 'src/common/utils/decorate.js';

export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['zoom', 'ridingseal', 'isActive', 'color', 'pageHeight', 'isEdit'],
    data() {
        return {
            moveRealtiveY: 0, // 记录上一次移动的位置
            isMoveing: false,
            startY: this.ridingseal.y,
            top: 0, // 标记当前操作元素的y
        };
    },
    computed: {
        componentWidth() {
            const CLOSE_ICON_WIDTH = 10;
            return this.isEdit ? DEFAULT_RS_HEIGHT - CLOSE_ICON_WIDTH : DEFAULT_RS_HEIGHT;
        },
    },
    methods: {
        handleDelete() {
            this.$emit('delete');
        },
        handleRidingSealMouseStart(e) {
            if (!this.isEdit) {
                return;
            }
            this.isMoveing = true;
            this.moveRealtiveY = e.y;
            this.top = this.ridingseal.y;
            this.startY = this.ridingseal.y;
            this.$emit('update-operate-page-info');
            document.addEventListener('mousemove', this.handleRidingSealMouseMove);
            document.addEventListener('mouseup', this.handleRidingSealMouseEnd);
        },
        handleRidingSealMouseMove(e) {
            if (!this.isMoveing) {
                return false;
            }
            this.positionUpdate(e);
        },
        handleRidingSealMouseEnd(e) {
            if (!this.isMoveing) {
                return;
            }

            this.isMoveing = false;
            document.removeEventListener('mousemove', this.handleRidingSealMouseMove);
            document.removeEventListener('mouseup', this.handleRidingSealMouseEnd);
            this.positionUpdate(e, true);
        },
        openContextmenu(e) {
            this.$emit('openContextmenu', e);
        },
        positionUpdate(e, isEnd = false) {
            let posY = this.top + (e.y - this.moveRealtiveY) / this.zoom;
            this.moveRealtiveY = e.y; // 更新相对位置

            const [mixHeight, maxHeight] = [0, this.pageHeight];
            // 下边界
            if (posY + DEFAULT_RS_HEIGHT >= maxHeight) {
                posY = maxHeight - DEFAULT_RS_HEIGHT;
            }
            // 上边界
            if (posY <= mixHeight) {
                posY = mixHeight;
            }
            this.top = posY;
            // 移动结束 更新
            if (isEnd) {
                this.$emit('update-server', {
                    y: posY,
                    startY: this.startY,
                });
            }
        },
    },
};
</script>

<style lang="scss">
    .seal-item {
        cursor: move;
        z-index: 999;
        &.active-seal {
            z-index: 9999;
        }
        &.cur-default {
            cursor: default;
        }
        .del-btn {
            cursor: pointer;
        }
    }

</style>
