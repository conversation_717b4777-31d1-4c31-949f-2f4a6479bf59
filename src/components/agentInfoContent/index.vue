<template>
    <div class="risk-judge-popup__agent">
        <div class="risk-judge-popup__agent-body">
            <div class="risk-judge-popup__agent-body-box">
                <div class="risk-judge-popup__agent-body-role" v-if="!docAnalysisId && !contractId">
                    <div
                        class="risk-judge-popup__agent-body-role-icon robot"
                    >
                        <div class="hubble"></div>
                    </div>
                    <div
                        class="risk-judge-popup__agent-body-role-content"
                    >
                        <div
                            class="content-container ai"
                        >
                            <p>{{ uploadText }}</p>
                        </div>
                    </div>
                </div>
                <div class="risk-judge-popup__agent-body-role" v-if="docAnalysisId && !rightRiskReady">
                    <div
                        class="risk-judge-popup__agent-body-role-icon robot"
                    >
                        <div class="hubble"></div>
                    </div>
                    <div
                        class="risk-judge-popup__agent-body-role-content"
                    >
                        <div
                            class="content-container ai"
                        >
                            <p>{{ toolTips }}</p>
                        </div>
                    </div>
                </div>
                <div class="risk-judge-popup__agent-body-role" v-if="riskList.length > 1">
                    <div
                        class="risk-judge-popup__agent-body-role-icon robot"
                    >
                        <div class="hubble"></div>
                    </div>
                    <div
                        class="risk-judge-popup__agent-body-role-content"
                    >
                        <div
                            class="content-container ai"
                        >
                            <VueMarkdown
                                class="ai"
                                :source="chooseTips"
                            ></VueMarkdown>
                        </div>
                        <div class="user-operate">
                            <div
                                class="user-operate-checkbox"
                                :class="documentId ?
                                    documentId === file.documentId ?
                                        'checked' :
                                        'unchecked' :
                                    ''"
                                v-for="file in riskList"
                                :key="file.documentId"
                                @click="initOrRecord(file)"
                            >
                                <div
                                    class="symbol"
                                    v-if="documentId === file.documentId"
                                >
                                    <i class="el-icon-ssq-selected"></i>
                                </div>
                                {{ file.fileName }}
                            </div>
                        </div>
                    </div>
                </div>
                <template v-for="(msg, num) in historyAnalysisResults">
                    <div class="risk-judge-popup__agent-body-role" :key="msg.messageId" v-if="msg.textContent && msg.textContent !== ''">
                        <div
                            class="risk-judge-popup__agent-body-role-icon"
                            :class="msg.roleType === 'AI' ? 'robot' :'human'"
                        >
                            <div class="hubble" v-if="msg.roleType === 'AI' && msg.textContent !== ''"></div>
                            <i class="el-icon-ssq-yonghu" v-if="msg.roleType === 'USER' && msg.textContent !== ''" />
                        </div>
                        <div
                            class="risk-judge-popup__agent-body-role-content"
                        >
                            <div
                                class="content-container"
                                :class="msg.roleType === 'AI' ? 'ai' :'user'"
                            >
                                <DeepThink :content="msg.reasoning" v-if="msg.roleType === 'AI' && msg.reasoning"></DeepThink>
                                <VueMarkdown
                                    class="risk-judge-popup-line"
                                    :source="getContent(msg.textContent)"
                                ></VueMarkdown>
                                <template v-if="msg.roleType === 'AI' && msg.riskWpsBuffer && msg.riskWpsBuffer.originalContent">
                                    <ExpandableBox :open="false" :buffer="msg.riskWpsBuffer"></ExpandableBox>
                                </template>
                                <el-button
                                    v-if="num > 2 && msg.contentPositions && msg.contentPositions.length && !isWpsMode"
                                    class="location"
                                    type="primary"
                                    @click="locateShadow(msg.contentPositions)"
                                >
                                    {{ $t('agent.content') }}
                                    <span class="location-index">{{ contentIndex(msg.contentPositions) + 1 }}</span>
                                </el-button>
                            </div>
                            <div class="user-operate">
                                <div
                                    class="user-operate-checkbox"
                                    :class="getCheckOrNot({btn, num}) ?
                                        'checked' :
                                        num === historyAnalysisResults.length - 1 ?
                                            '' :
                                            'unchecked'"
                                    v-for="(btn, index) in msg.selectedOptions"
                                    :key="index"
                                    @click="submitChoose(btn, num + 1, true)"
                                >
                                    <div
                                        class="symbol"
                                        v-if="getCheckOrNot({btn, num}) && btn !== $t('agent.satisfy') && btn !== $t('agent.dissatisfy')"
                                    >
                                        <i class="el-icon-ssq-selected"></i>
                                    </div>
                                    <i
                                        class="satisfy-symbol el-icon-ssq-xiaolian"
                                        v-if="btn === $t('agent.satisfy')"
                                    ></i>
                                    <i
                                        class="satisfy-symbol el-icon-ssq-kulian"
                                        v-if="btn === $t('agent.dissatisfy')"
                                    ></i>
                                    {{ btn }}
                                </div>
                                <div
                                    class="user-operate-checkbox"
                                    v-if="
                                        !needAnalysis && num === historyAnalysisResults.length - 1 && msg.selectedOptions &&
                                            !['agreementRiskJudgement', 'jumpToWeChatMiniProgram', 'needPay', 'riskJudgementPlan', 'customRuleUsing'].includes(operateBtns.userInputHelperType) ||
                                            operateBtns.userInputHelperType === 'agreementTerminologyExtract'
                                    "
                                    @click="showInput"
                                    :class="{'checked': isElse}"
                                >
                                    {{ $t('agent.others') }}
                                </div>
                            </div>
                            <template v-if="!needAnalysis && num === historyAnalysisResults.length - 1">
                                <div v-if="isElse">
                                    <el-input class="dialog-input" :maxlength="elseLength" v-model="message" :placeholder="operateBtns.tipsForOtherOptions">
                                        <el-button slot="append" size="small" type="primary" @click="submitMessage(message)">{{ $t('agent.submit') }}</el-button>
                                    </el-input>
                                </div>
                                <div v-if="isCustom" class="user-operate-custom-input">
                                    <el-input class="dialog-input"
                                        resize="none"
                                        :autosize="{ minRows: 4, maxRows: 10}"
                                        type="textarea"
                                        v-model="message"
                                        :placeholder="$t('agent.custom')"
                                    ></el-input>
                                    <el-button size="small" type="primary" :disabled="!message" @click="submitMessage(message)">{{ $t('agent.submit') }}</el-button>
                                </div>
                            </template>
                        </div>
                    </div>
                </template>
                <div class="risk-judge-popup__agent-body-role" v-if="needAnalysis">
                    <div class="risk-judge-popup__agent-body-role-icon robot">
                        <div class="hubble"></div>
                    </div>
                    <div class="risk-judge-popup__agent-body-role-content">
                        <div
                            class="content-container ai"
                        >
                            <DeepThink :isThinking="isThinking" isOpen :content="currentReasoning" v-if="currentReasoning"></DeepThink>
                            <div class="risk-judge-popup-result">
                                <el-button
                                    v-if="needExport &&
                                        ( agentType === 'RISK_JUDGEMENT_REASONING'||
                                            agreementAnalysisType === 'RISK_JUDGEMENT_REASONING'
                                        )
                                    "
                                    type="primary"
                                    @click="exportPDF"
                                >{{ $t('agent.exportPDF') }}</el-button>
                                <VueMarkdown
                                    class="risk-judge-popup-line"
                                    :source="responseContent ? responseContent : analysisMsg"
                                    :config="markdownConfig"
                                ></VueMarkdown>
                            </div>
                            <template v-if="riskWpsBuffer.originalContent">
                                <ExpandableBox :buffer="riskWpsBuffer" :currentTab="inProposal ? $t('agent.revision') : ''"></ExpandableBox>
                            </template>
                            <el-button
                                v-if="currentPosition.length"
                                class="location"
                                type="primary"
                                @click="locateShadow(currentPosition)"
                            >
                                {{ $t('agent.content') }}
                                <span class="location-index">{{ contentIndex(currentPosition) + 1 }}</span>
                            </el-button>
                        </div>
                        <p v-show="positionPending">{{ positionMsg }}</p>
                        <div class="user-operate">
                            <span v-show="inUserInput || inWpsChange">{{ btnMsg }}</span>
                            <div
                                class="user-operate-checkbox"
                                :class="{'checked': btn === '去小程序查看' || btn === '支付解锁完整的风险判断报告'}"
                                v-for="(btn, index) in operateBtns.choosen"
                                v-show="btn !== '去小程序查看'"
                                :key="index"
                                @click="submitChoose(btn, null, true)"
                            >
                                <i
                                    class="satisfy-symbol el-icon-ssq-xiaolian"
                                    v-if="btn === $t('agent.satisfy')"
                                ></i>
                                <i
                                    class="satisfy-symbol el-icon-ssq-kulian"
                                    v-if="btn === $t('agent.dissatisfy')"
                                ></i>
                                {{ btn }}
                            </div>
                            <div
                                class="user-operate-checkbox"
                                v-if="
                                    operateBtns.choosen &&
                                        operateBtns.choosen.length &&
                                        !['agreementRiskJudgement', 'jumpToWeChatMiniProgram', 'needPay', 'riskJudgementPlan', 'customRuleUsing'].includes(operateBtns.userInputHelperType) ||
                                        operateBtns.userInputHelperType === 'agreementTerminologyExtract'
                                "
                                @click="showInput"
                                :class="{'checked': isElse}"
                            >
                                {{ $t('agent.others') }}
                            </div>
                        </div>
                        <div v-if="isElse">
                            <el-input class="dialog-input" :maxlength="elseLength" v-model="message" :placeholder="operateBtns.tipsForOtherOptions">
                                <el-button slot="append" size="small" type="primary" @click="submitMessage(message)">{{ $t('agent.submit') }}</el-button>
                            </el-input>
                        </div>
                        <div v-if="isCustom" class="user-operate-custom-input">
                            <el-input class="dialog-input" type="textarea" v-model="message" :placeholder="operateBtns.tipsForOtherOptions"></el-input>
                            <el-button size="small" type="primary" @click="submitMessage(message)">{{ $t('agent.submit') }}</el-button>
                        </div>
                    </div>
                </div>
                <div
                    v-if="convertToRisk"
                    class="convert-to-risk"
                    @click="changeAgentType"
                >
                    <p>{{ convertToRisk }}</p>
                    <span>{{ $t('agent.useLawyer') }}</span>
                </div>
            </div>
        </div>
        <div class="content-mask" v-if="preventClick"></div>
        <div class="risk-judge-popup__agent-footer">
            <div class="footer-operate">
                <el-checkbox v-model="autoReply" shape="square">
                    {{ agentType === 'EXTRACT' ? $t('agent.autoExtract') : $t('agent.autoRisk') }}
                </el-checkbox>
                <el-button
                    v-if="inSignPage"
                    :disabled="historyAnalysisResults.length === 0"
                    @click="rejudge"
                >
                    {{ $t('agent.reJudge') }}
                </el-button>
            </div>
            <p>{{ $t('agent.aiGenerated') }}</p>
        </div>
    </div>
</template>

<script>
import VueMarkdown from 'vue-markdown';
import { mapState } from 'vuex';
import { hubbleWps } from 'src/mixins/hubbleWps.js';
import { fetchAppletUrl } from 'src/api/sign';
import ExpandableBox from './expandableBox';
import DeepThink from './deepThink';
import {
    confirmOperateType,
    hubbleJudgementDetail,
    initialAnalysis,
    viewPosition,
    covertToRiskJudgement,
} from 'src/api/judgeRisk.js';
export default {
    components: {
        VueMarkdown,
        ExpandableBox,
        DeepThink,
    },
    mixins: [hubbleWps],
    props: {
        docAnalysisId: {
            type: String,
            default: '',
        },
        rightRiskReady: {
            type: Boolean,
            default: false,
        },
        hasHistoryEvent: {
            type: Boolean,
            default: false,
        },
        contractId: {
            type: String,
            default: '',
        },
        receiverId: {
            type: String,
            default: '',
        },
        riskList: {
            type: Array,
            default: () => [],
        },
        agentType: {
            type: String,
            default: '',
        },
        productType: {
            type: Number,
            default: 15, // 15比对，16翻译
        },
        productConfig: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            isInit: false,
            agreementAnalysisType: '',
            historyAnalysisResults: [],
            analysisId: '',
            documentId: '',
            message: '',
            responseContent: '',
            currentReasoning: '',
            operateBtns: {},
            autoReply: false,
            needAnalysis: false,
            userName: '',
            inResponse: false,
            isThinking: false,
            inAnalysis: false,
            inWpsChange: false,
            inOriginal: false,
            inProposal: false,
            wpsChangeMsg: '',
            riskWpsBuffer: { originalContent: '', proposalContent: '' },
            wpsReplaced: false,
            inUserInput: false,
            tagBuffer: '',
            streamBuffer: '',
            reader: null,
            isElse: false,
            isCustom: false,
            markdownConfig: {
                html: true, // 允许在 Markdown 中使用 HTML 标签
                breaks: true, // 转换换行符为 <br> 标签
            },
            analysisInterval: null,
            analysisMsg: '',
            positionMsg: '',
            btnMsg: '',
            preventClick: false,
            positions: [],
            messageId: '',
            currentPosition: [],
            positionPending: false,
            timer: null,
            convertToRisk: '',
            needExport: false,
        };
    },
    computed: {
        ...mapState({
            commonHeaderInfo: state => state.commonHeaderInfo,
            documentType: state => state.hubble.documentType,
            allowEdit: state => state.hubble.allowEdit,
        }),
        getCheckOrNot() {
            return ({ btn, num }) => {
                const type = {
                    [this.$t('agent.extractTitle')]: 'EXTRACT',
                    [this.$t('agent.riskTitle')]: 'RISK_JUDGEMENT',
                    [this.$t('agent.deepInference')]: 'RISK_JUDGEMENT_REASONING',
                }[btn];
                if (this.historyAnalysisResults[num + 1]) {
                    return this.historyAnalysisResults[num + 1].textContent === btn;
                } else if (type) {
                    return this.agreementAnalysisType === type;
                } else {
                    return false;
                }
            };
        },
        chooseTips() {
            if (this.agentType === 'EXTRACT') {
                return this.$t('agent.chooseExtract');
            } else {
                return this.$t('agent.chooseRisk');
            }
        },
        isWpsMode() {
            return this.documentType === 'wps';
        },
        inSignPage() {
            return this.$route.path === '/sign/signing' &&
                (this.$route.query.type && this.$route.query.type === 'sign' ||
                    !this.$route.query.type);
        },
        uploadText() {
            if (this.$route.path === '/hubble-apply/interpretation') {
                return this.$t('contractCompare.interpretText');
            } else {
                return this.$t('contractCompare.uploadText');
            }
        },
        toolTips() {
            if (this.$route.path === '/hubble-apply/interpretation') {
                return this.$t('contractCompare.interpretTips');
            } else {
                return this.$t('contractCompare.startTips');
            }
        },
        elseLength() {
            return this.agentType === 'CONTRACT_INTERPRETATION' ? 10 : 100;
        },
    },
    watch: {
        needAnalysis(val) {
            const dotsArray = ['.', '..', '...'];
            let index = 0;
            if (val) {
                this.analysisInterval = setInterval(() => {
                    this.analysisMsg = `${this.$t('agent.analyzing')}${dotsArray[index]}`;
                    this.btnMsg = `${this.inWpsChange ? this.$t('agent.advice') : this.$t('agent.options')}${dotsArray[index]}`;
                    index = index === 2 ? 0 : index + 1;
                }, 500);
            } else {
                this.analysisMsg = '';
                clearInterval(this.analysisInterval);
            }
        },
        positionPending(val) {
            const dotsArray = ['.', '..', '...'];
            let index = 0;
            if (val) {
                this.analysisInterval = setInterval(() => {
                    this.positionMsg = `${this.$t('agent.locate')}${dotsArray[index]}`;
                    index = index === 2 ? 0 : index + 1;
                }, 500);
            } else {
                this.positionMsg = '';
                clearInterval(this.analysisInterval);
            }
        },
    },
    methods: {
        handleHistoryData(analysisMessages) {
            return analysisMessages.map(msg => {
                if (msg.roleType === 'AI') {
                    msg.riskWpsBuffer = this.handleWpsData(msg.textContent);
                    msg.reasoning && (msg.reasoning = this.getReasoning(msg.reasoning));
                }
                return msg;
            });
        },
        getReasoning(reasoning) {
            const regex = /<hubbleThink>([\s\S]*?)<\/hubbleThink>/;
            const msg = reasoning.match(regex);
            if (msg) {
                return msg[1].replace(/^\n|\n$/g, '').trim().replace(/\n\s*/g, '\n');
            } else {
                return reasoning;
            }
        },
        formatString(str) {
            return str
                .replace(/```/g, '')
                .replace(/\\n/g, '\n')
                .trim()
                .split('\n')
                .map(line => line.trim())
                .join('\n');
        },
        initOrRecord(document) {
            if (this.analysisId) {
                return;
            }
            if (this.documentId) {
                return;
            }
            this.documentId = document.documentId;
            this.historyAnalysisResults = [];
            this.historyAnalysisResults.push({
                roleType: 'USER',
                textContent: document.fileName,
            });
            if (document.analysisInfos && document.analysisInfos.length) {
                const analysis = document.analysisInfos.find(item =>
                    item.analysisType === this.agentType,
                );
                if (analysis) {
                    this.analysisId = analysis.analysisId;
                    this.getAnalysisRecord(this.analysisId);
                } else {
                    this.analysisId = '';
                    this.init();
                }
            } else {
                this.analysisId = '';
                this.init();
            }
        },
        init() {
            initialAnalysis(this.contractId, this.receiverId, {
                documentId: this.documentId,
                paymentPlanType: null,
                approverAnnotationType: this.agentType,
            }).then(async res => {
                this.analysisId = res.data;
                this.needAnalysis = true;
                await this.sendMessage();
            });
        },
        initAnalyze() {
            this.isInit = true;
            this.historyAnalysisResults = [{
                messageId: '',
                textContent: this.$t('agent.selectFunc'),
                selectedOptions: [
                    this.$t('agent.riskTitle'),
                    this.$t('agent.deepInference'),
                    this.$t('agent.extractTitle'),
                ],
                reasoning: '',
                contentPositions: [],
                roleType: 'AI',
                riskWpsBuffer: { originalContent: '', proposalContent: '' },
            }];
        },
        getAnalysisRecord(analysisId) {
            this.needAnalysis = false;
            if (!this.analysisId) {
                this.analysisId = analysisId;
            }
            hubbleJudgementDetail(analysisId).then(res => {
                if (res.data) {
                    this.$store.state.hubble.allowEdit = res.data.allowEdit;
                    this.$store.state.hubble.multiVersionFileId = res.data.editModeExtensionInfo?.multiVersionFileId;
                    this.agreementAnalysisType = res.data.agreementAnalysisType;
                    if (this.$route.path === '/hubble-apply/interpretation' && !res.data.agreementAnalysisType) {
                        this.$emit('changeAgentStatus');
                        return;
                    }
                    if (!res.data.agreementAnalysisType) {
                        return this.initAnalyze();
                    }
                    this.$store.state.hubble.documentType = this.agreementAnalysisType === 'RISK_JUDGEMENT' && this.allowEdit ? 'wps' : 'doc';
                    const analysisMessages = this.handleHistoryData(res.data.analysisMessages);
                    const lastOne = analysisMessages[analysisMessages.length - 1];
                    lastOne && (this.riskWpsBuffer = (lastOne.riskWpsBuffer || { originalContent: '', proposalContent: '' }));
                    this.handleUserInputHelperType(lastOne);
                    this.historyAnalysisResults = this.historyAnalysisResults.concat(res.data.analysisMessages);
                    if (this.historyAnalysisResults.length > 3) {
                        const positionMessage = this.historyAnalysisResults.slice(3);
                        positionMessage.forEach(result => {
                            if (result.contentPositions && result.contentPositions.length && !this.positions.length) {
                                this.positions.push([result.contentPositions]);
                            } else if (result.contentPositions && result.contentPositions.length) {
                                const index = this.positions.findIndex(subArr => this.areArraysEqual(subArr[0], result.contentPositions));
                                if (index === -1) {
                                    this.positions.push([result.contentPositions]);
                                }
                            }
                        });
                    }
                    if (res.data.retry?.ifRetry) {
                        this.needAnalysis = true;
                        this.message = res.data.retry.retryUserMessage || '';
                        this.sendMessage();
                        return;
                    }
                }
            }).finally(() => {
                this.scrollToEnd();
            });
        },
        handleUserInputHelperType(lastOne) {
            if (lastOne && lastOne.roleType === 'AI' && lastOne.textContent) {
                const parser = new DOMParser();
                const doc = parser.parseFromString(lastOne.textContent, 'text/html');
                const userInputHelperText = doc.querySelector('userInputHelper')?.textContent || '{}';
                try {
                    const userInputHelper = JSON.parse(userInputHelperText);
                    this.setOperateBtns(userInputHelper);
                    console.log('operateBtns', this.operateBtns);
                    console.log('showElse', this.showElse);
                } catch (error) {
                    this.operateBtns.userInputHelperType = '';
                }
            }
        },
        async handleWpsSend() {
            if (this.message === this.$t('agent.satisfy')) {
                await this.handleWpsChangeMsg();
            } else if (this.message === this.$t('agent.dissatisfy')) {
                this.rejectAll();
            }
            return Promise.resolve();
        },
        handleStreamResponse(totalStream) {
            let totalResponse = totalStream;
            const parser = new DOMParser();
            let doc = parser.parseFromString(totalResponse, 'text/html');
            const hasClosingTag = (tag) => totalResponse.includes(`</${tag}>`);
            const hubbleThink = doc.querySelector('hubbleThink');
            if (hubbleThink) {
                this.isThinking = true;
                this.currentReasoning = this.formatString(hubbleThink.textContent);
                if (hasClosingTag('hubbleThink')) {
                    this.isThinking = false;
                    totalResponse = totalResponse.replace(/<hubbleThink>[\s\S]*?<\/hubbleThink>/, '');
                    doc = parser.parseFromString(totalResponse, 'text/html');
                } else {
                    return;
                }
            }

            const messageId = doc.querySelector('messageId');
            if (messageId) {
                this.messageId = this.formatString(messageId.textContent);
            }

            const response = doc.querySelector('response');
            response && (this.inResponse = true);

            const needPay = doc.querySelector('needPay');
            if (needPay && hasClosingTag('needPay')) {
                setTimeout(() => {
                    this.$emit('showCharge');
                }, 1000);
                return;
            }

            const analysis = doc.querySelector('analysis');
            if (analysis) {
                this.inAnalysis = true;
                this.responseContent = this.formatString(analysis.textContent);
                if (hasClosingTag('analysis')) {
                    this.inAnalysis = false;
                }
            }
            const exportContet = doc.querySelector('exportReport');
            if (exportContet) {
                console.log(this.agentType);
                this.needExport = true;
                this.responseContent = this.formatString(exportContet.textContent);
            }

            const revisionContents = doc.querySelector('revisionContents');
            if (revisionContents) {
                this.inWpsChange = true;
                const original = doc.querySelector('original');
                if (original) {
                    this.inOriginal = true;
                    this.riskWpsBuffer.originalContent = this.formatString(original.textContent);
                    if (hasClosingTag('original')) {
                        this.inOriginal = false;
                    }
                }
                const proposal = doc.querySelector('proposal');
                if (proposal) {
                    this.inProposal = true;
                    this.riskWpsBuffer.proposalContent = this.formatString(proposal.textContent);
                    if (hasClosingTag('proposal')) {
                        this.inProposal = false;
                    }
                }
                if (hasClosingTag('revisionContents')) {
                    if (this.isWpsMode && !this.wpsReplaced) {
                        this.replaceText();
                        this.wpsReplaced = true;
                    }
                    this.inWpsChange = false;
                }
            }
            const convertToRiskJudgement = doc.querySelector('convertToRiskJudgement');
            if (convertToRiskJudgement) {
                this.convertToRisk = this.formatString(convertToRiskJudgement.textContent);
            }
            const btnTag = doc.querySelector('userInputHelper');
            if (btnTag) {
                this.inUserInput = true;
                if (hasClosingTag('userInputHelper')) {
                    this.inUserInput = false;
                    const userInputHelper = JSON.parse(this.formatString(btnTag.textContent));
                    this.setOperateBtns(userInputHelper);
                }
            }
        },
        setOperateBtns(userInputHelper) {
            this.operateBtns = {
                ...userInputHelper,
                choosen: userInputHelper.contractTypes ||
                    userInputHelper.stakeholders ||
                    userInputHelper.terminologyProvidedToUsers ||
                    userInputHelper.askUserSatisfied ||
                    userInputHelper.agreementProvidedToUsers ||
                    userInputHelper.optionsProvidedToUsers,
            };
        },
        async sendMessage() {
            if (this.isWpsMode) {
                await this.handleWpsSend();
            }
            const data = {
                userMessage: `${this.message}${this.wpsChangeMsg}`,
                autoReply: this.autoReply,
            };
            this.responseContent = '';
            this.currentReasoning = '';
            this.operateBtns = {};
            this.preventClick = true;
            this.riskWpsBuffer = { originalContent: '', proposalContent: '' };
            this.wpsReplaced = false;
            await fetch(`/web/hubble/agreement-analysis/event-stream/${this.analysisId}/submit-message`, {
                method: 'POST',
                headers: {
                    'authorization': 'bearer ' + this.$cookie.get('access_token'),
                    'content-type': 'application/json',
                },
                body: JSON.stringify(data),
            }).then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP 错误，状态码: ${response.status}`);
                }
                this.reader = response.body.getReader();
                const that = this;
                let totalResponse = '';
                return new ReadableStream({
                    start(controller) {
                        function push() {
                            that.reader.read().then(({ done, value }) => {
                                if (done) {
                                    controller.close();
                                    const historyLength = JSON.parse(JSON.stringify(that.historyAnalysisResults)).length;
                                    that.preventClick = false;
                                    if (that.messageId && that.operateBtns.userInputHelperType === 'agreementRiskJudgement') {
                                        that.getContentPosition();
                                    }
                                    if (that.autoReply && Object.keys(that.operateBtns).length) {
                                        if (that.operateBtns.userInputHelperType === 'stakeholdersJudgement') {
                                            that.autoReply = false;
                                        } else if (that.operateBtns.userInputHelperType === 'scannedCopyRecognized') {
                                            that.submitChoose('');
                                        } else if (that.operateBtns.userInputHelperType === 'needPay') {
                                            return;
                                        } else if (!that.operateBtns.choosen.length && that.operateBtns.userInputHelperType === 'contractInterpretation') {
                                            that.submitChoose('');
                                        } else {
                                            const msg = that.operateBtns.choosen[0];
                                            that.submitChoose(msg);
                                        }
                                    } else if (Object.keys(that.operateBtns).length &&
                                        that.operateBtns.userInputHelperType === 'scannedCopyRecognized'
                                    ) {
                                        that.submitChoose('');
                                    } else if (Object.keys(that.operateBtns).length &&
                                        that.operateBtns.userInputHelperType === 'needPay'
                                    ) {
                                        return;
                                    } else if (that.operateBtns.userInputHelperType === 'stakeholdersJudgement') {
                                        that.autoReply = false;
                                    } else if (!Object.keys(that.operateBtns).length && historyLength <= 1) {
                                        that.submitChoose('');
                                    }
                                    return;
                                }
                                const text = new TextDecoder().decode(value);
                                const lines = text.split('\n');
                                lines.forEach(line => {
                                    if (line.trim() !== '') {
                                        let result = line;
                                        if (line.indexOf('data:') !== -1) {
                                            result = line.slice(5);
                                        }
                                        totalResponse += result;
                                    }
                                });
                                that.handleStreamResponse(totalResponse);
                                that.scrollToEnd();
                                push();
                            });
                        }
                        push();
                    },
                });
            }).catch(err => {
                console.log('Fetch Error: ', err);
            }).finally(() => {
                this.message = '';
            });
        },
        openWxMiniProgram(from) {
            const currentAccount = this.commonHeaderInfo?.platformUser?.account;
            fetchAppletUrl(101, 'views/riskJudge/index',
                           `account=${currentAccount}&isFromH5Home=1&accessFrom=${from}`, 'release')
                .then((res) => {
                    window.location.href = res.data?.url;
                });
        },
        scrollToEnd() {
            this.$nextTick(() => {
                const agentBody = document.querySelector('.risk-judge-popup__agent-body-box');
                if (agentBody) {
                    agentBody.scrollIntoView({
                        behavior: 'instant',
                        block: 'end',
                    });
                }
            });
        },
        submitMessage(val) {
            if (val.trim() !== '') {
                this.submitChoose(val);
            } else {
                this.$MessageToast.warning(this.$t('agent.inputTips'));
            }
        },
        chooseAnalysisType(val) {
            const type = {
                [this.$t('agent.extractTitle')]: 'EXTRACT',
                [this.$t('agent.riskTitle')]: 'RISK_JUDGEMENT',
                [this.$t('agent.deepInference')]: 'RISK_JUDGEMENT_REASONING',
                [this.$t('judgeRisk.aiInterpret')]: 'CONTRACT_INTERPRETATION',
            }[val];
            confirmOperateType(this.analysisId, type).then(() => {
                this.agreementAnalysisType = type;
                this.$store.state.hubble.documentType = this.agreementAnalysisType === 'RISK_JUDGEMENT' && this.allowEdit ? 'wps' : 'doc';
                this.needAnalysis = true;
                this.isInit = false;
                if (this.hasHistoryEvent) {
                    this.$emit('updateHistory');
                }
                this.sendMessage();
            }).catch((err) => {
                this.$MessageToast.error(err.response?.data?.message);
                setTimeout(() => {
                    this.$emit('showCharge');
                }, 1000);
            });
        },
        submitChoose(val, index, fromClick) {
            if (this.isInit) {
                return this.chooseAnalysisType(val);
            }
            if (
                [
                    this.$t('agent.riskTitle'),
                    this.$t('agent.deepInference'),
                    this.$t('agent.extractTitle'),
                    this.$t('judgeRisk.aiInterpret'),
                ].includes(val)
            ) {
                return;
            }
            if (val === this.$t('filter.yes')) {
                this.isCustom = true;
                return;
            }
            if (val === '去小程序查看') {
                !fromClick && this.scrollToEnd();
                fromClick && this.openWxMiniProgram('analysisBottom');
                return;
            }
            if (!this.autoReply && this.preventClick) {
                return;
            }
            this.isCustom = false;
            this.isElse = false;
            this.timer = setInterval(() => {
                if (!this.positionPending) {
                    clearInterval(this.timer);
                    this.timer = null;
                    this.isElse = false;
                    if (index && index === this.historyAnalysisResults.length || !index) {
                        this.message = val;
                        if (this.responseContent) {
                            this.historyAnalysisResults.push({
                                roleType: 'AI',
                                textContent: this.responseContent,
                                selectedOptions: this.operateBtns.choosen,
                                contentPositions: this.currentPosition,
                                riskWpsBuffer: this.riskWpsBuffer,
                                reasoning: this.currentReasoning,
                            });
                        }
                        if (val) {
                            this.historyAnalysisResults.push({
                                roleType: 'USER',
                                textContent: val,
                            });
                        }
                        this.currentPosition = [];
                        this.messageId = '';
                        this.$nextTick(() => {
                            this.sendMessage();
                            const agentBody = document.querySelector('.risk-judge-popup__agent-body-box');
                            if (agentBody) {
                                agentBody.scrollIntoView({
                                    behavior: 'instant',
                                    block: 'end',
                                });
                            }
                        });
                        this.needAnalysis = true;
                    } else {
                        return;
                    }
                }
            }, 200);
        },
        getContent(content) {
            const regex = /<analysis>([\s\S]*?)<\/analysis>/;
            const msg = content.match(regex);
            if (msg) {
                return msg[1].replace(/^\n|\n$/g, '').trim().replace(/\n\s*/g, '\n').replace(/```/g, '\n');
            } else {
                return (content);
            }
        },
        getContentPosition() {
            this.positionPending = true;
            viewPosition(this.analysisId, this.messageId).then(res => {
                this.currentPosition = res.data;
                if (res.data && res.data.length && !this.positions.length) {
                    this.positions.push([res.data]);
                } else if (res.data && res.data.length) {
                    const index = this.positions.findIndex(subArr => this.areArraysEqual(subArr[0], res.data));
                    if (index === -1) {
                        this.positions.push([res.data]);
                    }
                }
                this.$nextTick(() => {
                    this.positionPending = false;
                });
            }).catch(() => {
                this.positionPending = false;
            });
        },
        showInput() {
            this.isElse = true;
            this.scrollToEnd();
        },
        getPayResult() {
            this.historyAnalysisResults = [];
            if (this.riskList.length > 1) {
                const file = this.riskList.find(file => file.documentId === this.documentId);
                this.historyAnalysisResults.push({
                    roleType: 'USER',
                    textContent: file.fileName,
                });
            }
            this.getAnalysisRecord(this.analysisId);
        },
        areArraysEqual(a, b) {
            if (a.length !== b.length) {
                return false;
            }
            return a.every((objA, index) => {
                const objB = b[index];
                const keysA = Object.keys(objA);
                const keysB = Object.keys(objB);
                return keysA.length === keysB.length &&
                    keysA.every(key => objA[key] === objB[key]);
            });
        },
        contentIndex(target) {
            const index = this.positions.findIndex(subArr => this.areArraysEqual(subArr[0], target));
            return index;
        },
        locateShadow(positions) {
            this.$emit('renderFragment', [[positions]]);
            this.$nextTick(() => {
                this.$emit('locateShadow', 0);
            });
        },
        reset() {
            this.historyAnalysisResults = [];
            this.operateBtns = {};
            this.needAnalysis = false;
            this.preventClick = false;
            this.positions = [];
            this.currentPosition = [];
            this.analysisId = '';
            this.messageId = '';
            this.agreementAnalysisType = '';
            this.isCustom = false;
            this.inResponse = false;
            this.isThinking = false;
            this.inAnalysis = false;
            this.inUserInput = false;
            this.needExport = false;
            this.responseContent = '';
            this.$store.state.hubble.documentType = 'doc';
            if (this.reader) {
                this.reader.cancel();
                this.reader = null;
            }
        },
        rejudge() {
            this.$confirm(this.$t('agent.tipsContent'), this.$t('uploadFile.tip'), {
                confirmButtonText: this.$t('agent.confirm'),
                showCancelButton: true,
            }).then(() => {
                let fileInfo = {};
                if (this.historyAnalysisResults.length > 0 && this.riskList.length > 1) {
                    fileInfo = this.historyAnalysisResults[0];
                }
                this.reset();
                if (this.riskList.length > 1) {
                    this.historyAnalysisResults.push(fileInfo);
                }
                this.init();
            }).catch(() => {
                return;
            });
        },
        changeAgentType() {
            if (this.$route.path === '/hubble-apply/interpretation') {
                covertToRiskJudgement(this.analysisId).then(res => {
                    if (res.data) {
                        this.$router.push(`/hubble-apply/risk-judgement?analysisId=${res.data}`);
                    }
                });
            } else {
                this.$emit('convertToRisk');
            }
        },
        async exportPDF() {
            this.$MessageToast.success(this.$t('agent.exporting'));
            this.$http.post(
                `/web/hubble/agreement-analysis/${this.analysisId}/download-deep-seek-report`,
                {},
                { responseType: 'blob' },
            ).then(res => {
                const regex = new RegExp('filename=(.*)');
                const match = res.headers['content-disposition'].split(';')[1].match(regex);
                const name = match[1] ? match[1].replace(/"/g, '') : this.$t('agent.defaultExportName');
                const u = URL.createObjectURL(res.data);
                const a = document.createElement('a');
                a.download = name;
                a.href = u;
                a.style.display = 'none';
                document.body.appendChild(a);
                a.click();
                a.remove();
            });
        },
    },
    mounted() {
        if (this.docAnalysisId) {
            this.analysisId = this.docAnalysisId;
            return;
        }
        let analysis = null;
        if (this.agentType === 'EXTRACT') {
            this.autoReply = false;
        }
        if (this.riskList.length === 1) {
            analysis = this.riskList[0].analysisInfos.find(item =>
                item.analysisType === this.agentType && item.analysisId,
            );
            this.documentId = this.riskList[0].documentId;
        }
        if (this.riskList.length === 1 && analysis) {
            this.analysisId = analysis.analysisId;
            this.getAnalysisRecord(this.analysisId);
        } else if (this.riskList.length === 1 && !analysis) {
            this.init();
        }
    },
    created() {},
    beforeDestroy() {
        if (this.reader) {
            this.reader.cancel();
            this.reader = null;
        }
    },
};
</script>

<style lang="scss" scoped>
.risk-judge-popup {
    * {
        box-sizing: border-box;
    }
    &__agent {
        height: 100%;
        display: flex;
        flex-direction: column;
        position: relative;
        background-color: #FFF;
        &-body {
            flex-grow: 1;
            overflow-y: auto;
            overflow-x: hidden;
            padding: 10px 15px 0;
            .loading {
                text-align: center;
            }
        }
        .content-mask {
            position: absolute;
            width: 100%;
            height: calc(100% - 70px);
            background-color: rgba(255,255,255, 0);
            left: 0;
            top: 0;
        }
        &-body-role{
            display: flex;
            align-items: flex-start;
            margin-bottom: 12px;
            .content-container {
                flex-grow: 1;
                line-height: 20px;
                padding: 5px 10px;
                border-radius: 3px;
                user-select: text;
                .location {
                    font-size: 12px;
                    padding: 4px;
                    border-radius: 5px;
                    margin-left: 100%;
                    transform: translateX(-100%);
                    [dir="rtl"] & {
                        margin-left: 0;
                        transform: translateX(0);
                    }
                    span {
                        display: flex;
                        align-items: center;
                    }
                    .location-index {
                        display: inline-block;
                        margin-left: 4px;
                        border-radius: 50%;
                        font-size: 10px;
                        line-height: 13px;
                        width: 13px;
                        height: 13px;
                        transform: translateY(-1px);
                        background-color: #fff;
                        color: #098AEE;
                    }
                }
            }
            .ai {
                background-color: #E6F4FF;
            }
            .user {
                background-color: #F5F5F5;
            }
            &::v-deep ul, &::v-deep ol{
                padding-left: 18px;
                margin: 0;
                font-size: 0;
                [dir="rtl"] & {
                    padding-left: 0px;
                    padding-right: 18px;
                }
                li {
                    font-size: 14px;
                }
            }
            &::v-deep ol{
                li {
                    list-style: decimal;
                    li {
                        list-style: disc;
                        li {
                            list-style: circle;
                        }
                    }
                }
            }
            &::v-deep ul {
                li {
                    list-style: disc;
                    li {
                        list-style: circle;
                    }
                }
            }
            &::v-deep code {
                white-space: pre-line;
                word-break: break-all;
            }
            &-icon {
                width: 30px;
                height: 30px;
                font-size: 15px;
                padding: 7.5px;
                margin-right: 10px;
                border-radius: 3px;
                flex-shrink: 0;
                box-sizing: border-box;
                &.robot {
                    background-color: #098AEE;
                }
                &.human {
                    background-color: #E5E5E5;
                    color: #fff;
                }
                [dir="rtl"] & {
                    margin-right: 0px;
                    margin-left: 10px;
                }
                .hubble {
                    height: 16px;
                    width: 15px;
                    background-image: url("~src/common/assets/svg/hubble_icon.svg");
                    background-size: 100%;
                }
            }
            &-content {
                flex-grow: 1;
                .dialog-input::v-deep .el-input-group__append {
                    background-color: #098AEE;
                    color: #fff;
                }
            }
            .user-operate {
                width: 100%;
                margin-top: 10px;
                display: flex;
                flex-wrap: wrap;
                &-checkbox {
                    padding: 4px 10px;
                    border-radius: 3px;
                    border: 1px solid #666;
                    line-height: 24px;
                    margin-bottom: 4px;
                    margin-right: 4px;
                    position: relative;
                    background: #fff;
                    color: #666;
                    display: flex;
                    align-items: center;
                    cursor: pointer;
                    .satisfy-symbol {
                        display: inline-block;
                        font-size: 14px;
                        margin-right: 5px;
                        [dir="rtl"] & {
                            margin-left: 5px;
                            margin-right: 0px;
                        }
                    }
                    .symbol {
                        position: absolute;
                        right: 0;
                        bottom: 0;
                        background: #098AEE;
                        color: #fff;
                        border-radius: 5px 0px 2px 0px;
                        display: flex;
                        align-items: center;
                        font-size: 4px;
                        width: 8px;
                        height: 8px;
                        padding-left: 1px;
                        i {
                            display: block;
                            margin: auto;
                        }
                    }
                    &.checked {
                        background: #fff;
                        border: 1px solid #098AEE;
                        color: #098AEE;
                    }
                    &.unchecked {
                        background: #E5E5E5;
                        border: 1px solid #E5E5E5;
                        color: #A6A6A6;
                    }
                }
                &-custom-input::v-deep {
                    position: relative;
                    border: 1px solid #e5e5e5;
                    border-radius: 5px;
                    padding: 10px 0 35px;
                    overflow: hidden;
                    .el-button {
                        position: absolute;
                        right: 5px;
                        bottom: 5px;
                        border-radius: 20px;
                    }
                    .el-textarea .el-textarea__inner{
                        border: none;
                        outline: none;
                        &:hover, &:focus, &:active{
                            border: none;
                            outline: none;
                            box-shadow: none;
                        }
                    }
                }
            }
        }
        &-footer {
            height: 70px;
            background-color: #fff;
            border-top: 1px solid #EEE;
            padding: 12px;
            flex-shrink: 0;
            box-sizing: border-box;
            .footer-operate {
                display: flex;
                align-items: center;
                justify-content: space-between;
                .el-button {
                    padding: 8px 14px;
                    font-size: 12px;
                }
            }
            p {
                font-size: 12px;
                font-weight: 400;
                line-height: 28px;
                color: #C9C7C7;
            }
        }
        .convert-to-risk {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 16px;
            border: 1px solid #333;
            border-radius: 2px;
            cursor: pointer;
            p {
                margin-right: 10px;
            }
            span {
                color: #098AEE;
            }
        }
    }
    &-result .el-button {
        float: right;
        font-size: 12px;
        padding: 4px;
        border-radius: 5px;
        [dir="rtl"] & {
            float: left;
        }
    }
}
</style>
