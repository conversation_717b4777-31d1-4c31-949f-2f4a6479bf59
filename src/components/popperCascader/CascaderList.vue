<template>
    <ul class="cascader-list el-cascader-menu">
        <li
            v-for="item in data"
            :key="item.value"
            class="cascader-item el-cascader-menu__item"
            :class="[{
                'is-active': item.active,
            }, sizeClass]"
            @click="itemClick(item)"
        >
            <el-checkbox
                :size="size"
                :indeterminate="item.indeterminate"
                v-model="item.checked"
                @click.native.stop="checkboxClick(item, $event)"
            ></el-checkbox>
            <span class="cascader-item-name">{{ item.name }}</span>
            <i class="el-icon-arrow-right" v-if="item.children && item.children.length"></i>
        </li>
    </ul>
</template>

<script>
export default {
    props: {
        data: {
            type: Array,
            default: () => [],
        },
        size: {
            type: String,
            default: '',
        },
    },
    computed: {
        sizeClass() {
            return this.size && `cascader-item--${this.size}`;
        },
    },
    methods: {
        checkboxClick(node, e) {
            if (e.target.nodeName === 'INPUT') {
                this.$emit('setCheckbox', node, e.target.checked);
            }
        },
        itemClick(node) {
            this.$emit('expand', node.children);
            this.$emit('nodeClick', node);
        },
    },
};
</script>

<style lang="scss" scoped>
    .cascader-list {
        max-height: 250px;
    }
    .cascader-item {
        font-size: 12px;
        position: relative;
        .cascader-item-name {
            margin-left: 5px;
            max-width: 250px;
            text-overflow: ellipsis;
            overflow: hidden;
            vertical-align: top;
        }
        .el-icon-arrow-right {
            position: absolute;
            right: 10px;
            top: 12px;
        }
    }
</style>
