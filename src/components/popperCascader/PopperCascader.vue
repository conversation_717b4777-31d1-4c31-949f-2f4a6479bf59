<template>
    <div
        class="popper-cascader-wrap"
        v-clickoutside="handleClickoutside"
        ref="reference"
    >
        <el-input
            :size="size"
            :placeholder="$t('poperCascader.plsSelect')"
            readonly
            class="popper-cascader-input"
            :value="inputValue"
            @focus="input.expand = true"
            @click="input.expand = !input.expand"
            :icon="inputIcon"
        >
        </el-input>
        <i
            v-if="clearable && checkedNodes.length"
            class="el-icon-circle-close icon-clear"
            @click="clearData"
        >
        </i>
        <!-- <pre style="height: 101px;overflow: scroll;">{{markedData}}</pre> -->
        <el-collapse-transition>
            <div class="el-cascader-menus" v-show="input.expand" ref="popper">
                <CascaderList
                    :size="size"
                    :data="list"
                    v-for="(list, index) in cascaderLists"
                    :key="index"
                    @setCheckbox="setCheckbox"
                    @nodeClick="nodeClick"
                    @expand="setCascaderList(index + 1, $event)"
                />
            </div>
        </el-collapse-transition>
    </div>
</template>

<script>
import CascaderList from './CascaderList.vue';
import Clickoutside from 'element-ui/lib/utils/clickoutside';
import Popper from 'element-ui/lib/utils/vue-popper';
import i18n from 'src/lang';

const PopperMixin = {
    props: {
        placement: {
            type: String,
            default: 'bottom-start',
        },
        appendToBody: Popper.props.appendToBody,
        visibleArrow: {
            type: Boolean,
            default: false,
        },
        arrowOffset: Popper.props.arrowOffset,
        offset: Popper.props.offset,
        boundariesPadding: Popper.props.boundariesPadding,
        popperOptions: Popper.props.popperOptions,
    },
    methods: Popper.methods,
};
export default {
    components: {
        CascaderList,
    },
    mixins: [PopperMixin],
    directives: {
        Clickoutside,
    },
    props: {
        data: {
            type: Array,
            default: () => [],
        },
        unit: {
            type: String,
            default: i18n.t('poperCascader.person'), // 人
        },
        size: {
            type: String,
            default: '',
        },
        clearable: {
            type: Boolean,
            default: false,
        },
        inputText: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            input: {
                expand: false,
            },
            dataTransfered: [],
            cascaderLists: [],
            markedData: {},
            checkedNodes: [],
            leafNodes: [],
        };
    },
    computed: {
        inputIcon() {
            return this.input.expand ? 'caret-top' : 'caret-bottom';
        },
        checkedLeafNodesLength() {
            return this.checkedNodes.filter((v) => !v.children).length;
        },
        leafNodesLength() {
            return this.leafNodes.length;
        },
        inputValue() {
            if (this.inputText) {
                return this.inputText;
            }
            if (!this.checkedLeafNodesLength) {
                return '';
            }
            return this.$t('poperCascader.selectNumTip', {
                A: this.checkedLeafNodesLength,
                B: this.leafNodesLength,
                C: this.unit,
            }); // 已选择${this.checkedLeafNodesLength}/${this.leafNodesLength}个${this.unit}
        },
    },
    watch: {
        data: {
            handler(nv) {
                this.dataTransfered = nv;
                while (
                    this.dataTransfered.length === 1 &&
                    this.dataTransfered[0].children
                ) {
                    this.dataTransfered = this.dataTransfered[0].children || [];
                }
                if (this.dataTransfered.length > 1) {
                    this.dataTransfered = [
                        {
                            name: this.$t('poperCascader.allSelect'), // 全选
                            type: 'ROOT',
                        },
                        ...this.dataTransfered,
                    ];
                }
                this.transferData(this.dataTransfered);
                this.markupData(this.dataTransfered);
                this.leafNodes = this.getLeafCheckbox(this.getRootNode());
                if (this.dataTransfered.length) {
                    this.setCascaderList(0, this.dataTransfered);
                }
            },
            immediate: true,
        },
        'input.expand': {
            handler() {
                this.$nextTick(() => {
                    this.updatePopper();
                });
            },
        },
    },
    methods: {
        // 数据转换
        transferData(data) {
            (data || []).forEach((node) => {
                this.$set(node, 'checked', false);
                this.$set(node, 'indeterminate', false);
                this.$set(node, 'active', false);
                this.transferData(node.children);
            });
        },
        // 节点标记
        markupData(data, level = 0, mark = '') {
            (data || []).forEach((node, index) => {
                const newMark = level > 0 ? `${mark}-${index}` : `${index}`;
                this.$set(node, '__index', newMark);
                this.markedData[newMark] = node;
                this.markupData(node.children, level + 1, newMark);
            });
        },
        getRootNode() {
            return this.markedData['0'] || {};
        },
        getParentNode(node) {
            let path = node.__index.split('-');
            if (path.length > 1) {
                path = path.slice(0, -1);
                const parent = this.markedData[path.join('-')];
                return parent;
            } else if (path.length === 1) {
                if (~~path[0] > 0) {
                    return this.getRootNode();
                }
            }
            return null;
        },
        getChildren(node) {
            if (node.children) {
                return node.children;
            } else if (node.type === 'ROOT') {
                return this.dataTransfered.slice(1);
            }
            return null;
        },
        getCheckboxByStatus(node, option) {
            let result = [];
            if (node.type !== 'ROOT') {
                let flag = true;
                for (const key in option) {
                    if (option.hasOwnProperty(key)) {
                        if (!!node[key] !== option[key]) {
                            flag = false;
                            break;
                        }
                    }
                }
                if (flag) {
                    result.push(node);
                }
            }
            (this.getChildren(node) || []).map((node) => {
                result = result.concat(this.getCheckboxByStatus(node, option));
            });
            return result;
        },
        // 获取选中的节点
        getCheckedCheckbox(node) {
            return this.getCheckboxByStatus(node, {
                checked: true,
            });
        },
        // 获取所有叶子节点
        getLeafCheckbox(node) {
            return this.getCheckboxByStatus(node, {
                children: false,
            });
        },
        setCascaderList(level, data) {
            this.cascaderLists.splice(level);
            if (data && data.length) {
                this.cascaderLists[level] = data;
                this.$nextTick(() => {
                    this.updatePopper();
                });
            }
        },
        // 设置子节点的状态
        setChildrenCheckbox(children, value) {
            (children || []).forEach((node) => {
                node.checked = value;
                node.indeterminate = false;
                node.active = false;
                this.setChildrenCheckbox(node.children, value);
            });
        },
        // 设置父节点状态
        setParentCheckbox(parent) {
            if (!parent) {
                return;
            }
            parent.checked = false;
            parent.indeterminate = false;
            parent.active = false;
            const children = this.getChildren(parent);
            if (children.every((node) => node.checked)) {
                parent.checked = true;
            } else if (children.some((node) => node.checked || node.indeterminate)) {
                parent.indeterminate = true;
            }
            this.setParentCheckbox(this.getParentNode(parent));
        },
        // 设置节点状态
        setCheckbox(node, value) {
            node.checked = value;
            node.indeterminate = false;
            this.setChildrenCheckbox(this.getChildren(node), node.checked);
            this.setParentCheckbox(this.getParentNode(node));
            this.checkedNodes = this.getCheckedCheckbox(this.getRootNode());
            this.$emit('updateData', this.checkedNodes);
        },
        resetNodeActive(node) {
            const children = this.getChildren(node);
            node.active = false;
            (children || []).forEach((node) => {
                this.resetNodeActive(node);
            });
        },
        nodeClick(node) {
            this.resetNodeActive(this.getRootNode());
            // this.setParentCheckbox(this.getParentNode(node));
            node.active = true;
        },
        handleClickoutside() {
            this.input.expand = false;
        },
        clearData() {
            const rootNode = this.getRootNode();
            this.setCheckbox(rootNode, false);
            this.setChildrenCheckbox(this.getChildren(rootNode), false);
        },
    },
};
</script>

<style lang="scss" scoped>
.popper-cascader-wrap {
  position: relative;
}
</style>
<style lang="scss">
@import "~css/el-reset.scss";
.popper-cascader-wrap {
  .icon-clear {
    display: none;
    color: #bfcbd9;
    position: absolute;
    right: 10px;
    background: #fff;
    top: 8px;
    font-size: 14px;
    cursor: pointer;
    &:hover {
      display: block;
    }
  }
}
.popper-cascader-input {
  input {
    color: $checkBox-blue;
  }
  .el-input__icon {
    font-size: 12px;
    color: #888;
  }
  &:hover + .icon-clear {
    display: inline-block;
  }
}
</style>
