<!--
 * @Author: fan_liu
 * @Date: 2021-03-01 14:11:08
 * @LastEditors: fan_liu
 * @LastEditTime: 2021-03-01 15:15:20
 * @Description: 下载密码弹框
-->
<template>
    <div class="contract-download-password-dialog" v-if="show">
        <el-dialog
            title="下载提示"
            :visible.sync="show"
            :modal="false"
            :close-on-click-modal="false"
        >
            <div class="notice">{{ `您所下载的合同文件《${fileName}.zip》已被系统加密，解压密码如下：` }}</div>
            <el-input readonly v-model="password" class="url-clipboard">
                <template slot="append"><div class="copy-btn" @click="handleCopyUrl">复制</div></template>
            </el-input>
            <p class="tips">注：每次下载密码随机发送！</p>
        </el-dialog>
    </div>
</template>

<script>
import store from 'src/store/store.js';
export default {
    data() {
        return {
            show: true,
        };
    },
    computed: {
        fileName() {
            return store.state.downloadFileName;
        },
        password() {
            return store.state.downloadFilePassword;
        },
    },
    methods: {
        handleCopyUrl() {
            this.$copyText(this.password).then(() => {
                this.$MessageToast({
                    message: '复制成功',
                    type: 'success',
                });
            }, () => {
                this.$MessageToast.error('复制失败');
            });
        },
    },
};
</script>

<style lang="scss">
.contract-download-password-dialog .el-dialog{
    right: 0;
    bottom: 0;
    left: unset;
    top: unset !important;
    transform: unset !important;
    margin: 0 !important;
    width: 490px;
    height: 253px;
    box-shadow: 0px 0px 11px 0px rgba(217,218,219,1);
    border-radius: 6px;
    .el-dialog__header{
        line-height: 54px;
        padding: 0 20px;
        border-bottom: 1px solid #E9E9E9;
        .el-dialog__title{
            font-weight: normal;
            color: #333;
        }
    }
    .el-dialog__body{
        padding: 25px 80px;
        .notice{
            line-height: 28px;
        }
        .url-clipboard{
            margin: 10px 22px;
            width: 266px;
            input{
                color: #127FD2;
                border-radius: 4px 0 0 4px;
            }
            .el-input-group__append{
                color: #127FD2;
                cursor: pointer;
            }
        }
        .tips{
            font-size: 12px;
            color: #999999;
            text-align: center;
        }
    }
}
</style>
