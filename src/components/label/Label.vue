<!-- 普通发起和模板发起使用到的 -->
<template>
    <div class="send-label-container">
        <!-- 系统默认的，印章，签名和日期 -->
        <div class="send-label-default" :style="defaultStyle" v-if="['SEAL', 'SIGNATURE', 'DATE'].includes(mark.type)">
            <div class="send-label-name" v-if="['SEAL', 'SIGNATURE'].includes(mark.type)">{{ receiverName }}</div>
            <div class="send-label-wrapper"
                :style="`background-color: rgba(${color}, 0.6); width: ${markWidth}px; height: ${markHeight}px;`"
                @mousedown="onMousedown"
            >
                <!-- 印章 -->
                <div class="seal-label" :class="{'send-label-seal': !getIsForeignVersion}" v-if="mark.type=== 'SEAL'">
                    <div class="seal-content-ja" v-if="getIsForeignVersion">{{ $t('prepare.sealArea') }}</div>
                    <div class="send-label-seal-img" v-else>
                        <svg
                            class="icon"
                            aria-hidden="true"
                        >
                            <use
                                style="pointer-events: none;"
                                x="0"
                                y="0"
                                width="154"
                                height="96"
                                :xlink:href="`#${iconType}`"
                            >
                            </use>
                        </svg>
                        <!-- 直接用icon，放缩时会导致印章变模糊 -->
                        <!-- <i :class="`${iconType}`"></i> -->
                    </div>
                </div>
                <!-- 签名 -->
                <div class="send-label-signature signature-label" v-else-if="mark.type === 'SIGNATURE'">
                    <!-- <i :class="`${iconType}`"></i> -->
                    <!-- <div class="signature-ja" :style="`transform: rotate(${mark.style.tiltDegrees}deg)`" v-if="getIsForeignVersion">{{ $t('prepare.sealArea') }}</div> -->
                    <svg
                        class="icon"
                        aria-hidden="true"
                    >
                        <use
                            style="pointer-events: none;"
                            x="0"
                            :y="-6"
                            width="140"
                            height="83"
                            :xlink:href="`#${iconType}`"
                        >
                        </use>
                    </svg>
                </div>
                <!-- 日期类型，垂直居中 -->
                <div v-else-if="mark.type === 'DATE'"
                    class="send-label-date"
                    :style="`fontSize:${dateLabelFontSize(mark)}px;line-height: ${markHeight}px;`"
                >
                    {{ $t('sign.signDate') }}
                </div>
            </div>
            <i class="label-close-icon el-icon-ssq-guanbi" :class="{'label-close-date': mark.type === 'DATE', 'none-close-icon': !needCloseIcon}" @click="onDelete" v-if="templateStatus==='edit'"></i>
        </div>

        <!-- 二维码 -->
        <div v-else-if="mark.type==='QR_CODE'" class="send-label-qrcode" :style="`width: ${markWidth}px; height: ${markHeight}px;`">
            <img :width="`${markWidth}px`" :src="`${mark.value ? mark.value: mark.imageHref}?access_token=${$cookie.get('access_token')}`" alt="qr_code" />
        </div>

        <!-- 自定义的业务字段 -->
        <div v-else :style="normalStyle" class="send-label-normal">
            <span class="describe-tooltip" v-if="mark.description && ['TEXT', 'TEXT_NUMERIC'].includes(mark.type)">{{ mark.description }}</span>
            <div v-if="['TEXT', 'BIZ_DATE', 'TEXT_NUMERIC'].includes(mark.type)">
                {{ mark.name }}
            </div>
        </div>
    </div>
</template>

<script>
import { markInfo, markIconInfo } from 'src/pages/foundation/sign/common/info/info.js';
import { mapGetters } from 'vuex';
export default {
    props: {
        templateStatus: {
            type: String,
            default: 'edit',
        },
        mark: {
            type: Object,
            default: () => {},
        },
        color: {
            type: String,
            default: '',
        },
        pageHeight: {
            type: Number,
            default: 1122,
        },
        pageWidth: {
            type: Number,
            default: 793,
        },
        canDrag: {
            type: Boolean,
        },
        scale: {
            type: Number,
            default: 1,
        },
        receiverName: {
            type: String,
            default: '',
        },
        needCloseIcon: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            drag: 'end',
        };
    },
    computed: {
        ...mapGetters(['getIsForeignVersion']),
        defaultWidth() {
            return markInfo(this.mark.type).width;
        },
        defaultHeight() {
            return markInfo(this.mark.type).height;
        },
        iconType() {
            return markIconInfo(this.mark.type).type;
        },
        normalStyle() {
            const { height, x, y, fontSize } = this.mark;
            const left = x;
            const top = 1 - y - height;
            return {
                top: `${top * 100}%`,
                left: `${left * 100}%`,
                width: `${this.markWidth}px`,
                height: `${this.markHeight}px`,
                backgroundColor: `rgba(${this.color}, 0.8)`,
                fontSize: `${fontSize}px`,
            };
        },
        defaultStyle() {
            const { x, y, type, height } = this.mark;
            const left = x;
            // 新的坐标系根据文字定位，标签的左下角相对于pdf左下角的位置
            const top = 1 - y - height;
            const transformHeight = ['SEAL', 'SIGNATURE'].includes(type) ? '-26' : 0;
            // const topOffset = this.getIsForeignVersion ? 0 : 0.4;
            return {
                top: `${top * 100 - 0}%`,
                left: `${left * 100}%`,
                marginTop: `${transformHeight}px`,
                // transform: `translate(0px, ${transformHeight}px)`,
            };
        },
        // 标签的高度
        markHeight() {
            return this.pageHeight * this.mark.height;
        },
        // 标签的宽度
        markWidth() {
            return this.pageWidth * this.mark.width;
        },
    },
    methods: {
        dateLabelFontSize(mark) {
            const isZh = this.$i18n.locale.toLowerCase() === 'zh';
            return isZh ? mark.style && mark.style.fontSize || 14 : 13;
        },
        onMousedown(event) {
            if (this.templateStatus !== 'edit') {
                return;
            }
            // 如果是二维码操作，直接跳转二维码页面
            if (this.mark.type === 'QR_CODE') {
                return;
            }
            this.drag = 'start';
            document.addEventListener('mousemove', this.onMousemove);
            document.addEventListener('mouseup', this.onMouseup);
            this.$emit('mark-start', event);
        },
        onMousemove(event) {
            if (this.templateStatus !== 'edit') {
                return;
            }
            if (this.drag !== 'start' && this.drag !== 'move') {
                return;
            }
            this.drag = 'move';
            this.$emit('mark-move', event);
        },
        onMouseup(event) {
            if (this.templateStatus !== 'edit') {
                return;
            }
            if (this.drag !== 'start' && this.drag !== 'move') {
                return;
            }
            if (this.drag === 'move') {
                this.$emit('mark-end', event);
            }
            this.drag = 'end';
            document.removeEventListener('mousemove', this.onMousemove);
            document.removeEventListener('mouseup', this.onMouseup);
        },
        onDelete() {
            if (this.templateStatus !== 'edit') {
                return;
            }
            this.$emit('mark-delete');
        },
    },
};
</script>

<style lang="scss">
    .send-label-container {
        .send-label-default {
            position: absolute;
            border-radius: 2px;
            // transform-origin: left top;
            z-index: 10;
            user-select: none;
            .send-label-name {
                text-align: left;
                width: 100%;
                border-radius: 2px;
                background-color: rgba(#fff7b6, 0.8);
                margin-bottom: 2px;
                font-size: 12px;
                padding-left: 5px;
                box-sizing: border-box;
                white-space: nowrap;
                line-height: 24px;
            }
            .icon {
                width: 100%;
                height: 100%;
            }
            .send-label-wrapper {
                position: relative;
                border-radius: 2px;
                cursor: move;
            }
            .send-label-signature {
                height: 100%;
            }
            .send-label-seal {
                position: absolute;
                width: 162px;
                height: 162px;
                left: 53px;
                top: 21px;
                background-color: rgba(#fff, 0.3);
                border-radius: 50%;
                &-img {
                    text-align: center;
                    left: 50%;
                    top: 50%;
                    position: absolute;
                    width: 154px;
                    height: 96px;
                    margin-left: -77px;
                    margin-top: -48px;
                    // transform: translate(-50%, -50%);
                }
                .el-icon-ssq-gaizhang1 {
                    font-size: 100px;
                }
            }
            .send-label-date {
                font-size: 14px;
                text-align: left;
            }
        }
        .send-label-qrcode {
            display: flex;
            align-items: center;
            position: absolute;
            right: 0;
            bottom: 0;
        }
        .send-label-normal {
            position: absolute;
            border-radius: 2px;
            cursor: move;
            .describe-tooltip {
                visibility: hidden;
                position: absolute;
                z-index: 9999;
                text-align: left;
                bottom: 22px;
                left:0;
                background-color: #FFF7B6 !important;
                opacity: 0.8;
                color: #333333;
                border: 1px solid #999;
                font-size: 12px;
            }
            &:hover .describe-tooltip {
                visibility: visible;
            }
        }
        .label-close-icon {
            color: white;
            font-size: 12px;
            background-color: rgba(51, 51, 51, .75);
            padding: 5px;
            border-radius: 12px;
            top: 14px;
            position: absolute;
            right: -9px;
            cursor: pointer;
            z-index: 12;
        }
        .label-close-date {
            top: -10px;
        }
        .none-close-icon {
            display: none;
        }
    }

</style>
