<!-- 图形验证码 -->
<template>
    <div class="picture-verify" @click="changeImg">
        <img v-if="imgSrc" :src="imgSrc">
    </div>
</template>
<script>
export default {
    props: {
        imageKey: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            imgSrc: '',
        };
    },
    methods: {
        getImageVerCode() {
            this.$http.getImageVerCode(this.imageKey)
                .then((res) => {
                    this.$emit('change-imageKey', res.data.imageKey);
                    this.imgSrc = `data:image/jpeg;base64,${res.data.image}`;
                });
        },
        changeImg() {
            this.getImageVerCode(this.imageKey);
        },
    },
    created() {
        this.getImageVerCode(this.imageKey);
    },
};
</script>
<style lang="scss">
    .picture-verify {
        display: inline-block;
        vertical-align: middle;
        cursor: pointer;
        img {
            width: 100%;
            height: 100%;
        }
    }
</style>
