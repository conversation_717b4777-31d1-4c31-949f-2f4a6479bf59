<!-- 扫码签名二维码 -->
<!-- todo: 关闭弹窗，清除定时器 -->
<template>
    <div class="qrcode-container" @click="handleClickImg">
        <img :src="QRcodeURL" alt="" height="168px">
    </div>
</template>
<script>
import QRious from 'qrious';
import Bus from 'components/bus/bus.js';

export default {
    /**
     * business表示所属业务，对应调用的接口不同
     * sign: 签署页
     * usercenter: 用户中心修改签名
     */
    props: {
        initialBusiness: {
            type: String,
            default: 'usercenter',
        },
        parentGift: {
            type: Object,
            default: () => ({
                contractId: '',
                labelId: '',
                labelValue: '',
                receiverId: '',
                handWritingRecognition: false,
                name: '',
            }),
        },
    },
    data() {
        return {
            interval: 0,
            QRcodeURL: '',
            signatureImgData: '',
            writeBusiness: ['usercenter', 'batchSign', 'addSignature'],
            signBusiness: ['sign-single', 'sign-all'],
        };
    },
    computed: {
        // Map结构：不同的initialBusiness，对应的二维码图片地址和状态接口
        QRCodeUrlMap() {
            return new Map()
                .set(this.writeBusiness, {
                    QRCodeImgURL: '/users/signatures/qrcode-image',
                    statusURL: '/users/signatures/qrcode/status',
                })
                .set(this.signBusiness, {
                    QRCodeImgURL: `${signPath}/contracts/${this.parentGift.contractId}/sign/qrcode/${this.parentGift.labelId}?handWritingRecognition=${this.parentGift.handWritingRecognition}&receiverId=${this.parentGift.receiverId}&showReplaceAllBtn=${!!this.parentGift.labelValue}&name=${this.parentGift.name}`,
                    statusURL: `${signPath}/contracts/${this.parentGift.contractId}/sign/qrcode/status/${this.parentGift.labelId}`,
                });
        },
        mapValue() {
            return this.writeBusiness.includes(this.initialBusiness)
                ? this.QRCodeUrlMap.get(this.writeBusiness) : this.QRCodeUrlMap.get(this.signBusiness);
        },
    },
    methods: {
        clearQRCodeInterval() {
            window.clearInterval(this.interval);
        },
        checkScanCodeStatus() {
            this.$http.get(this.mapValue.statusURL)
                .then(res => {
                    const resData = res.data;
                    if (resData !== '' && resData != null) {
                        if (resData.status === 'complete') { // 成功
                            this.clearQRCodeInterval();
                            if (this.initialBusiness.match(/usercenter|addSignature/i) || this.initialBusiness === '') {
                                this.signatureImgData = resData.image;
                                this.QRcodeURL = `data:image/jpeg;base64,${resData.image}`;
                            }
                            // 发消息：手写完毕
                            Bus.$emit('write-done', { imageData: resData.image });
                        }
                    }
                });
        },
        handleClickImg() {
            this.clearQRCodeInterval();
            this.getQRCodeImg();
        },
        // ajax
        getQRCodeImg() {
            return this.$http.get(this.mapValue.QRCodeImgURL)
                .then(res => {
                    // 将中文 name 转译，然后转成二维码
                    let url = res.data.url;
                    const [pre, name] = url.split('&name=');
                    if (name) {
                        url = `${pre}&name=${encodeURIComponent(name)}`;
                    }
                    const qr = new QRious({
                        background: 'white',
                        backgroundAlpha: 1,
                        foreground: 'black',
                        foregroundAlpha: 1,
                        level: 'H',
                        size: 300,
                        value: url,
                    });
                    this.QRcodeURL = qr.toDataURL('image/jpeg');
                    this.interval = setInterval(this.checkScanCodeStatus, 3000);
                });
        },
    },
    created() {
        this.getQRCodeImg();
    },
    beforeDestroy() {
        this.clearQRCodeInterval();
    },
};
</script>
<style lang="scss">
    .qrcode-container {
        height: 168px;
        cursor: pointer;
    }
</style>
