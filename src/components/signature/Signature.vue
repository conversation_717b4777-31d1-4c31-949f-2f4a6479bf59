<!-- 签名管理 -->
<template>
    <div class="signature-main">
        <div>
            <div class="title">
                <i class="el-icon-ssq-qianmingguanli"></i>
                <h1>签名管理</h1>
            </div>
            <div class="signature-con">
                <div v-for="(perSign, index) in signaturesList" class="perSignature" :key="index">
                    <p class="inline-block">
                        <img :src="`/users/signatures/${perSign.sigId}/signature/${perSign.fileId}?access_token=${accessToken}`" class="signatureImg">
                    </p>
                    <p class="inline-block">
                        <span class="signatureType">{{ perSign.name }}</span>
                    </p>
                    <p v-if="perSign.isDefault" class="inline-block defaultSignature">
                        <i class="el-icon-ssq-right-filling"></i>
                        <span>默认签名</span>
                    </p>
                    <p v-else class="inline-block common-font-color cursor-point" :data-id="perSign.sigId" @click="setDefaultSign">
                        设为默认签名
                    </p>

                    <p class="fr inline-block operation">
                        <span class="cursor-point edit common-font-color" :data-signid="perSign.sigId" :data-fileid="perSign.fileId" :data-isSameName="perSign.isSameName" @click="editSignatureFun">编辑</span>
                        <span>&nbsp;&nbsp;|&nbsp;&nbsp; </span>
                        <span class="cursor-point delete common-font-color" :data-id="perSign.sigId" @click="deleteSign">删除</span>
                    </p>
                </div>

                <button class="addSignature btn-type-one" @click="addSignatureFun">
                    <span class="el-icon-ssq-jia"></span>
                    <span>添加签名</span>
                </button>
            </div>
        </div>
        <div class="personage-sign-style">
            <h1>个人账号签名样式</h1>
            <div class="radios-con images-con">
                <el-radio-group v-model="SIGN_STYLE.data" @change="checkboxChange('SIGN_STYLE')">
                    <div class="inline-block per-signstyle-con">
                        <el-radio class="radio" label="2">显示上上签边框与二维码</el-radio>
                        <p class="inline-block one">
                            <span class="inline-block"></span>
                        </p>
                    </div>
                    <div class="inline-block per-signstyle-con">
                        <el-radio class="radio" label="1">只显示上上签边框</el-radio>
                        <p class="inline-block two">
                            <span class="inline-block"></span>
                        </p>
                    </div>
                    <div class="inline-block per-signstyle-con">
                        <el-radio class="radio" label="3">不显示上上签边框与二维码</el-radio>
                        <p class="inline-block three">
                            <span class="inline-block"></span>
                        </p>
                    </div>
                </el-radio-group>
            </div>
        </div>
        <div class="pop-content">
            <!-- 添加签名弹框 -->
            <SlidePopModel class="addSignaturePop" :outsideShow.sync="addSignatureData.popShow">
                <div slot="slide-pop-content">
                    <h1 class="slide-pop-head">
                        {{ editSignatureData.status === 1 ? '编辑签名' : '添加签名' }}
                    </h1>
                    <div class="slide-pop-body">
                        <el-menu mode="horizontal" :default-active="addSignatureData.activeIndex" @select="handleSelect">
                            <el-menu-item index="1">扫码签名</el-menu-item>
                            <el-menu-item index="2">上传签名</el-menu-item>
                            <template v-if="$store.getters.getUserFullName">
                                <el-menu-item index="3">艺术字体</el-menu-item>
                                <el-menu-item index="4">生成签名章</el-menu-item>
                            </template>
                        </el-menu>
                        <div class="signaturSettingCon">
                            <div
                                v-if="addSignatureData.activeIndex === '2'"
                                class="upload-section-con"
                            >
                                <div class="section">
                                    <el-upload
                                        class=""
                                        :action="uploadAjaxUrl"
                                        :show-file-list="uploadShowFileListData"
                                        :before-upload="beforeUpload"
                                    >
                                        <div class="uploadCon" v-if="uploadStatus == 0">
                                            <i class="inline-block el-icon-ssq-shangchuantupian"></i>
                                            <button class="btn-type-two">上传签名</button>
                                        </div>
                                        <div class="imgCon" v-else>
                                            <img :src="uploadImgUrl">
                                        </div>
                                    </el-upload>
                                </div>
                                <p class="info">
                                    <i class="el-icon-ssq-tishi1"></i>
                                    <span>上传签名图片时请将签名充满整个图片</span>
                                </p>
                                <div class="eg-con">
                                    <p>图例示范：</p>
                                    <img src="./images/eg-1.png">
                                    <img src="./images/eg-2.png">
                                </div>
                            </div>
                            <div
                                v-if="addSignatureData.activeIndex === '1'"
                                class="scanCode-section-con"
                            >
                                <div class="section scanCodeSection">
                                    <div class="scanCodeCon">
                                        <!-- <img src="../images/ssqQRcode.png"> -->
                                        <img :src="QRcodeURL">
                                    </div>
                                    <p class="info">
                                        <i class="el-icon-ssq-tishi1"></i>
                                        <span>请使用手机微信或浏览器扫描二维码后签名</span>
                                    </p>
                                </div>
                            </div>

                            <div class="section" v-if="addSignatureData.activeIndex === '3'">
                                <el-radio-group v-model="addSignatureData.artFonts" class="artFontSetting">
                                    <div class="perRadio">
                                        <el-radio :label="1"><img :src="addSignatureData.artFontsData.imgUrlList[0]"></el-radio>
                                    </div>
                                    <div class="perRadio">
                                        <el-radio :label="2"><img :src="addSignatureData.artFontsData.imgUrlList[1]"></el-radio>
                                    </div>
                                    <div class="perRadio">
                                        <el-radio :label="3"><img :src="addSignatureData.artFontsData.imgUrlList[2]"></el-radio>
                                    </div>
                                </el-radio-group>
                            </div>
                            <div class="section" v-if="addSignatureData.activeIndex === '4'">
                                <div class="createSignatureSeal">
                                    <img :src="addSignatureData.signatureSealUrl">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="slide-pop-foot">
                        <el-button
                            v-if="addSignatureData.activeIndex == 2"
                            class="btn-type-one btn-padding"
                            @click="saveUploadCode"
                        >
                            保存
                        </el-button>
                        <el-button
                            v-else-if="addSignatureData.activeIndex == 1"
                            class="btn-type-one btn-padding"
                            @click="saveScanCode"
                        >
                            保存
                        </el-button>
                        <button
                            v-else-if="addSignatureData.activeIndex == 3"
                            class="btn-type-one btn-padding"
                            @click="saveArtFont"
                        >
                            保存
                        </button>
                        <button
                            v-else-if="addSignatureData.activeIndex == 4"
                            class="btn-type-one btn-padding"
                            @click="savePersonSeal"
                        >
                            保存
                        </button>
                    </div>
                </div>

            </SlidePopModel>
            <!-- 编辑签名弹框 -->
            <SlidePopModel class="editSignaturePop" :outsideShow.sync="editSignatureData.popShow">
                <div slot="slide-pop-content">
                    <h1 class="slide-pop-head">
                        编辑签名
                    </h1>
                    <div class="slide-pop-body">
                        <div class="signatureImgCon">
                            <p>
                                <img :src="editSignatureData.imgSrc">
                            </p>
                        </div>
                    </div>
                    <div class="slide-pop-foot">
                        <button class="btn-type-one btn-padding" @click="changeSignature">更换签名</button>
                    </div>
                </div>
            </SlidePopModel>
        </div>
    </div>
</template>

<script>
import SlidePopModel from 'components/slide_pop/SlidePopModel.vue';
import { goReturnUrl } from 'src/common/utils/returnUrl.js';

export default {
    components: {
        SlidePopModel,
    },
    data() {
        return {
            accessToken: this.$cookie.get('access_token'),
            uploadAjaxUrl: '',
            uploadShowFileListData: false,
            uploadImgUrl: '',
            uploadStatus: 0, // 0 未上传 1 上传
            QRcodeURL: '',
            QRcodeURLData: '',
            interval: 0,
            saveScanCodeDisabled: true,
            editSignatureData: {
                status: 1, // 1 编辑签名 0 新增
                popShow: false,
                imgSrc: '',
                sigId: '',
                fileId: '',
                isSameName: '',
            },
            addSignatureData: {
                status: 1, // 1 编辑签名 0 新增
                popShow: false,
                activeIndex: '1',
                ajaxUrl: `/users/signatures`,
                file: {},
                artFonts: 1,
                artFontsData: {
                    imgUrlDataList: [],
                    imgUrlList: [],
                },
                signatureSealUrl: '',
                signatureSealData: '',
            },
            signaturesList: [	//	签名列表
                // {
                // 	fileId: '',	//	处理后图片文件主键	number
                // 	isDefault: '',	//	是否默认	boolean
                // 	isSameName: '',	//	是否通过笔迹鉴定	boolean
                // 	name: '',	//	签名名称	string
                // 	sigId: '',	//	主键	number
                // 	sourceFileId: '',	//	原图片文件主键	number
                // 	type: '',	//	签名类型	number	0: 普通签名，1:历史签名
                // 	userId: '',	//	用户编号	number
                // }
            ],
            SIGN_STYLE: {	// 个人账号签名样式
                data: '1',
                name: 'SIGN_STYLE',
                value: '',
            },
            returnUrl: ['null', ''].includes(this.$route.query.returnUrl) ? null : this.$route.query.returnUrl,
        };
    },
    computed: {
        paramSigId: function() {
            if (this.editSignatureData.status === 0) {	// 新增
                return '';
            } else {	// 编辑
                return this.editSignatureData.sigId;
            }
        },
    },
    watch: {
        'addSignatureData.popShow': function(val) {
            const _this = this;
            if (!val) {
                _this.uploadStatus = 0;
                _this.uploadImgUrl = '';
                window.clearInterval(_this.interval);
            } else {
                _this.handleSelect('1');
            }
        },
    },
    methods: {
        /**
         * @desc   获取当前用户签名样式
         */
        getUserConfigs: function() {
            const _this = this;
            _this.$http.get(`/users/configs`)
                .then(res => {
                    const resData = res.data;
                    if (resData && resData.length > 0) {
                        for (var i = resData.length - 1; i >= 0; i--) {
                            const nowObj = resData[i];
                            switch (nowObj.name) {
                                case 'SIGN_STYLE':
                                    _this.SIGN_STYLE.data = nowObj.value;
                                    break;
                            }
                        }
                    }
                });
        },
        /**
         * @desc   更改签名样式
         * @param  {String}
         */
        checkboxChange(name) {
            const params = {
                name: 'SIGN_STYLE',
                value: this.SIGN_STYLE.data,
            };

            this.$http.post(`/users/configs/${name}`, params)
                .then(() => {

                });
        },
        /**
         * @desc   添加签名
         */
        addSignatureFun: function() {
            if (this.signaturesList.length >= 10) {
                this.$MessageToast.info('最多设置10个签名');
            } else {
                this.editSignatureData.status = 0;
                this.addSignatureData.popShow = true;
            }
        },
        /**
         * @desc   获取签名列表
         */
        initSignaturesList: function() {
            const _this = this;
            this.$http.get(`/users/signatures`)
                .then(res => {
                    const resData = res.data;
                    _this.signaturesList = [];
                    resData.forEach((value) => {
                        if (value.isDefault) {
                            _this.signaturesList.unshift(value);
                        } else {
                            _this.signaturesList.push(value);
                        }
                    });
                });
        },
        /**
         * @desc   更改签名
         */
        changeSignature: function() {
            this.editSignatureData.popShow = false;
            this.editSignatureData.status = 1;
            this.addSignatureData.popShow = true;
        },
        /**
         * @desc   上传图片
         */
        beforeUpload: function(file) {
            const _this = this;
            this.addSignatureData.file = file;
            const isLt5M = file.size / 1024 / 1024 < 5;
            if (!isLt5M) {
                this.$MessageToast({
                    message: '上传图片大小不能超过 5MB!',
                    iconClass: 'el-icon-ssq-yiguoqi',
                });
                return isLt5M;
            }
            if (typeof FileReader !== 'undefined') {
                var reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = function() {
                    _this.uploadImgUrl = reader.result;
                    _this.uploadStatus = 1;
                };
            }
            return false;
        },
        getSignaturesList: function() {

        },
        /**
         * @desc  设置默认签名
         */
        setDefaultSign: function(event) { // 设为默认签名
            const _this = this;
            const sigId = event.target.getAttribute('data-id');
            this.$http.put(`/users/signatures/${sigId}/default`)
                .then(() => {
                    _this.$MessageToast({
                        message: '设置成功',
                        iconClass: 'el-icon-ssq-qianyuewancheng',
                    });
                    const signatures = _this.signaturesList;
                    _this.signaturesList = [];
                    for (var i = 0; i < signatures.length; i++) {
                        const nowSeal = signatures[i];
                        if (nowSeal.sigId === sigId) {
                            nowSeal.isDefault = true;
                            _this.signaturesList.unshift(nowSeal);
                        } else {
                            nowSeal.isDefault = false;
                            _this.signaturesList.push(nowSeal);
                        }
                    }
                });
        },
        /**
         * @desc  删除当前签名
         */
        deleteSign: function(event) {
            const _this = this;
            const sigId = event.target.getAttribute('data-id');
            this.$http.delete(`/users/signatures/${sigId}`)
                .then(() => {
                    this.$MessageToast({
                        message: '删除成功',
                        iconClass: 'el-icon-ssq-qianyuewancheng',
                    });
                    if (this.checkReturnUrl()) {
                        return;
                    }
                    const signList = _this.signaturesList;
                    _this.signaturesList = [];
                    for (var i = 0; i < signList.length; i++) {
                        if (signList[i].sigId !== sigId) {
                            _this.signaturesList.push(signList[i]);
                        }
                    }
                });
        },
        /**
         * @desc   添加签名导航切换
         * @param  {String}
         * @param  {[type]}
         */
        handleSelect: function(key) {
            const _this = this;
            this.addSignatureData.activeIndex = key;
            if (+key === 4) { // 生成签名章
                this.$http.get(`/users/signatures/person-seal-image`)
                    .then(res => {
                        this.addSignatureData.signatureSealUrl = `data:image/jpeg;base64,${res.data.value}`;
                        this.addSignatureData.signatureSealData = res.data.value;
                    });
            } else if (+key === 1) { // 签名二维码生成
                // let jumpUrl = encodeURI(`${location.origin}/enterprise/usercenter/handpainted?business=usercenter%26token=%s`);
                this.$http.get(`/users/signatures/qrcode-image`)
                    .then(res => {
                        _this.QRcodeURLData = res.data.value;
                        _this.QRcodeURL = `data:image/jpeg;base64,${res.data.value}`;
                        _this.interval = setInterval(_this.checkScanCodeStatus, 3000);
                    });
            } else if (+key === 3) {
                this.$http.get(`/users/signatures/art-image`)
                    .then(res => {
                        this.addSignatureData.artFontsData.imgUrlDataList = res.data;

                        const temImgUrlList = this.addSignatureData.artFontsData.imgUrlDataList;
                        for (var i = 0; i < temImgUrlList.length; i++) {
                            const base64Obj = `data:image/jpeg;base64,${temImgUrlList[i]}`;
                            this.addSignatureData.artFontsData.imgUrlList.push(base64Obj);
                        }
                    });
            }
        },
        /**
         * @desc   保存上传签名
         */
        saveUploadCode: function() {
            const _this = this;
            if (!this.uploadImgUrl) {
                return this.$MessageToast.error('请先上传签名');
            }
            const option = {
                file: '',
                sigId: '',
            };
            option.file = this.addSignatureData.file;
            option.sigId = this.paramSigId;
            const formData = new FormData();
            Object.keys(option).forEach(key => {
                formData.append(key, option[key]);
            });
            // 保存上传签名
            this.$http.post(`/users/signatures`,
                            formData,
            )
                .then(() => {
                    this.$MessageToast({
                        message: '保存成功',
                        iconClass: 'el-icon-ssq-qianyuewancheng',
                    });
                    _this.addSignatureData.popShow = false;
                    if (this.checkReturnUrl()) {
                        return;
                    }
                    _this.initSignaturesList();
                });
        },
        checkScanCodeStatus: function() {
            const _this = this;
            this.$http.get(`/users/signatures/qrcode/status`)
                .then(res => {
                    const resData = res.data;
                    if (resData !== '' && resData != null) {
                        if (resData.status === 'complete') { // 成功
                            window.clearInterval(_this.interval);
                            _this.saveScanCodeDisabled = false;
                            _this.QRcodeURLData = resData.image;
                            _this.QRcodeURL = `data:image/jpeg;base64,${resData.image}`;
                        }
                    }
                });
        },
        /**
         * @desc   保存扫码签名
         */
        saveScanCode: function() {
            const _this = this;
            if (this.saveScanCodeDisabled) {
                return  this.$MessageToast.error('请先扫码签名');
            }
            this.$http.post(`/users/signatures/qrcode`, {
                file: _this.QRcodeURLData,
                sigId: _this.paramSigId,
            })
                .then(() => {
                    this.saveScanCodeDisabled = true;
                    this.$MessageToast({
                        message: '保存成功',
                        iconClass: 'el-icon-ssq-qianyuewancheng',
                    });
                    _this.addSignatureData.popShow = false;
                    if (this.checkReturnUrl()) {
                        return;
                    }
                    _this.initSignaturesList();
                });
        },
        /**
         * @desc   保存艺术字体签名
         */
        saveArtFont: function() {
            const _this = this;
            // 保存艺术字体签名
            let artFontName = ''; // "楷体签名" "宋体签名" "行楷签名"
            let artFontFile = '';
            switch (this.addSignatureData.artFonts) {
                case 1:
                    artFontName = '楷体签名';
                    artFontFile = this.addSignatureData.artFontsData.imgUrlDataList[0];
                    break;
                case 2:
                    artFontName = '宋体签名';
                    artFontFile = this.addSignatureData.artFontsData.imgUrlDataList[1];
                    break;
                case 3:
                    artFontName = '行楷签名';
                    artFontFile = this.addSignatureData.artFontsData.imgUrlDataList[2];
                    break;
            }

            this.$http.post(`/users/signatures/art`, {
                file: artFontFile,
                name: artFontName,
                sigId: this.paramSigId,
            })
                .then(() => {
                    this.$MessageToast({
                        message: '保存成功',
                        iconClass: 'el-icon-ssq-qianyuewancheng',
                    });
                    _this.addSignatureData.popShow = false;
                    if (this.checkReturnUrl()) {
                        return;
                    }
                    _this.initSignaturesList();
                });
        },
        /**
         * @desc   保存签名章签名
         */
        savePersonSeal: function() {
            const _this = this;
            // 保存签名章
            this.$http.post(`/users/signatures/person-seal`, {
                file: this.addSignatureData.signatureSealData,
                sigId: this.paramSigId,
            })
                .then(() => {
                    this.$MessageToast({
                        message: '保存成功',
                        iconClass: 'el-icon-ssq-qianyuewancheng',
                    });
                    _this.addSignatureData.popShow = false;
                    if (this.checkReturnUrl()) {
                        return;
                    }
                    _this.initSignaturesList();
                });
        },
        /**
         * @desc   编辑签名
         */
        editSignatureFun: function(event) {
            this.editSignatureData.sigId = event.target.getAttribute('data-signid');
            this.editSignatureData.fileId = event.target.getAttribute('data-fileid');
            this.editSignatureData.isSameName = event.target.getAttribute('data-isSameName');
            this.editSignatureData.status = 1;
            this.editSignatureData.imgSrc = `/users/signatures/${this.editSignatureData.sigId}/signature/${this.editSignatureData.fileId}?access_token=${this.accessToken}`;
            this.editSignatureData.popShow = true;
        },
        checkReturnUrl() {
            if (this.returnUrl) {
                setTimeout(() => {
                    goReturnUrl(this.returnUrl);
                }, 1500);
                return true;
            }
        },
    },
    created: function() {
        this.initSignaturesList();
        this.getUserConfigs();
    },
    destroyed: function() {
        const _this = this;
        window.clearInterval(_this.interval);
    },
};
</script>

<style lang="scss">
	$per-signature-height: 115px;
    $left-padding: 17px;

	.usercenter .signature-main {
		.btn-padding {
			width: 90px;
			height: 34px;
			line-height: 34px;
		}
		.perSignature {
			margin-left: 15px;
			margin-right: 15px;
			height: $per-signature-height;
			border-bottom: 1px dashed $border-color;
			&:hover {
				background-color: #fbfbfb;
			}
			p {
					vertical-align: middle;
				}
				p:nth-child(1) {
					width: 135px;
					margin-left: 12px;
					img {
						width: 135px;
					}
				}
				p:nth-child(2) {
					width: 65px;
					margin-left: 15%;
					text-align: center;
				}
				.signatureType {
					color: #343434;
				}
				.signaturePassStatus {
					width: 100%;
					margin-top: 7px;
					padding-top: 3px;
					padding-bottom: 5px;
					color: #fff;
					font-size: 11px;
				}
				p:nth-child(3),p:nth-child(4) {
					line-height: $per-signature-height;
				}
				p:nth-child(3) {
					margin-left: 20%;
				}
				.defaultSignature {
					i {
						color: #2baa3f;
					}
					span {
						color: #999;
					}
				}
				p:nth-child(4) {
					margin-right: 18px;
					span:nth-child(2) {
						color: $border-color;
					}
				}
			}
			.addSignature {
				width: 100px;
				height: 28px;
				line-height: 28px;
				margin-top: 17px;
				margin-left: 30px;
				margin-bottom: 45px;
				span:first-child {
					margin-right: 7px;
					font-size: 16px;
				}
				span:last-child {
					font-size: 12px;
				}
			}
		.personage-sign-style {
			margin-left: 30px;
			margin-top: 40px;
			margin-bottom: 50px;
			h1 {
				margin-bottom: 15px;
				font-weight: bold;
				font-size: 16px;
			}
			.per-signstyle-con {
				margin-right: 10px;
				width: 32%;
				height: 150px;
				background-color: #f8f8f8;
				&:hover {
					background-color: #f1f6f9;
				}
			}
			.radios-con {
				.el-radio-group {
					width: 100%;
				}
				.el-radio {
					display: block;
					padding-left: 35px;
					padding-top: 18px;
					padding-bottom: 18px;

				}
			}
			p {
				margin-left: 15px;
				padding-left: 35px;
				vertical-align: top;
				span {
					margin-left: 13px;
					vertical-align: middle;
				}
			}
			p.one {
					margin-left: 0;
				}
			p.two span {
				width: 132px;
				height: 67px;
				background-image: url(../images/showBorder.png);
				background-size: 100% 100%;
				background-repeat: no-repeat;
			}
			p.one span {
				width: 150px;
				height: 67px;
				background-image: url(../images/showBorderQRcode.png);
				background-size: 100% 100%;
				background-repeat: no-repeat;
			}
			p.three span {
				width: 65px;
				height: 36px;
				background-image: url(../images/blankBorderQRcode.png);
				background-size: 100% 100%;
				background-repeat: no-repeat;
			}
        }

			.addSignaturePop {
				padding-top: 18px;
				padding: 7px 19px 9px 0;
				font-size: 12px;
				span {
					font-size: 14px;
				}
				.el-menu li {
					font-size: 12px;
				}
				// 重写导航样式
				.el-menu {
					background-color: #fff;
					.el-menu-item {
						padding: 0;
						margin: 0 10px;
						&:nth-child(1) {
							margin-left: $left-padding;
						}
						&:hover {
							color: #0070c9;
							border-bottom: 2px solid #0070c9;
						}
					}
				}
				.el-menu--horizontal .el-menu-item {
					height: 37px;
					line-height: 37px;
					background-color: #fff;
					border-bottom: 1px solid transparent;
				}
				.el-menu--horizontal.el-menu--dark .el-submenu .el-menu-item.is-active, .el-menu-item.is-active {
					color: #0070c9;
					border-bottom: 2px solid #0070c9;
				}
				//右弹窗设置签名
			.signaturSettingCon {
				.section {
					width: 262px;
					height: 217px;
					margin-left: $left-padding;
					background-color: #f6f6f6;
				}
				.upload-section-con, .scanCode-section-con {
					.info {
						margin-top: 13px;
						span {
							font-size: 12px;
							color: #666;
						}
						i {
							font-size: 14px;
							color: #f86e2b;
						}
					}
				}
				.upload-section-con {
					.info, .eg-con {
						margin-left: 20px;
					}
					.eg-con {
						margin-top: 20px;
						p {
							margin-bottom: 8px;
							font-size: 12px;
							color: #666;
						}
						img {
							display: inline-block;
							width: 130px;
						}
					}
				}
				// 上传签名
				.el-upload {
					width: 100%;
				}
				.imgCon {
					display:table-cell;
					width: 262px;
					height: 217px;
					vertical-align: middle;
					text-align: center;
					img {
						max-width: 100%;
						max-height: 100%;
						vertical-align: middle;
					}
				}
				.uploadCon {
					width: 81px;
					margin: 0 auto;
					padding-top: 64px;
					text-align: center;
					i {
						margin: 9px auto;
						font-size: 42px;
						color: #999;
					}
					button {
						width: 100%;
						height: 30px;
						line-height: 30px;
						margin: 9px auto;
						background-color: #f6f6f6;
						border: 1px solid #ccc;
						color: #333;
					}
				}
				// 扫码签名
				.scanCodeSection {
					// padding-top: 20px;

					.scanCodeCon {
						margin: 0 auto;
						// padding-top: 17px;
						// width: 151px;
						// height: 151px;
						width: 262px;
						min-height: 217px;
						line-height: 217px;
						text-align: center;
						vertical-align: middle;
						img {
							// width: 100%;
							// height: 100%;
							width: 151px;
							vertical-align: middle;
						}
					}
				}
				// 艺术字体
				.section:nth-child(3) {
					height: auto;
				}
				.artFontSetting {
					width: 100%;
					.perRadio {
						width: 100%;
						padding-top: 10px;
						padding-bottom: 10px;
						border-bottom: 1px solid #eee;
						&:last-child {
							border-bottom: none;
						}
					}
					.el-radio+.el-radio {
						margin-left: 0;
					}
					.el-radio {
						display: block;
						padding-left: 29px;
					}
					span {
						display: inline-block;
						vertical-align: middle;
					}
					img {
						width: 100px;
						height: 50px;
						margin-left: 20px;
					}
				}
				//生成签名章
				.section:nth-child(4) {
					padding-top: 45px;
				}
				.createSignatureSeal {
					width: 122px;
					height: 122px;
					margin: 0 auto;
					img {
						width: 100%;
						height: 100%;
						margin-top: 35px;
					}
				}
			}
			}

			.editSignaturePop {
				.slide-pop-body > div {
					text-align: center;
				}
				.signatureImgCon {
					width: 260px;
					height: 190px;
					margin: 20px auto 10px;
					vertical-align: middle;
					border: 1px solid #eee;
					p {
						display:table-cell;
						width: 260px;
						height: 190px;
						vertical-align: middle;
						text-align: center;
						img {
							max-width: 100%;
							max-height: 100%;
							vertical-align: middle;
						}
					}
				}
				.passStatusCon {
					text-align: center;
				}
				.signatureStatus {
					margin: 0 auto;
					padding: 3px 8px;
					background-color: #ccc;
				}
			}

	}
</style>
