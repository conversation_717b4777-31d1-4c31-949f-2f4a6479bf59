<template>
    <div id="hand-painted">
        <template v-if="!resultTxt">
            <el-row class="drawSign" v-if="orientation=='portrait'">
                <el-col :span="4" class="footer-box col-draw">
                    <div class="sign-footer">
                        <el-row class="rotate_footer">
                            <el-col :span="8" class="draw_footer_left">
                                <el-button type="primary"
                                    @click="submitSign"
                                    class="useBtn btn-type-one"
                                    v-loading.fullscreen.lock="fullscreenLoading"
                                >{{ $t('handwrite.confirm') }}
                                </el-button>
                                <el-button @click="repaint" class="reButton" type="text">{{ $t('handwrite.rewrite') }}
                                </el-button>
                                <el-button v-if="isShowCancle" @click="closeDraw('click')">{{ $t('handwrite.cancel') }}</el-button>
                            </el-col>
                            <el-col :span="8" class="draw_footer_center">
                                <el-button icon="arrow-left" class="scrollLeft-btn" :disabled="isDisableLeft" @click="move('left')">
                                </el-button>
                                <el-button icon="arrow-right" @click="move('right')">
                                </el-button>
                                <span class="draw-tip" v-if="isShowTip">{{ $t('handwrite.clickExtend') }}</span>
                                <el-checkbox class="draw-btn" v-if="isMobile && showReplaceAllBtn" :label="true" v-model="replaceAllSealAndSignature">{{ $t('handwrite.replaceAllSignature') }}</el-checkbox>
                            </el-col>
                            <el-col :span="7" class="draw_footer_right" v-if="business === 'mobileSign'">
                                <div class="draw-setting">
                                    <div>
                                        <el-checkbox :label="true" v-model="settingDefault" :disabled="hasNoSignatures">{{ $t('handwrite.settingDefault') }}</el-checkbox>
                                        <el-tooltip popper-class="draw-tooltip" effect="dark" :content="$t('handwrite.moreTip')" placement="top">
                                            <i class="el-icon-ssq-icon-bs-bangzhuzhongxin"></i>
                                        </el-tooltip>
                                    </div>
                                    <div>
                                        <el-checkbox :label="true" v-if="showReplaceAllBtn || isMobile" v-model="replaceAllSealAndSignature">{{ $t('handwrite.replaceAllSignature') }}</el-checkbox>
                                    </div>
                                </div>
                            </el-col>
                        </el-row>
                    </div>
                </el-col>
                <el-col :span="17" class="col-draw draw_scroll_box">
                    <div class="canvas_box" id="j_canvas_box">
                        <div class="canvas_box-center" :style="drawStyle">
                            <div v-if="useNewVersion" class="drawBoard-char__bg" :style="{ width: drawStyle.height, height: drawStyle.width, fontSize: bgStyle.fontSize, transformOrigin: `${canvasH / 2}px 50%`, lineHeight: bgStyle.width }">
                                <span v-for="(char, index) in name" :key="index" :class="{ 'canvas-box__charBg-char--dot': isDotChar(char) }" class="drawBoard-char__bg-char" :style="{ width: `${charWidth}px` }">{{ char }}</span>
                            </div>
                            <canvas id="drawBoard"
                                :key="keyIndex"
                                :class="drawBoardBg"
                                :width="canvasWidthWithDevicePixelRatio"
                                :height="canvasHeightWithDevicePixelRatio"
                                :style="drawStyle"
                            >
                                {{ $t('handwrite.upgradeBrowser') }}
                            </canvas>
                        </div>
                    </div>
                </el-col>
                <el-col :span="3" class="col-draw">
                    <div class="sign-title">
                        <div class="rotate_title">
                            <p class="title" ref="title">
                                {{ $t('handwrite.title') }}
                            </p>
                        </div>
                    </div>
                </el-col>
            </el-row>
            <div class="el-row-sign" v-else>
                <!-- <vHeader class="header" :isReturn="true">
               <div slot="operate" class="title">手写签名</div>
             </vHeader>-->
                <div class="draw_scroll_box">
                    <div class="landscape_box" id="j_landscape_box">
                        <div class="canvas_box-center" :style="drawStyle">
                            <div v-if="useNewVersion" class="drawBoard-char__bg--land" :style="bgStyle">
                                <span v-for="(char, index) in name" :key="index" :class="{ 'canvas-box__charBg-char--dot': isDotChar(char) }" class="drawBoard-char__bg-char" :style="{ width: `${charWidth}px` }">{{ char }}</span>
                            </div>
                            <canvas id="drawBoard"
                                :key="keyIndex"
                                :class="drawBoardBg"
                                :width="canvasWidthWithDevicePixelRatio"
                                :height="canvasHeightWithDevicePixelRatio"
                                :style="drawStyle"
                            >
                                {{ $t('handwrite.upgradeBrowser') }}
                            </canvas>
                        </div>

                    </div>
                </div>

                <el-row class="draw_footer">
                    <el-col :span="8" class="draw_footer_left">
                        <el-button type="primary"
                            @click="submitSign"
                            class="useBtn btn-type-one"
                            v-loading.fullscreen.lock="fullscreenLoading"
                        >{{ $t('handwrite.confirm') }}
                        </el-button>
                        <el-button @click="repaint" class="reButton" type="text">{{ $t('handwrite.rewrite') }}</el-button>
                        <el-button v-if="isShowCancle" @click="closeDraw('click')">{{ $t('handwrite.cancel') }}</el-button>
                    </el-col>
                    <el-col :span="isMobile ? 16 : 8" class="draw_footer_center">
                        <el-button icon="arrow-left" :disabled="isDisableLeft" class="scrollLeft-btn" @click="move('left')">
                        </el-button>
                        <el-button icon="arrow-right" @click="move('right')">
                        </el-button>
                        <span class="draw-tip" v-if="isShowTip">{{ $t('handwrite.clickExtend') }}</span>
                        <el-checkbox class="draw-btn" v-if="isMobile && showReplaceAllBtn" :label="true" v-model="replaceAllSealAndSignature">{{ $t('handwrite.replaceAllSignature') }}</el-checkbox>
                    </el-col>
                    <el-col :span="7" class="draw_footer_right">
                        <div class="draw-setting">
                            <div>
                                <el-checkbox :label="true" v-model="settingDefault">{{ $t('handwrite.settingDefault') }}</el-checkbox>
                                <el-tooltip popper-class="draw-tooltip" effect="dark" :content="$t('handwrite.moreTip')" placement="top">
                                    <i class="el-icon-ssq-icon-bs-bangzhuzhongxin"></i>
                                </el-tooltip>
                            </div>
                            <div>
                                <el-checkbox :label="true" v-if="showReplaceAllBtn" v-model="replaceAllSealAndSignature">{{ $t('handwrite.replaceAllSignature') }}</el-checkbox>
                            </div>
                        </div>
                    </el-col>

                </el-row>

            </div>
        </template>
        <div v-else class="result-page">
            {{ resultTxt }}
        </div>
    </div>
</template>
<script>
import { isPC } from 'src/common/utils/device.js';
// import Draw from 'utils/draw.js';
import Bus from 'components/bus/bus.js';
import SignaturePad from 'utils/signature_pad.js';
import { isHUAWEI, isIOS, IOSVersion } from 'src/common/utils/device.js';
import { positionsDivide, base64ImageDivide } from 'src/common/utils/handwriteDivide.js';
/* import { getQueryString, urlDelParameter } from 'utils/getQueryString.js';*/
import { syncAllSignLabelIgnore, syncAllSignLabel } from 'src/api/sign.js';
import { loadLanguageAsync } from 'src/lang/index.js';

const dotReg = /[\·\.]/g;
// 中文单个字符的引导书写框的宽高比
const aspectRatio = 0.69;

export default {
    components: {},
    /**
     * business表示所属业务，对应调用的接口不同
     * usercenter: 用户中心修改签名
     * sign: web签署页和移动端签署页
     * mobileSign: 扫码签名
     */
    props: {
        initialBusiness: {
            type: String,
            default: '',
        },
        'parentGift': {
            type: Object,
            default: function() {
                return {
                    contractId: '',
                    labelId: '',
                    labelValue: '',
                    receiverId: '',
                    handWritingRecognition: false, // 是否需要笔迹识别
                    name: '',
                    useNewVersion: false,
                    showReplaceAllBtn: false,
                };
            },
        },
        'canvasWidth': {
            default: 0,
            type: Number,
        },
        'canvasHeight': {
            default: 0,
            type: Number,
        },
    },
    data() {
        return {
            isMobile: !isPC(),
            fullscreenLoading: false,
            submitUrl: '',
            business: this.$route.query.business ? this.$route.query.business : this.initialBusiness,
            isNewLabel: this.$route.query.isNewLabel ? this.$route.query.isNewLabel : 0,   // 标识是不是wap自定义签署位置进入的
            canvasElement: null,
            signaturePad: null,
            lastTouchEnd: 0,
            actNum: 1,
            orientation: '',
            lastOrientation: '',
            canvasW: 0,
            canvasH: 0,
            canvas64: '',
            penColor: '#333',
            isIosWX: false,
            devicePixelRatio: this.initDevicePixelRatio(), /* 获取屏幕是几倍屏，做画板的高清显示用*/
            resultTxt: '', // 签署完后的结果提示文案
            handWritingRecognitionErrorInfo: '', // 笔迹识别报错文案
            isShowTip: true,
            isDisableLeft: true,
            cacheWritingPath: [], // 手写笔迹的路径缓存
            aspectRatio,
            keyIndex: 0, // 点重写后重绘，会导致画笔错乱，加index,强制刷新
            settingDefault: false, // 是否选中设置为默认签名
            hasNoSignatures: false,
            replaceAllSealAndSignature: false,
            signType: this.$route.query.type || 'sign', // send sign approval view
            lang: this.$route.query.lang,
        };
    },
    computed: {
        sensorsEventName() {
            return this.signType === 'sign' ? 'ContractSign' : (this.signType === 'approval' ? 'ContractApproval' : '');
        },
        sensorsPageName() {
            return this.signType === 'sign' ? '合同签署页' : (this.signType === 'approval' ? '合同审批页' : '');
        },
        showReplaceAllBtn() {
            return !!this.parentGift.labelValue || !!this.parentGift.showReplaceAllBtn;
        },
        name() {
            // 兼容‘䶮’
            return decodeURIComponent(this.parentGift.name || '').replace(/[^\u36A2\u4e00-\u9fa5\u4dae\ue863\.\·]/g, '');
        },
        useNewVersion() {
            return this.name.length > 0;
        },
        needRecognizeDotInName() {
            return !!(this.useNewVersion && this.name.match(dotReg));
        },
        isJa() {
            return this.$i18n.locale === 'ja';  // 设置了语言为日文
        },
        isEn() {
            return this.$i18n.locale === 'en'; // 设置了语言为英语
        },
        isRu() {
            return this.$i18n.locale === 'ru'; // 设置了语言为俄语
        },
        drawBoardBg() {
            return this.useNewVersion ? `drawBoard_${this.$i18n.locale}` : `drawBoard drawBoard_${this.$i18n.locale}`;
        },
        isWap() {
            return this.$route.query.from === 'wap';
        },
        drawStyle: function() {
            const style = {};
            const self = this;
            if (this.orientation === 'portrait') {
                if (this.devicePixelRatio) {
                    style.width = `${self.canvasH}px`;
                    style.height = `${self.canvasW}px`;
                }
            } else {
                if (this.devicePixelRatio) {
                    style.width = `${self.canvasW}px`;
                    style.height = `${self.canvasH}px`;
                }
            }

            return style;
        },
        bgStyle() {
            return {
                fontSize: `${this.canvasW / this.name.length * 0.7}px`,
                lineHeight: `${this.drawStyle.height}`,
                ...this.drawStyle,
            };
        },
        charWidth() {
            return this.canvasH * this.aspectRatio;
        },
        // 提交手写签名
        putHandWriteUrl() {
            let url = '';
            switch (this.business) {
                case 'usercenter':
                    /* 这个感觉废掉了，用户中心的是直接连接到这里来的，就是默认的提交方式*/
                    url = `/users/ignore/signatures/handWrite`;
                    break;
                case 'sign-single':
                    /* pc版已经实名过的直接签署*/
                    url = `${signPath}/contracts/${this.parentGift.contractId}/labels/${this.parentGift.labelId}/handWriteSignature`;
                    break;
                case 'sign-all':
                    /* 手机短信的链接直接手机打开的时候签署,或者pc版新注册未实名的用户进来的时候签署*/
                    url = `${signPath}/contracts/${this.parentGift.contractId}/labels/handWriteSignature`;
                    break;
                case 'addSignature':
                    // 签署人自定义签署位置 添加手绘签名时保存为我的签名
                    url = `/users/signatures/qrcode`;
                    break;
                case 'mobileSign':
                    /* 手机扫码签署的时候提交的接口*/
                    url = `${signPath}/ignore/contracts/handWriteSignature/${this.$route.query.token}`;
                    break;
                case 'batchSign':
                    /* 批量签署的时候，如果没有实名的话，会走这个逻辑*/
                    url = `/users/signatures/default/hand`;
                    break;
                case 'sign-demo':
                    url = '';
                    break;
                default:
                    /* 用户中心的设置签名的提交接口*/
                    url = `/users/ignore/signatures/handWrite`;
            }
            return url;
        },
        isShowCancle() {
            return !['sign', 'mobileSign'].includes(this.business) && !this.isEn && !this.isJa;
        },
        canvasWidthWithDevicePixelRatio() {
            if (this.orientation === 'portrait') {
                return this.devicePixelRatio ? this.canvasH * this.devicePixelRatio : this.canvasH;
            } else {
                return this.devicePixelRatio ? this.canvasW * this.devicePixelRatio : this.canvasW;
            }
        },
        canvasHeightWithDevicePixelRatio() {
            if (this.orientation === 'portrait') {
                return this.devicePixelRatio ? this.canvasW * this.devicePixelRatio : this.canvasW;
            } else {
                return this.devicePixelRatio ? this.canvasH * this.devicePixelRatio : this.canvasH;
            }
        },
    },

    methods: {
        /*
         * @method
         *  @param
         * @returns
         * @desc 根据手机设备像素比来判断要几倍图 目前发现华为手机自带浏览器的上传图片太大的话，在网速较慢的情况下会传不上去，所以这里尽量取<3的值
         */
        initDevicePixelRatio() {
            if (isHUAWEI()) {
                return 1;
            }
            if (window.devicePixelRatio >= 2) {
                return 2;
            } else {
                return window.devicePixelRatio;
            }
        },
        goBack() {
            // 以下代码是为了，在wap上点击手写进入签名之后，点击返回或者不写了之后回到签署页
            if (this.isNewLabel === '1') {
                this.$http.delete(`/contract-api/contracts/${this.parentGift.contractId}/labels/${this.parentGift.labelId}`);
            }
        },
        // 初始化 canvas
        initSignaturePad() {
            const canvasElement = document.querySelector('#drawBoard');
            this.canvasElement = canvasElement;
            this.signaturePad = new SignaturePad(canvasElement, {
                throttle: 0,
                velocityFilterWeight: 1,
                minWidth: 2,
                maxWidth: 7,
                penColor: this.penColor,
                gridBox: this.needRecognizeDotInName ? {
                    grid: this.name.length,
                    silenceNums: new Array(this.name.length).fill(null).reduce((arr, char, index) => {
                        if (this.name[index].match(dotReg)) {
                            arr.push(index);
                        }
                        return arr;
                    }, []),
                } : null,
                onBegin: function() {
                    /* 手写的时候把背景字去掉*/
                    document.getElementById('drawBoard').className = '';
                },
                /* dotSize: 2,
                     minWidth: 1.5,
                     maxWidth: 5.5,
                     throttle: 0,
                     minDistance: 0,
                     velocityFilterWeight: 0*/
            });
        },
        /*
         * @method
         *  @param
         * @returns
         * @desc 手机情况下，根据屏幕旋转动态计算画板的宽高
         */
        updateOrientation() {
            const canvas = document.querySelector('#drawBoard');
            if (!canvas) {
                return false;
            }

            // console.log(this.canvas64);
            this.orientation = document.documentElement.clientWidth > document.documentElement.clientHeight ? 'landscape' : 'portrait';
            if (this.lastOrientation === this.orientation) { /* 每次转屏幕会进入resize两次，为了避免重复出现问题，这里控制只进一次*/
                return false;
            }

            const oldH = this.canvasH;
            const oldW = this.canvasW;

            this.$nextTick(function() {
                const self = this;
                setTimeout(() => { // 延迟100毫秒，保证resize结束，获取到新到宽高
                    self.canvas64 = canvas.toDataURL('image/png');
                    if (self.orientation === 'portrait') {
                        // 为了和之前的画布保持一致，横屏的高度就是竖屏的宽度，旋转不会重新计算。但是ios微信需要重新计算下。
                        self.canvasH = oldH > 0 && !self.isIosWX ? oldH : document.getElementById('j_canvas_box').offsetWidth;
                        // 如果使用了底纹样式，那么画板宽度根据名字的长度而定
                        self.canvasW = oldW > 0 && !self.isIosWX ? oldW : (self.useNewVersion
                            ? self.name.length * self.charWidth
                            : document.getElementById('j_canvas_box').offsetHeight);
                        /* 原来的画要跟着旋转*/
                        self.rotateImage(self.canvas64, 90, document.getElementById('drawBoard')).then(function(data) {
                            self.canvas64 = data;
                        });
                    } else {
                        /* 如果是ios微信，因为每次旋转都会重新计算，所以这里把这个地方改大点*/
                        const newHeight = self.isIosWX ? Math.round(window.innerHeight * 0.8) : Math.round(window.innerHeight * 0.70833 * 0.95);
                        const newWidth = document.getElementById('j_landscape_box').offsetWidth;
                        // 为了和之前的画布保持一致，横屏的高度就是竖屏的宽度，旋转不会重新计算。但是ios微信需要重新计算下。
                        self.canvasH = oldH > 0 && !self.isIosWX ? oldH : newHeight;
                        // 如果使用了底纹样式，那么画板宽度根据名字的长度而定
                        self.canvasW = oldW > 0 && !self.isIosWX ? oldW : (self.useNewVersion
                            ? self.name.length * self.charWidth
                            : newWidth);
                        /* 原来的画要跟着旋转*/
                        self.rotateImage(self.canvas64, -90, document.getElementById('drawBoard')).then(function(data) {
                            self.canvas64 = data;
                        });
                    }
                    this.$nextTick(function() {
                        /* 一定要等新的canvas渲染完了才能初始化*/
                        self.initSignaturePad();
                    });
                }, 100);
                if (this.lastOrientation) {
                    /* 存在则代表旋转过一次了，则需要把画板背景去掉*/
                    this.$nextTick(function() {
                        document.getElementById('drawBoard').className = '';
                    });
                }
                self.lastOrientation = self.orientation;
            });
        },
        isWeiXin() {
            var ua = window.navigator.userAgent.toLowerCase();
            if (ua.match(/MicroMessenger/i) === 'micromessenger') {
                return true;
            } else {
                return false;
            }
        },
        /*
         * @method ios的新版微信下面多了一个bar，旋转的时候需要重新计算画布的宽高
         * @param
         * @returns 判断是否是ios的微信
         */
        isIosWXFn() {
            var u = navigator.userAgent;
            var isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
            return isIOS && this.isWeiXin();
        },
        /**
         *
         * @param {参数类型} 参数名 参数说明
         * @return {返回值类型} 返回值说明
         * @desc 浏览器关闭画板
         */
        CloseWebPage() {
            if (navigator.userAgent.indexOf('MSIE') > 0) {  // close IE
                if (navigator.userAgent.indexOf('MSIE 6.0') > 0) {
                    window.opener = null;
                    window.close();
                } else {
                    window.open('', '_top');
                    window.top.close();
                }
            } else if (navigator.userAgent.indexOf('Firefox') > 0) { // close firefox
                window.location.href = 'about:blank ';
            } else { // close chrome;It is effective when it is only one
                window.opener = null;
                window.open('', '_self', '');
                window.close();
            }
            window.history.back(); // 部分手机
        },
        // 返回笔画数、笔画最长的点数
        formatStroke() {
            const stroke = this.signaturePad.toData();
            let pointSum = 0;
            stroke.forEach((item) => {
                pointSum += item.points.length;
            });
            return pointSum;
        },

        // 将 data 转为 [x, y, x, y] 的格式
        formatPositionCount() {
            const arr = [];
            const paths = [];
            const stroke = this.signaturePad.toData();

            if (stroke.length) {
                stroke.forEach(path => {
                    paths.push(...path.points);
                });

                paths.forEach(point => {
                    arr.push(point.x, point.y);
                });
            }

            return arr;
        },
        // 手迹识别数据格式化[[x, y, x, y], [x, y, x, y]]
        formatPositionCountForHandWritingRecognition() {
            const canvas = document.querySelector('#drawBoard');
            const arr = [...this.cacheWritingPath];
            const stroke = this.signaturePad.toData();
            // console.log(stroke);
            if (stroke.length) {
                stroke.forEach(path => {
                    const paths = [];
                    path.points.forEach(point => {
                        const { x, y } = point;
                        if (this.orientation === 'portrait') {
                            paths.push(y, canvas.clientWidth - x);
                        } else {
                            paths.push(x, y);
                        }
                    });
                    arr.push(paths);
                });
            }
            return arr;
        },
        /*
         * @method
         * @param {canvas64} canvas画布画好的base64图片
         * @returns
         * @desc 发请求提交手写签名到服务端，不存在putHandWriteUrl return给组件处理
         */
        uploadDrawImage(canvas64) {
            this.$store.commit('setSignImageBase64', canvas64);
            if (!this.putHandWriteUrl) {
                this.$emit('write-done', canvas64);
                return;
            }
            if (this.parentGift.handWritingRecognition && this.name) {
                // 需要识别笔迹
                this.handWritingRecognitionAjax(canvas64);
            } else {
                this.postHandWriteAjax(canvas64);
            }
        },
        // 提交手写签名ajax
        async postHandWriteAjax(canvas64, isSameName) {
            const _this = this;
            const submitData = _this.getSubmitData(canvas64);
            if (!submitData) {
                return;
            }// 未获取到数据

            this.fullscreenLoading = true;
            try {
                const res = await this.$http.post(this.putHandWriteUrl, {
                    ...submitData,
                    isSameName,
                    isDefault: this.settingDefault,
                });
                // 保存上传签名
                if (this.business !== 'mobileSign' && this.settingDefault) {
                    this.$http.post(`/users/signatures/qrcode`, {
                        file: submitData.file,
                        sigId: '',
                        setDefaultSig: this.settingDefault,
                        ifFromContract: true,
                        ...(isSameName && { isSameName }),
                    }).then(() => {
                        _this.sensorsEventName && _this.$sensors.track({
                            eventName: `Ent_${_this.sensorsEventName}Window_Result`,
                            eventProperty: {
                                page_name: `${_this.sensorsPageName}`,
                                window_name: '手写签名',
                                is_success: true,
                                icon_name: '使用',
                                request_url: _this.putHandWriteUrl,
                                contract_id: _this.parentGift.contractId,
                            },
                        });
                    })
                        .catch((err) => {
                            const errMsg = err.response?.data?.message;
                            const status = err.response?.status;
                            const code = err.response?.data?.code;
                            _this.sensorsEventName && _this.$sensors.track({
                                eventName: `Ent_${_this.sensorsEventName}Window_Result`,
                                eventProperty: {
                                    page_name: `${_this.sensorsPageName}`,
                                    window_name: '手写签名',
                                    is_success: false,
                                    icon_name: '使用',
                                    fail_http_code: status,
                                    fail_error_code: code,
                                    fail_reason: errMsg,
                                    request_url: _this.putHandWriteUrl,
                                    contract_id: _this.parentGift.contractId,
                                },
                            });
                        });
                }

                // 自定义签署位置 手绘签名后增加到我的签名列表
                if (this.business === 'addSignature') {
                    Bus.$emit('save-signature');
                } else {
                    _this.$MessageToast({
                        message: this.$t('handwrite.submitTip'),
                        type: 'success',
                    });
                }
                if (this.replaceAllSealAndSignature) {
                    if (this.business === 'mobileSign') {
                        await syncAllSignLabelIgnore({ token: this.$route.query.token });
                    } else {
                        await syncAllSignLabel({ contractId: this.parentGift.contractId, labelId: this.parentGift.labelId });
                    }
                }

                this.$emit('write-done', {
                    'imageData': submitData.file,
                    'resData': res.data,
                });
                Bus.$emit('write-done', {
                    'imageData': submitData.file,
                    'resData': res.data,
                });
                _this.fullscreenLoading = false;
                document.body.style.overflow = '';
                document.body.style.position = '';

                this.closeDraw();
            } catch (err) {
                _this.fullscreenLoading = false;
                document.body.style.overflow = '';
                document.body.style.position = '';
            }
        },
        // 笔迹识别ajax
        async handWritingRecognitionAjax(canvas64) {
            const submitData = this.formatPositionCountForHandWritingRecognition();
            if (!submitData) {
                return;
            }// 未获取到数据
            const imageData = canvas64.split(',')[1];
            const handWritingRecognitionDataList = this.useNewVersion ? await this.positionAndBase64Divide(submitData, canvas64) : [{
                positions: submitData,
                signatureImageData: imageData,
            }];
            if (!imageData) {
                this.$MessageToast.error(this.$t('handwrite.msg.cantGet'));
                return false;
            }
            this.fullscreenLoading = true;
            this.$http.post(
                `${signPath}/ignore/contracts/${this.parentGift.contractId}/receiver/${this.parentGift.receiverId}/hand-writing-recognition`,
                {
                    checkIfShading: this.useNewVersion,
                    handWritingRecognitionDataList,
                    // HandWritingRecognitionVO: {},
                })
                .then(async res => {
                    this.fullscreenLoading = false;
                    if (res.data && res.data.isPass) { // 识别成功
                        // 如果是有手写版底纹，并且名字中带 点 （新疆等少数名），则提示用户确认
                        if (this.name.match(/[\·\.]/) && this.useNewVersion) {
                            await this.$alert(`您的手写签名识别为"${this.name}"，请确认`);
                        }
                        this.postHandWriteAjax(canvas64, true);
                    } else { // 识别失败
                        if (res.data.mismatchingPositions) {
                            // 开了精准识别
                            this.handWritingRecognitionErrorInfo = this.$t('handwrite.signNotMatchExact', { numList: res.data.mismatchingPositions.map(i => `${i + 1}(${this.name[i]})`).join(',') });
                        } else {
                            // 未开精准识别
                            this.handWritingRecognitionErrorInfo = this.$t('handwrite.signNotMatch');
                        }
                        this.$MessageToast.error(this.handWritingRecognitionErrorInfo);
                        this.repaint();
                    }
                })
                .finally(() => {
                    this.fullscreenLoading = false;
                });
        },
        /**
         *
         * @param {参数类型} 参数名 参数说明
         * @return {返回值类型} 返回值说明
         * @desc 点击按钮"不写了"的时候执行这个，如果是扫码进来的话则是关闭，其他的如果是在弹窗里面的话则发消息给父组件关闭弹窗
         */
        closeDraw(method) {
            this.handleClickEventTrack(3);
            // 只有wap端需要跳转, wap端this.business恒为'sign-all'
            if (this.isWap) {
                /* 为了兼容有些手机$router.go(-1)不起作用，所以这样。。。*/
                if (window.localStorage && window.localStorage.getItem('transitionHref')) {
                    const transitionHref = window.localStorage.getItem('transitionHref');
                    this.$router.push(transitionHref);
                    // 以下代码是为了，在wap上点击手写进入签名之后，点击返回或者不写了之后回到签署页 删除默认加入的签名标签
                    if (method === 'click' && this.isNewLabel === '1') {
                        this.$http.delete(`/contract-api/contracts/${this.parentGift.contractId}/labels/${this.parentGift.labelId}`);
                    }
                } else {
                    /* 因为如果是手机签署页过来的时候，刷新的时候并没有走一次router的next渲染，所以这只需要go-1就ok*/
                    this.$router.go(-1);
                }
                return;
            } else if (this.business.match(/sign-all|sign-single|batchSign|addSignature/i)) {
                Bus.$emit('close-handwrite');
                return;
            } else if (this.business.match(/sign-demo/i)) {
                return this.$emit('close-handwrite');
            }
            if (['sign', 'mobileSign'].includes(this.business)) { // 用户中心修改签名以及扫码签名结束后展示结果文案
                this.resultTxt = this.business === 'sign' ? this.$t('handwrite.msg.successToUser') : this.$t('handwrite.msg.successToSign');
            }
            // if (this.isWeiXin()) {
            //     WeixinJSBridge.call('closeWindow');
            // } else {
            //     this.CloseWebPage();
            // }
        },
        /* 画布重绘*/
        repaint() {
            this.handleClickEventTrack(2);
            this.signaturePad.clear();
            this.isDisableLeft = true;
            this.clearCacheWritingPath();
            // const canvas = document.querySelector('#drawBoard');
            // const context = canvas.getContext('2d');
            // context.clearRect(0, 0, canvas.width, canvas.height);
            // Draw._positionCount = [];//重绘时统计数组清零
            if (this.canvasW < 1000) {
                // 如果画板没有被加长过，则不需要走下面的逻辑
                return;
            }
            if (this.useNewVersion) {
                if (this.orientation === 'portrait') {
                    document.querySelector('.draw_scroll_box').scrollTop = 0;
                } else {
                    document.querySelector('.draw_scroll_box').scrollLeft = 0;
                }
                return;
            }
            this.keyIndex++; // 重绘时,index++，强制刷新
            const myCanvas = document.querySelector('#drawBoard');
            if (this.orientation === 'portrait') {
                document.querySelector('.draw_scroll_box').scrollTop = 0;
                this.canvasW = document.getElementById('j_canvas_box').offsetHeight;
            } else {
                document.querySelector('.draw_scroll_box').scrollLeft = 0;
                this.canvasW = document.getElementById('j_landscape_box').offsetWidth;
            }
            const img = new Image();
            this.$nextTick(function() {
                /* 一定要等新的canvas渲染完了才能初始化*/
                this.initSignaturePad();
                setTimeout(() => {
                    // 如果以前画了，这时候需要画一张空白的
                    myCanvas.getContext('2d').drawImage(img, 0, 0);
                }, 10);
            });
        },
        /**
         *
         * @param {canvas} canvas的dom节点
         * @return {bool} 是否是空的canvas
         * @desc 判断是否是空的canvas
         */
        isCanvasBlank(canvas) {
            var blank = document.createElement('canvas');
            blank.width = canvas.width;
            blank.height = canvas.height;

            return canvas.toDataURL() === blank.toDataURL();
        },
        /*
         * @method
         * @param {canvas64} canvas画布画好的base64图片
         * @returns {imageData} 请求保存签名的时候发送给服务端的数据
         * @desc 获取保存签名的时候发送给服务端的数据
         */
        getSubmitData(canvas64) {
            const imageData = canvas64.split(',')[1];

            if (!imageData) {
                this.$MessageToast.error(this.$t('handwrite.msg.cantGet'));
                return false;
            }
            const token = this.$route.query.token || '';

            let submitData;
            switch (this.business) {
                case 'batchSign':
                    submitData = {
                        file: imageData,
                    };
                    break;
                case 'sign-single':
                case 'mobileSign':
                    submitData = {
                        file: imageData,  //  签名数据  string
                        token: token, //  二维码中获取的token  string
                    };
                    break;
                case 'sign-all':
                    submitData = {
                        labelId: this.parentGift.labelId,
                        file: imageData,  //  签名数据  string
                        token: token, //  二维码中获取的token  string
                    };
                    break;
                case 'addSignature':
                    submitData = {
                        file: imageData,  //  签名数据  string
                        sigId: '',
                    };
                    break;
                default:
                    submitData = {
                        data: this.formatPositionCount(),
                        file: imageData,  //  签名数据  string
                        token: token, //  二维码中获取的token  string
                    };
            }
            return submitData;
        },
        /*
         * @method 确认使用按钮的响应事件
         */
        submitSign() {
            this.handleClickEventTrack(1);
            const canvas = document.querySelector('#drawBoard');
            if (!!canvas && this.isCanvasBlank(canvas)) {
                this.$MessageToast.error(this.$t('handwrite.needRewrite'));
                return false;
            }
            // 未开启手写笔迹识别时，判断最长的路径点小于 10 则提示
            const pointSum = this.formatStroke();
            if (!this.parentGift.handWritingRecognition && pointSum < 10) {
                this.$MessageToast.error(this.$t('handwrite.needRewrite'));
                return false;
            }

            // 因为涉及到旋转的时候的重绘，不能再用这个方法判断是否是空没填了
            /* let clearedWriting = this.formatStroke();

                 /!* 判断不为空笔画数小于2，并且记录的最长的路径点小于 7 则提示 *!/
                 if (clearedWriting.length < 2 && clearedWriting.maxLength < 7) {
                 this.$MessageToast.error('请手绘正确的姓名！');
                 return false;
                 }*/

            this.canvas64 = canvas.toDataURL('image/png');
            const self = this;
            if (self.orientation === 'portrait') {
                /* 如果是竖屏，则需要将图片90°旋转之后再提交*/
                self.rotateImage(this.canvas64, -90).then(function(data) {
                    self.uploadDrawImage(data);
                });
            } else {
                self.uploadDrawImage(self.canvas64);
            }
        },
        /*
         * @method
         * @param {url,degrees,canvasEl}
         * @returns Promise对象 异步处理图片旋转
         * @desc 根据图片和需要的旋转角度进行旋转
         */
        rotateImage(url, degrees, canvasEl = null) {
            const self = this;
            return new Promise(function(resolve) {
                const image = new Image();

                image.onload = function() {
                    const data = self.rotate(image, degrees, canvasEl);
                    resolve(data);
                };

                /* image.onerror = function() {
                     reject();
                     };*/

                image.src = url;
            });
        },
        /*
         * @method
         * @param {image,degrees,canvasEl}
         * @returns 图片旋转后的base64数据
         * @desc 根据图片和需要的旋转角度进行旋转
         */
        rotate(image, degrees, canvasEl) {
            var _this = this;
            var w = image.naturalWidth;
            var h = image.naturalHeight;
            var canvasWidth = Math.max(w, h);
            var cvs = _this._getCanvas(canvasWidth, canvasWidth);
            var ctx = cvs.getContext('2d');
            ctx.translate(canvasWidth / 2, canvasWidth / 2);
            ctx.rotate(degrees * (Math.PI / 180));
            var x = -canvasWidth / 2;
            var y = -canvasWidth / 2;
            degrees = degrees % 360;
            if (degrees === 0) {
                // eslint-disable-next-line no-undef
                return callback(src, w, h);
            }
            if ((degrees % 180) !== 0) {
                if (degrees === -90 || degrees === 270) {
                    x = -w + canvasWidth / 2;
                } else {
                    y = canvasWidth / 2 - h;
                }
                const c = w;
                w = h;
                h = c;
            } else {
                x = canvasWidth / 2 - w;
                y = canvasWidth / 2 - h;
            }
            ctx.drawImage(image, x, y);
            // var cvs2 = _this._getCanvas(w, h);
            var cvs2 = !canvasEl ? _this._getCanvas(w, h) : canvasEl;
            var ctx2 = cvs2.getContext('2d');
            if (canvasEl) {
                /* 如果是提交前旋转的话是画板是真实图大小的1/放大倍率*/
                ctx2.drawImage(cvs, 0, 0, w, h, 0, 0, w / _this.devicePixelRatio, h / _this.devicePixelRatio);
            } else {
                /* 如果是要提交了，这里的画布的高宽和实际图片是同等高宽的*/
                ctx2.drawImage(cvs, 0, 0, w, h, 0, 0, w, h);
            }

            /* var mimeType = _this._getImageType(image.src);
                 var data = cvs2.toDataURL(mimeType, 1);*/
            const data = cvs2.toDataURL('image/png');
            return data;
        },
        /**
         *
         * @param {参数类型} 参数名 参数说明
         * @return {返回值类型} 返回值说明
         * @desc 根据高宽得到画板dom
         */
        _getCanvas(width, height) {
            var canvas = document.createElement('canvas');
            canvas.width = width;
            canvas.height = height;
            return canvas;
        },
        /* 根据颜色设置画板画笔颜色*/
        selectColor(actNum) {
            this.actNum = actNum;
            if (+this.actNum === 3) {
                this.penColor = '#cb0404';
            } else if (+this.actNum === 2) {
                this.penColor = '#127fd2';
            } else {
                this.penColor = '#333';
            }
            this.signaturePad.setPenColor(this.penColor);
        },
        move(type) {
            this.sensorsEventName && this.$sensors.track({
                eventName: `Ent_${this.sensorsEventName}Window_BtnClick`,
                eventProperty: {
                    page_name: `${this.sensorsPageName}`,
                    window_name: '手写签名',
                    icon_name: type === 'left' ? '左移' : '右移',
                    contract_id: this.parentGift.contractId,
                },
            });
            // 小于一屏的时候，点右箭头延长画板
            if (this.canvasW < 1000 && type === 'right' && !this.useNewVersion) {
                const myCanvas = document.querySelector('#drawBoard');
                this.canvas64 = myCanvas.toDataURL('image/png');
                this.canvasW += 750;
                const img = new Image();
                img.src = this.canvas64;

                // 加长面板前先换成当前的笔迹
                this.cacheWritingPath = this.formatPositionCountForHandWritingRecognition();

                //  必须等加长画板渲染完了才能把图画上去，要不然可能就是空的。
                this.$nextTick(function() {
                    this.initSignaturePad();

                    setTimeout(() => {
                        myCanvas.getContext('2d').drawImage(img, 0, 0, img.naturalWidth, img.naturalHeight, 0, 0, img.naturalWidth / this.devicePixelRatio, img.naturalHeight / this.devicePixelRatio);
                    }, 10);
                });
            }
            // 画板延长之后渲染需要时间，所以滚动需要延迟一会才能操作
            setTimeout(() => {
                if (this.orientation === 'portrait') {
                    if (type === 'right') {
                        this.isShowTip = false;
                        document.querySelector('.draw_scroll_box').scrollTop += document.body.clientHeight / this.devicePixelRatio;
                    } else {
                        document.querySelector('.draw_scroll_box').scrollTop -= document.body.clientHeight / this.devicePixelRatio;
                    }
                    if (+document.querySelector('.draw_scroll_box').scrollTop === 0) {
                        this.isDisableLeft = true;
                    } else {
                        this.isDisableLeft = false;
                    }
                } else {
                    if (type === 'right') {
                        this.isShowTip = false;
                        document.querySelector('.draw_scroll_box').scrollLeft += document.body.clientHeight / this.devicePixelRatio;
                    } else {
                        document.querySelector('.draw_scroll_box').scrollLeft -= document.body.clientHeight / this.devicePixelRatio;
                    }
                    if (+document.querySelector('.draw_scroll_box').scrollLeft === 0) {
                        this.isDisableLeft = true;
                    } else {
                        this.isDisableLeft = false;
                    }
                }
            }, 50);
        },
        // 清除手写笔迹的路径缓存
        clearCacheWritingPath() {
            this.cacheWritingPath = [];
        },
        async positionAndBase64Divide(positions, base64) {
            const canvas = document.querySelector('#drawBoard');
            const width = this.orientation === 'portrait' ? canvas.clientHeight : canvas.clientWidth;
            const height = this.orientation === 'portrait' ? canvas.clientWidth : canvas.clientHeight;
            let result = null;
            if (this.useNewVersion && this.name) {
                const len = this.name.length;
                const positionsGroup = positionsDivide(positions, width, height, len);
                const imageGroup = await base64ImageDivide(base64, len);
                result = new Array(len).fill(null).map((d, index) => ({
                    positions: positionsGroup[index],
                    signatureImageData: imageGroup[index].split(',')[1],
                })).filter((item, index) => {
                    return !dotReg.test(this.name[index]);
                });
            }
            return result;
        },
        isDotChar(char) {
            return !!char.match(dotReg);
        },
        handleClickEventTrack(index) {
            const eventMap = {
                1: '使用',
                2: '重写',
                3: '不写了',
            };
            this.sensorsEventName && this.$sensors.track({
                eventName: `Ent_${this.sensorsEventName}Window_BtnClick`,
                eventProperty: {
                    page_name: `${this.sensorsPageName}`,
                    window_name: '手写签名',
                    icon_name: eventMap[index],
                    contract_id: this.parentGift.contractId,
                },
            });
        },
    },
    mounted() {
        const _this = this;

        if (isIOS && IOSVersion >= 13) {
            document.body.classList.add('HandPainted-IOS13');
        }
        document.body.style.backgroundColor = '#F6FAFD';
        if (!!this.canvasWidth && !!this.canvasHeight) {
            _this.canvasH = _this.canvasHeight;
            _this.canvasW = (_this.useNewVersion
                ? _this.name.length * _this.charWidth
                : _this.canvasWidth);
            this.$nextTick(function() {
                this.initSignaturePad();
            });
        } else {
            _this.updateOrientation();
            window.addEventListener('resize', _this.updateOrientation);
        }
        // 初始化的时候判断好，后面旋转就不用重复判断了
        this.isIosWX = this.isIosWXFn();
        if (this.isWap) {
            if (window.history && window.history.pushState) {
                window.addEventListener('popstate', this.goBack, false);
            }
        }
        if (this.lang) {
            const lang = this.lang.toLowerCase();
            this.$i18n.locale = lang;
            this.$cookie.set('language', lang);
            loadLanguageAsync({ language: lang });
        }
    },
    destroyed() {
        if (this.isWap) {
            window.removeEventListener('popstate', this.goBack, false);
        }
        if (document.body.classList.value.includes('HandPainted-IOS13')) {
            document.body.classList.remove('HandPainted-IOS13');
        }
    },
};
</script>
<style lang="scss">
    body.HandPainted-IOS13 {
        width: 100%;
        height: 100%;
        //设置不可滚动
        overflow: hidden;
        //设置相对浏览器定位，解决此bug
        position:fixed;
        //解决卡顿问题
        -webkit-overflow-scrolling:touch;
        // background-color: #F6FAFD;
    }
    .draw-tooltip {
        width: 224px;
    }
</style>
<style lang="scss" scoped>
    @import './HandPainted.scss';
</style>
