@function pxTorem($px) {
  @return $px / 75 * 1rem;
}
#hand-painted{
	//background-color: #F6FAFD;
    overflow:hidden;
	-webkit-touch-callout: none; /*系统默认菜单被禁用*/
	-webkit-user-select: none; /*webkit浏览器*/
	-moz-user-select: none;/*火狐*/
	-ms-user-select: none; /*IE10*/
	user-select: none;
	.col-draw{
		height:100%;
	}
    .draw_scroll_box{
        height:100%;
        overflow:auto;
    }
    .draw_footer{
        width: 96%;
        margin: 0 auto;
		.draw-setting {
			height: 40px;
			line-height: 20px;
            .el-checkbox, .el-icon-ssq-icon-bs-bangzhuzhongxin {
                color: #999;
            }
            .el-icon-ssq-icon-bs-bangzhuzhongxin:hover {
                cursor: pointer;
            }
        }
    }
	.title{
		text-align: center;
		color: #333;
		font-size: 15px;
		height: 50px;
		line-height: 50px;
	}

	.drawSign{
		height: 100vh;
		width: 100%;
		overflow: hidden;

	}
	.row-sign{
		min-height: 100vh;
		width: 100%;
		overflow: auto;
		background-color: #f6f6f6;
	}
	.sign-title{
		transform: rotate(90deg);
		-ms-transform:rotate(90deg); /* Internet Explorer */
		-moz-transform:rotate(90deg); /* Firefox */
		-webkit-transform:rotate(90deg); /* Safari 和 Chrome */
		-o-transform:rotate(90deg); /* Opera */
		width: 100vh;
		height: 100%;
		position: relative;
		background-color: #fff;
	}
	.rotate_title{
		width: 100%;
		position: absolute;
		left: 0;
		bottom: 0;
	}
	.footer-box{
		overflow: hidden;
	}
	.sign-footer{
		transform: rotate(90deg);
		-ms-transform:rotate(90deg); /* Internet Explorer */
		-moz-transform:rotate(90deg); /* Firefox */
		-webkit-transform:rotate(90deg); /* Safari 和 Chrome */
		-o-transform:rotate(90deg); /* Opera */
		width: 100vh;
		height: 100%;
		position: relative;
	}
	.rotate_footer{
		position: absolute;
		left: 0;
		bottom: -4px;
		width: 100%;
		height: 60px;
		padding: 0 15px;
	}
	.canvas_box{
		width: 95%;
		min-height: 96%;
		background-color: #f6f6f6;
		border-radius: 2px;
		margin-top: 5%;
		margin-left: 5px;
	}
	.drawBoard{
		background-size: 51px 219px;
		background: #fff url('../images/drawBkg1.png') no-repeat  center;
		&_en {
			background-image: url('../images/draw-bg-en-2.png');
			background-size: 32px 219px;
			background-repeat: no-repeat;
			background-position: center;
		}
		&_ru {
			background-image: url('../images/draw-bg-ru-2.png');
			background-repeat: no-repeat;
			background-position: center;
		}
		&_ja {
			background-image: url('../images/draw-bg-ja-2.png');
			background-repeat: no-repeat;
			background-position: center;
		}
		&_ar {
			background-image: url('../images/draw-bg-ar-01.png');
			background-size: 32px 219px;
			background-repeat: no-repeat;
			background-position: center;
		}
	}
	.landscape_box{
		position: relative;
		width: 96%;
		margin: 0 auto;
		.drawBoard{
			background-size:219px 51px;
			background-image: url('../images/drawBkg2.png');
			&_en {
				background-image: url('../images/draw-bg-en.png');
				background-size:219px 32px;
			}
			&_ru {
				background-image: url('../images/draw-bg-ru.png');
			}
			&_ja {
				background-image: url('../images/draw-bg-ja.png');
			}
			&_ar {
				background-image: url('../images/draw-bg-ar-02.png');
			}
		}
	}
	.draw_footer{
		margin-top: 10px;
		[dir=rtl] & {
			display: flex;
			align-items: center;
			.draw_footer_center {
				width: 42%;
				.el-button {
					float: left;
					&:first-child {
						margin-right: 5px;
					}
				}
			}
			.draw_footer_right {
				width: 23%;
				padding-right: 10px;
			}
		}
	}
	.draw_footer_left{
		text-align: left;
		[dir=rtl] & {
			text-align: right;
		}
	}
	.draw_footer_right{
		text-align: left;
		overflow: hidden;

        .scrollLeft-btn{
            margin-right: 10px;
        }

		[dir=rtl] & {
			text-align: right;
			.scrollLeft-btn{
				margin-right: 0;
				margin-left: 10px;
			}
		}
	}
	.canvas_box-center {
		position: relative;
		margin: auto;
	}
	#j_canvas_box {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		flex-shrink: 0;
		margin-bottom: 110px;
	}
	#drawBoard{
        //background-color: #fff;
		position: relative;
		margin: 8px auto;
		display: block;
        user-select: none;

	}
	.drawBoard{
		margin: 8px auto 4px;
	}
	.drawBoard-char__bg--land, .drawBoard-char__bg {
		position: absolute;
		top: 0;
		left: 0;
		pointer-events: none;
		background-color: #e6f4ff;
		background-image: url('../images/charBg_land.png');
		background-size: contain;
		white-space: nowrap;
	}
	.drawBoard-char__bg {
		margin: 8px 0;
		transform: rotate(90deg);
	}
	.drawBoard-char__bg-char {
		display: inline-block;
		text-align: center;
		color: rgba(180, 180, 180, 0.5);
		font-family: KaiTi STKaiti,SimSong,serif;
		-webkit-touch-callout: none; /*系统默认菜单被禁用*/
		-webkit-user-select: none; /*webkit浏览器*/
		-moz-user-select: none;/*火狐*/
		-ms-user-select: none; /*IE10*/
		user-select: none;
		&.canvas-box__charBg-char--dot {
			background-color: #f6f6f6;
			color: transparent;
		}
	}
	.finish{
		text-align: center;
		margin-top: 100px;
		color: red;
		font-size: 14px;
	}
	.reButton{
        background-color: #F6FAFD;
        border-radius: 0;
		// border-right: 1px solid #b6b6b6;
		padding-right: 20px;
		[dir=rtl] & {
			padding-left: 10px;
			padding-right: 0;
		}
	}
    .useBtn{
		padding: 10px 30px;
	}
	.colorBtn{
		width: 26px;
		height: 26px;
		border-radius: 13px;
		margin-left: 15px;
		padding: 7px 0;
		outline: 0 none;
		display: inline-block;
	}
	.blackBtn{
		background: #333;
	}
	.blueBtn{
		background: #127fd2;
	}
	.redBtn{
		background: #cb0404;
	}
	.reButton:hover, .reButton:focus, .reButton:active{
		background-color: #F6FAFD;
		border-color: #f6f6f6;
		// border-right: 1px solid #b6b6b6;
	}
	.activeBtn{
		border: 3px solid #cfcfcf;
		width: 30px;
		height: 30px;
		border-radius: 15px;
	}
}
#hand-painted.fullScreen {
	.bottom {
		bottom: 0;
	}
}
#hand-painted.noFullScreen {
	background-color: #f2f2f2;
	.blank-section {
		width: 100%;
		height: 25px;
		background-color: #f2f2f2;
		border-bottom: 1px solid #ddd;
	}
	.bottom {
		margin-top: -2px;
		border-bottom: 1px solid #ddd;
	}
}
#hand-painted.vertical {
	position: absolute;
	.title, .bottom {
		position: absolute;
	}

    /*去掉overflow 微信显示正常，但是浏览器有问题，竖屏时强制横屏缩小*/
    overflow: hidden;
    .title {
		-webkit-transform: rotate(90deg);
		-ms-transform: rotate(90deg);
		transform: rotate(90deg);
		-ms-transform-origin: left top;
		transform-origin: left top;
		-webkit-transform-origin: left top;
    }
    .bottom {
		-webkit-transform: rotate(90deg);
		-ms-transform: rotate(90deg);
		transform: rotate(90deg);
		-ms-transform-origin: left bottom;
		transform-origin: left bottom;
		-webkit-transform-origin: left bottom;
    }
}

#hand-painted{
    user-select: none;
    .result-page{
        padding-top:100px;
        color:red;
        text-align: center;
        font-size: 14px;
    }
	.draw_footer_center{
		position: relative;
		.draw-tip{
			color:#999;
			font-size: 12px;
			position: absolute;
			display: contents;
			white-space: nowrap;
			[dir=rtl] & {
				display: inline-block;
				line-height: 32px;
			}
		}
		.draw-btn {
			margin-left: 10px;
			color:#999;
			font-size: 12px;
		}
	}
}

.ar-page{
	.drawSign {
		direction:ltr;
		.draw_footer_left {
			direction:rtl;
			float: right;
			text-align: left !important;
		}
	}
}