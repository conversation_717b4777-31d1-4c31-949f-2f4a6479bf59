<template>
    <el-dialog
        class="confirm-cpn"
        :visible.sync="dialogVisible"
        :modal-append-to-body="true"
        :before-close="handleClose"
    >
        <p>如果您继续使用<strong> {{ account }} </strong>签署，</p>
        <p>需要驳回当前<strong> {{ fullName }} </strong>的实名,</p>
        <p>然后以<strong> {{ entName }} </strong>要求的<strong> {{ signerName }} </strong>重新实名，</p>
        <p>是否继续？</p>
        <div slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">{{ $t('sign.cancel') }}</el-button>
            <el-button @click="confirmHandle" type="primary">{{ $t('personAuthIntercept.confirmReject') }}</el-button>
        </div>
    </el-dialog>
</template>

<script>
import { mapState } from 'vuex';
import { getMaskName, getMaskPhoneOrMail } from 'src/common/utils/reg';

export default {
    name: 'ReAuthDialog',
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        isIntercept: { // 是否是拦截页过来：数据源不一样
            type: Boolean,
            default: false,
        },
        infoData: {
            type: Object,
            default: () => ({
                inputUserName: '',
                letterSender: '',
            }),
        },
    },
    data() {
        return {
            signerName: '',
            entName: '',
        };
    },
    computed: {
        ...mapState(['commonHeaderInfo']),
        account() {
            const account = this.commonHeaderInfo.platformUser.account;
            return getMaskPhoneOrMail(account);
        },
        fullName() {
            return getMaskName(this.commonHeaderInfo.platformUser.fullName);
        },
        dialogVisible: {
            set(value) {
                this.$emit('update:visible', value);
            },
            get() {
                return this.visible;
            },
        },
    },
    watch: {
        'infoData': {
            handler() {
                this.initPageData();
            },
            deep: true,
            immediate: true,
        },
    },
    methods: {
        // 初始化页面数据
        initPageData() {
            try {
                // console.log(this.isIntercept);
                if (this.isIntercept) {
                    const params = JSON.parse(sessionStorage.getItem('personAuthIntercept'));
                    if (params) {
                        this.signerName = params.inputUserName ? getMaskName(params.inputUserName) : '';
                        this.entName = params.letterSender;
                    }
                } else {
                    this.signerName = this.infoData.inputUserName ? getMaskName(this.infoData.inputUserName) : '';
                    this.entName = this.infoData.letterSender;
                }
            } catch (e) {
                // console.log(e);
            }
        },
        handleClose() {
            this.$emit('update:visible', false);
        },
        confirmHandle() {
            this.dialogVisible = false;
            this.$emit('confirm');
        },
    },
    created() {
        this.initPageData();
    },
};
</script>

<style lang="scss">
    .confirm-cpn {
        width: 50%;
        margin: 0 auto;
        padding: 15px;
        .confirm-content {
            padding: 15px;
        }
        p{
            padding: 0 25px;
            margin-top: 8px !important;
        }
    }

</style>
