<!--
签署页面组件：实名冲突，前置弹窗引导
wiki: http://wiki.bestsign.tech/pages/viewpage.action?pageId=14290610
-->
<template>
    <el-dialog v-model="moduleShow"
        class="dialog__ForceGuide"
        :title="$t('sign.prompt')"
        :close-on-click-modal="false"
        :show-close="false"
        :append-to-body="true"
    >
        <!-- 个人如果已实名，名字冲突 -->
        <template v-if="receiver.hasAuthenticated && !isEnt">
            <!-- <p>{{ senderName }}要求您<label v-html="personText"></label>进行查看和签署合同。这和您实名的身份信息不符。请您自行联系发起方确认身份信息，并要求再次发起合同。</p> -->
            <p v-html="$t('sign.authTip.viewAndSignConflict', {text: personText, x: senderName})">></p>
            <p>{{ noteText }}</p>
            <div class="dialog__ForceGuide--btn" v-if="isPC">
                <el-button type="primary" @click="goToHome" class="el-button--center">{{ $t('sign.authGuide.goToHome') }}</el-button>
            </div>
        </template>
        <!-- 未实名，或者企业可以新建一个企业引导去用发件人指定的名字实名 -->
        <template v-else-if="!askSignVis">
            <!--
                <p v-if="isEnt">{{ senderName }}要求您以 <span>{{ inputName }}</span> 的身份进行实名认证，完成认证后即可查看和签署合同。</p>
                <p v-else>{{ senderName }}要求您<label v-html="personText"></label>进行实名认证，完成认证后即可查看和签署合同。</p>
            -->
            <p v-html="$t('sign.authTip.t3', {text: isEnt ? inputName : personText, x: senderName}) + $t('sign.authTip.viewAndSign1')"></p>
            <p>{{ noteText }}</p>
            <div class="dialog__ForceGuide--btn">
                <el-button type="primary" @click="goToAuth" class="el-button--center">{{ $t('sign.goToVerify') }}</el-button>
                <el-button
                    v-if="receiver.signDecision !== 'MORE_AUTHENTICATE_WITH_PROXY_AUTH' && isEnt"
                    type="primary"
                    @click.stop.prevent="askSignVis = true"
                >请求他人认证</el-button>
            </div>
        </template>
        <AskSign
            v-else
            :entName="inputName"
            @close="askSignVis = false"
        ></AskSign>
    </el-dialog>
</template>

<script>
import { isPC } from 'src/common/utils/device.js';
import { createEnterpriseMinxin } from 'src/mixins/index.js';
import authPath from 'src/common/utils/authPath';
import AskSign from 'src/components/askSign/index.vue';
import { getMaskName, getMaskIdCard } from 'src/common/plugins/validation/validation-config.js';

export default {
    components: {
        AskSign,
    },
    mixins: [createEnterpriseMinxin],
    props: {
        isEnt: {
            type: Boolean,
            default: false, // 是否是企业
        },
        senderName: {
            type: String,
            default: 'XX', // 发送人名字
        },
        sendAccount: {
            type: String,
            default: '', // 发送人账号
        },
        contractTitle: {
            type: String,
            default: '**', // 合同名字
        },
        receiver: {
            default: () => {},
            type: Object,
        },
    },
    data() {
        return {
            moduleShow: false,
            isPC: isPC(),
            contractId: this.$route.query.contractId,
            askSignInput: {
                entName: '',
                account: '',
            },
            askSignVis: false,
        };
    },
    computed: {
        inputName() {
            const { inputEnterpriseName, inputUserName } = this.receiver;
            return this.isEnt ? inputEnterpriseName : inputUserName;
        },
        // 签约主体名称
        subjectName() {
            return this.isEnt ? this.receiver.enterpriseName : this.receiver.userName;
        },
        noteText() {
            const tempT = this.$t('sign.authTip.t2');
            // `注：${this.isEnt ? '企业名称' : '身份信息'}完全一致才能查看和签署合同。`
            return `${tempT[0]}${this.isEnt ? tempT[2] : tempT[3]}${tempT[4]}`;
        },
        personText() {
            // return message;
            const { inputName, receiver } = this;
            if (this.isEnt) {
                // `以<span> ${inputName} </span>的身份`;
                return this.$t('sign.authTip.tCommon1', {
                    entName: `<span> ${inputName} </span>`,
                });
            }
            let message = '';
            // 个人 -> 以姓名为{name}，身份证号为{idCard}
            const [name, idCard] = [
                `<span>${getMaskName(inputName)}</span>`,
                `<span>${getMaskIdCard(receiver.idNumberForVerify)}</span>`,
            ];
            if (inputName && receiver.idNumberForVerify) {
                message = this.$t('sign.authTip.tCommon2_1', {
                    name,
                    idCard,
                });
            } else if (inputName && !receiver.idNumberForVerify) {
                message = this.$t('sign.authTip.tCommon2_2', { name });
            } else if (!inputName && receiver.idNumberForVerify) {
                message = this.$t('sign.authTip.tCommon2_3', { idCard });
            } else {
                message = '';
            }
            return `<label>${message}</label>`;
        },
    },
    watch: {
        moduleShow(val) {
            if (val) {
                // fix SAAS-2980 modal浮层移动端滑动穿透
                this.$el.addEventListener('touchmove', e => e.preventDefault(), false);
            } else {
                this.$el.removeEventListener('touchmove', e => e.preventDefault(), false);
            }
        },
    },
    methods: {
        goToAuth() {
            if (this.isEnt) {
                return this.switchIdentityAndAuth().then(() => this.goToCurrentAuth());
            }

            // 使用当前身份引导去实名
            return this.goToCurrentAuth();
        },
        goToHome() {
            this.$router.push('/account-center/home');
        },
        // 使用当前主体去实名认证
        goToCurrentAuth() {
            if (this.isEnt) {
                this.$localStorage.remove('designatedIdentityData');
            } else if (this.inputName || this.receiver.idNumberForVerify) {
                this.$localStorage.set('designatedIdentityData', JSON.stringify({
                    name: this.inputName || '',
                    idCard: this.receiver.idNumberForVerify || '',
                    contractId: this.contractId,
                }));
            }
            this.$localStorage.set('transitionHref', this.$route.fullPath);
            this.$router.push(this.isEnt ? '/ent/sign/guide' : authPath('sign', {
                fromPath: encodeURIComponent(this.$route.fullPath),
                contractId: this.$route.query.contractId,
            }));
        },
        setSignWithSameEntityVar() {
            const { mustSignWithSameEntity } = this.receiver;
            // 接口配置
            // receiver.signDecision === 'NOT_ADD'；收件人不存在该名称实名信息，但上上签平台中存在相应的企业实名信息时
            if (mustSignWithSameEntity && this.receiver.signDecision !== 'NOT_ADD') {
                // 个人收件方实名不一致不展示该弹窗（该弹窗无重新实名入口），后面点击签署会有带重新实名入口的弹窗
                if (!(this.receiver.hasAuthenticated && !this.isEnt)) {
                    this.moduleShow = true;
                }
            }
        },
        // 给管理员发通知
        postApplyForJoin() {
            return this.$http.post(`${signPath}/contracts/${this.$route.query.contractId}/receivers/${this.receiver.receiverId}/apply-for-join`).then(() => {
                this.$MessageToast.success(this.$t('sign.SentSuccessfully'));
                this.moduleShow = false;
            });
        },
    },
};
</script>

<style lang="scss">
.dialog__ForceGuide {
    p {
        color: #666666;
        font-size: 16px;
        span {
            color: #000000;
        }
        margin-bottom: 20px;
    };
    &--btn {
        margin-top: 40px;
        overflow: hidden;
        .el-button {
            height: 32px;
            line-height: 32px;
            padding: 0 16px;
            background: #108EE8;
            font-size: 14px;
            float: right;
            margin-left: 5px;
            [dir="rtl"] & {
                float: left;
                margin-left: 0;
                margin-right: 5px;
            }
        }
    }
    &--rejectBtn {
        background-color: #f8f8f8;
        border-radius: 2px;
        border: solid 1px #cccccc;
        color: #666666;
    }
    .el-dialog {
        max-width: 483px;
        border-radius: 4px;
        overflow: hidden;
    }
    .el-dialog__header {
        padding: 24px 35px;
    }
    .el-dialog__body {
        padding: 27px 37px;
    }
    .el-button + .el-button {
        margin-left: 1%;
        [dir="rtl"] & {
            margin-left: 0;
            margin-right: 1%;
        }
    }
    .el-dialog__body {
        background-color: #ffffff;
    }
    @media (max-width: 768px) {
        .el-dialog{
            width: 90%;
        }
    }
}
</style>
