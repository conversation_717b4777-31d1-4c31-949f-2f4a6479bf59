<template>
    <!-- 创建企业 -->
    <div class="box-sizing-dialog">
        <el-dialog
            :title="title"
            :visible.sync="visible"
            size="tiny"
            :before-close="handleClose"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :modal-append-to-body="false"
            class="home-dialog-createCompany el-dialog-bg"
        >
            <div>
                <h4 class="dialog_body_header">
                    {{ $t('home.createCompnayP.p1') }}
                </h4>
                <el-form
                    class="setPhone-form"
                    :model="createCompany"
                >
                    <el-radio-group v-model="createCompany.option">
                        <el-form-item>
                            <el-radio :label="0">{{ $t('home.createCompnayP.p2') }}</el-radio>
                        </el-form-item>
                        <el-form-item>
                            <el-radio :label="1">{{ $t('home.createCompnayP.p3') }}</el-radio>

                            <div class="input-entName" v-show="createCompany.option == 1">
                                <el-input
                                    v-model="createCompany.name"
                                    :placeholder="$t('home.enterEnterprise')"
                                    size="tiny"
                                    v-focus
                                ></el-input>
                                <p>{{ $t('home.createCompnayP.p4') }}</p>
                            </div>
                        </el-form-item>
                    </el-radio-group>
                </el-form>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button
                    type="primary"
                    @click="handleCreate"
                >{{ $t('home.createEnt') }}</el-button>
                <el-button
                    @click="handleClose"
                >{{ $t('home.cancel') }}</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>

export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            createCompany: {
                option: 0,
                name: '',
            },
        };
    },
    methods: {
        // 创建新企业
        handleCreate() {
            const fillName = +this.createCompany.option === 1;
            const data = {};
            const entName = this.createCompany.name.replace(/\s+/g, '');

            if (fillName) {
                if (entName.length === 0) {
                    this.$MessageToast(this.$t('home.plzEnterRightEnt'));
                    return;
                } else {
                    data.entName = this.createCompany.name;
                }
            }

            this.$http.post('/authenticated/enterprise-register', data).then(res => {
                // 自动切换到企业，需要替换 token
                this.$token.save(res.data.access_token, res.data.refresh_token);

                this.$MessageToast.success(this.$t('home.createSuccess'));

                this.$router.push(fillName
                    ? '/console/enterprise/account/setting'
                    : `/enterprise/authentication`,
                );
            });
        },
        handleClose() {
            this.$emit('update:visible', false);
        },
    },
};
</script>

<style lang="scss">
	.box-sizing-dialog .home-dialog-createCompany.el-dialog-bg{
		.el-dialog{
			width: 438px;
			min-height: 312px;

			.el-dialog__body{
				.dialog_body_header{
					text-align: left;
				}

				.setPhone-form{
					.el-form-item{
						margin-bottom: 0px;

						.el-radio__label{
							font-size: 14px;
							color: #333;
						}

						.input-entName{
							padding-left: 20px;
							line-height: 25px;

							.el-input__inner{
								height: 30px;
								line-height: 30px;
							}

							p{
								color: #999;
								font-size: 12px;
							}
						}
					}
				}
			}

			.el-dialog__footer{
				padding-bottom: 35px;
			}
		}
	}
</style>
