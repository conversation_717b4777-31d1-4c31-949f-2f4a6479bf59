<template>
    <li
        v-if="info.visible"
        v-module-id="info.moduleId"
        :data-href="info.href"
        :class="['bs-header-nav__li', info.class, {
            'router-link-active bs-header-nav__li-active': isActive(info.href)
        }]"
        :data-id="info.id"
    >
        <div class="nav-item-name" @click="goToLink(info.href, info.router)">{{ info.text }}</div>
        <ul class="bs-header-subnav" v-if="info.children && info.children.filter(v => v.visible).length">
            <NavItem
                class="subnav-item"
                v-for="nav in info.children"
                :key="nav.id"
                :info="nav"
            />
        </ul>
    </li>
</template>

<script>
export default {
    name: 'NavItem',
    props: {
        info: {
            type: Object,
            default: () => {},
        },
    },
    methods: {
        isActive(href) {
            return href && location.pathname.indexOf(href) > -1;
        },
        goToLink(hostName, isSpa) {
            if (!hostName) {
                return;
            }
            if (isSpa) {
                this.$router.push(hostName);
            } else {
                window.location.href = `//${window.location.host}${hostName}`; // ie10 一下不支持 location.origin
            }
        },
    },
};
</script>

<style lang="scss" scoped>
</style>
