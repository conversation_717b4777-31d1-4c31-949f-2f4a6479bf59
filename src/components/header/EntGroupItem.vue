<template>
    <div v-if="isAllCompanyDialog">
        <div class="select-company">
            <p>
                <span :class="{'is-group-ent':isGroup}">
                    <!-- 企业完成意愿性认证后显示企业名称 -->
                    <template v-if="perEnterprise.entName">
                        <template v-if="perEnterprise.authStatus == 2">
                            {{ perEnterprise.empName }}
                            <template v-if="perEnterprise.empName"> - </template>
                            {{ perEnterprise.fullEntName }}
                            <template v-if="perEnterprise.isGroupProxyAuthStatus">（集团代认证）</template>
                        </template>
                        <template v-else-if="perEnterprise.ecologyFlag">
                            <template v-if="perEnterprise.entId === `0-${perEnterprise.clientId}-ecology`">
                                {{ commonHeaderInfo.platformUser.account }} （{{ $t('home.ecologyPerson',{developerName:perEnterprise.developerName}) }}）
                            </template>
                            <template v-else>
                                {{ perEnterprise.empName }}
                                <template v-if="perEnterprise.empName"> - </template>
                                {{ perEnterprise.entName }}（{{ $t('home.ecology',{developerName:perEnterprise.developerName}) }}）
                            </template>
                        </template>
                        <template v-else-if="perEnterprise.authStatus == 1">
                            {{ perEnterprise.fullEntName }}（{{ $t('home.authenticating') }}）
                        </template>
                        <template v-else>
                            {{ perEnterprise.fullEntName }}（{{ perEnterprise.ifAuthRejected ? $t('home.rejectAuthenticate') : $t('home.unAuthenticate') }}）
                        </template>
                    </template>
                    <template v-else>
                        {{ $t('home.entAccount') }} - {{ userInfo.platformUser.account }}
                    </template>
                </span>
            </p>
            <!-- 当前企业小图标 -->
            <i v-show="selectEntId === perEnterprise.entId"
                class="el-icon-ssq-qianyuewancheng curAccountInfo"
            ></i>
            <!-- 群主企业小图标 -->
            <i
                v-if="perEnterprise.isGroupAdmin"
                class="el-icon-ssq-wujiaoxing1  groupOwnerEnt"
                :class="{'isSelectEnt':selectEntId === perEnterprise.entId}"
            ></i>
        </div>
    </div>
    <div v-else>
        <div class="inline-block cursor-point content" :class="{'is-group-ent':isGroup}">
            <!-- 企业账号 -->
            <template v-if="perEnterprise.entId !== '0-ecology'">
                <!-- 企业完成意愿性认证后显示企业名称 或者生态版显示马甲 -->
                <template v-if="perEnterprise.entName">
                    <template v-if="perEnterprise.authStatus == 2">
                        <p
                            class="content-paragraph"
                            v-if="perEnterprise.empName"
                        >
                            {{ perEnterprise.empName
                            }}<span class="account-type-text">（{{ $t("home.entAccount") }}）</span>
                        </p>
                        <p class="content-paragraph">
                            {{ perEnterprise.bizName ? `${perEnterprise.entName}_${perEnterprise.bizName}` : perEnterprise.entName }}
                            <template v-if="perEnterprise.isGroupProxyAuthStatus">（<!-- 集团代认证 -->{{
                                $t("commonHeader.groupCertification")
                            }}）</template>
                        </p>
                    </template>
                    <template v-else-if="perEnterprise.ecologyFlag">
                        <p
                            class="content-paragraph"
                            v-if="perEnterprise.empName"
                        >
                            {{ perEnterprise.empName
                            }}<span class="account-type-text">（{{ $t("home.ecology",{developerName:perEnterprise.developerName}) }}）</span>
                        </p>
                        <p class="content-paragraph">
                            {{ perEnterprise.entName }}
                        </p>
                    </template>
                    <template v-else-if="perEnterprise.authStatus == 1">
                        {{ perEnterprise.bizName ? `${perEnterprise.entName}_${perEnterprise.bizName}` : perEnterprise.entName }}
                        <span class="account-type-text">（{{ $t("home.authenticating") }}）</span>
                    </template>
                    <template v-else>
                        {{ perEnterprise.bizName ? `${perEnterprise.entName}_${perEnterprise.bizName}` : perEnterprise.entName }}
                        <span class="account-type-text">（{{ perEnterprise.ifAuthRejected ? $t('home.rejectAuthenticate') :$t("home.unAuthenticate") }}）</span>
                    </template>
                </template>

                <template v-else>
                    {{ userInfo.platformUser.account }}
                    <span class="account-type-text">（{{ $t("home.entAccount") }}）</span>
                </template>
            </template>
        </div>
        <!-- 当前企业小图标 -->
        <i
            v-show="userInfo.currentEntId == perEnterprise.entId"
            class="el-icon-ssq-qianyuewancheng curAccountInfo"
        ></i>
        <!-- 群主企业小图标 -->
        <i
            v-if="perEnterprise.isGroupAdmin"
            class="el-icon-ssq-wujiaoxing1  groupOwnerEnt"
            :class="{'isSelectEnt':selectEntId === perEnterprise.entId}"
        ></i>
    </div>

</template>

<script>
export default {
    name: 'NavItem',
    props: {
        perEnterprise: {
            type: Object,
            default: () => ({
            }),
        },
        userInfo: {
            type: Object,
            default: () => ({
            }),
        },
        isGroup: {
            type: Boolean,
            default: false,
        },
        isAllCompanyDialog: {
            type: Boolean,
            default: false,
        },
        selectEntId: {
            type: String,
            default: '',
        },
    },
    methods: {

    },
};
</script>

<style lang="scss" scoped>
.select-company{
     p {
        line-height: 35px;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: left;
        padding-right: 24px;
        vertical-align: middle;
        max-height: 42px;
        span {
            overflow:hidden;
            text-overflow: ellipsis;
            display:inline-block;
            vertical-align:middle;
            text-overflow: -o-ellipsis-lastline;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
        }
    }
    .is-group-ent{
        padding-left: 25px !important;
    }
    .group-mark{
        color: #333333;
        font-size: 14px;
        line-height: 40px;
        margin-right: 5px;
    }

    .curAccountInfo {
        position: absolute;
        right: 37px;
        top: 50%;
        transform: translateY(-7px);
        color: #127fd2;
        font-weight: bold;
        font-size: 14px;
        vertical-align: middle;
    }
    .groupOwnerEnt{
        position: absolute;
        right: 37px;
        top: 50%;
        transform: translateY(-7px);
        color: #127fd2;
        font-weight: bold;
        font-size: 14px;
        vertical-align: middle;
    }
    .isSelectEnt{
        top: 73% !important;
    }
}
</style>
