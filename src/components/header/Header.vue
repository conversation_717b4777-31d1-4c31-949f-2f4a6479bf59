<template>
    <div class="common-header">
        <div class="common-header-width">
            <h1
                class="ssq-logo inline-block cursor-point"
                v-if="!logoConfig || logoConfig.visible"
            >
                <template v-if="!logoConfig || logoConfig.clickAble">
                    <template v-if="currentLogoStatus === 0">
                        <router-link tag="div" :to="`${GLOBAL.rootPathName}/account-center/home`">
                            <img
                                class="default-logo cur-pointer"
                                :src="GLOBAL.WHITE_LOGO"
                                width="86"
                                height="37"
                                alt
                            />
                        </router-link>
                    </template>
                    <router-link
                        v-else
                        class="entLogo"
                        tag="img"
                        :to="`${GLOBAL.rootPathName}/account-center/home`"
                        :src="currentLogoSrc"
                    ></router-link>
                </template>
                <template v-else>
                    <img
                        v-if="currentLogoStatus === 0"
                        class="default-logo"
                        :src="GLOBAL.WHITE_LOGO"
                        width="86"
                        height="37"
                        alt
                    />
                    <!-- <i v-if="currentLogoStatus == 0" class="el-icon-ssq-fenzu3"></i> -->
                    <img v-else class="entLogo" :src="currentLogoSrc" />
                </template>
            </h1>
            <ul class="nav-list" v-if="!noNavList">
                <NavItem
                    class="cursor-point inline-block nav-item"
                    v-for="item in navList"
                    :key="item.id"
                    :info="item"
                />
            </ul>

            <div>
                <div class="guide-ent-step1 guide-step">
                    <img src="./img/home_ent_guide1.png" height="159" width="476" alt />
                    <span class="guide-text">{{ $t("home.usercenter") }}</span>
                    <span class="guide-trigger" @click="handleNextGuide"></span>
                </div>
                <div class="guide-ent-step2 guide-step">
                    <img src="./img/home_ent_guide2.png" height="216" width="371" />
                    <span class="guide-text">{{ $t("home.console") }}</span>
                    <span class="guide-trigger" @click="handleNextGuide"></span>
                </div>
                <div class="guide-ent-step3 guide-step">
                    <img src="./img/home_ent_guide3.png" height="136" width="164" />
                    <span class="guide-trigger" @click="handleNextGuide"></span>
                </div>
            </div>

            <div class="right-content" v-if="!noNavList">
                <div class="console-entrance-con inline-block">
                    <!-- 集团控制台 -->
                    <router-link
                        v-if="userInfo.hasGroupConsole"
                        tag="p"
                        :to="`${GLOBAL.rootPathName}/console/group/account/setting`"
                        class="console-entrance console-entrance_group btn-type-one"
                    >{{ $t("home.groupConsole") }}</router-link>
                    <!-- 企业控制台 -->
                    <el-tooltip
                        class="item"
                        effect="dark"
                        :content="$t('home.enterpriseConsole')"
                        placement="bottom"
                        :disabled="$i18n.locale === 'zh'"
                    >
                        <router-link
                            v-if="userInfo.hasManager"
                            tag="p"
                            :to="`${GLOBAL.rootPathName}/console/enterprise/account/setting`"
                            class="console-entrance btn-type-one"
                        >{{ $t("home.enterpriseConsole") }}</router-link>
                    </el-tooltip>
                    <div
                        v-if="createEntVisible"
                        class="console-entrance btn-type-one"
                        @click="createCompany.visible = true"
                    >
                        {{ $t("home.createEnt") }}
                    </div>
                </div>
                <!-- 数据保图标 -->
                <!--        <el-tooltip class="item" effect="dark" :content="$t('home.datainsure')" placement="bottom">-->
                <!--          <p-->
                <!--            class="el-icon-ssq-shujubaoweidakai cursor-point icon-padding icon-common-style"-->
                <!--            @click="openHref(`${GLOBAL.rootPathName}/feature-detail/${hostIsCn?'100009':'46'}`)"-->
                <!--          ></p>-->
                <!--        </el-tooltip>-->
                <!-- 混合云网络状态图标 -->
                <el-tooltip
                    class="item"
                    effect="dark"
                    :content="$t('home.viewDetail')"
                    placement="bottom"
                >
                    <span
                        v-if="hybridUser"
                        class="hybridNetStatus"
                        @click="showHybridTip"
                    >
                        <i
                            class="el-icon-ssq-lianjiegongyouyun icon-padding"
                            :class="hybridInLAN && 'active'"
                        ></i>
                    </span>
                </el-tooltip>
                <el-tooltip
                    class="item"
                    effect="dark"
                    :content="$t('home.message')"
                    placement="bottom"
                >
                    <i
                        @click="noticeShowFn"
                        class="iconfont el-icon-ssq-tongzhi notice-icon icon-padding"
                    >
                        <span v-if="noticeCount.unreadMessageCount > 0"></span>
                    </i>
                </el-tooltip>
                <transition
                    enter-active-class="animated fadeIn"
                    leave-active-class="animated fadeOut"
                >
                    <NoticeDec
                        v-if="noticeShow"
                        :style="{
                            right: getUserType === 'Enterprise' ? '19px' : '-9px',
                        }"
                        class="icon-common-style"
                        :noticeShow="noticeShow"
                        :noticeCount="noticeCount"
                        @updateNoticeCount="updateNoticeCount"
                        @changeStatus="noticeShowFn"
                    ></NoticeDec>
                </transition>
                <!--操作视频-->
                <el-tooltip
                    class="item"
                    effect="dark"
                    :content="$t('home.video')"
                    placement="bottom"
                    :disabled="playTextDisabled"
                >
                    <IconPlay class="icon-padding icon-common-style"></IconPlay>
                </el-tooltip>

                <!--语言切换-->
                <LangSwitch></LangSwitch>
                <!--用户切换-->
                <div
                    class="present-identity inline-block multi-identity"
                    :class="presentIdentityClass"
                    @click="switchPresentIdentityClick"
                >
                    <p class="inline-block">
                        <template v-if="userInfo.currentEntId !== '0'">
                            <template v-if="currentEnt">
                                <template v-if="getAuthStatus == 2">
                                    {{ currentEmpName }}
                                    <template v-if="currentEmpName">-</template>
                                    {{ currentEnt }}
                                    <template
                                        v-if="enterprises[0].isGroupProxyAuthStatus"
                                    >（<!-- 集团代认证 -->{{
                                        $t("commonHeader.groupCertification")
                                    }}）</template>
                                </template>
                                <template v-else-if="getAuthStatus == 1">{{ currentEnt }}（{{ $t("home.authenticating") }}）</template>
                                <template v-else>{{ currentEnt }}（{{ $t("home.unAuthenticate") }}）</template>
                            </template>
                            <template v-else>{{ $t("home.entAccount") }} -
                                {{ userInfo.platformUser.account }}</template>
                        </template>

                        <template v-else>
                            {{
                                getAuthStatus == 2
                                    ? userInfo.platformUser.fullName
                                    : userInfo.platformUser.account
                            }}（{{ $t("home.personAccount") }}）
                        </template>
                    </p>
                    <i class="inline-block arrow animated"></i>
                    <!-- <transition
                        name="custom-classes-transition"
                        enter-active-class="animated fadeIn"
                        leave-active-class="animated fadeOut"
                    > -->
                    <ul v-if="userSwitchPopShow" class="user-switch-pop animated">
                        <!---->
                        <template v-if="!getIsLoginFromDeveloper">

                            <!-- 选中的非集团的其它企业，排第一位 -->
                            <template v-if="selectEnterprise">
                                <li
                                    class="cursor-point"
                                    :data-id="selectEnterprise.entId"
                                    :key="selectEnterprise.entId"
                                    @click="switchAccout(selectEnterprise)"
                                >
                                    <EntGroupItem
                                        :perEnterprise="selectEnterprise"
                                        :userInfo="userInfo"
                                        :isGroup="false"
                                    ></EntGroupItem>
                                </li>
                            </template>

                            <!-- 集团 -->
                            <template v-if="firstGroupInfo">
                                <!-- 集团标题 -->
                                <li class="group-item">
                                    <div class="inline-block cursor-point content">
                                        <i
                                            class="el-icon-ssq-jituantubiao group-mark"
                                        ></i> {{ firstGroupInfo.entGroupName }}
                                    </div>
                                    <i
                                        class="el-icon-arrow-up group-arrow"
                                    ></i>
                                </li>
                                <!-- 集团下子企业显示 -->
                                <li
                                    v-for="perEnterprise in firstGroupInfo.enterprises"
                                    class="cursor-point"
                                    :data-id="perEnterprise.entId"
                                    :key="perEnterprise.entId"
                                    @click="switchAccout(perEnterprise)"
                                >

                                    <EntGroupItem
                                        :perEnterprise="perEnterprise"
                                        :userInfo="userInfo"
                                        :isGroup="true"
                                    ></EntGroupItem>
                                </li>
                            </template>

                            <!-- 非集团的其它企业显示 -->
                            <template v-if="additionEnterprises.length>0">
                                <li
                                    v-for="perEnterprise in additionEnterprises"
                                    class="cursor-point"
                                    :data-id="perEnterprise.entId"
                                    :key="perEnterprise.entId"
                                    @click="switchAccout(perEnterprise)"
                                >
                                    <EntGroupItem
                                        :perEnterprise="perEnterprise"
                                        :userInfo="userInfo"
                                        :isGroup="false"
                                    ></EntGroupItem>
                                </li>
                            </template>

                            <!-- 个人主体-显示在下面 -->
                            <li
                                class="cursor-point"
                                :data-id="personSubject.entId"
                                :key="personSubject.entId"
                                @click="switchAccout(personSubject)"
                            >
                                <div class="inline-block cursor-point content">
                                    <!-- 生态版个人账号 -->
                                    <template v-if="personSubject.ecologyFlag">
                                        {{
                                            $store.getters.getPlatformUserAuthStatus === 2
                                                ? userInfo.platformUser.fullName
                                                : userInfo.platformUser.account
                                        }}
                                        <span class="account-type-text">（{{ $t("home.ecologyPerson",{developerName:perEnterprise.developerName}) }}）</span>
                                    </template>
                                    <!-- 个人账号 -->
                                    <template v-else>
                                        {{
                                            $store.getters.getPlatformUserAuthStatus === 2
                                                ? userInfo.platformUser.fullName
                                                : userInfo.platformUser.account
                                        }}
                                        <span class="account-type-text">（{{ $t("home.personAccount") }}）</span>
                                    </template>
                                </div>
                                <!-- 当前企业小图标 -->
                                <i
                                    v-show="userInfo.currentEntId == personSubject.entId"
                                    class="el-icon-ssq-qianyuewancheng curAccountInfo"
                                ></i>
                            </li>

                            <!-- 如果企业多于5个或者存在多集团，则"显示更多" -->
                            <li
                                v-if="userInfo.enterprises.length > 5 || userInfo.entGroups.groups.length > 1"
                                class="extra-ent-line extra-ent-more cursor-point"
                                @click="selectCreateEnt"
                            >
                                <span>{{ $t("home.viewAllEnt") }}</span>
                            </li>
                            <!-- 新增企业 -->
                            <li
                                v-if="userInfo.isCanCreateEnterprise"
                                class="extra-ent-line cursor-point"
                                @click="handleCreateEnt"
                            >
                                <i class="el-icon-ssq-jia"></i>
                                <span>{{ $t("home.addEnt") }}</span>
                            </li>
                        </template>
                        <li class="extra-ent-line" @click="toLogout">
                            <span class="logout cursor-point el-icon-ssq-tuichu"></span>
                            {{ $t("home.exit") }}
                        </li>
                    </ul>
                    <!-- </transition> -->
                </div>
                <!--<el-tooltip class="item" effect="dark" content="退出" placement="bottom">-->
                <!--<span @click="toLogout" class="logout cursor-point el-icon-ssq-tuichu"></span>-->
                <!--</el-tooltip>-->
            </div>
            <div
                class="right-content noNavList"
                v-if="noNavList"
                @click="$router.push('/account-center/home')"
            >
                <!-- 返回首页 -->{{ $t("commonHeader.goHomePage") }}
            </div>
        </div>
        <div
            class="mask"
            v-if="userSwitchPopShow || noticeShow"
            @click="
                noticeShow
                    ? (noticeShow = !noticeShow)
                    : (userSwitchPopShow = !userSwitchPopShow)
            "
        ></div>

        <!-- 创建企业 -->
        <CreateCompany
            :title="createCompany.title"
            :visible.sync="createCompany.visible"
        ></CreateCompany>
        <!-- 查询企业 -->
        <SelectCompany
            :visible.sync="selectCompany.visible"
            @switchAccount="switchAccout"
            :currentEntId="userInfo.currentEntId"
            :userInfo="userInfo"
        ></SelectCompany>
    </div>
</template>
<script>
// import i18n from 'src/lang';
import resRules from 'utils/regs.js';
import CreateCompany from './Dialog-CreateCompany.vue';
import SelectCompany from './Dialog-SelectCompany.vue';
import EntGroupItem from './EntGroupItem.vue';
import NavItem from './NavItem.vue';
import NoticeDec from './NoticeDec.vue';
import LangSwitch from 'components/langSwitch';
import Bus from 'components/bus/bus.js';
import IconPlay from 'src/pages/common/operationVideo/IconPlay';
import { switchHandler } from 'src/mixins/switchEnt.js';

import { mapState, mapGetters } from 'vuex';

export default {
    components: {
        LangSwitch,
        CreateCompany,
        SelectCompany,
        NoticeDec,
        IconPlay,
        NavItem,
        EntGroupItem,
    },
    props: {
        logoConfig: {
            type: Object,
            default: () => ({}),
        },
        pageModule: {
            type: String,
            default: '',
        },
        noNavList: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            //   hostIsCn: window.location.host.includes("bestsign.cn"),
            hostIsCn: this.GLOBAL.ENV_NAME === 'PE_ENV',
            HOST_ENV: this.GLOBAL.HOST_ENV,
            refresh_token: this.$cookie.get('refresh_token'),
            userSwitchPopShow: false,
            currentEnt: '',
            currentEmpName: '',
            currentAuthStatus: '',
            currentLogoStatus: '', // 0 没有 1 有
            currentLogoSrc: '',
            userInfo: {
                currentEntId: '',
                hasManager: false,
                isCanCreateEnterprise: false,
                enterprises: [],
                platformUser: {
                    fullName: '',
                },
                userType: '', // Enterprise
            },
            createCompany: {
                title: this.$t('home.createEnt'),
                visible: false,
            },
            selectCompany: {
                visible: false,
            },
            resRules: resRules,
            couldRevokeIdList: [],
            noticeShow: false, // 消息展示
            title: '',
        };
    },
    computed: {
        ...mapState(['features', 'commonHeaderInfo', 'noticeCount']),
        ...mapGetters([
            'getUserType',
            'getInLAN',
            'getAuthStatus',
            'getHybridUserType',
            'getIsLoginFromDeveloper',
            'checkFeat',
            'getUserPermissons',
        ]),
        /* 排序，并只展示排序后的前5个 */
        enterprises() {
            // eslint-disable-next-line vue/no-side-effects-in-computed-properties
            return this.userInfo.enterprises.sort((a, b) => {
                /* 既不是个人账号也不是被选中账号的，不进行排序 */
                let compare = 0;
                /* 个人账号排在第二优先级 */
                if (a.entId === '0' || b.entId === '0') {
                    compare = a.entId === '0' ? -1 : 1;
                }
                /* 被选中账号排在第一优先级 */
                if (
                    this.userInfo.currentEntId === a.entId ||
                    this.userInfo.currentEntId === b.entId
                ) {
                    compare = this.userInfo.currentEntId === a.entId ? -1 : 1;
                }
                return compare;
            }).slice(0, 5);
        },
        // 是否加入集团
        hasGroup() {
            return this.userInfo.entGroups.groups.length > 0;
        },
        // 第一个集团信息
        firstGroupInfo() {
            return this.userInfo.entGroups.groups[0];
        },
        // 是否选中了集团
        selectGroup() {
            return this.userInfo.entGroups.groups.find(e => e.selectedGroup);
        },
        // 是否选中了非集团的企业
        selectEnterprise() {
            return this.userInfo.entGroups.enterprises.find(e => e.selectedFlag);
        },
        // 个人主体
        personSubject() {
            return  this.userInfo.entGroups.enterprises.find(e => e.entId === '0');
        },
        // 除了集团，补充的非集团的企业
        additionEnterprises() {
            let num = 5;
            if (this.selectEnterprise) {
                num--;
            }
            if (this.firstGroupInfo) {
                if (this.firstGroupInfo.enterprises.length >= num) {
                    num = 0;
                } else {
                    num = num  - this.firstGroupInfo.enterprises.length;
                }
            }

            // 过滤个人主体
            const enterpriseList = this.userInfo.entGroups.enterprises.filter(item => {
                if (this.selectEnterprise) {
                    return  item.entId !== '0' &&  item.entId !== this.selectEnterprise.entId;
                }
                return  item.entId !== '0';
            });
            // 过滤生态版个人主体
            const ecologyEntList = this.commonHeaderInfo.ecologyEnterprises.filter(item => {
                if (this.selectEnterprise) {
                    return  item.entId !== '0-ecology' &&  item.entId !== this.selectEnterprise.entId;
                }
                return  item.entId !== '0-ecology';
            });
            // 将生态版企业主体和旗舰版正常的企业合并在一起
            return [...enterpriseList, ...ecologyEntList].slice(0, num);
        },
        presentIdentityClass: function() {
            return {
                active: this.userSwitchPopShow,
            };
        },
        // 创建企业按钮显示
        createEntVisible() {
            return (
                this.userInfo.enterprises.length === 1 &&
                this.userInfo.userType === 'Person'
            );
        },
        // 判断和公有云 public 搭边的 getHybridUserType 全都不显示小图标
        hybridUser() {
            return (
                this.getHybridUserType !== 'public' &&
                this.getHybridUserType !== 'publicHybrid' &&
                this.getHybridUserType !== 'publicAliyun'
            );
        },
        // 混合云网络状态
        hybridInLAN() {
            return this.getInLAN;
            // 如果是进入其他混合云，则取 cacheLANConnected 的状态，否则取 getInLAN
            /* if (this.getHybridUserType === 'otherHybrid') {
					return this.commonHeaderInfo.cacheLANConnected;
				} else {
					return this.getInLAN;
				}*/
        },
        navList() {
            return [
                {
                    visible: true,
                    router: true,
                    text: this.$t('home.home'),
                    href: `${this.GLOBAL.rootPathName}/account-center/home`,
                    id: 'home',
                    moduleId: this.pageModule + '_1_home',
                },
                {
                    visible: true,
                    router: true,
                    text: this.$t('home.contractManage'),
                    href: `${this.GLOBAL.rootPathName}/doc`,
                    id: 'doc',
                    moduleId: this.pageModule + '_1_doc',
                },
                {
                    visible:
                        this.userInfo.userType === 'Enterprise' && this.checkFeat.template,
                    router: true,
                    text: this.$t('home.templateManage'),
                    href: `${this.GLOBAL.rootPathName}/template/list`,
                    id: 'tem',
                    moduleId: this.pageModule + '_1_tpl',
                },
                {
                    visible:
                        this.userInfo.userType === 'Enterprise' && this.getUserPermissons.statistics,
                    router: true,
                    text: this.$t('home.statisticCharts'),
                    href: `${this.GLOBAL.rootPathName}/statistics`,
                    id: 'statistics',
                    moduleId: this.pageModule + '_1_stats',
                },
                {
                    visible: true,
                    router: true,
                    text: this.$t('home.userCenter'),
                    href: `${this.GLOBAL.rootPathName}/usercenter`,
                    id: 'usercenter',
                    moduleId: this.pageModule + '_1_usc',
                },
                {
                    visible: this.userInfo.currentEntId !== '0' && this.features.includes('86'),
                    router: true,
                    text: this.$t('userCentral.fileService'),
                    href: `${this.GLOBAL.rootPathName}/box/`,
                    id: 'databox',
                    moduleId: this.pageModule + '_1_dtb',
                    class: 'nav-item-box',
                },
            ];
        },
        cmVisible() {
            const isPublic = this.getHybridUserType === 'public';
            const isEnterprise = this.userInfo.userType === 'Enterprise';
            // src/pages/common/doc/list/common/Doc-Slider.vue 中的 couldSeeContract 逻辑
            // const couldSeeContract = !!(this.$store.getters.doc_getUserRole || []).reduce((prev, cur) => {
            //     return prev.concat(cur.privileges);
            // }, []).find(v => v.name === 'VIEW_CONTRACT');
            return isPublic && isEnterprise;
        },
        playTextDisabled() {
            return this.$store.state.newFromExperience;
        },
    },
    watch: {
        commonHeaderInfo: {
            handler(val) {
                this.initData(val);
            },
            deep: true,
        },
    },
    methods: {
        initData(value) {
            this.userInfo = value;

            // let personalIndex;
            for (var i = 0; i < this.userInfo.enterprises.length; i++) {
                const temO = this.userInfo.enterprises[i];
                // if (temO.entId === '0') {
                //     personalIndex = i;
                // }
                if (temO.entId === this.userInfo.currentEntId) {
                    this.currentEnt = temO.fullEntName;
                    this.currentAuthStatus = temO.authStatus;
                    this.currentEmpName = temO.empName;
                    if (temO.logoFileId || this.$cookie.get('homeLogoFileId')) {
                        this.currentLogoStatus = 1;
                        this.currentLogoSrc = `/ents/logo?t=${Date.parse(new Date())}`;
                    } else {
                        this.currentLogoStatus = 0;
                    }
                }
            }
        },
        // 切换身份下拉
        switchPresentIdentityClick() {
            // if ((!this.$store.getters.getIsLoginFromDeveloper)) {
            // 	this.userSwitchPopShow = !this.userSwitchPopShow;
            // this.userSwitchPopShow && this.getCouldLogOffList();
            // }
            this.userSwitchPopShow = !this.userSwitchPopShow;
        },
        // 切换身份
        switchAccout(needSwitchEnterprise) {
            // 需要切换的企业主体
            const needSwitchEnterpriseId = needSwitchEnterprise.entId;
            // 需要切换的企业主体 id 与当前 id 相等时，不需要切换
            if (needSwitchEnterpriseId === this.userInfo.currentEntId) {
                return;
            }
            // 如果是生态版的马甲，进入生态版相关界面
            if (needSwitchEnterprise.ecologyFlag) {
                const tempWindow = window.open();
                // 生态版切换主体 跳转至生态版合同列表页
                return this.$http.get(`/ents/ecology-contract-url?entId=${needSwitchEnterprise.entId.split('-')[0]}&clientId=${needSwitchEnterprise.clientId}`).then(res => {
                    tempWindow.location = res.data.value;
                    this.selectCompany.visible = false;
                }).catch(() => {
                    this.selectCompany.visible = false;
                    tempWindow.close();
                });
            }
            // 不是上述情况时，切换主体
            this.$http
                .post(`/authenticated/switch-ent`, {
                    entId: needSwitchEnterpriseId,
                    refreshToken: this.$cookie.get('refresh_token'),
                })
                .then(({ data }) => {
                    switchHandler(data);
                });
        },
        handleNextGuide() {
            this.$emit('nextGuide');
        },
        // 登出账号
        toLogout() {
            this.$cookie.delete('foundation_account_into');
            this.$http.logOut().then(() => {
                this.$router.push(`${this.GLOBAL.rootPathName}/login`);
            });
        },
        // 新增企业
        handleCreateEnt() {
            this.switchPresentIdentityClick();
            this.createCompany.visible = true;
        },
        // 选择企业
        selectCreateEnt() {
            this.selectCompany.visible = true;
        },
        // 混合云网络状态提醒
        showHybridTip() {
            const $c = this.$createElement;

            if (this.hybridInLAN) {
                this.$msgbox({
                    customClass: 'hybridConnectedDialog',
                    title: this.$t('home.tip'),
                    message: $c(
                        'div',
                        null,
                        this.$t('commonHeader.companyPrivateSaveTypeContactTip'), // 您的企业采用了合同私有存储的方式当前网络已连接至合同存储服务器
                    ),
                    confirmButtonText: this.$t('home.ok'),
                });
            } else {
                this.$msgbox({
                    customClass: 'hybridUnConnectedDialog',
                    title: this.$t('home.tip'),
                    message: $c('div', null, [
                        $c('div', null, [
                            $c(
                                'div',
                                null,
                                this.$t('commonHeader.companyPrivateSaveTypeNoContactTip'), // 您的企业采用了合同私有存储的方式，当前网络无法连接至合同存储服务器
                            ),
                            $c('ul', null, [
                                $c('li', null, this.$t('commonHeader.advise')), // 建议：
                                $c(
                                    'li',
                                    null,
                                    this.$t('commonHeader.checkCompanyInteralNetContact'),
                                ), // ① 检查当前网络能否访问企业内网
                                $c(
                                    'li',
                                    null,
                                    this.$t('commonHeader.checkContactServerNetContact'),
                                ), // ② 检查合同存储服务器是否正常运行
                            ]),
                        ]),
                    ]),
                    confirmButtonText: this.$t('home.ok'),
                });
            }
        },
        // 点击消息按钮
        noticeShowFn() {
            this.noticeShow = !this.noticeShow;
        },
        // 更新消息数量
        updateNoticeCount(data) {
            this.$store.commit('pushNoticeCount', data);
            if (this.$route.path.indexOf('/notice/') > -1) {
                Bus.$emit('update-notice');
            }
        },
        openHref(url) {
            window.open(url);
        },
    },
    created: function() {
        // console.log(i18n);
        this.initData(this.commonHeaderInfo);
        this.$http.getNoticeCount(1);
    },
};
</script>
<style lang="scss">
$common_header_height: 63px;
$uc-header-height: 63px;

.common-header {
    width: 100%;
    height: $common_header_height;
    background-color: #002b45;
    font-size: 12px;
    position: relative;
    z-index: 100;
    * {
        box-sizing: border-box;
    }
    .unautherized-con {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: $common_header_height;
        line-height: $common_header_height;
        text-align: center;
        background-color: #012134;
        i:nth-of-type(1) {
            padding-right: 3px;
            vertical-align: middle;
            color: #f76b26;
            font-size: 17px;
        }
        i:nth-of-type(2) {
            position: relative;
            left: 20%;
            color: #4e6471;
            font-size: 15px;
            cursor: pointer;
            &:hover {
                color: #fff;
            }
        }
        span, p {
            font-size: 12px;
        }
        span:nth-of-type(1) {
            color: #fff;
            em {
                font-size: 14px;
                font-weight: bold;
                text-decoration: underline;
            }
        }
        span:nth-of-type(2) {
            color: #a1a1a1;
        }
        p {
            display: inline-block;
            width: 56px;
            height: 26px;
            line-height: 26px;
        }
    }
    .common-header-width {
        position: relative;
        @include base-width;
        height: 100%;
        margin: 0 auto;
        background-color: #002b45;
        font-size: 0;
        .common-header-title {
            color: #fff;
            line-height: 63px;
            position: absolute;
            top: 0;
            left: 50%;
            font-size: 18px;
            transform: translateX(-50%);
        }
        .common-user-name {
            height: 63px;
            line-height: 63px;
            color: #fff;
            font-size: 12px;
            float: right;
        }
    }
    .inline-block {
        display: inline-block;
    }
    .cursor-point {
        cursor: pointer;
    }
    .text-align-right {
        text-align: right;
    }
    .text-align-center {
        text-align: center;
    }
    .no-cursor-point {
        cursor: auto;
    }
    .ssq-logo {
        margin-left: 18px;
        // width: 124px;
        height: $common_header_height;
        line-height: $common_header_height;
        color: #fff;
        font-size: 12px;
        i {
            color: #fff;
            font-size: 37px;
            vertical-align: middle;
        }
        img.entLogo {
            width: 90px;
            height: 30px;
            vertical-align: middle;
        }
    }
    .nav-list {
        // position: absolute;
        // top: 0;
        // left: $uc-sidebar-width;
        // left: 153px;
        display: inline-block;
        margin-left: 55px;
        height: $uc-header-height;
        line-height: $uc-header-height;
        .nav-item {
            text-align: center;
            color: #8095a2;
            font-size: 14px;
            position: relative;
            &.router-link-active,
            &:hover {
                background-color: #054068;
                background: -webkit-gradient(
                    linear,
                    left top,
                    left bottom,
                    from(#054068),
                    to(#002c46)
                    );
                background: -webkit-linear-gradient(to bottom, #054068, #002c46);
                background: -moz-linear-gradient(to bottom, #054068, #002c46);
                background: -ms-linear-gradient(to bottom, #054068, #002c46);
                background: -o-linear-gradient(to bottom, #054068, #002c46);
                background: linear-gradient(to bottom, #054068, #002c46);
                color: #fff;
            }
            .nav-item-name {
                @include nav-li-padding;
            }
            &:hover > .bs-header-subnav {
                display: block;
            }
            & > .bs-header-subnav {
                display: none;
                position: absolute;
                top: 100%;
                left: 50%;
                z-index: 99;
                transform: translateX(-50%);
                height: auto;
                // width: 110px;
                width: 130px;
                padding: 5px 0;
                margin: 0;
                margin-top: -3px;
                white-space: nowrap;
                background: #fff;
                border: 1px solid #d1dbe5;
                border-radius: 3px;
                box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12),
                0 0 6px 0 rgba(0, 0, 0, 0.04);
                .bs-header-nav__li {
                    display: block;
                    padding: 0;
                    height: auto;
                    text-align: center;
                    font-size: 12px;
                    color: #666;
                    line-height: 30px;
                    &:hover,
                    &.bs-header-nav__li-active {
                        background: #f4f9fc;
                        color: #127fd2;
                    }
                }
            }
        }
    }
    .present-identity {
        position: relative;
        width: 240px;
        height: 100%;
        line-height: $common_header_height;
        //margin-left: 15px;
        padding-left: 15px;
        padding-right: 15px;
        color: #fff;
        text-align: left;
        vertical-align: top;
        font-size: 12px;
        &.multi-identity {
            cursor: pointer;
        }
        &.multi-identity:hover,
        &.multi-identity.active {
            background-color: #054068;
            background: -webkit-gradient(
                linear,
                left top,
                left bottom,
                from(#054068),
                to(#002c46)
                );
            background: -webkit-linear-gradient(to bottom, #054068, #002c46);
            background: -moz-linear-gradient(to bottom, #054068, #002c46);
            background: -ms-linear-gradient(to bottom, #054068, #002c46);
            background: -o-linear-gradient(to bottom, #054068, #002c46);
            background: linear-gradient(to bottom, #054068, #002c46);
        }
        p {
            width: 200px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .arrow {
            position: absolute;
            top: 29px;
            width: 0;
            height: 0;
            border: 4px solid transparent;
            border-top: 6px solid #fff;
            transition: transform 0.2s ease-in;
        }
    }
    .user-switch-pop {
        position: absolute;
        top: $common_header_height;
        right: 0;
        display: block;
        width: auto;
        min-width: 240px;
        min-height: 45px;
        // max-width: 265px;
        box-shadow: 0 0 5px rgba(221, 221, 221, 0.7);
        background-color: #fff;
        z-index: 2;
        li {
            position: relative;
            padding: 12px 30px 12px 15px;
            border-top: 1px solid #eee;
            color: #333;
            line-height: 1.5;
            &:first-child {
                border-top: none;
            }
            &:hover {
                background-color: #f6f6f6;
            }
            .content {
                min-width: 193px;
                width: auto;
                display: block;
                white-space: nowrap;
                text-overflow: ellipsis;
                text-align: left;
                .content-paragraph {
                    width: auto;
                    font-size: 12px;
                    line-height: 1.5;
                }
            }
            &.extra-ent-line {
                color: #127fd2;
                font-size: 14px;
                text-align: center;
                &.extra-ent-more {
                    font-size: 12px;
                }
                p {
                    text-align: center;
                    i {
                        font-weight: bold;
                    }
                }
           }
            .log-off-btn {
                position: absolute;
                right: 10px;
                top: 0;
                color: #127fd2;
                font-size: 12px;
                line-height: 45px;
                vertical-align: top;
                &.logOffCurEnt {
                  right: 35px;
                }
            }
        }
        .is-group-ent{
            padding-left: 25px;
        }
        .group-mark{
            color: #333333;
            font-size: 14px;
            line-height: 40px;
            margin-right: 5px;
        }
        .group-arrow{
            position: absolute;
            right: 10px;
            top: 7px;
            color: #CCCCCC;
            // font-weight: bold;
            font-size: 12px;
            vertical-align: top;
        }

        .curAccountInfo {
            position: absolute;
            right: 10px;
            top: 0px;
            color: #127fd2;
            font-weight: bold;
            font-size: 14px;
            vertical-align: top;
        }
        .groupOwnerEnt{
            position: absolute;
            right: 10px;
            top: 28px;
            color: #127fd2;
            font-weight: bold;
            font-size: 14px;
            vertical-align: top;
        }
        .isSelectEnt{
            top: 30px !important;
        }
        .account-type-text {
            color: #999;
        }
   }
    .right-content {
        position: absolute;
        right: 18px;
        top: 0;
        height: 100%;
        background-color: #002b45;
        &.noNavList {
            cursor: pointer;
            line-height: 63px;
            font-size: 14px;
            color: #fff;
        }
        .console-entrance-con {
            height: 100%;
            padding-top: 19px;
            padding-right: 10px;
            vertical-align: top;
            overflow: hidden;
        }
        .console-entrance {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 135px;
            height: 26px;
            line-height: 26px;
            font-size: 14px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            &.console-entrance_group {
                float: left;
                margin-right: 10px;
                width: 110px;
            }
        }
        .hybridNetStatus {
            cursor: pointer;
            i {
                // margin-top: 24px;
                color: #4d6b7d;
                font-size: 16px;

                &.active {
                    color: #0fb396;
                }
            }
        }
        .lang-switch {
            height: 100%;
            line-height: 63px;
        }
        .icon-padding {
            margin-right: 5px;
            margin-left: 5px;
        }
        // .logout,
        .icon-common-style {
            font-size: 16px;
            color: #fff;
            .el-tooltip {
                display: none;
            }
            i {
               vertical-align: text-bottom;
            }
            &:hover {
                color: #2298f1;
                .el-tooltip {
                    display: block;
                }
            }
        }
        // .logout,
        .icon-common-style {
            // margin-top: 24px;
            line-height: $common_header_height;
        }
        .logout {
            color: #2298f1;
            margin-top: 0;
            vertical-align: text-bottom;
        }
        .notice-icon {
            color: #fff;
            font-size: 16px;
            cursor: pointer;
            position: relative;
            &:hover {
                color: #2298f1;
            }
            span {
                width: 6px;
                height: 6px;
                border-radius: 6px;
                background-color: red;
                content: "";
                display: inline-block;
                position: absolute;
                top: 12px;
                right: -2px;
            }
        }
        .header-quit-tooltip {
            position: absolute;
            top: 25px;
            left: 188px;
        }
    }
    .mask {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
    }
}

.hybridConnectedDialog {
    width: 296px;

    .el-message-box__message {
        line-height: 25px;
    }
}

.hybridUnConnectedDialog {
    width: 534px;
    div.el-message-box__content {
        padding-bottom: 20px;

        div {
            font-size: 14px;
        }

        ul {
            margin-top: 25px;

            li {
                line-height: 25px;
                color: #666;
            }
        }
    }
}
</style>
