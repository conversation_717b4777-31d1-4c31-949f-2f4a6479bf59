<template>
    <div
        class="present-identity inline-block multi-identity"
        :class="presentIdentityClass"
        @click="switchPresentIdentityClick"
    >
        <p class="inline-block">
            <template v-if="userInfo.currentEntId !== '0'">
                <template v-if="currentEnt">
                    <template v-if="getAuthStatus == 2">
                        {{ currentEmpName }}
                        <template v-if="currentEmpName">-</template>
                        {{ currentEnt }}
                        <template
                            v-if="enterprises[0].isGroupProxyAuthStatus"
                        >（<!-- 集团代认证 -->{{
                            $t("commonHeader.groupCertification")
                        }}）</template>
                    </template>
                    <template v-else-if="getAuthStatus == 1">{{ currentEnt }}（{{ $t("home.authenticating") }}）</template>
                    <template v-else>{{ unAuthEntName }}</template>
                </template>
                <template v-else>{{ $t("home.entAccount") }} -
                    {{ userInfo.platformUser.account }}</template>
            </template>

            <template v-else>
                {{
                    getAuthStatus == 2
                        ? userInfo.platformUser.fullName
                        : userInfo.platformUser.account
                }}（{{ $t("home.personAccount") }}）
            </template>
        </p>
        <i class="inline-block arrow animated"></i>
        <ul v-if="userSwitchPopShow" class="user-switch-pop animated">
            <!---->
            <template v-if="!getIsLoginFromDeveloper">

                <!-- 选中的非集团的其它企业，排第一位 -->
                <template v-if="selectEnterprise">
                    <li
                        class="cursor-point"
                        :data-id="selectEnterprise.entId"
                        :key="selectEnterprise.entId"
                        @click="switchAccout(selectEnterprise)"
                    >
                        <EntGroupItem
                            :perEnterprise="selectEnterprise"
                            :userInfo="userInfo"
                            :isGroup="false"
                        ></EntGroupItem>
                    </li>
                </template>

                <!-- 集团 -->
                <template v-if="firstGroupInfo">
                    <!-- 集团标题 -->
                    <li class="group-item">
                        <div class="inline-block cursor-point content">
                            <i
                                class="el-icon-ssq-jituantubiao group-mark"
                            ></i> {{ firstGroupInfo.entGroupName }}
                        </div>
                        <i
                            class="el-icon-arrow-up group-arrow"
                        ></i>
                    </li>
                    <!-- 集团下子企业显示 -->
                    <li
                        v-for="perEnterprise in firstGroupInfo.enterprises"
                        class="cursor-point"
                        :data-id="perEnterprise.entId"
                        :key="perEnterprise.entId"
                        @click="switchAccout(perEnterprise)"
                    >

                        <EntGroupItem
                            :perEnterprise="perEnterprise"
                            :userInfo="userInfo"
                            :isGroup="true"
                        ></EntGroupItem>
                    </li>
                </template>

                <!-- 非集团的其它企业显示 -->
                <template v-if="additionEnterprises.length>0">
                    <li
                        v-for="perEnterprise in additionEnterprises"
                        class="cursor-point"
                        :data-id="perEnterprise.entId"
                        :key="perEnterprise.entId"
                        @click="switchAccout(perEnterprise)"
                    >
                        <EntGroupItem
                            :perEnterprise="perEnterprise"
                            :userInfo="userInfo"
                            :isGroup="false"
                        ></EntGroupItem>
                    </li>
                </template>

                <!-- 个人主体-显示在下面 -->
                <li
                    class="cursor-point"
                    :data-id="personSubject.entId"
                    :key="personSubject.entId"
                    @click="switchAccout(personSubject)"
                >
                    <div class="inline-block cursor-point content">
                        <!-- 生态版个人账号 -->
                        <template v-if="personSubject.ecologyFlag">
                            {{
                                $store.getters.getPlatformUserAuthStatus === 2
                                    ? userInfo.platformUser.fullName
                                    : userInfo.platformUser.account
                            }}
                            <span class="account-type-text">（{{ $t("home.ecologyPerson",{developerName:perEnterprise.developerName}) }}）</span>
                        </template>
                        <!-- 个人账号 -->
                        <template v-else>
                            {{
                                $store.getters.getPlatformUserAuthStatus === 2
                                    ? userInfo.platformUser.fullName
                                    : userInfo.platformUser.account
                            }}
                            <span class="account-type-text">（{{ $t("home.personAccount") }}）</span>
                        </template>
                    </div>
                    <!-- 当前企业小图标 -->
                    <i
                        v-show="userInfo.currentEntId == personSubject.entId"
                        class="el-icon-ssq-qianyuewancheng curAccountInfo"
                    ></i>
                </li>

                <!-- 如果企业多于5个或者存在多集团，则"显示更多" -->
                <li
                    v-if="userInfo.enterprises.length > 5 || userInfo.entGroups.groups.length > 1"
                    class="extra-ent-line extra-ent-more cursor-point"
                    @click="selectCreateEnt"
                >
                    <span>{{ $t("home.viewAllEnt") }}</span>
                </li>
                <!-- 新增企业 -->
                <li
                    v-if="userInfo.isCanCreateEnterprise"
                    class="extra-ent-line cursor-point"
                    @click="handleCreateEnt"
                >
                    <i class="el-icon-ssq-jia"></i>
                    <span>{{ $t("home.addEnt") }}</span>
                </li>
            </template>
            <li class="extra-ent-line" @click="toLogout">
                <span class="logout cursor-point el-icon-ssq-tuichu"></span>
                {{ $t("home.exit") }}
            </li>
        </ul>
        <div
            class="mask"
            v-if="userSwitchPopShow"
            @click.stop="userSwitchPopShow = !userSwitchPopShow"
        ></div>
        <!-- 创建企业 -->
        <CreateCompany
            @click.native.stop
            :title="createCompany.title"
            :visible.sync="createCompany.visible"
        ></CreateCompany>
        <!-- 查询企业 -->
        <SelectCompany
            @click.native.stop
            :visible.sync="selectCompany.visible"
            @switchAccount="switchAccout"
            :currentEntId="userInfo.currentEntId"
            :userInfo="userInfo"
        ></SelectCompany>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import CreateCompany from './Dialog-CreateCompany.vue';
import SelectCompany from './Dialog-SelectCompany.vue';
import EntGroupItem from './EntGroupItem.vue';
import { switchHandler } from 'src/mixins/switchEnt.js';

export default {
    components: {
        CreateCompany,
        SelectCompany,
        EntGroupItem,
    },
    data() {
        return {
            userSwitchPopShow: false,
            userInfo: {
                currentEntId: '',
                hasManager: false,
                isCanCreateEnterprise: false,
                enterprises: [],
                platformUser: {
                    fullName: '',
                },
                userType: '', // Enterprise
                // currentAuthStatus: '',
            },
            currentEnt: '',
            currentEmpName: '',
            createCompany: {
                title: this.$t('home.createEnt'),
                visible: false,
            },
            selectCompany: {
                visible: false,
            },
        };
    },
    computed: {
        ...mapState(['features', 'commonHeaderInfo', 'noticeCount']),
        ...mapGetters([
            'getUserType',
            'getInLAN',
            'getAuthStatus',
            'getHybridUserType',
            'getIsLoginFromDeveloper',
            'checkFeat',
            'getUserPermissons',
        ]),
        presentIdentityClass: function() {
            return {
                active: this.userSwitchPopShow,
            };
        },
        // 除了集团，补充的非集团的企业
        additionEnterprises() {
            let num = 5;
            if (this.selectEnterprise) {
                num--;
            }
            if (this.firstGroupInfo) {
                if (this.firstGroupInfo.enterprises.length >= num) {
                    num = 0;
                } else {
                    num = num  - this.firstGroupInfo.enterprises.length;
                }
            }

            // 过滤个人主体
            const enterpriseList = this.userInfo.entGroups.enterprises.filter(item => {
                if (this.selectEnterprise) {
                    return  item.entId !== '0' &&  item.entId !== this.selectEnterprise.entId;
                }
                return  item.entId !== '0';
            });
            // 过滤生态版个人主体
            const ecologyEntList = this.commonHeaderInfo.ecologyEnterprises.filter(item => {
                if (this.selectEnterprise) {
                    return  item.entId !== '0-ecology' &&  item.entId !== this.selectEnterprise.entId;
                }
                return  item.entId !== '0-ecology';
            });
            // 将生态版企业主体和旗舰版正常的企业合并在一起
            return [...enterpriseList, ...ecologyEntList].slice(0, num);
        },
        // 第一个集团信息
        firstGroupInfo() {
            return this.userInfo.entGroups.groups[0];
        },
        // 是否选中了集团
        selectGroup() {
            return this.userInfo.entGroups.groups.find(e => e.selectedGroup);
        },
        // 是否选中了非集团的企业
        selectEnterprise() {
            return this.userInfo.entGroups.enterprises.find(e => e.selectedFlag);
        },
        // 个人主体
        personSubject() {
            return  this.userInfo.entGroups.enterprises.find(e => e.entId === '0');
        },
        /* 排序，并只展示排序后的前5个 */
        enterprises() {
            // eslint-disable-next-line vue/no-side-effects-in-computed-properties
            return this.userInfo.enterprises.sort((a, b) => {
                /* 既不是个人账号也不是被选中账号的，不进行排序 */
                let compare = 0;
                /* 个人账号排在第二优先级 */
                if (a.entId === '0' || b.entId === '0') {
                    compare = a.entId === '0' ? -1 : 1;
                }
                /* 被选中账号排在第一优先级 */
                if (
                    this.userInfo.currentEntId === a.entId ||
                    this.userInfo.currentEntId === b.entId
                ) {
                    compare = this.userInfo.currentEntId === a.entId ? -1 : 1;
                }
                return compare;
            }).slice(0, 5);
        },
        unAuthEntName() {
            let currentEnterprise = this.selectEnterprise;
            if (this.selectGroup) {
                currentEnterprise = this.firstGroupInfo.enterprises.find(ent => ent.entId === this.userInfo.currentEntId);
            }
            if (!currentEnterprise.entName) {
                return `${this.currentEnt} (${this.$t('home.unAuthenticate')}）`;
            }
            const entName = currentEnterprise.bizName ? `${currentEnterprise.entName}_${currentEnterprise.bizName}` : currentEnterprise.entName;
            return  `${entName} (${currentEnterprise.ifAuthRejected ? this.$t('home.rejectAuthenticate') :  this.$t('home.unAuthenticate')}）`;
        },
    },
    watch: {
        commonHeaderInfo: {
            handler(val) {
                this.initData(val);
            },
            deep: true,
        },
    },
    methods: {
        // 切换身份下拉
        switchPresentIdentityClick() {
            this.userSwitchPopShow = !this.userSwitchPopShow;
        },
        initData(value) {
            this.userInfo = value;

            // let personalIndex;
            for (var i = 0; i < this.userInfo.enterprises.length; i++) {
                const temO = this.userInfo.enterprises[i];
                // if (temO.entId === '0') {
                //     personalIndex = i;
                // }
                if (temO.entId === this.userInfo.currentEntId) {
                    this.currentEnt = temO.fullEntName;
                    this.currentAuthStatus = temO.authStatus;
                    this.currentEmpName = temO.empName;
                    if (temO.logoFileId || this.$cookie.get('homeLogoFileId')) {
                        this.currentLogoStatus = 1;
                        this.currentLogoSrc = `/ents/logo?t=${Date.parse(new Date())}`;
                    } else {
                        this.currentLogoStatus = 0;
                    }
                }
            }
        },
        // 选择企业
        selectCreateEnt() {
            this.selectCompany.visible = true;
        },
        // 切换身份
        switchAccout(needSwitchEnterprise) {
            // 需要切换的企业主体
            const needSwitchEnterpriseId = needSwitchEnterprise.entId;
            // 需要切换的企业主体 id 与当前 id 相等时，不需要切换
            if (needSwitchEnterpriseId === this.userInfo.currentEntId) {
                return;
            }
            // 如果是生态版的马甲，进入生态版相关界面
            if (needSwitchEnterprise.ecologyFlag) {
                const tempWindow = window.open();
                // 生态版切换主体 跳转至生态版合同列表页
                return this.$http.get(`/ents/ecology-contract-url?entId=${needSwitchEnterprise.entId.split('-')[0]}&clientId=${needSwitchEnterprise.clientId}`).then(res => {
                    tempWindow.location = res.data.value;
                    this.selectCompany.visible = false;
                }).catch(() => {
                    this.selectCompany.visible = false;
                    tempWindow.close();
                });
            }
            // 不是上述情况时，切换主体
            this.$http
                .post(`/authenticated/switch-ent`, {
                    entId: needSwitchEnterpriseId,
                    refreshToken: this.$cookie.get('refresh_token'),
                })
                .then(({ data }) => {
                    switchHandler(data);
                });
        },
        // 登出账号
        toLogout() {
            this.$cookie.delete('foundation_account_into');
            this.$http.logOut().then(() => {
                sessionStorage.removeItem('fromDocument');
                sessionStorage.removeItem('signingPagePath');
                this.$router.push(`${this.GLOBAL.rootPathName}/login`);
            });
        },
        // 新增企业
        handleCreateEnt() {
            this.switchPresentIdentityClick();
            this.createCompany.visible = true;
        },
    },
    created() {
        this.initData(this.commonHeaderInfo);
    },
};
</script>

<style lang="scss">
$common_header_height: 63px;
.inline-block {
    display: inline-block;
}
.present-identity {
    position: relative;
    width: 240px;
    height: 100%;
    line-height: $common_header_height;
    //margin-left: 15px;
    padding-left: 15px;
    padding-right: 15px;
    color: #fff;
    text-align: left;
    vertical-align: top;
    font-size: 12px;
    &.multi-identity {
        cursor: pointer;
    }
    &.multi-identity:hover,
    &.multi-identity.active {
        background-color: #054068;
        background: -webkit-gradient(
            linear,
            left top,
            left bottom,
            from(#054068),
            to(#002c46)
            );
        background: -webkit-linear-gradient(to bottom, #054068, #002c46);
        background: -moz-linear-gradient(to bottom, #054068, #002c46);
        background: -ms-linear-gradient(to bottom, #054068, #002c46);
        background: -o-linear-gradient(to bottom, #054068, #002c46);
        background: linear-gradient(to bottom, #054068, #002c46);
    }
    p {
        width: 200px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    .arrow {
        position: absolute;
        top: 29px;
        width: 0;
        height: 0;
        border: 4px solid transparent;
        border-top: 6px solid #fff;
        transition: transform 0.2s ease-in;
    }
    .mask {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 3;
    }
}
.user-switch-pop {
    position: absolute;
    top: $common_header_height;
    right: 0;
    display: block;
    width: auto;
    min-width: 240px;
    min-height: 45px;
    // max-width: 265px;
    box-shadow: 0 0 5px rgba(221, 221, 221, 0.7);
    background-color: #fff;
    z-index: 4;
    li {
        position: relative;
        padding: 12px 30px 12px 15px;
        border-top: 1px solid #eee;
        color: #333;
        line-height: 1.5;
        &:first-child {
            border-top: none;
        }
        &:hover {
            background-color: #f6f6f6;
        }
        .content {
            min-width: 193px;
            width: auto;
            display: block;
            white-space: nowrap;
            text-overflow: ellipsis;
            text-align: left;
            .content-paragraph {
                width: auto;
                font-size: 12px;
                line-height: 1.5;
            }
        }
        &.extra-ent-line {
            color: #127fd2;
            font-size: 14px;
            text-align: center;
            &.extra-ent-more {
                font-size: 12px;
            }
            p {
                text-align: center;
                i {
                    font-weight: bold;
                }
            }
        }
        .log-off-btn {
            position: absolute;
            right: 10px;
            top: 0;
            color: #127fd2;
            font-size: 12px;
            line-height: 45px;
            vertical-align: top;
            &.logOffCurEnt {
                right: 35px;
            }
        }
    }
    .is-group-ent{
        padding-left: 25px;
    }
    .group-mark{
        color: #333333;
        font-size: 14px;
        line-height: 40px;
        margin-right: 5px;
    }
    .group-arrow{
        position: absolute;
        right: 10px;
        top: 7px;
        color: #CCCCCC;
        // font-weight: bold;
        font-size: 12px;
        line-height: 45px;
        vertical-align: top;
    }

    .curAccountInfo {
        position: absolute;
        right: 10px;
        top: 0;
        color: #127fd2;
        font-weight: bold;
        font-size: 14px;
        line-height: 45px;
        vertical-align: top;
    }
    .account-type-text {
        color: #999;
    }
}
</style>
