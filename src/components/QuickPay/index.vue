<!-- 业务组件：快捷支付 弹框-->
<!-- 引用位置 高级功能、哈勃购买-->
<template>
    <div class="quick-pay">
        <!-- 购买弹窗 -->
        <el-dialog
            class="pay-dialog"
            :visible.sync="isShowDialog"
            :close-on-click-modal="false"
            :before-close="handleClose"
            width="600px"
        >
            <div slot="title" class="top-title-box">
                <!-- <i class="el-icon-ssq-tongzhi"></i> -->
                <p class="dialog-title">请扫码支付</p>
            </div>
            <div class="pay-channel">
                <div
                    v-for="(item ,index) in payChannelList"
                    class="channel-item"
                    :class="{ 'pay-active': index === selectedPayChannel}"
                    :key="index"
                    @click="handlePayChannelChange(index)"
                >
                    {{ item }}
                </div>
            </div>
            <div class="pay-dialog-pay">
                <div class="wechat-pay-qrcode" v-if="!isIcbcDeveloper">
                    <img class="qrcode-border" v-if="isAlipay" src="~img/alipay-pay.png" alt="alipay border">
                    <img class="qrcode-border" v-else src="~img/wechat-pay.png" alt="wechat pay border">
                    <!-- 支付宝二维码是iframe -->
                    <div class="qrcode-img-con-ali" v-if="isAlipay" v-loading="imgScrLoading">
                        <iframe class="qrcode-img-ali"
                            :src="payQrCode"
                            ref="aliIframe"
                            frameborder="0"
                        ></iframe>
                    </div>
                    <!-- 微信支付二维码是图片 -->
                    <div class="qrcode-img-con-wechat" v-else v-loading="imgScrLoading">
                        <img class="qrcode-img" :src="payQrCode" v-if="!imgScrLoading" alt="pay qrcode">
                    </div>
                </div>
                <div class="pay-dialog-pay__container">
                    <div class="open-box" v-if="type!=='signerPay'">
                        <div class="title">开通功能：</div>
                        <div class="content">{{ payFeatureNames }}</div>
                    </div>
                    <div class="pay-amount">
                        <div class="title">支付金额：</div>
                        <span>￥{{ orderInfo.rechargeMoney }}</span>元
                    </div>
                    <div class="pay-btn" v-if="isIcbcDeveloper">
                        <el-button type="primary" @click="toIcbcPay">去工行支付</el-button>
                    </div>
                </div>

            </div>
            <div class="pay-dialog-pay__container__tips" v-if="!isIcbcDeveloper">
                <p>1.请扫码完成支付，完成支付后可在个人中心-我的订单中申请发票。</p>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import {  mapGetters } from 'vuex';
import JSConfetti from 'js-confetti';
export default {
    components: {
    },
    props: {
        dialogVisible: {
            type: Boolean,
            default: false,
        },
        type: {
            type: String,
            default: 'advanceFeature', // advanceFeature:高级功能 hubble:哈勃 signerPay:签署方付费
        },
        featuredIdList: {
            type: Array,
            default: () => [], // 高级功能要开通的featureId数组
        },
        // hubble的订单信息
        hubbleOrderInfo: {
            type: Object,
            default: () => ({}),
        },
        signerPayOrderInfo: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            weiXinPayImg: '', // 微信支付img
            aliPayImg: '', // 支付宝支付img
            interval: null, // 定时器
            weixinImgSrcLoading: false, // 微信支付二维码loading
            aliImgSrcLoading: false, // 支付宝支付二维码loading
            orderInfo: {}, // 支付订单信息
            isCreateOrder: false, // 是否生成订单
            selectedPayChannel: 0, // 支付通道
            enterPageDate: '', // 进页面时间
        };
    },
    computed: {
        ...mapGetters([
            'isNewGroupPath',
        ]),
        payChannelList() {
            return this.isIcbcDeveloper ? ['工商银行支付'] : ['支付宝支付', '微信支付'];
        },
        isIcbcDeveloper() {
            return this.$store.state.commonHeaderInfo.isICBCDeveloper;
        },
        // 是否显示支付弹窗
        isShowDialog() {
            return this.dialogVisible && this.isCreateOrder;
        },
        // 支付二维码
        payQrCode() {
            return  this.isAlipay ?  this.aliPayImg : this.weiXinPayImg;
        },
        // 购买的功能
        payFeatureNames() {
            if (this.orderInfo && this.orderInfo.products) {
                const products = this.orderInfo?.products.map(e => e.productName);
                return products.join(';');
            }
            return '';
        },
        // 支付二维码loading
        imgScrLoading() {
            return  this.isAlipay ?  this.aliImgSrcLoading : this.weixinImgSrcLoading;
        },
        // 是否为支付宝支付
        isAlipay() {
            return this.selectedPayChannel === 0;
        },
    },
    watch: {
        featuredIdList: {
            handler(val) {
                if (val && val.length > 0) {
                    this.handleBuyOperate();
                }
            },
            immediate: true,
            deep: true,
        },
        hubbleOrderInfo: {
            handler(val) {
                if (val && Object.keys(val).length > 0) {
                    this.orderInfo = val;
                    this.handleOrderInfo();
                }
            },
            immediate: true,
            deep: true,
        },
        signerPayOrderInfo: {
            handler(val) {
                if (val && Object.keys(val).length > 0) {
                    this.orderInfo = val;
                    this.handleOrderInfo();
                }
            },
            immediate: true,
            deep: true,
        },
        isCreateOrder(val) {
            if (val) {
                this.enterPageDate = Date.parse(new Date());
                this.handleSensorsTrack('pageView', {});
            } else {
                this.handleSensorsTrack('pageLeave', {
                    $event_duration: (Date.parse(new Date()) - this.enterPageDate) / 1000,
                });
            }
        },
    },
    methods: {
        toIcbcPay() {
            window.open('https://corporbank.icbc.com.cn/icbc/corporbank/epassclogon.jsp?ItemNo=E9638');
        },
        // 支付通道切换
        async handlePayChannelChange(index) {
            this.selectedPayChannel = index;
            const iconMap = {
                '0': '支付宝',
                '1': '微信支付',
            };
            this.handleSensorsTrack('btnClick', {
                'icon_name': iconMap[this.selectedPayChannel],
            });
            if (this.selectedPayChannel === 0) {
                this.aliPayImg = '';
                this.getAlipayQrCode();
            } else if (this.selectedPayChannel === 1) {
                await this.getWechatPayQrCode();
            }
        },
        // 支付宝二维码loading监听
        iframeLoad() {
            this.aliImgSrcLoading = true;
            const iframe = this.$refs.aliIframe;
            // 兼容处理
            if (iframe.attachEvent) {
                // IE
                iframe.attachEvent('onload', () => {
                    this.aliImgSrcLoading = false;
                });
            } else {
                // 非IE
                iframe.onload = () => {
                    this.aliImgSrcLoading = false;
                };
            }
        },
        // 关闭弹窗
        handleClose() {
            this.isCreateOrder = false;
            this.aliPayImg = '';
            this.interval && clearInterval(this.interval);
            this.$emit('handlePayCancel');
        },
        // 获取支付宝支付img
        getAlipayQrCode() {
            this.aliImgSrcLoading = true;
            return this.$http.get(`/ents/alipay/request/${this.orderInfo.orderId}`).then(res => {
                if (res.data) {
                    this.aliPayImg = res.data;
                    this.iframeLoad();
                    this.checkPayResult();
                }
            });
        },
        // 获取微信支付img
        getWechatPayQrCode() {
            this.weixinImgSrcLoading = true;
            return this.$http.post(`/ents/wechat/request/${this.orderInfo.orderId}`).then(res => {
                if (res.data) {
                    this.weixinImgSrcLoading = false;
                    this.weiXinPayImg = `data:image/png;base64,${res.data}`;
                    this.checkPayResult();
                }
            });
        },
        // 轮询检查支付结果
        checkPayResult() {
            this.interval && clearInterval(this.interval);
            let times = 180;
            this.interval = setInterval(() => {
                times--;
                const url = `/ents/charging/rechargeRecord/order/${this.orderInfo.orderId}`;

                this.$http.get(url)
                    .then(res => {
                        if (res.data.status === 1) {
                            clearInterval(this.interval);
                            this.handlePaySuccess();
                            const iconMap = {
                                '0': '支付宝',
                                '1': '微信支付',
                            };
                            this.handleSensorsTrack('urlResult', {
                                'order_id': this.orderInfo.orderId,
                                'pay_type': iconMap[this.selectedPayChannel],
                                'is_use_discount': false, // 是否使用优惠券
                                'discount_name': '', // 优惠券名称
                                'discount_amount': 0, // 优惠金额
                                'is_success': true,
                                'request_url': url,
                            });
                        } else if (times <= 0) {
                            clearInterval(this.interval);
                        }
                    });
            }, 2000);
        },
        // 支付成功
        handlePaySuccess() {
            this.$emit('handlePaySuccess');
            const confetti = new JSConfetti();
            confetti.addConfetti().then(() => {
                const msg = this.type === 'signerPay' ? '支付成功' : '开通成功';
                this.$MessageToast.success(msg);
            });
        },
        // 购买操作
        handleBuyOperate() {
            this.$http.post(`ents/charging/advancedFeatures/ordering`, {
                featureIds: this.featuredIdList,
                isNewGroup: false,
            }).then(res => {
                this.orderInfo = res.data;
                this.handleOrderInfo();
            }).catch(() => {
            });
        },
        async handleOrderInfo() {
            this.isCreateOrder = true;
            this.getAlipayQrCode();
        },
        handleSensorsTrack(type, params = {}) {
            const eventName = 'Ent_CommodityDetail_PageView';
            const eventMap = {
                btnClick: 'Ent_CommodityDetail_BtnClick', // 云平台_商品详情页_按钮点击
                pageView: 'Ent_CommodityDetail_PageView', // 云平台_商品详情页_页面浏览
                urlResult: 'Ent_OrderSubmit_Result', // 云平台_订单支付_结果
                pageLeave: 'Ent_CommodityDetail_PageLeave', // 云平台_商品详情页_页面离开
            };
            const eventParams = {
                'previous_page_name': '功能入口处',
                'commodity_id': this.orderInfo.orderId, // 商品id
                'commodity_name': this.payFeatureNames, // 商品名称
                'commodity_price': '', // 商品价格
                'purchase_num': this.orderInfo.products.length, // 采购数量
                'purchase__price': this.orderInfo.rechargeMoney, // 采购价格
            };
            this.$sensors.track({
                eventName: eventMap[type] || eventName,
                eventProperty: {
                    ...eventParams,
                    ...params,
                },
            });
        },
    },
    destroyed() {
        window.clearInterval(this.interval);
    },
};
</script>

<style lang="scss">
   $--color-primary: #127fd2 !default;
    $--color-text-primary: #333333 !default;
    /// 次要文字颜色 color|1|Font Color|2
    $--color-text-secondary: #999999 !default;
    $--color-warning: #F2A93E !default;
    $--border-color-lighter: #EEEEEE !default;
    $--background-color-regular: #F8F8F8 !default;

    .quick-pay{
        color: white;
        .el-dialog__header{
            height: 50px !important;
            padding: 0px 20px !important;
            background-color: $--color-primary;
            .el-dialog__title{
                font-size: 16px;
                color: white;
                font-weight: normal;
                line-height: 50px;
                height: 50px;
            }
            .el-dialog__close {
                padding: 10px 0;
            }
        }
        .top-title-box{
            display: flex;
            align-content: center;
            .el-icon-bell {
                padding-top: 15px;
                font-size: 20px;
            }
            .dialog-title{
                // padding-top: 14px;
                font-size: 16px;
                margin-left: 10px;
                position:absolute;
                top: 10px;
            }
        }

        .el-dialog.is-fullscreen{
            margin-top: 40px !important;
        }
         // 购买弹窗
        .pay-dialog{
            .el-dialog{
                width: 600px;
                .el-dialog__header{
                    height: 50px !important;
                    padding: 0px 20px !important;
                    background-color: $--color-primary;
                    .el-dialog__title{
                        font-size: 16px;
                        color: white;
                        font-weight: normal;
                        line-height: 50px;
                        height: 50px;
                    }
                }
                .el-dialog__body{
                    padding: 30px 0;
                    padding-top: 0 !important;
                }
                // width: unset !important;
                border-radius: 4px !important;
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
            }
            .pay-channel{
                display: flex;
                flex-direction: row;
                height: 44px;
                font-size: 14px;
                padding-left: 10px;
                margin-bottom: 25px;
                border-bottom: 1px solid $--border-color-lighter;
                background-color: $--background-color-regular;
                color: $--color-text-primary;
                .channel-item{
                    line-height: 44px;
                    margin: 0 20px;
                    cursor: pointer;
                }
            }
            .pay-active{
                color: $--color-primary;
                border-bottom:  2px solid $--color-primary;
            }

            &-pay{
                display: flex;
                flex-direction: row;
                padding-left: 30px;
                padding-right: 120px;
                .wechat-pay-qrcode{
                    margin-right: 30px;
                    position: relative;
                    .qrcode-border{
                        height: 200px;
                    }
                    .qrcode-img-con-ali{
                        position: absolute;
                        width: 135px;
                        height: 135px;
                        left: 9px;
                        top: 9px;
                        background-color: white;
                    }
                    .qrcode-img-con-wechat{
                        position: absolute;
                        width: 130px;
                        height: 130px;
                        left: 12px;
                        top: 10px;
                    }
                    .qrcode-img{
                        width: 130px;
                        height: 130px;
                    }
                    .qrcode-img-ali{
                        width: 120px;
                        height: 120px;
                        padding-top: 15px;
                        padding-left: 15px;
                    }
                }
                &__container{
                    display: flex;
                    font-size: 14px;
                    font-weight: 400;
                    color: $--color-text-primary;
                    flex-direction: column;
                    &__tips {
                        margin-top: 5px;
                        padding-left: 30px;
                        p{
                            list-style-type: auto;
                            font-size: 12px;
                            color: $--color-text-secondary;
                            padding-top: 5px ;
                        }
                    }
                    .open-box{
                        margin-top: 8px;
                        display: flex;
                        .title{
                            width: 75px;
                            font-weight: 500;
                        }
                        .content{
                            font-weight: 400;
                        }
                    }
                    .pay-amount{
                        display: flex;
                        margin-top: 15px;
                        font-size: 14px;
                        font-weight: 500;
                        .title{
                            width: 75px;
                        }
                        span{
                            color: #FF4444;
                            font-weight: 600;
                            padding-right: 5px;
                        }
                    }
                    .pay-btn {
                        margin-top: 10px;
                    }
                }
            }
        }
    }
</style>
