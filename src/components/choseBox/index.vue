<template>
    <el-dialog
        :close-on-click-modal="false"
        :show-close="false"
        :visible="true"
        :before-close="handleClose"
        class="chose-box-dialog"
    >
        <h2 class="title">>{{ $t('choseBoxForReceiver.dataNeedForReceiver') }}<i @click="handleClose" class="el-icon-close fr"></i></h2>
        <div class="content">
            <p class="tip">{{ $t('choseBoxForReceiver.dataFromDataBox') }}</p>
            <el-input :placeholder="$t('choseBoxForReceiver.searchTp')" class="search-input" v-model="query" /><el-button class="dialog-btn" type="primary" @click="search">{{ $t('choseBoxForReceiver.search') }}</el-button>
            <el-radio v-if="searchObject" v-model="searchObject.archiveId" :label="searchObject.archiveId">{{ searchObject.archiveName }}（{{ searchObject.archiveId }}）</el-radio>
            <p class="search-tip" v-if="showSearchTip"><i class="el-icon-ssq-info_hollow"></i>&nbsp;{{ $t('choseBoxForReceiver.boxNotFound') }}</p>
        </div>
        <span slot="footer" class="dialog-footer" v-if="searchObject">
            <el-button class="dialog-btn" @click="handleClose">{{ $t('choseBoxForReceiver.cancel') }}</el-button>
            <el-button class="dialog-btn" type="primary" @click="confirm">{{ $t('choseBoxForReceiver.confirm') }}</el-button>
        </span>
    </el-dialog>
</template>
<script>
export default {
    props: {
        type: {
            type: Number,
            default: 0,
        },
    },
    data() {
        return {
            query: '',
            searchObject: '',
            showSearchTip: false,
        };
    },
    methods: {
        handleClose() {
            this.$emit('close');
        },
        search() {
            this.$http.get(`/octopus-api/box/archive/query?query=${encodeURIComponent(this.query)}&type=${this.type}`).then(res => {
                if (res.data) {
                    this.searchObject = res.data;
                    this.showSearchTip = false;
                } else {
                    this.searchObject = '';
                    this.showSearchTip = true;
                }
            });
        },
        confirm() {
            this.$emit('confirm', this.searchObject.archiveId);
        },
    },
};
</script>
<style lang="scss">
.chose-box-dialog{
    .el-dialog{
        width: 490px;
        .el-dialog__header{
            display: none;
        }
        .el-dialog__body{
            padding: 0;
             .title{
                color: #333333;
                padding: 0 30px;
                line-height: 50px;
                border-bottom: 1px solid #E9E9E9;
                i{
                    color: #999;
                    font-size: 12px;
                    line-height: 50px;
                }
            }
            .content{
                padding: 0 30px 35px 30px;
                .tip{
                    color: #999999;
                    line-height: 48px;
                }
                .search-input{
                    width:360px;
                    height: 30px;
                    line-height: 30px;
                    vertical-align: middle;
                    input{
                        height: 30px;
                    }
                }
                .el-radio{
                    color: #000000;
                    line-height: 18px;
                    overflow: auto;
                    margin-top: 20px;
                }
                .search-tip{
                    margin-top: 20px;
                    color: #999999;
                    font-size: 14px;
                }
            }
        }
        .el-button--primary{
            border-color: #127fd2;
            background: #127fd2;
            color: #fff;
            &:hover{
                background: #1687dc;
                border-color: #1687dc;
            }
        }
        .dialog-btn{
            border-radius: 4px;
            font-size: 14px;
            margin-left: 10px;
            height: 30px;
            line-height: 0;
            vertical-align: middle;
        }
    }
}
</style>
