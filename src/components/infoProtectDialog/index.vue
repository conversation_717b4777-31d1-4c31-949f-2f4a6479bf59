<!-- 上上签如何保护您的个人信息-弹窗 -->
<template>
    <div class="info-protect-dialog-wrap">
        <el-dialog
            class="info-protect-dialog"
            top="25%"
            :visible.sync="dialogVisible"
            :title="$t('infoProtectDialog.title')"
            @close="handleClose"
        >
            <p class="title-line">{{ $t('infoProtectDialog.contentDesp') }}</p>
            <p class="rule-item">{{ $t('infoProtectDialog.detailTip1', {title: title}) }}</p>
            <p class="rule-item">{{ $t('infoProtectDialog.detailTip2', {title: title}) }}</p>
            <p class="rule-item">{{ $t('infoProtectDialog.detailTip3') }}</p>
            <el-button
                slot="footer"
                type="primary"
                class="btn btn-type-one"
                @click="handleClose"
            >
                {{ $t('infoProtectDialog.know') }}
            </el-button>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'InfoProtectDialog',
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        isSignFace: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            dialogVisible: false,
        };
    },
    computed: {
        title() {
            return this.isSignFace ? this.$t('infoProtectDialog.faceSign') : this.$t('infoProtectDialog.auth');
        },
    },
    watch: {
        visible(value) {
            this.dialogVisible = value;
        },
    },
    methods: {
        handleClose() {
            this.$emit('update:visible', false);
            this.$emit('close');
        },
    },
};
</script>

<style lang="scss">
    .info-protect-dialog-wrap {
        .el-dialog {
            width: 600px;
            background-color: #f6f9fc;
            .el-dialog__header {
                padding-bottom: 20px;
                background-color: #fff;
            }
            .el-dialog__title {
                font-size: 16px;
            }
            .el-dialog__body {
                padding: 30px 33px 20px;
            }
            .el-dialog__footer {
                padding-right: 33px;
                padding-bottom: 25px;
                .el-button--primary.btn-type-one {
                    display: inline-block;
                    width: auto;
                    min-width: 100px;
                    margin: 0;
                    height: 30px;
                    line-height: 30px;
                }
            }
        }
        p {
            font-size: 14px;
            color: #333333;
        }
        .title-line {
            margin-bottom: 15px;
        }
        .rule-item {
            margin-bottom: 5px;
        }
    }

</style>
