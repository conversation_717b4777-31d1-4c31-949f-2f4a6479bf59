<template>
    <div class="hubble-apply" @click="openPage">
        <div><i class="el-icon-ssq-gongnengfankui"></i></div>
        <div class="hubble-apply-text">功能反馈</div>
    </div>
</template>

<script>
export default {
    methods: {
        openPage() {
            window.open('https://jinshuju.net/f/I5z9Zb');
        },
    },
};
</script>

<style lang="scss">
$--color-white: #FFFFFF;

.hubble-apply {
    position: fixed;
    right: 0;
    padding: 10px 0;
    bottom: 60px;
    width: 22px;
    height: 84px;
    flex-shrink: 0;
    background-image: linear-gradient(179deg, #128FF3 5.07%, #0C8AEE 97.76%);
    filter: drop-shadow(0 0 10px #0087f34d);
    font-size: 10px;
    color: $--color-white;
    text-align: center;
    border-radius: 2px 0 0 2px;
    cursor: pointer;
    &-text {
        width: 10px;
        margin: auto;
        margin-top: 6px;
        word-break: break-all;
        line-height: 12px;
    }
}
</style>
