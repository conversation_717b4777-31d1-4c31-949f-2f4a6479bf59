<!--
 * template:
 * <SsqDialog v-model="show"></SsqDialog>
 * script:
 * this.show = true;
 -->

<template>
    <div class="SsqDialog-cpn"
        v-show="maskShow"
        @click="close"
    >
        <div class="SsqDialog-content">
            <slot v-if="value" name="content"></slot>
        </div>
    </div>
</template>
<script>
export default {
    props: {
        value: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            maskShow: false,
        };
    },
    watch: {
        value(v) {
            this.maskShow = v;
        },
        maskShow(v) {
            this.$emit('input', v);
        },
    },
    methods: {
        close(e) {
            if (e.target.className.indexOf('SsqDialog-cpn') > -1) {
                this.maskShow = false;
            }
        },
    },
};
</script>
<style lang="scss">
	.SsqDialog-cpn {
		position: fixed;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		background-color: rgba(0,0,0,.7);
		z-index: 999;
		.SsqDialog-content {
			position: fixed;
			top: 50%;
			left: 50%;
			transform: translateX(-50%) translateY(-50%);
			width: 330px;
			// padding-bottom: 10px;
			background-color: #fff;
		}
	}
</style>
