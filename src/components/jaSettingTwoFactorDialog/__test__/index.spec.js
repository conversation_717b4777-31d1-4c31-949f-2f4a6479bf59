import TwoElementVerifyDIalog from '../index.vue';
import { initWrapper } from 'src/testUtils';
jest.mock('src/lang');
const mockEmitFun = jest.fn();
describe('TwoElementVerifyDIalog', () => {
    const baseStoreOptions = {};
    const baseWrapperOptions = {
        mocks: {
            $emit: mockEmitFun,
        },
    };
    const wrapper = initWrapper(TwoElementVerifyDIalog, { ...baseStoreOptions }, { ...baseWrapperOptions });
    test('点击确定', () => {
        wrapper.vm.handleConfirm();
        expect(mockEmitFun).toHaveBeenCalledWith('confirmSaveSetting');
    });
});
