<template>
    <!--设置二要素验证器弹窗-->
    <el-dialog
        :visible.sync="dialogVisible"
        :show-close="true"
        :title="$t('twoFactor.settingTwoFactor')"
        width="600px"
        class="two-factor-setting-dialog"
    >
        <div class="dialog-content">
            <div class="step1">
                <h1>{{ $t('twoFactor.step1') }}</h1>
                <div>{{ $t('twoFactor.step1Tip') }}</div>
                <div>
                    <div><span class="ios-download-address">{{ $t('twoFactor.iosAddress') }}</span><a href="https://apps.apple.com/jp/app/google-authenticator/id388497605" target="_blank">{{ $t('twoFactor.chromeVerify') }}</a></div>
                    <div><span class="android-download-address">{{ $t('twoFactor.androidAddress') }}</span><a href="https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2" target="_blank">{{ $t('twoFactor.chromeVerify') }}</a></div>
                </div>
            </div>
            <div class="step2">
                <h1>{{ $t('twoFactor.step2') }}</h1>
                <div>{{ $t('twoFactor.step2Tip1') }}</div>
                <div>{{ $t('twoFactor.step2Tip2') }}</div>
                <img class="verify-code-img" width="190" alt="" :src="imgUrl" />
            </div>
        </div>
        <div slot="footer" class="dialog-footer">
            <el-button
                type="primary"
                @click="handleConfirm"
            >{{ $t('twoFactor.nextBtn') }}
            </el-button>
        </div>
    </el-dialog>
</template>
<script>
export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            imgUrl: '/users/authenticator/qr-code-image',
        };
    },
    computed: {
        dialogVisible: {
            set(value) {
                this.$emit('update:visible', value);
            },
            get() {
                return this.visible;
            },
        },
    },
    methods: {
        handleConfirm() {
            this.dialogVisible = false;
            this.$emit('confirmSaveSetting');
        },
    },
};
</script>
<style lang="scss">
.two-factor-setting-dialog{
    .el-dialog {
        width: 500px;
    }
    .dialog-content {
        padding: 0 20px;
    }
    h1 {
        border-bottom: 1px solid #eee;
        padding: 15px 0;
    }
    .step1, .step2, .step3 {
        font-size:12px;
        padding: 0 0 10px;
        div {
            padding: 5px 0;
        }
    }
    .el-input {
        width: 300px;
    }
}
</style>
