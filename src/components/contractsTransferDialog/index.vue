<template>
    <el-dialog
        :title="$t('docDialog.contractTransfer')"
        class="el-dialog-bg contract-transfer"
        v-show="visible"
        :visible.sync="visible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :before-close="handleClose"
    >
        <div class="contract-transfer__body">
            <!-- 原持有人列表 -->
            <section class="member-block origin-owner-block">
                <p><!-- 原持有人 -->{{ $t('docDialog.originOwner') }}</p>
                <div class="member-cloumn">
                    <!-- 单选成员 -->
                    <el-radio-group
                        v-model="selectedInfo.sourceEmployeeId"
                    >
                        <div v-for="(ent, index) in originOwnersEnts" :key="index" class="owner-ent">
                            <p class="owner-ent__name">{{ ent.enterpriseName }}</p>
                            <p class="owner-ent__radio"
                                @click.prevent="handleOriginSelectChange(member.employeeId, member.userId, ent.enterpriseId)"
                                v-for="member in ent.employees"
                                :key="member.employeeId"
                            >
                                <el-radio :label="member.employeeId">{{ member.employeeName }}</el-radio>
                            </p>
                        </div>
                    </el-radio-group>
                </div>
            </section>
            <!-- 新持有人 -->
            <section class="member-block company-block">
                <p><!-- 新持有人 -->{{ $t('docDialog.newOwner') }}</p>
                <div class="member-cloumn">
                    <!-- 企业成员列表 -->
                    <div class="member-select-list">
                        <div @keyup.enter.prevent.stop="handleEmployeeSearch">
                            <el-input :placeholder="$t('CSMembers.searchTip')"
                                @blur="handleEmployeeSearch"
                                size="small"
                                class="ssq-search"
                                v-model.trim="filterStr"
                            >
                                <el-button
                                    slot="prepend"
                                    icon="search"
                                    size="small"
                                    @click="handleEmployeeSearch"
                                ></el-button>
                            </el-input>
                        </div>
                        <div class="member-radios-container" v-loading="loading">
                            <!-- 单选成员 -->
                            <el-radio-group
                                v-model="selectedInfo.targetUserId"
                                @change="handleSelectChange"
                            >
                                <p v-for="member in employeeList" :key="member.userId">
                                    <el-radio :disabled="member.userId === selectedInfo.sourceUserId"
                                        :label="member.userId"
                                    >{{ member.empName }} ({{ member.account }})</el-radio>
                                </p>
                            </el-radio-group>
                        </div>
                    </div>
                    <div class="clear"></div>
                </div>
            </section>
            <div class="clear"></div>
        </div>
        <div slot="footer" class="dialog-footer">
            <el-button
                type="primary"
                class="confirm-btn"
                @click="chooseMembers"
            ><!-- 确定 -->{{ $t('docContentTable.confirm') }}</el-button>
        </div>
    </el-dialog>
</template>

<script>
export default {
    props: {
        params: {
            type: Object,
            default: () => {
                return {
                    originOwners: [],
                    selectedContracts: [],
                };
            },
        },
    },
    data() {
        return {
            visible: true,
            selectedInfo: {
                sourceUserId: null, // 原持有人userId
                sourceEmployeeId: null, // 原持有人的企业角色id
                targetUserId: null, // 新持有人
                enterpriseId: null,
            },
            originOwnersEnts: [],
            employeeList: [],
            filterStr: '',
            loading: true,
        };
    },
    computed: {
        isManager() {
            return this.$store.getters.hasManager;
        },
    },
    watch: {
        'selectedInfo.enterpriseId': {
            handler(v) {
                this.getEmployeeList(v);
            },
        },
    },
    methods: {
        // 关闭弹框
        handleClose() {
            this.$emit('close');
        },
        // 选择原持有人
        handleOriginSelectChange(employeeId, userId, entId) {
            this.selectedInfo.sourceUserId = userId;
            this.selectedInfo.sourceEmployeeId = employeeId;
            this.selectedInfo.enterpriseId = entId;
            this.selectedInfo.targetUserId = null;
            this.filterStr = '';
        },
        handleEmployeeSearch() {
            this.getEmployeeList();
        },
        // 选择新持有人
        handleSelectChange(value) {
            this.selectedInfo.targetUserId = value;
        },
        // 选择完成员，传回父组件
        chooseMembers() {
            const { enterpriseId, targetUserId, sourceEmployeeId, sourceUserId } = this.selectedInfo;
            if (!targetUserId) {
                // 请选择新持有人
                return this.$MessageToast.info(this.$t('docDialog.chooseNewOwner'));
            }
            this.$http.post('/contract-api/contracts/deliver', {
                contractIds: this.params.selectedContracts,
                enterpriseId,
                targetUserId,
                sourceEmployeeId: sourceEmployeeId === enterpriseId ? '' : sourceEmployeeId, // 用enterpriseId标记未认领选项，提交时会置空
                sourceUserId: sourceUserId === enterpriseId ? '' : sourceUserId, // 用enterpriseId标记未认领选项，提交时会置空
            }).then((data) => {
                this.params.callback && this.params.callback(data);
            }).finally(() => {
                this.handleClose();
            });
        },
        // 获取员工列表
        getEmployeeList(entId) {
            this.loading = true;
            this.$http.get(`/ents/employees/all-employee?entId=${entId || this.selectedInfo.enterpriseId}&searchContent=${encodeURIComponent(this.filterStr)}`)
                .then((res) => {
                    this.employeeList = res.data;
                }).finally(() => {
                    this.loading = false;
                });
        },
        // 获取原持有人信息
        getOriginOwners() {
            this.$http.post('/contract-api/contracts/enterprises', {
                contractIds: this.params.selectedContracts,
            }).then((resp) => {
                const originOwnersData = resp.data.result || [];

                if (this.isManager) { // 主管理员可以转交未认领合同
                    this.originOwnersEnts = originOwnersData.map((own) => {
                        if (own.hasUnClaimedContracts) { // 有合同未认领
                            own.employees.push({
                                userId: own.enterpriseId, // 用enterpriseId标记未认领选项，提交时会置空
                                employeeId: own.enterpriseId,
                                employeeName: this.$t('docDialog.notCliam'), // '合同未被认领'
                            });
                        }
                        return own;
                    });
                } else {
                    this.originOwnersEnts = originOwnersData.filter(a => a.employees && a.employees.length > 0);
                }
                const hasEmployee = this.originOwnersEnts[0] && this.originOwnersEnts[0].employees[0];
                this.selectedInfo.sourceUserId = hasEmployee ? this.originOwnersEnts[0].employees[0].userId : '';
                this.selectedInfo.sourceEmployeeId = hasEmployee ? this.originOwnersEnts[0].employees[0].employeeId : '';
                this.selectedInfo.enterpriseId = this.originOwnersEnts[0] ? this.originOwnersEnts[0].enterpriseId : '';
            });
        },
    },
    beforeMount() {
        this.getOriginOwners();
    },
};
</script>

<style lang="scss">
.contract-transfer {
    .el-dialog {
        width: 600px;
    }
    .contract-transfer__body {
        padding: 10px 25px;
        height: 350px;
    }

    .member-block{
        display: inline-block;
        float: left;

        .member-cloumn {
            margin-top: 5px;
            height: 330px;
            border: 1px solid #eee;
            background: #fff;
        }
    }

    .origin-owner-block {
        width: 260px;
        margin-right: 12px;
        .member-cloumn {
            overflow-y: auto;

            .owner-ent {
                margin-bottom: 5px;
                &__name {
                    padding: 5px 0 0 10px;
                    line-height: 30px;
                    font-size: 14px;
                    max-width: 225px;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    vertical-align: middle;
                }
                &__radio {
                    padding-left: 25px;
                    height: 35px;
                    line-height: 35px;
                    &__label {
                        padding-left: 12px;
                    }
                }
            }
        }
    }

    .company-block {
        width: 260px;
        .member-select-list {
            width: 245px;
            height: 100%;
            padding: 10px;
            .member-radios-container {
                max-height: 100%;
            }

            p {
                position: relative;
                height: 35px;
                margin-bottom: 5px;
                line-height: 35px;

               .el-radio__label {
                    padding-left: 12px;
                }
            }
            .el-radio-group {
                overflow-y: scroll;
                width: 100%;
                height: 280px;
                .el-radio {
                    &__input {
                        vertical-align: middle;
                    }
                    &__label {
                        display: inline-block;
                        max-width: 225px;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        vertical-align: middle;
                    }
                }
            }
        }
    }

    .confirm-btn {
        min-width: 100px;

        span{
            font-size: 14px;
        }
    }
}
</style>
