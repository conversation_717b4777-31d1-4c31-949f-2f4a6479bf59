<template>
    <div class="contract-extract-sidebar" v-show="extractedTaskId">
        <div v-show="showSideBar" ref="sidebar" class="contract-extract-sidebar__content">
            <i
                ref="move"
                class="contract-extract-sidebar__move el-icon-ssq-y<PERSON><PERSON><PERSON><PERSON><PERSON>"
            ></i>
            <div class="contract-extract-sidebar__header">
                合同摘要
                <i class="el-icon-ssq-delete" @click="showSideBar = false"></i>
            </div>
            <div class="contract-extract-sidebar__keyword">
                <div
                    class="contract-extract-sidebar__keyword-item"
                    :class="{ active: activeKeyword === keyword }"
                    v-for="(keyword, i) in keywordList"
                    :key="i"
                    @click="activeKeyword = keyword"
                >
                    <span>{{ keyword }}</span>
                </div>
            </div>
            <div class="contract-extract-sidebar__outline">
                <div class="contract-extract-sidebar__outline-header">
                    <i class="el-icon-ssq-Hubblehetongxiangqing"></i>
                    大纲
                </div>
                <ul class="contract-extract-sidebar__outline-list">
                    <li
                        class="contract-extract-sidebar__outline-item"
                        v-for="(summary, index) in currentSummaryList"
                        :key="index"
                    >
                        <div class="contract-extract-sidebar__outline-keyword">
                            <span>{{ summary.keyword }}</span>
                        </div>
                        <ul class="contract-extract-sidebar__outline-content-list">
                            <div
                                class="contract-extract-sidebar__outline-content"
                                v-for="(paragraph, idx) in summary.summaryParagraphs"
                                :key="idx"
                            >
                                <div class="contract-extract-sidebar__outline-content-keyword">
                                    <span>{{ paragraph.summaryPoint.chineseText }}</span>
                                    <span v-show="paragraph.summaryPoint.englishText">{{
                                        paragraph.summaryPoint.englishText
                                    }}</span>
                                </div>
                                <div
                                    class="contract-extract-sidebar__outline-content-item"
                                    :class="{
                                        active: activeSentenceIndex === `${index}-${idx}-${i}`,
                                        disabled: !sentence.markPositions,
                                    }"
                                    v-for="(sentence, i) in paragraph.summarySentences"
                                    :key="i"
                                    @click="setActiveSentence(sentence, `${index}-${idx}-${i}`)"
                                >
                                    <span>{{ sentence.summarySentence.chineseText }}</span>
                                    <span v-show="sentence.summarySentence.englishText">{{
                                        sentence.summarySentence.englishText
                                    }}</span>
                                </div>
                            </div>
                        </ul>
                    </li>
                </ul>
            </div>

        </div>
        <div class="contract-extract-sidebar__icon" v-show="!showSideBar" @click="handleShowSideBar">
            <div>合同摘要</div>
            <i class="el-icon-ssq-hubblexingongnnegchouqu"></i>
        </div>
        <!-- <el-button class="contract-extract-sidebar__footer" @click="reGenerate">重新生成</el-button> -->
    </div>
</template>

<script>
import { mapState } from 'vuex';
export default {
    props: {
        receiverId: {
            type: String,
            default: '',
        },
        value: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            loading: true,
            showSideBar: false,
            timeout: null,
            hasGotResult: false,
            extractedTaskId: '',
            summaryResult: [],
            activeKeyword: '',
            activeSentenceIndex: '',
            contractId: this.$route.query.contractId,
        };
    },
    computed: {
        ...mapState(['currentSummarySentence']),
        keywordList() {
            return this.summaryResult.map((item) => item.keyword);
        },
        currentSummaryList() {
            return this.summaryResult.filter(
                (item) => item.keyword === this.activeKeyword,
            );
        },
    },
    methods: {
        handleShowSideBar() {
            if (this.loading) {
                this.$MessageToast('合同摘要生成中，请稍后再试');
                return;
            }
            this.showSideBar = !this.showSideBar;
        },
        setActiveSentence(summarySentence, index) {
            this.activeSentenceIndex = index;
            this.$store.state.currentSummarySentence = summarySentence;
        },
        initMove() {
            const chatHandle = this.$refs.move;
            const sidebar = this.$refs.sidebar;
            let isDragging = false;
            let lastX;
            chatHandle.addEventListener('mousedown', (e) => {
                isDragging = true;
                lastX = e.clientX;
                document.body.style.userSelect = 'none';
            });
            document.addEventListener('mousemove', (e) => {
                const chatWidth = sidebar.offsetWidth;
                const delta = lastX - e.clientX;
                if (!isDragging || (chatWidth <= 211 && delta < 0)) {
                    return;
                }
                sidebar.style.width = `${chatWidth + delta}px`;
                lastX = e.clientX;
            });

            document.addEventListener('mouseup', () => {
                isDragging = false;
                document.body.style.userSelect = 'unset';
            });
        },
        getSummary() {
            return this.$http(`/contract-api/contract/${this.contractId}/receiver/${this.receiverId}/contract-summary`).then((res) => {
                this.summaryResult = res.data;
                this.activeKeyword = this.summaryResult[0]?.keyword;
                this.showSideBar = true;
            }).finally(() => {
                this.loading = false;
            });
        },
        init() {
            const query = async() => {
                const {
                    data: { extractedTaskId, displaySummary, allComplete },
                } = await this.$http.get(`/contract-api/contract/${this.contractId}/receiver/${this.receiverId}/contract-summary-status`);
                this.extractedTaskId = extractedTaskId;
                if (!extractedTaskId) {
                    return;
                }
                if (allComplete) {
                    this.timeout && clearTimeout(this.timeout);
                    return this.getSummary();
                } else if (displaySummary && !this.hasGotResult) {
                    await this.getSummary();
                    this.hasGotResult = true;
                }
                this.timeout = setTimeout(query, 3000);
            };
            query();
        },
    },
    mounted() {
        this.initMove();
    },
    created() {
        this.init();
    },
    beforeDestroy() {
        this.timeout && clearTimeout(this.timeout);
    },
};
</script>

<style lang="scss">
.contract-extract-sidebar {
    &__content{
        width: 211px;
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        z-index: 1000;
        background: #fff;
        box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
        display: flex;
        flex-direction: column;
    }
    &__move {
        position: absolute;
        top: 50%;
        margin-top: -30px;
        left: -16px;
        cursor: col-resize;
        z-index: 999;
        background: #fff;
        box-shadow: -6px 0px 8px 0px rgba(0, 0, 0, 0.1);
        padding: 22px 2px;
        border-radius: 5px 0 0 5px;
    }
    &__header {
        line-height: 38px;
        border-bottom: 1px solid #dddddd;
        padding: 0 16px;
        i {
        float: right;
        margin-top: 13px;
        cursor: pointer;
        }
    }
    &__keyword {
        padding: 11px;
        display: flex;
        flex-wrap: wrap;
        &-item {
        margin: 5px;
        display: flex;
        flex-direction: column;
        padding: 3px 12px;
        background: #f6f6f6;
        border-radius: 3px;
        cursor: pointer;
        &:hover, &.active {
            background: #E7F3FB;
            color: $theme-color;
        }
        span {
            margin-right: 10px;
        }
        }
    }
    &__outline {
        flex-grow: 1;
        min-height: 0;
        margin: 0 16px;
        border: 1px solid #eee;
        border-radius: 3px;
        display: flex;
        flex-direction: column;
        &-header {
        line-height: 36px;
        padding: 0 12px;
        background: #f8f8f8;
        border-bottom: 1px solid #eee;
        }
        &-list {
        margin: 12px 0;
        padding: 0 12px;
        overflow: auto;
        flex-grow: 1;
        }
        &-keyword {
        display: flex;
        flex-direction: column;
        font-size: 14px;
        margin-bottom: 10px;
        }
        &-content {
        margin-left: 2px;
        padding-left: 8px;
        word-break: break-all;
        &-list {
            position: relative;
            &::before {
            content: "";
            position: absolute;
            left: 2px;
            top: 6px;
            bottom: 0;
            width: 1px;
            background: #d8d8d8;
            }
        }
        &-keyword {
            color: #3d3d3d;
            font-size: 13px;
            margin-bottom: 5px;
            position: relative;
            display: flex;
            flex-direction: column;
            &::before {
                content: "";
                position: absolute;
                left: -11px;
                top: 6px;
                height: 5px;
                width: 5px;
                background: #fff;
                border-radius: 5px;
                border: 1px solid #333;
            }
        }
        &-item {
            color: #666;
            font-size: 12px;
            margin-bottom: 5px;
            margin-left: 12px;
            position: relative;
            display: flex;
            flex-direction: column;
            &::before {
                content: "";
                position: absolute;
                left: -7px;
                top: 7px;
                height: 2px;
                width: 2px;
                background: #333;
                border-radius: 2px;
            }
            &:hover, &.active {
                color: $theme-color;
                cursor: pointer;
            }
            &.disabled:hover {
                color: #666;
                cursor: unset;
            }
        }
        }
    }
    &__footer {
        margin: 20px auto;
    }
    &__icon{
        position: fixed;
        top: 50%;
        right: 0;
        line-height: 32px;
        color: $theme-color;
        background: #E7F3FB;
        display: flex;
        border-radius: 5px;
        cursor: pointer;
        &:hover div{
            display: block;
            padding: 0 12px;
        }
        div{
            display: none;
        }
        i{
            display: block;
            line-height: 32px;
            color: #fff;
            background: $theme-color;
            padding: 0 10px;
            border-radius: 5px 0 0 5px;
        }
    }
}
</style>
