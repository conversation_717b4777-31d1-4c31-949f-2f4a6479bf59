<!-- 合同关联组件，在普通发起合同、模板发起合同、合同详情页面展示 -->
<!-- 注意一下模板的使用方式不同，需要手动查询合同Id是否存在并手动渲染，在模板下一步时父组件会取值 -->
<template>
    <div class="link-contracts" :class="{'diseditable': !editable}">
        <h4>{{ $t('linkContract.title') }}</h4>
        <div class="linked-contracts">
            <div class="linked-contracts__item" v-for="(link, index) in linkedContracts" :key="link.id">
                <span class="linked-column linked-CName">
                    <span class="linked-content">
                        <el-tooltip effect="dark" popper-class="doc-content-table-popper">
                            <div slot="content" class="cloumn-signers-detail" style="max-width: 300px;">
                                {{ link.correlateeDetail.contract.contractTitle }}
                            </div>
                            <a href="javascript:void(0)" @click="goDetail(link.correlateeDetail.contract.contractId)">
                                {{ link.correlateeDetail.contract.contractTitle }}
                            </a>
                            <!-- <router-link :to="`/doc/detail/${link.correlateeDetail.contract.contractId}`" target="_blank"></router-link> -->
                        </el-tooltip>
                    </span>
                </span>
                <span class="linked-column linked-CId">
                    <span class="linked-content">{{ link.correlateeDetail.contract.contractId }}</span>
                </span>
                <span class="linked-column linked-CSigners">
                    <el-tooltip effect="dark" popper-class="doc-content-table-popper">
                        <div slot="content" class="cloumn-signers-detail" style="max-width: 300px;">
                            {{ renderSigners(link.correlateeDetail.receivers) }}
                        </div>
                        <span class="linked-content">{{ renderSigners(link.correlateeDetail.receivers) }}</span>
                    </el-tooltip>
                </span>
                <span class="linked-column linked-CStatus">
                    <span class="linked-content">
                        <span class="status-btn" :class="cancelStateMap.get(link.correlateeDetail.contract.contractStatus) && 'fail'">{{ allStateMap.get(link.correlateeDetail.contract.contractStatus) }}</span>
                    </span>
                </span>
                <span class="linked-column linked-COperation">
                    <span class="linked-content">
                        <i class="el-icon-ssq--bs-guanbi" @click="handleDelLink(link, index)"></i>
                    </span>
                </span>
            </div>
        </div>
        <div class="link-more">
            <p v-if="!linkOperationVisible" class="link-more__tip" @click="checkCouldLink"><i class="el-icon-ssq-jia"></i>{{ $t('linkContract.connectMore') }}</p>
            <p v-else>
                <el-input v-model="contractIdToLink" :placeholder="$t('linkContract.placeholder')"></el-input>
                <span class="link-more__btn" @click="handleToLink">{{ $t('sign.submit') }}</span>
                <span class="link-more__btn" @click="handleCancelLink">{{ $t('sign.cancel') }}</span>
            </p>
        </div>
    </div>
</template>
<script>
import { resendTemplateMinxin } from 'src/mixins/resendTemplate.js';

import { openPage } from 'common_pages/doc/list/list.js';
import { mapGetters } from 'vuex';
export default {
    mixins: [resendTemplateMinxin],
    // eslint-disable-next-line vue/require-prop-types
    props: ['linkedContractId', 'linkTempId', 'editable'],
    data() {
        return {
            linkedContracts: [],
            contractIdToLink: null,
            linkOperationVisible: false,
        };
    },
    computed: {
        ...mapGetters(['getCurrentEntInfo', 'checkFeat']),
        // 判断是否是模板绑定合同，处理不同交互
        fromTemplate() {
            return this.linkTempId !== void (0) && this.linkTempId !== null && this.linkTempId !== '';
        },
        // 模板下一步时提交给后端的值，只取其中的 correlateeDetail
        tempSubmitLinks() {
            return this.linkedContracts.map(item => {
                return item.correlateeDetail;
            });
        },
        cancelState() {
            return [['REVOKE_CANCEL', this.$t('linkContract.revoke')], ['OVERDUE', this.$t('linkContract.overdue')], ['SEND_APPROVAL_NOT_PASSED', this.$t('linkContract.approvalNotPassed')], ['REJECT', this.$t('linkContract.reject')]];
        },
        successState() {
            return [['SENT', this.$t('linkContract.signing')], ['COMPLETE', this.$t('linkContract.complete')], ['IN_SEND_APPROVAL', this.$t('linkContract.approvaling')]];
        },
        cancelStateMap() {
            return new Map(this.cancelState);
        },
        allStateMap() {
            return new Map([...this.successState, ...this.cancelState]);
        },
    },
    watch: {
        linkedContractId: {
            handler(v) {
                v && this.getLinkedList();
            },
            immediate: true,
        },
        linkTempId: {
            handler(v) {
                v && this.getTempLinkedList();
            },
            immediate: true,
        },
    },
    methods: {
        // 过滤返回签署人
        filterSigners(receivers) {
            return receivers.filter(item => {
                return item.receiverType === 'SIGNER';
            });
        },
        // 渲染签署人名称
        renderSigners(receivers) {
            return this.filterSigners(receivers).map(item => {
                if (item.userType === 'ENTERPRISE') {
                    return `${item.enterpriseName}(${item.employeeName || item.userName || this.$t('docDetail.entAccount')})`;
                } else {
                    return `${item.userName || item.userAccount}(${this.$t('docDetail.personAccount')}})`;
                }
            }).toString().replace(/,/g, '、');
        },
        // 获取已关联合同列表
        getLinkedList() {
            this.$http(`/contract-center-bearing/correlation/list?correlatorContractNo=${this.linkedContractId}`)
                .then(res => {
                    this.linkedContracts = res.data.data;
                });
        },
        // 获取模板已关联合同的列表
        getTempLinkedList() {
            let url = `/template-api/multiple-dynamic-signer/${this.linkTempId}/correlation-param-cache`;
            url = this.handleRequestLink(url);
            this.$http(url)
                .then(res => {
                    const result = res.data || [];
                    this.linkedContracts = [];
                    result.forEach(item => {
                        this.linkedContracts.push({
                            id: Math.random().toString(36).substr(2),
                            correlateeDetail: item,
                        });
                    });
                });
        },
        // 解除关联，二次确认
        handleDelLink(obj, index) {
            if (this.fromTemplate) {
                this.delTempLink(index);
                return false;
            }
            const h = this.$createElement;
            const msg = this.$t('docSlider.linkContractMap.dissolveContractTip');
            this.$msgbox({
                title: this.$t('linkContract.disconnect'),
                message: h('p', null, [
                    h('span', null, msg), // 确定要解除关联合同
                    h('span', { style: 'color: #127FD2' }, obj.correlateeDetail.contract.contractTitle),
                    h('span', null, '？'),
                ]),
                showCancelButton: true,
            }).then(() => {
                this.confirmDelContractLink(obj);
            }).catch(() => {});
        },
        // 确认解除合同关联
        confirmDelContractLink(obj) {
            this.$http.post(`/contract-center-bearing/correlation/${obj.id}/revoke/`, {
            }).then(() => {
                this.$MessageToast(this.$t('linkContract.disconnectSuccess'));
                this.getLinkedList();
            });
        },
        // 解除模板-合同关联，前端操作
        delTempLink(index) {
            this.linkedContracts.splice(index, 1);
        },
        // 检查是否达到关联数量上限
        checkCouldLink() {
            if (!this.checkFeat.contractRelate) {
                // 如果乐高城没开通此功能
                this.$MessageToast(this.$t('docSlider.linkContractMap.notSupportFuctionTip')); // 贵司未开通该功能，可联系客服开通。
                return;
            }
            if (this.linkedContracts.length < 100) {
                this.linkOperationVisible = true;
            } else {
                this.$MessageToast(this.$t('linkContract.connectLimit'));
            }
        },
        // 关联合同
        handleToLink() {
            if (!this.contractIdToLink) {
                this.$MessageToast(this.$t('docSlider.linkContractMap.inputContractNum')); // 请先输入合同编号
                return false;
            }

            this.fromTemplate ? this.linkTemplateId() : this.linkContractId();
        },
        // 与合同 id 相关联
        linkContractId() {
            this.$http.post('/contract-center-bearing/correlation/add/', {
                correlatorContractNo: this.linkedContractId,
                correlateeContractNo: this.contractIdToLink,
            }).then(() => {
                this.$MessageToast(this.$t('docSlider.linkContractMap.linkSuccess')); // 关联成功
                this.handleCancelLink();
                this.getLinkedList();
            });
        },
        // 与模板 id 相关联
        linkTemplateId() {
            this.$http(`/contract-api/contracts/info-with-receivers/${this.contractIdToLink}`)
                .then(res => {
                    if (this.linkedContracts.filter(item => {
                        return item.correlateeDetail.contract.contractId === this.contractIdToLink;
                    }).length) {
                        this.$MessageToast(this.$t('docSlider.linkContractMap.linkExist')); // 合同关联已存在
                    } else {
                        this.linkedContracts.push({
                            id: Math.random().toString(36).substr(2),
                            correlateeDetail: res.data.result,
                        });
                        this.$MessageToast(this.$t('docSlider.linkContractMap.linkSuccess')); // 关联成功
                        this.handleCancelLink();
                    }
                });
        },
        // 取消关联
        handleCancelLink() {
            this.contractIdToLink = null;
            this.linkOperationVisible = !this.linkOperationVisible;
        },
        // 合同详情
        goDetail(contractId) {
            openPage({ url: `/doc/detail/${contractId}`, contractId, entId: this.getCurrentEntInfo.entId, operation: 'DETAIL' });
        },
    },
};
</script>
<style lang="scss">
    @import './linkContracts.scss';
</style>
