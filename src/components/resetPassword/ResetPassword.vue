<template>
    <div class="reset-password" v-if="show" :class="{'en-page': lang.locale === 'en'}">
        <el-dialog
            :title="lang.t('resetPwd.title')"
            :visible.sync="show"
            :show-close="canClose"
            :class="{ 'isPhone': !isPC }"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
        >
            <p class="notice">
                <i class="el-icon-ssq-xuyaowoqianshu"></i>
                <span>{{ lang.t('resetPwd.notice') }}</span>
            </p>
            <el-form :model="formData" :rules="rules" ref="ruleForm" class="reset-password__form" :label-width="isPC ? '80px' : ''">
                <el-form-item :label="lang.t('resetPwd.oldLabel')" prop="oldPwd">
                    <el-input
                        type="password"
                        :placeholder="lang.t('resetPwd.oldPlaceholder')"
                        auto-complete="new-password"
                        v-model="formData.oldPwd"
                    >
                    </el-input>
                </el-form-item>
                <el-form-item :label="lang.t('resetPwd.newLabel')" prop="newPwd">
                    <el-input
                        type="password"
                        :placeholder="lang.t('resetPwd.newPlaceholder')"
                        auto-complete="new-password"
                        v-model="formData.newPwd"
                    >
                        <ElIEye slot="icon"></ElIEye>
                    </el-input>
                </el-form-item>
                <el-form-item class="submit">
                    <el-button class="btn-type-one" @click="formSubmit">{{ lang.t('resetPwd.submit') }}</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>

<script>
import i18n from 'src/lang';
import { isPC } from 'src/common/utils/device.js';
import { isComplexPass } from 'utils/reg.js';
import ElIEye from 'components/el_i_eye/ElIEye.vue';
export default {
    components: {
        ElIEye,
    },
    data() {
        return {
            isPC: isPC(),
            show: true,
            formData: {
                oldPwd: '',
                newPwd: '',
            },
            lang: i18n,
            canClose: false, // CFD-10313 更换密码弹窗改成不允许关闭
        };
    },
    computed: {
        // 已废弃，因为在 CFD-10313 默认不允许关闭
        // huaweiUserFlag() {
        //     return store.state.commonHeaderInfo.huaweiUserFlag;
        // },
        rules() {
            return {
                oldPwd: [
                    { required: true, message: this.lang.t('resetPwd.oldRule'), trigger: 'blur' },
                ],
                newPwd: [
                    { required: true, message: this.lang.t('resetPwd.newRule'), trigger: 'blur' },
                ],
            };
        },
    },
    watch: {
        show(val) {
            if (!val) {
                this.$localStorage.remove('isPwdLogin');
            }
        },
    },
    methods: {
        formSubmit() {
            const checkNewPwd = () => {
                if (isComplexPass(this.formData.newPwd)) {
                    this.changeLoginCode();
                } else {
                    this.formData.newPwd = '';
                }
            };
            this.$refs.ruleForm.validate(val => {
                val && checkNewPwd();
            });
        },
        // 更改登录密码
        changeLoginCode() {
            this.$http.put('/users/login-pwd', {
                newPwd: this.formData.newPwd,
                oldPwd: this.formData.oldPwd,
            }).then(() => {
                this.$MessageToast({
                    message: this.lang.t('resetPwd.success'),
                    type: 'success',
                });
                this.show = false;
            });
        },
    },
};
</script>

<style lang="scss">
    .reset-password{
        &.en-page{
            .el-dialog .notice{
                line-height: 24px;
            }
        }
        .el-dialog{
            width: 436px;
            border-radius: 4px;
            .el-dialog__title{
                font-weight: normal;
            }
            .el-dialog__body{
                padding: 81px 20px 10px;
            }
            .notice{
                color: #333;
                font-size: 12px;
                line-height: 35px;
                background: #FFEEE6;
                width: 100%;
                position: absolute;
                top: 57px;
                left: 0;
                z-index: 2;
                text-align: center;
                i{
                    color: #FC653C;
                    font-size: 14px;
                }
            }
            .el-form-item{
                margin-bottom: 20px;
                .el-form-item__content{
                    width: 280px;
                    line-height: 30px;
                }
                .el-form-item__label{
                    padding: 8px 12px 8px 0;
                }
                .el-input__inner{
                    height: 30px;
                }
                .el-button{
                    padding: 6px 25px;
                }
            }
        }
        .isPhone .el-dialog{
            width: 90%;
        }
    }
</style>
