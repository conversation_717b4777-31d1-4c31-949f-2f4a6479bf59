<!-- 业务组件：功能介绍留资基础组件 || 功能介绍试用 || 试用结束 弹框-->
<!-- 引用位置 低版本可见不可用功能的介绍、提示、留资、试用组件-->
<template>
    <div class="function-support">
        <!-- 留资弹窗 -->
        <el-dialog
            :visible.sync="dialogInfo.isShowLeaveMsg"
            :modal-append-to-body="true"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :before-close="handleClose"
            :class="{ 'isPhone': !isPC }"
            class="leave-msg"
        >
            <div slot="title" class="top-title-box">
                <i class="el-icon-ssq-tongzhi"></i>
                <p class="dialog-title">{{ isLeaveMsg ? lang.t('functionSupportDialog.title') :lang.t('functionSupportDialog.applyTrial') }}</p>
            </div>
            <p v-if="isLeaveMsg" class="intro top-intro">{{ lang.t('functionSupportDialog.title') }}：</p>
            <p v-if="isLeaveMsg" style="white-space: pre-wrap;" class="intro">{{ introContent }}</p>
            <div class="input-tip">{{ isLeaveMsg ? lang.t('functionSupportDialog.inputTip'): lang.t('functionSupportDialog.trialTip') }}</div>

            <el-form label-position="top"
                :model="formData"
                :rules="rules"
                ref="ruleForm"
                class="function-support__form"
                :label-width="isPC ? '120px' : ''"
            >
                <el-form-item label="请选择您的岗位" prop="position">
                    <el-select v-model="formData.position" placeholder="请选择您的岗位">
                        <el-option
                            v-for="item in options"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item :label="lang.t('functionSupportDialog.useSence')" prop="useSence">
                    <el-checkbox-group v-model="formData.useSence">
                        <el-checkbox v-for="(item,index) in useSenceCheckList" :label="item" :key="index">{{ item }}</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item :label="lang.t('functionSupportDialog.requireContent')" prop="requireConent">
                    <el-input
                        type="textarea"
                        :rows="4"
                        :maxlength="200"
                        show-word-limit
                        :placeholder="lang.t('functionSupportDialog.requireContentTip')"
                        v-model="formData.requireConent"
                    >
                        <ElIEye slot="suffix"></ElIEye>
                    </el-input>
                </el-form-item>

            </el-form>
            <div v-if="isLeaveMsg" class="bottom-content">
                <el-button class="btn-type-one" @click="formSubmit">{{ lang.t('functionSupportDialog.getSupport') }}</el-button>
                <div class="phone-tip">{{ lang.t('functionSupportDialog.callServiceHotline') }}</div>
            </div>
            <div v-else slot="footer" class="dialog-footer">
                <el-button @click="formSubmit" type="primary">{{ lang.t('functionSupportDialog.submitTrial') }}</el-button>
                <el-button @click="handleClose">{{ lang.t('download.cancel') }}</el-button>
            </div>

        </el-dialog>
        <!-- 功能介绍 -->
        <el-dialog
            :visible.sync="dialogInfo.isShowIntro"
            :modal-append-to-body="true"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :fullscreen="true"
            :before-close="handleClose"
            :class="{ 'isPhone': !isPC }"
            class="intro-dialog"
        >
            <div slot="title" class="top-title-box">
                <i class="el-icon-ssq-tongzhi"></i>
                <p class="dialog-title">{{ lang.t('functionSupportDialog.title') }}</p>
            </div>
            <img v-if="imgSrc" :src="imgSrc" alt="" class="img-box">
            <div v-else class="img-box"></div>
            <div class="vedio-container">
                <div class="title">
                    功能演示：
                </div>
                <div v-for="item in vedioIntroList"
                    :key="item.featureId"
                    class="vedio-box"
                    :class="vedioIntroList.length > 1 && 'multi-vedio'"
                    @click="handleViewVedioClick(item.link)"
                >
                    <span class="name">{{ item.title }}</span>
                    <i class="iconfont el-icon-ssq-yindaojiantou" />
                </div>
            </div>
            <div class="tip-box">
                <img src="~img/bestsign-market.png" alt="" class="contract-customer">
                <div class="trial-instruct">
                    <p>{{ lang.t('functionSupportDialog.trialTipMap.title') }}：</p>
                    <p>{{ lang.t('functionSupportDialog.trialTipMap.tip1') }}</p>
                    <p>{{ lang.t('functionSupportDialog.trialTipMap.tip2') }}</p>
                    <p>{{ lang.t('functionSupportDialog.trialTipMap.tip3') }}</p>
                    <p>{{ lang.t('functionSupportDialog.trialTipMap.tip4') }}</p>
                    <p>{{ lang.t('functionSupportDialog.trialTipMap.tip5') }}</p>
                </div>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="handleTrialClick" :disabled="isTrialed" type="primary">{{ lang.t('functionSupportDialog.toTrial') }}</el-button>
                <el-button v-if="!isLearnDetail" @click="handleBuyClick">{{ lang.t('functionSupportDialog.goBuy') }}</el-button>
            </div>
        </el-dialog>
        <!-- 试用结束 -->
        <el-dialog
            :visible.sync="dialogInfo.isShowTrialEnd"
            :modal-append-to-body="true"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :before-close="handleClose"
            :class="{ 'isPhone': !isPC }"
            class="trial-end-dialog"
        >
            <div slot="title" class="top-title-box">
                <i class="el-icon-ssq-tongzhi"></i>
                <p class="dialog-title">{{ lang.t('functionSupportDialog.trialEnd') }}</p>
            </div>
            <div v-for="item in trialEndList" :key="item.featureId" class="conent-box">

                <p class="title">
                    <i class="el-icon-ssq-tishizhuangtai"></i>{{ item.titleTip() }}
                </p>
                <p class="clean-tip"> {{ item.cleanMethodTip() }}</p>
                <img v-if="item.img" :src="item.img" alt="" class="img-box">

            </div>
            <img v-if="imgSrc" :src="imgSrc" alt="" class="img-box">
            <div slot="footer" class="dialog-footer">
                <el-button @click="handleClose">{{ lang.t('download.cancel') }}</el-button>
                <el-button @click="handleBuyClick" type="primary">{{ lang.t('functionSupportDialog.goBuy') }}</el-button>
            </div>
        </el-dialog>
        <!-- 购买弹窗 -->
        <QuickPay
            v-if="dialogInfo.isShowBuy"
            :dialogVisible="dialogInfo.isShowBuy"
            type="advanceFeature"
            :featuredIdList="featuredIdData"
            @handlePaySuccess="handleReGetAdvancedFeatureInfo"
        >
        </QuickPay>
    </div>
</template>
<script>
import { isPC } from 'src/common/utils/device.js';
import { featureIdConfig } from 'src/common/const/featureMap.js';
import i18n from 'src/lang';
import {  mapGetters } from 'vuex';
import { TRIAL_FEATURE_END, FEATURE_VEDIO_LIST } from './const.js';
import QuickPay from 'src/components/QuickPay';

export default {
    components: {
        QuickPay,
    },
    data() {
        return {
            isPC: isPC(),
            lang: i18n,
            dispatch: null,
            formData: {
                useSence: [], // 使用场景
                requireConent: '', // 需求内容
                position: '', // 岗位
            },
            rules: {
                position: [
                    { required: true, message: '岗位不能为空', trigger: 'change' }, // 岗位不能为空
                ],
                useSence: [
                    { type: 'array', required: true, message: i18n.t('functionSupportDialog.useSenceNotEmpty'), trigger: 'change' }, // 使用场景不能为空
                ],

            },
            options: [
                {
                    value: '企业法人',
                    label: '企业法人',
                },
                {
                    value: '人力资源',
                    label: '人力资源',
                },
                {
                    value: '采购管理',
                    label: '采购管理',
                },
                {
                    value: '财务管理',
                    label: '财务管理',
                },
                {
                    value: '法务管理',
                    label: '法务管理',
                },
                {
                    value: '市场营销',
                    label: '市场营销',
                },
                {
                    value: '产品设计',
                    label: '产品设计',
                },
                {
                    value: 'IT技术',
                    label: 'IT技术',
                },
            ],
            useSenceCheckList: ['人事', '采购销售', '物流单据', '知识产权', '教育培训', '广告协议', '商务合作', '其他'],
            calFeatureId: '', // 功能的featureId
            introContent: '', // 功能介绍文案
            type: '', // 试用:'' ; 留资:leaveMsg'; 了解详情：'learnDetail'
            optionData: {}, // data数据
            imgSrc: '', // 功能介绍图
            adminInfo: {}, // 主管理员信息
            dialogInfo: {
                isShowLeaveMsg: false, // 留资弹窗是否显示
                isShowIntro: false, // 功能介绍弹窗
                isShowTrialEnd: false, // 试用结束
                isShowBuy: false, // 购买弹窗
            },
            featuredIdData: [], // 高级功能要开通的featureId数组
            enterPageDate: '', // 进页面时间
        };
    },
    computed: {
        ...mapGetters([
            'checkAdvancedFeatureData',
            'getUserPermissons',
        ]),
        isManager() {
            return this.$store.getters.hasManager;
        },
        isLeaveMsg() { // 是否是留资类型
            return this.type !== 'learnDetail' && this.type !== 'trial';
        },
        isLearnDetail() { // 是否为了解详情
            return this.type === 'learnDetail';
        },
        isTrialed() { // 是否已经试用过
            const data = this.checkAdvancedFeatureData(this.calFeatureId);
            // onTrial(0:未试用，1：试用中，2：试用到期, 3:已购买)
            return data.onTrial !== 0;
        },
        isTrialEnd() { // 是否试用功能结束
            return this.type === 'trialEnd';
        },
        trialEndList() { // 试用功能结束弹窗信息
            if (!this.optionData || !this.optionData.endData) {
                return [];
            }
            const endData = (Object.keys(this.optionData.endData) || []).filter(item => !this.optionData.endData[item]);
            return TRIAL_FEATURE_END.filter(item => endData.includes(item.key));
        },
        // 功能演示信息List
        vedioIntroList() {
            const info = FEATURE_VEDIO_LIST.find(item => item.featureId === this.calFeatureId);
            return info?.data;
        },
    },
    watch: {
        optionData: {
            handler(val) {
                if (val.featureKey) {
                    const map = featureIdConfig.find(e => e.key === val.featureKey);
                    this.calFeatureId = map ? map.featureId : '';
                } else if (val.featureId) {
                    this.calFeatureId = val.featureId;
                }
                this.type =  val.type;
                if (this.calFeatureId) {
                    this.fetchFeatureGuidance();
                }
                if (this.type) {
                    this.fetchAdminInfo(); // 获取主管理员信息
                }
            },
            immediate: true,
            deep: true,
        },
    },
    methods: {
        formSubmit() {
            this.$refs.ruleForm.validate(val => {
                val && this.submitFunctionRequire();
            });
        },
        handleClose() {
            let pageName = '';
            if (this.dialogInfo.isShowLeaveMsg) {
                pageName = '留资页';
            } else if (this.dialogInfo.isShowIntro) {
                pageName = '功能介绍页';
            } else if (this.dialogInfo.isShowBuy) {
                pageName = '支付页';
            }
            const duration = (Date.parse(new Date()) - this.enterPageDate) / 1000;
            if (pageName && duration > 0) {
                this.handleSensorsTrack('pageLeave', {
                    'page_name': pageName,
                    $event_duration: duration,
                });
            }
            this.dialogInfo.isShowLeaveMsg = false;
            this.dialogInfo.isShowIntro = false;
            this.dialogInfo.isShowTrialEnd = false;
            this.dialogInfo.isShowBuy = false;
            this.enterPageDate = Date.parse(new Date());
        },
        // 获取Feature的功能介绍内容
        fetchFeatureGuidance() {
            // 获取功能介绍文案
            this.$http.get(`/ents/feature-guidance/${this.calFeatureId}/introduction`).then((res) => {
                this.introContent = res.data.value;
                this.handleSensorsTrack('pageView', {
                    'page_name': '功能介绍页',
                });
            });
            // 获取功能介绍图
            this.imgSrc = `/ents/feature-guidance/${this.calFeatureId}/introductionPic`;
        },
        // 获取主管理员信息
        fetchAdminInfo() {
            this.$http.get(`/ents/employees/mask-admin-info`).then((res) => {
                this.adminInfo = res.data;
            });
        },
        // 提交功能支持
        async submitFunctionRequire() {
            const params = {
                scenes: this.formData.useSence.join('、'),
                featureId: this.calFeatureId,
                post: this.formData.position,
                addInfo: this.formData.requireConent,
                onTrial: !this.isLeaveMsg,
            };
            const trackParams = {
                'page_name': '留资页',
                'post_name': params.post,
                'scene_list': this.formData.useSence,
                'content': params.addInfo,
                'icon_name': '立即试用',

            };
            this.handleSensorsTrack('btnClick', trackParams);
            await this.$http.post('/ents/feature-guidance/information/submit', params);
            this.handleSensorsTrack('urlResult', {
                ...trackParams,
                'icon_name': '立即试用',
                is_success: true,
                request_url: '/ents/feature-guidance/information/submit',
            });
            this.$MessageToast({
                message: this.isLeaveMsg ? i18n.t('functionSupportDialog.submitSuccess') : i18n.t('functionSupportDialog.trialSuccTip'), // 修改成功
                type: 'success',
            });
            this.dialogInfo.isShowLeaveMsg = false;
            // 重新获取高级功能信息
            this.handleReGetAdvancedFeatureInfo();
            this.dispatch('resolve');
        },
        // 重新获取高级功能信息
        async handleReGetAdvancedFeatureInfo() {
            this.handleClose();
            const res =  await Vue.$http.get('/ents/advanced-features-detail');
            this.$store.commit('pushAdvancedFeatures', res.data);
            // 乐高成-客户管理/客户配置，即商品化不同版本的功能点
            const { data } =  await Vue.$http.get('/users/features');
            this.$store.commit('pushFeatures', data);
        },
        // 去试用点击
        handleTrialClick() {
            this.handleSensorsTrack('btnClick', {
                'page_name': '功能介绍页',
                'icon_name': '去试用',
            });
            this.handleSensorsTrack('pageLeave', {
                'page_name': '功能介绍页',
                $event_duration: (Date.parse(new Date()) - this.enterPageDate) / 1000,
            });
            this.enterPageDate = Date.parse(new Date());
            this.dialogInfo.isShowIntro = false;
            this.dialogInfo.isShowLeaveMsg = true;
        },
        // 查看功能演示vedio
        handleViewVedioClick(link) {
            this.handleSensorsTrack('btnClick', {
                'page_name': '功能介绍页',
                'icon_name': '功能介绍',
            });
            window.open(link);
        },
        // 直接购买点击
        handleBuyClick() {
            this.handleSensorsTrack('btnClick', {
                'page_name': '功能介绍页',
                'icon_name': '直接购买',
            });
            this.handleSensorsTrack('pageLeave', {
                'page_name': '功能介绍页',
                $event_duration: (Date.parse(new Date()) - this.enterPageDate) / 1000,
            });
            this.enterPageDate = Date.parse(new Date());
            if (!this.getUserPermissons.recharge_m) { // 无充值权限
                this.$MessageToast(i18n.t('functionSupportDialog.contactAdminTip', { tip: ` ${this.adminInfo.empName}(${this.adminInfo.account}) ` }));
                return;
            }
            this.handleClose();
            let data = [this.calFeatureId];
            if (this.isTrialEnd) {
                data = this.trialEndList.map(item => item.featureId);
            }
            this.featuredIdData = data;
            this.dialogInfo.isShowIntro = false;
            this.dialogInfo.isShowBuy = true;
        },
        handleSensorsTrack(type, params = {}) {
            const eventName = 'Ent_CommodityIntroduce_PageView';
            const eventMap = {
                btnClick: 'Ent_CommodityIntroduce_BtnClick', // 云平台_商品介绍页_按钮点击
                pageView: 'Ent_CommodityIntroduce_PageView', // 云平台_商品介绍页_页面浏览
                urlResult: 'Ent_CommodityIntroduce_Result', // 云平台_商品介绍页_结果
                pageLeave: 'Ent_CommodityIntroduce_PageLeave', // 云平台_商品介绍页_页面离开
            };
            const info = FEATURE_VEDIO_LIST.find(item => item.featureId === this.calFeatureId);
            const eventParams = {
                'previous_page_name': this.isLearnDetail ? '控制台' : '功能入口处',
                'commodity_id': this.calFeatureId, // 商品id
                'commodity_name': info.featureName, // 商品名称
            };
            this.$sensors.track({
                eventName: eventMap[type] || eventName,
                eventProperty: {
                    ...eventParams,
                    ...params,
                },
            });
        },
    },
    created() {
        this.enterPageDate = Date.parse(new Date());
    },
};
</script>

<style lang="scss">
    $--color-primary: #127fd2 !default;
    $--color-text-primary: #333333 !default;
    /// 次要文字颜色 color|1|Font Color|2
    $--color-text-secondary: #999999 !default;
    $--color-warning: #F2A93E !default;
    .function-support{
        color: white;
        .el-dialog__header{
            height: 50px !important;
            padding: 0px 30px;
            background-color: $--color-primary;
            .el-dialog__title{
                font-size: 16px;
                color: white;
                font-weight: normal;
                line-height: 50px;
                height: 50px;
            }
        }
        .top-title-box{
            display: flex;
            flex: 1;
            align-content: center;
            .el-icon-ssq-tongzhi {
                padding-top: 15px;
                font-size: 20px;
            }
            .dialog-title{
                padding-top: 14px;
                font-size: 16px;
                margin-left: 10px;
            }
        }

        .el-dialog.is-fullscreen{
            margin-top: 40px !important;
        }
        //功能介绍弹窗
        .intro-dialog .el-dialog{
            width: 860px;
            max-height: 680px;
            overflow-y: auto;
            border-radius: 4px;
            top: 5% !important;
            .el-dialog__header{
                display: flex;
                height: 50px !important;
                padding: 0px 30px;
            }
            .el-dialog__body{
                padding-top: 0;
                padding-bottom: 0;
            }
            .img-box{
                width: 100%;
                height: 302px;
                text-align: center;
                margin-top: 20px;
            }
            .vedio-container{
                display: flex;
                height: 34px;
                margin:  20px 0;
                font-size: 12px;
                align-items: center;
                justify-content: flex-start;
                .title{
                    padding: 8px 20px;
                    border-radius: 4px 0px 0px 4px;
                    color: #FFFFFF;
                    background-color: $--color-primary;
                }
                .vedio-box{
                    display: flex;
                    flex: 1;
                    padding: 8px 12px 8px 20px;
                    border-radius: 0px 4px 4px 0px;
                    cursor: pointer;
                    background-color: #F6F9FC;
                    .name{
                        color: $--color-primary;
                        flex: 1;
                    }
                    .el-icon-ssq-yindaojiantou{
                        font-size: 12px;
                        margin-top: 2px;
                        color: $--color-primary;
                    }
                }
                .multi-vedio{
                    margin-left: 10px !important;
                    border-radius:4px !important;
                }
            }
            .tip-box{
                padding-top: 10px;
                display: flex;
                p{
                    height: 25px;
                    line-height: 25px;
                }
                .contract-customer{
                    width: 150px;
                    height: 150px;
                    margin-right: 20px;
                }
            }
        }

        //留资弹窗
        .leave-msg .el-dialog{
            width: 500px;
            max-height: 730px;
            overflow-y: auto;
            border-radius: 4px;
            .el-dialog__header{
                display: flex;
                height: 50px !important;
                padding: 0px 30px;
            }
            .el-dialog__body{
                padding-top: 0;
                padding-bottom: 0;
            }
            .top-intro{
                padding-top: 20px !important;
            }
            .intro{
                padding-top: 5px;
                font-size: 14px;
                color: #333333;
                font-weight: normal;
            }
            .input-tip{
                padding: 25px 15px;
                font-size: 14px;
                margin-top: 20px;
                background: #F6F6F6;
                color: #999999;
                font-weight: normal;
            }
            .function-support__form{
                padding: 25px 0 0 0;
            }

            .el-form-item{
                margin-bottom: 20px;
                .el-form-item__content{
                    line-height: 30px;
                }
                .el-form-item__label{
                    word-break: break-word;
                    white-space: normal;
                    line-height: 1;
                    padding: 8px 12px 8px 0;
                }
                .el-input__inner{
                    height: 30px;
                }
                .el-select{
                    width: 460px;
                }
                .el-button{
                    padding: 6px 25px;
                }
                .el-checkbox{
                    width: 80px;
                }
                .el-checkbox+.el-checkbox{
                    margin-left: 0;
                }
            }
            .bottom-content{
                margin-top: 18px;
                text-align: center;
                .phone-tip{
                    margin-top: 10px;
                    font-size: 12px;
                    color: #999999;
                    font-weight: normal;
                }
            }
        }

        //试用功能结束弹窗
        .trial-end-dialog .el-dialog{
            width: 560px;
            max-height: 570px;
            overflow-y: auto;
            border-radius: 4px;
            .el-dialog__header{
                display: flex;
                height: 50px !important;
                padding: 0px 30px;
            }
            .el-dialog__body{
                padding-top: 0;
                padding-bottom: 20px;
            }
            .conent-box{
                text-align: center;
                margin-top: 15px;
                .title{
                    text-align: start;
                    font-size: 14px;
                    color: $--color-text-primary;
                    word-break: break-word;
                    white-space: normal;
                }
                .clean-tip{
                    margin: 15px 0 8px 20px;
                    text-align: start;
                    font-size: 12px;
                    color: $--color-text-secondary;
                    word-break: break-word;
                    white-space: normal;
                }
                .img-box{
                    width: 416px;
                    text-align: center;
                }
            }
            .el-icon-ssq-tishizhuangtai{
                color: $--color-warning;
                margin: 3px 5px 0 0;
            }
        }
        .isPhone .el-dialog{
            width: 90%;
        }
        .el-dialog__header{
            display: flex !important;
        }
    }
</style>
