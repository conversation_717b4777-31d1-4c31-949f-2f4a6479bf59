import i18n from 'src/lang/';

// 试用功能结束提示信息
const TRIAL_FEATURE_END = [
    {
        key: 'attachRequires',
        featureId: '34', // 合同附属资料
        titleTip: () => i18n.t('functionSupportDialog.trialEndMap.deactivateTip', { feature: i18n.t('functionSupportDialog.trialEndMap.feature1') }),
        cleanMethodTip: () => i18n.t('functionSupportDialog.trialEndMap.remove1'),
        img: require('img/trialFeature/attachRequires.png'),
    },
    {
        key: 'handwritingRecognition',
        featureId: '79', // 必须手写并开启笔迹识别
        titleTip: () => i18n.t('functionSupportDialog.trialEndMap.deactivateTip', { feature: i18n.t('functionSupportDialog.trialEndMap.feature2') }),
        cleanMethodTip: () => i18n.t('functionSupportDialog.trialEndMap.remove2'),
        img: require('img/trialFeature/handwritingRecognition.png'),
    },
    {
        key: 'contractDecoration',
        featureId: '68', // 合同装饰（水印和骑缝章）
        titleTip: () => i18n.t('functionSupportDialog.trialEndMap.deactivateTip', { feature: i18n.t('functionSupportDialog.trialEndMap.feature3') }),
        cleanMethodTip: () => i18n.t('functionSupportDialog.trialEndMap.remove3'),
        img: require('img/trialFeature/contractDecoration.png'),
    },
    {
        key: 'ourCompanyIsSenderApproval',
        featureId: '185', // 发送前审批
        titleTip: () => i18n.t('functionSupportDialog.trialEndMap.deactivateTip', { feature: i18n.t('functionSupportDialog.trialEndMap.feature4') }),
        cleanMethodTip: () => i18n.t('functionSupportDialog.trialEndMap.remove4'),
        img: require('img/trialFeature/contractSendApproval.png'),
    },

];

// 功能演示信息
const FEATURE_VEDIO_LIST = [
    {
        featureId: '34', // 合同附属资料
        featureName: '合同附属资料',
        data: [
            {
                title: '合同附属资料',
                link: 'https://www.bestsign.cn/help/FAQ/683?entId=2602682953502823427&token=8322bd57-f4a2-48f7-8f57-937256131a1d',
            },
        ],
    },
    {
        featureId: '79', // 必须手写并开启笔迹识别
        featureName: '手写笔迹识别',
        data: [
            {
                title: '手写笔迹识别',
                link: 'https://www.bestsign.cn/help/FAQ/684?entId=2602682953502823427&token=8322bd57-f4a2-48f7-8f57-937256131a1d',
            },
        ],
    },
    {
        featureId: '68', // 合同装饰（水印和骑缝章）
        featureName: '骑缝章+水印',
        data: [
            {
                title: '骑缝章和水印',
                link: 'https://www.bestsign.cn/help/FAQ/686?entId=2602682953502823427&token=8322bd57-f4a2-48f7-8f57-937256131a1d',
            },
        ],
    },
    {
        featureId: '185', // 发送前审批
        featureName: '合同发起审批',
        data: [
            {
                title: '创建“我司是发件方”时的审批',
                link: 'https://www.bestsign.cn/help/FAQ/489?entId=2602682953502823427&token=8322bd57-f4a2-48f7-8f57-937256131a1d',
            },
            {
                title: '合同审批操作流程',
                link: 'https://www.bestsign.cn/help/FAQ/491?entId=2602682953502823427&token=8322bd57-f4a2-48f7-8f57-937256131a1d',
            },
        ],
    },
    {
        featureId: '179', // 模板授权
        featureName: '模板授权',
        data: [
            {
                title: '模板的创建、分配',
                link: 'https://www.bestsign.cn/help/FAQ/503?entId=2602682953502823427&token=8322bd57-f4a2-48f7-8f57-937256131a1d',
            },
            {
                title: '模板权限说明',
                link: 'https://www.bestsign.cn/help/FAQ/504?entId=2602682953502823427&token=8322bd57-f4a2-48f7-8f57-937256131a1d',
            },
        ],
    },
    {
        featureId: '81', // 自定义合同归档
        featureName: '自定义合同归档',
        data: [
            {
                title: '自定义合同归档',
                link: 'https://www.bestsign.cn/help/FAQ/685?entId=2602682953502823427&token=8322bd57-f4a2-48f7-8f57-937256131a1d',
            },
        ],
    },
];
export {
    TRIAL_FEATURE_END,
    FEATURE_VEDIO_LIST,
};
