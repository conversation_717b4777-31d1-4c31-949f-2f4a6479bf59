<template>
    <el-dialog
        class="watermark-dialog"
        title=""
        :visible.sync="visible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :show-close="false"
    >
        <span>{{ msg }}</span>
        <span slot="footer" class="dialog-footer">
            <el-button @click="$emit('cancel')">{{
                cancelText
            }}</el-button>
            <el-button
                type="primary"
                @click="$emit('confirm')"
                class="btn-type-one"
            >{{ confirmText }}</el-button>
        </span>
    </el-dialog>
</template>

<script>
export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        msg: {
            type: String,
            default: '',
        },
        confirmText: {
            type: String,
            default: '确认',
        },
        cancelText: {
            type: String,
            default: '取消',
        },
    },
};
</script>

<style lang="scss">
.watermark-dialog{
    .el-button{
        padding: 8px !important;
    }
}
</style>
