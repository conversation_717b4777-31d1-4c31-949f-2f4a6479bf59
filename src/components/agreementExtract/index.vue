<template>
    <el-dialog
        :visible.sync="dialogVisible"
        size="tiny"
        append-to-body
        :show-close="false"
        class="share-view-dialog"
        @close="initInfo"
    >
        <div slot="title" class="share-view-dialog__header">
            <p>{{ $t('keyInfoExtract.operate') }}</p>
            <!-- <div>
                <el-button type="primary" @click="toPage('https://jsj.top/f/dRdhUU')">问卷反馈</el-button>
            </div> -->
        </div>
        <div class="share-content" v-loading="loading" :element-loading-text="loadingText">
            <template v-if="step === 1">
                <p>请选择需要提取的合同</p>
                <el-radio-group v-model="documentId">
                    <el-radio :label="doc.documentId" v-for="doc in docList" :key="doc.documentId">{{ doc.fileName }}</el-radio>
                </el-radio-group>
                <el-button @click="extractCheckedDocInfo" style="margin-left: 10px;" type="primary">确认</el-button>
            </template>
            <template v-else-if="step === 2">
                <el-form label-width="120px" v-if="!showError">
                    <el-form-item :label="$t('keyInfoExtract.contractType')">
                        <el-select v-model="contractType">
                            <el-option
                                v-for="item in supportedTypes"
                                :key="item"
                                :label="item"
                                :value="item"
                            ></el-option>
                        </el-select>
                        <el-button style="margin-left: 10px;" @click="toPage('https://jsj.top/f/LghZO6')" type="text">不支持的合同类型?</el-button>
                    </el-form-item>
                </el-form>
                <p v-else>{{ $t('keyInfoExtract.errorMessage') }} <a href="https://jsj.top/f/dRdhUU" target="_blank">填写表单</a></p>
            </template>
            <div v-else-if="step === 3" class="terms-list">
                <p class="terms-list__tip">{{ $t('keyInfoExtract.tooltips') }}</p>
                <el-checkbox-group v-model="termList">
                    <el-checkbox
                        v-for="item in terminologyTypes"
                        :key="item"
                        :label="item"
                    >{{ item }}</el-checkbox>
                </el-checkbox-group>
            </div>
            <div v-else class="terms-list">
                <p class="terms-list__tip">{{ $t('keyInfoExtract.result') }}</p>
                <!-- <p class="terms-list__result">{{ results }}</p> -->
                <!-- <div v-html="results"></div> -->
                <VueMarkdown class="terms-list__result" :source="results"></VueMarkdown>
                <!-- <div class="terms-list__result" v-for="result in results" :key="result.terminologyName"> -->
                <!-- <p class="terms-list__result-type">{{ result.terminologyName }}：</p> -->
                <!-- </div> -->
            </div>
        </div>
        <div slot="footer" v-if="step === 2 && !showError">
            <el-button type="primary" @click="getTermsList" :loading="btnLoading">确认合同类型</el-button>
        </div>
        <div slot="footer" v-if="step === 3">
            <el-button type="primary" @click="extractInfo" :loading="btnLoading">提取信息</el-button>
        </div>
        <div slot="footer" v-if="step === 4">
            <el-button type="text" @click="toPage('https://jsj.top/f/dRdhUU')">是否需要批量提取功能?</el-button>
        </div>
    </el-dialog>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import * as extract from '../../api/keyInfoExtract';
import VueMarkdown from 'vue-markdown';
export default {
    name: 'AgreementExtractDialog',
    components: {
        VueMarkdown,
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        contractId: {
            type: String,
            default: '',
        },
        receiverId: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            loading: false,
            loadingText: '正在预测合同类型',
            documentId: '',
            contractType: '',
            newAnnotationId: null,
            termList: [],
            terminologyTypes: [],
            supportedTypes: [],
            results: '',
            showError: false,
            btnLoading: false,
        };
    },
    computed: {
        ...mapState({
            docList: state => state.docList,
            step: state => state.step,
            annotationId: state => state.commonHeaderInfo.annotationId,
        }),
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(v) {
                this.$emit('update:visible', v);
            },
        },
    },
    watch: {
        visible: {
            handler(val) {
                if (val && this.annotationId) {
                    this.newAnnotationId = this.annotationId;
                    this.getHistoryInfo();
                    return;
                }
                if (val && this.docList.length === 1) {
                    this.documentId = this.docList[0].documentId;
                    this.extractCheckedDocInfo();
                }
            },
            immediate: true,
        },
    },
    methods: {
        ...mapActions('approval', ['getAnnotationHistory']),
        extractCheckedDocInfo() {
            this.loading = true;
            this.getPredictType().then(() => {
                this.getContractType();
                this.$store.state.step = 2;
            }).finally(() => {
                this.loading = false;
            });
        },
        getPredictType() {
            return new Promise((resolve, reject) => {
                extract.predictContractType(this.contractId, this.receiverId, this.documentId).then(res => {
                    if (res.data.success) {
                        this.loading = false;
                        this.contractType = res.data.contractType;
                    }
                    this.newAnnotationId = res.data.annotationId;
                    this.getAnnotationHistory();
                    resolve();
                }).catch((err) => {
                    if (err.response.data.code === '140008') {
                        this.showError = true;
                        this.$store.state.step = 2;
                    } else {
                        Vue.$MessageToast.error(err.message);
                    }
                    reject();
                });
            });
        },
        getContractType() {
            extract.supportedContractTypes(this.contractId, this.receiverId, this.documentId).then(res => {
                if (res.data) {
                    this.supportedTypes = res.data.contractTypes;
                }
            });
        },
        getTermsList() {
            this.btnLoading = true;
            extract.queryTerminologyList(this.contractId, this.receiverId, this.documentId, {
                contractType: this.contractType,
            }).then(res => {
                if (res.data) {
                    this.terminologyTypes = [...new Set(res.data.terminologyList.map(obj => obj.terminologyType))];
                    this.$store.state.step = 3;
                }
            }).finally(() => {
                this.btnLoading = false;
            });
        },
        extractInfo() {
            this.btnLoading = true;
            this.loading = true;
            this.loadingText = '正在提取合同信息';
            extract.extractKeyInfoByTerm(this.contractId, this.receiverId, this.documentId, {
                contractType: this.contractType,
                annotationId: this.newAnnotationId,
                terminologyTypes: this.termList,
            }).then(res => {
                this.results = res.data.terminologyExtractionResult.replace(/\n\n/g, '\n<br/>\n\n');
                this.$store.state.step = 4;
                this.loading = false;
            }).catch(err => {
                if (err.response.data.code === '140008') {
                    this.showError = true;
                    this.$store.state.step = 2;
                }
            }).finally(() => {
                this.loading = false;
                this.btnLoading = false;
            });
        },
        getHistoryInfo() {
            this.loading = true;
            this.loadingText = '正在获取合同信息';
            extract.queryExtractionResult(this.contractId, this.receiverId, {
                annotationId: this.newAnnotationId,
            }).then(res => {
                // const converter = new showdown.Converter();
                this.results = res.data.terminologyExtractionResult.replace(/\n\n/g, '\n<br/>\n\n');
            }).finally(() => {
                this.loading = false;
            });
        },
        initInfo() {
            this.loading = false;
            this.btnLoading = false;
            this.loadingText = '正在预测合同类型';
            this.$store.state.step = 1;
            this.documentId = '';
            this.contractType = '';
            this.newAnnotationId = '';
            this.termList = [];
            this.terminologyTypes = [];
            this.supportedTypes = [];
            this.results = '';
            this.showError = false;
            this.$store.state.commonHeaderInfo.annotationId = '';
            this.getAnnotationHistory();
        },
        toPage(url) {
            window.open(url);
        },
    },
};
</script>

<style lang="scss" scoped>
.share-view-dialog {
    &__header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        p {
            font-size: 18px;
        }
    }
    .el-button {
        border-radius: 5px;
    }
    &::v-deep .el-dialog {
        width: 580px;
        max-height: 80vh;
        position: relative;
        &__body {
            max-height: calc(80vh - 85px);
            overflow-y: auto;
            box-sizing: border-box;
            padding-bottom: 40px!important;
        }
        &__footer {
            // position: absolute;
            background-color: #fff;
            // bottom: 0;
            // left: 0;
            width: 100%;
            padding: 0px 30px 10px!important;
        }
        .share-content {
            .input-tip {
                height: 20px;
                font-size: 12px;
                line-height: 12px;
                color: #999999;
            }
        }
        .terms-list {
            .el-checkbox {
                margin-left: 0;
                margin-right: 10px;
                line-height: 24px;
                &__label {
                    font-size: 14px;
                }
            }
            &__tip {
                //text-align: center;
                font-size: 16px;
                color: #666;
                margin-bottom: 10px;
            }
            &__result {
                user-select: text;
                ul{
                    padding-left: 20px;
                    li {
                        list-style: disc;
                    }
                }
                // &-type {
                //     font-size: 14px;
                //     margin-bottom: 4px;
                // }
                // &-content {
                //     font-size: 12px;
                // }
            }
        }
    }
    .share-footer {
        text-align: center;
    }
}
</style>
