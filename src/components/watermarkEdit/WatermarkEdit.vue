<template>
    <div class="water-mark">
        <p>{{ watermark.showName? watermark.showName: watermark.roleName }}</p>
        <div class="watermark-text" :style="`backgroundColor: ${color}; borderColor: ${color}`">
            <i class="close-icon el-icon-ssq-guanbi" @click="handleWMDelete" v-if="templateStatus === 'edit'"></i>
            <span v-if="readonly">{{ watermark.watermarkText }}</span>
            <el-input v-else v-model="text" :maxlength="20" @blur="handleChange"></el-input>
        </div>
    </div>
</template>

<script>
export default {
    // props: ['watermark', 'color', 'readonly'],
    props: {
        watermark: {
            type: Object,
            default: () => {},
        },
        color: {
            type: String,
            default: '',
        },
        readonly: {
            type: Boolean,
        },
        templateStatus: {
            type: String,
            default: 'edit',
        },
    },

    data() {
        return {
            text: this.watermark.watermarkText,
        };
    },
    methods: {
        // 删除水印
        handleWMDelete() {
            this.$emit('delete', this.watermark);
        },
        handleChange() {
            this.$emit('update', {
                ...this.watermark,
                watermarkText: this.text,
            });
        },
    },
};
</script>

<style lang="scss">
    .watermarks-container {
        margin: 0 auto 20px;
        .water-mark {
            display: inline-block;
            min-width: 180px;
            margin: 10px 20px 0 0;
            p {
                padding-left: 5px;
                height: 20px;
                line-height: 20px;
                font-size: 12px;
                background-color: #FFF6B9;
            }
            .watermark-text {
                position: relative;
                margin-top: 2px;
                padding-left: 5px;
                font-size: 14px;
                font-size: 14px;
                .el-input .el-input__inner {
                    height: 30px;
                    line-height: 30px;
                    padding-left: 0;
                    background-color: transparent;
                    border: 0 none;
                    box-shadow: none;
                }
                .close-icon {
                    position: absolute;
                    top: -12px;
                    right: -12px;
                    padding: 5px;
                    color: white;
                    font-size: 12px;
                    background-color: rgba(51, 51, 51, .75);
                    border-radius: 12px;
                    cursor: pointer;
                    z-index: 1;
                }
            }
        }
    }
</style>
