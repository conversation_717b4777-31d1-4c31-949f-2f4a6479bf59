<template>
    <!-- 查看签署顺序弹窗 -->
    <el-dialog
        :title="$t('docDetail.viewSignOrders')"
        :visible.sync="signOrder.show"
        size="tiny"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        class="el-dialog-bg signOrder-dialog"
        id="signOrder-dialog"
        :beforeClose="handleClose"
        :lock-scroll="!unlockScroll"
    >
        <!-- 发件方，第一行 -->
        <div class="signOrder-start signOrder-step-line">

            <span class="signOrder-step">
                {{ $t('docDetail.sender') }}
            </span>

            <div class="signOrder-step-tree">
                <div class="signOrder-step-tree">
                    <div class="signOrder-step-treeNode">
                        <span class="signOrder-step-item" v-html="computedUserPhoto(contractSender)"></span>
                        <span class="signOrder-step-treeNode-name">
                            <span
                                class="name"
                            >
                                {{ contractSender.userType === 'ENTERPRISE' ? contractSender.enterpriseName || `${contractSender.userAccount || ''}（${$t('docDetail.entAccount')}）` : contractSender.userName || contractSender.userAccount }}
                            </span>
                            <!-- 是企业并且存在经办人则显示 -->
                            <div v-if="contractSender.userType === 'ENTERPRISE' && contractSender.userName" class="signOrder-step-item-popover-wrap">
                                <ul class="signOrder-step-item-popover">
                                    <li>
                                        {{ contractSender.enterpriseName || `${contractSender.userAccount || ''}（${$t('docDetail.entAccount')}）` }}
                                        <span v-if="contractSender.userName">{{ $t('docDetail.operator') }}：{{ contractSender.userName }}</span>
                                    </li>
                                </ul>
                            </div>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 循环中间的列表 -->
        <div class="signOrder-step-line" v-for="(list, index) in computedRecipients" :key="index">
            <span class="signOrder-step">
                {{ index + 1 }} {{ computedSignType(list[0]) }}
            </span>
            <div class="signOrder-step-tree" v-if="list.length <= 3">
                <div
                    class="signOrder-step-treeNode"
                    v-for="(node, index2) in list"
                    :key="index2"
                >
                    <span class="signOrder-step-item" v-html="computedUserPhoto(node)"></span>
                    <span class="signOrder-step-treeNode-name">
                        <span
                            class="name"
                        >
                            {{ node.userType === 'ENTERPRISE' ? node.enterpriseName || `${node.userAccount || ''}（${$t('docDetail.entAccount')}）` : node.userName || node.userAccount }}
                        </span>
                        <!-- 是企业并且存在经办人则显示 -->
                        <div v-if="node.userType === 'ENTERPRISE' && node.userName" class="signOrder-step-item-popover-wrap">
                            <ul class="signOrder-step-item-popover">
                                <li>
                                    {{ node.enterpriseName || `${node.userAccount || ''}（${$t('docDetail.entAccount')}）` }}
                                    <span v-if="node.userName">{{ $t('docDetail.operator') }}：{{ node.userName }}</span>
                                </li>
                            </ul>
                        </div>
                    </span>
                </div>
            </div>
            <!-- 如果超过三人则用数字代替 -->
            <div class="signOrder-step-tree" v-else>
                <div
                    class="signOrder-step-treeNode"
                    v-for="(node, index3) in list.slice(0, 2)"
                    :key="index3"
                >
                    <span class="signOrder-step-item" v-html="computedUserPhoto(node)">
                    </span>
                    <span class="signOrder-step-treeNode-name">
                        <span
                            class="name"
                        >
                            {{ node.userType === 'ENTERPRISE' ? node.enterpriseName || `${node.userAccount || ''}（${$t('docDetail.entAccount')}）` : node.userName || node.userAccount }}
                        </span>
                        <!-- 是企业并且存在经办人则显示 -->
                        <div v-if="node.userType === 'ENTERPRISE' && node.userName" class="signOrder-step-item-popover-wrap">
                            <ul class="signOrder-step-item-popover">
                                <li>
                                    {{ node.enterpriseName || `${node.userAccount || ''}（${$t('docDetail.entAccount')}）` }}
                                    <span v-if="node.userName">{{ $t('docDetail.operator') }}：{{ node.userName }}</span>
                                </li>
                            </ul>
                        </div>
                    </span>
                </div>
                <div class="signOrder-step-treeNode">
                    <span class="signOrder-step-item">
                    </span>
                    <span class="signOrder-step-treeNode-name">
                        <span class="name">
                            +{{ list.length - 2 }}
                        </span>
                        <div class="signOrder-step-item-popover-wrap">
                            <ul class="signOrder-step-item-popover">
                                <li v-for="(node, index4) in list.slice(2)" :key="index4">
                                    <template v-if="node.userType === 'ENTERPRISE'">
                                        {{ node.enterpriseName || `${node.userAccount || ''}（${$t('docDetail.entAccount')}）` }}
                                        <span v-if="node.userName">{{ $t('docDetail.operator') }}：{{ node.userName }}</span>
                                    </template>
                                    <template v-else>
                                        {{ node.userName || node.userAccount }}
                                    </template>
                                </li>
                            </ul>
                        </div>
                    </span>
                </div>
            </div>
        </div>

        <!-- 已完成，最后一行 -->
        <div class="signOrder-finish signOrder-step-line">

            <span class="signOrder-step">
                {{ $t('docDetail.completed') }}
            </span>

            <div class="signOrder-step-tree">
                <div class="signOrder-step-treeNode">
                    <span class="signOrder-step-item">
                        <i class="el-icon-ssq-qianyuewancheng"></i>
                    </span>
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['recipients', 'ordered', 'unlockScroll', 'sender'],
    data() {
        return {
            signOrder: {
                show: false,
            },
        };
    },
    computed: {
        ...mapGetters(['getCurrentEntInfo', 'getUserAccount', 'getUserType']),
        contractSender() {
            // 合同详情页直接有sender;
            if (this.sender) {
                return this.sender;
            }
            const data = this.recipients.filter(recipient => {
                return recipient.receiverType === 'SENDER';
            })[0];
            if (!(data && data.userType)) {
                return {
                    userType: this.getUserType === 'Enterprise' ? 'ENTERPRISE' : 'PERSON',
                    enterpriseName: this.getCurrentEntInfo.entName,
                    userAccount: this.getUserAccount,
                };
            }
            return data;
        },
        /**
         **   必须要的参数  **
         *  userName
         *  userType
         *  routeOrder
         *  photoHref
         *  receiverType
         *  enterpriseName
         *
         */
        computedRecipients() {
            const recipients = this.recipients;

            if (!this.ordered) {
                recipients.forEach(item => {
                    return item.routeOrder = 0;
                });
            }

            const signers = recipients.filter((item) => {
                return item.receiverType === 'SIGNER';
            });
            const copyer = recipients.filter((item) => {
                return item.receiverType === 'CARBON_COPY';
            });

            const signerSorted = signers.sort((a, b) => {
                return a.routeOrder - b.routeOrder;
            });
            const copyerSorted = copyer.sort((a, b) => {
                return a.routeOrder - b.routeOrder;
            });

            return this.sortReceipents(signerSorted.concat(copyerSorted));
        },
    },
    methods: {
        // 签署类型
        computedSignType(item) {
            let signType = '';

            switch (item.receiverType) {
                case 'SIGNER':
                    signType = this.$t('docDetail.sign');
                    break;
                // 据说现在没有这个类型了，暂时不敢删除
                case 'CARBON_COPY':
                    signType = this.$t('docDetail.cc');
                    break;
            }

            return signType;
        },
        // 用户头像或者默认头像
        computedUserPhoto() {
            const userPhoto = '';
            return userPhoto;
        },
        // 接收方按顺序分组
        sortReceipents(list) {
            const cacheOrderObj = {};
            const cacheList = [];
            let cacheListIndex = -1;

            list.forEach((item) => {
                if (!cacheOrderObj[item.routeOrder]) {
                    cacheListIndex++;
                    cacheOrderObj[item.routeOrder] = true;
                    cacheList[cacheListIndex] = [];
                }
                cacheList[cacheListIndex].push(item);
            });

            return cacheList;
        },
        handleOpen() {
            this.signOrder.show = true;
        },
        handleClose() {
            this.signOrder.show = false;
        },
    },
};
</script>

<style lang="scss">
    .signOrder-dialog{

        .el-dialog{
            width: 525px;
            color: #333;

            & > .el-dialog__body{
                padding: 0 0 33px!important;
            }
        }

        .signOrder-step-line{
            position: relative;
            padding: 9px 0;
            height: 80px;
            line-height: 80px;
            border-bottom: 1px dashed #ddd;

            &:last-child{
                border-bottom: none;
            }
        }

        .signOrder-step{
            display: inline-block;
            position: absolute;
            left: 33px;
            top: 0;
            color: #333;
        }

        .signOrder-step-tree{
            text-indent: 25px;
            position: relative;
            height: 62px;
            text-align: center;
            font-size: 0;
            $treeNodeNameWidth: 95px;
            $signOrderStepItemWidth: 40px;
            $left: 20px;

            .signOrder-step-treeNode{
                text-indent: 0;
                position: relative;
                display: inline-block;
                width: $signOrderStepItemWidth * 2;
                height: 62px;
                padding-left: $left;
                padding-right: $left;
                border-top: 2px solid #ccc;
                border-bottom: 2px solid #ccc;
                line-height: 62px;
                vertical-align: top;
                font-size: 12px;
                background: #f6f9fc;

                // 两个节点时的连接线
                &:first-child:nth-last-child(2)::before{
                    content: '';
                    position: absolute;
                    width: 2px;
                    background: #ccc;
                    height: 81px;
                    top: -11px;
                    left: 78px;
                    z-index: -1;
                }

                // 2-3个节点的第一个节点
                &:first-child:nth-last-child(2),&:first-child:nth-last-child(3){
                    .signOrder-step-item{
                        position: absolute;
                        left: -$left;
                        top: 10px;

                        &::before{
                            left: 18px;
                            top: -12px;
                            height: 62px;
                        }
                    }

                    .signOrder-step-treeNode-name{
                        left: -$left + $signOrderStepItemWidth / 2;
                        // top: -2px;
                    }
                }

                // 2-3个节点的最后一个节点
                &:last-child:nth-child(2),&:last-child:nth-child(3){
                    .signOrder-step-item{
                        position: absolute;
                        left: 100%;
                        transform: translate(-50%, 0);

                        &::before{
                            left: 18px;
                            top: -11px;
                            height: 62px;
                        }
                    }
                    .signOrder-step-treeNode-name{
                        left: 100%;
                    }
                }

                // 单个节点
                &:only-child{
                    border: none;
                }

                .signOrder-step-item{
                    position: absolute;
                    left: $left;
                    top: 9px;
                    display: inline-block;
                    width: $signOrderStepItemWidth;
                    height: $signOrderStepItemWidth;
                    line-height: $signOrderStepItemWidth;
                    border-radius: 1px;
                    background: transparent;
                    text-align: center;
                    font-size: 14px;
                    color: #fff;
                    z-index: 1;
                    cursor: default;

                    i{
                        margin-bottom: -4px;
                        font-size: 36px;
                        vertical-align: bottom;
                        color: #e9e9e9;
                    }

                    .el-icon-ssq-company{
                        margin-bottom: 1px;
                        font-size: 34px;
                    }

                    img{
                        width: 40px;
                        height: 40px;
                        background: #fff;
                        border-radius: 1px;
                    }

                    &::before{
                        content: '';
                        width: 2px;
                        display: block;
                        height: 81px;
                        position: absolute;
                        background: #ccc;
                        left: 18px;
                        top: -19px;
                        z-index: -1;
                    }

                    &::after{
                        content: '';
                        display: inline-block;
                        position: absolute;
                        left: 0;
                        top: -10px;
                        width: 38px;
                        height: 10px;
                    }

                    @keyframes fade-in {
                        from{
                            opacity: 0;
                            bottom: 53px
                        }
                        to{
                            opacity: 1;
                            bottom: 40px
                        }
                    }
                }

                .signOrder-step-treeNode-name{
                    width: $treeNodeNameWidth;
                    height: $signOrderStepItemWidth;
                    line-height: $signOrderStepItemWidth;
                    position: absolute;
                    left: $left + $signOrderStepItemWidth / 2;
                    transform: translate(-50%, 0);
                    z-index: 1;
                    border: 1px solid #ccc;
                    background: #fff;
                    text-align: center;
                    margin-top: 12px;

                    .name {
                        display: block;
                        padding: 0 8px;
                        text-overflow: ellipsis;
                        overflow: hidden;
                        white-space: nowrap;
                    }

                    &:hover{
                        .signOrder-step-item-popover-wrap{
                            display: block;
                        }
                    }

                    .signOrder-step-item-popover-wrap{
                        display: none;
                        padding-bottom: 20px;
                        position: absolute;
                        bottom: 25px;
                        left: 50%;
                        transform: translate(-50%, 0);
                        &:after, &:before {
                            content: ' ';
                            width: 0;
                            height: 0;
                            margin-left: 1px;
                            display: inline-block;
                            left: 50%;
                            transform: translate(-50%, 0);
                            bottom: 5px;
                            position: absolute;
                            border: 10px solid transparent;
                            border-top-color: white;
                        }
                        &:before {
                            content: ' ';
                            border: 8px solid transparent;
                            border-top-color: #d7d8d9;
                        }
                        .signOrder-step-item-popover{
                            padding: 0 16px;
                            width: 215px;
                            white-space: nowrap;
                            max-height: 200px;
                            border: 1px solid #ccc;
                            overflow-y: scroll;
                            background: #fff;
                            border-radius: 1px;
                            text-align: left;
                            box-shadow: 0px 0px 5px 0px #ccc;
                            animation: fade-in;
                            animation-duration: 0s;

                            /* &::before{
                                content: '';
                                position: absolute;
                                left: 50%;
                                bottom: -12px;
                                display: block;
                                border-top: 6px solid #ccc;
                                border-bottom: 6px solid transparent;
                                border-right: 6px solid transparent;
                                border-left: 6px solid transparent;
                                transform: translate(-50%);
                            }

                            &::after{
                                content: '';
                                position: absolute;
                                left: 50%;
                                bottom: -10px;
                                display: block;
                                border-top: 6px solid #fff;
                                border-bottom: 6px solid transparent;
                                border-right: 6px solid transparent;
                                border-left: 6px solid transparent;
                                transform: translate(-50%);
                            } */

                            li{
                                // height: 35px;
                                line-height: 25px;
                                padding: 8px 5px;
                                position: relative;
                                z-index: 1;
                                border-bottom: 1px solid #eee;
                                color: #333;
                                font-size: 12px;
                                white-space: nowrap;
                                text-overflow: hidden;
                                overflow: hidden;
                                span {
                                    display: block;
                                    margin-top: -8px;
                                    color: #999;
                                }
                            }

                            li:last-child{
                                border-bottom: none;
                            }
                        }
                    }
                }
            }

        }

        .signOrder-start{
            .signOrder-step-tree > .signOrder-step-treeNode > .signOrder-step-item{
                &::before{
                    top: initial;
                    bottom: -24px;
                    height: 24px;
                }
            }
        }

        .signOrder-finish{

            .signOrder-step-tree > .signOrder-step-treeNode > .signOrder-step-item{
                width: 40px;
                height: 40px;
                line-height: 40px;
                border: 2px solid #ccc;
                box-sizing: border-box;
                text-align: center;
                border-radius: 50%;
                background: #fff;

                &::before{
                    top: -24px;
                    left: 16px;
                    height: 24px;
                }

                .el-icon-ssq-qianyuewancheng{
                    font-size: 24px;
                    vertical-align: sub;
                    color: #2baa3f;
                }

            }
        }
    }
</style>
