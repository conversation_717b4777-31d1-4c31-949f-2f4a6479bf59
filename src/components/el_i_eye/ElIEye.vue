<template>
    <i class="iconfont el-input__icon i-eye el-icon-ssq-eye-filling" :class="isShow?'active':''" @click="handleIconClick"></i>
</template>

<script>
	// 和element深度耦合，慎用
export default {
    data() {
        return {
            isShow: 0,
        };
    },
    methods: {
        handleIconClick: function() {
            const type = this.$parent.$refs.input.type;
            this.isShow = !this.isShow;
            this.$parent.$refs.input.type = type === 'password' ? 'text' : 'password';
        },
    },
};
</script>

<style lang="scss">
	.i-eye {
		font-size: 20px !important;
		color: #ccc;
        &.active {
            color: #2298f1;
        }
	}
</style>
