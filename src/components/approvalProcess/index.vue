<template>
    <div class="approval-process">
        <div class="submitter-info">
            <Portrait class="operator-portrait" :src="sender.photoHref" :size="30"></Portrait>
            <div class="approval-operator-container">
                <div class="approval-operator">{{ $t('sign.submitter') }}</div>
                <div class="intro">
                    <span class="name">{{ sender.userName || '' }}</span>
                    <span class="account">({{ sender.userAccount || '' }})</span>
                </div>
            </div>
        </div>
        <div class="approvers-info">
            <div class="approvers-info-item" v-for="(item, index) in approvers" :key="index">
                <Portrait class="operator-portrait" :src="item.photoHref" :size="30">
                    <i class="i-status" :class="icon(item.signStatus)"></i>
                </Portrait>
                <div class="approval-operator-container">
                    <div class="approval-operator">{{ $t('sign.approveOperator') }}{{ index + 1 }}</div>
                    <div class="intro">
                        <span class="name">{{ item.userName || '' }}</span>
                        <span class="account">({{ item.userAccount || '' }})</span>
                    </div>
                </div>
                <div class="approval-opinion" v-if="item.approvalOpinion">
                    <div>{{ $t('sign.approvalOpinion') }}</div>
                    <div class="approval-opinion-content">{{ item.approvalOpinion }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import Portrait from 'components/portrait/index.vue';
export default {
    name: 'ApprovalProcess',
    components: {
        Portrait,
    },
    props: {
        sender: {
            type: Object,
            default: () => ({}),
        },
        approvers: {
            type: Array,
            default: () => [],
        },
    },
    methods: {
        icon(type) {
            switch (type) {
                case 'APPROVAL_PASS':
                    return 'el-icon-ssq-tongguo green';
                case 'APPROVAL_WAIT':
                    return 'el-icon-ssq-dengdaitarenqianshu blue';
                case 'APPROVAL_DENY':
                    return 'el-icon-ssq-beijuqian red';
                case 'NOT_START':
                    return 'el-icon-ssq-dengdaitarenqianshu blue';
            }
        },
    },
};
</script>

<style lang="scss">
.approval-process{
    .approval-operator-container{
        display: inline-block;
        margin-left: 10px;
        .approval-operator{
            font-size: 12px;
            color: #999999;
        }
        .intro{
            font-size: 12px;
            color: #333333;
        }
    }
    .approval-opinion{
        font-size: 12px;
        color: #999999;
        margin-left: 40px;
        margin-top: 8px;
        .approval-opinion-content{
            margin-top: 9px;
            color: #333333;
        }
    }
    .submitter-info,.approvers-info-item{
        margin-bottom: 25px;
    }
    .approvers-info-item:last-child{
        margin-bottom: 0;
    }
    .approval-operator{
        display: inline-block;
    }
    .i-status {
        position: absolute;
        top: 16px;
        left: 16px;
        font-size: 16px;
        background-color: #fff;
        border-radius: 50%;
    }
    .green {
        color: #00AA64;
    }
    .blue {
        color: #1687DC;
    }
    .red {
        color: #FF5500;
    }
    .operator-portrait{
        position: relative;
        top: 3px;
    }
}
</style>
