<!-- 文件上传后预览弹窗 -->
<template>
    <transition name="fade">
        <!-- 遮罩层 -->
        <div class="preview-pdf-modal">
            <div class="preview-pdf-popup" id="preview-pdf-popup">
                <!-- <div class="head" @click="clickClose">
                <span class="close thick"></span>
            </div> -->
                <div class="wrapper" :style="wrapperStyle">
                    <div class="title">{{ previewDoc.name || previewDoc.fileName }}</div>
                    <div class="content" :style="styleObject">
                        <Pdf :scale="scale"
                            :waterMarkPng="null"
                            @updateCurrentPageIndex="handleUpdatePageIndex"
                            :doc="previewDoc"
                            :isShowRidingSeal="false"
                            @ready="handleReady"
                            parentElId="preview-pdf-popup"
                        >
                        </Pdf>
                    </div>
                </div>
            </div>
        </div>

    </transition>
</template>
<script>
import Pdf from 'components/pdf/Pdf.vue';

export default {
    components: {
        Pdf,
    },
    // eslint-disable-next-line vue/require-prop-types
    props: ['contractId'],
    data() {
        return {
            isShow: false,
            previewDoc: {},
        };
    },
    computed: {
        wrapperStyle() { // 外层元素设置实际高度，否则scale放缩无法撑开
            const sumHeight = this.previewDoc.page.reduce((sum, page) => {
                sum += ((page.height || 0) + 20);
                return sum;
            }, 0);

            return {
                height: `${sumHeight * this.scale + 80}px`,
            };
        },
        // 渲染的文档所有页面的最大高度，切换文档时，当缩放的scale小于1时，会导致高度留白，因此要设置高度
        sumHeight() {
            const sumHeight = this.previewDoc.page.reduce((sum, page) => {
                sum += ((page.height || 0) + 20);
                return sum;
            }, 0);
            return Math.min(sumHeight, sumHeight * this.scale);
        },
        styleObject() {
            if (this.scale === 1) {
                return {};
            }
            if (this.scale > 1) {
                return {
                    maxWidth: `${this.maxWidth}px`,
                    transform: `scale(${this.scale})`,
                    height: `${this.sumHeight}px`,
                };
            } else {
                return {
                    minWidth: `${this.maxWidth}px`,
                    transform: `scale(${this.scale})`,
                    height: `${this.sumHeight}px`,
                };
            }
        },
        // 计算页面的缩放比
        scale() {
            const { maxWidth } = this;
            return Math.min(maxWidth ? 940 / maxWidth : 1, 1); // 避免文档放大导致的字体模糊
        },
        // 文档的最大宽度
        maxWidth() {
            return (this.previewDoc.page || []).reduce((max, page) => Math.max(max, page.width || 0), 0);
        },
    },
    methods: {
        handleUpdatePageIndex() {
        },
        handleReady(page) {
            this.$set(this.previewDoc, 'page', page);
        },
    },
};
</script>
<style lang="scss">
.preview-pdf-popup {
    position: fixed;
    left: 50%;
    z-index: 1002;
    width: 1000px;
    border-radius: 4px;
    height: calc(100% - 60px);
    overflow-y: scroll;
    overflow-x: hidden;
    top: 60px;
    margin-left: -500px;
    // background-color: #EAEBED;

    .head {
        z-index: 1004;
        position: fixed;
        top: 0;
        width: 100%;
        height: 50px;
        background-color: #000;

        .close {
            z-index: 999;

            position: absolute;
            top: 15px;
            right: 30px;
            display: inline-block;
            width: 20px;
            height: 20px;
            overflow: hidden;
            cursor: pointer;
        }

        .thick::before,
        .thick::after {
            height: 4px;
            margin-top: -2px;
        }

        .thick::before,
        .thick::after {
            content: '';
            position: absolute;
            height: 2px;
            width: 100%;
            top: 50%;
            left: 0;
            margin-top: -1px;
            background: #fff;

        }
        .thick::before {
            transform: rotate(45deg);
        }

        .thick::after {
            transform: rotate(-45deg);
        }
    }

    .wrapper {
        box-sizing: border-box;
        left: 0px;
        z-index: 1005;
        position: absolute;
        width: 1000px;
        right: 0px;
        top: 0;
        background-color: #EAEBED;
        padding: 0 30px 20px;
        min-height: calc(100% - 60px);
        .title {
            height: 60px;
            line-height: 60px;
            font-size: 24px;
            text-align: center;
        }

        .content {
            transform-origin: 0 0 0;
            position: relative;
            // padding: 0 0 18px;
            text-align: center;
            background-color: #EAEBED;
        }
    }

}
.preview-pdf-modal {
    z-index: 1001;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.6);
}
</style>
