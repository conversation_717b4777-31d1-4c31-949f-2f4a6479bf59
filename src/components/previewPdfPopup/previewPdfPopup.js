import PreviewPdfPopup from './PreviewPdfPopup.vue';
import i18n from '../../lang';
const PreviewConstructor = Vue.extend(PreviewPdfPopup);
let instance;

// PreviewConstructor.prototype.close = function() {
//     console.log('asdfasdf');
//     this.$el.style.display = 'none';
//     // document.body.removeChild(this.$el);
// }

export default function open(data) {
    instance = new PreviewConstructor({
        data,
        i18n,
    }).$mount();
    document.body.appendChild(instance.$el);
    function close(event) {
        if (event.target.closest('.preview-pdf-popup .wrapper')) {
            return;
        }
        instance.$destroy();
        document.body.removeChild(instance.$el);
        instance = null;
        document.body.removeEventListener('mouseup', close);
    }
    document.body.addEventListener('mouseup', close, false);
    return instance;
}
