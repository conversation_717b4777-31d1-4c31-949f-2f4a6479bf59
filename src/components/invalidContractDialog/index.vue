<template>
    <el-dialog
        :title="$t('sign.commonTip')"
        :visible.sync="visible"
        custom-class="invalid-contract-dialog"
        :before-close="handleBtnClick"
    >

        <div class="invalid-contract-dialog-header">
            <i class="el-icon-ssq-hetongxiangqing-chexiao1"></i>
            <span>{{ $t('sign.invalidContract') }}</span>
        </div>
        <div class="invalid-contract-dialog-content-wrapper">
            <div v-for="(item, listIndex) in initialContractList" :key="listIndex" @click="handleGoToView(item.invalidContractId)">
                <div v-for="(doc, docIndex) in item.docVOList" :key="docIndex" class="invalid-contract-dialog-content">
                    <el-tooltip class="item" effect="dark" :content="doc.documentName" placement="right">
                        <span class="invalid-contract-dialog-docname">{{ doc.documentName }}</span>
                    </el-tooltip>
                    <span class="invalid-contract-dialog-fileId">{{ `（${$t('sign.No')}：${doc.documentId}）` }}</span>
                </div>
            </div>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button @click="handleBtnClick">{{ $t('sign.cancel') }}</el-button>
            <el-button type="primary" @click="handleBtnClick('nextStep')">{{ $t('sign.nextStep') }}</el-button>
        </span>
    </el-dialog>
</template>
<script>

export default {
    props: {
        dialogVisible: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            visible: false,
            initialContractId: [],
            initialContractList: [],
        };
    },
    watch: {
        dialogVisible: {
            handler(val) {
                this.visible = val;
            },
            immediate: true,
        },
    },
    methods: {
        handleBtnClick(from) {
            this.$emit('closeInvalidContractDialog', from);
        },
        handleGoToView(contractId) {
            window.open(`${location.origin}/sign-flow/doc-manage/view?contractId=${contractId}&showLabel=true`);
        },
    },
    created() {
        this.$http.get(`/contract-api/contracts/${this.$route.query.contractId}`).then(res => {
            this.initialContractId = res.data.invalidContractId;
            this.$http.post(`/contract-api/contracts/invalid-contract/doc-list`, {
                invalidContractIds: [this.initialContractId],
            }).then((resList) => {
                this.initialContractList = resList.data;
            });
        });
    },
};
</script>

<style lang="scss">
    .invalid-contract-dialog {
        width: 460px;
    }
    .invalid-contract-dialog-header {
        height: 44px;
        line-height: 44px;
        padding-left: 30px;
    }
    .invalid-contract-dialog-content-wrapper {
        height: 104px;
        overflow-y: scroll;
    }
    .invalid-contract-dialog-docname {
        display: inline-block;
        width: 150px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        margin-right: 10px;
        [dir=rtl] & {
            margin-right: 0;
            margin-left: 10px;
        }
    }
    .invalid-contract-dialog-fileId {
        font-size: 12px;
        color: #999;
    }
    .el-icon-ssq-hetongxiangqing-chexiao1 {
        color: #f3a93f;
    }
    .invalid-contract-dialog-content {
        padding-left: 50px;
        height: 26px;
        line-height: 26px;
        display: flex;
        align-items: center;
        cursor: pointer;
        [dir=rtl] & {
            padding-left: 0;
        }
        &:hover {
            color: $theme-color;
        }
    }
</style>
