import Vue from 'vue';
import MessageBox from './MessageBox.vue';
import store from '../../store/store.js';

const plugin = {};
let bridge = null;
let vm = null;
let queue = 0;

const Component = Vue.extend(MessageBox);

const clearCache = () => {
    // 完全销毁一个实例。清理它与其它实例的连接，解绑它的全部指令及事件监听器。
    vm.$destroy();
    // 删除dom
    vm.$el.parentNode.removeChild(vm.$el);
    // 释放内存
    vm = null;
    bridge = null;
    queue = 0;
};

const dispatchCallBack = (action) => {
    if (action === 'resolve') {
        bridge.resolve('confirm');
    } else {
        bridge.reject('cancel');
    }
    clearCache();
};

const $MessageBox = (option = {}) => {
    // init optios
    if (typeof option === 'string') {
        option = {
            message: option,
        };
    }

    // 先把组件实例渲染到内存
    if (queue < 1) {
        vm = new Component().$mount();
        queue++;
    }

    // 然后把传入的参数赋值到组件实例
    if (option.iClass)  {
        vm.iClass   = option.iClass;
    }
    if (option.icon)    {
        vm.icon     = option.icon;
    }
    if (option.title)   {
        vm.title    = option.title;
    }
    if (option.message) {
        vm.message  = option.message;
    }
    if (option.confirmBtnText) {
        vm.confirmBtnText  = option.confirmBtnText;
    }
    if (option.cancelBtnText)  {
        vm.cancelBtnText   = option.cancelBtnText;
    }
    if (option.errCode)  {
        vm.errCode   = option.errCode;
    }
    vm.state = store.state;
    vm.showHeader = option.showHeader || false;
    vm.headerTitle = option.headerTitle || '';
    vm.showClose = option.showClose !== false;
    vm.dispatch = dispatchCallBack;

    // 最后挂载到body
    document.body.appendChild(vm.$el);

    // 显示MessageBox组件
    Vue.nextTick(() => {
        vm.visible = true;
    });

    // 返回一个promise
    return new Promise((resolve, reject) => {
        bridge = { resolve, reject };
    });
};

plugin.install = function(Vue) {
    Vue.prototype.$messageBox = $MessageBox;
    Vue.MessageBox = $MessageBox;
};

export default plugin;
