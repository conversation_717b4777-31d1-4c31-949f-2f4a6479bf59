<template>
    <!-- 报错弹窗，非提示框，主要场景：模板权限问题、商品化版本的资源不足 -->
    <div class="box-sizing-dialog" :class="iClass">
        <el-dialog
            class="temp-error-dialog"
            :class="[showHeader ? '' : 'hideHeader']"
            :visible.sync="visible"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :show-close="showClose"
            :before-close="clickClose"
            :title="headerTitle"
            size="tiny"
        >
            <p v-if="icon">
                <i :class="icon"></i>
            </p>
            <h5 v-if="title">
                {{ title }}
            </h5>
            <template v-if="message">
                <p v-for="(msg) in computedMessage" :key="msg">
                    {{ msg }}

                <!--
                    查看更多版本按钮，无奈加在这里，只在商品化需求-账号资源不足提示升级版本时出现
                    判断条件的是除最高版外其他版本资源不足时在第一行显示此按钮
                 -->
                <!-- <span v-if="!isTopVersion && index === 0 && errCode === '120103'" @click="previewShow = true" class="viewVersionBtn">查看更多版本</span> -->
                </p>
            </template>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="clickConfirmBtn">{{ confirmBtnText }}</el-button>
                <el-button v-if="cancelBtnText" class="cancel-btn" @click="clickCancelBtn">{{ cancelBtnText }}</el-button>
            </span>
        </el-dialog>

        <!-- 查看更多版本图片弹窗 -->
        <Preview
            v-if="previewShow"
            title="查看更多版本"
            @closePreview="previewShow = false"
        ></Preview>
    </div>
</template>
<script>
import i18n from 'src/lang';
import Preview from './PreviewDialog.vue';

export default {
    components: {
        Preview,
    },
    data() {
        return {
            visible: false,
            iClass: 'messageDialog',
            icon: 'el-icon-ssq-tishi1',
            title: '',
            message: '',
            confirmBtnText: i18n.t('CSCommon.confirm'),
            cancelBtnText: '',
            dispatch: null,
            showHeader: false,
            headerTitle: '',
            showClose: true,
            previewShow: false,
            state: {},
            errCode: null,
        };
    },
    computed: {
        // 判断是最高版本
        isTopVersion() {
            return this.state.version === 1;
        },
        computedMessage() {
            const message = this.message;
            const type = Object.prototype.toString.call(message);
            if (type === '[object Array]' || type === '[object Object]') {
                return message;
            }
            return [message];
        },
    },
    methods: {
        // beforeClose
        clickClose() {
            this.closeBox('reject');
        },
        // 确认
        clickConfirmBtn() {
            this.closeBox('resolve');
        },
        // 取消
        clickCancelBtn() {
            this.closeBox('reject');
        },
        // 统一关闭
        closeBox(type) {
            this.visible = false;
            this.dispatch(type);
        },
    },
};
</script>
<style lang="scss">
    .box-sizing-dialog{
        .temp-error-dialog {

            &.hideHeader{
                .el-dialog__header{
                    display: none;
                }
            }
            .el-dialog {
                div.el-dialog__body{
                    padding-top: 35px;
                    padding-bottom: 15px;
                    text-align: center;
                    background: #f6f9fc;

                    .el-icon-ssq-tishi1{
                        color: #f86b26;
                        font-size: 28px;
                    }

                    h5{
                        margin-bottom: 10px;
                        color: #333;
                        font-size: 14px;
                    }

                    p{
                        margin-bottom: 10px;
                        color: #666;
                        font-size: 14px;

                        .viewVersionBtn{
                            cursor: pointer;
                            color: #2298f1;
                        }
                    }
                }

                .el-dialog__footer{
                    padding-top: 15px;
                    border-top: 1px solid #eee;
                    text-align: center;

                    .el-button--primary{
                        width: 190px;
                        height: 34px;
                    }

                    .cancel-btn {
                        position: relative;
                        top: 1px;
                    }
                }
            }
        }
        @media screen and (max-width: 767px) {
            .temp-error-dialog {
                .el-dialog {
                    width: 85%;
                }
            }
        }
        &.messageDialog .auth-preview-img {
            z-index: 9000;

            .wapper {
                background: none;
                .title {
                    display: none;
                }
                .preview-content, .content {
                    padding: 0;

                    img{
                        border-radius: 2px;
                    }
                }
            }
        }
    }
</style>
