<!-- 图片放大显示 -->
<template>
    <div
        class="auth-preview-img"
        :class="{'isPC-con': isPC,'isPhone-con': !isPC}"
    >
        <div class="preview-head">
            <span class="close stick" @click="close"></span>
        </div>

        <div class="wapper">
            <h4 class="title">
                {{ title }}
            </h4>
            <div class="preview-content">
                <img v-if="title === $t('entAuth.businessLicense')" src="./images/BusinessLicense.png" width="449" alt="">
                <img v-else-if="title === $t('entAuth.ssqAttorney')" :src="operateSrc" width="812" alt="">
                <img v-else-if="title === $t('entAuth.checkMoreVerison')" src="./images/versionPrice.png" width="1000" alt="">
                <img
                    v-else
                    :src="imgPath"
                    :width="width"
                    alt="title"
                >
            </div>
        </div>
    </div>
</template>
<script>
import { isPC } from 'src/common/utils/device.js';

export default {
    props: {
        title: {
            type: String,
            default: '',
        },
        imgPath: {
            type: String,
            default: '',
        },
        width: {
            type: Number,
            default: 0,
        },
    },
    data() {
        return {
            isPC: isPC(),
        };
    },
    computed: {
        operateSrc() {
            return `/ents/auth/authorizationFile/preview?access_token=${this.$cookie.get('access_token')}&t=${Date.parse(new Date())}`;
        },
    },
    methods: {
        close() {
            this.$emit('closePreview');
        },
    },
};
</script>
<style lang="scss">
	.auth-preview-img{
		position: fixed;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		overflow-y: scroll;
		background: rgba(0, 0, 0, 0.5);
		z-index: 9;
		.preview-head{
			position: absolute;
			height: 60px;
			width: 100%;
			min-width: 1000px;
			background: #000;
		}
		.close {
			position: absolute;
			top: 20px;
			right: 30px;
			display: inline-block;
			width: 20px;
			height: 20px;
			overflow: hidden;
			cursor: pointer;
			z-index: 2;
		}
		.stick::before, .stick::after {
			height: 4px;
			margin-top: -2px;
		}
		.stick::before, .stick::after {
			content: '';
			position: absolute;
			height: 2px;
			width: 100%;
			top: 50%;
			left: 0;
			margin-top: -1px;
			background: #fff;
		}
		.stick::before {
			transform: rotate(45deg);
		}
		.stick::after {
			transform: rotate(-45deg);
		}
		&.isPC-con {
			.wapper {
				width: 1000px;
			}
		}
		&.isPhone-con {
            .preview-head{
                min-width: 300px;
            }
			.wapper {
				width: 90%;
				.preview-content {
					padding: 20px 25px;
					img {
						width: 100%;
					}
				}
			}
		}
		.wapper{
			margin: 60px auto 0;
			background: #eee;
			border-radius: 4px;
			text-align: center;

			.title{
				height: 86px;
				line-height: 86px;
				font-size: 22px;
				color: #666;
				border-bottom: 1px solid #ddd;
			}

			.preview-content{
				padding: 50px 80px;
			}
		}

	}
</style>
