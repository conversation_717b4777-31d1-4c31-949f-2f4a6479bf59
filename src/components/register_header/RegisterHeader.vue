<template>
    <div class="register-header" :class="{'register-header__mobile': !isPC}">
        <div class="container" v-if="isPC">
            <router-link v-if="isPC" tag="div" class="ilBlock" to="/" exact>
                <img class="default-logo cur-pointer" :src="GLOBAL.WHITE_LOGO" width="86" height="37" alt="">
            </router-link>
            <span v-if="isPC" class="line"></span>
            <img
                v-if="isPC && clientId && appLogoUrl"
                class="login-title"
                :src="appLogoUrl"
                height="37"
                width="100"
            >
            <span class="title">{{ headerTitle }}</span>
            <span v-if="isPC && needLogin" class="to-login">{{ $tc('login.haveRegistereLoginNow', 1) }}<router-link :to="loginLink">{{ $tc('login.haveRegistereLoginNow', 2) }}</router-link></span>
        </div>
        <img v-else :src="appLogoUrl || '/static/img/bestsign-logo.png'">
    </div>
</template>

<script>
import { isPC } from 'src/common/utils/device.js';
export default {
    props: {
        headerTitle: {
            type: String,
            default: '',
        },
        clientId: {
            type: String,
            default: '',
        },
        redirectUrl: {
            type: String,
            default: '',
        },
        entLogoUrl: {
            type: String,
            default: '',
        },
        needLogin: {
            type: Number,
            default: 1,
        },
    },
    data() {
        return {
            isPC: isPC(),
            HOST_ENV: this.GLOBAL.HOST_ENV,
        };
    },
    computed: {
        loginLink() {
            let link = '/login';
            if (this.HOST_ENV === 'CCB') {
                const id = this.$route.query.companyid ? this.$route.query.companyid : '';
                link = `/login?companyid=${id}`;
            }
            if (this.clientId) {
                link = `/oauth/login?clientId=${this.clientId}&redirectUrl=${this.redirectUrl}${this.$route.query.gotoAuth ? ('&gotoAuth=' + this.$route.query.gotoAuth) : ''}`;
            }
            return link;
        },

        appLogoUrl() {
            return this.entLogoUrl;
        },
    },
};
</script>

<style lang="scss">
	.register-header {
		width: 100%;
		background: $header-color;
		.container {
			max-width: 100%;
			height: 65px;
			line-height: 65px;
			color: #fff;
			margin: 0 auto;
			.el-icon-ssq-fenzu3 {
				font-size: 40px;
				vertical-align: middle;
				cursor: pointer;
			}
			.line {
				display: inline-block;
				padding-left: 18px;
				height: 30px;
				// border-right: 1px solid #eee;
				border-right-width: 1px;
				border-right-style: solid;
				border-right-color: rgba(255, 255, 255, .2);
				vertical-align: middle;
			}
			.login-title {
				vertical-align: middle;
				margin-left: 21px;
			}
			.title {
				font-size: 20px;
				// font-weight: bold;
				margin-left: 12px;
			}
			.to-login {
				float: right;
				line-height: 65px;
				font-size: 14px;
				a {
					color: $base-color;
				}
			}
		}
        &.register-header__mobile {
            background: #fff;
            padding: 20px 16px 0 16px;
            box-sizing: border-box;
            img {
                width: 74px;
            }
        }
	}

	/*
		手机屏幕适配
	*/

	@media (min-width: 320px) and (max-width: 768px) {

		.register-header {
			.container {
				width: 100%;
				text-align: center;
			}

		}

	}
</style>
