<template>
    <div class="box-content">
        <p>档案柜：{{ archiveContent.archiveName }}</p>
        <!-- 个人档案柜 -->
        <div class="basic-info section" v-if="archiveContent.type === 0">
            <h4>基础资料采集</h4>
            <el-checkbox v-model="isPerAuth" :disabled="true">
                个人实名认证
            </el-checkbox>
            <el-checkbox v-for="(item, index) in baseArchiveCollectionListFiltered"
                :key="index"
                v-model="item.selectFlag"
                :disabled="true"
            >
                {{ item.collectionName }} <span v-if="requiredField(item)">(必选项)</span>
            </el-checkbox>
        </div>
        <!-- 企业档案柜 -->
        <div class="basic-info section" v-else-if="archiveContent.type === 3">
            <h4>申请获取授权</h4>
            <el-checkbox v-for="(item, index) in entAuthorizationList" :key="index" v-model="item.checked" :disabled="true">
                {{ item.label }}
            </el-checkbox>
        </div>
        <div class="self-defined section" v-if="archiveContent.boxArchiveCollectionList.length">
            <h4>自定义资料采集</h4>
            <CustomInfoItem v-for="(item, index) in archiveContent.boxArchiveCollectionList"
                :key="index"
                :infoIndex="index"
                :customInfo="item"
            >
            </CustomInfoItem>
        </div>
    </div>
</template>
<script>
import CustomInfoItem from './CustomInfoItem.vue';
export default {
    components: {
        CustomInfoItem,
    },
    props: {
        archiveId: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            archiveContent: {
                boxArchiveCollectionList: [],
            },
        };
    },
    computed: {
        // 基础采集项过滤
        baseArchiveCollectionListFiltered() { /** SAAS-6401 档案+隐藏“刷脸校验选项” */
            return this.archiveContent.baseArchiveCollectionList.filter(item => item.collectionCode !== '1008' && item.selectFlag);
        },
        // 是否个人实名
        isPerAuth() {
            // authType: 0, // 邀请用户实名，0 未勾选 1个人认证 2企业认证
            return this.archiveContent.authType === 1;
        },
        // 是否企业实名
        isEntAuth() {
            return this.archiveContent.authType === 2;
        },
        // 企业档案柜获取授权
        entAuthorizationList() {
            return [
                { checked: this.isEntAuth, label: '企业实名认证' },
                { checked: !!this.archiveContent.seal, label: '企业印章（用于代理签署）' },
                { checked: !!this.archiveContent.send, label: '合同代发' },
            ];
        },
    },
    methods: {
        initData() {
            this.$http.get(`/octopus-api/box/archive/construct?archiveId=${this.archiveId}`).then(res => {
                const result = res.data;
                if (result) {
                    this.archiveContent = result;
                    // this.baseArchiveCollectionList = result.baseArchiveCollectionList;
                    // this.boxArchiveCollectionList = result.boxArchiveCollectionList;
                    // this.authType = result.authType;
                    // this.archiveName = result.archiveName;
                    // 返回的基础资料'联系方式'排在最后面，这里要换下位置
                    this.archiveContent.baseArchiveCollectionList && this.archiveContent.baseArchiveCollectionList.splice(1, 0, ...this.archiveContent.baseArchiveCollectionList.splice(6, 1));
                }
            });
        },
        // 基础采集资料必选项
        requiredField(item) {
            // 选择了合同模板时联系方式才为必填
            return item.collectionName === '姓名' || (item.collectionName === '联系方式' &&
                ((this.archiveContent.authType !== 0)));
        },
    },
    created() {
        this.initData();
    },
};
</script>
<style lang="scss">
    .box-content{
        .basic-info{
            margin: 20px 0;
            line-height: 16px;
            h4{
                margin-bottom: 14px;
            }
            .el-checkbox{
                margin: 0 30px 10px 0;
                &:last-child{
                    margin-right: 0;
                }
                .el-checkbox__inner{
                    background: #F8F8F8;
                    border-color:#DDDDDD;
                    &::after{
                        border-color: #DDDDDD;
                    }
                }
                .el-checkbox__label {
                    font-size: 12px;
                    padding-left: 10px;
                    color: #999;
                }
            }
        }
    }
</style>
