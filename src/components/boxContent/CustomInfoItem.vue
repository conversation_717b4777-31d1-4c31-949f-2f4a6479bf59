<template>
    <div class="custom-info">
        <div class="custom-info__title">
            {{ infoIndex + 1 }}.{{ storeTypeList[customInfo.storeType] }}
        </div>
        <div class="input-line">
            <p class="input-line__label">
                资料名称：
            </p>
            <el-input size="small"
                class="custom-info__input"
                v-model="customInfo.collectionName"
                :disabled="true"
                :maxlength="30"
                clearable
            >
            </el-input>
        </div>
        <div class="input-line"
            v-if="isRemarksType"
        >
            <p class="input-line__label">备注：</p>
            <el-input size="small"
                class="custom-info__input"
                v-model="customInfo.collectionRemarks"
                :disabled="true"
                :maxlength="50"
                clearable
            >
            </el-input>
        </div>
        <div v-if="isChoiceType">
            <div class="input-line option-line"
                v-for="(item, index) in customInfo.boxCollectionExtends"
                :key="index"
            >
                <p class="input-line__label">选项{{ index + 1 }} {{ index === 0 || index === 1 ? '(必填)':'' }}：</p>
                <el-input size="small"
                    class="custom-info__input"
                    v-model="item.name"
                    :disabled="true"
                    :maxlength="20"
                    clearable
                >
                </el-input>
            </div>
        </div>
        <div v-if="isAddressType" class="input-line address-line">
            <p class="input-line__label">收集地址的详细程度(必填)：</p>
            <el-checkbox
                v-for="item in addressCheckboxs"
                :key="item.key"
                :disabled="true"
                :value="item.key <= customInfo.extendedField"
            >
                {{ item.text }}
            </el-checkbox>
        </div>
        <div class="operation-area">
            {{ customInfo.fillType ?'必填项':'选填项' }}
        </div>
    </div>
</template>

<script>
export default {
    name: 'CustomInfoItem',
    props: {
        customInfo: {
            type: Object,
            require: true,
            default: () => {},
        },
        infoIndex: {
            type: Number,
            require: true,
            default: 0,
        },
    },
    data() {
        return {
            addressCheckboxs: [
                {
                    key: '0', // 省
                    text: '省',
                },
                {
                    key: '1', // 市
                    text: '市',
                },
                {
                    key: '2', // 区
                    text: '区',
                },
                {
                    key: '3', // 详细地址（如街道、门牌号等）
                    text: '详细地址（如街道、门牌号等）',
                },
            ],
            storeTypeList: {
                1: '文字资料',
                2: '图片资料',
                3: '单选资料',
                4: '多选资料',
                5: 'PDF资料',
                6: '日期资料',
                7: '地址资料',
            },
        };
    },
    computed: {
        // 可以添加备注的类型
        isRemarksType() {
            return [1, 2, 6].includes(this.customInfo.storeType);
        },
        // 需要添加选项的类型
        isChoiceType() {
            return [3, 4].includes(this.customInfo.storeType);
        },
        // 需要添加地址的类型
        isAddressType() {
            return this.customInfo.storeType === 7;
        },
    },
};
</script>

<style lang="scss">
.custom-info{
    position: relative;
    margin-right: 20px;
    padding: 5px 0px 8px 80px;
    font-size: 12px;
    color: #333333;
    overflow: hidden;
    .custom-info__title {
        position: absolute;
        left: 10px;
        top: 26px;
        line-height: 30px;
    }
    .input-line {
        display: inline-block;
        vertical-align: top;
        .input-line__label {
            height: 20px;
            line-height: 20px;
            color: #999999;
        }
    }
    .option-line {
        margin-top: 5px;
    }
    .custom-info__input {
        width: 289px!important;
        margin-right: 9px;
        height: 30px;
        line-height: 30px;
        float: left;
        &.el-input.is-disabled .el-input__inner{
            width: 289px!important;
            color: #999;
            background: #F8F8F8;
            border-color:#DDDDDD;
            margin-left: 0;
            &:hover{
                    border-color: #DDDDDD;
                    box-shadow:none;
            }
        }
    }
    .address-line {
        .el-checkbox {
            margin-right: 40px;
            margin-left: 0;
            line-height: 30px;
            &:last-child{
                margin-right: 0px;
            }
            .el-checkbox__inner{
                background: #F8F8F8;
                border-color:#DDDDDD;
                &::after{
                    border-color: #DDDDDD;
                }
            }
            .el-checkbox__label {
                font-size: 14px;
                padding-left: 10px;
                color: #999;
            }
        }
    }
    .operation-area{
        position: absolute;
        top: 25px;
        right: 0;
        width: 40px;
        line-height: 30px;
        color: #999999;
    }
}
</style>
