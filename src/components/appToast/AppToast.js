import AppToast from './AppToast.vue';

const plugin = {};
let currentMsg = null;
let instance = null;

plugin.install = function(Vue) {
    const ToastController = Vue.extend(AppToast);
    const $AppToast = (option = {}) => {
        instance = new ToastController().$mount(document.createElement('div'));
        instance.iClass = typeof option === 'string' ? '' : option.iClass;
        instance.title = typeof option === 'string' ? '' : option.title;
        instance.message = typeof option === 'string' ? option : option.message;
        instance.btnText = typeof option === 'string' ? '' : option.btnText;
        instance.dispatch = dispatchCallBack;
        document.body.appendChild(instance.$el);
        return new Promise((resolve, reject) => {
            currentMsg = { resolve, reject };
        });

        function dispatchCallBack(action) {
            if (action === 'resolve') {
                currentMsg.resolve('confirm');
            } else {
                currentMsg.reject('cancel');
            }
        }
    };
    Vue.prototype.$appToast = $AppToast;
    Vue.$appToast = $AppToast;
};

export default plugin;
