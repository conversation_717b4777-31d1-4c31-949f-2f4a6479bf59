<template>
    <div :class="iClass">
        <transition name="fadeIn">
            <div v-show="show" class="AppToast-cpn" @click="handleClickMask">
                <div class="AppToast-container">
                    <div class="title">{{ title }}</div>
                    <div class="message">{{ message }}</div>
                    <div class="AppToast-btn" @click="handleClickConfirm">{{ btnText || `确定` }}</div>
                </div>
            </div>
        </transition>
    </div>
</template>
<script>
export default {
    data() {
        return {
            show: true,
            iClass: '',
            title: '',
            message: '',
            btnText: '',
            dispatch: null,
        };
    },
    watch: {
        show() {
            this.$destroy(true);
            this.$el.parentNode.removeChild(this.$el);
        },
    },
    methods: {
        handleClickMask(e) {
            if (e.target.className.indexOf('appToast-cpn') > -1) {
                this.show = false;
                this.dispatch('reject');
            }
        },
        handleClickConfirm() {
            this.show = false;
            this.dispatch('resolve');
        },
    },
};
</script>
<style lang="scss">
	.AppToast-cpn {
		* {
			box-sizing: border-box;
		}
		z-index: 9999;
		position: fixed;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		background-color: rgba(0, 0, 0, .7);
		.AppToast-container {
			position: fixed;
			top: 50%;
			left: 50%;
			width: 270px;
			text-align: center;
			background-color: #fff;
			border-radius: 10px;
			transform: translateX(-50%) translateY(-50%);
			.title {
				font-size: 17px;
				padding-top: 23px;
			}
			.message {
				font-size: 13px;
				line-height: 20px;
				padding: 10px 15px 22px;
				border-bottom: 1px solid #e0e0e0;
			}
			.AppToast-btn {
				height: 43px;
				line-height: 43px;
				font-size: 17px;
				color: #157efb;
				text-align: center;
			}
		}

		// 动画
		.fadeIn-enter-active{
			transition: all .6s ease;
		}
		.fadeIn-leave-active {
			transition: all .6s ease;
		}
		.fadeIn-enter, .fadeIn-leave-to /* .fade-leave-active in below version 2.1.8 */ {
			opacity: 0;
			background-color: red;
		}
	}

</style>
