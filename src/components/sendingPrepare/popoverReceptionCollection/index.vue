// 前台代收弹框
<template>
    <div
        class="popoverReceptionCollection"
        v-clickoutside="handleClickOutside"
    >
        <el-input
            :placeholder="tagShow ? '' : '点击输入框选择代收账号'"
            class="popoverReceptionCollection__mainInput"
            v-model.trim="account"
            :readonly="true"
            @focus="handleMainInputFocus"
        >
        </el-input>
        <p
            v-if="tagShow"
            class="popoverReceptionCollection__tagCon"
        >
            <span
                class="popoverReceptionCollection__tag"
                @click="handleMainInputFocus"
            >
                对方前台代收
            </span>
            <i
                class="el-icon-ssq-delete"
                @click="handleClearData"
            >
            </i>
        </p>
        <el-popover
            v-model="popoverShow"
            placement="bottom"
            visible-arrow="false"
            popper-class="popoverReceptionCollectionPop"
        >
            <p
                class="popoverReceptionCollectionPopNotice"
                :class="((noticeAccountLess && collectionAccountsValuableLength <= minCollectionAccountsNum) || (noticeAccountMore && collectionAccountsLength >= maxCollectionAccountsNum)) && 'popoverReceptionCollectionPopNoticeAttention'"
            >
                <i class="el-icon-ssq-tishi1"></i>
                <!-- 有待认领账号 -->
                <template v-if="haveAccount">
                    <template v-if="noticeAccountLess && collectionAccountsValuableLength <= minCollectionAccountsNum">
                        必须填写{{ minCollectionAccountsNum }}个或{{ minCollectionAccountsNum }}个以上的待认领账号
                    </template>
                    <template v-else-if="noticeAccountMore && collectionAccountsLength >= maxCollectionAccountsNum">
                        最多只能添加{{ maxCollectionAccountsNum }}个待认领账号
                    </template>
                    <template v-else>
                        一份合同同时发给多人，且该合同最终只被一人认领
                    </template>
                </template>
                <!-- 无待认领账号 -->
                <template v-else>
                    对方无账号时，上上签可将认领链接推送到通知手机上
                </template>
            </p>
            <div class="popoverReceptionCollectionPop__main">
                <div class="popoverReceptionCollectionPop__radios">
                    <el-radio
                        class="popoverReceptionCollectionPop__radio--first"
                        v-model="haveAccount"
                        :label="true"
                    >
                        待认领账号
                    </el-radio>
                    <el-radio
                        v-model="haveAccount"
                        :label="false"
                    >
                        签约方无账号
                    </el-radio>
                </div>
                <div class="popoverReceptionCollectionPop__accountInputs">
                    <!-- 有待认领账号 -->
                    <template v-if="haveAccount">
                        <p class="popoverReceptionCollectionPop__accountInputsInfo">
                            添加账号
                            <span class="popoverReceptionCollectionPop__accountInputsInfo--one">
                                （必须填写{{ minCollectionAccountsNum }}个或以上）
                            </span>：
                        </p>
                        <p
                            class="popoverReceptionCollection__perAccountInputCon"
                            v-for="(item, index) in collectionAccounts"
                            :key="index"
                        >
                            <el-input
                                placeholder="手机号/邮箱"
                                class="popoverReceptionCollection__accountInput"
                                v-model.trim="item.value"
                                @blur="handleCheck(index)"
                            >
                            </el-input>
                            <!-- 超过最少账号数量，展示删除按钮 -->
                            <i
                                v-if="collectionAccountsLength > minCollectionAccountsNum"
                                class="el-icon-ssq-delete popoverReceptionCollection__accountDeleteIcon"
                                @click="handleDeleteAccount(index)"
                            >
                            </i>
                            <!-- 格式错误 -->
                            <span
                                v-if="item.errMsg"
                                class="popoverReceptionCollection__accountErrorMsg"
                            >
                                {{ item.errMsg }}
                            </span>
                        </p>
                        <p class="popoverReceptionCollectionPop__accountInputsOperations">
                            <span
                                class="popoverReceptionCollectionPop__accountInputsOperationAdd"
                                @click="handleAddAccount"
                            >
                                <i class="el-icon-ssq-jia"></i>
                                新增
                            </span>
                            <span
                                class="popoverReceptionCollectionPop__accountInputsOperationImport"
                                @click="clickAdressBook"
                            >
                                从地址薄导入
                            </span>
                        </p>
                    </template>
                    <!-- 无待认领账号 -->
                    <template v-else>
                        <p class="popoverReceptionCollectionPop__accountInputsInfo">
                            填写通知手机（选填）：
                        </p>
                        <p class="popoverReceptionCollection__perAccountInputCon">
                            <el-input
                                placeholder="手机号"
                                class="popoverReceptionCollection__accountInput"
                                v-model.trim="notification"
                                @focus="notificationErrMsg = ''"
                                @blur="handleCheckNotification"
                            >
                            </el-input>
                            <!-- 格式错误 -->
                            <span
                                v-if="notificationErrMsg"
                                class="popoverReceptionCollection__accountErrorMsg"
                            >
                                {{ notificationErrMsg }}
                            </span>
                        </p>
                    </template>
                </div>
                <el-button
                    class="popoverReceptionCollectionPop__doneBtn btn-type-one"
                    @click="handleDone"
                >
                    完成
                </el-button>
            </div>
        </el-popover>
        <!-- 联系人地址簿 -->
        <DialogAddressBook
            v-if="addressBookDialogVisible"
            :visible.sync="addressBookDialogVisible"
            :addressBookSelectType="addressBookSelectType"
            @save="clickSaveChoosed"
            @close="addressBookDialogVisible = false"
        >
        </DialogAddressBook>
    </div>
</template>

<script>
import Clickoutside from 'element-ui/lib/utils/clickoutside';
import regRules from 'utils/regs.js';
import DialogAddressBook from 'src/components/sendingPrepare/dialogAddressBook/index.vue';

export default {
    components: {
        DialogAddressBook,
    },
    directives: { // 声明指令
        Clickoutside,
    },
    props: {
        notificationOuter: { // 前台代收外部的通知手机号
            default: '',
            type: String,
        },
        existingCollectionAccounts: { // 后端传来的初始数据
            default: function() {
                return [];
            },
            type: Array,
        },
        ifProxyClaimerEnd: { // 后端传来的初始数据，是否使用了前台代收，只用于初始化
            default: false,
            type: Boolean,
        },
    },
    data() {
        return {
            // 地址簿
            addressBookDialogVisible: false,
            addressBookSelectType: '',

            maxCollectionAccountsNum: 5, // 最多可填写的账号数量
            minCollectionAccountsNum: 2, // 最少可填写的账号数量
            popoverShow: false,
            account: '',
            haveAccount: true, // 是否有待认领账号
            noticeAccountLess: false,
            noticeAccountMore: false,
            tagShow: false,
            collectionAccounts: [...this.cloneDefaultCollectionAccounts(), ...this.cloneDefaultCollectionAccounts()], // 待认领的账号们
            notification: '', // 通知手机
            notificationErrMsg: '',
        };
    },
    computed: {
        collectionAccountsLength() {
            return this.collectionAccounts.length;
        },
        collectionAccountsValuable() {
            return this.collectionAccounts.filter(item => item.value);
        },
        collectionAccountsValuableLength() {
            return this.collectionAccountsValuable.length;
        },
    },
    watch: {
        notificationOuter: {
            handler(val) {
                this.notification = val;
            },
            immediate: true,
        },
        existingCollectionAccounts: {
            handler(val) {
                if (this.ifProxyClaimerEnd) {
                    this.tagShow = true;

                    if (val && val.length > 0) {
                        this.haveAccount = true;
                        this.collectionAccounts = val.map(item => {
                            return {
                                value: item,
                                errMsg: '',
                            };
                        });
                    } else {
                        this.haveAccount = false;
                    }
                }
            },
            immediate: true,
        },
    },
    methods: {
        cloneDefaultCollectionAccounts() {
            const defaultCollectionAccounts = [{
                value: '',
                errMsg: '',
            }];
            return defaultCollectionAccounts;
        },
        // 点击地址簿
        clickAdressBook() {
            if (this.collectionAccountsValuableLength < this.maxCollectionAccountsNum) {
                this.addressBookSelectType = 'checkBox';
                this.addressBookDialogVisible = true;
            }
        },
        // 点击地址簿保存
        clickSaveChoosed(param) {
            if (param.choosedMembers.length && param.choosedMembers.length > 0) {
                this.initNoticeData();
                let choosedMembers = param.choosedMembers
                    .filter(item => {
                        return item.account || item.contactAccount;
                    })
                    .map(item => {
                        return {
                            value: item.account || item.contactAccount,
                            errMsg: '',
                        };
                    });
                const num = this.maxCollectionAccountsNum - this.collectionAccountsValuableLength;
                if (choosedMembers.length > num) {
                    choosedMembers = choosedMembers.slice(0, num);
                    this.noticeAccountMore = true;
                }
                this.collectionAccounts = [...this.collectionAccountsValuable, ...choosedMembers];
                if (this.collectionAccounts.length === 1) {
                    this.handleAddAccount();
                }
            }
            this.addressBookDialogVisible = false;
        },
        handleClickOutside() {
            this.popoverShow = false;
        },
        initNoticeData() {
            this.noticeAccountLess = false;
            this.noticeAccountMore = false;
        },
        handleMainInputFocus() {
            this.popoverShow = true;
        },
        // 删除前台代收的数据
        handleClearData() {
            this.initNoticeData();
            this.collectionAccounts = [...this.cloneDefaultCollectionAccounts(), ...this.cloneDefaultCollectionAccounts()];
            this.tagShow = false;
        },
        // 新增账号
        handleAddAccount() {
            if (this.collectionAccountsLength < this.maxCollectionAccountsNum) {
                this.collectionAccounts.push(this.cloneDefaultCollectionAccounts()[0]);
            } else {
                this.noticeAccountMore = true;
            }
        },
        // 删除账号
        handleDeleteAccount(i) {
            if (this.collectionAccountsLength > this.minCollectionAccountsNum) {
                this.collectionAccounts.splice(i, 1);
            }
        },
        // 校验格式
        handleCheck(i) {
            const curAccountObj = this.collectionAccounts[i];
            const v = curAccountObj.value;
            if (v && !regRules.userAccount.test(v)) {
                curAccountObj.errMsg = '请输入正确的邮箱或手机号';
            } else {
                curAccountObj.errMsg = '';
            }
        },
        handleCheckNotification() {
            if (this.notification && !regRules.userPhone.test(this.notification)) {
                this.notificationErrMsg = '请输入正确的手机号';
            } else {
                this.notificationErrMsg = '';
            }
        },
        // 保存前格式校验，抽离出来方便外部调用
        handleCheckBeforeSave() {
            if (this.haveAccount) { // 有待认领账号
                this.initNoticeData();
                // 校验是否有报错
                if (this.collectionAccounts.some((item) => {
                    return item.errMsg !== '';
                })) {
                    return false;
                }
                // check填写的账号数量
                if (
                    this.collectionAccountsValuableLength < this.minCollectionAccountsNum
                ) {
                    this.noticeAccountLess = true;
                    return false;
                }
            } else { // 无待认领账号
                this.handleCheckNotification();
                if (this.notificationErrMsg) {
                    return false;
                }
            }
            return true;
        },
        // 前台代收账号填写完成
        handleDone() {
            const status = this.handleCheckBeforeSave();
            if (status) {
                if (this.haveAccount) { // 有待认领账号
                    const collectionAccounts =
                        this.collectionAccounts
                            .filter(item => item.value !== '')
                            .map(item => item.value);

                    if (new Set(collectionAccounts).size < collectionAccounts.length) {
                        this.$MessageToast.error('账号不能重复');
                        return;
                    }
                    this.$emit('save', collectionAccounts);
                } else { // 无待认领账号
                    this.$emit('update:notificationOuter', this.notification);
                    this.$emit('updateNotificationStatus');
                    this.$emit('save', []);
                }
                this.popoverShow = false;
                this.tagShow = true;
            }
        },
    },
};
</script>

<style lang="scss">
.popoverReceptionCollection {
    position: relative;
    width: 192px;
    height: 30px;
    font-size: 12px;
}
.popoverReceptionCollection__tagCon {
    position: absolute;
    top: 6.5px;
    left: 11px;
    .el-icon-ssq-delete {
        padding-left: 7px;
        color: #ccc;
        cursor: pointer;
        &:hover {
            color: #999;
        }
    }
}
.popoverReceptionCollection__tag {
    display: inline-block;
    width: 85px;
    height: 19px;
    line-height: 17px;
    background: #DAF0FF;
    border: 1px solid #92C4EB;
    border-radius: 2px;
    color: #127FD2;
    text-align: center;
    cursor: pointer;
}
// 弹框
.popoverReceptionCollectionPop {
    width: 340px;
    // max-height: 280px;
    padding: 0;
    // overflow-y: scroll;
}
.popoverReceptionCollectionPopNotice {
    height: 30px;
    padding-left: 20px;
    line-height: 30px;
    background-color: #F8F8F8;
    color: #999;
    text-align: left;
}
.popoverReceptionCollectionPopNoticeAttention {
    background: rgba(255,85,0,0.10);
    color: #333;
    .el-icon-ssq-tishi1 {
        color: rgb(255, 85, 0);
    }
}
.popoverReceptionCollectionPop__main {
    padding: 14px 20px 17px;
}
// 单选框
.popoverReceptionCollectionPop__radio--first {
    margin-right: 71px;
}
// 账号输入部分
.popoverReceptionCollectionPop__accountInputs {
    margin-top: 20px;
}
.popoverReceptionCollectionPop__accountInputsInfo {
    margin-bottom: 8px;
}
.popoverReceptionCollectionPop__accountInputsInfo--one {
    color: #3F3F3F;
}
.popoverReceptionCollection__accountInput {
    margin-bottom: 15px;
}
.popoverReceptionCollection__perAccountInputCon {
    position: relative;
}
.popoverReceptionCollection__accountInput {
    width: 270px;
}
.popoverReceptionCollection__accountDeleteIcon {
    position: absolute;
    right: 0;
    top: 10px;
    font-size: 13px;
    font-weight: bold;
    color: #ccc;
    cursor: pointer;
    &:hover {
        color: #999;
    }
}
.popoverReceptionCollection__accountErrorMsg {
    position: absolute;
    left: 0;
    bottom: 0;
    color: #f86b26;
    font-size: 10px;
}
.popoverReceptionCollectionPop__accountInputsOperations {
    position: relative;
    margin-bottom: 19px;
    color: #127FD2;
}
.popoverReceptionCollectionPop__accountInputsOperationAdd,
.popoverReceptionCollectionPop__accountInputsOperationImport {
    display: inline-block;
    width: 49%;
    cursor: pointer;
}
.popoverReceptionCollectionPop__accountInputsOperationImport {
    text-align: right;
    padding-right: 30px;
}
.popoverReceptionCollectionPop__doneBtn {
    width: 70px;
    height: 33px;
}
</style>
<style lang="scss">
.footer-follow-page .prepare-container.footer-follow-page-container {
    padding-bottom: 400px;
}
</style>
