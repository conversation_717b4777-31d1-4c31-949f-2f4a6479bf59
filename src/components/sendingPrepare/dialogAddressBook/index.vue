<!-- 对话框：联系人地址簿 -->
<template>
    <el-dialog
        class="addressBookDialog"
        size="small"
        :title="$t('addReceiver.contactAddress')"
        :modal-append-to-body="true"
        :visible.sync="addressBookDialogVisible"
        @close="handleClose"
        :close-on-click-modal="false"
    >
        <AddressBook
            :selectType="addressBookSelectType"
            :key="addressBookKey"
            @showDataBoxInvite="$emit('showDataBoxInvite')"
            @choose="onAddressBookChoose"
        >
        </AddressBook>
        <div slot="footer" class="dialog-footer register addressBookDialog-btn">
            <el-button type="primary" @click="clickSaveChoosed">{{ $t('addReceiver.save') }}</el-button>
        </div>
    </el-dialog>
</template>

<script>
import AddressBook from './addressBook';

export default {
    components: {
        AddressBook,
    },
    props: {
        addressBookSelectType: {
            default: '',
            type: String,
        },
        visible: {
            default: false,
            type: Boolean,
        },
    },
    data() {
        return {
            addressBookKey: 0,
            choosedType: '',
            choosedMembers: [],
            addressBookDialogVisible: this.visible,
        };
    },
    computed: {

    },
    watch: {
        visible(val) {
            this.addressBookDialogVisible = val;
        },
    },
    methods: {
        handleClose() {
            this.addressBookKey = Math.random();
            this.$emit('close');
        },
        // 选中地址簿某人时
        onAddressBookChoose(members, opts) {
            this.choosedType = opts.type;
            this.choosedMembers = members;
        },
        clickSaveChoosed() {
            this.$emit('save', {
                choosedType: this.choosedType,
                choosedMembers: this.choosedMembers,
            });
        },
    },
};
</script>

<style lang="scss">
// 联系人地址簿
    .addressBookDialog {
        overflow: hidden;
        * {
            box-sizing: border-box;
        }
        .el-dialog {
            width: 650px;
        }
        .el-dialog__header {
            padding: 16px 30px;
            .el-dialog__title {
                font-weight: normal;
            }
        }
        .el-dialog__body {
            padding: 0;
        }
        .el-dialog__footer {
            padding: 12px 33px 15px;
            border-top: 1px solid #eee;
        }

        .addressBookDialog-btn {
            button {
                width: 100px;
                height: 34px;
                padding: 0;
                background-color: #127fd2;
                font-weight: normal;
            }
        }

    }
</style>
