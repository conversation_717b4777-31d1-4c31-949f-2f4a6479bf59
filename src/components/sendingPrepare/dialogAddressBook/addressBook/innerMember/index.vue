<template>
    <div class="address-book__inner-member">
        <div class="address-book__outer-contact-notice">
            <span>{{ $t('addressBook.innerMember.tips') }}</span>
            <span class="invite-btn" @click="toConsole">{{ $t('addressBook.innerMember.operation') }}</span>
        </div>
        <div class="address-book__content-search search-list fl">
            <DeptTree
                v-if="type"
                :type="type"
                :entGroupId="entGroupId"
                @getCurrentDept="handleGetCurrentDept"
                @updateKeyword="updateKeyword"
                ref="CompanyDeptTree"
            ></DeptTree>
        </div>
        <div class="address-book__content-member fl">
            <!-- 多选成员 -->
            <p v-show="memberList.length">
                <el-checkbox :indeterminate="isSelectedAll" v-model="selectedAllMembers" @change="handleCheckAllChange">{{ $t('docContentTable.searchAll') }}</el-checkbox>
            </p>
            <el-checkbox-group
                v-model="selectedMembers"
                @change="handleSelectChange"
            >
                <p v-for="(member, index) in memberList" :key="index">
                    <el-checkbox :label="member" :key="member.userId">
                        {{ `${member.showName}(${member.account})` || `${member.account}` }}
                    </el-checkbox>
                </p>
            </el-checkbox-group>
        </div>
        <div class="address-book__content-selected fl">
            <p>{{ $t('addressBook.selected') }}</p>
            <p class="select-list" v-for="(member, index) in selectedMembers" :key="index">
                <span class="user-name ellipsis" :title="userName(member)">{{ userName(member) }}</span>
                <i class="el-icon-ssq-delete" @click="cancelSelectedMember(index)"></i>
            </p>
        </div>
    </div>
</template>

<script>
import DeptTree from '../deptTree';
export default {
    components: {
        DeptTree,
    },
    props: {
        onlyActive: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            type: '',
            entGroupId: '',
            selectedAllMembers: false,
            memberList: [],
            cacheMembers: [],
            selectedMembers: [], // 返回给父组件的选中的成员
            keyword: '',
        };
    },
    computed: {
        hasGroupConsole() {
            return this.$store.state.commonHeaderInfo.hasGroupConsole;
        },
        isSelectedAll() {
            return this.selectedMembers.length > 0 && this.selectedMembers.length < this.memberList.length;
        },
    },
    watch: {
        selectedMembers: 'handleChoose',
    },
    methods: {
        toConsole() {
            // 集团企业跳到集团控制台
            window.open(this.$store.state.commonHeaderInfo.hasGroupConsole ? '/console/group/account/members' : '/console/enterprise/account/members');
        },
        handleChoose() {
            this.$emit('choose', this.selectedMembers, {
                type: 'innerMember',
            });
        },
        userName(member) {
            return `${member.showName}(${member.account})` || `${member.account}`;
        },
        checkGroup() {
            if (this.hasGroupConsole) {
                this.$http.get('/ents/group').then(res => {
                    this.entGroupId = res.data.entGroupId;
                    this.type = 'group';
                });
            } else {
                this.type = 'company';
            }
        },
        handleGetCurrentDept(dept) {
            this.currentDept = dept;
            this.getMembers();
        },
        updateKeyword(keyword) {
            this.keyword = keyword;
            this.type === 'group' ? this.getGroupMembers() : this.getMembers();
        },
        getGroupMembers() {
            this.$http.get(`/ents/employees/search-all?searchContent=${this.keyword}&entGroupId=${this.entGroupId}`).then(res => {
                const memberList = [];
                this.cacheMembers = {};

                res.data.forEach(member => {
                    member.formatUserId = `${member.userId}${member.entName}`;
                    member.showName = `${member.entName} ${member.empName}`;
                    memberList.push(member);
                    // 通过 userId 标志缓存部门成员
                    this.cacheMembers[member.formatUserId] = member;
                });
                this.memberList = memberList;
            });
        },
        getMembers() {
            const defaultPaging = {};
            if (this.type === 'group') {
                defaultPaging.entId = this.currentDept.entId;
            }
            this.$http({
                method: 'get',
                url: `/ents/employees/active/search?searchContent=${this.keyword}&deptId=${this.currentDept.deptId}`,
                params: defaultPaging,
            }).then(res => {
                const memberList = [];
                this.cacheMembers = {};

                res.data.forEach(member => {
                    member.showName = `${member.empName}`;
                    memberList.push(member);
                    // 通过 userId 标志缓存部门成员
                    this.cacheMembers[member.userId] = member;
                });
                this.memberList = memberList;
            });
        },
        handleCheckAllChange(event) {
            // 如果全选则将 cacheMember 中的成员添加到已选成员，否则将已选成员置空
            // 并且切换全选 checkBox 的 indeterminate 状态
            if (event.target.checked) {
                for (const i in this.cacheMembers) {
                    this.selectedMembers.push(this.cacheMembers[i]);
                }
            } else {
                this.selectedMembers = [];
            }
        },
        handleSelectChange(value) {
            const checkedCount = value.length;
            this.selectedAllMembers = checkedCount === this.memberList.length;
        },
        cancelSelectedMember(index) {
            this.selectedMembers.splice(+index === 0 ? 0 : index, 1);
        },
    },
    created() {
        this.checkGroup();
    },
};
</script>
<style lang="scss">
.address-book__inner-member{
    height: 100%;
}
</style>
