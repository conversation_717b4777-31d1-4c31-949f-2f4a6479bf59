<!--
 * @Author: fan_liu
 * @Date: 2021-01-26 16:02:20
 * @LastEditors: fan_liu
 * @LastEditTime: 2021-03-02 15:19:51
 * @Description: Do not edit
-->
<template>
    <div class="address-book__outer-contact">
        <div class="address-book__outer-contact-notice">
            <span>{{ $t('addressBook.outerContacts.tips') }}</span>
            <span class="invite-btn" @click="$emit('showDataBoxInvite')">{{ $t('addressBook.outerContacts.operation') }}</span>
        </div>
        <div class="address-book__content-search fl" v-loading="searchLoading">
            <div @keyup.enter.prevent.stop="searchOuterEnt">
                <el-input :placeholder="$t('addressBook.search')" size="small" class="ssq-search department-search" v-model.trim="search.keyword">
                    <i slot="suffix" class="el-icon-ssq-sousuo" @click="searchOuterEnt"></i>
                </el-input>
            </div>
            <ul class="search-list">
                <li
                    v-for="(ent, i) in searchList"
                    :key="i"
                    :class="{active: currentEnt.name === ent.name}"
                    class="ellipsis"
                    @click="chooseEnt(ent)"
                >
                    {{ ent.name }}
                </li>
                <li class="load-more" v-show="!isEnd" @click="loadMore">{{ $t('addressBook.loadMore') }}</li>
                <li class="nomore ellipsis" v-show="isEnd">{{ $t('addressBook.end') }}</li>
            </ul>
        </div>
        <div class="address-book__content-member fl">
            <!-- 多选成员 -->
            <p v-show="memberList.length">
                <el-checkbox :indeterminate="isSelectedAll" v-model="selectedAllMembers" @change="handleCheckAllChange">{{ $t('docContentTable.searchAll') }}</el-checkbox>
            </p>
            <el-checkbox-group
                v-model="selectedMembers"
                @change="handleSelectChange"
            >
                <p v-for="(member, index) in memberList" :key="index">
                    <el-checkbox class="ellipsis" :label="member" :key="member.id">
                        {{ userName(member) }}
                    </el-checkbox>
                </p>
            </el-checkbox-group>
        </div>
        <div class="address-book__content-selected fl">
            <p>{{ $t('addressBook.selected') }}</p>
            <p class="select-list" v-for="(member, index) in selectedMembers" :key="index">
                <span class="user-name ellipsis" :title="userName(member)">{{ userName(member) }}</span>
                <i class="el-icon-ssq-delete" @click="cancelSelectedMember(index)"></i>
            </p>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        onlyActive: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            selectedAllMembers: false,
            memberList: [],
            selectedMembers: [], // 返回给父组件的选中的成员
            keyword: '',
            search: {
                pageNum: 1,
                pageSize: 10,
                keyword: '',
            },
            isEnd: false,
            searchList: [],
            currentEnt: {},
            showDataBoxInviteDialog: false,
            searchLoading: false,
        };
    },
    computed: {
        hasGroupConsole() {
            return this.$store.state.commonHeaderInfo.hasGroupConsole;
        },
        isSelectedAll() {
            return this.selectedMembers.length > 0 && this.selectedMembers.length < this.memberList.length;
        },
    },
    watch: {
        selectedMembers: 'handleChoose',
    },
    methods: {
        chooseEnt(ent) {
            this.currentEnt = ent;
            this.getContacts();
        },
        searchOuterEnt() {
            this.searchList = [];
            this.getOuterEnt();
        },
        loadMore() {
            this.search.pageNum += 1;
            this.getOuterEnt();
        },
        getOuterEnt() {
            this.searchLoading = true;
            this.$http.get('/octopus-api/box/contact/enterprise/paging', {
                params: this.search,
            }).then(res => {
                this.searchList = this.searchList.concat(res.data.records);
                this.isEnd = this.searchList.length === +res.data.total;
            }).finally(() => {
                this.searchLoading = false;
            });
        },
        handleChoose() {
            this.$emit('choose', this.selectedMembers, {
                type: 'outerContact',
            });
        },
        userName(member) {
            return `${member.userName || member.entName}(${member.contact})`;
        },
        getContacts() {
            this.$http.get(`/octopus-api/box/contacts?entName=${this.currentEnt.name}`).then(res => {
                this.memberList = res.data;
            });
        },
        handleCheckAllChange(event) {
            // 如果全选则将 cacheMember 中的成员添加到已选成员，否则将已选成员置空
            // 并且切换全选 checkBox 的 indeterminate 状态
            if (event.target.checked) {
                this.selectedMembers = this.memberList;
            } else {
                this.selectedMembers = [];
            }
        },
        handleSelectChange(value) {
            const checkedCount = value.length;
            this.selectedAllMembers = checkedCount === this.memberList.length;
        },
        cancelSelectedMember(index) {
            this.selectedMembers.splice(+index === 0 ? 0 : index, 1);
        },
    },
    created() {
        this.getOuterEnt();
    },
};
</script>
<style lang="scss">
.address-book__outer-contact{
    height: 100%;
    &-notice{
        height: 30px;
        font-size: 12px;
        background: #F9F9F9;
        line-height: 30px;
        border-bottom: 1px solid #EEEEEE;
        color: #999999;
        margin-left: -30px;
        padding: 0 30px;
        .invite-btn{
            color: #127FD2;
            float: right;
            text-decoration: underline;
            cursor: pointer;
        }
    }
}
</style>
