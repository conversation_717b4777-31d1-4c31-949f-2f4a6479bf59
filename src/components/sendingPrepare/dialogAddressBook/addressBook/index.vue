<!--
 * @Author: fan_liu
 * @Date: 2020-05-15 15:18:26
 * @LastEditors: fan_liu
 * @LastEditTime: 2021-03-02 15:12:48
 * @Description: Do not edit
-->
<template>
    <div class="address-book">
        <ul class="address-book__tabs">
            <li
                class="address-book__tab"
                :class="{ active: component === tab.component }"
                v-for="(tab, index) in tabs"
                :key="index"
                @click="handleClickTab(tab)"
            >
                {{ tab.title }}
            </li>
        </ul>
        <div class="address-book__content" :class="{ 'outer': component === 'OuterContacts' }">
            <component :is="component" @choose="onChoose" @showDataBoxInvite="$emit('showDataBoxInvite')"></component>
        </div>
    </div>
</template>

<script>
import InnerMember from './innerMember';
import OuterContacts from './outerContacts';
import MyContacts from './myContacts';
export default {
    components: {
        InnerMember,
        OuterContacts,
        MyContacts,
    },
    data() {
        return {
            component: '',
        };
    },
    computed: {
        tabs() {
            if (this.$store.state.commonHeaderInfo.userType === 'Enterprise') {
                return [
                    {
                        title: this.$t('addressBook.innerMember.title'),
                        component: 'InnerMember',
                    },
                    {
                        title: this.$t('addressBook.outerContacts.title'),
                        component: 'OuterContacts',
                    },
                    {
                        title: this.$t('addressBook.myContacts.title'),
                        component: 'MyContacts',
                    },
                ];
            } else {
                return [
                    {
                        title: this.$t('addressBook.myContacts.title'),
                        component: 'MyContacts',
                    },
                ];
            }
        },
    },
    methods: {
        handleClickTab(tab) {
            this.component = tab.component;
        },
        onChoose(members, opts) {
            this.$emit('choose', members, opts);
        },
    },
    created() {
        this.component = this.tabs[0].component;
    },
};
</script>

<style lang="scss">
.address-book{
    .ellipsis{
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
    }
    &__tabs{
        line-height: 30px;
        overflow: hidden;
        padding: 0 15px;
        border-bottom: 1px solid #eee;
    }
    &__tab{
        float: left;
        margin: 0 15px;
        font-size: 12px;
        cursor: pointer;
        &.active{
            color: #127FD2;
            border-bottom: 2px solid #127FD2;
        }
    }
    &__content{
        padding-left: 30px;
        height: 280px;
        &-search{
            width: 164px;
            height: 250px;
            padding: 18px 12px 0 0;
            border-right: 1px solid #eee;
            .search-list{
                height: 200px;
                overflow: auto;
                li {
                    font-size: 12px;
                    line-height: 36px;
                    list-style: disc;
                    list-style-position: inside;
                    width: 150px;
                    cursor: pointer;
                    &.nomore{
                        list-style: none;
                        color: #ccc;
                        text-align: center;
                    }
                    &.load-more{
                        text-align: center;
                        list-style: none;
                        cursor: pointer;
                        &:hover{
                            color: #127FD2;
                        }
                    }
                }
            }
        }
        &-member, &-selected{
            width: 266px;
            font-size: 12px;
            padding: 16px 12px 0 12px;
            border-right: 1px solid #eee;
            line-height: 36px;
            height: 250px;
            overflow: auto;
            .user-name{
                display: inline-block;
                width: 125px;
                margin-right: 10px;
            }
        }
        &-selected{
            width: unset;
            border: none;
            .el-icon-ssq-delete{
                cursor: pointer;
                float: right;
                line-height: 36px;
            }
            .select-list{
                height: 36px;
            }
        }
    }
}
</style>
