<!--
 * @Author: fan_liu
 * @Date: 2021-01-26 16:29:10
 * @LastEditors: fan_liu
 * @LastEditTime: 2021-03-02 15:17:20
 * @Description: Do not edit
-->
<!-- 业务组件：部门架构树+成员搜索 -->
<!-- 引用位置 业务组件：Companyfull-->
<template>
    <div class="company-dept-tree">
        <div v-if="showMemberSearch" @keyup.enter.prevent.stop="handleSearchMember">
            <el-input :placeholder="$t('addressBook.search')" size="small" class="ssq-search department-search" v-model.trim="memberAccount">
                <i slot="suffix" class="el-icon-ssq-sousuo" @click="handleSearchMember"></i>
            </el-input>
        </div>
        <el-tree
            :data="departmentData"
            :props="defaultProps"
            :check-strictly="selectAble"
            @node-click="handleNodeClick"
            @node-expand="handleNodeExpand"
            @check-change="handleCheckChange"
            :highlight-current="true"
            node-key="deptId"
            :current-node-key="expandedKeys[0]"
            :default-expanded-keys="expandedKeys"
            class="ssq-tree dept-tree"
            :show-checkbox="selectAble"
            ref="companyDeptTree"
        >
        </el-tree>
        <template v-if="entGroupId">
            <div class="load-more" v-show="!isEnd" @click="loadMore">{{ $t('addressBook.loadMore') }}</div>
            <div class="nomore" v-show="isEnd">{{ $t('addressBook.end') }}</div>
        </template>
    </div>

</template>

<script>
export default {
    props: {
        selectAble: {
            type: Boolean,
            default: false,
        },
        showMemberSearch: {
            type: Boolean,
            default: true,
        },
        showDeptSearch: {
            type: Boolean,
            default: false,
        },
        companySelectAble: {
            type: Boolean,
            default: true,
        },
        onlyActive: {
            type: Boolean,
            default: true,
        },
        // 企业部门的企业ID
        entId: {
            type: String,
            default: '',
        },
        // 集团ID
        entGroupId: {
            type: String,
            default: '',
        },
        // 部门树类型：企业'company' || 集团'group'
        type: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            expandedKeys: [],
            // 组织架构树数据
            departmentData: [

            ],
            // 设置子节点和节点名称的参数名
            defaultProps: {
                children: 'childDeptVOS',
                label: 'deptName',
            },
            memberAccount: '',
            curDept: null,
            pageNum: 1,
            isEnd: false,
        };
    },
    methods: {
        loadMore() {
            this.pageNum += 1;
            this.getDepartments();
        },
        getDepartmentsAjax() {
            const companyUrl = '/ents/depts';
            const groupUrl = '/ents/enterprises/paging';
            if (this.type === 'group') {
                return this.$http.get(groupUrl, {
                    params: {
                        entGroupId: this.entGroupId,
                        pageNum: this.pageNum,
                        pageSize: 10,
                    },
                });
            } else {
                let paramsObj = {};
                if (this.entId) {
                    paramsObj = { entId: this.entId };
                }
                return this.$http.get(companyUrl, {
                    params: paramsObj,
                });
            }
        },
        handleDepartments(option, resData) {
            if (this.type === 'group') {
                const deptData = resData.records || [];
                deptData.forEach(item => {
                    if (item.bizName) {
                        item.deptName = `${item.deptName}_${item.bizName}`;
                    }
                });
                this.departmentData = this.departmentData.concat(deptData);
                this.isEnd = this.departmentData.length === +resData.total;

                if (option.type === 'init') {
                    this.expandedKeys = [];
                    this.expandedKeys.push(deptData[0].deptId);

                    if (option.expandedKey) {
                        this.expandedKeys.push(option.expandedKey);
                    }
                    this.curDept = deptData[0];
                    this.$emit('setTopDeptId', deptData[0].deptId);
                    this.$emit('getCurrentDept', deptData[0]);
                    this.$emit('getWholeCompanyDept', deptData);
                }
            } else {
                resData = resData || {};
                if (resData.bizName) {
                    resData.deptName = `${resData.deptName}_${resData.bizName}`;
                }
                // 设置根节点的 disabled 为 true，则禁用选中
                resData.disabled = !this.companySelectAble;

                resData.type = option.type;

                this.departmentData = [];
                this.departmentData.push(resData);

                this.expandedKeys = [];
                this.expandedKeys.push(resData.deptId);

                if (option.expandedKey) {
                    this.expandedKeys.push(option.expandedKey);
                }

                // console.log(this.$refs.companyDeptTree.filter(option.expandedKey));

                this.curDept = resData;
                this.$emit('setTopDeptId', resData.deptId);
                this.$emit('getCurrentDept', resData);
                this.$emit('getWholeCompanyDept', resData);
            }
            // this.$nextTick(function () {
            //     this.handleSetCurrentKey(this.expandedKeys[0]);
            // });
        },
        /**
         * * 获取所有部门列表
         * * option.type 用于在成员管理页判断是否需要获取部门成员
         * * 如果 type = update 则只更新当前部门的数据
         * @param  option {type: 'init','update'}
         *
         */
        getDepartments(option) {
            this.getDepartmentsAjax().then((res) => {
                // 设置根节点的 node-key 为 1，默认展开
                // res.data.id = 1;
                // this.rootKey.push(res.deptId);
                this.handleDepartments(option, res.data);
            })
                .catch(() => {

                });
        },
        // 搜索成员，并将数据返回给父组件
        handleSearchMember() {
            this.$emit('updateKeyword', this.memberAccount);
        },
        // 点击节点时，父组件可以获取当前节点的数据
        handleNodeClick(data) {
            this.curDept = data;
            this.$emit('getCurrentDept', data);
        },
        // 展开节点，返回当前节点的数据
        handleNodeExpand() {
            // this.$emit('getCurrentDept', data);

        },
        // 选中节点时，父组件可以获取当前节点的数据
        handleCheckChange(data, checked) {
            if (!this.companySelectAble && data.deptLevel === 1) {
                return false;
            }

            data.checked = checked;

            if (checked === true) {
                this.$emit('getCheckedDept', data);
            } else {
                this.$emit('getDisCheckDept', data);
            }
            // if (data.deptLevel != 1){
            // data.checked = checked;

            // }
        },
        // 通过 deptId[] 选择节点
        // handleCheckNode(list) {
        //     this.$refs.companyDeptTree.setCheckedKeys(list);
        // },
        // // 通过 deptId 取消选择节点
        // handleUnCheckNode(key) {
        //     this.$refs.companyDeptTree.setChecked(key, false);
        // },
        // handleSetCurrentKey(deptId) {
        //     this.$refs.companyDeptTree.setCurrentKey(deptId);
        // }
    },
    beforeMount() {
        this.getDepartments({ type: 'init' });
    },
};
</script>
<style lang="scss">
.company-dept-tree{
    height: 225px;
    overflow: auto;
    .nomore{
        list-style: none;
        color: #ccc;
        text-align: center;
    }
    .load-more{
        text-align: center;
        list-style: none;
        cursor: pointer;
        &:hover{
            color: #127FD2;
        }
    }
    .el-tree{
        border: none;
        .el-tree-node__expand-icon{
            margin-left: 0;
        }
    }
    .el-tree.dept-tree{
        overflow: hidden;
        .el-tree-node__content{
            height: auto;
            .el-tree-node__label::before{
                height: 0;
            }
        }
        .el-tree-node__label{
            white-space: pre-wrap;
            padding: 5px 20px 5px 0;
            line-height: 20px;
            margin-left: 0;
        }
    }
}

</style>
