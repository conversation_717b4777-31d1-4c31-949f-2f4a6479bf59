<!--
 * @Author: fan_liu
 * @Date: 2021-01-26 16:02:20
 * @LastEditors: fan_liu
 * @LastEditTime: 2021-03-02 15:21:39
 * @Description: Do not edit
-->
<template>
    <div class="address-book__my-contact">
        <div class="address-book__outer-contact-notice">
            <span>{{ $t('addressBook.myContacts.tips') }}</span>
            <span class="invite-btn" @click="toContact">{{ $t('addressBook.myContacts.operation') }}</span>
        </div>
        <div class="address-book__content-search fl">
            <div @keyup.enter.prevent.stop="searchContacts">
                <el-input :placeholder="$t('addressBook.search')" size="small" class="ssq-search department-search" v-model.trim="keyword">
                    <i slot="suffix" class="el-icon-ssq-sousuo" @click="searchContacts"></i>
                </el-input>
                <ul class="search-list">
                    <li v-for="(group, i) in groupList" :key="i" :class="{active: currentGroupId === group.groupId}" class="ellipsis" @click="changeGroup(group)">
                        {{ group.groupName }}
                    </li>
                </ul>
            </div>
        </div>
        <div class="address-book__content-member fl">
            <!-- 多选成员 -->
            <!-- <el-checkbox :indeterminate="isSelectedAll" v-model="selectedAllMembers" @change="handleCheckAllChange">{{$t('docContentTable.searchAll')}}</el-checkbox> -->
            <el-checkbox-group
                v-model="selectedMembers"
            >
                <el-checkbox :label="member" class="ellipsis" v-for="(member, index) in memberList" :key="index" :title="userName(member)">
                    {{ userName(member) }}
                </el-checkbox>
            </el-checkbox-group>
        </div>
        <div class="address-book__content-selected fl">
            <p>{{ $t('addressBook.selected') }}</p>
            <p class="select-list" v-for="(member, index) in selectedMembers" :key="index">
                <span class="user-name ellipsis" :title="userName(member)">{{ userName(member) }}</span>
                <i class="el-icon-ssq-delete" @click="cancelSelectedMember(index)"></i>
            </p>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            // selectedAllMembers: false,
            memberList: [],
            // cacheMembers: [],
            selectedMembers: [], // 返回给父组件的选中的成员
            groupList: [],
            currentGroupId: 0,
            keyword: '',
            memberLoading: false,
        };
    },
    computed: {
        // isSelectedAll() {
        //     return this.selectedMembers.length > 0 && this.selectedMembers.length < this.memberList.length;
        // },
    },
    watch: {
        selectedMembers: 'handleChoose',
    },
    methods: {
        toContact() {
            window.open('/usercenter/contacts');
        },
        getGroupList() {
            this.$http.get('/ents/contacts/out-groups').then(res => {
                this.groupList = [{
                    groupName: '全部',
                    groupId: 0,
                    sharedGroup: true,
                }].concat(res.data);
            });
        },
        handleChoose() {
            this.$emit('choose', this.selectedMembers, {
                type: 'myContacts',
            });
        },
        userName(member) {
            return `${member.contactAccount} ${member.contactName} ${member.entName || ''}`;
        },
        updateKeyword(keyword) {
            this.keyword = keyword;
            this.getMembers();
        },
        changeGroup(group) {
            if (this.currentGroupId === group.groupId) {
                return;
            }
            this.currentGroupId = group.groupId;
            this.getContacts(group.groupId, group.sharedGroup);
        },
        getContacts(groupId, sharedGroup) {
            // 避免重复调用
            if (this.memberLoading) {
                return;
            }
            this.memberLoading = true;
            return this.$http.get(`/ents/contacts/out-groups/${groupId}?isSharedGroup=${sharedGroup}`).then(res => {
                this.memberList = res.data;
            }).finally(() => {
                this.memberLoading = false;
            });
        },
        searchContacts() {
            this.$http.get(`/ents/contacts/out-search?keyWord=${this.keyword}`).then(res => {
                this.memberList = res.data;
            });
        },
        cancelSelectedMember(index) {
            this.selectedMembers.splice(+index === 0 ? 0 : index, 1);
        },
    },
    created() {
        this.getGroupList();
        this.getContacts(0, true);
    },
};
</script>
<style lang="scss">
.address-book__my-contact{
    .address-book__content-member{
        .el-checkbox{
            display: block;
            margin: 0;
            overflow: hidden;
            .el-checkbox__input{
                float: left;
                margin-top: 10px;
            }
            .el-checkbox__label{
                float: left;
                display: inline-block;
                width: 200px;
                overflow: hidden;
                text-overflow: ellipsis;
                word-break: break-all;
            }
        }
    }
}
</style>
