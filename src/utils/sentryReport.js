import * as Sentry from '@sentry/vue';
export const sentryErrorReport = (err, type) => {
    Sentry.captureException(err, {
        contexts: {
            message: {
                url: location.href,
                target: err,
                message: err.message || 'unKnownError',
                reason: err.reason || 'unKnownReason',
                types: type,
            },
        },
    });
};
/**
 * @desc 主动上报接口
 * @param { Object } option 上报配置
 * @param { Object } option.errName 错误附带数据
 * @param { Object } option.data 错误附带数据
 * @param { String } option.tag 错误附带标签
 * @param { String } option.tagContent 错误附带标签
 * @param { String } option.account 用户账号
 * @param { String } option.entId 企业id
 * */
export const customErrorReport = (option) => {
    Sentry.withScope(scope => {
        if (option.tag) {
            scope.setTag(option.tag, option.tagContent);
        }
        scope.setUser({
            account: option.account || '',
            entId: option.entId || '',
        });
        scope.setExtra('errData', option.data);
        scope.setExtra('errType', 'selfReport');
        scope.setLevel('error');
        Sentry.captureMessage(option.errName);
    });
};
