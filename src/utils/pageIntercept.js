// 拦截当前合同的查看和签署 去往实名认证前的拦截页
import router from 'src/router/router.js';
/**
 * 检查实名认证情况接口
 * 个人收件方
 * @params { String } params.mustSignWithSameEntity - 发件方是否设置以及指定人和收件人实际姓名和身份信息是否一致，true进入拦截，false不走拦截
 * @params { String } params.hasUserAuthenticated - 个人用户是否已实名认证
 * @params { String } params.idNumberForVerify - 发件方指定收件方的身份证号
 * @params { String } params.inputUserName - 发件方指定的个人收件方的姓名
 * @params { String } params.letterSender - 发件方的企业名称
 * 企业收件方
 * @params { String } params.hasEntAuth - 企业是否已实名
 * @params { String } params.hasEntEnroll - 收件方是否已加入企业
 * @params { String } params.entAdminName - 企业收件方管理员姓名
 * @params { String } params.entAdminAccount - 企业收件方管理员账号
 * @params { String } params.userId - 申请加入企业的用户id
 * @params { String } params.userAccount - 申请加入企业的用户账号
 * @params { String } params.inputEnterpriseName - 发件方指定的收件方企业名称
**/
export async function needToAuthInterceptPage(contractId) {
    const { data } = await Vue.$http.get(`/contract-api/contracts/detail/check-person-auth?contractId=${contractId}`);
    // 需要拦截的类型
    if (data.userType === 'PERSON') {
        const { contractId, mustSignWithSameEntity, hasUserAuthenticated, idNumberForVerify, inputUserName, letterSender, senderEntId } = data;
        if ((mustSignWithSameEntity && !hasUserAuthenticated) || (mustSignWithSameEntity && hasUserAuthenticated && (idNumberForVerify || inputUserName))) {
            const params = {
                contractId,
                hasUserAuthenticated,
                idNumberForVerify,
                inputUserName,
                letterSender,
                senderEntId,
            };
            try {
                sessionStorage.setItem('personAuthIntercept', JSON.stringify(params)); // 跳转页面时存储参数
                const hasQuery = location.search.length > 0;
                const hasContractIdQuery = hasQuery && location.search.includes('contractId');
                let returnUrl;
                if (hasContractIdQuery) {
                    returnUrl = location.href;
                } else {
                    returnUrl = location.href + (hasQuery ? '&' : '?') + 'contractId=' + contractId;
                }
                localStorage.setItem('transitionHref', returnUrl); // 实名页面要返回的地址
                localStorage.setItem('contractsFaceConfig', JSON.stringify({ contractIds: [contractId] })); // 刷脸供应商根据contractId查询
            } catch (err) {
                console.log('storage set error');
            }
            router.push(`/personAuthIntercept`);
            return true;
        }
    } else if (data.userType === 'ENTERPRISE') {
        const { contractId, mustSignWithSameEntity, hasEntAuth, hasEntEnroll, letterSender, entAdminName, entAdminAccount, userId, userAccount, inputEnterpriseName, receiverId } = data;
        // 企业未实名，或者企业已实名但收件方未加入企业
        if ((mustSignWithSameEntity && !hasEntAuth) || (mustSignWithSameEntity && hasEntAuth && !hasEntEnroll)) {
            const params = {
                contractId,
                hasEntAuth,
                hasEntEnroll,
                letterSender,
                entAdminName,
                entAdminAccount,
                userId,
                userAccount,
                inputEnterpriseName,
            };
            try {
                sessionStorage.setItem('entAuthIntercept', JSON.stringify(params)); // 跳转页面时存储参数
                const hasQuery = location.search.length > 0;
                const hasContractIdQuery = hasQuery && location.search.includes('contractId');
                let returnUrl;
                if (hasContractIdQuery) {
                    returnUrl = location.href;
                } else {
                    returnUrl = location.href + (hasQuery ? '&' : '?') + 'contractId=' + contractId;
                }
                localStorage.setItem('transitionHref', returnUrl); // 实名页面要返回的地址
                localStorage.setItem('contractsFaceConfig', JSON.stringify({ contractIds: [contractId] })); // 刷脸供应商根据contractId查询
            } catch (err) {
                console.log('storage set error');
            }
            // 企业未实名
            sessionStorage.setItem('applyPermission', JSON.stringify({ senderEntName: letterSender, receiverEntName: inputEnterpriseName, receiverId }));
            // 企业未实名
            if (!hasEntAuth) {
                router.push(`/sign-flow/sign/un-permission-remind/noAuth?contractId=${contractId}`);
            // 企业已实名用户未加入
            } else if (hasEntAuth && !hasEntEnroll) {
                router.push(`/sign-flow/sign/un-permission-remind/noJoin?contractId=${contractId}`);
            }
            return true;
        }
    }
    return false;
}
