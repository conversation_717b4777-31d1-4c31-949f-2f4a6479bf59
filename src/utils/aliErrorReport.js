import store from 'src/store/store.js';
import { formatDateToString } from 'utils/date.js';
const Base64 = require('js-base64').Base64;

function handleErrorMsg(error) {
    let _msg = '';
    if (error instanceof Error) {
        _msg = error.toString() + error.stack;
    } else {
        _msg = error;
    }

    return _msg;
}
// window error捕获的错误
window.onerror = (errorMessage, scriptURI, lineNumber, col, error) => {
    const msg = handleErrorMsg(error);
    customAliErrorReport({
        errorMsg: errorMessage + 'detailError:' + msg,
        type: 'window ERROR',
        level: 'error',
        deviceinfo: scriptURI + ' line:' + lineNumber + ' column:' + col,
        errorAutoReport: true, // 报错自动上报
    });
    return true; // error不会以error形式打印到控制台
};

// vue捕获的错误不会上报到window.error
Vue.config.errorHandler = function(error, vm, msg) {
    const errorMsg = handleErrorMsg(error);
    customAliErrorReport({
        type: 'Vue errorHandler',
        level: 'error',
        errorMsg: errorMsg + 'detailError:' + msg,
        errorAutoReport: true, // 报错自动上报
    });
};

/**
 * @desc 主动上报接口
 * */
export const customAliErrorReport = (option) => {
    const refer = document.URL;
    // 生产环境采集
    if (process.env.NODE_ENV !== 'production' ||  refer.indexOf('bestsign.cn') === -1) {
        // 主动上报的
        if (!option.errorAutoReport) {
            Vue.$MessageToast.info('日志上报功能只支持生产环境');
        }
        return;
    }
    const userAgent = window.navigator.userAgent.toLowerCase() || '';
    const headerInfo = store.state.commonHeaderInfo;
    const userId = headerInfo.platformUser.userId || '';
    const account = headerInfo.platformUser.account || '';
    const entId =  headerInfo.currentEntId || '';
    const project = 'delta-front-end'; // 项目
    const time = formatDateToString({});
    const data = {
        refer,
        userAgent,
        userId,
        account,
        entId,
        project,
        time,
    };
    const reportData = { ...option, ...data };
    Vue.$http.post(`front/log/encode-record`, { content: Base64.encode(JSON.stringify(reportData)) }).then(() => {
        if (!option.errorAutoReport) { // 主动上报的不toast提示
            Vue.$MessageToast.success('上报成功');
        }
    });
};
