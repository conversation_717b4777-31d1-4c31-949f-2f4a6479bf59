// 签约须知、审批须知相关的本地存储
import LocalStorage from 'src/common/plugins/localStorage';

// 检查传入合同id的签约须知是否已读
export function checkContractIdViewed(contractId) {
    const letterViewedList = LocalStorage.get('letterViewedList');
    const letterViewed = letterViewedList ? letterViewedList.includes(contractId) : false;
    return letterViewed;
}

// 将新的已读合同id存入storage
export function stashViewContractId(contractId) {
    const letterViewedList = LocalStorage.get('letterViewedList') || '';
    if (!contractId || letterViewedList.includes(contractId)) {
        return;
    }
    let newList = [];

    if (letterViewedList) {
        try {
            newList = letterViewedList.split(',');
        } catch (error) {
            console.log(error);
        }
    }

    newList.push(contractId);
    LocalStorage.set('letterViewedList', newList);
}

// 从storage中删除已读合同id
// export function popViewContractId(contractId) {
//     if (!contractId) {
//         return;
//     }
//     const letterViewedList = LocalStorage.get('letterViewedList');

//     if (letterViewedList && letterViewedList.includes(contractId)) {
//         let arr = [];
//         try {
//             arr = letterViewedList.split(',');
//         } catch (error) {
//             console.log(error);
//         }

//         const idIndex = arr.findIndex(function(value) {
//             return value === contractId;
//         });
//         arr.splice(idIndex, 1);

//         LocalStorage.set('letterViewedList', arr);
//     }
// }
