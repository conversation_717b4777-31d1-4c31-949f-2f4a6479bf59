/*
    使用说明
    -- main.js
    import sensors from 'pub-utils/sensors.js';
    sensors.usePageLeavePlugin(); // 注意引用顺序：先引用插件，后调用 init
    sensors.init();
    sensors.registerCommonProperty();

    建议绑定到Vue实例上：
    Vue.prototype.$sensors = sensors;
    Vue.$sensors = sensors;

    -- 各业务代码中需要埋点的地方
    this.$sensors.track({
        eventName: 'test',
        eventProperty: {
            'test_property': 'testPropertyValue',
        },
    });
    const anonymousID = await this.$sensors.getAnonymousID();
    console.log(`AnonID: ${anonymousID}`);

    -- 控制台埋点Log默认关闭,需要时在浏览器控制台输入命令：
    开启: sensorsDataAnalytic201505.enableLocalLog()
    关闭: sensorsDataAnalytic201505.disableLocalLog()

*/
// import sensors from 'sa-sdk-javascript';
// import pageleave from 'sa-sdk-javascript/dist/web/plugin/pageleave/index.es6.js';
import Vue from 'vue';
const LANG_MAP = {
    'zh': '中文',
    'en': '英文',
    'ja': '日文',
};

const sensors = {
    init: () => {},
    registerPage: () => {},
    track: () => {},
    setProfile: () => {},
    login: () => {},
    identify: () => {},
    quick: () => {},
};
const ssqSensors = {
    // 按顺序引入插件 ！先 import 主 SDK，后 import plugin。先调用 use，后调用 init。
    // 版本必须一致 ！如果单独升级了插件，必须同时单独更新主 SDK。建议 dist 下的目录一起更新，这样就不会出现版本不一致。
    usePageLeavePlugin() {
        // sensors.use(pageleave, {
        //     custom_props: {}, // 自定义属性
        //     heartbeat_interval_time: 5, // 心跳记录刷新时间 单位：秒 ，默认：5
        //     max_duration: 5 * 24 * 60 * 60, // 最大页面浏览时长：单位：秒 默认：5天
        //     isCollectUrl: function() {
        //         return true; // 采集
        //     },
        // });
    },
    // 是否开启全埋点
    openAutoTrack: true,
    // 初始化
    init({ options = {} } = {}) {
        sensors.init({
            server_url: `https://sensors.bestsign.cn/sa?project=${window.location.host.includes('bestsign.cn') ? 'production' : 'default'}`,
            is_track_single_page: true, // 单页面配置，默认开启，若页面中有锚点设计，需要将该配置删除，否则触发锚点会多触发 $pageview 事件
            use_client_time: true,
            send_type: 'beacon',
            show_log: false,
            heatmap: {
                clickmap: ssqSensors.openAutoTrack ? 'default' : 'not_collect', // 是否开启点击图，default 表示开启，自动采集 $WebClick 事件，'not_collect' 表示关闭。默认只有点击 a input button textarea 四种元素时，才会触发 $WebClick 元素点击事件。
                scroll_notice_map: ssqSensors.openAutoTrack ? 'default' : 'not_collect', // 是否开启触达图， 'default' 表示开启, 自动采集 $WebStay 事件，'not_collect' 表示关闭。
            },
            ...options,
        });
    },
    // 注册公共属性
    registerCommonProperty() {
        sensors.registerPage({
            pub_platform_type: '上上签云平台',
            pub_platform_version: '4.0',
            pub_source_platform: () => {
                return Vue.$cookie.get('sourcePlatform') || '云平台';
            },
            pub_is_login: () => {
                return !!Vue.$cookie.get('access_token');
            },
            pub_account_type: () => {
                return JSON.parse(Vue.$cookie.get('sensorsUserInfo'))?.currentAccountType || '';
            },
            pub_company_name: () => {
                return JSON.parse(Vue.$cookie.get('sensorsUserInfo'))?.currentEntName || '';
            },
            pub_company_id: () => {
                return JSON.parse(Vue.$cookie.get('sensorsUserInfo'))?.currentEntId || '';
            },
            pub_role_name: () => {
                return JSON.parse(Vue.$cookie.get('sensorsUserInfo'))?.currentRoleName || [];
            },
            pub_language: () => {
                return LANG_MAP[Vue.$cookie.get('language') || 'zh'];
            },
        });
    },
    track({ eventName = '', eventProperty = {} } = {}) {
        sensors.track(eventName, { ...eventProperty });
    },
    // 设置用户属性
    setUserProfile(options = {}) {
        sensors.setProfile({ ...options });
    },
    // 用户登录
    sensorsLogin(id = '') {
        sensors.login(id);
    },
    // 修改匿名 ID
    setIdentifyId(id = '', ifSaveCookie = true) {
        sensors.identify(id, ifSaveCookie); // 替换神策默认分配的匿名 ID, 并把这个id保存在cookie中
    },
    // 获取匿名 ID
    getAnonymousID() {
        return new Promise((resolve) => {
            sensors.quick('isReady', function() {
                const anonymousID = sensors.quick('getAnonymousID');
                resolve(anonymousID);
            });
        });
    },
};
ssqSensors.usePageLeavePlugin(); // 注意引用顺序：先引用插件，后调用 init
ssqSensors.init();
ssqSensors.registerCommonProperty();
ssqSensors.openAutoTrack && sensors.quick('autoTrack'); // 用于采集 $pageview 事件。设置之后，SDK 就会自动收集页面浏览事件，以及设置初始来源。
// ssqSensors.track({
//     eventName: 'test',
//     eventProperty: {
//         'test_property': '',
//     },
// });
Vue.prototype.$sensors = ssqSensors;
Vue.$sensors = ssqSensors;

export default ssqSensors;
