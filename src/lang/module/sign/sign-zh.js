export default {
    ssoLoginConfig: {
        notBelongToEntTip: '需要重新登录上上签平台才能发送合同（或管理模板）',
        operationStep: {
            one: '第一步 点击继续后，返回登录页面',
            two: '第二步 输入密码，进入上上签平台',
            three: '第三步 发送合同（或管理模板）',
        },
        continue: '继续',
        cancel: '取消',
        tip: '提示',
    },
    sign: {
        sealLabelsTip: '您需要在合同上盖{sealLabelslen}个章。{personStr}将为您盖{otherSealLen}个章，剩余{mySealLen}个章由您亲自盖章。所需使用的章已在页面上展示。请确认是否继续。',
        continue: '继续',
        nonMainlandCARenewalTip: '申请续期后，系统会自动驳回原实名结果，请尽快完成认证。',
        reselect: '重选',
        approvalFeatures: {
            dialogTitle: '新功能介绍',
            understand: '我知道了',
            feature1: '划句批注',
            feature2: '字段高亮',
            tip1: '点击按钮将合同中的所有“模板内容字段”高亮，方便抓取关键信息。',
            tip2: '点击左下角提示按钮，开启模板内容字段高亮。',
            tip3: '通过高亮，快速定位合同的内容填写字段，高效完成审批。',
            tip4: '按住鼠标圈选一个段落后松开鼠标，点击批注按钮可添加批注文本，完成后点击修改或删除。批注的内容可在合同详情页-公司内部操作日志中查看。',
            tip5: '第一步：选中所需批注文本字段，添加批注；',
            tip6: '第二步：点击编辑或删除批注。',
            annotate: '批注',
            delete: '删除',
            edit: '修改',
            operateTitle: '添加审批批注',
            placeholder: '不超过255个字',
        },
        needRemark: '您还需要填写备注',
        notNeedRemark: '您不需要填写备注',
        switchToReceiver: '已为您切换至{receiver}',
        notAddEntTip: '当前用户不是该企业成员，请联系主管理员加入企业。',
        contractPartiesYouChoose: '您可以选择的签约主体:',
        contractPartyFilled: '发件人填写的签约主体为:',
        certifyOtherCompanies: '认证其他企业',
        youCanAlso: '您也可以：',
        needVerification: '您需要实名认证后才能签署',
        prompt: '提示',
        submit: '确定',
        cancel: '取消',
        sign: '立即签约',
        addSeal: '请使用电脑登录上上签官网添加印章',
        noSealAvailable: '对不起，您目前没有可使用的印章，请联系企业主管理员添加印章并授权。',
        memberNoSealAvailable: '当前无可用印章，请联系管理员配置后再签署。或者线下联系主管理员配置。',
        noticeAdminFoSeal: '发通知给主管理员',
        requestSomeone: '请求他人认证',
        requestOthersToContinue: '通知主管理员补充实名认证',
        requestOthersToContinueSucceed: '已向管理员发送通知',
        requestSomeoneList: '请求以下人员完成实名认证：',
        electronicSeal: '电子公章',
        changeTheSeal: '不想使用该印章？实名认证后可更换印章',
        goToVerify: '去实名认证',
        noSealToChoose: '没有可切换的印章，如果需要管理印章，请先进行实名认证',
        goVerify: '去认证',
        goToVerifyEnt: '去认证企业',
        digitalCertificateTip: '上上签正在调用您的数字证书',
        signDes: '您在安全签约环境中，请放心签署！',
        signAgain: '继续签署',
        send: '发送',
        person: '个人',
        ent: '企业',
        entName: '企业名称',
        account: '账号',
        accountPH: '手机或邮箱',
        approved: '审批',
        signVerification: '签署',
        cannotReview: '无法查看合同',
        connectFail: '发件方企业采用了合同私有存储的方式，但当前网络无法连接至合同存储服务器。',
        connectFailTip: '您可以尝试以下方法解决问题：',
        connectFailTip1: '1、刷新页面。',
        connectFailTip2: '2、耐心等待并稍后重试。有可能是因为发件方企业部署的服务器出现了异常，企业IT技术人员重启服务器需要时间。',
        connectFailTip3: '3、发件方企业是否向你强调过，需要使用特定的wifi网络才能访问？如果有过这方面的说明，你需要切换手机或电脑设备连接的网络。',
        personalMaterials: '发件人要求您补充更多认证材料',
        noSupportface: '合同发起方要求您刷脸签署，非大陆人士暂不支持刷脸签署，请联系发起方修改签署要求',
        lackEntName: '请填写企业名称',
        errAccount: '请填写正确的邮箱或手机号',
        noticeAdmin: '申请加入',
        signDone: '签署完成',
        signDoneTip: '您已签署该合同',
        approveDone: '审批完成',
        approveDoneTip: '您已审批该合同',
        completeSign: '请先点击“盖章处”或“签字处”完成签署',
        fillFirst: '请先在输入框中填写合同内容',
        stillSignTip: '在您签署此{alias}后，仍有其他签署方可能更改{alias}内容，是否继续签署？',
        signHighLightTip: '可被新增或修改的{alias}内容共{count}处',
        riskDetails: '风险详情',
        noviewDifference: '由于发起方开启了签署方可以填写本{alias}固定字段的功能，本{alias}其他签署方仍可能更改发起方指定的合同内容，上上签不对本{alias}的签署前版本与生效版本之间的内容差异进行审核，当您签署完本{alias}后，视为您同意其它签署方对{alias}内容中固定字段内容的增加或修改，并认可本{alias}各签署方均签署完成后的生效版本。\n' +
            '如果您不同意在您签署后其他签署方仍可以变更本{alias}字段，您可以拒绝本次签署，并与发件方协商（即要求发起方关闭"签署人填写字段"功能，规避您的相应风险）。',
        highLightTip: '这些有风险的内容将会被呈现为“高亮”效果，请仔细核对。重新刷新页面可取消高亮效果。',
        commonTip: '提示',
        understand: '我知道了',
        view: '查看',
        start: '开始',
        nextStep: '下一步',
        help: '帮助',
        faceFailed: '非常抱歉，您的人脸比对失败',
        faceFailedtips: '提示',
        dualFailed: '非常抱歉，双录校验不通过，请核实您的信息后重试',
        verifyTry: '请核实身份信息后重试',
        faceLimit: '今天的人脸比对次数已达到上限',
        upSignReq: '今天的人脸比对次数已达到上限，请明天重试或联系合同发起者修改签署要求',
        reqFace: '发件人要求你进行刷脸校验',
        signAfterFace: '刷脸通过后即可完成合同签署',
        qrcodeInvalid: '二维码信息已过期，请刷新',
        faceFirstExceed: '刷脸未通过，接下来将使用验证码校验',
        date: '日期',
        chooseSeal: '选择印章',
        seal: '印章',
        signature: '签名',
        handwrite: '手写',
        mysign: '我的签名',
        approvePlace: '审批留言，可不填',
        approvePlace_1: '审批留言',
        approvePlace_2: '选填，不超过255字。',
        approveAgree: '审批结果：同意',
        approveReject: '审批结果：驳回',
        signBy: '由',
        signByEnd: '盖章',
        sealBy: '由',
        sealByEnd: '签名',
        coverBy: '需盖',
        applicant: '申请人',
        continueVeri: '继续认证',
        registerAndReal: '请注册并实名',
        goToResiter: '请注册并认证',
        sureToUse: '确定使用',
        toSign: '签约吗?',
        pleaseComplete: '请先完成',
        confirmSign: '再确认签署',
        admin: '管理员',
        contratAdmin: '请联系管理员将您的账号',
        addToEnt: '添加为企业成员',
        alreadyExists: '在上上签已存在',
        sendMsg: '上上签将以短信形式给管理员发以下内容：',
        applyJoin: '申请加入',
        title: '标题',
        viewImg: '查看图片',
        priLetter: '私信',
        priLetterFromSomeone: '来自{name}的私信',
        readLetter: '我知道了',
        approve: '同意',
        disapprove: '驳回',
        refuseSign: '拒签',
        paperSign: '改用纸质签署',
        refuseTip: '请选择拒绝理由',
        refuseReason: '填写拒签理由有助于对方了解你的问题，加快合同流程',
        reasonWriteTip: '请填写拒签理由',
        refuseReasonOther: '更多拒签理由（可不填） | 更多拒签理由（必填）',
        refuseConfirm: '拒签',
        refuseConfirmTip: '您以"{reason}"理由拒绝签署，是否继续？确定后将不可再次签署。',
        waitAndThink: '我再想想',
        signValidationTitle: '签署校验',
        email: '邮箱',
        phoneNumber: '手机号',
        password: '密码',
        verificationCode: '验证码',
        mailVerificationCode: '验证码',
        forgetPsw: '忘记密码',
        if: '，是否',
        forgetPassword: '忘记密码',
        rejectionVer: '拒签校验',
        msgTip: '一直收不到信息？试试',
        voiceVerCode: '语音验证码',
        SMSVerCode: '短信验证码',
        or: '或',
        emailVerCode: '邮箱验证码',
        SentSuccessfully: '发送成功！',
        intervalTip: '发送时间间隔过短',
        signPsw: '签约密码',
        useSignPsw: '使用签约密码校验',
        setSignPsw: '设置签约密码校验',
        useVerCode: '使用验证码校验',
        inputVerifyCodeTip: '请输入验证码',
        inputSignPwdTip: '请输入签约密码',
        signConfirmTip: {
            1: '您是否确定要签署此{contract}？',
            2: '点击确定按钮将立即签署此{contract}',
            confirm: '确认签署',
        },
        signSuc: '签署成功',
        refuseSuc: '拒签成功',
        approveSuc: '审批成功',
        hdFile: '查看高清文件',
        otherOperations: '其他操作',
        reviewDetails: '审批详情',
        close: '关 闭',
        submitter: '提交人',
        signatory: '签署人',
        reviewSchedule: '审批进度',
        signByPc: '由{name}签名',
        signPageDescription: '第{index}页, 共{total}页',
        sealBySomeone: '由{name}盖章',
        signDate: '签署日期',
        download: '下载',
        signPage: '页数：{page}页',
        signNow: '立即签署',
        sender: '发件方',
        signer: '签约方',
        startSignTime: '发起签约时间',
        signDeadLine: '签约截止时间',
        authGuide: {
            goToHome: '回到首页',
            tip_1: '认证完成后，可查看并签署合同。',
            tip_2: '请使用身份 | 进行认证。',
            tip_3: '发来合同',
            tip_4: '请联系合同发起者 | 更改收件人。',
            tip_5: '您认证的 | 无法查看合同',
            new_tip_1: '基于发件方的合规要求，您需要完成以下步骤：',
            new_tip_2: '基于发件方的合规要求，您需要以：',
            new_tip_3: '完成以下步骤。',
            new_tip_4: '如果您已有印章权限，会为您自动跳过第2步',
            entUserName: '姓名：',
            idNumberForVerify: '身份证号：',
            realNameAuth: '实名认证',
            applySeal: '申请印章',
            signContract: '签署合同',
        },
        switch: '切换',
        rejectReasonList: {
            // authReason: '不想/不会做实名认证',
            signOperateReason: '对签署操作/校验操作有疑问，需要进一步沟通',
            termReason: '对合同条款/内容有疑议，需要进一步沟通',
            explainReason: '对合同内容不知情，请提前告知',
            otherReason: '其他（请填写理由）',
        },
        selectSignature: '选择签名',
        selectSigner: '选择签名人',
        pleaseScanToSign: '请用支付宝或微信扫一扫签署',
        pleaseScanAliPay: '请使用支付宝app扫描二维码签署',
        pleaseScanWechat: '请使用微信app扫描二维码签署',
        requiredFaceSign: '合同发件人要求您刷脸签署',
        requiredDualSign: '合同发件人要求你使用双录校验',
        verCodeVerify: '验证码校验',
        applyToSign: '申请签署合同',
        autoRemindAfterApproval: '*审批通过后，自动发送签署提醒给签署人',
        cannotSignBeforeApproval: '审批未完成，暂不能签署！',
        finishSignatureBeforeSign: '请先完成盖章/签名再确认签署',
        uploadFileOnRightSite: '您还有附件未上传，请先在右边栏上传附件',
        cannotApplySealNeedPay: '该份合同需要您支付，不支持申请他人盖章',
        cannotOtherSealReason: '合同开启了刷脸签署校验，不支持他人盖章',
        unlimitedNotice: '该合同计费不限量使用',
        units: '{num}份',
        contractToPrivate: '对私合同',
        contractToPublic: '对公合同',
        paySum: '共{sum}需要您支付',
        payTotal: '共计{total}元。',
        fundsLack: '您的可用合同份数不足，为确保合同顺利签署，建议立即充值。',
        contactToRecharge: '请联系主管理员充值。',
        deductPublicNotice: '对私合同可用份数不足时会扣除对公合同。',
        needSignerPay: '合同发送方设置了相对方到付，并指定由您来支付合同费用。',
        recharge: '充值',
        toSubmit: '提交',
        appliedSeal: '用印申请已提交',
        noSeal: '无印章',
        noSwitchSealNeedDistribute: '没有可切换的印章，请联系企业主管理员添加印章并授权',
        viewApproveProcess: '查看审批流程',
        approveProcess: '审批流程',
        noApproveContent: '未提交审批资料',
        knew: '知道了',
        noSwitchSealNeedAppend: '没有可切换的印章，请联系管理员添加印章',
        hadAutoSet: '已在另外{num}处自动',
        setThatSignature: '放置该签名',
        setThatSeal: '放置该印章',
        applyThatSeal: '申请该印章',
        hasSetTip: '已在另外{index}处自动放置',
        hasSetSealTip: '已在另外{index}处自动放置该印章',
        hasSetSignatureTip: '已在另外{index}处自动放置该签名',
        hasApplyForSealTip: '已在另外{index}处自动申请该印章',
        savedOnLeftSite: '已保存到左侧签名栏',
        ridingSealMinLimit: '文档页数仅一页，无法加盖骑缝章',
        ridingSealMaxLimit: '超过146页，不支持加盖骑缝章',
        ridingSealMinOrMaxLimit: '文档页数仅一页或者超过146页，无法加盖骑缝章',
        noSealForRiding: '您没有可使用的印章，无法加盖骑缝章',
        noSwitchSealNeedAppendBySelf: '没有可切换的印章，您可以前往企业控制台添加印章',
        gotoAppendSeal: '去添加印章',
        approvalFlowSuccessfulSet: '审批流设置成功',
        mandate: '同意授权',
        loginToAppendSeal: '您也可以用电脑登录上上签，去企业控制台添加印章',
        signIdentityAs: '当前正在以{person}的名义签署合同',
        enterNextContract: '进入下一份合同',
        fileList: '文件列表',
        addSignerFile: '添加附属资料',
        signatureFinish: '已全部盖章/签名',
        dragSignatureTip: '请将以下签章/日期拖放到文件中，可多次拖放',
        noticeToManager: '给管理员发通知',
        gotoAuthPerson: '去认证个人',
        senderRequire: '发件方要求您',
        senderRequireUseFollowIdentity: '发件方要求您满足以下身份之一',
        suggestToAuth: '您还未实名认证，建议您实名认证后签署',
        contactEntAdmin: '请联系企业主管理员',
        setYourAccount: '将您的账号',
        authInfoUnMatchNeedResend: '进行签署合同。这和您实名的身份信息不符。请您自行联系发起方确认身份信息，并要求再次发起合同',
        noEntNameNeedResend: '未指定签约企业名称，该合同无法被签署，请联系发起方重新发送合同',
        pleaseUse: '请使用',
        me: '我',
        myself: '本人，',
        reAuthBtnTip: '我是当前手机号的实际使用者，',
        reAuthBtnContent: '重新实名后，该账号的原实名会被驳回，请确认。',
        descNoSame1: ' 的身份签署合同',
        descNoSame2: '这与您当前登录的账号已完成的实名信息不符。',
        authInfoNoSame: '的身份签署合同。这与您当前登录的账号已完成的实名信息不符。',
        authInfoNoSame2: '的身份签署合同。这与您当前登录账号的基础身份信息不符。',
        goHome: '返回合同列表页>>',
        authInfo: '检测到您当前账号的实名身份为 ',
        authInfo2: '检测到您当前账号的基础身份信息为 ',
        in: '于',
        finishAuth: '完成实名，用于合规签署合同',
        ask: '是否继续以当前账号签署？',
        reAuthBtnText: '是的，我要用本账号重新实名签署',
        changePhoneText: '不是，联系发件方更改签署手机号',
        changePhoneTip1: '应发件方要求，请联系',
        changePhoneTip2: '，更换签署信息(手机号/姓名)，并指定由您签署。',
        confirmOk: '确认',
        goOnAuth: {
            0: '进行认证，',
            1: '请进行实名认证，',
            2: '进行实名认证，',
        },
        signContractAfterAuth: {
            0: '认证完成后，可签署合同。',
            1: '完成认证后即可签署合同。',
        },
        useIdentity: '以{name}的身份',
        inTheName: '以',
        of: '的',
        identity: '身份',
        nameIs: '姓名为',
        IDNumIs: '身份证号为',
        provideMoreAuthData: '补充更多认证材料',
        leadToAuthBeforeSign: '继续认证后即可签署合同',
        groupProxyAuthNeedMore: '您目前认证状态为集团代认证，若需单独签署合同请补充实名认证材料',
        contactSender: '如有疑问请联系发件方。',
        note: '注:',
        identityInfo: '身份信息',
        signNeedCoincidenceInfo: '完全一致才能签署合同。',
        needAuthPermissionContactAdmin: '您暂时没有实名认证权限，请联系管理员',
        iHadReadContract: '已阅读，本人已知晓{alias}内容',
        scrollToBottomTip: '需滑动到最后一页',
        getVerCodeFirst: '请先获取验证码',
        appScanVerify: '上上签APP扫码校验',
        downloadBSApp: '下载上上签APP',
        scanned: '扫码成功',
        confirmInBSApp: '请在上上签APP中确认签署',
        qrCodeExpired: '二维码已失效，请刷新重试',
        appKey: 'APP安全校验',
        goToScan: '去扫码',
        setNotificationInUserCenter: '请先到用户中心设置通知方式',
        doNotWantUseVerCode: '不想用验证码',
        try: '试试',
        retry: '重试',
        goToFaceVerify: '去刷脸',
        faceExceedTimes: '当日刷脸次数已达上线',
        returnBack: '返回',
        switchTo: '切换至',
        youCanChooseIdentityBlow: '您可以选择以下签约主体',
        needDrawSignatureFirst: '您还没有签名，请先添加手绘签名',
        lacksSealNeedAppend: '您还未添加任何印章，请先去添加印章。',
        manageSeal: '管理印章',
        needDistributeSealToSelf: '您暂无可用印章，请先将自己设为印章持有人',
        chooseSealAfterAuth: '不想使用上面印章？ 实名认证后可更换印章',
        appendDrawSignature: '添加手绘签名',
        senderUnFill: '（发件人未填写）',
        declare: '说明',
        fileLessThan: '请上传小于{num}M的文件',
        fileNeedUploadImg: '上传时请使用支持的附件格式',
        serverError: '服务器开了点小差，请稍后再试',
        oldFormatTip: '支持jpg、png、jpeg、pdf、txt、zip、xml格式，单份文件大小不超过10M',
        fileLimitFormatAndSize: '单个资料图片数量不超过10张。',
        fileFormatImage: '支持jpg、png、jpeg格式，单张图片大小不超过20M，允许上传10张',
        fileFormatFile: '支持pdf、excel、word、txt、zip、xml格式，单份文件大小不超过10M',
        signNeedKnow: '签约须知',
        signNeedKnowFrom: '来自{sender}的签约须知',
        approvalInfo: '审批须知',
        approveNeedKnowFrom: '来自{sender}-{sendEmployeeName}提交的审批资料（{approvalType}）',
        approveBeforeSend: '合同发送前审批',
        approveBeforeSign: '合同签署前审批',
        approveOperator: '审批人',
        approvalOpinion: '审批留言',
        employeeDefault: '员工',
        setLabel: '设置标签',
        addRidingSeal: '添加骑缝章',
        delRidingSeal: '删除骑缝章',
        file: '附件文件',
        compressedFile: '压缩文件',
        attachmentContent: '附件内容',
        pleaseClickView: '（请点击下载查看）',
        downloadFile: '下载源文件',
        noLabelPleaseAppend: '还没有标签，请前往企业控制台添加',
        archiveTo: '归档到',
        hadArchivedToFolder: '已将合同成功移动到{who}的{folderName}文件夹中',
        pleaseScanToHandleWrite: '请用微信或者手机浏览器扫码，在移动设备上手写签名',
        save: '保存',
        remind: '提醒',
        riskTip: '风险提醒',
        chooseApplyPerson: '选择盖章执行人',
        chooseAdminSign: '选择印章管理员',
        useSealByOther: '他人盖章',
        getSeal: '获取印章',
        nowApplySealList: '您正在请求以下印章',
        nowAdminSealList: '你正在申请获得以下印章',
        chooseApplyPersonToDeal: '请选择盖章执行人，合同将由所选人员来处理（你仍能继续查看、跟进此合同）',
        chooseTransferPerson: '转交他人签署',
        chooseApplyPersonToMandate: '请选择印章管理员，所选人收到通知、审核通过后，您将获得该印章的使用权限，届时可以使用该印章来盖章并签署合同',
        contactGroupAdminToDistributeSeal: '请联系集团管理员分配印章',
        sealApplySentPleaseWait: '印章分配申请已发送，请等待审核通过。或者您可以选择其他盖章方式',
        successfulSent: '发送成功',
        authTip: {
            t2: ['注：', '完全一致才能签署合同。', '企业名称', '身份信息', '完全一致才能查看和签署合同。'],
            t3: '{x}要求您{text}进行实名认证。',
            tCommon1: '以{entName}的身份',
            tCommon2_1: '以姓名为{name}，身份证号为{idCard}',
            tCommon2_2: '以姓名为{name}',
            tCommon2_3: '以身份证号为{idCard}',
            viewAndSign1: '完成认证后即可查看和签署合同。',
            viewAndSignConflict: '{x}要求您{text}进行查看和签署合同。这和您实名的身份信息不符。请您自行联系发起方确认身份信息，并要求再次发起合同。',
        },
        needSomeoneToSignature: '由{x}盖{y}',
        needToSet: '需盖',
        approver: '申请人：',
        clickToSignature: '点击此处签名',
        transferToOtherToSign: '转给其他人签',
        signatureBy: '由{x}签名',
        tipRightNumber: '请输入正确的数字',
        tipRightIdCard: '请正确填写18位大陆居民身份证',
        tipRightPhoneNumber: '请正确填写11位手机号码',
        tip: '提示',
        tipRequired: '必填值不可为空',
        confirm: '确定',
        viewContractDetail: '查看合同详情',
        required: '必填',
        optional: '选填',
        decimalLimit: '限小数点后{x}位',
        intLimit: '要求整数',
        invalidContract: '签署此合同视为您同意将以下合同作废：',
        No: '编号',
        chooseFrom2: '发件方设置了二选一盖章，请选择一处盖章',
        crossPlatformCofirm: {
            message: '您好，当前合同需要跨平台签署，签署的文件需要传输到境外，您是否同意？',
            title: '数据授权',
            confirmButtonText: '同意授权',
            cancelButtonText: '取消',
        },
        sealScope: '印章使用范围',
        currentContract: '当前合同',
        allContract: '所有合同',
        docView: '合同预览',
        fixTextDisplay: '纠正页面乱码',
        allPage: '共{num}页',
        notJoinTip: '请联系管理员添加为企业成员后再签署',
    },
    signJa: {
        beforeSignTip1: '根据发件方要求, 请以此企业名义进行签署：',
        beforeSignTip2: '发件方指定了 {signer} 完成签署。如确认信息正确, 可直接签署。',
        beforeSignTip3: '如信息有误, 请与发件方联系, 更换指定的签署人信息。',
        beforeSignTip4: '检测到该账号已注册的姓名为 {currentUser}, 与当前发件方要求的 {signer} 不一致, 是否确认更换为 {signer} ',
        beforeSignTip5: '检测到当前账号绑定的姓名为：{currentUser}, 与甲方指定要求 {signer} 签署, 不一致',
        beforeSignTip6: '请根据实际情况, 确认修改为甲方指定的 {signer} 进行签署',
        beforeSignTip7: '或者与甲方进行沟通，更换指定的签署人',
        entNamePlaceholder: '请输入企业名称',
        corporateNumberPlaceholder: '请输入法人番号',
        corporateNumber: '法人番号',
        singerNamePlaceholder: '请输入签署人姓名',
        singerName: '签署人姓名',
        businessPic: '印鉴证明书',
        waitApprove: '审核中。如果您需要了解审核进度可邮件联系我们：<EMAIL>',
        itsMe: '是我本人',
        wrongInformation: '信息有误',
        confirmChange: '确认更换',
        communicateSender1: '不更换, 与甲方沟通',
        communicateSender2: '取消, 去与发件方沟通',
        createSeal: {
            title: '输入姓名',
            tip: '请输入您的姓名（空格可以进行换行）',
            emptyErr: '请输入姓名',
        },
        areaRegister: '企业注册地',
        jp: '日本',
        cn: '中国大陆',
        are: '阿拉伯联合酋长国',
        other: '其他',
        plsSelect: '请选择',
        tip1: '注册地为中国大陆地区的企业，需在 ent.bestsign.cn 中完成实名注册。在与中国大陆地区以外的企业签署合同时，可利用“跨境签”功能，在确保用户数据安全、不外泄的前提下，高效完成合同互签。',
        tip2: '若您的企业已在上上签中国大陆版完成实名认证，可直接登录 ent.bestsign.cn，便捷使用相关服务。需注意的是，您在上上签海外版所产生的数据，与中国大陆版是完全独立隔离的。',
        tip3: '请提供您从当地商业监管机构获取的证件编号',
        tip4: '请按照以下步骤操作',
        tip5: '1、请您与您的专属客户经理联系，引导您完成企业实名。',
        tip6: '点击“充值管理”。',
        tip7: '2、请上传贵司与上上签的商务合同截图或与专属客户经理的业务往来邮件。',
        tip8: '至少购买一份合同，并保存购买记录的截图。',
        tip9: '3、非日本、中国大陆地区的企业才可以使用此方式。',
        tip10: '4、提交后上上签在三个工作日内进行审核。',
        tip11: '重要提示',
        tip12: '购买人必须为企业用户。',
        tip13: '付款账户中的企业全称必须与您已填写的“企业名称”完全一致。',
        tip14: '非日本、中国大陆地区以外的企业才可以使用此方式。',
        comNum: '企业证件号',
        buyRecord: '证明材料',
        selectArea: '请选择企业注册地',
        uaeTip1: '注册地为阿联酋的企业，需在 uae.bestsign.com中完成实名注册。在与阿联酋以外的企业签署合同时，可利用“跨境签”功能，在确保用户数据安全、不外泄的前提下，高效完成合同互签。',
        uaeTip2: '若您的企业已在上上签阿联酋版完成实名认证，可直接登录 uae.bestsign.com，便捷使用相关服务。需注意的是，您在上上签海外版所产生的数据，与阿联酋版是完全独立隔离的。',
        uaeTip3: '注册地为阿联酋和中国大陆以外的企业，需在 ent.bestsign.com中完成实名注册。在与阿联酋的企业签署合同时，可利用“跨境签”功能，在确保用户数据安全、不外泄的前提下，高效完成合同互签。',
    },
    signPC: {
        commonSign: '确认签署',
        contractVerification: '签约校验',
        VerCodeVerify: '验证码校验',
        QrCodeVerify: '二维码校验',
        verifyTip: '上上签正在调用您的安全数字证书，您正在安全签约环境中，请放心签署！',
        verifyAllTip: '上上签正在调用企业数字证书和您的个人数字证书，您正在安全签约环境中，请放心签署！',
        selectSeal: '选择印章',
        adminGuideTip: '因为您是企业主管理员，可以直接将企业印章分配给自己',
        toAddSealWithConsole: '电子公章待启用，添加其他印章可前往控制台操作。',
        use: '使用',
        toAddSeal: '去添加印章',
        mySeal: '我的印章',
        operationCompleted: '操作完成',
        FDASign: {
            date: '签署时间',
            signerAdd: '新增',
            signerEdit: '修改',
            editTip: '提示：中文姓名请输入拼音，如San Zhang（张三）',
            inputNameTip: '请输入您的姓名',
            inputName: '请输入英文或中文拼音',
            signerNameFillTip: '您还需要填写签字姓名',
            plsInput: '请输入',
            plsSelect: '请选择',
            customInput: '自行输入',
        },
        signPlaceBySigner: {
            signGuide: '签署指导',
            howDragSeal: '如何拖章',
            howDragSignature: '如何拖签名',
            iKnow: '我知道了',
            step: {
                one: '第一步：阅读合同',
                two1: '第二步：点击“拖章”',
                two2: '第二步：点击“拖签名”',
                three: '第三步：点击“签署”按钮',
            },
            dragSeal: '拖章',
            continueDragSeal: '继续拖章',
            dragSignature: '拖签名',
            continueDragSignature: '继续拖签名',
            dragPlace: '按住此处拖动',
            notRemind: '不再提醒',
            signTip: {
                one: '第一步：通过点击“开始”，定位到需要签名/盖章处。',
                two: '第二步：通过点击“签名处/盖章处”，根据要求完成签名/盖章。',
            },
            finishSignatureBeforeSign: '请先完成拖签名/拖章再确认签署',
        },
        continueOperation: {
            success: '操作成功',
            exitApproval: '退出审批',
            continueApproval: '继续审批',
            next: '下一份：',
            none: '没有了',
            tip: '提示',
            approvalProcess: '共需{totalNum}人审批，当前已有{passNum}人审批通过',
            receiver: '接收方：',
        },
    },
    signTip: {
        contractDetail: '合同详情',
        downloadBtn: '下载APP',
        tips: '提示',
        submit: '确定',
        SigningCompleted: '签署成功',
        submitCompleted: '等待他人处理',
        noTurnSign: '尚未轮到签署或没有签署权限或登录身份已过期',
        noRightSign: '合同正在签署中，当前用户不允许签署操作',
        noNeedSign: '内部决议合同，已无需签署',
        ApprovalCompleted: '审批成功',
        contractRevoked: '该{alias}已被撤销',
        contractRefused: '该{alias}已被拒签',
        linkExpired: '该链接已失效',
        contractClosed: '该{alias}已截止签约',
        approvalReject: '该{alias}审批已被驳回',
        approving: '{alias}正在审批中',
        viewContract: '查看{alias}详情',
        viewContractList: '查看合同列表',
        needMeSign: '（{num}份待签署）',
        downloadContract: '下载合同',
        sign: '签署',
        signed: '签署',
        approved: '审批',
        approval: '审批',
        person: '人',
        personHas: '已',
        personHave: '已',
        personHasnot: '未',
        personsHavenot: '未',
        headsTaskDone: '{num}{has}{done}',
        headsTaskNotDone: '{num}{not}{done}',
        taskStatusBetween: '，',
        cannotReview: '无法查看合同',
        cannotDownload: '该合同不支持手机下载。因为合同由发件方私有存储，上上签无法取到合同。',
        privateStorage: '发件方企业采用了合同私有存储的方式，但当前网络无法连接至合同存储服务器',
        beenDeleted: '您的账号已被企业管理员删除',
        unActive: '无法继续激活账',
        back: '返回',
        contratStatusDes: '{key}状态：',
        contractConditionDes: '{key}情况：',
        contractIng: '{alias}{key}中',
        contractComplete: '{alias}{key}完成',
        dataProduct: {
            tip1: '{entName}致各位优质经销商/供应商企业负责人：',
            tip2: '为答谢您为{entName}的稳定发展作出的贡献，特此联合{bankName}推出供应链金融服务，助力您的企业加速发展！',
            btnText: '去向老板分享这个喜讯',
        },
        signOnGoing: '合同{status}中',
        operate: '合同操作',
        freeContract: '完成首次合同发送，可免费再获取合同份数',
        sendContract: '去发合同',
        congratulations: '恭喜{name}已完成{num}份合同签署，',
        carbonSaving: '预估节碳{num}g',
        signGift: '上上签赠送您{num}份对公合同（使用期限至{limit}）',
        followPublic: '关注微信公众号，随时接收合同消息',
        congratulationsSingle: '恭喜{name}完成合同签署，',
        carbonSavingSingle: '预估新增节碳量2002.4g',
        viewContractTip: '如需更换盖章人，可点击“查看详情”按钮打开合同详情页，随后点击“申请盖章”按钮',
        congratulationsCn: '感谢选择电子签！',
        carbonSavingSingleCn: '您为地球减碳{num}gCO₂e',
        carbonVerification: '*经「碳阻迹」科学核算',
    },
    view: {
        title: '查看合同',
        ok: '完成',
        cannotReview: '无法查看合同',
        privateStorage: '发件方企业采用了合同私有存储的方式，但当前网络无法连接至合同存储服务器',
    },
    prepare: {
        sealArea: '盖章处',
        senderNotice: '当前合同发送主体为：{entName}',
        preSetDialogConfirm: '我知道了',
        preSetDialogContact: '马上联系上上签销售人员开通',
        preSetDialogInfo: '合同预置成功后，系统根据模板自动填写相应的签约方信息、签署要求、签署位置、合同描述字段等',
        preSetDialogTitle: '什么是合同预置模板？',
        initialValues: '根据合同内容预置初始值',
        proxyUpload: '上传本地文件后，可选择合同发起方',
        signHeaderTitle: '添加文件和签约方',
        step1: '第一步',
        confirmSender: '确认发起方',
        step2: '第二步',
        uploadFile: '上传文件',
        step3: '第三步',
        addSigner: '添加签约方',
        actionDemo: '操作演示',
        next: '下一步',
        isUploadingErr: '文件还未上传完成，请在完成后继续操作',
        noUploadFileErr: '未上传文件，请上传后继续操作',
        noContractTitleErr: '未填写合同名称，请填写后继续',
        contractTypeErr: '当前合同类型已删除，请重新选择合同类型',
        expiredDateErr: '签署截止时间有误，请修改后继续',
        noExpiredDateErr: '请填写签署截止时间后继续',
        describeFieldsErr: '请填写必填内容字段后继续',
        noRecipientsErr: '至少添加一个签约方',
        noAccountErr: '账号不能为空',
        noUserNameErr: '姓名不能为空',
        noIDNumberErr: '身份证号码不能为空',
        accountFormatErr: '格式不正确，请输入正确的手机号或邮箱',
        userNameFormatErr: '格式不正确，请输入正确的姓名',
        enterpriseNameErr: '请填写正确的企业名称',
        idNumberForVerifyErr: '格式不正确，请输入正确的身份证',
        signerErr: '签约方有误',
        noSignerErr: '请至少添加一个签署人',
        lackAttachmentNameErr: '请填写附件名称',
        repeatRecipientsErr: '非顺序签署时不能重复添加签约方',
        innerContact: '内部联系人',
        outerContact: '外部联系人',
        search: '搜索',
        accountSelected: '已选账号',
        groupNameAll: '全部',
        unclassified: '未分类',
        fileLessThan: '请上传小于{num}M的文件',
        beExcel: '请上传Excel文件',
        usePdf: '上传时请使用PDF文件或图片',
        usePdfFile: '上传时请使用PDF文件',
        fileNameMoreThan: '文件名称长度超过{num}，已为您自动截取',
        needAddSender: '未设置本企业/本人作为签约方，合同发出后，你方将不会参与签署过程。是否需要将你方加入签署？',
        addSender: '添加为签约方',
        tip: '提示',
        cancel: '取消',
    },
    addReceiver: {
        English: '英语',
        Japanese: '日语',
        Chinese: '中文',
        Arabic: '阿拉伯语',
        setNoticelang: '设置通知语言',
        limitFaceConfigTip: '你的合同单价过低，该功能不可用，请联系上上签协商',
        individual: '签约个人',
        enterprise: '签约企业',
        addInstructions: '添加签约须知',
        instructionsContent: '提交的资料用于帮助您追踪合同履约状态，判断业务执行是否正常。设置后，该签署人必须按要求提交',
        addContractingInfo: '提交签约主体资料',
        contractingInfoContent: '提交的资料用于帮助您查验签约方的主体资质，判断是否可以与其开始或继续开展业务。如果签约方已提交过的相同资料可以不再重复提交',
        payer: '付费方',
        handWriting: '开启手写笔迹识别',
        realName: '经办人需要实名',
        sameTip: '提示：签约方的企业名称完全一致才能签署',
        proxy: '对方前台代收',
        aboradTip: '提示：该签约方为境外人士，实名认证有风险，请先核对该人员的身份',

        busRole: '业务角色',
        busRoleTip: '能帮助您识别签约方，方便管理',
        busRolePlaceholder: '如员工/经销商',
        handWritingTip: '该用户需要在签署时手写清晰可辨的姓名才能完成签署',
        instructions: '添加签约须知 | （限255字）',
        contractingParty: '签约主体资料',
        signerPay: '本合同由该签署方付费',
        afterReadingTitle: '阅读完毕再签署',
        afterReading: '签署人必须阅读，并且知晓合同内容才可进行后续操作。',
        handWritingTips: '该用户手写的姓名将与发件人指定的或实名信息中的姓名进行比对，比对一致才可完成签署',
        SsTitle: '盖章并签字',
        SsTip: '使用企业印章签署时，需同时添加个人签名完成签署。签名前需完成个人实名认证',
        signature: '签字',
        stamp: '盖章',
        Ss: '盖章并签字',
        mutexError: '已设置“{msg}”，请先删除“{msg}”的设置后再选择',
        handWriteNotAllowed: '不允许手写签名',
        forceHandWrite: '必须手写签名',
        faceFirst: '优先刷脸，备用验证码签署',
        faceVerify: '必须刷脸签署',
        attachmentRequired: '添加合同附属资料',
        newAttachmentRequired: '提交签约主体资料',
        attachmentError: '合同附属资料名称不能相同',
        receiver: '接收手机/邮箱 |（最多支持5个，可用分号隔开）',
        receiverJa: '接收邮箱 |（最多支持5个，可用分号隔开）',
        orderSignLabel: '顺序签署',
        contactAddress: '联系人地址簿',
        signOrder: '签署顺序',
        account: '账号',
        accountPlaceholder: '手机/邮箱（必填）',
        accountPlaceholderJa: '邮箱（必填）',
        accountReceptionCollection: '前台代收',
        accountReceptionCollectionTip1: '不知道对方具体账号或对方没有账号，',
        accountReceptionCollectionTip2: '请选择前台代收',
        signSubjectPerson: '签约主体：个人',
        nameTips: '姓名（选填，用于签约身份核对）',
        requiredNameTips: '姓名（必填，用于签约身份核对）',
        entOperatorNameTips: '姓名（选填）',
        needAuth: '需要实名',
        operatorNeedAuth: '经办人需要实名',
        signSubjectEnt: '签约主体：公司',
        entNameTips: '企业名称（必填，用于签约身份核对）',
        operator: '经办人',
        sign: '签署',
        more: '更多',
        faceFirstTips: '签署时系统默认采用刷脸校验，当刷脸不通过的次数达到当日上限时自动切换为验证码校验',
        mustFace: '必须刷脸签署',
        mustHandWrite: '必须手写签名',
        fillIDNumber: '身份证号',
        fillNoticeCall: '通知手机',
        fillNoticeCallTips: '请填写通知手机',
        addNotice: '添加私信',
        attachTips: '添加合同附属资料',
        faceSign: '必须刷脸签署',
        faceSignTips: '该用户需要通过刷脸认证才能完成签署（刷脸签署暂只支持大陆居民使用）',
        handWriteNotAllowedTips: '该用户只能选择已经设置的签名或者使用默认字体签名才能完成签署',
        handWriteTips: '该用户需要手写签名才能完成签署',
        idNumberTips: '用于签约身份核对',
        verifyBefore: '查看文件前验证身份',
        verify: '验证身份',
        verifyTips: '最多20字',
        verifyTips2: '您须将此验证信息提供给该用户',
        sendToThirdPlatform: '发送给第三方平台',
        platFormName: '平台名称',
        fillThirdPlatFormName: '请输入第三方平台名称',
        attach: '资料',
        attachName: '资料名称',
        exampleID: '例：身份证照片',
        attachInfo: '备注',
        attachInfoTips: '例：请上传本人的身份证照片',
        addAttachRequire: '增加资料',
        addSignEnt: '添加签约企业',
        addSignPerson: '添加签约个人',
        selectContact: '选择联系人',
        save: '保 存',
        searchVerify: '查询校验',
        fillImageContentTips: '请填写图片内容',
        ok: '确定',
        findContact: '从合同中找到以下签约方',
        signer: '签约方',
        signerTips: '小提示：选择签约方后，平台可以帮助定位签字及盖章位置。',
        add: '添加',
        notAdd: '不添加',
        cc: '抄送',
        notNeedAuth: '不需要实名',
        operatorNotNeedAuth: '经办人不需要实名',
        extracting: '提取中',
        autoFill: '自动填写签署人',
        failExtracting: '未提取到签约方',
        idNumberForVerifyErr: '请输入正确的身份证',
        noAccountErr: '账号不能为空',
        noUserNameErr: '姓名不能为空',
        noIDNumberErr: '身份证号码不能为空',
        noEntNameErr: '企业名称不能为空',
        accountFormatErr: '请输入正确的手机号或邮箱',
        enterpriseNameErr: '请输入正确的公司名称',
        userNameFormatErr: '请输入正确的姓名',
        riskCues: '风险提示',
        riskCuesMsg: '如果签约方未实名签署，在文件发生纠纷时，需要您自己提供该签约方身份认定的证据。如需避免风险，请选择需要实名。',
        confirmBtnText: '选择需要实名',
        cancelBtnText: '选择不需要实名',
        attachLengthErr: '您最多只能为单个签署人添加50个附件要求',
        collapse: '收起',
        expand: '展开',
        delete: '删除',
        saySomething: '说点什么吧',
        addImage: '添加文档',
        addImageTips: '（支持word、pdf以及图片，不超过3份文档）',
        give: '给',
        fileMax: '上传数量超过数量上限!',
        signerLimit: '您当前的版本不支持超过{limit}个相对签署/抄送方。',
        showExamle: '查看示例图片',
        downloadExamle: '下载示例文件',
    },
    addReceiverGuide: {
        guideTitle: '如何添加新的签署人',
        receiverType: '您需要选择签署人参与合同的方式（六选一）：',
        asEntSign: '代表企业签署：',
        signatureSub: '法人或高管在合同上代表企业签字。签字完成后的合同是可以被企业收走的',
        vipOnly: '高级版本可用',
        sealSub: '签署人需在合同上加盖公章或合同专用章等',
        stampSub: '签署人既要盖章，也要代表企业签字',
        confirmSeal: '代表企业使用业务核对章',
        confirmSealSub: '财务对账单、询证函等文件先核实再盖章',
        asPersonSign: '代表个人签署：',
        asPersonSignTip: '仅代表个人签字，不代表任何企业',
        asPersonSignDesc: '签署人的私人合同，如借贷合同、入职离职协议等',
        scanSign: '扫码签字',
        scanSignDesc: '发合同时不需要写签署人，发出后任何人扫码/点查验页链接都可签署，适用物流单据收货场景',
        selectSignTypeTip: '请先选择签署方参与合同的方式',
        notRemind: '下次不再提醒',
        sign: '签字',
        entSign: '企业签字',
        stamp: '盖章',
        stampSign: '盖章并签字',
        requestSeal: '业务核对章',
    },
    linkContract: {
        title: '关联合同',
        connectMore: '关联更多合同',
        placeholder: '请输入合同编号',
        revoke: '合同已撤销',
        overdue: '逾期未签',
        approvalNotPassed: '审批被驳回',
        reject: '合同已拒签',
        signing: '签署中',
        complete: '已完成',
        approvaling: '审批中',
        disconnect: '解除关联',
        disconnectSuccess: '解除关联成功',
        connectLimit: '关联合同数量上限为100份',
    },
    field: {
        fieldTip: {
            title: '缺少签署位置',
            error: '没有在以下合同指定签署位置（{type}）',
            add: '添加字段',
            continue: '继续发送',
        },
        accountCharge: {
            notice: '该合同按参与账号数计费',
            able: '可以正常发送',
            unable: '可用账号数不足，请联系上上签客服',
            notify: '该合同给所有签约方发英文通知',
            noNotify: {
                1: '该合同不发签约相关通知',
                2: '（包括签署、审批、抄送、截止签署等通知短信和邮件）',
            },
        },
        ridingStamp: '骑缝章',
        watermark: '水印',
        senderSignature: '盖章人签字',
        optional: '选填',
        clickDecoration: '点击合同装饰',
        decoration: '合同装饰',
        sysError: '系统繁忙，请稍后再试',
        partedMarkedError: '指定了“盖章并签字”的签约方，必须同时指定盖章和签字',
        fieldTitle: '共有{length}份合同需要指定签署位置',
        send: '发送',
        contractDispatchApply: '申请发送合同',
        contractNeedYouSign: '该文件需要您签署',
        ifSignRightNow: '是否马上签署',
        signRightNow: '马上签署',
        signLater: '稍后签署',
        signaturePositionErr: '请为每个签署方指定签署位置',
        sendSucceed: '发送成功',
        confirm: '确定',
        cancel: '取消',
        qrCodeTips: '签署后扫码，即可查看签署详情、验证签名有效性及该合同是否被篡改',
        pagesField: '第{currentPage}页，共{totalPages}页',
        suitableWidth: '适合宽度',
        signCheck: '签名查验',
        locateSignaturePosition: '定位签署位置',
        locateTips: '可以帮助快速定位到签署位置。当前仅支持定位每个签署方第一个签署位置',
        step1: '第一步',
        selectSigner: '选择签约方',
        step2: '第二步',
        dragSignaturePosition: '拖动签署位置',
        signingField: '签署字段',
        docTitle: '文档',
        totalPages: '页数：{totalPages}页',
        receiver: '接收方',
        delete: '删除',
        deductPublicNotice: '对私合同可用份数不足时会扣除对公合同',
        unlimitedNotice: '该合同计费不限量使用',
        charge: '计费',
        units: '{num}份',
        contractToPrivate: '对私合同',
        contractToPublic: '对公合同',
        costTips: {
            1: '对公合同：签署人（不包含发件人）中有企业账户的合同',
            2: '对私合同：签署人（不包含发件人）中没有企业账户的合同',
            3: '计费份数根据文件份数计算',
            4: '计费份数 = 文件份数 × 批量导入用户组（行）数',
        },
        costInfo: '发送合同成功后将立即扣除费用，合同完成、逾期、撤回或拒签均不退还。',
        toCharge: '去充值',
        contractNeedCharge: {
            1: '可用合同份数不足，无法发送',
            2: '可用合同份数不足，请联系主管理员充值',
        },
        chooseApprover: '选择审批人：',
        nextStep: '下一步',
        submitApproval: '提交审批',
        autoSendAfterApproval: '*审批通过后，自动发送合同',
        chooseApprovalFlow: '请选择一个审批流',
        completeApprovalFlow: '您提交的审批流程不完整，请补全后重新提交',
        viewPrivateLetter: '查看私信',
        addPrivateLetter: '添加私信',
        append: '添加',
        privateLetter: '私信',
        signNeedKnow: '签约须知',
        maximum5M: '请上传小于5M的文档',
        uploadServerFailure: '上传到服务器失败',
        uploadFailure: '上传失败',
        pager: '页码',
        seal: '盖章',
        signature: '签名',
        signDate: '签署日期',
        text: '文本',
        date: '日期',
        qrCode: '二维码',
        number: '数字',
        dynamicTable: '动态表格',
        terms: '合同条款',
        checkBox: '复选框',
        radioBox: '单选框',
        image: '图片',
    },
    addressBook: {
        innerMember: {
            title: '企业内部成员',
            tips: '调整企业成员信息，让发件人都能更快找到内部联系人',
            operation: '去控制台',
        },
        outerContacts: {
            title: '外部企业联系人',
            tips: '邀请您的合作伙伴提前注册实名，以便与您顺利开展业务',
            operation: '邀请您的合作伙伴',
        },
        myContacts: {
            title: '我的联系人',
            tips: '修改联系人，确保签署人信息准确无误',
            operation: '去用户中心',
        },
        selected: '已选账号',
        search: '搜索',
        loadMore: '加载更多',
        end: '全部加载完成',
    },
    dataBoxInvite: {
        title: '邀请您的合作伙伴',
        step1: '分享链接给您的合作伙伴提前创建企业',
        step2: '通过链接/二维码授权后的合作伙伴会出现在地址簿',
        step3: '在“档案+”对您的合作伙伴做更多管理',
        imgName: '分享采集二维码',
        saveQrcode: '保存二维码到本地',
        copy: '复制',
        copySuccess: '复制成功',
        copyFailed: '复制失败',
    },
    shareView: {
        title: '转发审阅',
        account: '手机号/邮箱',
        role: '审阅人角色',
        note: '备注',
        link: '链接：',
        signerMessage: '签署人留言',
        rolePlaceholder: '如公司法务、部门领导等',
        notePlaceholder: '给审阅人留言，200字以内',
        generateLink: '生成链接',
        saveQrcode: '保存微信小程序码',
        regenerateLink: '重新生成链接',
        inputAccount: '请输入手机号码或者邮箱',
        inputCorrectAccount: '请输入正确的手机号码或者邮箱',
        accountInputTip: '为确保链接正常打开，请准确输入合同审阅人信息',
        shareLinkTip1: '保存微信小程序码或',
        shareLinkTip: '复制链接，分享给审阅人',
        linkTip1: '合同正文属于保密内容，非必要情况下请勿外泄',
        linkTip2: '链接有效期为2天；重新生成链接后，历史链接自动失效',
    },
    recoverSpecialSeal: {
        title: '印章无法使用',
        description1: '发件方要求您需使用该印章才能签署合同，但贵公司已删除该印章。为确保签署顺利进行，请向管理员恢复该印章。',
        description2: '如果该印章确实不合适被继续使用，可联系发件方修改对印章图案的要求后，再签署合同。',
        postRecover: '申请恢复印章',
        note: '点击后管理员将收到恢复印章申请的短信/邮件，同时在印章管理页面时也能看到申请。',
        requestSend: '恢复申请提交成功',
    },
    paperSign: {
        title: '使用纸质方式签署',
        stepText: ['下一步', '确认纸质签', '确定'],
        needUploadFile: '请先上传扫描件',
        uploadError: '上传失败',
        cancel: '取消',
        downloadPaperFile: '获取纸质签文件',
        step0: {
            title: '您需要先下载打印合同，加盖物理章后，邮寄给发件方。',
            address: '邮寄地址：',
            contactName: '接收人姓名：',
            contactPhone: '接收人联系方式：',
            defaultValue: '请通过线下方式向发件方索取',
        },
        step1: {
            title0: '第一步：下载&打印纸质合同',
            title0Desc: ['下载打印的合同应包含已签署的电子章的图案。请', '获取纸质签文件。'],
            title1: '第二步：加盖印章',
            title1Desc: '在纸质合同上加盖合同有效的公司印章。',
            title2: ['第三步：', '上传扫描件，', '回到签署页面，点击签署按钮，完成纸质签'],
            title2Desc: ['将纸质合同扫描转换成合同扫描件（PDF格式文件）后上传，', '电子合同中不展示您的印章图案，但会记录您此次操作过程。'],
        },
        step2: {
            title: ['将纸质合同扫描件（PDF格式文件）上传', '请确认纸质合同已下载并签署后，再点击确定按钮，结束纸质签署流程。'],
            uploadFile: '上传扫描件',
            getCodeVerify: '获取合同签署校验',
            isUploading: '上传中...',
        },
    },
    allowPaperSignDialog: {
        title: '允许纸质签',
        content: '该合同为{senderName}发给{receiverName}的合同, 允许使用纸质方式签署。',
        tip: '您也可以选择下载合同文档并打印，交由企业印章负责人线下盖章签署。',
        icon: '转纸质签署 >>',
        goSign: '去电子签',
        cancel: '取消',
    },
    sealInconformityDialog: {
        errorSeal: {
            title: '印章提示',
            tip: '检测到您当前印章图片与您的企业身份不符，当前印章图片识别结果：',
            tip1: '检测到有企业印章与企业名称：',
            tip2: '是否要继续使用当前印章图片？',
            tip3: '根据发件方要求，您需要使用企业名称为：',
            tip4: '的印章',
            tip5: '请确认印章已符合要求，否则将会影响合同的有效性！',
            tip6: '不匹配，请确保印章符合发件方要求。',
            guide: '如何上传正确的印章 >>',
            next: '继续使用',
            tip7: '且您的印章名称不符合规范，带有“{keyWord}”字样。',
            tip8: '检测到印章名称不符合规范，带有“{keyWord}”字样，是否要继续使用？',
        },
        exampleSeal: {
            title: '上传印章图案方式',
            way1: ['方式一：', '1、在白色纸张上盖一个实体印章图案', '2、拍照，并将图片上传至平台'],
            way2: ['方式二：', '直接使用平台的生成电子章功能，如图：'],
            errorWay: ['错误方式：', '手持印章', '无关图片', '营业执照'],
        },
        confirm: '确认',
        cancel: '取消',
    },
    addSealDialog: {
        title: '添加印章图片',
        dec1: '请从本地文件夹中选择一张印章图片（格式为JPG、JPEG、PNG等），由系统将此印章图片合并进入当前合同中。',
        dec2: '之后还需要您点击“签署”按钮通过签署校验，即可完成盖章。',
        updateNewSeal: '上传新章',
    },
};
