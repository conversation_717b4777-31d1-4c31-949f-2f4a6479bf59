export default {
    ssoLoginConfig: {
        notBelongToEntTip: '需要重新登录上上签平台才能发送合同（或管理模板）',
        operationStep: {
            one: '第一步 点击继续后，返回登录页面',
            two: '第二步 输入密码，进入上上签平台',
            three: '第三步 发送合同（或管理模板）',
        },
        continue: 'Continue',
        cancel: 'Cancel',
        tip: 'Tip',
    },
    sign: {
        sealLabelsTip: 'You need to affix {sealLabelslen} seals on the contract. {personStr} will affix {otherSealLen} seal for you, and you will personally affix the remaining {mySealLen} seal. The seals required are displayed on the page. Please confirm if you wish to proceed.',
        continue: 'Continue',
        nonMainlandCARenewalTip: '申请续期后，系统会自动驳回原实名结果，请尽快完成认证。',
        reselect: '重选',
        approvalFeatures: {
            dialogTitle: 'New Feature Introduction',
            understand: 'I understand',
            feature1: 'Sentence Highlighting Annotations',
            feature2: 'Field Highlighting',
            tip1: 'Click the button to highlight all "template content fields" in the contract to capture key information.',
            tip2: 'Click the prompt button in the lower left corner to enable template content field highlighting.',
            tip3: 'By highlighting, the content of the contract can be quickly located to fill in the fields, and the approval can be completed efficiently.',
            tip4: 'After selecting a text field, click the annotation button to add annotation text. Once completed, click modify or delete. The content of the annotations can be viewed in the contract details page - company internal operation log.',
            tip5: 'Step 1: Select the text field to be annotated and add the annotation.',
            tip6: 'Step 2: Click to edit or delete the annotation.',
            annotate: 'Annotations',
            delete: 'Deletion',
            edit: 'Modification',
            operateTitle: 'Add Approval Annotations',
            placeholder: 'No more than 255 words',
        },
        contractHighLight: {
            dialogTitle: 'Contract highlighting reminder',
        },
        needRemark: '您还需要填写备注',
        notNeedRemark: '您不需要填写备注',
        switchToReceiver: 'You have been switched to {receiver}',
        notAddEntTip: '当前用户不是该企业成员，请联系主管理员加入企业。',
        contractPartiesYouChoose: 'Contract parties you can choose:',
        contractPartyFilled: 'Contract party that filled in by sender is:',
        certifyOtherCompanies: 'Certify other companies',
        youCanAlso: 'You can also:',
        needVerification: 'You need real name verification to sign',
        prompt: 'Hint',
        submit: 'OK',
        cancel: 'Cancel',
        sign: 'Sign',
        addSeal: 'Please login PC BestSign website to add seal',
        noSealAvailable: 'Sorry, you don\'t have a seal and no seal management rights. Please contact the administrator to assign a seal to you',
        memberNoSealAvailable: '当前无可用印章，请联系管理员配置后再签署。或者线下联系主管理员配置。',
        noticeAdminFoSeal: 'Send notification to the master administrator',
        requestSomeone: 'Request someone else to verify',
        requestOthersToContinue: 'Ask Administrator to fulfill',
        requestOthersToContinueSucceed: 'Request sent successfully',
        requestSomeoneList: 'Request the following personnel to complete the real name certification:',
        electronicSeal: 'Electronic seal',
        changeTheSeal: 'Don’t want to use this seal? Change the seal after real name verification',
        goToVerify: 'Go to verify ',
        noSealToChoose: 'No seal to choose, if you need managing seals, please get real name verification',
        goToVerifyEnt: 'Go to verify enterprise',
        digitalCertificateTip: 'BestSign is calling your digital certificate',
        signDes: 'You\'re in secure signing environment, feel free to sign!',
        goVerify: 'Go verify ',
        signAgain: 'Sign',
        send: 'Send',
        person: 'Natural person',
        ent: 'Enterprise',
        entName: 'Name of enterprise',
        account: 'Account',
        accountPH: 'Telephone or email',
        approved: 'Approved',
        signVerification: 'Sign',
        cannotReview: 'Cannot review contract',
        connectFail: 'Sender\'s enterprise uses the contract private storage, but the current network cannot connect to the contract storage server.',
        connectFailTip: '您可以尝试以下方法解决问题：',
        connectFailTip1: '1、刷新页面。',
        connectFailTip2: '2、耐心等待并稍后重试。有可能是因为发件方企业部署的服务器出现了异常，企业IT技术人员重启服务器需要时间。',
        connectFailTip3: '3、发件方企业是否向你强调过，需要使用特定的wifi网络才能访问？如果有过这方面的说明，你需要切换手机或电脑设备连接的网络。',
        personalMaterials: 'Sender requests you to add more certification materials',
        noSupportface: 'The contract initiator requests you to sign with face ID, non-mainland Chinese people do not support face ID signing, please contact the initiator to modify the signing requirements',
        lackEntName: 'Please input name of enterprise',
        errAccount: 'Please input correct telephone or email',
        noticeAdmin: 'Apply',
        signDone: 'Signing completed',
        signDoneTip: 'You have signed this contract',
        approveDone: 'Approval completed',
        approveDoneTip: 'You have approved this contract',
        completeSign: 'Please click "Seal" or "Signature" to complete the signing',
        fillFirst: 'Please fill in the contract content in the input box first',
        stillSignTip: 'After you sign this {alias}, there are still other signatories who may modify the {alias} and continue to sign it?',
        signHighLightTip: 'A total of {count} {alias} contents can be added or modified',
        riskDetails: 'Risk details',
        noviewDifference: 'Because sender opened the parties can fill in the {alias} of fixed field function, other signed this {alias} may still change the initiator content specified in the {alias}, sign on the right version before the signing of this {alias} and the content of the difference between effective version for review, when you after signing the {alias}, signed as you agree to the other party to the {alias} content of fixed field increase or modify the content, And recognize the effective version of this {alias} signed by each signatory.\n' +
            'If you do not agree that other signatories can still change the fields of this {alias} after you sign, you may refuse to sign this {alias} and negotiate with the sender (that is, ask the initiator to turn off the function of "Signatory fill in the fields" to avoid the corresponding risks to you).',
        highLightTip: 'These risky content will be "highlighted", please check carefully. Refresh the page to remove the highlighting effect.',
        commonTip: 'Tip',
        understand: 'I understand',
        view: 'View',
        start: 'Start',
        nextStep: 'next',
        help: 'Help',
        faceFailed: 'Sorry, your face comparison failed',
        dualFailed: 'Sorry, the dual-recording verification has failed. Please verify your information and try again.',
        faceFailedtips: 'Tips',
        qrcodeInvalid: 'Qrcode is invalid，please refresh',
        faceFirstExceed: 'Face scan failed, Authentication code will be used in the next step for authentication.',
        verifyTry: 'Verify the identity and try again',
        faceLimit: 'Today\'s face comparison has reached the line',
        upSignReq: 'Please try again tomorrow or contact the contract initiator to modify the signing requirements',
        reqFace: 'Sender requests you to do Face ID verification',
        signAfterFace: 'After Face ID passes, you can complete the contract signing',
        date: 'date',
        chooseSeal: 'Choose seal',
        seal: 'seal',
        signature: 'Create signature ',
        handwrite: 'Handwritten',
        mysign: 'My signature',
        approvePlace: 'Approval message, not necessary',
        approvePlace_1: 'Approval message',
        approvePlace_2: 'Optional, maximum 255 characters',
        approveAgree: 'Approval result: Approved',
        approveReject: 'Approval result: Reject',
        signBy: 'Sign by ',
        signByEnd: '',
        sealBy: 'Seal by ',
        sealByEnd: '',
        coverBy: 'Cover by ',
        applicant: 'Applicant:',
        continueVeri: 'Go certify',
        registerAndReal: 'Please register and real name',
        goToResiter: 'Go to registration and certification',
        sureToUse: 'Are you sure with ',
        toSign: 'to sign?',
        pleaseComplete: 'Please complete the signatue before confirming the ',
        confirmSign: '',
        admin: 'administrator',
        contratAdmin: 'Please contact the administrator to ',
        addToEnt: 'add your account as a business',
        alreadyExists: 'On the BestSign already exists',
        sendMsg: 'BestSign will send the following to the administrator as a text message:',
        applyJoin: 'Apply to join ',
        title: 'Title',
        viewImg: 'View picture',
        priLetter: 'Message',
        priLetterFromSomeone: 'Message from {name}',
        readLetter: 'OK',
        approve: 'Approve',
        disapprove: 'Reject',
        refuseSign: 'Reject',
        paperSign: '改用纸质签署',
        refuseTip: 'Please choose the reason for rejection',
        refuseReason: 'Fill in the reason for the refusal to help the other party understand your problem and speed up the contract process',
        reasonWriteTip: 'Please fill in the reason for rejection',
        refuseReasonOther: 'More reasons for rejection (optional) | More reasons for rejection (required)',
        refuseConfirm: 'Reject',
        refuseConfirmTip: 'You have refused to sign this contract for the reason "{reason}". Do you want to continue? After confirmation, you will not be able to sign this contract again.',
        waitAndThink: 'Reconsider',
        signValidationTitle: 'Sign Verification',
        email: 'E-mail',
        phoneNumber: 'Phone',
        password: 'Password',
        verificationCode: 'SMS Code',
        mailVerificationCode: 'Verification code',
        forgetPsw: 'Forget?',
        if: ',',
        forgetPassword: 'Forgot?',
        rejectionVer: 'Verification',
        msgTip: 'Can\'t review the SMS messages? Try',
        voiceVerCode: 'a voice call',
        SMSVerCode: 'SMS verification code',
        emailVerCode: 'E-mail verification code',
        or: 'or',
        SentSuccessfully: 'Send successfully!',
        intervalTip: 'Please send again later',
        signPsw: 'Sign Password',
        signConfirmTip: {
            1: 'Are you sure you want to sign this {contract}?',
            2: 'Click the confirm button to sign the {contract} immediately',
            confirm: 'Confirm',
        },
        useSignPsw: 'Use the password for verification',
        setSignPsw: 'Set the signing password verification',
        useVerCode: 'Use verification code',
        inputSignPwdTip: 'Please enter the signing password',
        inputVerifyCodeTip: 'Please enter verification code',
        signSuc: 'Sign successfully',
        refuseSuc: 'Refused',
        approveSuc: 'Approval succeeded',
        hdFile: 'View HD files',
        otherOperations: 'Other operations',
        reviewDetails: 'Approval details',
        close: 'Close',
        submitter: 'Approval initiator',
        signatory: 'Signatory',
        reviewSchedule: 'Approval schedule',
        signByPc: 'Sign by {name}',
        signPageDescription: 'Page {index}, {total} in total',
        sealBySomeone: 'Seal by {name}',
        signDate: 'Signing date',
        download: 'Download',
        signPage: 'Page: {page}',
        signNow: 'Sign now',
        sender: 'Sender',
        signer: 'Signer',
        startSignTime: 'Start signing time',
        signDeadLine: 'Signing deadline',
        authGuide: {
            goToHome: 'Go to home',
            tip_1: 'After the certification is completed, you can view and sign the contract.',
            tip_2: 'Please use identity | to auth.',
            tip_3: 'send a contract',
            tip_4: 'Please contact the contract initiator | change recipient.',
            tip_5: 'Your certified | unable to view contract',
            new_tip_1: 'Based on the sender\'s compliance requirements, you need to complete the following steps:',
            new_tip_2: 'Based on the compliance requirements of the sender, you need to:',
            new_tip_3: 'complete the following steps.',
            new_tip_4: 'If you already have seal permission, step 2 will be automatically skipped for you',
            entUserName: 'username:',
            idNumberForVerify: 'IDCard:',
            realNameAuth: 'Real-name authentication',
            applySeal: 'Apply for the seal',
            signContract: 'Sign contract',
        },
        switch: 'Switch',
        rejectReasonList: {
            // authReason: '不想/不会做实名认证',
            signOperateReason: 'If you have questions about the signing operation/verification operation, further communication is needed',
            termReason: 'I have doubts about the terms/content of the contract and need to communicate further',
            explainReason: 'If you are not aware of the contract contents, please inform us in advance',
            otherReason: 'Others (please fill in the reason)',
        },
        selectSignature: 'Select signature',
        selectSigner: 'Choose signer',
        pleaseScanToSign: 'Please scan and sign with Alipay or WeChat',
        pleaseScanAliPay: 'Please use Alipay app to scan the QR code to sign',
        pleaseScanWechat: 'Please use WeChat app to scan the QR code to sign',
        requiredFaceSign: 'The sender of the contract asks you to scan your face to recognize and sign',
        requiredDualSign: 'The contract sender requires you to complete dual-recording verification',
        verCodeVerify: 'Verification code verification',
        applyToSign: 'Apply to sign a contract',
        autoRemindAfterApproval: '*After the approval is passed, the signing reminder will be automatically sent to the signatory',
        cannotSignBeforeApproval: 'The approval has not been completed and cannot be signed temporarily',
        finishSignatureBeforeSign: 'Please complete the stamp/signature before confirming the signature',
        uploadFileOnRightSite: 'You still have attachments that have not been uploaded, please upload them in the right column first',
        cannotApplySealNeedPay: 'This contract requires you to pay, and the application for seal is not supported',
        cannotOtherSealReason: 'Face verification is required for this contract. Signature by others is not allowed',
        unlimitedNotice: 'Unlimited use of the contract billing',
        units: '{num}',
        contractToPrivate: 'individual contracts',
        contractToPublic: 'company contracts',
        paySum: '共{sum}需要您支付',
        payTotal: '共计{total}元.',
        fundsLack: '您的可用合同份数不足，为确保合同顺利签署，建议立即充值.',
        contactToRecharge: 'Please contact the main administrator to recharge.',
        deductPublicNotice: 'Private contracts will be deducted when the number of available copies is insufficient.',
        needSignerPay: 'The sender of the contract has set recipient payment, and designates you to pay for the contract.',
        recharge: 'Recharge',
        toSubmit: 'Submit',
        appliedSeal: 'Application for seal has been submitted',
        noSeal: 'No seal',
        noSwitchSealNeedDistribute: 'Sorry, you do not currently have a seal that can be used, please contact the business owner administrator to add a seal and authorize',
        viewApproveProcess: 'View the approval process',
        approveProcess: 'Approval process',
        noApproveContent: 'No approval note were submitted',
        knew: 'I see',
        noSwitchSealNeedAppend: 'There is no switchable stamp, please contact the administrator to add a stamp',
        hadAutoSet: 'Automatically placed in {num} other places',
        setThatSignature: 'by this signature',
        setThatSeal: 'by this stamp',
        applyThatSeal: 'applied by this stamp',
        hasSetTip: 'Automatically placed at {index} other locations',
        hasSetSealTip: 'The seal has been automatically placed in {index} other places',
        hasSetSignatureTip: 'The signature has been automatically placed in {index} other places',
        hasApplyForSealTip: 'The seal has been automatically applied for in {index} other places',
        savedOnLeftSite: 'Saved to the signature bar on the left',
        ridingSealMinLimit: 'The document has only one page and cannot be stamped with a seam seal',
        ridingSealMaxLimit: 'More than 146 pages, stamping of seam seal is not supported',
        ridingSealMinOrMaxLimit: 'The document has only one page or exceeds 146 pages, and cannot be stamped',
        noSealForRiding: 'You do not have a seal that can be used, and cannot be stamped with a seam seal',
        noSwitchSealNeedAppendBySelf: 'There is no switchable stamp, you can go to the enterprise console to add a stamp',
        gotoAppendSeal: 'Go to add a seal',
        approvalFlowSuccessfulSet: 'Approval flow is set successfully',
        mandate: 'Agree to authorize',
        loginToAppendSeal: 'You can also log in to BestSign website with a computer and go to the corporate console to add a seal',
        signIdentityAs: 'Currently signing the contract in the name of {person}',
        enterNextContract: 'Go to the next contract',
        fileList: 'Document list',
        addSignerFile: 'Add additional information',
        signatureFinish: 'All stamped/signed',
        dragSignatureTip: 'Please drag and drop the following signatures/dates into the file, you can drag and drop multiple times',
        noticeToManager: 'Notify the administrator',
        gotoAuthPerson: 'To authenticate individuals',
        senderRequire: 'The sender requires you ',
        senderRequireUseFollowIdentity: 'The sender requires you to meet the following identity:',
        suggestToAuth: 'You have not verified your real name yet, it is recommended that you sign after real name verification',
        contactEntAdmin: 'Please contact the business owner administrator',
        setYourAccount: 'add your account',
        authInfoUnMatchNeedResend: 'to sign the contract. This does not match your real-name identity information. Please contact the initiator yourself to confirm your identity information and request to initiate the contract again',
        noEntNameNeedResend: 'The contract cannot be signed without specifying the name of the contracted company, please contact the initiator to resend the contract',
        pleaseUse: 'Please authenticate as',
        me: '我',
        myself: '本人，',
        reAuthBtnTip: '我是当前手机号的实际使用者，',
        reAuthBtnContent: '重新实名后，该账号的原实名会被驳回，请确认。',
        descNoSame1: ' 的身份签署合同',
        descNoSame2: '这与您当前登录的账号已完成的实名信息不符。',
        authInfoNoSame: '的身份签署合同。这与您当前登录的账号已完成的实名信息不符。',
        authInfoNoSame2: '的身份签署合同。这与您当前登录账号的基础身份信息不符。',
        goHome: '返回合同列表页>>',
        authInfo: '检测到您当前账号的实名身份为 ',
        authInfo2: '检测到您当前账号的基础身份信息为 ',
        in: '于',
        finishAuth: '完成实名，用于合规签署合同',
        ask: '是否继续以当前账号签署？',
        reAuthBtnText: '是的，我要用本账号重新实名签署',
        changePhoneText: '不是，联系发件方更改签署手机号',
        changePhoneTip1: '应发件方要求，请联系',
        changePhoneTip2: '，更换签署信息(手机号/姓名)，并指定由您签署。',
        confirmOk: 'OK',
        goOnAuth: {
            0: 'perform real-name authentication,',
            1: 'Please perform real-name authentication,',
            2: 'perform real-name authentication,',
        },
        signContractAfterAuth: {
            0: 'after the certification is completed, the contract can be signed.',
            1: 'after completing the certification, you can sign the contract.',
        },
        useIdentity: 'As {name}',
        inTheName: 'as',
        of: '',
        identity: '',
        nameIs: 'name as',
        IDNumIs: 'ID number as',
        provideMoreAuthData: 'Add more certification materials',
        leadToAuthBeforeSign: 'You can sign the contract after continuing the certification',
        groupProxyAuthNeedMore: 'Your current certification status is group certification, if you need to sign a contract separately, please add real-name certification materials',
        contactSender: 'If you have any questions, please contact the sender.',
        note: 'Note:',
        identityInfo: 'Identity Information',
        signNeedCoincidenceInfo: 'The information needs to be completely consistent to sign the contract.',
        needAuthPermissionContactAdmin: 'You do not have the real-name authentication authority for the time being, please contact the administrator',
        iHadReadContract: 'I have read, I know the content of the {alias}',
        scrollToBottomTip: 'You need to scroll to the last page',
        getVerCodeFirst: 'Please get the verification code first',
        appScanVerify: 'Scan code verification with BestSign APP',
        downloadBSApp: 'Download BestSign APP',
        scanned: 'Scan code successfully',
        confirmInBSApp: 'Please confirm your signature in BestSign APP',
        qrCodeExpired: 'The QR code has expired, please refresh and try again',
        appKey: 'APP security check',
        goToScan: 'Go scan code',
        setNotificationInUserCenter: 'Please go to the user center to set the notification method',
        doNotWantUseVerCode: 'Don\'t want to use verification code',
        try: 'try',
        retry: 'retry',
        goToFaceVerify: 'go to face recognition',
        faceExceedTimes: 'Face times has exceed',
        returnBack: 'return back',
        switchTo: 'switch to',
        youCanChooseIdentityBlow: 'You can choose the following signing subject',
        needDrawSignatureFirst: 'You have not signed, please add a hand-painted signature first',
        lacksSealNeedAppend: 'You have not added any stamps, please add a stamp first.',
        manageSeal: 'Management seal',
        needDistributeSealToSelf: 'You don’t have a stamp available, please set yourself as the stamp holder',
        chooseSealAfterAuth: 'Don\'t want to use the above stamp? The seal can be replaced after real-name authentication',
        appendDrawSignature: 'Add a hand-drawn signature',
        senderUnFill: '（The sender did not fill in）',
        declare: 'Description',
        fileLessThan: 'Please upload a file smaller than {num}M',
        fileNeedUploadImg: 'Please use pictures when uploading',
        serverError: 'The server went up a bit, please try again later',
        oldFormatTip: '支持jpg、png、jpeg、pdf、txt、zip、xml格式，单份文件大小不超过10M',
        fileLimitFormatAndSize: 'The total size of auxiliary data shall not exceed XXM, and the number of single data pictures shall not exceed 10M.',
        fileFormatImage: 'Support JPG, PNG and JPEG formats, and the size of a single picture no exceeds 20M',
        fileFormatFile: 'Support PDF, excel, word, txt, zip, xml, JPG, PNG and JPEG formats, and the size of a single file no exceeds 10M',
        signNeedKnow: 'Signing notice',
        signNeedKnowFrom: 'Signing instructions from {sender}',
        approvalInfo: 'Approval notice',
        approveNeedKnowFrom: 'Approval materials submitted by {sendEmployeeName} at {sender}({approvalType})',
        approveBeforeSend: 'Approve before sending',
        approveBeforeSign: 'Approve before signing',
        approveOperator: 'Approver',
        approvalOpinion: 'Approval opinion',
        employeeDefault: 'Employee',
        setLabel: 'Set label',
        addRidingSeal: 'Add seam seal',
        delRidingSeal: 'Remove seam seal',
        file: 'Attachment',
        compressedFile: '压缩文件',
        attachmentContent: 'Attachment content',
        pleaseClickView: '（请点击下载查看）',
        downloadFile: 'Download source file',
        noLabelPleaseAppend: 'No tags yet, please go to the enterprise console to add',
        archiveTo: 'Archive to',
        hadArchivedToFolder: 'The contract has been successfully moved to the {folderName} folder of {who}',
        pleaseScanToHandleWrite: 'Please use WeChat or mobile browser to scan the code, handwritten signature on the mobile device',
        save: 'Save',
        remind: 'Remind',
        riskTip: 'Risk reminder',
        chooseApplyPerson: 'Choose the person to stamp on the contract',
        chooseAdminSign: 'Select a seal administrator',
        useSealByOther: 'Stamped by others',
        getSeal: 'Get a seal',
        nowApplySealList: 'You are requesting the following seal',
        nowAdminSealList: 'You are applying to receive the following seal',
        chooseApplyPersonToDeal: 'Please select the person to stamp on the contract and the contract will be handled by the selected person (You can still continue to review and follow up on this contract)',
        chooseApplyPersonToMandate: 'Please select the seal administrator. After the selected person receives the notification and passed the review, you will be granted the right to use the seal, and you can use the seal to seal and sign the contract.',
        contactGroupAdminToDistributeSeal: 'Please contact the group administrator to assign a seal',
        sealApplySentPleaseWait: 'The seal distribution application has been sent, please wait for approval. Or you can choose other stamping methods',
        successfulSent: 'Sent successfully',
        authTip: {
            t2: ['Note: ', ' has to be exactly the same to sign the contract.', 'The company name', 'The identity information', ' has to be exactly the same to view and sign the contract.'],
            t3: '{x} requires you to perform real name authentication {text}. ',
            tCommon1: 'as {entName}',
            tCommon2_1: 'as using your name as {name} and ID card No. {idCard}',
            tCommon2_2: 'as using your name as {name}',
            tCommon2_3: 'as using ID card No. {idCard}',
            viewAndSign1: 'After completing the authentication, you can view and sign the contract. If you have any questions, please contact the sender. ',
            viewAndSignConflict: '{x} requires you to view and sign the contract {text}. This does not match your real-name identity information. Please contact the sender yourself to confirm your identity information and request to initiate the contract again.',
        },
        needSomeoneToSignature: 'Seal {x} by {y}',
        needToSet: 'Need to stamp',
        approver: 'Applicant:',
        clickToSignature: 'Click here to sign',
        transferToOtherToSign: 'Transfer to other people to sign',
        signatureBy: 'Signed by {x}',
        tipRightNumber: 'Please enter the correct number',
        tipRightIdCard: 'Please enter a valid 18-digit Mainland China Resident ID Card number',
        tipRightPhoneNumber: 'Please enter a valid 11-digit mobile phone number',
        tip: 'Tip',
        tipRequired: 'Required value cannot be blank',
        confirm: 'Confirm',
        viewContractDetail: 'View contract details',
        required: 'Required',
        optional: 'Optional',
        decimalLimit: 'Limited to {x} decimal places',
        intLimit: 'integer required',
        invalidContract: '签署此合同视为您同意将以下合同作废：',
        No: '编号',
        chooseFrom2: '发件方设置了二选一盖章，请选择一处盖章',
        crossPlatformCofirm: {
            message: 'Hello, the current contract needs to be signed across platforms, and the signed documents need to be transferred to overseas. Do you agree?',
            title: 'Data authorization',
            confirmButtonText: 'Agree to authorize',
            cancelButtonText: 'Cancel',
        },
        sealScope: 'Seal Usage Scope',
        currentContract: 'Current Contract',
        allContract: 'All Contracts',
        docView: 'Contract Preview',
        fixTextDisplay: 'Fix Text Display',
        allPage: '{num} Page',
        notJoinTip: 'Please contact the administrator to be added as a company member before signing',
    },
    signJa: {
        beforeSignTip1: 'According to the sender\'s request, please sign in the name of this enterprise:',
        beforeSignTip2: '发件方指定了 {signer} 完成签署。如确认信息正确, 可直接签署。',
        beforeSignTip3: '如信息有误, 请与发件方联系, 更换指定的签署人信息。',
        beforeSignTip4: '检测到该账号已注册的姓名为 {currentUser}, 与当前发件方要求的 {signer} 不一致, 是否确认更换为 {signer} ',
        beforeSignTip5: '检测到当前账号绑定的姓名为：{currentUser}, 与甲方指定要求 {signer} 签署, 不一致',
        beforeSignTip6: '请根据实际情况, 确认修改为甲方指定的 {signer} 进行签署',
        beforeSignTip7: '或者与甲方进行沟通，更换指定的签署人',
        entNamePlaceholder: '请输入企业名称',
        corporateNumberPlaceholder: '请输入法人番号',
        corporateNumber: 'corporate identification number',
        singerNamePlaceholder: '请输入签署人姓名',
        singerName: '签署人姓名',
        businessPic: 'Seal certificate',
        waitApprove: 'Under review. If you need to know the progress of the review, you can contact us by email: <EMAIL>',
        itsMe: '是我本人',
        wrongInformation: '信息有误',
        confirmChange: '确认更换',
        communicateSender1: '不更换, 与甲方沟通',
        communicateSender2: '取消, 去与发件方沟通',
        createSeal: {
            title: '输入姓名',
            tip: '请输入您的姓名（空格可以进行换行）',
            emptyErr: '请输入姓名',
        },
        areaRegister: 'Country of Incorporation',
        jp: 'Japan',
        cn: 'Chinese Mainland',
        are: 'United Arab Emirates',
        other: 'Other',
        plsSelect: 'Please select',
        tip1: 'Enterprises registered in Chinese Mainland need to complete real name registration in ent.bestsign.cn. When signing contracts with enterprises outside the Chinese Mainland, the "cross-border signing" function can be used to efficiently complete mutual signing of contracts on the premise of ensuring the security of user data without leakage.',
        tip2: 'If your enterprise has signed on the Chinese Mainland version to complete the real name authentication, you can directly log on to ent.bestsign.cn for convenient use of related services. It should be noted that the data generated by signing the overseas version is completely independent from the Chinese Mainland version.',
        tip3: 'Please provide the identification number you obtained from the local commercial regulatory agency',
        tip4: 'Please follow the steps below',
        tip5: '1. Please contact your dedicated account manager to guide you through enterprise verification.',
        tip6: 'Click on \'Recharge Management\'.',
        tip7: '2. Please provide screenshots of your commercial contract with Esign or business correspondence emails with your dedicated account manager.',
        tip8: 'Purchase at least one contract and save a screenshot of the purchase record.',
        tip9: '3. This verification method is only available for enterprises outside Mainland China and Japan.',
        tip10: '4. The review will be completed within 3 business days after submission.',
        tip11: 'Important Note',
        tip12: 'The purchaser must be a corporate user.',
        tip13: 'The full name of the company in the payment account must be exactly the same as the "company name" you have filled in.',
        tip14: 'Only enterprises outside Japan and Chinese Mainland can use this method.',
        comNum: 'Enterprise ID number',
        buyRecord: 'Supporting Documents',
        selectArea: 'Please select the registered address of the enterprise',
        uaeTip1: 'Enterprises registered in the United Arab Emirates must complete real name registration on uae.bestsign.com. When signing contracts with companies outside the United Arab Emirates, the "cross-border signing" function can be used to efficiently complete the mutual signing of contracts while ensuring the security and confidentiality of user data.',
        uaeTip2: 'If your company has completed real name authentication on the UAE version of Shangshang, you can directly log in to uae.bestsign.com to conveniently use related services. It should be noted that the data generated by the overseas version you signed on is completely separate from the UAE version.',
        uaeTip3: 'Enterprises registered outside the United Arab Emirates and Chinese Mainland need to complete real name registration in ent.bestsign.com. When signing contracts with companies in the United Arab Emirates, the "cross-border signing" function can be utilized to efficiently complete the mutual signing of contracts while ensuring the security and confidentiality of user data.',
    },
    signPC: {
        commonSign: 'Confirm signing',
        contractVerification: 'Signing verification',
        VerCodeVerify: 'Verification code check',
        QrCodeVerify: 'QR code check',
        verifyTip: 'BestSign is calling your Digital CA certificate (Certificate Athority), and you are in a secure signing environment, please be assured to sign!',
        verifyAllTip: 'BestSign is calling the enterprise digital certificate and your personal digital certificate, you are in a secure signing environment, please rest assured to sign!',
        selectSeal: 'Electronic seal',
        adminGuideTip: '因为您是企业主管理员，可以直接将企业印章分配给自己',
        toAddSealWithConsole: 'The electronic official seal is pending activation. To add other seals, please go to the console.',
        use: 'Use',
        toAddSeal: 'To add a seal',
        mySeal: 'My seals',
        operationCompleted: 'Operation completed',
        FDASign: {
            date: 'Date',
            signerAdd: 'Add',
            signerEdit: 'Edit',
            editTip: 'Note: Chinese name please input Pinyin, such as San Zhang',
            inputNameTip: 'Please enter your name',
            inputName: 'Please input English name or Chinese name in Pinyin',
            signerNameFillTip: 'You will also need to fill in your name',
            plsInput: 'Please input',
            plsSelect: 'Please select',
            customInput: 'Custom input',
        },
        signPlaceBySigner: {
            signGuide: 'Guide to signing',
            howDragSeal: 'How to drag a stamp',
            howDragSignature: 'How to drag signatures',
            iKnow: 'I see',
            step: {
                one: 'Step 1: read the contract',
                two1: 'Step 2: click "Drag stamp"',
                two2: 'Step 2: click "Drag signature"',
                three: 'Step 3: click the "Sign" button',
            },
            dragSeal: 'Drag stamp',
            continueDragSeal: 'Continue to drag stamp',
            dragSignature: 'Drag signature',
            continueDragSignature: 'Continue to drag signature',
            dragPlace: 'Drag it here',
            notRemind: 'Never show again',
            signTip: {
                one: 'Step 1: Locate where you need to sign/stamp by clicking "Start".',
                two: 'Step 2: Complete the signature/seal as required by clicking "Signature/Stamp".',
            },
            finishSignatureBeforeSign: 'Please finish dragging signature/stamp before confirming sign',
        },
        continueOperation: {
            success: 'Success',
            exitApproval: 'Exit approval',
            continueApproval: 'Continue approving',
            next: 'Next contract:',
            none: 'None',
            tip: 'Tip',
            approvalProcess: 'The contract needs {totalNum} people to approve; Currently {passNum} people have approved.',
            receiver: 'receivers:',
        },
    },
    signTip: {
        contractDetail: 'Contract Details',
        downloadBtn: 'Download APP',
        tips: 'Tip',
        submit: 'OK',
        SigningCompleted: 'Sign successfully',
        submitCompleted: '等待他人处理',
        noTurnSign: '尚未轮到签署或没有签署权限或登录身份已过期',
        noRightSign: '合同正在签署中，当前用户不允许签署操作',
        noNeedSign: '内部决议合同，已无需签署',
        ApprovalCompleted: 'Approval succeeded',
        contractRevoked: 'The contract has been canceled',
        contractRefused: 'The contract has been refused',
        linkExpired: 'The link has expired',
        contractClosed: 'The contract has been closed',
        approvalReject: 'The contract approval has been rejected',
        approving: 'The contract is under review',
        viewContract: 'View contract',
        viewContractList: 'View Contract List',
        needMeSign: ' ({num} Pending for Signature)',
        downloadContract: 'Download contract',
        sign: 'sign',
        signed: ' signed',
        approved: 'approved',
        approval: 'approval',
        person: ' person',
        personHas: ' ',
        personHave: ' ',
        personHasnot: ' not ',
        personsHavenot: ' not ',
        headsTaskDone: '{num}{has}{done}',
        headsTaskNotDone: '{num}{not}{done}',
        taskStatusBetween: ',',
        cannotReview: 'Cannot review contract',
        cannotDownload: 'The contract does not support mobile phone downloads. Because the contract is privately stored by the sender, the contract cannot be obtained by BestSign.',
        privateStorage: 'Sender\'s enterprise uses the contract private storage, but the current network cannot connect to the contract storage server',
        beenDeleted: 'Your account has been deleted by the enterprise administrator',
        unActive: 'Unable to continue to activate account',
        back: 'back',
        contratStatusDes: '{key} status: ',
        contractConditionDes: '{key} condition: ',
        contractIng: 'Contract in {key}',
        contractComplete: 'Contract {key} completed',
        dataProduct: {
            tip1: '{entName}致各位优质经销商/供应商企业负责人：',
            tip2: '为答谢您为{entName}的稳定发展作出的贡献，特此联合{bankName}推出供应链金融服务，助力您的企业加速发展！',
            btnText: '去向老板分享这个喜讯',
        },
        signOnGoing: 'Signing',
        operate: 'operate',
        freeContract: 'Upon completing the first contract dispatch, additional contract copies can be obtained for free.',
        sendContract: 'Send contract',
        congratulations: 'Congratulations to {name} for completing {num} contract signings,',
        carbonSaving: 'with an estimated carbon saving of {num}g.',
        signGift: 'BestSign presents you with {num} corporate contracts (usage period valid until {limit}).',
        followPublic: 'Follow our WeChat official account to receive contract updates promptly.',
        congratulationsSingle: 'Congratulations to {name} on the contract signing,',
        carbonSavingSingle: 'Estimated carbon reduction: 2,002.4g',
        viewContractTip: 'If you need to change the person who stamps, you can click the \"View detail\" button to open the contract details page, then click the \"Apply for Stamp\" button.',
        congratulationsCn: 'Thank you for choosing e-signature!',
        carbonSavingSingleCn: 'You have reduced carbon by {num}gCO₂e for the Earth',
        carbonVerification: "*Scientifically calculated by 'Carbonstop'",
    },
    view: {
        title: 'View Contract',
        ok: 'OK',
        cannotReview: 'Cannot review contract',
        privateStorage: 'Sender\'s enterprise uses the contract private storage, but the current network cannot connect to the contract storage server',
    },
    prepare: {
        sealArea: 'Seal here',
        senderNotice: 'Current contract sender:{entName}',
        preSetDialogConfirm: 'OK',
        preSetDialogContact: 'Contact BestSign sales representatives to open your account immediately',
        preSetDialogInfo: 'When sending a contract, the system will automatically fill in the corresponding signatory information, signing requirements, signing locations, contract description fields, etc. according to the pre-set template',
        preSetDialogTitle: 'What is a pre-set contract template?',
        initialValues: 'Pre-set initial values based on contract content',
        proxyUpload: 'Select contract sender after uploading local documents',
        signHeaderTitle: 'Add files and signer',
        step1: 'Step 1',
        confirmSender: 'Confirmation of sender',
        step2: 'Step 2',
        uploadFile: 'Upload a file',
        step3: 'Step 3',
        addSigner: 'Add Signer',
        actionDemo: 'Action demo',
        next: 'next',
        isUploadingErr: 'The file has not been uploaded yet. Please continue after the completion.',
        noUploadFileErr: 'File not uploaded, please continue after uploading',
        noContractTitleErr: 'The contract name is not filled out, please fill in and continue',
        contractTypeErr: 'The current contract type has been deleted. Please re-select the contract type.',
        expiredDateErr: 'The deadline for signing is incorrect. Please continue to modify it.',
        noExpiredDateErr: 'Please fill in the signing deadline and continue',
        noRecipientsErr: 'Add at least one contractor',
        noAccountErr: 'Account cannot be empty',
        noUserNameErr: 'Name cannot be empty',
        noIDNumberErr: 'ID card cannot be empty',
        noEntNameErr: 'Business name cannot be empty',
        accountFormatErr: 'Please enter the correct phone number or email address.',
        userNameFormatErr: 'Please enter the correct name',
        enterpriseNameErr: 'Please enter the correct company name',
        idNumberForVerifyErr: 'Please enter the correct ID card',
        signerErr: 'The signing party is wrong',
        noSignerErr: 'Please add at least one signer',
        lackAttachmentNameErr: 'Please fill in the attachment name',
        repeatRecipientsErr: 'Cannot add signing parties repeatedly when not in sequential order',
        innerContact: 'Internal contact',
        outerContact: 'External contact',
        search: 'Search for',
        accountSelected: 'Selected account',
        groupNameAll: 'All',
        unclassified: 'Unclassified',
        fileLessThan: 'Please upload files less than {num}M',
        beExcel: 'Please upload Excel file',
        usePdf: 'Use PDF files or pictures when uploading',
        usePdfFile: 'Use PDF files when uploading',
        fileNameMoreThan: 'File name length more than {num}, has been automatically intercepted for you',
        needAddSender: 'Your company/you are not set as a signing party. After the contract is sent out, you will not participate in the signing process. Do you need to add yourself as a signing party?',
        addSender: 'Add as signing party',
        tip: 'Hint',
        cancel: 'Cancel',
    },
    addReceiver: {
        English: 'English',
        Japanese: 'Japanese',
        Chinese: 'Chinese',
        Arabic: 'Arabic',
        setNoticelang: 'Notification Language',
        limitFaceConfigTip: 'This feature is unavailable due to your contract price being too low. Please contact BestSign for consultation',
        individual: 'Individual signatory',
        enterprise: 'Company signatory',
        addInstructions: 'Add signing instructions',
        instructionsContent: 'The submitted information is used to help you track the status of contract performance and determine whether business is being performed properly. Once set up, this signatory must submit the information as required',
        addContractingInfo: 'Submit information of the signatory',
        contractingInfoContent: 'The submitted information is used to help you check the qualifications of the signatories and determine whether you can start or continue business with them. If the same information has already been submitted by the signatory, it may not be submitted again.',
        payer: 'Signatory who pays for the contract signing',
        handWriting: 'Turn on handwriting recognition',
        realName: 'Handler individual real name authentication required',
        sameTip: 'Tip: Signing is only allowed when the signatory\'s company name is exactly the same to the name designated by the sender',
        proxy: 'Signing notice received by the signatory\'s reception desk (No specific account to receive the notice)',
        aboradTip: 'Tips: This is a foreign signatory and with risks in real name authentication. Please verify the identity of the person first.',
        busRole: 'Business roles',
        busRoleTip: 'Helps you identify the signatory for easy management',
        busRolePlaceholder: 'Such as employees/distributors',
        handWritingTip: 'The signatory will need to sign with a legible handwritten signature.',
        instructions: 'Add signing instructions for the signatory |  (up to 255 words)',
        contractingParty: 'Information of the signatory',
        signerPay: 'This signatory will pay for the contract signing',
        afterReadingTitle: 'Sign after reading',
        afterReading: 'The signatory must read and know the content of the contract before signing.',
        handWritingTips: 'The signatory\'s handwritten name will be compared with the name specified by the sender or with the real name authentication information in the system before signing',
        SsTitle: 'Signing with both stamp and signature',
        SsTip: 'If you need to sign with a company stamp and also with your personal signature at the same time, your individual real name authentication must be completed before signing.',
        signature: 'Signature',
        stamp: 'Stamp',
        Ss: 'Stamp and Signature',
        mutexError: 'Already set"{msg}", please delete the "{msg}"  setting first',
        forceHandWrite: 'Handwritten signature required',
        faceVerify: 'Must sign with facial verification',
        attachmentRequired: 'Add attachments to the contract',
        newAttachmentRequired: 'submit information of the contracting party ',
        attachmentError: 'The name of attachment cannot be the same to other attachment\'s',
        receiver: 'Receiving phone/email |（supports a maximum of 5, can use a semicolon）',
        orderSignLabel: 'Sequential signing',
        contactAddress: 'Contact address book',
        signOrder: 'Sequence',
        account: 'Account',
        accountPlaceholder: 'Phone/email (required)',
        accountReceptionCollection: 'Front desk collection',
        accountReceptionCollectionTip1: 'Do not know the other party specific account number or the other party has no account number',
        chooseTransferPerson: 'Transfer contract',
        accountReceptionCollectionTip2: 'Please choose the front desk to collect',
        signSubjectPerson: 'Signatory: individual',
        nameTips: 'Name (optional)',
        requiredNameTips: 'Name (required for signature identification)',
        entOperatorNameTips: 'Name (optional)',
        needAuth: 'Real name required',
        operatorNeedAuth: 'Handler individual real name authentication required',
        signSubjectEnt: 'Signatory: enterprise',
        entNameTips: 'Enterprise name (Optional)',
        operator: 'Operator',
        sign: 'Sign',
        more: 'More',
        faceFirst: 'Priority sign with facial verification, alternate sign with SMS code verification',
        faceFirstTips: 'When signing, the system defaults to face verification. When the number of times the brush fails to pass reaches the upper limit of the day, it automatically switches to verification code verification',
        mustFace: 'Must sign with facial recognition',
        handWriteNotAllowed: 'Handwritten signature not allowed',
        mustHandWrite: 'Must sign with handwritten signature',
        fillIDNumber: 'ID number',
        fillNoticeCall: 'notification phone',
        fillNoticeCallTips: 'Fill in the notification phone',
        addNotice: 'Add private message',
        attachTips: 'Annex requirements',
        faceSign: 'Must sign with facial recognition',
        faceSignTips: 'The user needs to pass the face authentication to complete the signing (the face signing is only supported by the mainland residents)',
        handWriteNotAllowedTips: 'The user can only select the signature that has been set or use the default font signature to complete the signing',
        handWriteTips: 'The user needs a handwritten signature to complete the signing',
        idNumberTips: 'Used for signing identity check',
        verifyBefore: 'Verify identity before viewing files',
        verify: 'Verify identidy',
        verifyTips: 'Up to 20 words',
        verifyTips2: 'You must provide this verification information to this user',
        sendToThirdPlatform: 'Send to third-party platforms',
        platFormName: 'Platform name',
        fillThirdPlatFormName: 'Please enter a third-party platform name',
        attach: 'Attachment',
        attachName: 'Accessory name',
        exampleID: 'Example: ID card photo',
        attachInfo: 'Attachment description',
        attachInfoTips: 'Example: Please upload my ID card photo',
        addAttachRequire: 'Add attachment requirements',
        addSignEnt: 'Add signing enterprise',
        addSignPerson: 'Add signing individual',
        selectContact: 'Select contact',
        save: 'Save',
        searchVerify: 'Query check',
        fillImageContentTips: 'Please fill in the image content',
        ok: 'Confirm',
        findContact: 'Find the following contractors from the contract',
        signer: 'Signer',
        signerTips: 'Tip: After selecting the contractor, the platform can help locate the signature and stamp location.',
        add: 'Add',
        notAdd: 'Don\'t add',
        cc: 'Cc',
        notNeedAuth: 'No real name required',
        operatorNotNeedAuth: 'No real name required',
        extracting: 'Extracting',
        autoFill: 'Auto-fill signer',
        failExtracting: 'Not drawn to the signatory',
        idNumberForVerifyErr: 'Please enter the correct ID card',
        noAccountErr: 'Account cannot be empty',
        noUserNameErr: 'Name cannot be empty',
        noIDNumberErr: 'ID card cannot be empty',
        noEntNameErr: 'Business name cannot be empty',
        accountFormatErr: 'Please enter the correct phone number or email address.',
        enterpriseNameErr: 'Please enter the correct company name',
        userNameFormatErr: 'Please enter the correct name',
        riskCues: 'risk warning',
        riskCuesMsg: 'If the signatory party does not sign the real name, you will need to provide evidence of the identity of the signatory party in the event of a dispute. To avoid risk, please choose real name',
        confirmBtnText: 'Choose real name',
        cancelBtnText: 'Choose not to have a real name',
        attachLengthErr: 'You can only add up to 50 attachment requests to a single signer',
        collapse: 'Fold',
        expand: 'Expand',
        delete: 'Delete',
        saySomething: 'say something',
        addImage: 'add pictures',
        addImageTips: '(Support pdf, word, ,jpg, png format, up to10 uploads)',
        give: 'to',
        fileMax: 'Uploads exceed the maximum number',
        signerLimit: 'Your current version does not support more than {limit} relative signatories.',
        showExamle: 'View Example Images',
        downloadExamle: 'Download Example File',
    },
    addReceiverGuide: {
        notRemind: 'Do not remind me next time',
        sign: 'Signature',
        entSign: 'Company signature',
        stamp: 'Stamp',
        stampSign: 'Stamp and signature',
        requestSeal: 'Stamp for Business Chedck',
        'guideTitle': 'How to add a new signatory',
        'receiverType': 'You need to choose the way the signatory participates in the contract (one of six options).',
        'asEntSign': 'Signing on behalf of the company:',
        'sealSub': 'The signatory needs to stamp the contract with the company seal created in the Company console module.',
        'signatureSub': 'The legal person or senior executive signs the contract on behalf of the enterprise. The enterprise has the right to transfer the contract, making the signer unable to view the contract.',
        'vipOnly': 'Available in Advanced Edition',
        'stampSub': 'The signatory can stamp and put handwritten shgnature on the contract.',
        'confirmSealSub': 'Such as financial statements, confirmation letters and other documents. The signatory shall check before stamping.',
        'asPersonSign': 'Signed on behalf of an individual.',
        'asPersonSignTip': 'Individual sign, not on behalf of any company',
        'asPersonSignDesc': 'Private contracts of the signatory, such as loan contracts, employment and resignation, etc.',
        'scanSign': 'Signing by scaning the QR code',
        'scanSignDesc': 'No need to specify the signatory when start the contract. After the contract is created, anyone can scan the QR code/click the link to sign, which is applicable to some scenarios, such as logistics document signing in goods receiving scenario.',
        'selectSignTypeTip': 'Please select the way the signatory participates in the contract first',
    },
    linkContract: {
        title: 'Relate this contract to another contract',
        connectMore: 'Relate this contract to more contracts',
        placeholder: 'Please enter the contract ID',
        revoke: 'The contract has been cancelled',
        overdue: 'Overdue and unsigned',
        approvalNotPassed: 'Approval rejected',
        reject: 'The contract has been rejected by signatory',
        signing: 'signing',
        complete: 'complete',
        approvaling: 'approval',
        disconnect: 'Unrelate these contracts',
        disconnectSuccess: 'disconnect successfully',
        connectLimit: 'Up to 100 contracts can be related',
    },
    field: {
        fieldTip: {
            title: 'No signing location indicated',
            error: 'Notsign with ({type}) in the designated locations in the following contract',
            add: 'Add fields',
            continue: 'Continue to send',
        },
        accountCharge: {
            notice: 'The contract is charged by the number of participating signatory',
            able: 'Can be sent normally',
            unable: 'Not enough accounts available for use. Please contact BestSign customer service.',
            notify: 'The contract will notify all contracting parties in English.',
            noNotify: {
                1: 'This contract will not issue any signing related notices',
                2: '(including notification SMS and email for signing, approval, CC, deadline for signing, etc.)',
            },
        },
        ridingStamp: 'Paging Stamp',
        watermark: 'Watermark',
        senderSignature: 'Signature of the sealer',
        optional: 'Optional',
        decoration: 'Decoration',
        clickDecoration: '点击合同装饰',
        sysError: 'System busy, please try later',
        partedMarkedError: 'You must set the locations of the stamp and signature for the signatory with "Stamp and signature"',
        fieldTitle: '{length} contracts need to be set the signing locations',
        send: 'Send',
        contractDispatchApply: 'Apply for a contract',
        contractNeedYouSign: 'This document needs you to sign',
        ifSignRightNow: 'Whether to sign it now',
        signRightNow: 'Sign it now',
        signLater: 'Sign later',
        signaturePositionErr: 'Please specify the signing location for each signer',
        sendSucceed: 'Send successfully',
        confirm: 'Confirm',
        cancel: 'Cancel',
        qrCodeTips: 'After signing the code, you can view the signing details, verify the validity of the signature and whether the contract has been tampered with.',
        pagesField: 'Page {currentPage}, {totalPages} in total',
        suitableWidth: 'Suitable for width',
        signCheck: 'Signature inspection',
        locateSignaturePosition: 'Locate the signing location',
        locateTips: 'Can help to quickly locate to the signing location. Currently only supports the first signing location for each signing party',
        step1: 'Step 1',
        selectSigner: 'Select signer',
        step2: 'Step 2',
        dragSignaturePosition: 'Drag sign position',
        signingField: 'Signing field',
        docTitle: 'Document',
        totalPages: 'Pages: {totalPages}',
        receiver: 'Receiver',
        delete: 'Delete',
        deductPublicNotice: 'If the number of copies of the private contract is insufficient, the contract will be deducted',
        unlimitedNotice: 'Unlimited use of the contract billing',
        charge: 'Billing',
        units: '{num} ',
        contractToPrivate: 'for private contract',
        contractToPublic: 'for enterprise contract',
        costTips: {
            1: 'Contract for a public contract: a contract with a corporate account in the signatory (excluding the sender)',
            2: 'Private contract: a contract that does not have a corporate account in the signatory (excluding the sender)',
            3: 'The number of billing shares is calculated based on the number of copies of the file.',
            4: 'Billing Shares = File Copies × Batch Import Users',
        },
        costInfo: 'After successfully sending the contract, the fee will be deducted immediately, and it will not be refunded for contract completion, expiration, withdrawal, or refusal.',
        toCharge: 'Recharge',
        contractNeedCharge: {
            1: 'The number of available contracts is insufficient and cannot be sent',
            2: 'The number of available contracts is insufficient. Please contact the main administrator to top up.',
        },
        chooseApprover: 'Select approver:',
        nextStep: 'Next',
        submitApproval: 'Submit for approval',
        autoSendAfterApproval: '*After approval, the contract will be sent automatically',
        chooseApprovalFlow: 'Please select an approval flow',
        completeApprovalFlow: 'The approval process you submitted is incomplete, please complete and resubmit',
        viewPrivateLetter: 'View messages',
        addPrivateLetter: 'Add messages',
        append: 'Add',
        privateLetter: 'Messages',
        signNeedKnow: 'Signing notice',
        maximum5M: 'Please upload documents smaller than 5M',
        uploadServerFailure: 'Failed to upload to server',
        uploadFailure: 'Upload failed',
        pager: 'Page',
        seal: 'Seal',
        signature: 'Signature',
        signDate: 'Date of signing',
        text: 'Text',
        date: 'Date',
        qrCode: 'QR code',
        number: 'Digits',
        dynamicTable: 'Dynamic table',
        terms: 'Contract terms',
        checkBox: 'Checkbox',
        radioBox: 'Radio button',
        image: 'Picture',
    },
    addressBook: {
        innerMember: {
            title: 'Internal members of the company',
            tips: 'Adjust enterprise membership information',
            operation: 'To the console',
        },
        outerContacts: {
            title: 'External contacts',
            tips: 'Invite your business partners to finish the real names authentication in advance for rapid business development',
            operation: 'Invite your business partners',
        },
        myContacts: {
            title: 'My contacts',
            tips: 'Modify contact, to ensure that the signatory information accurate',
            operation: 'To the user center',
        },
        selected: 'Selected accounts',
        search: 'Search',
        loadMore: 'Load more',
        end: 'All loading completed',
    },
    dataBoxInvite: {
        title: 'Invite your business partners',
        step1: 'Share the link with your business partner to create a company account in advance',
        step2: 'Company account created through the link / QR code will appear in your address book',
        step3: 'Manage your partners in "File +" module',
        imgName: 'Share the QR code',
        saveQrcode: 'Save the QR code',
        copy: 'Copy',
        copySuccess: 'Copy success',
        copyFailed: 'COPY FAILED',
    },
    shareView: {
        title: 'Forward for Review',
        account: 'Phone Number/Email',
        role: 'Reviewer Role',
        note: 'Remarks',
        link: 'Link:',
        saveQrcode: 'Save Wechat Mini-Program Code',
        signerMessage: 'Signer\'s Message',
        rolePlaceholder: 'e.g., company legal advisor, department head, etc.',
        notePlaceholder: 'Leave a message for the reviewer, within 200 characters',
        generateLink: 'Generate Link',
        regenerateLink: 'Regenerate Link',
        inputAccount: 'Please enter your phone number or email',
        inputCorrectAccount: 'Please enter correct phone number or email',
        accountInputTip: 'To ensure the link opens correctly, please enter the contract reviewer\'s information accurately',
        shareLinkTip1: 'Save the Wechat mini-program code or ',
        shareLinkTip: 'copy the link to share with the reviewer',
        linkTip1: 'The contents of the contract text are confidential, please do not disclose them unless necessary',
        linkTip2: 'The link is valid for 2 days; after regenerating the link, the previous link will automatically become invalid',
    },
    recoverSpecialSeal: {
        title: '印章无法使用',
        description1: '发件方要求您需使用该印章才能签署合同，但贵公司已删除该印章。为确保签署顺利进行，请向管理员恢复该印章。',
        description2: '如果该印章确实不合适被继续使用，可联系发件方修改对印章图案的要求后，再签署合同。',
        postRecover: '申请恢复印章',
        note: '点击后管理员将收到恢复印章申请的短信/邮件，同时在印章管理页面时也能看到申请。',
        requestSend: '恢复申请提交成功',
    },
    paperSign: {
        title: '使用纸质方式签署',
        stepText: ['下一步', '确认纸质签', '确定'],
        needUploadFile: '请先上传扫描件',
        uploadError: '上传失败',
        cancel: '取消',
        downloadPaperFile: '获取纸质签文件',
        step0: {
            title: '您需要先下载打印合同，加盖物理章后，邮寄给发件方。',
            address: '邮寄地址：',
            contactName: '接收人姓名：',
            contactPhone: '接收人联系方式：',
            defaultValue: '请通过线下方式向发件方索取',
        },
        step1: {
            title0: '第一步：下载&打印纸质合同',
            title0Desc: ['下载打印的合同应包含已签署的电子章的图案。请', '获取纸质签文件。'],
            title1: '第二步：加盖印章',
            title1Desc: '在纸质合同上加盖合同有效的公司印章。',
            title2: ['第三步：', '上传扫描件，', '回到签署页面，点击签署按钮，完成纸质签'],
            title2Desc: ['将纸质合同扫描转换成合同扫描件（PDF格式文件）后上传，', '电子合同中不展示您的印章图案，但会记录您此次操作过程。'],
        },
        step2: {
            title: ['将纸质合同扫描件（PDF格式文件）上传', '请确认纸质合同已下载并签署后，再点击确定按钮，结束纸质签署流程。'],
            uploadFile: '上传扫描件',
            getCodeVerify: '获取合同签署校验',
            isUploading: '上传中...',
        },
    },
    allowPaperSignDialog: {
        title: 'Allow Paper Signatures',
        content: 'This contract is issued by {senderName} to {receiverName}, allowing for signing in paper form.',
        tip: "You may choose to compile the contract documents at present and print them, subsequently to be signed and sealed offline by the individual responsible for the company's seal, in accordance with formal procedures.",
        icon: 'Conversion to paper-based signing >>',
        goSign: 'To sign electronically',
        cancel: 'Cancel',
    },
    sealInconformityDialog: {
        errorSeal: {
            title: '印章提示',
            tip: '检测到您当前印章图片与您的企业身份不符，当前印章图片识别结果：',
            tip1: '检测到有企业印章与企业名称：',
            tip2: '是否要继续使用当前印章图片？',
            tip3: '根据发件方要求，您需要使用企业名称为：',
            tip4: '的印章',
            tip5: '请确认印章已符合要求，否则将会影响合同的有效性！',
            tip6: '不匹配，请确保印章符合发件方要求。',
            guide: '如何上传正确的印章 >>',
            next: '继续使用',
            tip7: 'And your seal name does not comply with the specifications, with the words “{keyWord}” .',
            tip8: 'Detected that the seal name does not comply with the specifications and contains the words “{keyWord}”. Do you want to continue using it?',
        },
        exampleSeal: {
            title: '上传印章图案方式',
            way1: ['方式一：', '1、在白色纸张上盖一个实体印章图案', '2、拍照，并将图片上传至平台'],
            way2: ['方式二：', '直接使用平台的生成电子章功能，如图：'],
            errorWay: ['错误方式：', '手持印章', '无关图片', '营业执照'],
        },
        confirm: '确认',
        cancel: '取消',
    },
    addSealDialog: {
        title: 'Add Stamp Image',
        dec1: 'Please select a stamp image from your local folder (formats: JPG, JPEG, PNG, etc.). The system will merge this stamp image into the current contract.',
        dec2: 'After that, you need to click the "Sign" button to pass the signing verification to complete the stamping.',
        updateNewSeal: 'Upload New Stamp',
    },
};
