CKEDITOR.plugins.add('sensitive', {
    init: function (editor) {
        /*
        *      取消所有高亮
        */
        editor.cancleSensitiveWordsHighlight = function () {
            let regstrEpswh = '<span class="ep_ckeditor_sensitivewords" style="background-color:[^<>:]+[;]*">([^<>]+)<\\/span>';
            let htmlEpswh = this.getData();
            htmlEpswh = htmlEpswh.replace(eval('/' + regstrEpswh + '/ig'), '$1');
            if (this.document != null) { this.document.getBody().setHtml(htmlEpswh); }
            return htmlEpswh;
        };
        /*
        *    words 敏感词
        *    igChar 敏感词中忽略的特殊字符
        *    color 高亮底色
        */
        editor.sensitiveWordsHighlight = function (epswhlwords, epswhligChar, epswhlcolor) {
            // 空的字符串
            if (typeof epswhlwords == 'string' && !epswhlwords) { return; }
            // 空数组
            if (typeof epswhlwords == 'object' && epswhlwords[0] == undefined) { return; }
            let htmlEpswh = this.getData();
            // 高亮模板
            let highLighCOde = '<span>&nbsp;</span><span class="ep_ckeditor_sensitivewords" style="color:{$color};">{$word}</span><span>&nbsp;</span>';
            if (!epswhlcolor) { epswhlcolor = '#ffff00'; }
            highLighCOde = highLighCOde.replace('{$color}', epswhlcolor);
            // 如果内容中有高亮内容先进行清理
            if (htmlEpswh.indexOf('ep_ckeditor_sensitivewords') > -1) {
                htmlEpswh = this.cancleSensitiveWordsHighlight();
            }
            // 重新高亮
            let epswhlkeyWords = [];
            if (typeof epswhlwords == 'string') { epswhlkeyWords = epswhlwords.split(','); } else { epswhlkeyWords = epswhlwords; }
            // 需要忽略的分隔符
            if (epswhligChar && epswhligChar.length > 0) {
                epswhligChar = epswhligChar.replace(/[<>&"]/g, function (c) { return { '<': '&lt;', '>': '&gt;', '&': '&amp;', '"': '&quot;' }[c]; });
                epswhligChar = '[' + epswhligChar + ']*';
            } else {
                epswhligChar = '';
            }
            for (let i = 0; i < epswhlkeyWords.length; i++) {
                let allkey = epswhlkeyWords[i].split('');
                let regstr = allkey.join(epswhligChar);
                regstr = '(' + regstr + ')';
                let reg = eval('/' + regstr + '/ig');
                let hcode = highLighCOde.replace('{$word}', '$1');
                htmlEpswh = htmlEpswh.replace(reg, hcode);
            }
            // document 对象在源码模式无效，this.setData是重新加载，不是同步方法，不能使用
            // if (this.document != null)
            //     this.document.getBody().setHtml(htmlEpswh);
            editor.setData(htmlEpswh);
            // const el = this.document.getById('editor');
            // if (el) el.setHtml(htmlEpswh);
        };
    }
});
