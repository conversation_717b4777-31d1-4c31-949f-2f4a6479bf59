### 全局依赖
node版本：v8+
npm版本: v5.6.0+

### 安装模块
```shell
npm i --registry https://registry.npm.taobao.org
```

### 开发
```shell
npm run dev
```

### 打包
```shell
npm run build
```

### 组件
 - 组件文件最上面注释下该组件用途：通用（完全复用）/业务通用（业务复用）/业务（不可复用）
 - 组件最外层标签className，组件为页面则命名为xxx-page，组件为标签组件则命名为xxx-cpn，且xxx需要保持唯一，以避免样式干扰

### 路径简写

#### 1. js中引入路径
 - src -> src/
 - css -> src/common/assets/css/
 - img -> src/common/assets/img/
 - components -> src/components/
 - common_pages -> src/pages/common/
 - enterprise_pages -> src/pages/enterprise/
 - foundation_pages -> src/pages/foundation/  

```javascript
import SSQHeader from 'components/header/Header.vue';
```

#### 2. scss中引入路径
需要在路径前加~，*.vue文件的template中也可用~

 ```scss
 @import '~css/style.scss';
 body {
    background: url(~img/test.jpg);
 }
 ```

#### 3. template中引入路径
```html
<img src="~img/login_logo.png">
```

### 静态资源
 - 图片文件统一放在src/common/assets/img文件夹下，方便复用

### 代码风格
 - 4缩进

### 命名
 - 组件文件夹使用小写，使用'_'分隔
 - 组件文件名和变量名一律使用大写开头：NotFound.vue NotFound
 - 组件标签名：NotFound（建议） 或者 not-found
 - 样式名：-（英文横杠），比如`<div class="ssq-header"></div>`
 - scss变量：用-连接，前缀表明所属或者功能，后缀表明属性，例如`$base-color: blue`，`$body-background-color: gold`，`$accent-color: red`
 - 图片：使用小写，下划线"_"分隔，例如 ssq_logo.png

### 签署中转页格式
http://0.0.0.0:9000/signing/transition?contractId=1994813488463085568&token=7rMriu&type=sign

### 已有的全局变量

- HOST_ENV  （判断是官网还是建行）
- ENV_NAME  （判断是预发布还是生产环境）



